<?php

namespace common\services\v2;

use common\models\Comment;
use common\models\News;
use common\models\NewsCategory;
use common\models\NewsContent;
use common\models\NewsTags;
use common\helpers\DataHelper;
use common\models\CollegeContent;
use common\models\NewsSubdomain;
use common\models\Tags;
use common\models\User;
use common\models\UserTranslation;
;

use Yii;
use yii\caching\TagDependency;
use yii\data\ActiveDataProvider;
use yii\data\Pagination;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\web\Response;

class NewsService
{
    const APP_LIVE_TIME = **********; // DO NOT CHANGE THIS

    protected $ttl = 60 * 60;

    /**
     * Get news by Slug
     * @param $slug Slug will be the news slug
     *
     * @return object
     */
    public function getBySlug($slug)
    {
        return News::find()
            ->with(['tags'])
            ->where(['slug' => $slug])
            ->andWhere(['status' => NewsContent::STATUS_ACTIVE])
            ->one();
    }

    /**
     * Get recent news.
     *
     *
     * @return object
     */
    public function getRecent($limit = 5, $streamId = null)
    {
        $lang_code = DataHelper::getLangId();

        $query = new Query();
        if ($lang_code == 1) {
            $query->select([
                'news.banner_image',
                'news.name',
                'news.slug',
                'content.content',
                'news.is_live',
                'news.updated_at',
                'author.name as authorName',
                'content.meta_title',
                'news.expired_at',
                'news.lang_code'
            ]);
        } else {
            $query->select([
                'news.banner_image',
                'news.name',
                'news.slug',
                'content.content',
                'news.is_live',
                'news.updated_at',
                'ut.user_name',
                'content.meta_title',
                'news.expired_at',
                'news.lang_code'
            ]);
        }
        $query->from(News::tableName() . ' news')
            ->innerJoin(NewsContent::tableName() . ' as content', 'content.news_id = news.id');

        if ($lang_code == 1) {
            $query->innerJoin('user as author', 'author.id = content.author_id');
        } else {
            $query->leftJoin('user_translation as ut', 'ut.tag_user_id = content.author_id');
        }

        $query->where(['news.status' => News::STATUS_ACTIVE])
            ->andWhere(['news.lang_code' => $lang_code])
            ->andWhere(['content.status' => NewsContent::STATUS_ACTIVE]);

        if (!empty($streamId)) {
            $query->andWhere(['stream_id' => $streamId]);
        }

        $query->orderBy(['news.published_at' => SORT_DESC])
            ->limit($limit);

        $results = $query->all();
        if (empty($results)) {
            return [];
        }
        $data = [];
        foreach ($results as $result) {
            if (!empty($result['user_name']) && $result['lang_code'] != 1) {
                $authorName = $result['user_name'];
            } else {
                $authorName = !empty($result['authorName']) ? $result['authorName'] : '';
            }
            $data[] = [
                'tag_id' => $result['is_live'] ?? '',
                'newsContent' => $result['content'] ?? '',
                'name' => $result['name'] ?? '',
                'slug' => $result['slug'] ?? '',
                'title' => !empty($result['meta_title']) ? $result['meta_title'] : $result['name'],
                'banner_image' => $result['banner_image'] ?? '',
                'author' => $authorName,
                'updated_at' => $result['updated_at'] ?? '',
                'expired_at' => $result['expired_at'] ?? '',
                'lang_code' => $result['lang_code'] ?? '',
            ];
        }

        return $data;
    }

    /**
     * Get News based on Category.
     * @param $categoryId Id will be the news category
     *
     * @return object
     */
    public function getByCategoryId($categoryId)
    {
        $lang_code = DataHelper::getLangId();

        $news = new ActiveDataProvider([
            'query' => News::find()->select([
                News::tableName() . '.id',
                'name',
                'banner_image',
                'slug',
                'created_at',
                'published_at',
                'is_popular',
                'is_live',
                'expired_at',
                'lang_code'
            ])->where(['news_category_id' => $categoryId])
                ->andWhere(['lang_code' => $lang_code])
                ->andWhere(['status' => News::STATUS_ACTIVE])
                ->with(['newsContent'])
                ->orderBy(['created_at' => SORT_DESC])
                ->active(),
            'pagination' => ['pageSize' => 20]
        ]);

        return $news;
    }

    /**
     * Get News Feature based on feature Id.
     * @param $categoryId Id will be the news category
     *
     * @return object
     */
    public function getFeatured($featureId)
    {
        $lang_code = DataHelper::getLangId();

        $query = new Query();
        if ($lang_code == 1) {
            $query->select([
                'news.banner_image',
                'news.name',
                'news.slug',
                'news.is_live',
                'news.created_at',
                'news.published_at',
                'author.name as authorName',
                'author.slug as authorSlug',
                'content.meta_description',
                'content.meta_title',
                'news.expired_at',
                'news.lang_code'
            ]);
        } else {
            $query->select([
                'news.banner_image',
                'news.name',
                'news.slug',
                'news.is_live',
                'news.created_at',
                'news.published_at',
                'author.name as authorName',
                'author.slug as authorSlug',
                'ut.user_name',
                'content.meta_description',
                'content.meta_title',
                'news.expired_at',
                'news.lang_code'
            ]);
        }
        $query->from(News::tableName() . ' news')
            ->leftJoin(NewsTags::tableName() . ' as tags', 'tags.news_id = news.id')
            ->leftJoin(NewsContent::tableName() . ' as content', 'content.news_id = news.id');
        if ($lang_code == 1) {
            $query->leftJoin(User::tableName() . ' as author', 'author.id = content.author_id');
        } else {
            $query->leftJoin(User::tableName() . ' as author', 'author.id = content.author_id')
                ->leftJoin(UserTranslation::tableName() . ' as ut', 'ut.tag_user_id = content.author_id');
        }
        $query->where(['tags.tags_id' => $featureId])
            ->andWhere(['news.lang_code' => $lang_code])
            ->andWhere(['news.status' => News::STATUS_ACTIVE])
            ->orderBy(['news.created_at' => SORT_DESC])
            ->groupBy(['news.slug']);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'db' => \Yii::$app->db,
            'pagination' => [
                'defaultPageSize' => 10
            ]
        ]);

        return $dataProvider ?? [];
    }

    /**
     * Get News latest news category.
     *
     * @return object
     */
    public function getLatestNews()
    {
        $lang_code = DataHelper::getLangId();
        $news = new ActiveDataProvider([
            'query' => News::find()->select([
                News::tableName() . '.id',
                'name',
                'banner_image',
                'slug',
                'created_at',
                'published_at',
                'is_popular',
                'is_live',
                'expired_at',
                'lang_code'
            ])->with(['newsContent', 'tags'])
                ->where(['status' => News::STATUS_ACTIVE])
                ->andWhere(['lang_code' => $lang_code])
                ->orderBy(['published_at' => SORT_DESC])
                ->active(),
            'pagination' => ['pageSize' => 20]
        ]);

        return $news;
    }

    /**
     * Get News Categories.
     *
     * @param $slug Slug will be the news category slug
     * @return object
     */
    public function getCategoryBySlug($identifier)
    {
        $category = NewsCategory::find()
            ->select(['id', 'name', 'slug'])
            ->active()
            ->bySlug($identifier)
            ->one();

        if (empty($category)) {
            $category = NewsCategory::find()
                ->select(['id', 'name', 'slug'])
                ->active()
                ->byId($identifier)
                ->one();
        }

        return $category;
    }

    /**
     * Get News Tags.
     * @param $slug Slug will be the news tag slug
     *
     * @return object
     */
    public function getByTag($slug, $limit)
    {
        return Tags::find()
            ->with(News::tableName())
            ->where(['slug' => $slug])
            ->andWhere(['status' => Tags::STATUS_ACTIVE])
            ->limit($limit)
            ->all();
    }

    /**
     * Get top search news
     *
     * @return common\models\News | []
     */
    public function getByTopSearches()
    {
        $lang_code = DataHelper::getLangId();

        return NewsSubdomain::find()
            ->select(['name', 'slug', 'display_name', 'lang_code'])
            ->where(['status' => News::STATUS_ACTIVE])
            ->andWhere(['lang_code' => $lang_code])
            ->andWhere(['is_popular' => News::POPULAR_YES])
            ->andWhere(['not', ['display_name' => null]])
            ->andWhere(['>=', 'popular_at', new \yii\db\Expression('NOW() - INTERVAL 4 DAY')])
            ->orderBy(['published_at' => SORT_DESC])
            ->all();
    }

    /**
     * Get next news from same category
     *
     * @return array || []
     */
    public function getReadNextFeature($newsId, $catId)
    {
        if (empty($catId)) {
            return '';
        }
        $lang_code = DataHelper::getLangId();

        $query = new Query();
        $query->select(['id', 'name', 'banner_image', 'slug', 'lang_code'])
            ->from([News::tableName()])
            ->where(['news_category_id' => $catId])
            ->andWhere(['lang_code' => $lang_code])
            ->andWhere(['status' => News::STATUS_ACTIVE])
            ->orderBy(['created_at' => SORT_DESC]);

        $readNextFeature = $query->all(\Yii::$app->db);

        if (empty($readNextFeature)) {
            return [];
        }

        $data = [];
        foreach ($readNextFeature as $news => $value) {
            $endData = end($readNextFeature);
            if ($value['id'] == $newsId && $endData['id'] != $newsId) {
                $result = $readNextFeature[$news + 1] ?? [];

                $data[] = [
                    'id' => $result['id'] ?? '',
                    'name' => $result['name'] ?? '',
                    'display_name' => $result['display_name'] ?? '',
                    'banner_image' => $result['banner_image'] ?? '',
                    'slug' => $result['slug'] ?? '',
                    'lang_code' => $result['lang_code'] ?? '',
                ];
            }
        }

        return $data;
    }

    /**
     * Get News based on identifier
     *
     * @return common\models\News
     */
    public function getNews($identifier)
    {
        $lang_code = DataHelper::getLangId();
        $news = News::find()->where(['lang_code' => $lang_code])
            ->active()
            ->bySlug($identifier)
            ->one();
        if (empty($news)) {
            $news = News::find()->active()->byId($identifier)->one();
        }

        return $news ?? null;
    }

    /**
     * Get Featured news based on tagId
     *
     * @return array || []
     */
    public function getFeaturedNews($tagId, $limit = 10)
    {
        $lang_code = DataHelper::getLangId();

        $query = new Query();
        if ($lang_code == 1) {
            $query->select([
                'news.banner_image',
                'content.meta_title as title',
                'news.name',
                'news.slug',
                'news.is_live',
                'author.name as author',
                'news.published_at',
                'news.created_at',
                'news.expired_at',
                'news.lang_code'
            ]);
        } else {
            $query->select([
                'news.banner_image',
                'content.meta_title as title',
                'news.name',
                'news.slug',
                'news.is_live',
                'author.name as author',
                'ut.user_name',
                'news.published_at',
                'news.created_at',
                'news.expired_at',
                'news.lang_code'
            ]);
        }
        $query->from(News::tableName() . ' news')
            ->leftJoin(NewsTags::tableName() . ' as tags', 'tags.news_id = news.id')
            ->leftJoin(NewsContent::tableName() . ' as content', 'content.news_id = news.id');
        if ($lang_code == 1) {
            $query->leftJoin(User::tableName() . ' as author', 'author.id = content.author_id');
        } else {
            $query->leftJoin(User::tableName() . ' as author', 'author.id = content.author_id')
                ->leftJoin(UserTranslation::tableName() . ' as ut', 'ut.tag_user_id = content.author_id');
        }
        $query->where(['tags.tags_id' => $tagId])
            ->andWhere(['news.lang_code' => $lang_code])
            ->andWhere(['news.status' => News::STATUS_ACTIVE])
            ->limit($limit)
            ->orderBy(['news.created_at' => SORT_DESC])
            ->groupBy(['news.slug']);

        $featuredNews = $query->all(\Yii::$app->db);
        return $featuredNews ?? [];
    }

    /**
     * Get Category news based on catId
     *
     * @return array || []
     */
    public function getCategoryNews($catId, $limit = 10)
    {
        if (empty($catId)) {
            return '';
        }
        $lang_code = DataHelper::getLangId();

        $query = new Query();
        if ($lang_code == 1) {
            $query->select([
                'news.banner_image',
                'news.name',
                'news.slug',
                'news.is_live',
                'news.updated_at',
                'author.name as authorName',
                'author.slug as authorSlug',
                'content.meta_title',
                'news.expired_at',
                'news.lang_code'
            ]);
        } else {
            $query->select([
                'news.banner_image',
                'news.name',
                'news.slug',
                'news.is_live',
                'news.updated_at',
                'ut.user_name',
                'author.slug as authorSlug',
                'content.meta_title',
                'news.expired_at',
                'news.lang_code'
            ]);
        }
        $query->from(News::tableName() . ' news')
            ->leftJoin(NewsContent::tableName() . ' as content', 'content.news_id = news.id');
        if ($lang_code == 1) {
            $query->leftJoin(User::tableName() . ' as author', 'author.id = content.author_id');
        } else {
            $query->leftJoin(UserTranslation::tableName() . ' as ut', 'ut.tag_user_id = content.author_id');
            $query->innerJoin(User::tableName() . ' as author', 'author.id = content.author_id');
        }
        $query->where(['news.status' => News::STATUS_ACTIVE])
            ->andWhere(['news.lang_code' => $lang_code])
            ->andWhere(['content.status' => NewsContent::STATUS_ACTIVE])
            ->andWhere(['news.news_category_id' => $catId])
            ->orderBy(['news.created_at' => SORT_DESC])
            ->limit($limit);

        $news = $query->all();

        $data = [];
        foreach ($news as $category) {
            if (!empty($result['user_name']) && $lang_code != 1) {
                $authorName = $category['user_name'];
            } else {
                $authorName = !empty($category['authorName']) ? $category['authorName'] : '';
            }
            $data[] = [
                'tag_id' => $category['is_live'] ?? '',
                'slug' => $category['slug'] ?? '',
                'title' => $category['name'] ?? '',
                'banner_image' => $category['banner_image'] ?? '',
                'author' => $authorName,
                'authorSlug' => $category['authorSlug'] ?? '',
                'updated_at' => $category['updated_at'] ?? '',
                'lang_code' => $category['lang_code'] ?? '',
                'user_name' => $category['user_name'] ?? '',
            ];
        }

        return $data;
    }

    public function getNewsByCategoryID($id)
    {
        $lang_code = DataHelper::getLangId();
        $query = new Query();
        if ($lang_code == 1) {
            $query->select([
                'news.banner_image',
                'news.name',
                'news.slug',
                'news.is_live',
                'news.updated_at',
                'author.name as authorName',
                'content.meta_title',
                'news.expired_at',
                'news.lang_code'
            ]);
        } else {
            $query->select([
                'news.banner_image',
                'news.name',
                'news.slug',
                'news.is_live',
                'news.updated_at',
                'author.name as authorName',
                'ut.user_name',
                'content.meta_title',
                'news.expired_at',
                'news.lang_code'
            ]);
        }
        $query->from(News::tableName() . ' news')
            ->innerJoin(NewsContent::tableName() . ' as content', 'content.news_id = news.id');
        if ($lang_code == 1) {
            $query->innerJoin(User::tableName() . ' as author', 'author.id = content.author_id');
        } else {
            $query->innerJoin(User::tableName() . ' as author', 'author.id = content.author_id')
                ->leftJoin(UserTranslation::tableName() . ' as ut', 'ut.tag_user_id = content.author_id');
        }
        $query->where(['news.news_category_id' => $id])
            ->andWhere(['news.lang_code' => $lang_code])
            ->andWhere(['news.status' => News::STATUS_ACTIVE])
            ->andWhere(['content.status' => NewsContent::STATUS_ACTIVE])
            ->orderBy(['published_at' => SORT_DESC])
            ->limit(10);

        return $query->all() ?? [];
    }

    public function getAllCategories()
    {
        $lang_code = DataHelper::getLangId();
        $categories = NewsCategory::find()
            ->select(['id', 'name', 'slug'])
            ->active()
            ->orderBy(['position' => SORT_ASC])
            ->all();
        foreach ($categories as $value) {
            //the list based on category ID
            $news = $this->getNewsByCategoryID($value->id);
            foreach ($news as $detail) {
                if (isset($detail['user_name']) && !empty($detail['user_name'] && $lang_code != 1)) {
                    $authorName = $detail['user_name'];
                } else {
                    $authorName = !empty($detail['authorName']) ? $detail['authorName'] : '';
                }
                $item[$value->name][] = [
                    'tag_id' => $detail['is_live'] ?? '',
                    'slug' => $detail['slug'] ?? '',
                    'title' => $detail['meta_title'] ?? '',
                    'banner_image' => $detail['banner_image'] ?? '',
                    'author' => $authorName,
                    'cat_slug' => $value->slug,
                    'lang_code' => $detail['lang_code'],
                ];
            }
        }

        return $item ?? [];
    }

    /**
     * Get News Tags based on newsId
     *
     * @return array || []
     */
    public function getTags($newsId)
    {
        $tags = NewsTags::find()
            ->where(['news_id' => $newsId])
            ->with(['tags'])
            ->all();

        foreach ($tags as $tag) {
            $data[] = $tag->tags;
        }

        return $data ?? [];
    }

    /**
     * Get News Detail based on newsId
     *
     * @return array || object
     */
    public function getContent($newsId)
    {
        $detail = NewsContent::find()
            ->select(['news_id', 'meta_keywords', 'content', 'h1', 'meta_title', 'meta_description', 'author_id', 'updated_at', 'created_at'])
            ->where(['news_id' => $newsId])
            ->andWhere(['status' => NewsContent::STATUS_ACTIVE])
            ->one();

        return $detail ?? null;
    }

    /**
     * Get News Comments based on newsId
     *
     * @return object
     */
    public function getComments($newsId)
    {
        return Comment::find()->select(['name', 'id', 'parent_id', 'comment', 'updated_at'])
            ->with('children')
            ->where(['entity' => News::ENTITY_NEWS])
            ->andWhere(['entity_id' => $newsId])
            ->andWhere(['parent_id' => null])
            ->active()
            ->all();
    }

    /*
     * Rss Feed News
     * @return common\models\News
     */
    public function getRssNews()
    {
        $query = News::find()
            ->where(['>=', 'published_at', new Expression('(NOW() - INTERVAL 1 DAY)')])
            ->active()
            ->orderBy(['published_at' => SORT_DESC]);
        $countQuery = clone $query;
        $pages = new Pagination([
            'totalCount' => $countQuery->count(),
            'defaultPageSize' => 20,
            'forcePageParam' => false,
        ]);

        $news = $query->offset($pages->offset)
            ->limit($pages->limit)
            ->all();

        return $news;
    }

    /*
     * Set Targeting
     *
     */
    public function getAdTargetData(News $news)
    {
        $hash = __CLASS__ . __FUNCTION__ . md5(base64_encode(serialize($news)));
        $data = Yii::$app->cache->getOrSet($hash, function () use ($news) {
            if (empty($news->college)) {
                return [];
            }
            foreach ($news->college as $college) {
                $query = new Query();
                $query->distinct();
                $query->select(['college.slug as collegeName', 'city.slug as cityName', 'state.slug as stateName', 'course.name as courseName', 'cc.course_id', 'stream.name as streamName', 'course.degree'])
                    ->from('college college')
                    ->leftJoin('college_program as cc', 'cc.college_id = college.id')
                    ->leftJoin('course as course', 'course.id = cc.course_id')
                    ->leftJoin('stream as stream', 'stream.id = course.stream_id')
                    ->leftJoin('city as city', 'city.id = college.city_id')
                    ->leftJoin('state as state', 'state.id = city.state_id')
                    ->where(['college.id' => $college->id]);
                $collegeData = $query->all(\Yii::$app->db);

                $courses = array_unique(ArrayHelper::getColumn($collegeData, 'courseName'));
                $streams = array_unique(ArrayHelper::getColumn($collegeData, 'streamName'));
                $degrees = array_unique(ArrayHelper::getColumn($collegeData, 'degree'));
                $cityName = array_unique(ArrayHelper::getColumn($collegeData, 'cityName'));
                $stateName = array_unique(ArrayHelper::getColumn($collegeData, 'stateName'));
                $collegeName = array_unique(ArrayHelper::getColumn($collegeData, 'collegeName'));

                $result[] = [
                    'CollegeName' => $collegeName ? $collegeName[0] : '',
                    'State' => $stateName ? $stateName[0] : '',
                    'City' => $cityName ? $cityName[0] : '',
                    'degree' => $degrees ?? '',
                    'discipline' => $streams ?? '',
                    'courses' => $courses ?? ''
                ];
            }

            if (!empty($result)) {
                return $result ?? [];
            }
        }, 60 * 60 * 6, new TagDependency(['tags' => 'get-ad-target-data-' . $news->slug]));

        return $data ?? [];
    }

    public function liveTagExpiredAt($expired_at, $tagId)
    {
        if (empty($expired_at)) {
            return $tagId;
        }

        $expired_news = date('Y-m-d H:i', strtotime($expired_at));
        $currentDate = date('Y-m-d H:i');
        if ($currentDate < $expired_news) {
            $tags = 1;
        } else {
            $tags = 0;
        }

        return $tags;
    }

    //get news page lead auto fetch based on product mapping
    public static function getNewsAutoFetchValues($post)
    {
        $className = '';
        $productId = '';

        if (empty($post)) {
            return '';
        }

        if (!empty($post->board)) {
            $className = 'leadBoardAutoCapture';
            $productId = $post->board[0]->id ?? '';
        } elseif (!empty($post->college)) {
            $className = 'collegeLeadAutoFetch';
            $productId = $post->college[0]->id ?? '';
        } elseif (!empty($post->exam)) {
            $className = 'examLeadValue';
            $productId = $post->exam[0]->id ?? '';
        } elseif (!empty($post->course)) {
            $className = 'leadCourseCapture';
            $productId = $post->course[0]->id ?? '';
        }

        return [
            'className' => $className,
            'productId' => $productId
        ];
    }

    public static function getInstance($tableName, $id)
    {

        if ($id !== 'news') {
            //return self::getNewsTableName($tableName);
            switch ($tableName) {
                case 'news':
                    $tableName = 'news_subdomain';
                    break;
                case 'news_content':
                    $tableName = 'news_content_subdomain';
                    break;
                case 'course_news':
                    $tableName = 'course_news_subdomain';
                    break;
                case 'college_news':
                    $tableName = 'college_news_subdomain';
                    break;
                case 'board_news':
                    $tableName = 'board_news_subdomain';
                    break;
                case 'article_news':
                    $tableName = 'article_news_subdomain';
                    break;
                case 'exam_news':
                    $tableName = 'exam_news_subdomain';
                    break;
                case 'news_news':
                    $tableName = 'news_news_subdomain';
                    break;
                case 'news_tags':
                    $tableName = 'news_subdomain_tag';
                    break;
                case 'live_update':
                    $tableName = 'news_subdomain_live_update';
                    break;
                case 'news_translation':
                    $tableName = 'news_subdomain_translation';
                    break;
            }

            return $tableName;
        } else {
            return $tableName;
        }
    }

    public function getNewsTableName($instance)
    {
        switch ($instance) {
            case 'news':
                $tableName = 'news_subdomain';
                break;
            case 'news_content':
                $tableName = 'news_content_subdomain';
                break;
            case 'course_news':
                $tableName = 'course_news_subdomain';
                break;
            case 'college_news':
                $tableName = 'college_news_subdomain';
                break;
            case 'board_news':
                $tableName = 'board_news_subdomain';
                break;
            case 'article_news':
                $tableName = 'article_news_subdomain';
                break;
            case 'exam_news':
                $tableName = 'exam_news_subdomain';
                break;
            case 'news_news':
                $tableName = 'news_news_subdomain';
                break;
            case 'news_tags':
                $tableName = 'news_subdomain_tag';
                break;
            case 'live_update':
                $tableName = 'news_subdomain_live_update';
                break;
            case 'news_translation':
                $tableName = 'news_subdomain_translation';
                break;
        }

        return $tableName;
    }

    public static function createCollegeContentNews($collegeIds)
    {
        foreach ($collegeIds as $collegeId) {
            $content =  CollegeContent::find()
                ->where(['entity_id' => $collegeId])
                ->andWhere(['sub_page' => 'news'])
                ->one();

            if (empty($content)) {
                $news = new Query();
                $news->select(['ns.id'])
                    ->from(['news_subdomain ns'])
                    ->innerJoin('college_news_subdomain cns', 'ns.id = cns.news_id')
                    ->innerJoin('college c', 'cns.college_id = c.id')
                    ->where(['c.id' => $collegeId])
                    ->andWhere(['ns.status' => News::STATUS_ACTIVE]);

                if ($news->count() >= 4) {
                    $newContent = new CollegeContent();
                    $newContent->author_id = 1;
                    $newContent->entity = 'college';
                    $newContent->entity_id = $collegeId;
                    $newContent->sub_page = 'news';
                    $newContent->status = 1;
                    $newContent->save();
                }
            }
        }

        Yii::$app->response->format = Response::FORMAT_JSON;
        return ['status' => true, 'msg' => 'News college content saved successfully.'];
    }
}
