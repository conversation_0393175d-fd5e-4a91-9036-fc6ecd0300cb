<?php

namespace common\services;

use common\models\documents\College;
use common\models\SponsorCollege;
use yii\helpers\ArrayHelper;
use common\models\CollegeElastic;

class SponsorService
{
    public function getSponsorColleges($stream = null, $course = null, $specialization = null, $state = null, $limit = 18)
    {
       
        $query = SponsorCollege::find()
           ->alias('sponsor')
           ->select(['college_id'])
            ->orderBy(
                [
                    new \yii\db\Expression('case when sponsor.position is null then 1 else 0 end, sponsor.position')
                ]
            );
        $query->innerJoin('college co', 'co.id = sponsor.college_id');
        $query->where(['or', ['state' => (!empty($state) ? $state : 'all-states')], ['state' => '']]);

        if ($stream && empty($course)) {
            $query->andWhere(['or', ['stream' => $stream], ['stream' => 'all-streams']]);
        }
        if ($course) {
            $query->andWhere(['or', ['course' => $course], ['course' => 'all-courses']]);
        }
        if (empty($stream)) {
            $query->andWhere(['or', ['is', 'stream', null], ['stream' => '']]);
        }
        if (empty($course)) {
            $query->andWhere(['or', ['is', 'course', null], ['course' => '']]);
        }
        $query->andWhere(['sponsor.status'=>SponsorCollege::STATUS_ACTIVE]);

        $query->andWhere(['co.sponser_college_view'=>SponsorCollege::VIEWS_LISTING_VIEW]);

        $sponsorColleges = $query->distinct('college_id')->limit($limit)->all();
        
        if (empty($sponsorColleges)) {
            return [];
        }
        if (is_array($specialization)) {
            $specialization =  $specialization[0] ?? '';
        } else {
            $specialization =  $specialization ?? '';
        }
        if (is_array($state)) {
            $state =  $state[0] ?? '';
        } else {
            $state = $state ?? '';
        }
       

        $ids = ArrayHelper::getColumn($sponsorColleges, 'college_id');
       
          $sponserCollegeList = [];
        foreach ($ids as $id) {
            $filterArray = [];
            if (!empty($specialization) &&  $specialization !='') {
                $filterArray['bool']['must'][0]['nested']['path'] = 'course';
                $filterArray['bool']['must'][0]['nested']['query']['bool']['must'] = CollegeElastic::specialization($specialization);
                if (!empty($state)) {
                    $filterArray['bool']['must'][] = CollegeElastic::state($state);
                }
            }
            $filterArray['bool']['must'][] = CollegeElastic::sponsercollege($id);
            $filterArray['bool']['must'][] = CollegeElastic::sponsercollegeviewtype(SponsorCollege::VIEWS_LISTING_VIEW);
            $filterArray['bool']['must'][] = CollegeElastic::isSponsored(1);
            $query =  CollegeElastic::find();
            $collegesElastic = $query->query($filterArray)->asArray()->one();
            if (!empty($collegesElastic)) {
                $sponserCollegeList[] =  $collegesElastic;
            }
        }
        
        if (empty($sponserCollegeList)) {
            return [];
        }
        $sortedCollegeList = [];
       
        foreach ($sponserCollegeList as $key => $college) {
             $college['isSponsored'] = true;
             $sortedCollegeList[$college['_source']['college_id']] = $college['_source'];
        }
        return $sortedCollegeList;
    }

    public function getSponsorCollegesGridView($params, $page = 0)
    {
        
        if (isset($page) && !empty($page) &&  $page!=0) {
            $page = $page;
        }
       
        $stream = $params['stream'] ?? '';
        $course = $params['course'] ?? '';
        if (is_array($params['specialization'])) {
            $specialization =  $params['specialization'][0] ?? '';
        } else {
            $specialization =  $params['specialization'] ?? '';
        }
        if (is_array($params['state'])) {
            $state =  $params['state'][0] ?? '';
        } else {
            $state = $params['state'] ?? '';
        }
       
        
          $query = SponsorCollege::find()
           ->alias('sponsor')
           ->select(['college_id'])
            ->orderBy(
                [
                    new \yii\db\Expression('case when sponsor.position is null then 1 else 0 end, sponsor.position')
                ]
            );
        $query->innerJoin('college co', 'co.id = sponsor.college_id');
        $query->where(['or', ['state' => (!empty($state) ? $state : 'all-states')], ['state' => '']]);

        if ($stream && empty($course)) {
            $query->andWhere(['or', ['stream' => $stream], ['stream' => 'all-streams']]);
        }
        if ($course) {
            $query->andWhere(['or', ['course' => $course], ['course' => 'all-courses']]);
        }
        if (empty($stream)) {
            $query->andWhere(['or', ['is', 'stream', null], ['stream' => '']]);
        }
        if (empty($course)) {
            $query->andWhere(['or', ['is', 'course', null], ['course' => '']]);
        }
        $query->andWhere(['sponsor.status'=>SponsorCollege::STATUS_ACTIVE]);

        $query->andWhere(['co.sponser_college_view'=>SponsorCollege::VIEWS_GRID_SLIDER]);

        $sponsorColleges = $query->distinct('college_id')->limit(12)->offset(12*(int)$page)->all();
        
        if (empty($sponsorColleges)) {
            return [];
        }
        $ids = ArrayHelper::getColumn($sponsorColleges, 'college_id');
       
           $sponsorCollegeList = [];
        foreach ($ids as $id) {
            $filterArray = [];
            if (!empty($specialization) &&  $specialization !='') {
                $filterArray['bool']['must'][0]['nested']['path'] = 'course';
                $filterArray['bool']['must'][0]['nested']['query']['bool']['must'] = CollegeElastic::specialization($specialization);
                if (!empty($state)) {
                    $filterArray['bool']['must'][] = CollegeElastic::state($state);
                }
            }
            $filterArray['bool']['must'][] = CollegeElastic::sponsercollege($id);
            $filterArray['bool']['must'][] = CollegeElastic::sponsercollegeviewtype(SponsorCollege::VIEWS_GRID_SLIDER);
            $filterArray['bool']['must'][] = CollegeElastic::isSponsored(1);
            $query =  CollegeElastic::find();
            $collegesElastic = $query->query($filterArray)->one();
            if (!empty($collegesElastic)) {
                $sponsorCollegeList[] =  $collegesElastic;
            }
        }
        if (empty($sponsorCollegeList)) {
            return [];
        }
        
        return $sponsorCollegeList;
    }
    /** Implement when listing page requirement for LAF comes
    public function getLAFColleges($stream = null, $course = null, $specialization = null, $state = null, $limit = 5)
    {
        $query = LiveApplication::find()
            ->where(['status' => 1]);

        if ($state) {
            $query->orWhere(['in', 'state.slug', $state]);
        }
        if ($stream) {
            $query->orWhere(['in', 'stream.slug', $stream]);
        }
        if ($course) {
            $query->orWhere(['in', 'course.slug', $course]);
        }
        if ($specialization) {
            $query->orWhere(['in', 'specialization.slug', $specialization]);
        }

        $lafColleges = $query->limit($limit)->asArray()->all();
        if (empty($lafColleges)) {
            return [];
        }


        foreach ($lafColleges as $college) {
            $collegeData = College::find()->where(['college_id' => $college['college_id']])->one();
            $items[] = [
                'name' => $collegeData->name ?? '',
                'slug' => $collegeData->slug ?? '',
                'image' =>  $collegeData->logo ?? '',
                'fees' => $college['total_fees'],
                'course' => $college['course'][0]['name'],
                'redirect_link' => $college['redirect_url'],
            ];
        }

        return $items;
    }
     */
}
