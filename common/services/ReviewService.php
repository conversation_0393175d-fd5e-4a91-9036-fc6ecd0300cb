<?php

namespace common\services;

use common\helpers\CollegeHelper;
use common\helpers\ReviewHelper;
use common\models\College;
use common\models\ReferralCode;
use common\models\Review;
use common\models\ReviewAnswer;
use common\models\ReviewCategory;
use common\models\ReviewQuestion;
use common\models\ReviewContent;
use common\models\ReviewFilter;
use common\models\ReviewFilterGroup;
use common\models\ReviewImage;
use frontend\helpers\Url;
use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;

class ReviewService
{
    private static $_fieldMappings = [
        'State' => 'state',
        'City' => 'city',
        'Streams' => 'stream',
        'Courses' => 'course',
        'Batch' => 'batch'
    ];

    public function getCategory()
    {
        return ReviewCategory::find()->active()->orderBy(['position' => SORT_ASC])->all();
    }

    public function getQuestion()
    {
        return ReviewQuestion::find()
            ->where(['business_unit_id' => ReviewHelper::$business_unit_id['getmyuni']])
            ->andWhere(['status' => ReviewQuestion::STATUS_ACTIVE])
            ->orderBy(['position' => SORT_ASC])
            ->all();
    }

    public function getReviewId($studentId, $collegeId, $courseId)
    {
        return Review::find()
            ->where(['student_id' => $studentId])
            ->andWhere(['college_id' => $collegeId])
            ->andWhere(['course_id' => $courseId])
            ->one();
    }

    public function getReviewById($id)
    {
        return Review::find()->where(['id' => $id])->one();
    }

    public function getReviewBySlug($slug)
    {
        $query = new Query();
        $query->select([
            'review.id', 'review.college_id', 'review.course_id', 'review.student_id', 'college.name as collegeName', 'college.slug as collegeSlug',
            'college.logo_image as collegeLogo', 'college.display_name as displayName', 'course.name as courseName', 'review.admission_year', 'review.created_at', 'review.college_fees', 'review.title'
        ])
            ->from('review review')
            ->leftJoin('college as college', 'college.id = review.college_id')
            ->leftJoin('course as course', 'course.id = review.course_id')
            ->where(['review.slug' => $slug])
            ->andWhere(['review.status' => Review::STATUS_APPROVED]);
        $reviews = $query->one();

        return $reviews ?? [];
    }

    public function getCollege($collegeId)
    {
        $query = new Query();
        $query->select(['id', 'city_id', 'parent_id', 'is_sponsored'])
            ->from('college')
            ->where(['id' => $collegeId])
            ->andWhere(['status' => College::STATUS_ACTIVE]);
        $college = $query->one();

        if (empty($college) || $college == false) {
            return [];
        }

        $queryCollege = new Query();
        $queryCollege->select([
            'c.name', 'c.display_name', 'c.logo_image', 'c.slug',
            'c.cover_image', 'city.name as cityName', 'state.name as stateName'
        ])
            ->from('college c')
            ->leftJoin('city as city', 'city.id = c.city_id')
            ->leftJoin('state as state', 'state.id = city.state_id')
            ->where(['c.id' => $college['parent_id']]);

        $parentCollege = $queryCollege->one();

        $item = [
            'college' =>  $college,
            'parent_college' => $parentCollege !== false ? $parentCollege : []
        ];

        return $item ?? [];
    }

    public function getStudentReferralCode($user)
    {
        if (empty($user)) {
            return '';
        }

        return ReferralCode::find()->where(['id' => $user->id])->one();
    }

    public function getStudent($studentId)
    {
        $query = new Query();
        $query->select(['name', 'profile_pic'])
            ->from('student')
            ->where(['id' => $studentId]);
        $student = $query->one();

        return $student ?? '';
    }

    public function getReviewCategoryRating($reviewId)
    {
        $query = new Query();
        $query->select(['rating', 'review_id', 'review_category_id'])
            ->from('review_content')
            ->where(['review_id' => $reviewId])
            ->andWhere(['status' => ReviewContent::STATUS_APPROVED]);

        $categoryrating = $query->all();

        foreach ($categoryrating as $rating) {
            if (empty($rating)) {
                continue;
            }
            $data[ReviewHelper::$reviewCategorySLug[$rating['review_category_id']]] = $rating['rating'] ?? '';
        }

        return $data ?? [];
    }

    public function getReviewAnswers($reviewId)
    {
        $answers = ReviewAnswer::find()
            ->with(['question'])
            ->where(['review_id' => $reviewId])
            ->active()
            ->orderBy(['id' => SORT_ASC])
            ->all();
        $data = [];
        foreach ($answers as $answer) {
            $data[$answer->question->slug] = [
                'value' => $answer->answer,
            ];
        }

        return $data;
    }

    public function getReviewImages($reviewId)
    {
        return ReviewImage::find()->where(['review_id' => $reviewId])->active()->all();
    }

    public function getcollegesByCourseLocation($courseId, $collegeLocation, $limit)
    {
        $query = new Query();
        $query->select(['c.id', 'c.name', 'c.display_name', 'c.cover_image', 'c.logo_image', 'city.name as cityName', 'state.name as stateName', 'c.slug'])
            ->from('college c')
            ->leftJoin('college_program as cc', 'cc.college_id = c.id')
            ->leftJoin('city as city', 'city.id = c.city_id')
            ->leftJoin('state as state', 'state.id = city.state_id')
            ->where(['cc.course_id' => $courseId])
            ->andWhere(['c.city_id' => $collegeLocation])
            ->groupBy(['cc.college_id'])
            ->limit($limit);
        $courseColleges = $query->all();

        return $courseColleges ?? [];
    }

    public function getExamsByCollege($collegeId)
    {
        $query = new Query();
        $query->select(['exam.name', 'exam.slug', 'exam.cover_image', 'exam.display_name', 'exam.id'])
            ->from('exam exam')
            ->leftJoin('college_exam as ce', 'ce.exam_id = exam.id')
            ->where(['ce.college_id' => $collegeId]);
        $exams = $query->all();

        if (empty($exams)) {
            return [];
        }

        $data = [];
        foreach ($exams as $exam) {
            $examDates = (new ExamService())->getUpcomingDate($exam['id']);
            $item = [
                $exam
            ];
            foreach ($examDates as $date) {
                $item += [
                    str_replace('-', '_', $date['slug']) => $date['start']
                ];
            }
            $data[] = $item;
        }

        return $data;
    }

    public function getContent($reviewId)
    {
        $categorySlug = ReviewHelper::$contentOrder;
        uksort($categorySlug, [$this, 'categoryOrder']);

        foreach ($categorySlug as $slug) {
            if (empty($slug)) {
                continue;
            }
            $query = new Query();
            $query->select(['content.content', 'rc.name as review_category_name'])
                ->from('review_content content')
                ->leftJoin('review_category as rc', 'rc.id = content.review_category_id')
                ->where(['rc.slug' => $slug])
                ->andWhere(['review_id' => $reviewId])
                ->andWhere(['content.status' => ReviewContent::STATUS_APPROVED]);

            $reviewContent[$slug] = $query->one();
        }

        return $reviewContent ?? [];
    }

    public function getOtherContent($reviewId)
    {
        $categorySlug = ReviewHelper::$contentOrder;
        uksort($categorySlug, [$this, 'categoryOrder']);

        foreach ($categorySlug as $slug) {
            if (empty($slug)) {
                continue;
            }

            $content = ReviewContent::find()
                ->select('content')
                ->where(['review_id' => $reviewId])
                ->andWhere(['not', ['content' => '']])
                ->andWhere(['review_category_id' => ReviewHelper::$reviewCategoryId[$slug]])
                ->andWhere(['status' => ReviewContent::STATUS_APPROVED])
                ->one();

            if (empty($content)) {
                continue;
            }

            $data = [
                'content' => $content['content'],
                'name' => ReviewHelper::$reviewCategory[$slug]
            ];

            return $data;
        }
    }
    public function categoryOrder($x, $y)
    {
        if ($x == $y) {
            return 0;
        }
        return ($x > $y) ? 1 : -1;
    }

    public function getOtherReviews($collegeId, $courseId = '', $limit = 4)
    {
        $query = new Query();
        $query->select(["course.name as courseName, review.admission_year, student.name as studentName, review.created_at,
            review.id, review.slug as reviewSlug, college.slug as collegeSlug,
            college.display_name, college.name, student.profile_pic, group_concat(concat(review_category_id,'@@@',content) SEPARATOR '###') content"])
            ->from('review_content content')
            ->leftJoin('review as review', 'review.id = content.review_id')
            ->leftJoin('course as course', 'course.id = review.course_id')
            ->leftJoin('student as student', 'student.id = review.student_id')
            ->leftJoin('college as college', 'college.id = review.college_id')
            ->where(['review.college_id' => $collegeId])
            ->andWhere(['content.status' => ReviewContent::STATUS_APPROVED])
            ->andWhere(['review.status' => Review::STATUS_APPROVED])
            ->andWhere(['not', ['content.content' => null]])
            ->groupBy('review.student_id')
            ->orderBy(['review.created_at' => SORT_DESC]);

        if (!empty($courseId)) {
            $query->andWhere(['review.course_id' => $courseId]);
        }

        if (!empty($limit)) {
            $query->limit($limit);
        }

        $reviews = $query->all();

        return $reviews ?? [];
    }

    public function getCollegeReviewCount($collegeId)
    {
        $query = new Query();
        $query->distinct();
        $query->select(['r.id'])
            ->from('review r')
            ->leftJoin('review_content as rc', 'rc.review_id = r.id')
            ->where(['r.college_id' => $collegeId])
            ->andWhere(['r.status' => Review::STATUS_APPROVED]);
        $approvedReviews = $query->all();

        return !empty($approvedReviews) ? count($approvedReviews) : '';
    }

    public function getFilterValues($searchModel, $currentUrl = '')
    {
        $availableFacets = $searchModel->availableFacets;
        $facetsItems = [];
        foreach ($availableFacets as $facetKey => $facetValues) {
            foreach ($facetValues as $facetValue) {
                if (isset($facetValue['count'])) {
                    $facetsItems[$facetKey][$facetValue['_id']] = $facetValue['count'];
                }
            }
        }

        $items = [];
        $filterValues = $this->getAllFilters();
        foreach ($filterValues as $filterValue) {
            if (isset(self::$_fieldMappings[$filterValue->reviewFilterGroup->name]) && isset($facetsItems[self::$_fieldMappings[$filterValue->reviewFilterGroup->name]])) {
                if (isset($facetsItems[self::$_fieldMappings[$filterValue->reviewFilterGroup->name]][$filterValue->slug])) {
                    $html = '<label for="' . $filterValue->slug . '">' . $filterValue->name . ' (' . $facetsItems[self::$_fieldMappings[$filterValue->reviewFilterGroup->name]][$filterValue->slug] . ')' . '</label>';
                    $items[$filterValue->reviewFilterGroup->name][$filterValue->slug] = $html;
                }
            }
        }

        return $this->sortFilters($items, $facetsItems);
    }

    /**
     * Service Method for Related Url of all the available filters.
     *
     * @param string| $url          Exisitng domain main URL.
     * @param string| $path         Exisitng domain first priority path.
     * @param string| $filter       Each filter Name.
     * @param string| $filterGroup  Each filterGroup Name.
     *
     * @return string| $newUrl New Generated Related Url for each filter option available.
     */
    public function getRelatedUrl($selectedFilters, $checkbox)
    {
        if (!isset($checkbox->reviewFilterGroup)) {
            return '';
        }
        // eliminate checkbox group from selected Fields
        foreach ($selectedFilters as $key => $selectedFilter) {
            if (isset($selectedFilter['reviewFilterGroup_name']) && $selectedFilter['url_position'] == $checkbox->reviewFilterGroup->url_position) {
                unset($selectedFilters[$key]);
            }
        }

        if ($checkbox->reviewFilterGroup->url_position == 2 && empty($selectedFilters)) {
            return [
                'relatedUrl' => Url::toTestUrl() . 'reviews?' . $checkbox->slug,
            ];
        }

        if ($checkbox->reviewFilterGroup->url_position == 1 && empty($selectedFilters)) {
            return [
                'relatedUrl' => Url::toTestUrl() . 'reviews?' . $checkbox->slug,
            ];
        }

        if ($checkbox->reviewFilterGroup->url_position == 0) {
            return '';
        }

        $eligibleFields[$checkbox->reviewFilterGroup->url_position] = [
            'slug' => $checkbox->slug
        ];

        $selectedFilters = ArrayHelper::index($selectedFilters, 'rule.priority', null);
        arsort($selectedFilters);
        $selectedFilters = array_slice($selectedFilters, 0, 1);

        if (isset($selectedFilters[0])) {
            $eligibleFields[$selectedFilters[0]['url_position']] = [
                'slug' => $selectedFilters[0]['slug']
            ];
        }

        $finalSlug = Url::toTestUrl() . 'reviews?' . $checkbox->slug;

        return [
            'relatedUrl' => $finalSlug,
        ];
    }

    public function sortFilters($items, $facetItems)
    {
        $fieldMapping = [];
        foreach (self::$_fieldMappings as $key => $mapping) {
            $fieldMapping[$mapping] = $key;
        }

        $fgroupItems = [];
        foreach ($facetItems as $facetKey => $facetItem) {
            foreach ($facetItem as $k => $val) {
                if (isset($fieldMapping[$facetKey])) {
                    if (isset($items[$fieldMapping[$facetKey]][$k])) {
                        $fgroupItems[$fieldMapping[$facetKey]][$k] = $items[$fieldMapping[$facetKey]][$k];
                    }
                }
            }
        }

        return $fgroupItems;
    }

    public function getAllFilters()
    {
        $currentPath = Yii::$app->request->url;
        $query = ReviewFilter::find()
            ->with(['reviewFilterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => ReviewFilter::STATUS_ACTIVE]);

        if ($currentPath == '/college/reviews') {
            $cityGroup = ReviewFilterGroup::find()->select(['id'])->where(['name' => 'City'])->one();

            $topCityList = ReviewFilter::find()
                ->with('reviewFilterGroup')
                ->where(['review_filter_group_id' => $cityGroup->id])
                ->andWhere(['IS NOT', 'position', null])
                ->andWhere(['status' => ReviewFilter::STATUS_ACTIVE])
                ->orderBy(['position' => SORT_ASC])
                ->limit(50)
                ->all();

            $query->andWhere(['<>', 'review_filter_group_id', $cityGroup->id]);
        }
        $results = $query->all();

        if (!empty($topCityList)) {
            return array_merge($results, $topCityList);
        }

        return $results;
    }

    public static function getFilterUrlPagination($bodyParams)
    {
        // Support both ReviewSearch and ReviewElasticSearch form names
        $searchParams = $bodyParams['ReviewElasticSearch'] ?? $bodyParams['ReviewSearch'] ?? [];

        if (isset($searchParams['state']) && !empty($searchParams['state'])) {
            $state['state'] = implode(',', $searchParams['state']);
        }
        if (isset($searchParams['city']) && !empty($searchParams['city'])) {
            $city['city'] = implode(',', $searchParams['city']);
        }
        if (isset($searchParams['stream']) && !empty($searchParams['stream'])) {
            $stream['stream'] = implode(',', $searchParams['stream']);
        }
        if (isset($searchParams['course']) && !empty($searchParams['course'])) {
            $course['course'] = implode(',', $searchParams['course']);
        }
        if (isset($searchParams['batch']) && !empty($searchParams['batch'])) {
            $batchs['batchs'] = implode(',', $searchParams['batch']);
        }

        $finalArray = array_merge($state ?? [], $city ?? [], $course ?? [], $stream ?? [], $batchs ?? []);

        return $finalArray;
    }

    //college wise review functions
    public static function getCollegeBasedCategoryRating($collegeId)
    {
        $query = new Query();
        $query->select(['content.review_category_id', 'SUM(content.rating) as rating', 'count(content.rating) as ratingCount'])
            ->from('review_content content')
            ->leftJoin('review as r', 'r.id = content.review_id')
            ->where(['r.college_id' => $collegeId])
            ->andWhere(['r.status' => Review::STATUS_APPROVED])
            ->andWhere(['content.status' => ReviewContent::STATUS_APPROVED])
            ->groupBy('content.review_category_id');

        $categoryRating = $query->all();

        $data = [];
        foreach ($categoryRating as $rating) {
            if ($rating['rating'] > 0) {
                if (array_key_exists($rating['review_category_id'], CollegeHelper::$reviewCategoryRatingValue)) {
                    $data[ReviewHelper::$reviewCategories[$rating['review_category_id']]] = round((($rating['rating'] * CollegeHelper::$reviewCategoryRatingValue[$rating['review_category_id']]) /
                        ($rating['ratingCount'] * CollegeHelper::$reviewCategoryRatingValue[$rating['review_category_id']]) * 1), 1);
                }
            }
        }
        return $data ?? [];
    }

    /**
     * Getting Total Rating grouping by rating Ex : 1 ,2 , etc.
     * @param int | $collegeId College ID
     * @return array |[]
     */
    public static function getRevDistributionRating($collegeId)
    {

        $query = new Query();
        $query->select([
            'SUM(CASE when rating BETWEEN 1 AND 1.9 THEN 1 ELSE 0 END) AS rating1',
            'SUM(CASE when rating BETWEEN 2 AND 2.9 THEN 1 ELSE 0 END) AS rating2',
            'SUM(CASE when rating BETWEEN 3 AND 3.9 THEN 1 ELSE 0 END) AS rating3',
           ' SUM(CASE when rating BETWEEN 4 AND 5 THEN 1 ELSE 0 END) AS rating4'
        ])->from([
            (new Query)
            ->select(['round(AVG(rating)) as rating'])
            ->from('review_content as content')
                ->innerJoin('review as r', 'r.id = content.review_id')
                ->where(['r.college_id' => $collegeId])
                ->andWhere(['content.status' => Review::STATUS_APPROVED])
                ->groupBy('content.review_id')
        ]);

        $distributionRating = $query->all();

        $data = [];
        $items =[];
        foreach ($distributionRating as $rating) {
            $totalRating = $rating['rating1'] + $rating['rating2'] + $rating['rating3'] + $rating['rating4'];
            if ($totalRating != 0) {
                $data['progress1'] = round(($rating['rating1'] / $totalRating) * 100);
                $data['progress2'] = round(($rating['rating2'] / $totalRating) * 100);
                $data['progress3'] = round(($rating['rating3'] / $totalRating) * 100);
                $data['progress4'] = round(($rating['rating4'] / $totalRating) * 100);
            }
            $data['rating1'] = $rating['rating1'];
            $data['rating2'] = $rating['rating2'];
            $data['rating3'] = $rating['rating3'];
            $data['rating4'] = $rating['rating4'];
        }

        return $data ?? [];
    }

    public static function getReviewRatingCount($collegeId)
    {
        $query = new Query();
        $query->select(['count(college_id) as reviewCount'])
            ->from('review')
            ->where(['college_id' => $collegeId]);

        $reviewCount = $query->one();

        return $reviewCount ?? '';
    }

    public function getCollegeReviews($collegeId, $limit = 10, $offSet = 0)
    {
        $query = new Query();
        $query->select([
            'r.id as reviewId', 'c.name as courseName',
            's.name as studentName', 's.profile_pic', 'college.name as collegeName',
            'r.slug as reviewSlug', 'r.admission_year', 'r.created_at', 'college.id as college_id', 'college.city_id'
        ])
            ->from('review as r')
            ->innerJoin('course as c', 'c.id = r.course_id')
            ->innerJoin('college as college', 'college.id = r.college_id')
            ->innerJoin('student as s', 's.id = r.student_id')
            ->where(['r.college_id' => $collegeId])
            ->andWhere(['r.status' => Review::STATUS_APPROVED])
            ->groupBy('r.student_id')
            ->orderBy(['r.created_at' => SORT_DESC, 'r.student_id' => SORT_DESC])
            ->limit($limit)->offset($offSet);

        $reviews = $query->all();

        $items = [];
        if (!empty($reviews)) {
            $reviews = ArrayHelper::index($reviews, 'reviewId');
            $reviewIds = (array_column($reviews, 'reviewId'));
            $qry = new Query();
            $qry->select(['review_id', 'review_category_id', 'rating', 'content'])
                ->from('review_content')
                ->where(['in', 'review_id', $reviewIds])
                ->andWhere(['status' => ReviewContent::STATUS_APPROVED]);
            $data = $qry->all();

            if (!empty($data)) {
                foreach ($data as $d) {
                    $items[$d['review_id']]['content'][] = $d['review_category_id'] . '@@@' . $d['content'];
                    $items[$d['review_id']]['categoryRating'][] = $d['review_category_id'] . '@@@' . $d['rating'];
                }
            }
        }

        $reviewData = [];
        foreach ($reviews as $key => $value) {
            $reviewData[$key] = $value;
            $reviewData[$key]['content'] = !empty($items[$key]['content']) ? implode('###', $items[$key]['content']) : ' ';
            $reviewData[$key]['categoryRating'] = !empty($items[$key]['categoryRating']) ? implode('###', $items[$key]['categoryRating']) : '';
        }

        return $reviewData ?? [];
    }

    public function getCollegeReviewsFiltePage($collegeId, $limit = 1)
    {
        $categorySlug = ReviewHelper::$contentOrder;
        uksort($categorySlug, [$this, 'categoryOrder']);

        foreach ($categorySlug as $slug) {
            if (empty($slug)) {
                continue;
            }

            $query = new Query();
            $query->select(['content.content'])
                ->from('review_content content')
                ->leftJoin('review as r', 'r.id = content.review_id')
                ->where(['r.college_id' => $collegeId])
                ->andWhere(['content.status' => ReviewContent::STATUS_APPROVED])
                ->andWhere(['r.status' => Review::STATUS_APPROVED])
                ->limit($limit);

            $content = $query->one();

            if (empty($content)) {
                continue;
            }

            $data = [
                'content' => $content['content'],
                'name' => ReviewHelper::$reviewCategory[$slug]
            ];

            return $data;
        }
    }

    public function getCollegeSubpageReviews($collegeId, $courseId = null, $limit = 5)
    {
        // $query = new Query();

        // $query->select(["course.name as courseName, review.admission_year, student.name as studentName, review.created_at,
        //     review.id, review.slug as reviewSlug, college.slug as collegeSlug, student.profile_pic,
        //     group_concat(concat(review_category_id,'@@@',content) SEPARATOR '###') content,
        //     group_concat(concat(review_category_id,'@@@',content.rating) SEPARATOR '###') categoryRating"])
        //     ->from('review_content content')
        //     ->leftJoin('review as review', 'review.id = content.review_id')
        //     ->leftJoin('course as course', 'course.id = review.course_id')
        //     ->leftJoin('student as student', 'student.id = review.student_id')
        //     ->leftJoin('college as college', 'college.id = review.college_id')
        //     ->where(['review.college_id' => $collegeId])
        //     ->andWhere(['content.status' => ReviewContent::STATUS_APPROVED])
        //     ->andWhere(['review.status' => Review::STATUS_APPROVED])
        //     ->andWhere(['not', ['content.content' => null]])
        //     ->groupBy('review.student_id')
        //     ->orderBy(['review.created_at' => SORT_DESC]);

        // if (!empty($courseId)) {
        //     $query->andWhere(['review.course_id' => $courseId]);
        // }

        // if (!empty($limit)) {
        //     $query->limit($limit);
        // }

        $query = new Query();
        $query->select([
            'r.id as reviewId', 'c.name as courseName',
            's.name as studentName', 's.profile_pic', 'college.name as collegeName', 'r.admission_year', 'r.created_at', 'college.id as college_id', 'college.city_id'
        ])
            ->from('review as r')
            ->innerJoin('course as c', 'c.id = r.course_id')
            ->innerJoin('college as college', 'college.id = r.college_id')
            ->innerJoin('student as s', 's.id = r.student_id')
            ->where(['r.college_id' => $collegeId])
            ->andWhere(['r.status' => Review::STATUS_APPROVED])
            ->groupBy('r.student_id')
            ->orderBy(['r.created_at' => SORT_DESC]);

        if (!empty($courseId)) {
            $query->andWhere(['r.course_id' => $courseId]);
        }

        if (!empty($limit)) {
            $query->limit($limit);
        }



        $reviews = $query->all();

        $items = [];
        if (!empty($reviews)) {
            $reviews = ArrayHelper::index($reviews, 'reviewId');
            $reviewIds = (array_column($reviews, 'reviewId'));
            $qry = new Query();
            $qry->select(['review_id', 'review_category_id', 'rating', 'content'])
                ->from('review_content')
                ->where(['in', 'review_id', $reviewIds])
                ->andWhere(['status' => ReviewContent::STATUS_APPROVED])
                ->andWhere(['not', ['content' => null]]);
            $data = $qry->all();

            if (!empty($data)) {
                foreach ($data as $d) {
                    $items[$d['review_id']]['content'][] = $d['review_category_id'] . '@@@' . $d['content'];
                    $items[$d['review_id']]['categoryRating'][] = $d['review_category_id'] . '@@@' . $d['rating'];
                }
            }
        }

        $reviewData = [];
        foreach ($reviews as $key => $value) {
            $reviewData[$key] = $value;
            $reviewData[$key]['content'] = !empty($items[$key]['content']) ? implode('###', $items[$key]['content']) : ' ';
            $reviewData[$key]['categoryRating'] = !empty($items[$key]['categoryRating']) ? implode('###', $items[$key]['categoryRating']) : '';
        }

        return $reviewData ?? [];

        $reviews = $query->all();

        return $reviews ?? [];
    }

    /**
     * Get the available Student Review Images of college
     *
     * @param  int |$collegeId College Id int
     * @return array |[]
     */
    public function getStudentReviewImage($collegeId)
    {
        if (empty($collegeId) || $collegeId == null) {
            return [];
        }

        $query = new Query();
        $query->select('review.id')
            ->from('review review')
            ->where(['review.college_id' => $collegeId])
            ->andWhere(['review.status'  => Review::STATUS_APPROVED]);

        $data = $query->all();

        if (empty($data)) {
            return [];
        }

        foreach ($data as $image) {
            if (empty($image)) {
                continue;
            }
            $images = new Query();
            $images->select(['file', 'alt'])
                ->from('review_image')
                ->where(['review_id' => $image['id']])
                ->andWhere(['status' => ReviewImage::STATUS_APPROVED]);

            $reviewImage[] = $images->all();
        }

        return $reviewImage ?? [];
    }

    public function getCityStateByCollegeId($collegeId)
    {
        $res = ['cityId' => 0, 'state_id' => 0];
        if (empty($collegeId)) {
            return $res;
        }

        $query  = new Query();
        $query->select(['city.id as cityId', 'city.state_id'])
            ->from('college c')
            ->leftJoin('city as city', 'city.id = c.city_id')
            ->where(['c.id' => $collegeId]);
        $cityStateId = $query->one();

        return $cityStateId ?? $res;
    }
}
