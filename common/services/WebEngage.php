<?php

namespace common\services;

use common\models\Lead;
use common\models\Student;

class WebEngage
{
    const ENDPOINT = 'https://api.in.webengage.com';
    const LICENSECODE = 'in~d3a49c51';
   // const APIKEY = 'c565084b-48ab-448b-afd5-11de6d5f1988';
    const APIKEY = 'c565084b-48ab-448b-afd5-11de6d5f1988';
    

    public static function registerUser($phone, $email, $fullName, $sourceUrl)
    {
        $name = self::processName($fullName);

        $data = [
            'userId' => $phone,
            'firstName' => $name[0] ?? '',
            'lastName' => $name[1] ?? '',
            'email' => $email,
            'phone' => $phone,
            'source_url' => $sourceUrl,
            'source' =>  Lead::SOURCE_ORGANIC,
            'user_status' => Student::STATUS_FORM_FILLED,
            'user_type' => Lead::STUDENT_TYPE_LEAD,

        ];

        self::curl('users', $data);
    }

    public static function pushEvent($userId, $eventName, $eventTime = null, $payLoad = [])
    {
        if (empty($eventName)) {
            return;
        }
        
        $data = [];
        $data['eventName'] = $eventName;
        if (!empty($userId)) {
            $data['userId'] = $userId;
        }

        if (!empty($eventTime)) {
            $data['eventTime'] = self::isoTimestamp($eventTime);
        }

        if (!empty($payLoad)) {
            $data['eventData'] = $payLoad;
        }

        self::curl('events', $data);
    }

    public static function pushEventLoginLogout($eventName, $message, $userId)
    {
        
        if (empty($eventName)) {
            return;
        }

        $data = [];
        $data['eventName'] = $eventName;
        if (!empty($userId)) {
            $data['userId'] = $userId;
        }

        if (!empty($message)) {
            if ($eventName=='logout') {
                $data['eventData'] = [$message=>'Yes'];
            }
            if ($eventName=='login') {
                $data['eventData'] = [$message=>'Yes'];
            }
        }
        
        self::curl('events', $data);
    }

    public static function curl($slug, $payload)
    {
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, sprintf('%s/v1/accounts/%s/%s', self::ENDPOINT, self::LICENSECODE, $slug));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . self::APIKEY . '',
        ]);
        curl_exec($ch);
        curl_close($ch);
    }

    public static function isoTimestamp($dateTimeString)
    {
        $date = \DateTime::createFromFormat('Y-m-d H:i:s', $dateTimeString);
        return $date->format('Y-m-d\TH:i:sO');
    }

    public static function processName($name)
    {
        $name = trim($name);
        $last_name = (strpos($name, ' ') === false) ? '' : preg_replace('#.*\s([\w-]*)$#', '$1', $name);
        $first_name = trim(preg_replace('#' . preg_quote($last_name, '#') . '#', '', $name));
        return [$first_name, $last_name];
    }
}
