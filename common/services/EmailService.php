<?php


namespace common\services;

use Yii;
use Exception;

class EmailService
{
    public static function sendEmail($to, $subject, $data, $template, $name = null)
    {
        if (is_array($to)) {
            foreach ($to as $email) {
                try {
                    $data['data']['name'] = $name;
                    Yii::$app->sysMailer->compose($template, $data)
                        ->setFrom(['<EMAIL>' => 'Getmyuni Notifications'])
                        ->setTo($email)
                        ->setSubject($subject)
                        ->send();
                } catch (Exception $e) {
                    Yii::error($e->getMessage(), 'Notification Error');
                }
            }
        }
    }

    public static function articleTranslationList()
    {

        $emails = [
            '<EMAIL>',
            '<EMAIL>',
            // '<EMAIL>',
            // '<EMAIL>',
            // '<EMAIL>',
            // '<EMAIL>',
            // '<EMAIL>',
            // '<EMAIL>',
            // '<EMAIL>',
            // '<EMAIL>'
        ];
        return $emails;
    }
}
