<?php

namespace common\services;

use Exception;

class SmsService
{
    const ENDPOINT = 'https://bulkpush.mytoday.com/BulkSms/SingleMsgApi';
    
    public static function sendOtp($mobile, $otp)
    {
        $password = 'Gv!28@Rp9$Lu&';

        $data = [
            'feedid'     => '394966',
            'username'   => '9990100012',
            'To'         =>  '91' . $mobile,
            'Text'       =>   'GetMyUni: Use OTP ' . $otp . ' to complete verification. Valid for 10 minutes. Do not share',
            'templateid' => '1107175490523889824',
            'senderid'   => 'GMUCOL',
            'short' => '0',   # or '0'
            'async' =>  '0'    # or '0'
        ];

        $query = http_build_query($data, '', '&', PHP_QUERY_RFC3986);

        $postData = 'password=' . $password . '&' . $query;
       
        try {
            $ch = curl_init(self::ENDPOINT);
            curl_setopt_array($ch, [
                CURLOPT_POST           => true,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTPHEADER     => [
                    'Content-Type: application/x-www-form-urlencoded'
                ],
                CURLOPT_POSTFIELDS     => $postData
            ]);
    
            $response = curl_exec($ch);
            $err = curl_error($ch);
            curl_close($ch);
    
           
            $xml = simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOENT | LIBXML_NOERROR | LIBXML_NOWARNING);

            // Get REQID attribute
            $reqid = (string) $xml['REQID'];

            return (!empty($reqid)) ? true : false;
        } catch (Exception $e) {
            return false;
        }
    }

    /** not in Use */
    public static function sendOtpold($mobile, $otp)
    {
        // $url = 'http://sms6.rmlconnect.net:8080/bulksms/bulksms?username=GMUTRANS1&password=gmuuni08&type=0&dlr=0&source=GMUCOL&entityid=1101648740000011506&tempid=';
        // $url .= '&destination=' . $mobile;
        // $url .= '&message=' . urlencode($otp . ', is your OTP from GetMyUni. Please enter the same to proceed.');

        // try {
        //     $parse_url = file($url);

        //     return strpos($parse_url[0], '1701') == 0 ? true : false;
        // } catch (Exception $e) {
        //     return false;
        // }

        $data = [
            'apikey' => 'zAypovr2t0PWQXnQ',
            'senderid'=> 'GMUCOL',
            'number' => '91' . $mobile,
            'message' =>  $otp . ', is your OTP from GetMyUni. Please enter the same to proceed.'
        ];

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, self::ENDPOINT);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
            ]);
            $result = curl_exec($ch);
    
            curl_close($ch);

            $res = json_decode($result);
            
            return ($res->status) == 'OK' ? true : false;
        } catch (Exception $e) {
            return false;
        }
    }
}
