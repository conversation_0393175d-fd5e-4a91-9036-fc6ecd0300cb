<?php

namespace common\services;

use common\models\Article;
use common\models\Category;
use common\models\City;
use common\models\College;
use common\models\HomepageSliders;
use common\models\Review;
use common\models\TrendingPage;

class HomeService
{
    public function getTrendingPageData($entity, $entityId = null, $subPage = false, $limit = null)
    {
        $displayName = [
            'exam' => 'display_name',
            'course' => 'short_name',
            'board' => 'display_name',
            'olympiad' => 'name'
        ];
        
        $query = TrendingPage::find()
            ->alias('tp')
            ->select(['tp.entity', 'tp.entity_id', 'tp.url', 'tp.display_name'])
            ->where(['tp.entity' => $entity, 'tp.status' => TrendingPage::STATUS_ACTIVE])
            ->orderBy(['tp.updated_at' => SORT_DESC])
            ->asArray();

        if (!empty($entityId)) {
            $query->andWhere(['entity_id' => $entityId]);
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        if (!$subPage && empty($entityId) && ($entity !== 'others')) {
            $query->select(['tp.entity', 'tp.entity_id', 'tp.url', 'entityTab.' . $displayName[$entity] . ' as display_name']);
            $query->innerJoin($entity . ' entityTab', 'entityTab.id = tp.entity_id');
            $query->groupBy(['tp.entity_id']);
        } elseif ($subPage && empty($entityId)) {
            return $query->all();
        }

        return $query->all();
    }

    public function homepageNewSlides()
    {
        $result = HomepageSliders::find()->select(['homepage_sliders.image', 'college.name AS display_name', 'homepage_sliders.redirect_link'])
         ->joinWith(College::tableName())
         ->where(['homepage_sliders.status' => 1])
         ->orderBy(['homepage_sliders.position' => SORT_ASC])
         ->asArray()
         ->all();

        return $result;
    }

    public function getTrendingCollegeCities($totalPost = 5)
    {
        $result = City::find()->select(['name', 'slug'])
            ->where(['is_popular' => 1])
            ->orderBy(['position' => SORT_DESC])
            ->limit($totalPost)
            ->all();
        return $result ?? [];
    }

    public function getPopularColleges($totalPost = 5)
    {
        $query = (new \yii\db\Query())
            ->select([
                'college.display_name',
                'college.slug',
                'college.cover_image',
                'college.image',
                'city.name AS cityName',
                'state.name AS stateName'
            ])
            ->from('college')
            ->innerJoin('city', 'city.id = college.city_id')
            ->innerJoin('state', 'state.id = city.state_id')
            ->where(['college.is_popular' => 1, 'college.status' => 1])
            ->andWhere(['is not', 'college.position', null])
            ->orderBy(['college.position' => SORT_ASC])
            ->limit($totalPost);

        $result = $query->all();
        return $result;
    }

    public function getLatestNews($totalPost = 5)
    {
        $query = (new \yii\db\Query())
            ->select(['news.name', 'news.banner_image', 'news.slug', 'news.updated_at'])
            ->from('news_subdomain news')
            ->where(['news.status' => 1])
            ->orderBy(['news.updated_at' => SORT_DESC])
            ->limit($totalPost);
       
        $result = $query->all();
        return $result;
    }

    public function getLatestArticles($totalPost = 10)
    {
        $query = (new \yii\db\Query())
            ->select(['article.title', 'article.cover_image', 'article.slug' , 'user.name', 'user.slug as user_slug'])
            ->from('article')
            ->leftJoin('user', 'user.id = article.author_id')
            ->where(['article.status' => 1])
            ->orderBy(['article.updated_at' => SORT_DESC])
            ->limit($totalPost);

        $result = $query->all();
        return $result;
    }

    public function getSponsoredColleges($totalPost = 20)
    {
        $query = (new \yii\db\Query())
            ->select([
                'college.display_name',
                'college.slug',
                'college.cover_image',
                'college.image',
                'city.name AS cityName',
                'state.name AS stateName'
            ])
            ->from('college')
            ->innerJoin('city', 'city.id = college.city_id')
            ->innerJoin('state', 'state.id = city.state_id')
            ->where(['college.is_sponsored' => 1, 'college.status' => 1])
            ->andWhere(['is not', 'college.position', null])
            ->orderBy(['college.position' => SORT_ASC]);
        
        $result = $query->all();
        return $result;
    }

    public function getCollegeCountbyStream()
    {
        $query = (new \yii\db\Query())
            ->select([
                'stream.name AS stream_name',
                'stream.slug AS stream_slug',
                'COUNT(DISTINCT college_program.college_id) AS college_count'
            ])
            ->from('college')
            ->innerJoin('college_program', 'college_program.college_id = college.id')
            ->innerJoin('course', 'course.id = college_program.course_id')
            ->innerJoin('stream', 'stream.id = course.stream_id')
            ->where(['college.status' => 1])
            ->groupBy(['stream.name', 'stream.slug']);

        $data = $query->all();

        return $data;
    }

    public function getCourseCountbyStream()
    {
        $query = (new \yii\db\Query())
            ->select([
                'stream.name AS stream_name',
                'stream.slug AS stream_slug',
                'COUNT(course.id) AS course_count'
            ])
            ->from('course')
            ->innerJoin('stream', 'stream.id = course.stream_id')
            ->where(['course.status' => 1])
            ->groupBy(['stream.name', 'stream.slug']);

        $result = $query->all();
        return $result;
    }

    public function getExamCountbyStream()
    {
        $query = (new \yii\db\Query())
        ->select([
            'stream.name AS stream_name',
            'stream.slug AS stream_slug',
            'COUNT(DISTINCT exam_course.exam_id) AS exam_count'
        ])
        ->from('exam')
        ->innerJoin('exam_course', 'exam_course.exam_id = exam.id')
        ->innerJoin('course', 'exam_course.course_id = course.id')
        ->innerJoin('stream', 'stream.id = course.stream_id')
        ->where(['exam.status' => 1])
        ->groupBy(['stream.name', 'stream.slug']);
        // ->all();

        $result = $query->all();
        return $result;
    }


    public function getRatingStats()
    {
        return Review::find()
        ->select(['COUNT(id) AS totalRatings'])
        ->scalar();
    }
    
    public function geRreviewStats()
    {
        return $query = (new \yii\db\Query())
        ->select(['COUNT(id) AS totalReviews'])
        ->from('review_content')
        ->where(['>', 'LENGTH(content)', 0])
        ->scalar();
    }
    
    public function getStudentStats()
    {
        return $query = (new \yii\db\Query())
        ->select(['COUNT(DISTINCT email) AS totalStudents'])
        ->from('student')
        ->scalar();
    }
    
    public function getCollegeStats()
    {
        return $query = (new \yii\db\Query())
        ->select(['COUNT(DISTINCT name) AS totalColleges'])
        ->from('college')
        ->scalar();
    }

    public function getQuestionCount()
    {
        return $query = (new \yii\db\Query())
        ->select(['COUNT(id) AS question_count'])
        ->from('forum_question')
        ->scalar();
    }

    public function getHomePageArticles($stream = null, $totalPost = 10, $isPopular = false)
    {
        $query = (new \yii\db\Query())
            ->select(['article.title', 'article.cover_image', 'article.slug', 'user.name'])
            ->from('article')
            ->leftJoin('stream', 'stream.id = article.stream_id')
            ->leftJoin('user', 'user.id = article.author_id')
            ->where(['article.status' => 1]);

        if (!empty($stream)) {
            $query->andWhere(['stream.slug' => $stream]);
        }
                
        if ($isPopular) {
            $query->andWhere(['article.is_popular' => Article::POPULAR_YES]);
        } else {
            $query->andWhere(['article.is_popular' => Article::POPULAR_NO]);
        }

        $query->orderBy(['article.published_at' => SORT_DESC])
        ->limit($totalPost);

        return $query->all();
    }
}
