<?php

namespace common\services;

use common\helpers\DataHelper;
use common\models\College;
use common\models\Degree;
use common\models\Student;
use common\models\StudentActivity;
use common\models\StudentCollegeShortlist;
use common\models\StudentPreference;
use Yii;

class StudentService
{

    //function to store the data for lead form and the student sign in form
    public static function logActivity($student, $request = [], $studentLogin = '')
    {
        if (isset($request['entity']) && !empty($request['entity'])) {
            $entity = 'news';
        } elseif ($studentLogin == 'student-signup') {
            $entity = $studentLogin;
        } else {
            $entity = !empty($request) && isset($request['LeadForm']) && !empty($request['LeadForm']) && !empty($request['LeadForm']['entity']) ? $request['LeadForm']['entity'] : '';
        }

        $utm_source = '';
        $utm_medium = '';

        if (empty($request['LeadForm']['utm_medium'])) {
            $utm_medium = isset(DataHelper::$utmDefaultMedium[$entity]) ? DataHelper::$utmDefaultMedium[$entity]['medium'] : '';
        } else {
            $utm_medium = $request['LeadForm']['utm_medium'];
        }

        if (empty($request['LeadForm']['utm_source'])) {
            $utm_source = isset(DataHelper::$utmDefaultMedium[$entity]) ? DataHelper::$utmDefaultMedium[$entity]['source'] : '';
        } else {
            $utm_source = $request['LeadForm']['utm_source'];
        }

        $studentActivity = new StudentActivity;
        $studentActivity->student_id = $student->id;
        // $studentActivity->url = $request['LeadForm']['url'] ?? $student->source_url;
        if (isset($request['entity']) && !empty($request['entity']) && $request['entity'] == 'news') {
            $studentActivity->url = $request['url'] ?? '';
        } else {
            $studentActivity->url = $request['LeadForm']['url'] ?? $student->source_url;
        }
        $studentActivity->cta_text = !empty($request['LeadForm']) ? (isset($request['LeadForm']['cta_text']) && !empty($request['LeadForm']['cta_text']) ? trim(strip_tags($request['LeadForm']['cta_text'])) : '') : (isset($request['cta_text']) && !empty($request['cta_text']) ? trim(strip_tags($request['cta_text'])) : $studentLogin);
        $studentActivity->cta_position = $request['LeadForm']['cta_location'] ?? ($request['cta_location'] ?? $studentLogin);
        $studentActivity->utm_source = $utm_source;
        $studentActivity->utm_medium = $utm_medium;
        $studentActivity->utm_campaign = $request['LeadForm']['utm_campaign'] ?? ($request['utm_campaign'] ?? '');
        $studentActivity->source = !empty($request) ? (!empty($request['source']) ? $request['source'] : 0) : (!empty($student) ? $student->source : 0);
        $studentActivity->category = $request['LeadForm']['entity_type'] ?? ($request['entity'] ?? 'site');
        $studentActivity->platform = \Yii::$app->devicedetect->isMobile() ? 'wap' : 'web';
        $studentActivity->category_sub_page = $request['LeadForm']['entity_subtype'] ?? ($request['entity_sub_type'] ?? $studentLogin);
        if (isset($request['old_page']) && $request['old_page'] == 1) {
            $studentActivity->source = isset(DataHelper::$leadSource['google_paid_ads']) ? DataHelper::$leadSource['google_paid_ads'] : '';
            $studentActivity->category = $request['lead_entries'];
            $studentActivity->category_sub_page = $request['lead_entries'];
            $studentActivity->cta_text = $request['lead_entries'];
            $studentActivity->cta_position = $request['lead_entries'];
        }

        if ($studentActivity->save() && !empty($request)) {
            if ((isset($request['entity']) && $request['entity'] == 'news') || $studentLogin == Student::STUDENT_CATEGORY) {
                self::logActivityPreferenceNewData($student, $request, $studentActivity);
            } else {
                $stateId = empty($request['LeadForm']['interested_location']) ? null : (new UserService)->getCityId('', $request['LeadForm']['interested_location']);
                if (!isset($request['old_page']) && !isset($request['mobile_lead_news'])) {
                    if (isset($request['LeadForm']) && $request['LeadForm']['entity'] == College::ENTITY_COLLEGE) {
                        $program = empty($request['LeadForm']['interested_course']) ? [] : (new LeadService)->getCourseData($request['LeadForm']['interested_course'], College::ENTITY_COLLEGE);
                        $courseId = empty($program) ? [] : $program['course_id'];
                    } else {
                        $courseId = (!empty($request['LeadForm']) && isset($request['LeadForm']['interested_course'])) ? $request['LeadForm']['interested_course'] : ($request['course'] ?? '');
                    }
                    $highestEducation = !empty($request) ? (!empty($request['LeadForm']['qualification']) ? DataHelper::$highestQualificationIdMapping[$request['LeadForm']['qualification']] : (!empty($request['education_level']) ? DataHelper::$highestQualificationIdMapping[$request['education_level']] : null)) : null;
                } else {
                    if (isset($request['mobile_lead_news'])) {
                        $courseId = $request['course'] ?? '';
                    } else {
                        $courseId = empty($request['LeadForm']['interested_course']) ? (empty($request['LeadForm']['course_interested']) ? ($request['LeadForm']['course'] ?? '') : $request['LeadForm']['course_interested']) : $request['LeadForm']['interested_course'];
                    }
                    $highestEducationByCourseId = empty($courseId) ? [] : (new LeadService)->getCourseData($courseId);
                    $highestEducation = empty($highestEducationByCourseId) ? '' : ($highestEducationByCourseId['highest_qualification'] ?? '');
                }
                if (empty($courseId) && isset($request['LeadForm']['course']) && !empty($request['LeadForm']['course'])) {
                    $courseId = $request['LeadForm']['course'];
                }
                $courseData = empty($courseId) ? [] : (new LeadService)->getCourseData($courseId);
                $degreeId = empty($courseData) && empty($courseData['degree']) ? null : Degree::find()->select('id')->where(['like', 'slug', $courseData['degree']])->one();
                $studentPreference = new StudentPreference();
                $studentPreference->activity_id = $studentActivity->id;
                $studentPreference->student_id = $student->id;
                $studentPreference->interested_city = (!empty($request['LeadForm']) && !empty($request['LeadForm']['interested_location'])) ? $request['LeadForm']['interested_location'] : null;
                $studentPreference->interested_state = (!empty($stateId) && !empty($stateId['stateId'])) ? $stateId['stateId'] : null;
                $studentPreference->course = isset($courseData) && !empty($courseData['parent_id']) ? $courseData['parent_id'] : $courseId;
                $studentPreference->child_course_id = !empty($courseId) && isset($request['LeadForm']) && $request['LeadForm']['entity'] !== College::ENTITY_COLLEGE ? (int) $courseId : '';
                $studentPreference->program_id = $program['program_id'] ?? '';
                $studentPreference->degree = !empty($courseId) && ($courseId == 610) ? null : (empty($degreeId) ? null : $degreeId['id']);
                $studentPreference->highest_qualification = $highestEducation ?? '';
                $studentPreference->distance_education = !empty($request) && !empty($request['distance_education']) ? $request['distance_education'] : 0;
                $studentPreference->specialization = isset($request['LeadForm']) && $request['LeadForm']['entity'] == College::ENTITY_COLLEGE ? $program['specialization_id'] : (isset($courseData) && !empty($courseData['specialization_id']) ? $courseData['specialization_id'] : null);
                if (!empty($request['LeadForm']) && $request['LeadForm']['entity'] == 'exam' || !empty($request['LeadForm']) && $request['LeadForm']['entity'] == 'exam_detail') {
                    $studentPreference->exam = (!empty($request['LeadForm']) && !empty($request['LeadForm']['entity_id'])) ? $request['LeadForm']['entity_id'] : null;
                } else if (!empty($request['LeadForm']) && !empty($request['LeadForm']['college-course-exam']) && $request['LeadForm']['entity'] == 'college') {
                    $studentPreference->exam = $request['LeadForm']['college-course-exam'];
                } else {
                    $studentPreference->exam = null;
                }

                if (!empty($courseData) && !empty($courseData['stream_id'])) {
                    $studentPreference->stream = $courseData['stream_id'] ?? null;
                }

                if ($studentPreference->save()) {
                    if ($studentLogin !== Student::STUDENT_CATEGORY && $studentLogin !== 'news') {
                        if (!empty($request['LeadForm']) && $request['LeadForm']['entity'] == 'college' || ($request['LeadForm']['entity'] == 'all-colleges' && $request['LeadForm']['cta_location'] != 'auto-popup') || $request['LeadForm']['entity'] == 'review') {
                            $studentCollegeShortlist = new StudentCollegeShortlist();
                            $studentCollegeShortlist->student_id = $student->id;
                            $studentCollegeShortlist->activity_id = $studentPreference->activity_id;
                            $studentCollegeShortlist->college_id = $request['LeadForm']['entity'] == 'review' ? $request['LeadForm']['college_id'] : $request['LeadForm']['entity_id'];
                            $studentCollegeShortlist->sponsored = empty($is_sponsorCollege) ? 0 : 1;
                            $studentCollegeShortlist->save();
                        }
                    }
                } else {
                    // return null;
                }
            }
        } else {
            // print_r($studentActivity->getErrors());
        }
    }

    // function to store news mobile student preference
    public function logActivityPreferenceNewData($student, $request, $studentActivity)
    {
        $studentPreference = new StudentPreference();
        $studentPreference->activity_id = $studentActivity->id;
        $studentPreference->student_id = $student->id;
        $studentPreference->interested_city = '';
        $studentPreference->interested_state = '';
        $studentPreference->course = '';
        $studentPreference->child_course_id = '';
        $studentPreference->program_id = '';
        $studentPreference->degree = '';
        $studentPreference->highest_qualification = '';
        $studentPreference->distance_education = 0;
        $studentPreference->specialization = '';
        $studentPreference->exam = null;
        $studentPreference->stream = $request['stream'] ?? null;
        $studentPreference->level = $request['level'] ?? null;

        if ($studentPreference->save()) {
        } else {
            throw new \Exception('Validation failed: ' . json_encode($studentPreference->getErrors()));
        }
    }

    /* Function for save student preference and college from clp form */
    public function logActivityPreference($student, $request = [], $studentLogin = '')
    {
        $utm_source = '';
        $utm_medium = '';

        if (empty($request['LeadForm']['utm_medium'])) {
            $utm_medium = isset(DataHelper::$utmDefaultMedium['clp']) ? DataHelper::$utmDefaultMedium['clp']['medium'] : '';
        } else {
            $utm_medium = $request['LeadForm']['utm_medium'];
        }

        if (empty($request['LeadForm']['utm_source'])) {
            $utm_source = isset(DataHelper::$utmDefaultMedium['clp']) ? DataHelper::$utmDefaultMedium['clp']['source'] : '';
        } else {
            $utm_source = $request['LeadForm']['utm_source'];
        }

        $studentActivity = new StudentActivity;
        $studentActivity->student_id = $student->id;
        $studentActivity->url = $request['LeadForm']['url'] ?? $student->source_url;
        $studentActivity->cta_text = !empty($request['LeadForm']) ? (isset($request['LeadForm']['cta_text']) && !empty($request['LeadForm']['cta_text']) ? trim(strip_tags($request['LeadForm']['cta_text'])) : '') : (isset($request['cta_text']) && !empty($request['cta_text']) ? trim(strip_tags($request['cta_text'])) : $studentLogin);
        $studentActivity->cta_position = $request['LeadForm']['cta_location'] ?? ($request['cta_location'] ?? $studentLogin);
        $studentActivity->utm_source = $utm_source;
        $studentActivity->utm_medium = $utm_medium;
        $studentActivity->utm_campaign = $request['LeadForm']['utm_campaign'] ?? ($request['utm_campaign'] ?? '');
        $studentActivity->source = !empty($request) ? (!empty($request['source']) ? $request['source'] : 0) : (!empty($student) ? $student->source : 0);
        $studentActivity->category = $request['LeadForm']['entity_type'] ?? ($request['entity'] ?? 'site');
        $studentActivity->category_sub_page = $request['LeadForm']['entity_subtype'] ?? ($request['entity_sub_type'] ?? $studentLogin);
        if (isset($request['old_page']) && $request['old_page'] == 1) {
            $studentActivity->source = isset(DataHelper::$leadSource['google_paid_ads']) ? DataHelper::$leadSource['google_paid_ads'] : '';
            $studentActivity->category = $request['lead_entries'];
            $studentActivity->category_sub_page = $request['lead_entries'];
            $studentActivity->cta_text = $request['lead_entries'];
            $studentActivity->cta_position = $request['lead_entries'];
        }

        if ($studentActivity->save() && !empty($request)) {
            $stateId = empty($request['LeadForm']['interested_location']) ? null : (new UserService)->getCityId('', $request['LeadForm']['interested_location']);
            $courseId = empty($request['LeadForm']['interested_course']) ? (empty($request['LeadForm']['course_interested']) ? ($request['LeadForm']['course'] ?? '') : $request['LeadForm']['course_interested']) : $request['LeadForm']['interested_course'];
            $highestEducationByCourseId = empty($courseId) ? [] : (new LeadService)->getCourseData($courseId);
            if (isset($highestEducationByCourseId['highest_qualification']) && !empty($highestEducationByCourseId)) {
                $highestEducation = empty($highestEducationByCourseId) ? '' : ($highestEducationByCourseId['highest_qualification'] ?? '');
            }
        } else {
            print_r($studentActivity->getErrors());
            throw new \Exception('Validation failed: ' . json_encode($studentActivity->getErrors()));
        }
        $courseData = empty($courseId) ? [] : (new LeadService)->getCourseData($courseId);
        // if (isset(DataHelper::$mapCLPHighestLevel[$highestEducation]) && !empty(DataHelper::$mapCLPHighestLevel[$highestEducation])) {
        //     $level = DataHelper::$mapCLPHighestLevel[$highestEducation];
        // } else {
        //     $level = '';
        // }
        $degreeId = empty($courseData) && empty($courseData['degree']) ? null : Degree::find()->select('id')->where(['like', 'slug', $courseData['degree']])->one();
        $studentPreference = new StudentPreference();
        $studentPreference->activity_id = $studentActivity->id;
        $studentPreference->student_id = $student->id;
        $studentPreference->interested_city = (!empty($request['LeadForm']) && !empty($request['LeadForm']['interested_location'])) ? $request['LeadForm']['interested_location'] : null;
        $studentPreference->interested_state = (!empty($stateId) && !empty($stateId['stateId'])) ? $stateId['stateId'] : null;
        $studentPreference->course = isset($courseData) && !empty($courseData['parent_id']) ? $courseData['parent_id'] : $courseId;
        $studentPreference->child_course_id = !empty($courseId) && isset($request['LeadForm']) && $request['LeadForm']['entity'] !== College::ENTITY_COLLEGE ? (int) $courseId : '';
        $studentPreference->program_id = $program['program_id'] ?? '';
        $studentPreference->degree = !empty($courseId) && ($courseId == 610) ? null : (empty($degreeId) ? null : $degreeId['id']);
        $studentPreference->highest_qualification = $highestEducation ?? '';
        // $studentPreference->level = $level ?? '';
        $studentPreference->level = !empty($courseId) && ($courseId == 610) ? null : (empty($degreeId) ? null : $degreeId['id']);
        $studentPreference->distance_education = !empty($request) && !empty($request['distance_education']) ? $request['distance_education'] : 0;
        $studentPreference->specialization =  (isset($courseData) && !empty($courseData['specialization_id']) ? $courseData['specialization_id'] : null);
        if (!empty($request['LeadForm']) && !empty($request['LeadForm']['exam_id'])) {
            $studentPreference->exam = $request['LeadForm']['exam_id'];
        } else {
            $studentPreference->exam = null;
        }

        if (!empty($courseData) && !empty($courseData['stream_id'])) {
            $studentPreference->stream = $courseData['stream_id'] ?? null;
        }

        if (!empty($request['LeadForm'])) {
            if ($studentPreference->save()) {
            } else {
                print_r($studentPreference->getErrors());
                throw new \Exception('Validation failed: ' . json_encode($studentPreference->getErrors()));
            }
        }

        if (!empty($request['LeadForm']['entity_id'])) {
            $studentCollegeShortlist = new StudentCollegeShortlist();
            $studentCollegeShortlist->student_id = $student->id;
            $studentCollegeShortlist->activity_id = $studentActivity->id;
            $studentCollegeShortlist->college_id = !empty($request['LeadForm']['entity_id']) ? $request['LeadForm']['entity_id'] : '';
            $studentCollegeShortlist->sponsored = empty($is_sponsorCollege) ? 0 : 1;
            $studentCollegeShortlist->save();
        }
    }

    //function to store the data for swipe pages
    public static function logSwipePageActivity($student, $request = [])
    {
        $utm_source = '';
        $utm_medium = '';
        $utm_campaign = '';
        $utm_id = '';
        $utm_term = '';

        $category = 'clp';
        if (empty($request['LeadForm']['utm_medium'])) {
            $utm_medium = isset(DataHelper::$utmDefaultMedium['swipe-pages']) ? DataHelper::$utmDefaultMedium['swipe-pages']['medium'] : '';
        } else {
            $utm_medium = $request['LeadForm']['utm_medium'];
        }

        if (empty($request['LeadForm']['utm_source'])) {
            $utm_source = isset(DataHelper::$utmDefaultMedium['swipe-pages']) ? DataHelper::$utmDefaultMedium['swipe-pages']['source'] : '';
        } else {
            $utm_source = $request['LeadForm']['utm_source'];
        }

        if (empty($request['LeadForm']['utm_campaign'])) {
            $utm_campaign = '';
        } else {
            $utm_campaign = $request['LeadForm']['utm_campaign'];
        }

        if (empty($request['LeadForm']['utm_id'])) {
            $utm_id = '';
        } else {
            $utm_id = $request['LeadForm']['utm_id'];
        }

        if (empty($request['LeadForm']['utm_term'])) {
            $utm_term = '';
        } else {
            $utm_term = $request['LeadForm']['utm_term'];
        }

        $studentActivity = new StudentActivity;
        $studentActivity->student_id = $student->id;
        $studentActivity->url = $request['LeadForm']['url'] ?? '';
        $studentActivity->utm_source = $utm_source;
        $studentActivity->utm_medium = $utm_medium;
        $studentActivity->utm_campaign = $utm_campaign;
        $studentActivity->utm_id = $utm_id;
        $studentActivity->utm_term = $utm_term;
        // $studentActivity->source = isset(DataHelper::$leadSource['google_paid_ads']) ? DataHelper::$leadSource['google_paid_ads'] : ;
        $studentActivity->source  = 0;
        $studentActivity->category = $category;
        $studentActivity->category_sub_page = $category;
        $studentActivity->cta_text = $category;
        $studentActivity->cta_position = $category;

        if ($studentActivity->save() && !empty($request)) {
            $studentPreference = new StudentPreference();
            $studentPreference->activity_id = $studentActivity->id;
            $studentPreference->student_id = $student->id;
            $studentPreference->interested_city = $student->current_city;
            $studentPreference->interested_state = $student->current_state;
            $studentPreference->course = !empty($request['LeadForm']['interested_course']) ? $request['LeadForm']['interested_course'] : null;
            $studentPreference->child_course_id = !empty($request['LeadForm']['child_course']) ? $request['LeadForm']['child_course'] : null;
            $studentPreference->program_id = null;
            $studentPreference->degree = !empty($request['LeadForm']['degree']) ? $request['LeadForm']['degree'] : null;
            $studentPreference->level = !empty($request['LeadForm']['degree']) ? $request['LeadForm']['degree'] : null;
            $studentPreference->highest_qualification = !empty($request['LeadForm']['highest_qualification']) ? $request['LeadForm']['highest_qualification'] : null;
            $studentPreference->distance_education = 0;
            $studentPreference->specialization = isset($request['LeadForm']) && !empty($request['LeadForm']['specialization_id']) ? $request['LeadForm']['specialization_id'] : null;
            $studentPreference->exam = !empty($request['LeadForm']) && !empty($request['LeadForm']['exam']) ? $request['LeadForm']['exam'] : null;
            $studentPreference->stream = !empty($request['LeadForm']['stream']) ? $request['LeadForm']['stream'] : null;

            if ($studentPreference->save()) {
                if (!empty($request['LeadForm']) && isset($request['LeadForm']['college']) && !empty($request['LeadForm']['college'])) {
                    $studentCollegeShortlist = new StudentCollegeShortlist();
                    $studentCollegeShortlist->student_id = $student->id;
                    $studentCollegeShortlist->activity_id = $studentPreference->activity_id;
                    $studentCollegeShortlist->college_id = $request['LeadForm']['college'] ?? null;
                    $studentCollegeShortlist->sponsored = empty($is_sponsorCollege) ? 0 : 1;
                    $studentCollegeShortlist->save();
                }
            } else {
                throw new \Exception('Validation failed: ' . json_encode($studentPreference->getErrors()));
            }
        } else {
            throw new \Exception('Validation failed: ' . json_encode($studentActivity->getErrors()));
        }
    }

    //function to store the data for google ads lead
    public static function logGoogleAdLeadActivity($student, $request = [])
    {
        $studentActivity = new StudentActivity;
        $studentActivity->student_id = $student->id;
        $studentActivity->url = (string) $request['LeadForm']['url'] ?? '';
        $studentActivity->utm_source = (string) $request['LeadForm']['utm_source'];
        $studentActivity->utm_medium = (string) $request['LeadForm']['utm_medium'];
        $studentActivity->utm_campaign = (string) $request['LeadForm']['utm_campaign'];
        $studentActivity->utm_term = (string) $request['LeadForm']['utm_term'];
        $studentActivity->source = isset(DataHelper::$leadSource['google_ads_lead']) ? DataHelper::$leadSource['google_ads_lead'] : '';
        $studentActivity->category = $request['lead_entries'];
        $studentActivity->category_sub_page = $request['lead_entries'];
        $studentActivity->cta_text = $request['lead_entries'];
        $studentActivity->cta_position = $request['lead_entries'];

        if ($studentActivity->save() && !empty($request)) {
            $studentPreference = new StudentPreference();
            $studentPreference->activity_id = $studentActivity->id;
            $studentPreference->student_id = $student->id;
            $studentPreference->interested_city = null;
            $studentPreference->interested_state = null;
            $studentPreference->course = !empty($request['LeadForm']['interested_course']) ? $request['LeadForm']['interested_course'] : null;
            $studentPreference->child_course_id = !empty($request['LeadForm']['child_course']) ? $request['LeadForm']['child_course'] : null;
            $studentPreference->program_id = null;
            $studentPreference->degree = null;
            $studentPreference->level = !empty($request['LeadForm']['degree']) ? $request['LeadForm']['degree'] : null;
            $studentPreference->highest_qualification = !empty($request['LeadForm']['highest_qualification']) ? $request['LeadForm']['highest_qualification'] : null;
            $studentPreference->distance_education = 0;
            $studentPreference->specialization = isset($request['LeadForm']) && !empty($request['LeadForm']['specialization_id']) ? $request['LeadForm']['specialization_id'] : null;
            $studentPreference->exam = !empty($request['LeadForm']) && !empty($request['LeadForm']['exam']) ? $request['LeadForm']['exam'] : null;
            $studentPreference->stream = !empty($request['LeadForm']['stream']) ? $request['LeadForm']['stream'] : null;

            if ($studentPreference->save()) {
            } else {
                throw new \Exception('Validation failed: ' . json_encode($studentPreference->getErrors()));
            }
        } else {
            throw new \Exception('Validation failed: ' . json_encode($studentActivity->getErrors()));
        }
    }

    public static function updateStudent($request, $lead, $studentActivity = '', $payload = [])
    {
        if ($studentActivity == 'google-ads-lead') {
            $mobileNumber = substr($request['LeadForm']['mobile'], -10);
        } else {
            $mobileNumber = $request['LeadForm']['mobile'];
        }

        $student = Student::findByPhone($mobileNumber);
        $userLocation = DataHelper::getUserLocation();
        $cleanedStudentName = preg_replace('/[^A-Za-z0-9\ ]/', '', $request['LeadForm']['name']);
        $source = '';
        $stateId = '';
        $is_mobile_verified = Student::IS_MOBILE_VERIFIED_YES;

        if (empty($cleanedStudentName)) {
            //send payload to the dsa log table
            $logsData = LeadService::saveDsaWebHookLogs($payload, $mobileNumber, $request, 500);
            return false;
        }

        if (isset($request['old_page']) && $request['old_page'] == 1) {
            // $source = isset(DataHelper::$leadSource['google_paid_ads']) ? DataHelper::$leadSource['google_paid_ads'] : '';
            $source = isset(DataHelper::$leadSource['organic']) ? DataHelper::$leadSource['organic'] : 0;
            $is_mobile_verified = Student::IS_MOBILE_VERIFIED_NO;
            // $state = !empty($request['LeadForm']['current_location']) ? (new UserService)->getCityId('', $request['LeadForm']['current_location'])['stateId'] : null;
            if (!empty($request['LeadForm']['current_state'])) {
                $stateId = $request['LeadForm']['current_state'];
            } else {
                $stateId = !empty($request['LeadForm']['current_location']) ? (new UserService)->getCityId('', $request['LeadForm']['current_location'])['stateId'] : null;
            }
        } elseif (isset($request['old_page']) && $request['old_page'] == 2) {
            // $source = isset(DataHelper::$leadSource['google_ads_lead']) ? DataHelper::$leadSource['google_ads_lead'] : '';
            $source = isset(DataHelper::$leadSource['organic']) ? DataHelper::$leadSource['organic'] : 0;
            $is_mobile_verified = Student::IS_MOBILE_VERIFIED_NO;
            $stateId = !empty($request['LeadForm']) && !empty($request['LeadForm']['current_state']) ? $request['LeadForm']['current_state'] : '';
        } else {
            $source = isset(DataHelper::$leadSource['organic']) ? DataHelper::$leadSource['organic'] : 0;
            $is_mobile_verified = Student::IS_MOBILE_VERIFIED_YES;
            $stateId = !empty($request['LeadForm']['current_location']) ? (new UserService)->getCityId('', $request['LeadForm']['current_location'])['stateId'] : null;
        }

        if (empty($student)) {
            $studentNew = new Student;
            $studentNew->phone = $mobileNumber;
            $studentNew->name = $cleanedStudentName;
            $studentNew->email = $request['LeadForm']['email'];
            $studentNew->current_city = $request['LeadForm']['current_location'];
            $studentNew->current_state = $stateId;
            $studentNew->source_url = (string) $request['LeadForm']['url'];
            $studentNew->source = $source;
            $studentNew->is_mobile_verified = $is_mobile_verified;
            $studentNew->current_city_ip = !empty($userLocation) && !empty($userLocation['cityId']) ? $userLocation['cityId'] : null;
            if (isset($request['old_page']) && ($request['old_page'] == 1 || $request['old_page'] == 2)) {
                $studentNew->user_type = $request['user_type'];
            }

            if ($studentNew->save()) {
                if (!empty($lead)) {
                    $lead->user_id = $studentNew->id;
                    $lead->save();
                }

                if ($studentActivity == 'clp-preference') {
                    self::logActivityPreference($studentNew, $request, $studentActivity);
                } elseif ($studentActivity == 'swipe-pages') {
                    self::logSwipePageActivity($studentNew, $request);
                } elseif ($studentActivity == 'google-ads-lead') {
                    self::logGoogleAdLeadActivity($studentNew, $request);
                } elseif ($studentActivity !== 'sign-in' && $studentActivity !== 'clp-preference') {
                    self::logActivity($studentNew, $request, $studentActivity);
                }
                Yii::$app->user->login($studentNew, 3600 * 24 * 30);
            } else {
                if ($studentActivity == 'google-ads-lead') {
                    //send payload to the dsa log table
                    $logsData = LeadService::saveDsaWebHookLogs($payload, $mobileNumber, $request, 500);
                    return false;
                } else {
                    throw new \Exception('Validation failed: ' . json_encode($studentNew->getErrors()) . ' ' . $studentActivity);
                }
            }
        } else {
            $student->name = $cleanedStudentName;
            $student->email = $request['LeadForm']['email'];
            $student->current_city = $request['LeadForm']['current_location'];
            $student->current_state = $stateId;
            $student->is_mobile_verified = Student::IS_MOBILE_VERIFIED_YES;
            $student->current_city_ip = !empty($userLocation) && !empty($userLocation['cityId']) ? $userLocation['cityId'] : null;

            if ($student->save()) {
            } else {
                if ($studentActivity == 'google-ads-lead') {
                    //send payload to the dsa log table
                    $logsData = LeadService::saveDsaWebHookLogs($payload, $mobileNumber, $request, 500);
                    return false;
                } else {
                    print_r($student->getErrors());
                    throw new \Exception('Validation failed: ' . json_encode($student->getErrors()) . ' ' . $studentActivity);
                }
            }
            if (!empty($lead)) {
                $lead->user_id = $student->id;
                $lead->save();
            }
            if ($studentActivity == 'clp-preference') {
                self::logActivityPreference($student, $request, $studentActivity);
            } elseif ($studentActivity == 'swipe-pages') {
                self::logSwipePageActivity($student, $request);
            } elseif ($studentActivity == 'google-ads-lead') {
                self::logGoogleAdLeadActivity($student, $request);
            } elseif ($studentActivity !== 'sign-in' && $studentActivity !== 'clp-preference') {
                self::logActivity($student, $request, $studentActivity);
            }
        }
    }

    public static function newUpdateStudent($request, $lead, $studentActivity = '')
    {
        $student = Student::findByPhone($request['LeadForm']['mobile']);
        $userLocation = DataHelper::getUserLocation();
        $cleanedStudentName = preg_replace('/[^A-Za-z0-9\ ]/', '', $request['LeadForm']['name']);

        if (empty($cleanedStudentName)) {
            return 'Name is Invalid';
        }

        if (empty($student)) {
            $studentNew = new Student;
            $studentNew->phone = $request['LeadForm']['mobile'];
            $studentNew->name = $cleanedStudentName;
            $studentNew->email = $request['LeadForm']['email'];
            $studentNew->current_city = $request['LeadForm']['current_location'];
            $studentNew->current_state = (new UserService)->getCityId('', $request['LeadForm']['current_location'])['stateId'];
            $studentNew->source_url = $request['LeadForm']['url'];
            $studentNew->source = 0;
            $studentNew->current_city_ip = !empty($userLocation) && !empty($userLocation['cityId']) ? $userLocation['cityId'] : null;
            if ($studentNew->validate() && $studentNew->save()) {
                $lead->user_id = $studentNew->id;
                $lead->save();
                if ($studentActivity !== 'sign-in') {
                    self::logActivity($studentNew, $request, $studentActivity);
                }
                //Yii::$app->user->login($studentNew, 3600 * 24 * 30);
            } else {
                throw new \Exception('Validation failed: ' . json_encode($studentNew->getErrors()) . ' ' . $studentActivity);
            }
        } else {
            $student->name = $cleanedStudentName;
            $student->email = $request['LeadForm']['email'];
            $student->current_city = $request['LeadForm']['current_location'];
            $student->current_state = (new UserService)->getCityId('', $request['LeadForm']['current_location'])['stateId'];
            $student->current_city_ip = !empty($userLocation) && !empty($userLocation['cityId']) ? $userLocation['cityId'] : null;
            // $student->source_url = $request['LeadForm']['url'];
            $student->save();
            $lead->user_id = $student->id;
            $lead->save();
            if ($studentActivity !== 'sign-in') {
                self::logActivity($student, $request, $studentActivity);
            }
        }
    }


    //activity for whatsapp integration
    public static function createStudentActivity($student, $text)
    {
        $studentActivity = new StudentActivity();
        $studentActivity->student_id = $student->id;
        if (!empty($request['student_activity_parent_id']) && $request['student_activity_parent_id'] != 'null') {
            $studentActivity->parent_id = $request['student_activity_parent_id'];
        }
        $studentActivity->url = $text;
        $studentActivity->cta_text = $text;
        $studentActivity->cta_position = $text;
        $studentActivity->platform = '';
        $studentActivity->utm_source = 'getmyuni';
        $studentActivity->utm_medium = 'LW12';
        $studentActivity->utm_campaign = '';
        $studentActivity->source = Student::SOURCE_ORGANIC;
        $studentActivity->category = $text;
        $studentActivity->category_sub_page = $text;

        if ($studentActivity->save()) {
            // (new LeadService)->sendLeadsToCld($student, [], $studentActivity, []);
        } else {
            throw new \Exception('Validation failed: ' . json_encode($studentActivity->getErrors()) . ' WhatsApp');
        }
    }
}
