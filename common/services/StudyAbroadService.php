<?php

namespace common\services;

use common\helpers\DataHelper;
use common\models\Article;
use common\models\Country;
use common\models\SaCollege;
use common\models\SaCollegeFeesDetail;
use common\models\SaCollegeSubpage;
use common\models\SaCollegeSubpageContent;
use common\models\SaCollegeSubpageSidemenu;
use common\models\SaCollegeSubpageSidemenuDetail;
use common\models\SaCountry;
use common\models\SaCountryDetail;
use common\models\SaFaq;
use yii\db\Query;
use yii\helpers\Json;

class StudyAbroadService
{
    public function getArticleCountryDetail($countrySlug, $slug)
    {
        return Article::find()
            ->where(['country_slug' => $countrySlug])
            ->andWhere(['slug' => $slug])
            ->active()
            ->one();
    }

    public function getTrendingArticle($country_slug, $limit = 5)
    {
        $article = Article::find()
            ->where(['is_popular' => Article::POPULAR_YES])
            ->andWhere(['status' => Article::STATUS_ACTIVE])
            ->andWhere(['country_slug' => $country_slug])
            ->limit($limit)
            ->orderBy(['updated_at' => SORT_DESC]);

        return $article->all();
    }

    public function getLatestArticle($country_slug, $limit = 5)
    {
        return Article::find()
            ->where(['country_slug' => $country_slug])
            ->andWhere(['status' => Article::STATUS_ACTIVE])
            ->limit($limit)
            ->orderBy(['updated_at' => SORT_DESC])
            ->all();
    }

    public function getCountry($countrySlug)
    {
        return Country::find()->where(['slug' => $countrySlug])->one();
    }

    public function getSaCountry($countrySlug = '', $countryId = '')
    {
        if (!empty($countrySlug)) {
            return SaCountry::find()->where(['slug' => $countrySlug])->one();
        }
        if (!empty($countryId)) {
            return SaCountry::find()->where(['id' => $countryId])->one();
        }
    }

    public function getCountryId($countrySlug)
    {
        return SaCountry::find()->where(['slug' => $countrySlug])->one();
    }

    public function getCountryDetail($countryId)
    {
        return SaCountryDetail::find()->where(['sa_country_id' => $countryId])->one();
    }

    public function getColleges($countryId, $limit = 10, $offset = 0)
    {
        $query = (new Query())
            ->select([
                'college_name' => 'c.name',
                'slug' => 'c.slug',
                'logo' => 'c.logo_image',
                'banner' => 'c.cover_image',
                'location' => 'c.location',
                'Undergraduate' => 'SUM(CASE WHEN cd.sa_degree_id = 3 THEN 1 ELSE 0 END)',
                'Postgraduate' => 'SUM(CASE WHEN cd.sa_degree_id = 2 THEN 1 ELSE 0 END)',
                'Doctorate' => 'SUM(CASE WHEN cd.sa_degree_id = 1 THEN 1 ELSE 0 END)',
            ])
            ->from('sa_college c')
            ->innerJoin('sa_course_detail cd', 'c.id = cd.sa_college_id')
            ->where([
                'c.sa_country_id' => $countryId,
                'c.status' => SaCollege::STATUS_ACTIVE,
            ])
            ->andWhere(['cd.sa_degree_id' => [1, 2, 3]]) // doctarate, master, bachelor
            ->groupBy('c.id')
            ->limit($limit)->offset($offset);

        $colleges = $query->all();

        return $colleges ?? [];
    }

    public function getFaqs($entity, $entityId)
    {
        $faqs = SaFaq::find()
            ->select(['qnas'])
            ->where(['entity' => $entity, 'entity_id' => $entityId, 'status' => SaFaq::STATUS_ACTIVE])
            ->asArray()
            ->all();

        if (empty($faqs)) {
            return [];
        }

        $result = [];
        $index = 1;

        foreach ($faqs as $faq) {
            if (empty($faq['qnas'])) {
                continue; // Skip empty entries
            }
            $qnas = Json::decode($faq['qnas'], false);
            if (!is_array($qnas)) {
                continue;
            }
            foreach ($qnas as $qna) {
                $result[$index++] = $qna;
            }
        }

        return $result ?? [];
    }

    public function getCollegeDetail($countryId, $collegeSlug)
    {
        if (empty($collegeSlug)) {
            return [];
        }

        $college = SaCollege::find()
            ->where(['slug' => $collegeSlug])
            ->andWhere(['sa_country_id' => $countryId])
            ->andWhere(['status' => SaCollege::STATUS_ACTIVE])
            ->one();

        return $college ?? [];
    }

    public function getCollegeFees($collegeId)
    {
        $collegeFees = SaCollegeFeesDetail::find()
            ->select(['fees', 'type'])
            ->where(['sa_college_id' => $collegeId])
            ->indexBy('type')
            ->asArray()
            ->all();

        return $collegeFees ?? [];
    }

    public function getMenu(SaCollege $college)
    {
        $saCollegeSubpages = SaCollegeSubpageContent::find()
            ->joinWith(['saCollegeSubpage saSubpage'])
            ->select(['saSubpage.slug AS subpage_slug', 'saSubpage.name AS subpage_name', 'sa_college_subpage_content.status'])
            ->where(['sa_college_id' => $college->id])
            ->andWhere(['sa_college_subpage_content.status' => SaCollegeSubpageContent::STATUS_ACTIVE])
            ->indexBy('subpage_slug')
            ->asArray()
            ->all();

        $menus = [];

        foreach ($saCollegeSubpages as $subpage) {
            $menus[$subpage['subpage_slug']] = $subpage['subpage_name'];
        }

        $orderByArray = [
            'overview',
            'programs-tuition',
            'admission',
            'ranking'
        ];

        $finalMenu = [];
        foreach ($orderByArray as $slug) {
            if (isset($menus[$slug])) {
                $finalMenu[$slug] = $menus[$slug];
            }
        }

        $finalMenu = array_merge($finalMenu, array_diff_key($menus, $finalMenu));

        return $finalMenu;
    }

    public function getSubpageSidemenu($collegeId, $pageId)
    {
        $sideMenu = SaCollegeSubpageSidemenuDetail::find()
            ->joinWith(['saCollegeSubpageSidemenu sidemenu'])
            ->where(['sa_college_id' => $collegeId])
            ->andWhere(['sa_college_subpage_id' => $pageId])
            ->andWhere(['sa_college_subpage_sidemenu_detail.status' => SaCollegeSubpageSidemenuDetail::STATUS_ACTIVE])
            ->indexBy('saCollegeSubpageSidemenu.slug')
            ->all();

        return $sideMenu ?? [];
    }

    public function getSubpageContent($collegeId, $pageId)
    {
        $subpageContent = SaCollegeSubpageContent::find()
            ->where(['sa_college_id' => $collegeId])
            ->andWhere(['sa_college_subpage_id' => $pageId])
            ->andWhere(['status' => SaCollegeSubpageContent::STATUS_ACTIVE])
            ->one();

        return $subpageContent;
    }

    public function getPageId($page)
    {
        return SaCollegeSubpage::find()->where(['slug' => $page])->one();
    }

    public function getSaCourses($collegeId, $degree = null, $stream = null, $course = null, $specialization = null)
    {
        $query = (new \yii\db\Query())
            ->select([
                'd.name as degree_name',
                's.name as stream_name',
                'c.name as course_name',
                'sp.name as specialization_name',
                'scd.total_fees',
                'scd.duration',
                'scd.duration_type',
                'scdt.name as duration_type_name',
                'd.slug as degree_slug',
                's.slug as stream_slug',
                'c.slug as course_slug',
            ])
            ->from('sa_course_detail scd')
            ->leftJoin('sa_degree d', 'd.id = scd.sa_degree_id')
            ->leftJoin('sa_stream s', 's.id = scd.sa_stream_id')
            ->leftJoin('sa_course c', 'c.id = scd.sa_course_id')
            ->leftJoin('sa_specialization sp', 'sp.id = scd.sa_specialization_id')
            ->leftJoin('sa_course_duration_type scdt', 'scdt.id = scd.sa_course_duration_type_id')
            ->where(['scd.sa_college_id' => $collegeId]);

        if (!empty($degree)) {
            $query->andWhere(['scd.sa_degree_id' => $degree]);
        }
        if (!empty($stream)) {
            $query->andWhere(['scd.sa_stream_id' => $stream]);
        }
        if (!empty($course)) {
            $query->andWhere(['scd.sa_course_id' => $course]);
        }
        if (!empty($specialization)) {
            $query->andWhere(['scd.sa_specialization_id' => $specialization]);
        }
        $query->andWhere(['IS NOT', 'scd.sa_specialization_id', null])
            ->groupBy(['scd.sa_stream_id', 'scd.sa_course_id', 'scd.sa_specialization_id', 'scd.sa_degree_id']);

        $results = $query->all();

        return $results;
    }

    public function getSaCoursesDetail($courses)
    {
        if (empty($courses)) {
            return [];
        }

        $degreeStreamArray = [];
        $headCard = [];
        $unique_degrees = [];

        foreach ($courses as $course) {
            $courseName = $course['course_name'];
            $degreeName = $course['degree_name'];
            $streamName = $course['stream_name'] ?? '';
            $streamSlug = $course['stream_slug'] ?? '';
            $degreeSlug = $course['degree_slug'] ?? '';
            $specializationName = $course['specialization_name'] ?? '';
            $unique_degrees = array_unique(array_column($courses, 'degree_name', 'degree_slug'));
            ;

            if (!isset($degreeStreamArray[$degreeSlug])) {
                $degreeStreamArray[$degreeSlug] = [];
            }
            if ($streamSlug && !in_array($streamSlug, $degreeStreamArray[$degreeSlug])) {
                $degreeStreamArray[$degreeSlug][] = $streamSlug;
            }
            if (!isset($headCard[$courseName])) {
                $duration = array_flip(DataHelper::$saDurationType);
                $headCard[$courseName] = [
                    'class' => '',
                    'HeadOne' => $courseName,
                    'course_slug' => $course['course_slug'],
                    'stream' => $streamName,
                    'Duration' => $course['duration'] . ' ' . (isset($duration[$course['duration_type']]) ? $duration[$course['duration_type']] : ''),
                    'Type' => $course['duration_type_name'],
                    'degree' => $degreeName,
                    'Application Deadline' => '',
                    'degree_slug' => $course['degree_slug'],
                    'stream_slug' => $streamSlug,
                ];
            }

            $streamchain = $degreeSlug . $streamSlug;

            $headCard[$courseName]['class'] .= $streamchain . ' ' . $streamchain . '_HeadoneBox ';
            ;

            $headCard[$courseName]['Tuition per year'][] = $course['total_fees'];

            if ($specializationName) {
                $specializationKey = $course['specialization_name'];
                if (!isset($headCard[$courseName]['specialisations'][$specializationKey])) {
                    $headCard[$courseName]['specialisations'][$specializationKey] = [
                        'class' => $course['degree_slug'] . $course['stream_slug'] . '_htbutton',
                        'degree' => $degreeName,
                        'degree_slug' => $course['degree_slug'],
                        'stream_slug' => $streamSlug,
                        'stream' => $streamName,
                        'headone' => $courseName,
                        'headtwo' => $specializationName,
                        'duration' => $course['duration'] . ' ' . $course['duration_type'],
                        'headonechain' => $course['degree_slug'] . $course['stream_slug'] . $course['course_slug'],
                        'streamchain' => $course['degree_slug'] . $course['stream_slug'],
                        'headone_slug' => $course['course_slug'],
                    ];
                }
            }
        }


        return [
            'degreeStreamArray' => $degreeStreamArray,
            'headCard' => $headCard,
            'only_degree_array' => $unique_degrees
        ];
    }

    public function getTopCollegeRecommendations($collegeId, $limit)
    {
        $colleges = SaCollege::find()
            ->where(['not', ['id' => $collegeId]])
            ->andWhere(['status' => SaCollege::STATUS_ACTIVE])
            ->limit($limit)
            ->orderBy(new \yii\db\Expression('RAND()'))
            ->all();

        return $colleges;
    }

    public static function getSaUsers($collegSlug)
    {
        $query = new Query();
        $query->select(['gau.name'])
            ->from('gmu_admin_users gau')
            ->leftJoin('gmu_sa_colleges as gsc', 'gsc.updated_by = gau.id')
            ->where(['gsc.college_slug' => $collegSlug]);

        $user = $query->one(\Yii::$app->gmudb);
        return $user;
    }
}
