<?php


namespace common\services;

use common\components\HttpService;
use common\models\Course;
use common\models\Student;
use common\helpers\DataHelper;
use common\models\Board;
use common\models\CldSsoLog;
use common\models\College;
use common\models\DsaWebhookLog;
use common\models\LeadGmailDomain;
use common\models\StudentAcademicDetials;
use frontend\controllers\LeadV2Controller;
use frontend\controllers\UserProfileController;
use Yii;

class LeadService extends HttpService
{
    // const ENDPOINT = 'https://sso.collegedekho.com/api/users/?lms=true';
    const ENDPOINT = 'https://sso-ms.collegedekho.com/api/users/?lms=true';
    const APIKEY = 'Token 2961f0aa05951161b5fbd69b936e33230735027c';
    const BUSINESS_UNIT = 2;
    const OBJECT_TYPE = 'institute';
    const YEAR = 2025;

    /*
    Function for send data to cld sso
    */
    public function sendLeadsToCld($studentDetail, $studentPreference, $studentActivity, $leadFollowUpFormData = [])
    {
        $name = $studentDetail['name'];
        $leadSource = array_flip(DataHelper::$leadSource);
        $mapSource = '';
        $category = $studentActivity->category ?? '';
        // dd($studentActivity);
        $mapSource = is_numeric($studentActivity->source) && isset(self::$_sourcemapping[$leadSource[$studentActivity->source] . '-' . $category]) ? self::$_sourcemapping[$leadSource[$studentActivity->source] . '-' . $category] : '';
        //  $mapSource = $studentActivity->source && isset(self::$_sourcemapping[$leadSource[$studentActivity->source]]) ? self::$_sourcemapping[$leadSource[$studentActivity->source] . '-' . $category] : '';
        // dd($mapSource);
        $cityId = empty($studentDetail['current_city']) ? (!empty($studentDetail['current_city_ip']) ? $studentDetail['current_city_ip'] : '') : $studentDetail['current_city'];
        $stateId = empty($studentDetail['current_state']) ? (!empty($studentDetail['current_state_ip']) ? $studentDetail['current_state_ip'] : '') : $studentDetail['current_state'];
        $highestQualification = !empty($studentPreference) ? $studentPreference->level : null;
        $budget = !empty($leadFollowUpFormData['budgetValueText']) ? $leadFollowUpFormData['budgetValueText'] : '';
        $budgetValue = !empty($budget) ? self::extractBudgetValue($budget) : '';
        $educationBudgetMapping = !empty($budgetValue) ? self::findBudgetMapping($budgetValue, DataHelper::$budgetDataSSO) : '';

        if (empty($mapSource) && $mapSource == '') {
            $mapSource = 43; // GMU OLD
        }

        $data = [
            'name' => $name,
            'email' =>  $studentDetail['email'] ?? '',
            'phone_no' =>   $studentDetail['phone'] ?? '',
            'level' =>  $highestQualification ?? '', // id diploma
            'url' =>  !empty($studentActivity->url) ? $studentActivity->url : '',
            'state' => (int)$stateId ?? '',
            'city' =>  (int)$cityId ?? '', //  curren city The City mapping ID (an integer field) if business_unit equals 2.
            'pref_state' => !empty($studentPreference) ? ($studentPreference->interested_state ?? (int)$studentDetail['current_state']) : null, // The preferred state mapping ID (an integer field) if business_unit equals 2.
            'pref_city' => !empty($studentPreference) ? ($studentPreference->interested_city ?? (int)$studentDetail['current_city']) : null, // The preferred city mapping ID (an integer field) if business_unit equals 2.
            'source' => $mapSource,
            'cta_id' => '',
            'object_id' => $leadFollowUpFormData['college_ids'] ?? [],
            'business_unit' => self::BUSINESS_UNIT,
            'object_type' => self::OBJECT_TYPE,
            'button_id' => '',
            'budget' => !empty($educationBudgetMapping) ? $educationBudgetMapping : '',
            'ip' => '',
            'whatsapp' => '',
            'stream' => !empty($studentPreference) && !empty($studentPreference->stream) ? $studentPreference->stream : '', // The Stream mapping ID (an integer field) if business_unit equals 2.
            'pref_spec' => !empty($leadFollowUpFormData) && !empty($leadFollowUpFormData['inputSpecialization']) ? $leadFollowUpFormData['inputSpecialization'] : [],
            'multiple_pref_cities' => !empty($leadFollowUpFormData) && !empty($leadFollowUpFormData['college_location']) ? $leadFollowUpFormData['college_location'] : [],
            'pref_study_mode' => !empty($leadFollowUpFormData['distanceEducation']) ? (int) DataHelper::$disatnceEduationSSo[$leadFollowUpFormData['distanceEducation']] : '',
            'pref_degree' => !empty($studentPreference) && !empty($studentPreference->course) ?  $studentPreference->course : '', // course table id  The preferred degree mapping ID (an integer field) if business_unit equals 2.
            'source_url' => !empty($studentActivity->url) ? $studentActivity->url : '',
            'first_source_url' => $studentDetail['source_url'] ?? '',
            'utm_source' => $studentActivity->utm_source ?? '',
            'utm_medium' => $studentActivity->utm_medium ?? '',
            'utm_campaign' => $studentActivity->utm_campaign ?? '',
            'utm_id' => $studentActivity->utm_id ?? '',
            'utm_term' => $studentActivity->utm_term ?? '',
            'pref_year' => self::YEAR,
        ];

        self::curl($data);
    }

    /*
    Function for send data to cld sso
    */
    public function sendLeadsToCldNew($studentDetail, $studentPreference, $studentActivity, $leadFollowUpFormData = [])
    {
        $name = $studentDetail['name'];
        $leadSource = array_flip(DataHelper::$leadSource);
        $category = $studentActivity->category ?? '';
        $sourceKey = is_numeric($studentActivity->source) ? ($leadSource[$studentActivity->source] ?? null) . '-' . $category : null;
        $mapSource = isset(self::$_sourcemapping[$sourceKey]) ? self::$_sourcemapping[$sourceKey] : 43; // fallback to GMU OLD

        $cityId = !empty($studentDetail['current_city'])
            ? $studentDetail['current_city']
            : (!empty($studentDetail['current_city_ip']) ? $studentDetail['current_city_ip'] : '');

        $stateId = !empty($studentDetail['current_state'])
            ? $studentDetail['current_state']
            : (!empty($studentDetail['current_state_ip']) ? $studentDetail['current_state_ip'] : '');

        $highestQualification = !empty($studentPreference) ? $studentPreference->level : null;

        $educationBudgetMapping = !empty($leadFollowUpFormData['budgetValueText'])
            ? self::findBudgetMapping(self::extractBudgetValue($leadFollowUpFormData['budgetValueText']), DataHelper::$budgetDataSSO)
            : '';

        $getArrayInt = function ($value) {
            if (empty($value)) {
                return [];
            }
            return is_array($value) ? array_map('intval', $value) : [(int) $value];
        };
        
        $data = [
            'name' => $name,
            'email' =>  $studentDetail['email'] ?? '',
            'phone_no' =>   $studentDetail['phone'] ?? '',
            'level' =>  $highestQualification ?? '', // id diploma
            'url' =>  !empty($studentActivity->url) ? $studentActivity->url : '',
            'state' => (int)$stateId ?? '',
            'city' =>  (int)$cityId ?? '', //  curren city The City mapping ID (an integer field) if business_unit equals 2.

            'pref_state' => !empty($studentPreference->interested_state)
                ? $getArrayInt($studentPreference->interested_state)
                : $getArrayInt($studentDetail['current_state'] ?? null),

            'pref_city' => !empty($studentPreference->interested_city)
                ? $getArrayInt($studentPreference->interested_city)
                : $getArrayInt($studentDetail['current_city'] ?? null),

            'stream' => !empty($studentPreference->stream)
                ? (is_array($studentPreference->stream)
                    ? array_map('intval', $studentPreference->stream)
                    : [(int) $studentPreference->stream])
                : [],

            'pref_spec' => !empty($leadFollowUpFormData['inputSpecialization'])
                ? (is_array($leadFollowUpFormData['inputSpecialization'])
                    ? array_map('intval', $leadFollowUpFormData['inputSpecialization'])
                    : [(int) $leadFollowUpFormData['inputSpecialization']])
                : [],

            'pref_degree' => !empty($studentPreference->course)
                ? (is_array($studentPreference->course)
                    ? array_map('intval', $studentPreference->course)
                    : [(int) $studentPreference->course])
                : [],

            'source' => $mapSource,
            'cta_id' => '',
            'object_id' => $leadFollowUpFormData['college_ids'] ?? [],
            'business_unit' => self::BUSINESS_UNIT,
            'object_type' => self::OBJECT_TYPE,
            'button_id' => '',
            'budget' => !empty($educationBudgetMapping) ? $educationBudgetMapping : '',
            'ip' => '',
            'whatsapp' => '',
            'multiple_pref_cities' => !empty($leadFollowUpFormData) && !empty($leadFollowUpFormData['college_location']) ? $leadFollowUpFormData['college_location'] : [],
            'pref_study_mode' => !empty($leadFollowUpFormData['distanceEducation']) ? (int) DataHelper::$disatnceEduationSSo[$leadFollowUpFormData['distanceEducation']] : '',
            'source_url' => !empty($studentActivity->url) ? $studentActivity->url : '',
            'first_source_url' => $studentDetail['source_url'] ?? '',
            'utm_source' => $studentActivity->utm_source ?? '',
            'utm_medium' => $studentActivity->utm_medium ?? '',
            'utm_campaign' => $studentActivity->utm_campaign ?? '',
            'utm_id' => $studentActivity->utm_id ?? '',
            'utm_term' => $studentActivity->utm_term ?? '',
            'pref_year' => self::YEAR,
        ];

        self::curl($data);
    }


    /*
    Function for send logs to dsa log table
    */
    public static function saveDsaWebHookLogs($payload, $mobile, $apiResponse, $apiResponseCode)
    {
        $statusCode = '';
        if (empty($payload) || empty($mobile)) {
            return [];
        }

        $mobileNumber = substr($mobile, -10);

        if ($apiResponseCode == null) {
            $statusCode = 200;
        } else {
            $statusCode = 500;
        }

        $dsaLogModel = new DsaWebhookLog();
        $dsaLogModel->payload_data = json_encode($payload, JSON_UNESCAPED_UNICODE);
        $dsaLogModel->api_response = json_encode($apiResponse);
        $dsaLogModel->api_response_code = $statusCode;
        $dsaLogModel->mobile =  $mobileNumber;
        $dsaLogModel->save();
    }

    /*
      function for send curl request with
      payload and save response
    */
    public static function curl($payload)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, sprintf(self::ENDPOINT));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization:  ' . self::APIKEY . '',
        ]);
        $result = curl_exec($ch);
        // $GmuCldSso = new GmuCldSso();
        $GmuCldSso = new CldSsoLog();
        $GmuCldSso->response = $result;
        $GmuCldSso->data = json_encode($payload);
        $GmuCldSso->phone =  $payload['phone_no'];
        $GmuCldSso->save();
        curl_close($ch);
    }

    //For block sso api for below URL
    public static $urlLeadBlock = [
        'https://www.getmyuni.com/articles/cbse-class-11-half-yearly-question-papers',
        'https://www.getmyuni.com/articles/mp-board-class-10th-blueprint',
        'https://www.getmyuni.com/articles/seba-board-class-10-half-yearly-syllabus',
        'https://www.getmyuni.com/articles/cbse-roll-number-finder',
        'https://www.getmyuni.com/articles/maharashtra-state-board-11th-books-download',
        'https://www.getmyuni.com/electrical-and-electronics-engineering-jobs-scope-salary',
        'https://www.getmyuni.com/sarkari-exam/national-investigation-agency-nia-recruitment/',
        'https://www.getmyuni.com/sarkari-exam/passport-office-recruitment/',
        'https://www.getmyuni.com/articles/cbse-class-11-registration-form',
        'https://www.getmyuni.com/articles/class-11-commerce-books-pdf'
    ];

    //  source mapping according to sso mapping sheet
    // public static $_sourcemapping = [
    //     'organic-college' => 2,
    //     'organic-course' => 3,
    //     'affiliate-ias-paper' => 5,
    //     'organic-exam' => 6,
    //     'organic-board' => 13,
    //     'organic-scholarship' => 14,
    //     'google_paid_ads-clp' => 16,
    //     'google_paid_ads-swipe-page' => 16,
    //     'organic-news' => 19,
    //     'organic-olympiad' => 20,
    //     'google_paid_ads-unbounce' => 12,
    //     'organic-' => 23,
    //     'google_paid_ads-' => 24,
    //     'affliates-' => 25,
    //     'procured-' => 26
    // ];

    public static $_sourcemapping = [
        'organic-college' => 51,
        'organic-course' => 52,
        'affiliate-ias-paper' => 54,
        'organic-exam' => 55,
        'organic-board' => 62,
        'organic-clp' => 10,
        'organic-scholarship' => 63,
        'google_paid_ads-clp' => 65,
        'google_paid_ads-unbounce' => 146,
        'google_paid_ads-swipe-page' => 65,
        'organic-news' => 68,
        'organic-olympiad' => 69,
        'organic-article' => 144,
        'organic-college-listing' => 145,
        'organic-' => 71,
        'google_paid_ads-' => 72,
        'affliates-' => 73,
        'procured-' => 74,
        'google_ads_lead-google-ads-lead' => 101,
        'organic-whatsapp' => 60,
        'organic-article-amp' => 144,
        'organic-news-amp' => 68
    ];


    // public static $_sourcemapping = [
    //     'organic-college' => 2,
    //     'organic-course' => 3,
    //     'affiliate-ias-paper' => 5,
    //     'organic-exam' => 6,
    //     'organic-board' => 13,
    //     'organic-scholarship' => 14,
    //     'google_paid_ads-clp' => 16,
    //     'google_paid_ads-unbounce' => 146,
    //     'google_paid_ads-swipe-page' => 16,
    //     'organic-news' => 19,
    //     'organic-olympiad' => 20,
    //     'organic-article' => 144,
    //     'organic-college-listing' => 145,
    //     'organic-' => 23,
    //     'google_paid_ads-' => 24,
    //     'affliates-' => 25,
    //     'procured-' => 26,
    //     'google_ads_lead-google-ads-lead' => 101,
    //     'organic-whatsapp' => 11
    // ];

    public static function getCourseData($id, $entity = '', $productMapping = '')
    {
        if (empty($id)) {
            return null;
        }
        $courseData = [];
        // if (isset($entity) && !empty($entity) && $entity == College::ENTITY_COLLEGE || $productMapping == College::ENTITY_COLLEGE) {
        //     $courseId = ProgramCourseMapping::find()
        //         ->select(['course_id', 'specialization_id', 'program_id'])
        //         ->where(['program_id' => $id])
        //         ->one();
        //     return empty($courseId) ? [] : $courseId;
        // }
        if (isset($entity) && $entity == 'user_profile') {
            $query = Course::find()
                ->select(['highest_qualification', 'parent_id', 'degree', 'id', 'specialization_id', 'stream_id'])
                ->where(['in', 'id', $id]);
            $courseData = $query->all();
        } else {
            $query = Course::find()
                ->select(['highest_qualification', 'parent_id', 'degree', 'id', 'specialization_id', 'stream_id']);
            if (is_numeric($id)) {
                $query->where(['id' => $id]);
                $courseData = $query->one();
            } else {
                $query->where(['slug' => $id]);
                $courseData = $query->one();
            }
        }
        return $courseData;
    }

    public function academicValidations($request)
    {
        $success = true;
        $response = [
            'success' => $success,
            'message' => '',
        ];

        if (!empty($request['boardEntityTenth']) || !empty($request['yearValueTenth']) || !empty($request['studentMarkTenth'])) {
            if (empty($request['boardEntityTenth'])) {
                $success = false;
                $errors['boardEntityTenth'] = 'Board cannot be blank.';
            }
            if (empty($request['yearValueTenth'])) {
                $success = false;
                $errors['yearValueTenth'] = 'Year cannot be blank.';
            }
            if (empty($request['studentMarkTenth'])) {
                $success = false;
                $errors['studentMarkTenth'] = 'Mark cannot be blank.';
            }
            $response = [
                'success' => $success,
                'message' => $errors ?? '',
            ];
        }

        if (!empty($request['boardEntityTwelve']) || !empty($request['yearValueTwelve']) || !empty($request['studentmarktweleve']) || !empty($request['tweleveSpecialization'])) {
            if (empty($request['boardEntityTwelve'])) {
                $success = false;
                $errors['boardEntityTwelve'] = 'Board cannot be blank.';
            }
            if (empty($request['yearValueTwelve'])) {
                $success = false;
                $errors['yearValueTwelve'] = 'Year cannot be blank.';
            }
            if (empty($request['studentmarktweleve'])) {
                $success = false;
                $errors['studentmarktweleve'] = 'Mark cannot be blank.';
            }
            if (empty($request['tweleveSpecialization'])) {
                $success = false;
                $errors['tweleveSpecialization'] = 'Specialization cannot be blank.';
            }
            $response = [
                'success' => $success,
                'message' => $errors ?? '',
            ];
        }

        if (!empty($request['boardEntityDiploma']) || !empty($request['yearValueDiploma']) || !empty($request['studentMarkDiploma'])) {
            if (empty($request['boardEntityDiploma'])) {
                $success = false;
                $errors['boardEntityTwelve'] = 'Board cannot be blank.';
            }
            if (empty($request['yearValueDiploma'])) {
                $success = false;
                $errors['yearValueDiploma'] = 'Year cannot be blank.';
            }
            if (empty($request['studentMarkDiploma'])) {
                $success = false;
                $errors['studentMarkDiploma'] = 'Mark cannot be blank.';
            }
            $response = [
                'success' => $success,
                'message' => $errors ?? '',
            ];
        }

        if (!empty($request['graduateCollege']) || !empty($request['yearValueGraduation']) || !empty($request['graduationMarks']) || !empty($request['graduationCourse'])) {
            if (empty($request['graduateCollege'])) {
                $success = false;
                $errors['graduateCollege'] = 'Board cannot be blank.';
            }
            if (empty($request['yearValueGraduation'])) {
                $success = false;
                $errors['yearValueGraduation'] = 'Year cannot be blank.';
            }
            if (empty($request['graduationMarks'])) {
                $success = false;
                $errors['graduationMarks'] = 'Mark cannot be blank.';
            }
            if (empty($request['graduationCourse'])) {
                $success = false;
                $errors['graduationCourse'] = 'Course cannot be blank.';
            }
            $response = [
                'success' => $success,
                'message' => $errors ?? '',
            ];
        }

        if (!empty($request['postGraduateCollege']) || !empty($request['yearValuePostGraduation']) || !empty($request['postGraduationMarks']) || !empty($request['postGraduationCourse'])) {
            if (empty($request['postGraduateCollege'])) {
                $success = false;
                $errors['postGraduateCollege'] = 'Board cannot be blank.';
            }
            if (empty($request['yearValuePostGraduation'])) {
                $success = false;
                $errors['yearValuePostGraduation'] = 'Year cannot be blank.';
            }
            if (empty($request['postGraduationMarks'])) {
                $success = false;
                $errors['postGraduationMarks'] = 'Mark cannot be blank.';
            }
            if (empty($request['postGraduationCourse'])) {
                $success = false;
                $errors['postGraduationCourse'] = 'Course cannot be blank.';
            }

            $response = [
                'success' => $success,
                'message' => $errors ?? '',
            ];
        }

        return $response;
    }

    public function storeAcademicsDetails($request, $category = '')
    {
        $response = [];

        if (isset($category) && !empty($category)) {
            $response = self::academicValidations($request);
            if ($response['success'] == true) {
                if (!empty($request['boardEntityTenth'])) {
                    $tenthResponse = self::updateStudentAcademicsTenth($request['student_id'], $request['boardEntityTenth'] ?? '', $request['studentMarkTenth'] ?? '', $request['yearValueTenth'] ?? '');
                }
                if (!empty($request['boardEntityTwelve'])) {
                    $twelevResponse = self::updateStudentAcademicsTwelve($request['student_id'], $request['boardEntityTwelve'] ?? '', $request['studentmarktweleve'] ?? '', $request['yearValueTwelve'] ?? '', empty($request['tweleveSpecialization']) ? '' : $request['tweleveSpecialization']);
                }
                if (!empty($request['boardEntityDiploma'])) {
                    $diplomaResponse = self::updateStudentAcademicsDiploma($request['student_id'], $request['boardEntityDiploma'] ?? '', $request['studentMarkDiploma'] ?? '', $request['yearValueDiploma'] ?? '');
                }
                if (!empty($request['graduateCollege'])) {
                    $graduateResponse = self::updateStudentAcademicsGraduation($request['student_id'], $request['graduateCollege'] ?? '', $request['graduationMarks'] ?? '', $request['yearValueGraduation'] ?? '', $request['graduationCourse'] ?? '');
                }
                if (!empty($request['postGraduateCollege'])) {
                    $postGraduateResponse = self::updateStudentAcademicsPostGraduation($request['student_id'], $request['postGraduateCollege'] ?? '', $request['postGraduationMarks'] ?? '', $request['yearValuePostGraduation'] ?? '', $request['postGraduationCourse'] ?? '');
                }

                $response['mobile'] = $request['mobile'];
                echo json_encode($response);
                exit;
            } else {
                echo json_encode($response);
                exit;
            }
        } else {
            $response = self::academicValidations($request);
            $degree = ['3', '5', '6', '7', '8'];
            if ($response['success'] == true && !empty($request['level'])) {
                if ($request['level'] == '1') {
                    $tenthResponse = self::updateStudentAcademicsTenth($request['student_id'], $request['boardEntityTenth'] ?? '', $request['studentMarkTenth'] ?? '', $request['yearValueTenth'] ?? '');
                    $twelevResponse = self::updateStudentAcademicsTwelve($request['student_id'], $request['boardEntityTwelve'] ?? '', $request['studentmarktweleve'] ?? '', $request['yearValueTwelve'] ?? '', empty($request['tweleveSpecialization']) ? '' : $request['tweleveSpecialization']);
                }

                if ($request['level'] == '4') {
                    $tenthResponse = self::updateStudentAcademicsTenth($request['student_id'], $request['boardEntityTenth'] ?? '', $request['studentMarkTenth'] ?? '', $request['yearValueTenth'] ?? '');
                    $diplomaResponse = self::updateStudentAcademicsDiploma($request['student_id'], $request['boardEntityDiploma'] ?? '', $request['studentMarkDiploma'] ?? '', $request['yearValueDiploma'] ?? '');
                }

                if ($request['level'] == '2') {
                    $twelevResponse = self::updateStudentAcademicsTwelve($request['student_id'], $request['boardEntityTwelve'] ?? '', $request['studentmarktweleve'] ?? '', $request['yearValueTwelve'] ?? '', $request['tweleveSpecialization'] ?? '');
                    $graduateResponse = self::updateStudentAcademicsGraduation($request['student_id'], $request['graduateCollege'] ?? '', $request['graduationMarks'] ?? '', $request['yearValueGraduation'] ?? '', $request['graduationCourse'] ?? '');
                }

                if (in_array($request['level'], $degree)) {
                    $graduateResponse = self::updateStudentAcademicsGraduation($request['student_id'], $request['graduateCollege'] ?? '', $request['graduationMarks'] ?? '', $request['yearValueGraduation'] ?? '', $request['graduationCourse'] ?? '');
                    $postGraduateResponse = self::updateStudentAcademicsPostGraduation($request['student_id'], $request['postGraduateCollege'] ?? '', $request['postGraduationMarks'] ?? '', $request['yearValuePostGraduation'] ?? '', $request['postGraduationCourse'] ?? '');
                }

                $student = Student::findIdentity($request['student_id']);

                if (!empty($student)) {
                    Yii::$app->user->login($student, 3600 * 24 * 30);
                }

                echo json_encode($response);
                exit;
            } else {
                echo json_encode($response);
                exit;
            }
        }
    }

    public function updateStudentAcademicsTenth($student_id, $entity_id, $mark, $year)
    {
        if (empty($entity_id) || empty($year)) {
            return false;
        }

        $model = StudentAcademicDetials::find()
            ->where(['student_id' => $student_id])
            ->andWhere(['entity' => StudentAcademicDetials::ENTITY_BOARD])
            ->andWhere(['qualification_level' => StudentAcademicDetials::QUALIFICATION_LEVEL_10])
            ->andWhere(['entity_id' => $entity_id])
            ->one();

        if (empty($model)) {
            $model = new StudentAcademicDetials();
        }

        $model->student_id = $student_id ?? '';
        $model->qualification_type = StudentAcademicDetials::QUALIFICATION_TYPE_ACADEMICS;
        $model->qualification_level = StudentAcademicDetials::QUALIFICATION_LEVEL_10;
        $model->entity = StudentAcademicDetials::ENTITY_BOARD;
        $model->entity_id = $entity_id ?? null;
        $model->passing_year = $year ?? null;
        $model->marks_type = StudentAcademicDetials::MARKS_TYPE_PERCENTAGE;
        $model->marks = $mark ?? null;
        $model->save();
    }

    public function updateStudentAcademicsTwelve($student_id, $entity_id, $mark, $year, $qualification_domain_id)
    {
        if (empty($entity_id) || empty($year)) {
            return false;
        }

        $model = StudentAcademicDetials::find()
            ->where(['student_id' => $student_id])
            ->andWhere(['entity' => StudentAcademicDetials::ENTITY_BOARD])
            ->andWhere(['qualification_level' => StudentAcademicDetials::QUALIFICATION_LEVEL_12])
            ->andWhere(['entity_id' => $entity_id])
            ->one();

        if (empty($model)) {
            $model = new StudentAcademicDetials();
        }

        $model->student_id = $student_id ?? '';
        $model->qualification_type = StudentAcademicDetials::QUALIFICATION_TYPE_ACADEMICS;
        $model->qualification_level = StudentAcademicDetials::QUALIFICATION_LEVEL_12;
        $model->entity = StudentAcademicDetials::ENTITY_BOARD;
        $model->entity_id = $entity_id ?? null;
        $model->passing_year = $year ?? null;
        $model->qualification_domain = StudentAcademicDetials::QUALIFICATION_DOMAINS_SPECIALIZATION;
        $model->qualification_domain_id = empty($qualification_domain_id) ? null : $qualification_domain_id;
        $model->marks_type = StudentAcademicDetials::MARKS_TYPE_PERCENTAGE;
        $model->marks = $mark ?? null;
        $model->save();
    }

    public function updateStudentAcademicsDiploma($student_id, $entity_id, $mark, $year)
    {
        if (empty($entity_id) || empty($year)) {
            return false;
        }

        $model = StudentAcademicDetials::find()
            ->where(['student_id' => $student_id])
            ->andWhere(['entity' => StudentAcademicDetials::ENTITY_BOARD])
            ->andWhere(['qualification_level' => StudentAcademicDetials::QUALIFICATION_LEVEL_DIPLOMA])
            ->andWhere(['entity_id' => $entity_id])
            ->one();

        if (empty($model)) {
            $model = new StudentAcademicDetials();
        }

        $model->student_id = $student_id ?? '';
        $model->qualification_type = StudentAcademicDetials::QUALIFICATION_TYPE_ACADEMICS;
        $model->qualification_level = StudentAcademicDetials::QUALIFICATION_LEVEL_DIPLOMA;
        $model->entity = StudentAcademicDetials::ENTITY_BOARD;
        $model->entity_id = $entity_id ?? null;
        $model->passing_year = $year ?? null;
        $model->marks_type = StudentAcademicDetials::MARKS_TYPE_PERCENTAGE;
        $model->marks = $mark ?? null;
        $model->save();
    }

    public function updateStudentAcademicsGraduation($student_id, $entity_id, $mark, $year, $qualification_domain_id)
    {
        if (empty($entity_id) || empty($year)) {
            return false;
        }

        $model = StudentAcademicDetials::find()
            ->where(['student_id' => $student_id])
            ->andWhere(['entity' => StudentAcademicDetials::ENTITY_COLLEGE])
            ->andWhere(['qualification_level' => StudentAcademicDetials::QUALIFICATION_LEVEL_GRADUATION])
            ->andWhere(['entity_id' => $entity_id])
            ->one();

        if (empty($model)) {
            $model = new StudentAcademicDetials();
        }

        $model->student_id = $student_id ?? '';
        $model->qualification_type = StudentAcademicDetials::QUALIFICATION_TYPE_ACADEMICS;
        $model->qualification_level = StudentAcademicDetials::QUALIFICATION_LEVEL_GRADUATION;
        $model->entity = StudentAcademicDetials::ENTITY_COLLEGE;
        $model->entity_id = $entity_id ?? null;
        $model->qualification_domain = StudentAcademicDetials::QUALIFICATION_DOMAINS_COURSE;
        $model->qualification_domain_id = $qualification_domain_id ?? null;
        $model->passing_year = $year ?? null;
        $model->marks_type = StudentAcademicDetials::MARKS_TYPE_PERCENTAGE;
        $model->marks = $mark ?? null;
        $model->save();
    }

    public function updateStudentAcademicsPostGraduation($student_id, $entity_id, $mark, $year, $qualification_domain_id)
    {
        if (empty($entity_id) || empty($year)) {
            return false;
        }

        $model = StudentAcademicDetials::find()
            ->where(['student_id' => $student_id])
            ->andWhere(['entity' => StudentAcademicDetials::ENTITY_COLLEGE])
            ->andWhere(['qualification_level' => StudentAcademicDetials::QUALIFICATION_LEVEL_POST_GRADUATION])
            ->andWhere(['entity_id' => $entity_id])
            ->one();

        if (empty($model)) {
            $model = new StudentAcademicDetials();
        }

        $model->student_id = $student_id ?? '';
        $model->qualification_type = StudentAcademicDetials::QUALIFICATION_TYPE_ACADEMICS;
        $model->qualification_level = StudentAcademicDetials::QUALIFICATION_LEVEL_POST_GRADUATION;
        $model->entity = StudentAcademicDetials::ENTITY_COLLEGE;
        $model->entity_id = $entity_id ?? null;
        $model->qualification_domain = StudentAcademicDetials::QUALIFICATION_DOMAINS_COURSE;
        $model->qualification_domain_id = $qualification_domain_id ?? null;
        $model->passing_year = $year ?? null;
        $model->marks_type = StudentAcademicDetials::MARKS_TYPE_PERCENTAGE;
        $model->marks = $mark ?? null;
        $model->save();
    }

    public function getAcademicPrefetchData($student_id, $type)
    {
        if (!empty($student_id)) {
            $query = StudentAcademicDetials::find()
                ->select(['qualification_type', 'qualification_level', 'entity', 'entity_id', 'qualification_domain', 'qualification_domain_id', 'passing_year', 'marks', 'exam_appearing_date', 'marks_type'])
                ->where(['student_id' => $student_id])
                ->andWhere(['not', ['entity_id' => null]]);

            if ($type == UserProfileController::CATEGORY) {
                $query->groupBy(['qualification_level']);
            }

            $student_academic = $query->all();

            foreach ($student_academic as $value) {
                if ($type == LeadV2Controller::CATEGORY && $value->qualification_type == StudentAcademicDetials::QUALIFICATION_TYPE_EXAM) {
                    $data['exam'][$value->entity_id] = [
                        'marks' => $value->marks ?? null,
                        'exam_appearing_date' => $value->exam_appearing_date ?? null,
                    ];
                }

                if ($value->qualification_type == StudentAcademicDetials::QUALIFICATION_TYPE_ACADEMICS) {
                    if ($value->qualification_level == StudentAcademicDetials::QUALIFICATION_LEVEL_10 || $value->qualification_level == StudentAcademicDetials::QUALIFICATION_LEVEL_12 || $value->qualification_level == StudentAcademicDetials::QUALIFICATION_LEVEL_DIPLOMA) {
                        $entityName = self::getBoardName($value->entity_id);
                        $domainName = empty($value->qualification_domain_id) ? '' : DataHelper::$leadSpecializationTwelveId[$value->qualification_domain_id];
                    } else {
                        $entityName = self::getCollegeName($value->entity_id);
                        $courseName = self::getCourseName($value->qualification_domain_id);
                    }
                    $data['academics'][$value->qualification_level] = [
                        'entity_id' => $value->entity_id,
                        'entity_name' => $entityName ?? '',
                        'qualification_domain_id' => $value->qualification_domain_id ?? null,
                        'qualification_domain_name' => $domainName ?? '',
                        'coursName' => $courseName ?? '',
                        'passing_year' => $value->passing_year ?? null,
                        'marks' => $value->marks ?? null,
                    ];
                }
            }
        }
        return $data ?? [];
    }

    public function getBoardName($boardId)
    {
        if (empty($boardId)) {
            return '';
        }
        $board = Board::find()->select(['display_name'])->where(['id' => $boardId])->one();

        return $board['display_name'] ?? '';
    }

    public function getCollegeName($collegeId)
    {
        if (empty($collegeId)) {
            return '';
        }
        $college = College::find()->select(['display_name'])->where(['id' => $collegeId])->one();

        return $college['display_name'] ?? '';
    }

    public function getCourseName($courseId)
    {
        if (empty($courseId)) {
            return '';
        }
        $college = Course::find()->select(['short_name'])->where(['id' => $courseId])->one();

        return $college['short_name'] ?? '';
    }

    public static function getCorrectedEmailDomain($request)
    {
        $email = isset($request['email']) ? $request['email'] : null;

        $explodeDomain = explode('@', $request['email']);
        $explodedDomain = !empty($explodeDomain[1]) ? $explodeDomain[1] : '';

        if (empty($explodedDomain)) {
            return $request['email'];
        }

        $emailModel = LeadGmailDomain::find()
            ->where(['wrong_domain' => $explodedDomain])
            ->andWhere(['status' => LeadGmailDomain::STATUS_ACTIVE])->one();

        if (!empty($emailModel)) {
            $email = $explodeDomain[0] . '@' . $emailModel->corrected_domain;
        }
        // else {
        //     $email = $request['email'];
        // }

        return $email;
    }

    public function findBudgetMapping($number, $array)
    {
        foreach ($array as $range => $key) {
            list($min, $max) = explode('-', $range);
            $min = (int)$min;
            $max = $max === '' ? PHP_INT_MAX : (int)$max;  // Treat empty max as infinity

            if ($number >= $min && $number <= $max) {
                return $key;
            }
        }
        return null;  // If no range matches
    }

    // public static function findBudgetMapping($value, $budgetMap)
    // {
    //     foreach ($budgetMap as $range => $id) {
    //         [$min, $max] = explode('-', $range);

    //         $min = (int) $min;
    //         $max = ($max !== '') ? (int) $max : PHP_INT_MAX;

    //         if ($value >= $min && $value <= $max) {
    //             return $id;
    //         }
    //     }

    //     return '';
    // }

    public function extractBudgetValue($displayName)
    {
        if (strpos($displayName, '-') !== false) { // Check if the string contains a hyphen
            $parts = explode('-', $displayName);
            return self::convertToNumeric(trim($parts[1])); // Return the part after the hyphen and convert it
        }

        if (strpos($displayName, '+') !== false) { // Check if the string contains a plus sign
            $parts = explode('+', $displayName);
            return self::convertToNumeric(trim($parts[0])); // Return the part before the plus sign and convert it
        }

        return self::convertToNumeric($displayName); // Default case (if no special characters found)
    }

    // public static function extractBudgetValue($budgetText)
    // {
    //     if (empty($budgetText)) {
    //         return '';
    //     }

    //     // Normalize text (e.g., "1 Lac - 2 Lac")
    //     $budgetText = strtolower(trim($budgetText));

    //     // Replace "lac" or "lakh" with 00000
    //     $budgetText = str_replace(['lac', 'lakh', 'lacs'], '00000', $budgetText);
    //     $budgetText = preg_replace('/[^0-9\-]/', '', $budgetText); // Keep only numbers and dash

    //     // Expect format like 100000-200000
    //     if (strpos($budgetText, '-') !== false) {
    //         [$min, $max] = explode('-', $budgetText);
    //         $min = (int) trim($min);
    //         $max = (int) trim($max);

    //         if ($min && $max) {
    //             return (int) (($min + $max) / 2); // average
    //         }
    //     }

    //     return (int) $budgetText; // fallback: treat as single value
    // }

    public function convertToNumeric($value)
    {
        $value = trim($value);

        if (stripos($value, 'Lac') !== false) {
            $numericValue = floatval($value) * 100000;
        } elseif (stripos($value, 'K') !== false) {
            $numericValue = floatval($value) * 1000;
        } else {
            $numericValue = floatval($value);
        }

        return $numericValue;
    }
}
