<?php


namespace common\services;

use yii\db\Query;
use common\components\HttpService;
use common\models\Exam;
use common\models\ExamDate;
use common\models\ExamContent;
use common\models\SeoInfo;
use Carbon\Carbon;
use common\helpers\CollegeHelper;
use common\models\College;
use common\models\ExamCourse;
use common\models\CollegeContent;
use common\helpers\DataHelper;
use common\models\Course;
use common\models\old\GmuExamCollegeMapping;
use common\models\StudentAcademicDetials;
use common\models\User;
use common\models\LeadBucket;
use common\models\LeadBucketTagging;
use Yii;
use yii\caching\TagDependency;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use common\models\ArticleSubpage;
use common\models\Article;
use common\models\ArticleSubpageSection;
use common\models\ArticleSubpageSubsectionSubtopic;
use common\models\ArticleSubpageSubsectionQuesAns;

class ExamService extends HttpService
{
    const SEO_ENTITY = 'exam';
    const NEW_RECORD = 1;
    const HOMEPAGE_CONTENT_ID = 3;
    const NATIONAL_COMMON_EXAM = 3;

    public function getAll($limit = 5, $orderBy = 'asc')
    {
        return Exam::find()->limit($limit)->orderBy(['id' => $orderBy])->all();
    }

    public function getInterestedArticleExams($streamId, $notIn, $limit, $orderBy = 'desc')
    {
        $lang_code = DataHelper::getLangId();
        $query = new Query;
        $query->select(['e.*'])
            ->from('exam as e')
            ->innerJoin('exam_stream as es', 'es.exam_id = e.id')
            ->where(['es.stream_id' => $streamId])
            ->andWhere(['e.status' => Exam::STATUS_ACTIVE])
            ->andWhere(['e.lang_code' => $lang_code])
            ->andWhere(['NOT IN', 'e.id', $notIn])
            ->limit($limit)
            ->orderBy([new \yii\db\Expression('FIELD(e.type, ' . Exam::TYPE_NATIONAL . ')DESC'), new \yii\db\Expression('FIELD(e.exam_type_id, ' . self::NATIONAL_COMMON_EXAM . ')DESC'), 'e.id' => SORT_DESC]);
        return $query->all();
    }

    public function getInterestedExams($streamId, $notIn, $limit, $orderBy = 'desc')
    {
        $lang_code = DataHelper::getLangId();
        $query = (new \yii\db\Query())
            ->select('e.*')
            ->from('exam as e')
            ->where(['e.status' => Exam::STATUS_ACTIVE])
            ->andWhere(['e.lang_code' => $lang_code])
            ->andWhere(['<>', 'e.id', $notIn]);

        // Add primary_stream_id condition only if it's not null
        if ($streamId !== null) {
            $query->andWhere(['e.primary_stream_id' => $streamId])
                ->andWhere(['is not', 'e.primary_stream_id', null]);
        }

        $query->limit($limit)
            ->orderBy([
                new \yii\db\Expression('CASE WHEN e.search_volume = 0 THEN 1 ELSE 0 END'),
                'e.search_volume' => SORT_ASC,
                'e.id' => SORT_DESC
            ]);
        // ->addOrderBy([new \yii\db\Expression('FIELD(e.search_volume, ' . ' 0)ASC'), 'e.id' => SORT_DESC]);
        return $query->all();
    }

    /**
     * Undocumented function
     *
     * @param string|integer $identifier id or slug of exam
     * @return object
     */
    public function getDetail($identifier)
    {
        $lang_code = DataHelper::getLangId();
        $exam = Exam::find()->active()->where(['lang_code' => $lang_code])->bySlug($identifier)->one();
        if (empty($exam) && !is_string($identifier)) {
            $exam = Exam::find()->active()->byId($identifier)->one();
        }

        return $exam;
    }

    public function getDate(int $id)
    {
        return ExamDate::find()->select(['name', 'slug', 'exam_id', 'start', 'end'])
            ->andWhere(['exam_id' => $id])
            ->andWhere(['status' => ExamDate::STATUS_ACTIVE])
            ->all();
    }

    //for reviews
    public function getUpcomingDate(int $id)
    {
        $query = new Query();
        $query->select(['slug', 'exam_id', 'start', 'end'])
            ->from('exam_date ed')
            ->where(['exam_id' => $id])
            ->andWhere(['not', ['start' => null]])
            ->andWhere(['in', 'ed.slug', ['exam-start', 'result-date']])
            ->andWhere(['>', 'start', Carbon::now()->toDateTimeString()])
            ->andWhere(['status' => ExamDate::STATUS_ACTIVE])
            ->orderBy(['start' => SORT_DESC]);

        $examDates = $query->all();

        return $examDates ?? [];
    }

    /**
     * Return pages for exam page
     *
     * @param integer $examId
     * @return void
     */
    public function getPages(int $examId)
    {
        return ExamContent::find()
            ->select(['name', 'slug'])
            ->andWhere(['exam_id' => $examId])
            ->andWhere(['status' => ExamContent::STATUS_ACTIVE])
            ->orderBy([new \yii\db\Expression('FIELD(slug, ' . ' "important-dates", "syllabus", "overview"' . ')DESC')])
            ->all();
    }

    public function getContent(int $id, string $section = '', $parentId = '')
    {
        $content = ExamContent::find()
            ->with('user')
            ->select(['name', 'slug', 'content', 'author_id', 'localize_year', 'created_at', 'updated_at', 'updated_by', 'parent_id'])
            ->andWhere(['lang_code' => DataHelper::getLangId()])
            ->andWhere(['exam_id' => $id]);

        if (!empty($parentId)) {
            $content = $content->andWhere(['parent_id' => $parentId]);
        }

        if ($section == '') {
            $content = $content->andWhere(['slug' => 'overview']);
        } else {
            $content = $content->andWhere(['slug' => $section]);
        }

        $modelContent =  $content->andWhere(['status' => ExamContent::STATUS_ACTIVE])->one();
        return $modelContent;
    }

    private function upcomingStreamExams($stream, $limit, $whereNotIn = [])
    {
        $query = new Query;
        $query->select(['e.*'])
            ->from('exam as e')
            ->innerJoin('exam_stream as es', 'es.exam_id = e.id')
            ->innerJoin('exam_date as ed', 'ed.exam_id = e.id')
            ->where(['>', 'ed.start', Carbon::now()->toDateTimeString()])
            ->andWhere(['es.stream_id' => $stream])
            ->andWhere(['ed.slug' => 'exam-start'])
            ->andWhere(['e.status' => Exam::STATUS_ACTIVE])
            ->groupBy('e.id')
            ->limit($limit)
            ->orderBy(['ed.start' => SORT_ASC]);

        if (!empty($whereNotIn)) {
            $query->andWhere(['not', ['e.id' => $whereNotIn]]);
        }

        if (empty($upcomingStreamExams = $query->all())) {
            return [];
        }

        return $this->examsCardDetail($upcomingStreamExams);
    }

    public function upcomingExams($streamId = null, $limit = 10, array $whereNotIn = [], $type = null)
    {
        if ($streamId) {
            return $this->upcomingStreamExams($streamId, $limit, $whereNotIn);
        }
        $lang_code = DataHelper::getLangId();
        $query = ExamDate::find()
            ->alias('ed')
            ->where(['>', 'ed.start', Carbon::now()->toDateTimeString()])
            ->andWhere(['ed.slug' => 'exam-start'])
            ->andWhere(['ed.status' => ExamDate::STATUS_ACTIVE])
            ->joinWith(['exam e'], true, 'INNER JOIN')
            ->andWhere(['=', 'e.status', Exam::STATUS_ACTIVE])
            ->andWhere(['=', 'e.lang_code', $lang_code])
            ->limit($limit)
            ->orderBy(['ed.start' => SORT_ASC]);

        if ($type) {
            $query->andWhere(['ed.type' => $type]);
        }

        return $query->all();
    }

    public function interestedExams($streamId, $notIn, $limit)
    {
        return $this->examsCardDetail($this->getInterestedExams($streamId, $notIn, $limit, 'desc'));
    }

    public function popularExams($skipId, $limit = 4)
    {
        $skipIds = [];
        $skipIds[] = $skipId;
        $lang_code = DataHelper::getLangId();
        $exams = Exam::find()->where(['>', 'id', $skipId])->andWhere(['is_popular' => Exam::POPULAR_YES])->andWhere(['lang_code' => $lang_code])->limit($limit)->all();
        $count = count($exams);
        $totalCard = 4;
        if ($count < $totalCard) {
            $limit = $totalCard - $count;
            foreach ($exams as $exam) {
                $skipIds[] = $exam->id;
            }
            $leftOutExams = Exam::find()->where(['not', ['id' => $skipIds]])->andWhere(['is_popular' =>  Exam::POPULAR_YES])->andWhere(['lang_code' => $lang_code])->limit($limit)->all();
            $exams = array_merge($exams, $leftOutExams);
        };
        if (!$exams) {
            return [];
        }
        return $this->examsCardDetail($exams);
    }

    private function examsCardDetail($exam)
    {
        $data = [];

        foreach ($exam as $key => $value) {
            $data[] = [
                'id' => (int) $value['id'],
                'name' => $value['name'],
                'slug' => $value['slug'],
                'display_name' => $value['display_name'],
                'cover_image' => $value['cover_image'],
                'lang_code' => $value['lang_code'],
                'pages' => ArrayHelper::toArray($this->getPages($value['id'])),
                'dates' => ArrayHelper::toArray($this->getDate($value['id'])),
            ];
        }

        return $data;
    }

    public function pageSeoInfo(int $id, $page, $parentPage = '')
    {
        $query = SeoInfo::find()->where([
            'entity' => 'exam',
            'entity_id' => $id,
            'page' => $page

        ]);

        if (!empty($parentPage)) {
            $query->andWhere(['parent_id' => $parentPage]);
        }

        return $query->one();
    }

    public function filter($params)
    {
        $exams = Exam::find()->with([
            'examDates',
            'streams' => function ($q) use ($params) {
                $q->where(['slug' => $params['stream']]);
            }
        ])->all();

        return $exams;
    }

    public function getCollegeAcceptingExams(int $id, $slug = '', $limit = 5)
    {

        $query = new Query();
        $query->select('college_id')
            ->distinct()
            ->from('college_exam')
            ->where(['exam_id' => $id]);
        $result = $query->all();

        if (empty($result)) {
            return [];
        }

        $colleges = College::find()
            ->select(['id', 'slug', 'name', 'display_name'])
            ->where(['in', 'id', array_column($result, 'college_id')])
            ->andWhere(['status' => College::STATUS_ACTIVE])
            ->orderBy(['-' . '`rank`' => SORT_DESC])
            ->limit(5)
            ->all();

        if (empty($colleges)) {
            return [];
        }
        $checkFilterStatus = (new CollegeService)->checkFilterPageStatus([], '', '', '', $slug);

        foreach ($colleges as $college) {
            if (!empty($college->collegeContents)) {
                $activeRecords['page'] = array_filter($college->collegeContents, function ($k) {
                    if ($k->status = College::STATUS_ACTIVE) {
                        if (in_array($k->sub_page, ['courses-fees', 'admission'])) {
                            return  $k->sub_page;
                        }
                    }
                });
            }

            $data['college'][] = [
                'college_name' => $college->name,
                'college_display_name' => !empty($college->display_name) ? $college->display_name : $college->name,
                'college_url' => $college->slug,
                'college_id' => $college->id,
                'college_ra1sum' => CollegeHelper::getTotalRating((new ReviewService)->getCollegeBasedCategoryRating($college->id)),
                'sub_page' => !empty($activeRecords['page']) ? array_reverse(array_unique(array_column($activeRecords['page'], 'sub_page'))) : ''
            ];
        }
        $data['filterPageStatus'] = $checkFilterStatus;

        return $data ?? [];
    }

    /**
     *  Get Exam Page URL formate
     *  @param $slug | Exam Slug
     *  @param $page | Exam Sub Page Slug
     *  return string | emty string
     */
    public function examUrlFormate($slug, $pageSlug, $dropSlug = null)
    {
        if (!empty($lang_code) && $lang_code != 'en') {
            if ($pageSlug == 'syllabus') {
                if (!empty($dropSlug)) {
                    return $slug . '-' . $dropSlug . '-syllabus';
                } else {
                    return $slug . '-syllabus';
                }
            } else if (($pageSlug == 'cut-off') && ($dropSlug != 'qualifying-marks')) {
                if (!empty($dropSlug)) {
                    return $slug . '-' . $pageSlug . '-' . $dropSlug;
                } else {
                    if ($pageSlug == 'overview') {
                        return $slug;
                    } else {
                        // echo "last4";
                        // dd($pageSlug);
                        return $slug . '-' . $pageSlug;
                    }
                }
            } else {
                if ($pageSlug == 'overview') {
                    return $slug;
                } else {
                    // echo "last3";
                    // dd($pageSlug);
                    return $slug . '-' . $pageSlug;
                }
            }
        } else {
            if ($pageSlug == 'syllabus') {
                if (!empty($dropSlug)) {
                    return $slug . '-' . $dropSlug . '-syllabus';
                } else {
                    return $slug . '-syllabus';
                }
            } else if (($pageSlug == 'cut-off') && ($dropSlug != 'qualifying-marks')) {
                if (!empty($dropSlug)) {
                    return $slug . '-' . $pageSlug . '-' . $dropSlug;
                } else {
                    if ($pageSlug == 'overview') {
                        return $slug;
                    } else {
                        // echo "last2";
                        // dd($pageSlug);
                        return $slug . '-' . $pageSlug;
                    }
                }
            } else if (in_array($pageSlug, ['answer-key', 'exam-pattern'])) {
                if (!empty($dropSlug)) {
                    return $slug . '-' . $dropSlug . '-' . $pageSlug;
                } else {
                    if ($pageSlug == 'overview') {
                        return $slug;
                    } else {
                        // echo "last1";
                        // dd($pageSlug);
                        return $slug . '-' . $pageSlug;
                    }
                }
            } else {
                if ($pageSlug == 'overview') {
                    return $slug;
                } else {
                    // echo "last";
                    // dd($pageSlug);
                    return $slug . '-' . $pageSlug;
                }
            }
        }
    }

    public function getAdTargetData(Exam $exam)
    {
        if (empty($exam)) {
            return [];
        }

        $hash = __CLASS__ . __FUNCTION__ . md5(base64_encode(serialize($exam)));
        $data = Yii::$app->cache->getOrSet($hash, function () use ($exam) {
            $query = new Query();
            $query->select(['exam.name', 'course.name as courseName', 'ec.course_id', 'es.stream_id', 'stream.name as streamName', 'exam.lang_code'])
                ->from('exam exam')
                ->leftJoin('exam_course as ec', 'ec.exam_id = exam.id')
                ->leftJoin('course as course', 'course.id = ec.course_id')
                ->leftJoin('exam_stream as es', 'es.exam_id = exam.id')
                ->leftJoin('stream as stream', 'stream.id = es.stream_id')
                ->where(['exam.id' => $exam->id]);

            $examData = $query->all(\Yii::$app->db);

            $courses = array_unique(ArrayHelper::getColumn($examData, 'courseName'));
            $streams = array_unique(ArrayHelper::getColumn($examData, 'streamName'));
            // $degrees = array_unique(ArrayHelper::getColumn($examData, 'degree'));

            return [
                'ExamName' => $exam->slug ?? '',
                // 'degree' => $degrees ?? '',
                'discipline' => $streams ?? '',
                'courses' => $courses ?? ''
            ];
        }, 60 * 60 * 6, new TagDependency(['tags' => 'get-ad-target-data-' . $exam->slug]));

        return $data ?? [];
    }

    public function leadExams($streamId, $level)
    {
        // $courseQuery = new Query();
        // $courseQuery->select(['exam.id', 'exam.display_name', 'ec.exam_id'])
        //     ->from(['exam_course ec'])
        //     ->leftJoin('exam as exam', 'exam.id = ec.exam_id')
        //     ->leftJoin(Course::tableName(), 'course.id = ec.course_id')
        //     ->where(['ec.course_id' => $course])
        //     ->andWhere(['not', ['ec.position' => null]])
        //     ->orderBy(['ec.position' => SORT_DESC]);
        $streamLevelExams = new Query();
        $streamLevelExams->select(['id', 'display_name'])
            ->from([Exam::tableName()])
            ->where(['primary_stream_id' => $streamId])
            ->andWhere(['derived_lead_level' => $level])
            ->limit(4);

        $exams = $streamLevelExams->all();

        return $exams ?? null;
    }

    public function leadUserProfileExams($courses, $examId = '', $type = '', $student_id = '', $data = '')
    {
        if (!empty($data)) {
            $exams['pre_fech'] = self::getExamValues($student_id);
            $exams['new'] = self::getExamNewValues($courses);
            $finalExams['new_record'] = self::NEW_RECORD;
            $finalExams = !empty($exams['pre_fech']) && !empty($exams['new']) ? array_merge($exams['pre_fech']['exams'], $exams['new']['exams']) : null;
            $newProducts = [];
            if (empty($finalExams)) {
                return [];
            }
            foreach ($finalExams as $item) {
                if (!empty($newProducts[$item['id']])) {
                    $currentValue = (array) $newProducts[$item['id']]['id'];
                    $newProducts[$item['id']]['id'] = array_merge($currentValue, (array) $item['id']);
                } else {
                    $newProducts[$item['id']] = $item;
                }
            }

            return [
                'exams' => array_values($newProducts) ?? [],
                'new_record' => self::NEW_RECORD
            ];
        }
        if (empty($courses) && !empty($type) && empty($examId)) {
            $examData = self::getExamValues($student_id);
            return $examData;
        }

        if (!empty($courses)) {
            $examData = self::getExamNewValues($courses);
            return $examData;
        }
    }

    public function getExamValues($student_id)
    {
        $query = new Query();
        $query->select(['marks', 'exam_appearing_date', 'exam.id', 'exam.display_name', 'marks_type'])
            ->from([StudentAcademicDetials::tableName()])
            ->leftJoin(Exam::tableName(), 'exam.id = student_academic_detials.entity_id')
            ->andWhere(['student_id' => $student_id])
            ->andWhere(['entity' => StudentAcademicDetials::ENTITY_EXAM])
            ->groupBy(['entity_id']);

        $studentAcademics['exams'] = $query->all();

        return $studentAcademics ?? null;
    }


    public function getExamNewValues($courses)
    {
        if (empty($courses)) {
            return [];
        }

        $courseQuery = new Query();
        $courseQuery->select(['exam.id', 'exam.display_name', 'ec.course_id'])
            ->from(['exam_course ec'])
            ->leftJoin('exam as exam', 'exam.id = ec.exam_id')
            ->leftJoin(Course::tableName(), 'course.id = ec.course_id')
            ->where(['in', 'ec.course_id', array_filter($courses)])
            ->andWhere(['not', ['ec.position' => null]])
            ->groupBy(['exam.id'])
            ->orderBy(['ec.position' => SORT_DESC]);
        $exams['exams'] = $courseQuery->all();

        $exams['new_record'] = self::NEW_RECORD;

        return $exams ?? null;
    }

    public static function getExamCourse($examId)
    {
        if (empty($examId)) {
            return [];
        }

        $courseQuery = new Query();
        $courseQuery->select(['course.short_name', 'course.name', 'course.slug', 'ec.course_id'])
            ->from(['exam_course ec'])
            ->leftJoin(Course::tableName(), 'course.id = ec.course_id')
            ->where(['ec.exam_id' => $examId, 'course.status' => Course::STATUS_ACTIVE])
            ->orderBy(['ec.position' => SORT_DESC]);
        $courses = $courseQuery->all();
        return $courses ?? null;
    }

    public function getUser($id)
    {
        return User::find()->select(['name', 'slug'])->where(['id' => $id])->one();
    }

    public function interestedArticleExams($streamId, $notIn, $limit)
    {
        return $this->examsCardDetail($this->getInterestedArticleExams($streamId, $notIn, $limit, 'desc'));
    }

    public function getArticleTopicPage($slug, $page)
    {
        $mainPageLink = [];
        if (array_key_exists($page, DataHelper::$articleTopicPage)) {
            $examArticleLink = ArticleSubpage::find()->select(['article_id', 'name'])
                ->where(['slug' => $slug])
                ->one();
            if (!empty($examArticleLink)) {
                $articleDetail = Article::find()->select(['slug', 'id'])
                    ->where(['id' => $examArticleLink->article_id])
                    ->andWhere(['status' => Article::STATUS_ACTIVE])
                    ->one();
                if (!empty($articleDetail)) {
                    $mainPageLink[$articleDetail->slug][$articleDetail->slug] = $examArticleLink->name . ' Important Questions';
                    if (!empty($articleDetail->articleSection)) {
                        foreach ($articleDetail->articleSection as $section) {
                            if ($section->status == 1) {
                                $mainPageLink[$articleDetail->slug][$section->slug] = $examArticleLink->name . ' ' . $section->name . ' Important Questions';
                            }
                        }
                    }
                }
            }
        }
        return  $mainPageLink;
    }

    public function getArticleTopicPageNormalArticle($examSlug, $article_id)
    {

        $mainPageLink = [];
        //if(array_key_exists($page,DataHelper::$articleTopicPage)){
        $allowdCta = [
            'Timetable',
            'Syllabus',
            'Sample Paper',
            'Registration',
            'Question Paper',
            'Previous Year Question Paper',
            'Predicted Question Paper',
            'Mock Test',
            'Mock Allotment',
            'Merit List',
            'Important Dates',
            'Exam Prep Strategy',
            'Exam Pattern',
            'Exam Day Instruction',
            'Eligibility Criteria',
            'Books',
            'Application',
            'Admit Card'
        ];
        $allLeadBucket = ArrayHelper::map(LeadBucket::find()->active()
            ->where(['entity_id' => LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE])
            ->andWhere(['in', 'bucket', $allowdCta])
            ->all(), 'id', 'bucket');
        $bucketname = ArrayHelper::map(LeadBucketTagging::find()->select(['id', 'bucket_id'])->where(['article_id' => $article_id, 'status' => LeadBucketTagging::STATUS_ACTIVE])->all(), 'id', 'bucket_id');
        if (!empty($bucketname)) {
            foreach ($bucketname as $bucketId) {
                $bucketID = $bucketId;
            }
            if (array_key_exists($bucketID, $allLeadBucket)) {
                $examArticleLink = ArticleSubpage::find()->select(['article_id', 'name'])
                    ->where(['slug' => $examSlug])
                    ->one();
                if (!empty($examArticleLink)) {
                    $articleDetail = Article::find()->select(['slug', 'id'])
                        ->where(['id' => $examArticleLink->article_id])
                        ->andWhere(['status' => Article::STATUS_ACTIVE])
                        ->one();
                    if (!empty($articleDetail)) {
                        $mainPageLink[$articleDetail->slug][$articleDetail->slug] = $examArticleLink->name . ' Important Questions';
                        if (!empty($articleDetail->articleSection)) {
                            foreach ($articleDetail->articleSection as $section) {
                                if ($section->status == 1) {
                                    $mainPageLink[$articleDetail->slug][$section->slug] = $examArticleLink->name . ' ' . $section->name . ' Important Questions';
                                }
                            }
                        }
                    }
                }
            }
        }
        return  $mainPageLink;
    }
}
