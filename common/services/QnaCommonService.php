<?php

namespace common\services;

use yii\db\Query;
use common\helpers\DataHelper;
use common\models\ForumVotes;
use common\models\Qna;
use common\models\QnaAnswer;
use yii;

class QnaCommonService
{
    public static $filterData = [
        'Exams' => 1,
        'Colleges' => 2,
        'Courses' => 3,
        'Boards' => 4,
        // 'Scholarship' => 5,
        // 'Olympiad' => 6,
    ];

    public static $filterDbData = [
        'exam' => 1,
        'college' => 2,
        'course' => 3,
        'board' => 4,
        // 'gmu_scholarship' => 5,
        // 'gmu_olympiad_name' => 6,
    ];

    public static $filterEntityData = [
        'exam' => 1,
        'college' => 2,
        'course' => 3,
        'board' => 4,
        // 'scholarship' => 5,
        // 'olympiad' => 6,
    ];

    public static $demoUsers = [459992, 459993, 459994, 459995, 459996];
    // public static $demoUsers = [459886, 459887, 459892, 459899, 459862];


    public function getForumQna($entity_id, $entity, $entity_sub_type, $type = null)
    {
        $voteArr = [];
        $entity_sub_type = $entity_sub_type == 39 ? 38 : $entity_sub_type;
        if ($entity_sub_type != null) {
            $qna = Qna::find()->select(['id', 'question', 'entity', 'entity_id', 'slug'])->where(['entity' => $entity])
                ->andWhere(['entity_id' => $entity_id])
                ->andWhere(['IN', 'entity_sub_type', $entity_sub_type])
                ->andWhere(['status' => Qna::STATUS_ACTIVE])
                ->orderBy(['updated_at' => SORT_DESC])
                ->with([
                    'answers' => function ($query) {
                        $query->select(['id', 'answer', 'updated_at', 'question_id']);
                    },
                    'student'
                ])->all();
            if ($type == 'qna_card' && !empty($qna)) {
                foreach ($qna as $key => $value) {
                    if (!empty($value->answers)) {
                        $vote = $this->getAnswerVotes($value->answers[0]);
                        $voteArr[$value->answers[0]->id] = ['upvote' => $vote['upVote'], 'downvote' => $vote['downVote'], 'userUpVote' => $vote['userUpVote'], 'userDownVote' => $vote['userDownVote']];
                    } else {
                        unset($qna[$key]);
                    }
                }
                if (!empty($voteArr)) {
                    $qna['votes'] = $voteArr;
                    array_push($qna, $voteArr);
                }
                return $qna ?? [];
            } else {
                return [];
            }

            // elseif ($type == 'qna_card' && empty($qna)) {
            //     $entityArr = ['1' => ['exam', 'overview'], '2' => ['college', 'info'], '3' => ['course', 'about'], '4' => ['board', 'overview']];
            //     $entity_sub_type = $this->getEntitySubTypeId($entityArr[$entity][1], $entityArr[$entity][0]);

            //     $qna = Qna::find()->where(['entity' => $entity])
            //         ->andWhere(['entity_id' => $entity_id])
            //         ->andWhere(['IN', 'entity_sub_type', $entity_sub_type])
            //         ->andWhere(['status' => Qna::STATUS_ACTIVE])
            //         ->orderBy(['updated_at' => SORT_DESC])
            //         ->with(['answers', 'student'])
            //         ->all();
            // }
            // return $qna;
        }
        return [];
    }

    public function getQnaDetails($slug, $entity = null)
    {
        $voteArr = [];
        $query = Qna::find()->select(['id', 'question', 'entity', 'entity_id', 'entity_sub_type', 'slug', 'created_at'])->where(['slug' => $slug])
            ->andWhere(['status' => Qna::STATUS_ACTIVE])
            ->orderBy(['updated_at' => SORT_DESC]);

        if (empty($query->all()) && $entity == null) {
            return [];
        }

        if (!empty($entity)) {
            $query->with(['answers', 'student', $entity]);
        }
        $query->with([
            'answers' => function ($query) {
                $query->select(['id', 'answer', 'created_at', 'updated_at', 'question_id']);
            },
            'student'
        ]);
        $qna_info = $query->all();

        if (empty($qna_info)) {
            $display_name = '';
            $entity_id = QnaCommonService::$filterDbData[$entity];
            if ($entity == 'course') {
                $query = new Query();
                $query->select(['id', 'short_name', 'slug'])->from($entity)->where(['slug' => $slug]);
                $entity_info = $query->one();
                $display_name = $entity_info['short_name'];
            } else {
                $query = new Query();
                $query->select(['id', 'display_name', 'slug'])->from($entity)->where(['slug' => $slug]);
                $entity_info = $query->one();
                $display_name = $entity_info['display_name'];
            }
            $query = Qna::find()->where(['entity_id' => $entity_info['id']])
                ->andWhere(['status' => Qna::STATUS_ACTIVE])
                ->orderBy(['updated_at' => SORT_DESC]);
            if (!empty($entity)) {
                $query->with(['answers', 'student', $entity]);
            } else {
                $query->with(['answers', 'student']);
            }
            $qna_info = $query->all();
            if (empty($qna_info)) {
                $qna_info[0]['entity'] = $entity_id;
                $qna_info[0]['slug'] = $entity_info['slug'];
                $qna_info[0]['display_name'] = $display_name;
                $qna_info[0]['entity_id'] = $entity_info['id'];
            }
        } else {
            foreach ($qna_info as $value) {
                foreach ($value->answers as $answer) {
                    $vote = $this->getAnswerVotes($answer);
                    $voteArr[$answer->id] = ['upvote' => $vote['upVote'], 'downvote' => $vote['downVote'], 'userUpVote' => $vote['userUpVote'], 'userDownVote' => $vote['userDownVote']];
                }
            }
            array_push($qna_info, $voteArr);
        }
        return $qna_info ?? [];
    }

    public function getRelatedQna($slug)
    {
        $question = Qna::find()->select(['entity', 'entity_id', 'slug'])->where(['slug' => $slug])
            ->andWhere(['status' => Qna::STATUS_ACTIVE])
            ->one();
        if (!$question) {
            return [];
        }

        $query = Qna::find()
            ->select('forum_question.*')
            ->innerJoin('forum_answer', 'forum_answer.question_id = forum_question.id')->where(['entity' => $question->entity, 'entity_id' => $question->entity_id, 'forum_question.status' => Qna::STATUS_ACTIVE])->andWhere(['!=', 'slug', $question->slug])->andWhere(['!=', 'forum_answer.status', 0])
            ->all();
        if (empty($query)) {
            $query = Qna::find()
                ->select('forum_question.*')
                ->innerJoin('forum_answer', 'forum_answer.question_id = forum_question.id')->where(['entity' => $question->entity, 'forum_question.status' => Qna::STATUS_ACTIVE])->andWhere(['!=', 'slug', $question->slug])->andWhere(['!=', 'forum_answer.status', 0])
                ->all();
        }
        return $query;
    }

    public function getQnaLandingPageSearch($qnaTab = '', $qnaParams = '', $qnaFilter = null, $subFilter = null, $subPageFilter = null, $limit = 10, $offSet = 0)
    {
        $voteArr = [];
        $qnaFilter = !empty($qnaFilter) ? (int) $qnaFilter : null;
        if ($qnaTab == 'unanswered') {
            $active_questions = QnaAnswer::find()->select(['question_id'])->where(['status' => QnaAnswer::STATUS_ACTIVE])->distinct()->all();
            $active_q_array = [];
            foreach ($active_questions as $value) {
                $active_q_array[] = $value->question_id;
            }
            $query = new Query();
            $query->select(['fq.*', 'fq.id as qn_id', 's.*'])
                ->from('forum_question fq')
                ->leftJoin('forum_answer as fa', 'fa.question_id = fq.id')
                ->leftJoin('student as s', 's.id = fq.student_id')
                ->where(['fq.status' => Qna::STATUS_ACTIVE])
                ->andWhere(['NOT IN', 'fq.id', $active_q_array]);
            if (!empty($qnaFilter)) {
                $query->andWhere(['entity' => $qnaFilter]);
            }
            if (!empty($subFilter)) {
                $query->andWhere(['in', 'entity_id', $subFilter]);
            }
            if (!empty($qnaParams)) {
                $query->andWhere(['fq.id' => $qnaParams]);
            }
            if (!empty($subPageFilter)) {
                $query->andWhere(['in', 'entity_sub_type', $subPageFilter]);
            }
            $totalQuestions = $this->getTotalQuestions($query);

            $query->orderBy(['fq.updated_at' => SORT_DESC]);

            // $pages = new Pagination([
            //     'totalCount' => $query->count(),
            //     'defaultPageSize' => 10
            // ]);
            $models = $query->offset($offSet)
                ->limit($limit)
                ->all();
            return ['models' => $models, 'total_question' => $totalQuestions, 'votes' => []];
        } else if ($qnaTab == 'search') {
            return Qna::find()->where(['status' => Qna::STATUS_ACTIVE])->andWhere(['like', 'question', '%' . $qnaParams . '%', false])->limit(10)->all();
        } else {
            $query = Qna::find()->joinWith(['qnAnswers', 'student'])
                ->where(['forum_question.status' => Qna::STATUS_ACTIVE])
                ->andWhere(['forum_answer.status' => QnaAnswer::STATUS_ACTIVE]);
            if ($qnaTab == 'popular') {
                $query->andWhere(['forum_question.is_popular' => Qna::POPULAR_YES]);
            }
            if (!empty($qnaFilter)) {
                $query->andWhere(['entity' => $qnaFilter]);
            }
            if (!empty($subFilter)) {
                $query->andWhere(['in', 'entity_id', $subFilter]);
            }
            if (!empty($qnaParams)) {
                $query->andWhere(['forum_question.id' => $qnaParams]);
            }
            if (!empty($subPageFilter)) {
                $query->andWhere(['in', 'entity_sub_type', $subPageFilter]);
            }
            $query->groupBy(['forum_question.id'])
                ->orderBy(['forum_question.updated_at' => SORT_DESC]);
            $totalQuestions = $this->getTotalQuestions($query);

            $cloneQuery = clone $query;
            // $pages = new Pagination([
            //     'totalCount' => $cloneQuery->count(),
            //     'defaultPageSize' => 10,
            // ]);
            $models = $query->offset($offSet)
                ->limit($limit)
                ->all();

            foreach ($models as $value) {
                foreach ($value->qnAnswers as $answer) {
                    $vote = $this->getAnswerVotes($answer);
                    $voteArr[$answer->id] = ['upvote' => $vote['upVote'], 'downvote' => $vote['downVote'], 'userUpVote' => $vote['userUpVote'], 'userDownVote' => $vote['userDownVote']];
                }
            }
            return ['models' => $models, 'total_question' => $totalQuestions, 'votes' => $voteArr];
        }
    }

    public function getQnaEntityFilter($qnaFilter)
    {
        $filter_array = array_flip(QnaCommonService::$filterDbData);
        $filter_table = $filter_array[$qnaFilter];
        // if($qnaFilter == 5 || $qnaFilter == 6){
        //     return Yii::$app->gmudb->createCommand((new \yii\db\Query)->select('*')->from($filter_table))->all();
        // }
        // else{
        $query = new Query();
        $query->select([$filter_table . '.id', $filter_table . '.name', $filter_table . '.slug'])
            ->from($filter_table)->leftJoin('forum_question as fq', 'fq.entity_id = ' . $filter_table . '.id')
            ->where(['fq.entity' => (int) $qnaFilter])
            ->andWhere([$filter_table . '.status' => Qna::STATUS_ACTIVE])
            ->andWhere(['fq.status' => Qna::STATUS_ACTIVE])
            ->distinct()
            ->limit(10);
        $entity_data = $query->all();
        return $entity_data;
        // }
    }

    public function getEntitySubTypeId($slug, $entity)
    {
        $page = ['info', 'about', 'overview'];
        if (in_array($slug, $page)) {
            return array_keys(DataHelper::$subPages[$entity]);
        } else {
            $subPageArr = DataHelper::$subPages[$entity];
            foreach ($subPageArr as $key => $value) {
                if ($value['slug'] == $slug) {
                    return $key;
                }
            }
        }
    }

    public function getTotalQuestions($query)
    {
        return $query->count();
    }

    public function getTotalQuestionsAnswers()
    {
        $total_question = Qna::find()->where(['status' => Qna::STATUS_ACTIVE])->count();
        $total_answer = QnaAnswer::find()->where(['status' => QnaAnswer::STATUS_ACTIVE])->count();

        return ['total_question' => $total_question, 'total_answer' => $total_answer];
    }

    public function getSearchFilter($qnaFilter, $qnaParams)
    {
        $filter_array = array_flip(QnaCommonService::$filterDbData);
        $filter_table = $filter_array[$qnaFilter];
        // if($qnaFilter == 5 || $qnaFilter == 6){
        //     return Yii::$app->gmudb->createCommand((new \yii\db\Query)->select('*')->from($filter_table))->all();
        // }
        // else{
        $query = new Query();
        $query->select(['id', 'name', 'slug'])
            ->from($filter_table)
            ->where(['like', 'name', '%' . $qnaParams . '%', false])
            ->andWhere([$filter_table . '.status' => Qna::STATUS_ACTIVE]);
        $search_data = $query->all();
        return $search_data;
        // }
    }

    // public function getSubpageByEntity($entity, $pageName, $qnaDetails)
    // {
    //     $pageIds = DataHelper::$subPages[$entity];
    //     $pageId = 0;
    //     foreach ($pageIds as $key => $page) {
    //         if ($page['slug'] == $pageName) {
    //             $pageId = $key == 39 ? 38 : $key;
    //         }
    //     }
    //     if (count($qnaDetails) > 2) {
    //         foreach ($qnaDetails as $value) {
    //             if ($value->entity_sub_type == $pageId) {
    //                 $qnaDetails = $value;
    //             }
    //         }
    //     } else {
    //         if ($qnaDetails[0]->entity_sub_type == $pageId) {
    //             $qnaDetails = $qnaDetails;
    //         }
    //     }

    //     return $qnaDetails;
    // }

    public function checkQnaDetails($entity_id)
    {
        $checkQna = Qna::find()->where(['entity_id' => $entity_id])
            ->andWhere(['status' => Qna::STATUS_ACTIVE])
            ->one();

        return $checkQna ?? '';
    }

    private function getAnswerVotes($answer)
    {
        $upVote = ForumVotes::find()->where(['answer_id' => $answer->id, 'vote' => 2])->count();
        $downVote = ForumVotes::find()->where(['answer_id' => $answer->id, 'vote' => 1])->count();

        if (!(Yii::$app->user->isGuest)) {
            $userUpVote = ForumVotes::find()->where(['answer_id' => $answer->id, 'vote' => 2, 'student_id' => Yii::$app->user->identity->id])->count();
            $userDownVote = ForumVotes::find()->where(['answer_id' => $answer->id, 'vote' => 1, 'student_id' => Yii::$app->user->identity->id])->count();
        } else {
            $userUpVote = 0;
            $userDownVote = 0;
        }

        return ['upVote' => $upVote, 'downVote' => $downVote, 'userUpVote' => $userUpVote, 'userDownVote' => $userDownVote];
    }

    //to be removed later qna
    public function getQnaLandingPage()
    {
        $limit = 10;
        $offSet = 0;
        $data = [];

        $query = Qna::find()->joinWith(['qnAnswers' => function ($query) {
            $query->select(['id', 'answer', 'question_id']);
        }, 'student'])
            ->where(['forum_question.status' => Qna::STATUS_ACTIVE])
            ->andWhere(['forum_answer.status' => QnaAnswer::STATUS_ACTIVE])
            ->groupBy(['forum_question.id'])
            ->orderBy(['forum_question.updated_at' => SORT_DESC]);

        $data['totalQuestions'] = $this->getTotalQuestions($query);

        $data['landingPage'] = $query->offset($offSet)
            ->limit($limit)
            ->all();

        foreach ($data['landingPage'] as $value) {
            foreach ($value->qnAnswers as $answer) {
                $vote = $this->getAnswerVotes($answer);
                $data['votes'][$answer->id] = ['upvote' => $vote['upVote'], 'downvote' => $vote['downVote'], 'userUpVote' => $vote['userUpVote'], 'userDownVote' => $vote['userDownVote']];
            }
        }

        return $data;
    }

    //to be removed later qna
    public function getEntityQnaLandingPage($slug, $entity)
    {
        $entityModel = '\common\models\\' . ucfirst($entity);
        $model = $entityModel::find()->select(['id'])->where(['slug' => $slug])->one();
        $entity_id = QnaCommonService::$filterDbData[$entity];

        $limit = 10;
        $offSet = 0;
        $data = [];

        $query = Qna::find()->joinWith(['qnAnswers', 'student'])
            ->where(['forum_question.status' => Qna::STATUS_ACTIVE])
            ->andWhere(['forum_answer.status' => QnaAnswer::STATUS_ACTIVE])
            ->andWhere(['entity' => $entity_id])
            ->andWhere(['entity_id' => $model->id])->groupBy(['forum_question.id'])
            ->orderBy(['forum_question.updated_at' => SORT_DESC]);

        $data['totalQuestions'] = $this->getTotalQuestions($query);

        $data['landingPage'] = $query->offset($offSet)
            ->limit($limit)
            ->all();

        foreach ($data['landingPage'] as $value) {
            foreach ($value->qnAnswers as $answer) {
                $vote = $this->getAnswerVotes($answer);
                $data['votes'][$answer->id] = ['upvote' => $vote['upVote'], 'downvote' => $vote['downVote'], 'userUpVote' => $vote['userUpVote'], 'userDownVote' => $vote['userDownVote']];
            }
        }

        return $data;
    }
}
