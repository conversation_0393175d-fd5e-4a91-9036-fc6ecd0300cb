<?php

namespace common\services;

use common\helpers\DataHelper;
use common\models\AlternateCtaText;
use common\models\Article;
use common\models\Board;
use common\models\City;
use common\models\Course;
use common\models\Exam;
use common\models\LeadBucket;
use common\models\LeadBucketCta;
use common\models\LeadBucketTagging;
use common\models\MediaDrive;
use common\models\MediaDriveCtaTypeMapping;
use common\models\MediaDriveUploadType;
use common\models\old\GmuUsers;
use common\models\State;
use common\models\StudentAcademicDetials;
use common\models\StudentPreference;
use common\models\User;
use frontend\helpers\Url;
use Symfony\Component\VarDumper\Cloner\Data;
use Yii;
use yii\caching\TagDependency;
use yii\db\Query;
use yii\helpers\Inflector;
use yii\web\Response;

class UserService
{
    protected $user;
    const CURRENT_YEAR = '2025';

    public function userById($identifier)
    {
        if (empty($identifier)) {
            return $this;
        }

        $this->user = GmuUsers::find()->where(['id' => $identifier])->one();

        return $this;
    }

    public function firstName()
    {
        if (!empty($this->user)) {
            return $this->user->first_name;
        }

        return '';
    }

    public function detail()
    {
        return $this->user;
    }
    /**
     * Getting User deatils By Slug
     * @param string $slug | Image Slug
     * @return object
     */
    public function getUserBySlug($slug)
    {
        $query =  new Query();
        $query->select(['profile.image'])
            ->from('profile')
            ->innerJoin('user user', 'user.id = profile.user_id')
            ->where(['user.slug' => $slug]);

        return $query->one() ?? null;
    }

    public static function hasSeoRole()
    {
        return Yii::$app->authManager->getAssignment('SEO', Yii::$app->user->identity->id) ? true : false;
    }

    public static function hasContentRole()
    {
        return Yii::$app->authManager->getAssignment('Content', Yii::$app->user->identity->id) ? true : false;
    }

    public static function getCityId($cityName = '', $id = '', $stateId = '', $stateName = '')
    {
        $result = [
            'cityId' => null,
            'stateId' => null
        ];

        if (!empty($stateId)) {
            $state = State::find()->where(['id' => $stateId])->one();
            return $state ?? [];
        }

        if (!empty($stateName)) {
            $stateName = State::find()->select(['id'])->where(['name' => $stateName])->one();
            return $stateName ?? [];
        }

        $query = City::find()->select(['id', 'state_id', 'name']);

        if (!empty($cityName)) {
            $query->where(['name' => $cityName]);
        }

        if (!empty($id)) {
            $query->where(['id' => $id]);
        }

        $cityId = $query->one();

        if (empty($cityId['id'])) {
            return $result;
        }

        return  [
            'cityId' => $cityId['id'] ?? null,
            'stateId' => $cityId['state_id'] ?? null,
            'cityName' => $cityId['name'] ?? null
        ];
    }

    public static function getBucketTagging($entity, $page, $name = '', $slug = '', $city = '')
    {
        $key = 'cta-' . $entity;
        // $data = Yii::$app->cache->getOrSet($key, function () use ($entity, $page, $name, $slug, $city) {
        $item = [];
        $entity_column = 'lbt.entity_id';
        if ($entity == LeadBucket::LEAD_ENTITY_NEWS || $entity == LeadBucket::LEAD_ENTITY_ARTICLE) {
            $articleQuery = new Query();
            $result = $articleQuery->select(['lb.entity_id as parent_entity', 'lbt.entity', 'lbt.entity_id', 'lbt.article_id', 'lbt.news_id', 'lbt.bucket_id'])
                ->from(LeadBucketTagging::tableName() . ' lbt')
                ->innerJoin(LeadBucket::tableName() . ' as lb', 'lb.id = lbt.bucket_id')
                ->where(['lbt.entity' => $entity, 'lbt.status' => LeadBucket::STATUS_ACTIVE])
                ->andWhere([
                    'or',
                    ['lbt.entity_id' => $page],
                    ['lbt.article_id' => $page],
                    ['lbt.news_id' => $page]
                ])
                ->all();
            foreach ($result as $key => $value) {
                if ($value['parent_entity'] == LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE) {
                    $entity_column = ($entity == LeadBucket::LEAD_ENTITY_ARTICLE) ? 'lbt.article_id' : 'lbt.news_id';
                }
            }
        }
        if (($entity == LeadBucket::LEAD_ENTITY_NEWS || $entity == LeadBucket::LEAD_ENTITY_ARTICLE) && ($entity_column == 'lbt.entity_id')) {
            $query = new Query();
            $query->select(['lb.lead_form_title', 'lb.status', 'lb.lead_form_description', 'lb.cta_text', 'lb.cta_title', 'lb.page_event', 'lb.page_link', 'lbt.cta_position', 'lbt.web', 'lbt.wap', 'lb.template_id'])
                ->from(['lead_bucket1 as lb'])
                ->rightJoin('lead_bucket_tagging1 as lbt', 'lbt.bucket_id = lb.id')
                ->where(['lbt.entity' => $entity]);

            if ($entity == LeadBucket::LEAD_ENTITY_NEWS || $entity == LeadBucket::LEAD_ENTITY_ARTICLE || $entity == LeadBucket::LEAD_ENTITY_NCERT) {
                $query->andWhere(['lbt.entity_id' => $page]);
            } else {
                $query->andWhere(['lbt.sub_page' => $page]);
            }
        } else {
            $query = new Query();
            $query->select(['lbc.lead_form_title', 'lbc.alternate_cta_text_id', 'lbc.cta_id', 'lb.status', 'lbc.lead_form_description', 'lbc.cta_text', 'lbc.cta_title', 'lbc.page_event', 'lbc.page_link', 'lbc.cta_position', 'lbc.web', 'lbc.wap', 'lb.template_id'])
                ->from([LeadBucket::tableName() . ' lb'])
                ->innerJoin(LeadBucketCta::tableName() . ' as lbc', 'lbc.bucket_id = lb.id')
                ->rightJoin(LeadBucketTagging::tableName() . ' as lbt', 'lbt.bucket_id = lb.id')
                ->where(['lbt.entity' => $entity, 'lb.status' => LeadBucket::STATUS_ACTIVE]);

            if ($entity == LeadBucket::LEAD_ENTITY_NEWS || $entity == LeadBucket::LEAD_ENTITY_ARTICLE || $entity == LeadBucket::LEAD_ENTITY_NCERT) {
                $query->andWhere([$entity_column => $page]);
            } else {
                $query->andWhere(['lbt.sub_page' => $page]);
            }

            $query->groupBy('cta_position');

            if ($entity == LeadBucket::LEAD_ENTITY_COLLEGES) {
                if ($page == 'info') {
                    $query->orderBy([new \yii\db\Expression('FIELD(lbc.cta_position, "card_top_cta", "card_center_cta", "card_right_cta2", "card_right_cta1", "card_right_cta", "card_left_cta", "bottom_right_cta", "bottom_left_cta", "top_right_cta", "top_left_cta")DESC')]);
                } else {
                    $query->orderBy([new \yii\db\Expression('FIELD(lbc.cta_position, "card_top_cta", "card_left_cta", "card_center_cta",  "card_right_cta2","card_right_cta1", "card_right_cta", "bottom_right_cta", "bottom_left_cta", "top_right_cta", "top_left_cta")DESC')]);
                }
            }
            if ($entity == LeadBucket::LEAD_ENTITY_COLLEGE_LISTING) {
                $query->orderBy([new \yii\db\Expression('FIELD(lbc.cta_position, "single_top_cta")ASC')]);
            }

            if ($entity == LeadBucket::LEAD_ENTITY_EXAM || $entity == LeadBucket::LEAD_ENTITY_BOARDS || $entity == LeadBucket::LEAD_ENTITY_COURSES) {
                $query->orderBy([new \yii\db\Expression('FIELD(lbc.cta_position, "bottom_right_cta", "bottom_left_cta", "top_right_cta", "top_left_cta")DESC')]);
            }
            if ($entity == LeadBucket::LEAD_ENTITY_ARTICLE || $entity == LeadBucket::LEAD_ENTITY_NEWS || $entity == LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE) {
                $query->orderBy([new \yii\db\Expression('FIELD(lbc.cta_position, "top_right_cta", "top_left_cta", "bottom_right_cta", "bottom_left_cta")DESC')]);
            }
        }

        $dynamicCtas = $query->all();
        foreach ($dynamicCtas as $key => $dynamicCta) {
            if (!empty($dynamicCta['cta_id'])) {
                $alternateText = self::alternativeCtaText($dynamicCta['cta_id'], $entity, $page, $dynamicCta['cta_text']);
                $alternateTextName = AlternateCtaText::find()->where(['id' => $dynamicCta['alternate_cta_text_id']])->one();
            } elseif (!isset($dynamicCta['cta_id']) && ($entity !== LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE && $entity !== LeadBucket::LEAD_ENTITY_ARTICLE)) {
                $alternateText = self::alternativeCtaTextProductPages($entity, $page, $slug, $dynamicCta['cta_text']);
                $alternateTextName = AlternateCtaText::find()->where(['id' => $dynamicCta['alternate_cta_text_id']])->one();
            }

            $item['cta_position_' . $key] = [
                'lead_form_title' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : (!empty($alternateText) && $alternateText['result'] == true && !empty($alternateTextName) && !empty($alternateTextName->cta_title) ? self::parseDynamicCta($alternateTextName->cta_title) : self::parseDynamicCta($dynamicCta['lead_form_title'], $name, $slug, $city)),
                // 'lead_form_title' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : self::parseDynamicCta($dynamicCta['lead_form_title'], $name, $slug, $city),
                'lead_form_description' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : self::parseDynamicCta($dynamicCta['lead_form_description'], $name, $slug, $city),
                'cta_text' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : (!empty($alternateText) && $alternateText['result'] == true && !empty($alternateTextName) ? self::parseDynamicCta($alternateTextName->cta_text) : self::parseDynamicCta($dynamicCta['cta_text'], $name, $slug, $city)),
                'cta_title' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : self::parseDynamicCta($dynamicCta['cta_title'], $name, $slug, $city),
                'page_event' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : $dynamicCta['page_event'],
                'page_link' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : $dynamicCta['page_link'],
                'web' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : self::parseDynamicCta($dynamicCta['web'], '', $slug),
                'wap' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : self::parseDynamicCta($dynamicCta['wap'], '', $slug),
                'template' => (int)$dynamicCta['status'] == LeadBucketCta::STATUS_INACTIVE ? '' : (int) $dynamicCta['template_id'],
                'alternate' => $alternateText['cta'] ?? '',
                'alternate_text' => $alternateText['result'] ?? '',
                'media' => $alternateText['media']['media_value'] ?? '',
                'page_redirect_slug' => $alternateText['media']['page_redirect_slug'] ?? '',
                'auto_pop_up_text' => $dynamicCta['cta_position'] == 'auto_popup_cta' ? $dynamicCta['cta_text'] : '',
                'auto_pop_up_title' => $dynamicCta['cta_position'] == 'auto_popup_cta' ? self::parseDynamicCta($dynamicCta['lead_form_title'], $name, $slug, $city) : ''
            ];
        }

        return $item;
        // }, 60 * 30 , new TagDependency(['tags' => 'cta-' . $entity]));
        // return $data ?? [];
    }

    public static function parseDynamicCta($ctaData, $name = '', $slug = '', $city = '')
    {
        $data = strtr($ctaData, [
            '{name}' => empty($name) ? $slug : $name,
            '{slug}' => $slug ?? '',
            '{year}' =>  self::CURRENT_YEAR,
            '{city}' => $city ?? '',
            '{download}' => '<span class="spriteIcon whiteDownloadIcon redDownloadIcon"></span>',
            '{list}' => '<span class="spriteIcon applyRedIcon"></span>',
            '{white_list}' => '<span class="spriteIcon applyWhiteIconCta"></span>',
            '{course}' =>  $slug
        ]);

        return $data ?? [];
    }

    public static function alternativeCtaText($ctaId, $entity, $entityId, $ctaText)
    {
        $entityNameMapping = [
            1 => 'Article',
            6 => 'NewsSubdomain',
        ];

        if (!isset($entityNameMapping[$entity])) {
            return [];
        }

        $modelClass = '\\common\\models\\' . $entityNameMapping[$entity];
        $modelData = $modelClass::findOne($entityId);

        return self::productMappingData($modelData, $ctaId, $ctaText);
    }

    public static function productMappingData($data, $ctaId, $ctaText)
    {
        $mappedProducts = [];
        foreach (DataHelper::$ctaMappingCategory as $label => $relation) {
            if (!empty($data->$relation)) {
                $mappedProducts =
                    [
                        'entity' => $relation,
                        'entity_id' => $data->$relation[0]->id
                    ];
                break;
            }
        }

        if (empty($mappedProducts)) {
            return [];
        }

        $uploadTypeQuery = MediaDriveCtaTypeMapping::find()
            ->select(['upload_type_id'])
            ->where(['cta_id' => $ctaId, 'entity' => $mappedProducts['entity'], 'status' => MediaDrive::STATUS_ACTIVE])
            ->one();

        $mediaFile = $uploadTypeQuery ? MediaDrive::find()
            ->select(['file_name'])
            ->where([
                'entity' => $mappedProducts['entity'],
                'entity_id' => $mappedProducts['entity_id'],
                'sub_page' => $uploadTypeQuery->upload_type_id,
                'status' => MediaDrive::STATUS_ACTIVE
            ])
            ->orderBy(['id' => SORT_DESC])
            ->one() : null;

        $ctaTextFinal = self::cleanText($ctaText, $mappedProducts);
        $entityUrl = $mappedProducts['entity'] == 'exam' ? 'exams' : $mappedProducts['entity'];
        $column = isset(DataHelper::$ctaEntityFieldsArr[$mappedProducts['entity']]) ? DataHelper::$ctaEntityFieldsArr[$relation] : '';
        $result = [];

        if (!empty($column)) {
            $model = '\common\models\\' . ucfirst($column['model']);
            $result = $model::find()->select(['id', $column['where'], $column['column_name']])
                ->where([$column['column_name'] => $ctaTextFinal['ctaTextFinal']])
                ->andWhere([$column['where'] => $mappedProducts['entity_id']])
                ->andWhere(['status' => 1])
                ->one();
        }

        if (!$ctaTextFinal['downloadIcon']) {
            if ($ctaTextFinal['predict_text']) {
                $model = '\common\models\\' . ucfirst($mappedProducts['entity']);
                $modelContent = '\common\models\\' . ucfirst($column['model']);

                $result = $modelContent::find()->select(['id', $column['where'], $column['column_name']])
                    ->with([$mappedProducts['entity']])
                    ->where(['IN', $column['column_name'], $ctaTextFinal['subpageValue']])
                    ->andWhere([$column['where'] => $mappedProducts['entity_id']])
                    ->andWhere(['status' => 1])
                    ->one();
            }

            return $alternateText = [
                'cta' => $result ? '' : $ctaTextFinal['ctaTextFinal'],
                'result' => !$result,
                'media' => $result ? [
                    'media_value' => 'page_redirect',
                    'page_redirect_slug' => !empty($result) ? Url::base(true) . '/' . $entityUrl . '/' . $result->{$mappedProducts['entity']}->slug . '-' . Inflector::slug($result[$column['column_name']]) : '',
                ] : [],
            ];
        }

        $alternateText = [
            'cta' => $ctaTextFinal['ctaTextFinal'],
            'result' => !$mediaFile && !$result,
            'media' => !$mediaFile && $result ? ['media_value' => 'page_download', 'page_redirect_slug' => ''] : []
        ];

        return $alternateText;
    }

    public static function cleanText($ctaText, $mappedProducts)
    {
        $cleanCtaText = preg_replace('/\{.*?\}/', '', $ctaText);
        $ctaTextFinal = trim($cleanCtaText);

        if (is_array($mappedProducts) && count($mappedProducts) > 1) { // articles and news
            $predict = (bool) preg_match('/^Discover Your Predicted Score\b/i', $ctaTextFinal);
            $subpageValue = array_values(DataHelper::$ctaSubPageNameRedirect) ?? [];
        } else {
            $predict = (bool) preg_match('/^Discover Your Predicted Score\b/i', $ctaTextFinal);
            $subpageValue = array_values(DataHelper::$ctaSubPageNameRedirect) ?? [];
        }

        $downloadIcon = (bool) preg_match('/\{(whiteDownloadIcon|redDownloadIcon|download)\}/', $ctaText);

        if (isset($mappedProducts['entity']) && in_array($mappedProducts['entity'], ['course', 'college']) || in_array($mappedProducts, ['course', 'college'])) {
            $ctaTextFinal = Inflector::slug($ctaTextFinal);
        }

        return [
            'ctaTextFinal' => $ctaTextFinal,
            'predict_text' => $predict,
            'downloadIcon' => $downloadIcon,
            'subpageValue' => $predict ? $subpageValue : ''
        ];
    }

    public static function alternativeCtaTextProductPages($entity, $page, $slug, $cta_text)
    {
        $mediaFile = [];
        $modelClass = isset(DataHelper::$ctaOtherCategoryarray[$entity]['model']) ? '\\common\\models\\' . DataHelper::$ctaOtherCategoryarray[$entity]['model'] : [];

        if (empty($modelClass)) {
            return [];
        }

        $modelData = $modelClass::find()
            ->where(['slug' => $slug])
            ->one();

        if (empty($modelData)) {
            return [];
        }

        $ctaTextFinal = self::cleanText($cta_text, DataHelper::$ctaOtherCategoryarray[$entity]['entity']);

        if (!empty($ctaTextFinal['ctaTextFinal']) && $ctaTextFinal['downloadIcon'] == true) {
            $mediaFile = MediaDriveUploadType::find()
                ->alias('mdu')
                ->select(['md.entity_id', 'md.page', 'md.sub_page'])
                ->leftJoin('media_drive md', 'md.sub_page = mdu.id')
                ->where([
                    'mdu.entity' => DataHelper::$ctaOtherCategoryarray[$entity]['entity'],
                    'md.entity_id' => $modelData->id,
                    'md.status' => MediaDrive::STATUS_ACTIVE
                ])
                ->andWhere(new \yii\db\Expression("upload_type REGEXP '" . preg_quote($ctaTextFinal['ctaTextFinal'], '/') . "?'"))
                ->asArray()
                ->all();
        }

        $column = isset(DataHelper::$ctaEntityFieldsArr[DataHelper::$ctaOtherCategoryarray[$entity]['entity']]) ? DataHelper::$ctaEntityFieldsArr[DataHelper::$ctaOtherCategoryarray[$entity]['entity']] : '';
        $result = [];

        if (!empty($column)) {
            $model = '\common\models\\' . ucfirst($column['model']);
            $result = $model::find()->select(['id', $column['where'], $column['column_name']])
                ->where([$column['column_name'] => $ctaTextFinal['ctaTextFinal']])
                ->andWhere([$column['where'] => $modelData->id])
                ->andWhere(['status' => 1])
                ->one();
        }
        if (!$ctaTextFinal['downloadIcon']) {
            $entityArray = [2, 5];
            if ($ctaTextFinal['predict_text']) {
                $modelContent = '\common\models\\' . ucfirst($column['model']);
                $result = $modelContent::find()->select(['id', $column['where'], $column['column_name']])
                    ->with([DataHelper::$ctaOtherCategoryarray[$entity]['entity']])
                    ->where(['IN', $column['column_name'], $ctaTextFinal['subpageValue']])
                    ->andWhere([$column['where'] => $modelData->id])
                    ->andWhere(['status' => 1])
                    ->one();
            }

            return $alternateText = [
                'cta' => $result ? '' : $ctaTextFinal['ctaTextFinal'],
                'result' => !$result,
                'media' => $result && in_array($entity, $entityArray) && $ctaTextFinal['subpageValue'] !== '' ? [
                    'media_value' => 'page_redirect',
                    'page_redirect_slug' => !empty($result) ? Url::base(true) . '/' . DataHelper::$ctaOtherCategoryarray[$entity]['entityRedirect'] . '/' . $slug . '-' . Inflector::slug($result[$column['column_name']]) : '',
                ] : [],
            ];
        }
        $alternateText = [
            'cta' => $ctaTextFinal['ctaTextFinal'],
            'result' => !$mediaFile && !$result,
            'media' => !$mediaFile && $result ? ['media_value' => 'page_download', 'page_redirect_slug' => ''] : []
        ];

        return $alternateText;
    }

    public static function hasCommentUpdateRole()
    {
        return Yii::$app->authManager->getAssignment('CommentUpdate', Yii::$app->user->identity->id) ? true : false;
    }

    public static function getDefaultUserData()
    {
        $user = User::find()->where(['slug' => 'getmyuni-content-team'])->one();
        return $user;
    }

    public static function getStreamLevelForAmp($id)
    {
        $highest_qualification_degree_id = ['9', '10', '11'];
        $streamLevel = Article::find()->select(['stream_id', 'highest_qualification'])->where(['id' => $id])->one();
        if (empty($streamLevel)) {
            return [];
        }
        //22 -> other
        if ($streamLevel['stream_id'] == 22 || in_array($streamLevel['highest_qualification'], $highest_qualification_degree_id)) {
            return [];
        }
        return $streamLevel;
    }

    public static function hasCollegeSlugUpdateRole()
    {
        return Yii::$app->authManager->getAssignment('CollegeSlugUpdate', Yii::$app->user->identity->id) ? true : false;
    }

    public static function hasNewsArticleInactiveRole()
    {
        // Check for either the role or the permission
        return Yii::$app->authManager->checkAccess(Yii::$app->user->identity->id, 'InactiveStatus');
        // Yii::$app->authManager->getAssignment('InactiveStatus', Yii::$app->user->identity->id) ||
    }
}
