<?php

namespace common\services\gis;

class CalculatorFactory
{
    public function getCalculator($calculatorType)
    {
        switch ($calculatorType) {
            case 'prCalculator':
                return new PrCalculator();
                break;

            case 'clbConverter':
                return new ClbConverter();
                break;

            case 'saskatchewanCalculator':
                return new SaskatchewanCalculator();
                break;

            case 'crsCalculator':
                return new CrsCalculator();
                break;

            case 'australiaPrPointsCalculator':
                return new AustraliaPrPointsCalculatorService();
                break;

            case 'germanyoppCalculator':
                return new GermanyOppCalculatorService();
                break;

            default:
                # code...
                break;
        }
    }
}
