<?php

namespace common\services\gis;

use common\models\calculator\GermanyOppCalculator;
use common\services\interfaces\ICalculator;

class GermanyOppCalculatorService implements ICalculator
{
    const COUNTRY_CITIZEHSHIP = 0;
    const FINANCIAL_PROOF  = 0;
    const QUALIFICATION = [
        'Bachelor' => '6',
        'Master' => '6',
        'Vocational' => '4',
        'None' => '0'
    ];
    const WORK_EXP = [
        '3-years' => '3',
        '1-3-years' => '2',
        '1-year' => '0',
    ];
    const GERMANY_LANGUAGE = [
        'b1-higher' => '3',
        'a1-a2' => '2',
        'none' => '0',
    ];
    const ENGLISH_LANGUAGE = [
        'b2-higher' => '2',
        'none' => '0',
    ];
    const AGE = [
        '<35' => '2',
        '35-40' => '1',
        '>40' => '0',
    ];
    const STAY_GERMANY_AND_SPOUSE = [
        '1' => '1',
        '0' => '0',
    ];


    public function calculate($data)
    {
        $score =  $citizenship = $finance = $qualification = $work_exp = $language = $age = $germanyPrevStay = $germanySpouseApplication = 0;

        if (isset($data['countryCitizenship']) && $data['countryCitizenship'] != '') {
            $score = $score + self::COUNTRY_CITIZEHSHIP;
            $citizenship = GermanyOppCalculator::YES_NO[$data['countryCitizenship']];
        }
        if (isset($data['financialProof']) && $data['financialProof'] != '') {
            $score = $score + self::FINANCIAL_PROOF;
            $finance = GermanyOppCalculator::FINANCIAL_PROOF_LIST[$data['financialProof']];
        }
        if (isset($data['qualification']) && $data['qualification'] != '') {
            $score = $score + self::QUALIFICATION[$data['qualification']];
            $qualification += self::QUALIFICATION[$data['qualification']];
        }
        if (isset($data['workExperience']) && $data['workExperience'] != '') {
            $score = $score + self::WORK_EXP[$data['workExperience']];
            $work_exp += self::WORK_EXP[$data['workExperience']];
        }

        if (isset($data['germanyLanguage']) && $data['germanyLanguage'] != '') {
            $score = $score + self::GERMANY_LANGUAGE[$data['germanyLanguage']];
            $language = $language + self::GERMANY_LANGUAGE[$data['germanyLanguage']];
        }
        if (isset($data['englishLanguage']) && $data['englishLanguage'] != '') {
            $score = $score + self::ENGLISH_LANGUAGE[$data['englishLanguage']];
            $language = $language + self::ENGLISH_LANGUAGE[$data['englishLanguage']];
        }

        if (isset($data['age']) && $data['age'] != '') {
            $score = $score + self::AGE[$data['age']];
            $age += self::AGE[$data['age']];
        }

        if (isset($data['germanyPrevStay']) && $data['germanyPrevStay'] != '') {
            $score = $score + self::STAY_GERMANY_AND_SPOUSE[$data['germanyPrevStay']];
            $germanyPrevStay += self::STAY_GERMANY_AND_SPOUSE[$data['germanyPrevStay']];
        }

        if (isset($data['germanySpouseApplication']) && $data['germanySpouseApplication'] != '') {
            $score = $score + self::STAY_GERMANY_AND_SPOUSE[$data['germanySpouseApplication']];
            $germanySpouseApplication += self::STAY_GERMANY_AND_SPOUSE[$data['germanySpouseApplication']];
        }

        return ['score' => $score, 'citizenship' => $citizenship, 'finance' => $finance, 'qualification' => $qualification, 'work_exp' => $work_exp, 'language' => $language, 'age' => $age, 'germanyPrevStay' => $germanyPrevStay, 'germanySpouseApplication' => $germanySpouseApplication];
    }
}
