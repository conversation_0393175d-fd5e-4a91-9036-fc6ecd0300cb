<?php

namespace common\services\gis;

use common\models\calculator\AustraliaPrPointsCalculator;
use common\services\interfaces\ICalculator;

class AustraliaPrPointsCalculatorService implements ICalculator
{

    const AGE_SCORE = [
        '18-24' => 25,
        '25-32' => 30,
        '33-39' => 25,
        '40-44' => 15
    ];

    const ENGLISH_PRO_SCORE = [
        'Competent English' => 0,
        'Proficient English' => 10,
        'Superior English' => 20
    ];

    const WORK_EXP_ABD_SCORE = [
        '0-2.11' => 0,
        '3-5' => 5,
        '5-8' => 10,
        '8+' => 15,
    ];

    const WORK_EXP_AUS_SCORE = [
        '0-1' => 0,
        '1-3' => 5,
        '3-5' => 10,
        '5-8' => 15,
        '8+' => 20,
    ];

    const OVERSEAS_EDUCATION = [
        'Diploma or Trade qualification' => 10,
        "Bachelor's Degree" => 15,
        'Master’s Degree' => 10,
        'Ph.D. (Doctor of Philosophy)' => 20
    ];

    const PARTNER_CATEGORY = [
        'Single' => 10,
        'Your spouse has applied for the same visa subclass as you, and possesses competent English proficiency, but is neither an Australian PR nor a citizen' => 5,
        'Spouse holds an Australian PR or Citizenship' => 10,
        'Your spouse is below 45 years, has competent English proficiency, a positive skills assessment for a suitable occupation (excluding Subclass 485), and applied for the same visa subclass as you' => 10
    ];

    const NOMINATION_SPONSORSHIP = [
        "You received an invitation to apply for Subclass 491 skilled work Regional (Provisional) visa since you were nominated, and the nominating State/Territory government agency didn't withdraw the nomination" => 15,
        'Your family member is sponsoring you for a Skilled Work Regional (Provisional) visa (Subclass 491), and the sponsorship has been accepted by the minister' => 15,
        'Not Applicable' => 0
    ];


    const AUS_EDU_QULI = [
        AustraliaPrPointsCalculator::YES => 5,
        AustraliaPrPointsCalculator::NO => 0,
    ];
    const REG_EDU_QUALI = [
        AustraliaPrPointsCalculator::YES => 5,
        AustraliaPrPointsCalculator::NO => 0,
    ];
    const SPECILIZATION = [
        AustraliaPrPointsCalculator::YES => 10,
        AustraliaPrPointsCalculator::NO => 0,
    ];
    const LANGUAGE_TEST = [
        AustraliaPrPointsCalculator::YES => 5,
        AustraliaPrPointsCalculator::NO => 0,
    ];
    const PROFESSIONAL_YEAR = [
        AustraliaPrPointsCalculator::YES => 5,
        AustraliaPrPointsCalculator::NO => 0,
    ];
    const NOMINATION = [
        AustraliaPrPointsCalculator::YES => 5,
        AustraliaPrPointsCalculator::NO => 0,
    ];

    public function calculate($data)
    {
        $score = $score190 = $score491 = $basic = $workExp = $eduQual = $splLang = $additional = 0;
        if (isset($data['age']) && $data['age'] != '') {
            $score += self::AGE_SCORE[$data['age']];
            $basic += self::AGE_SCORE[$data['age']];
        }
        if (isset($data['englishProficiency']) && $data['englishProficiency'] != '') {
            $score += self::ENGLISH_PRO_SCORE[$data['englishProficiency']];
            $basic += self::ENGLISH_PRO_SCORE[$data['englishProficiency']];
        }
        if (isset($data['workExperienceAbroad']) && $data['workExperienceAbroad'] != '') {
            $score += self::WORK_EXP_ABD_SCORE[$data['workExperienceAbroad']];
            $workExp += self::WORK_EXP_ABD_SCORE[$data['workExperienceAbroad']];
        }
        if (isset($data['workExperienceAus']) && $data['workExperienceAus'] != '') {
            $score += self::WORK_EXP_AUS_SCORE[$data['workExperienceAus']];
            $workExp += self::WORK_EXP_AUS_SCORE[$data['workExperienceAus']];
        }
        if (isset($data['overseaseducation']) && $data['overseaseducation'] != '') {
            $score += self::OVERSEAS_EDUCATION[$data['overseaseducation']];
            $eduQual += self::OVERSEAS_EDUCATION[$data['overseaseducation']];
        }
        if (isset($data['auseducationqualification']) && $data['auseducationqualification'] != '') {
            $score += self::AUS_EDU_QULI[$data['auseducationqualification']];
            $eduQual += self::AUS_EDU_QULI[$data['auseducationqualification']];
        }
        if (isset($data['regeducationqualification']) && $data['regeducationqualification'] != '') {
            $score += self::REG_EDU_QUALI[$data['regeducationqualification']];
            $eduQual += self::REG_EDU_QUALI[$data['regeducationqualification']];
        }
        if (isset($data['specialization']) && $data['specialization'] != '') {
            $score += self::SPECILIZATION[$data['specialization']];
            $splLang += self::SPECILIZATION[$data['specialization']];
        }
        if (isset($data['languagetest']) && $data['languagetest'] != '') {
            $score += self::LANGUAGE_TEST[$data['languagetest']];
            $splLang += self::LANGUAGE_TEST[$data['languagetest']];
        }
        if (isset($data['partnercategory']) && $data['partnercategory'] != '') {
            $score += self::PARTNER_CATEGORY[$data['partnercategory']];
            $additional += self::PARTNER_CATEGORY[$data['partnercategory']];
        }
        if (isset($data['professionalyear']) && $data['professionalyear'] != '') {
            $score += self::PROFESSIONAL_YEAR[$data['professionalyear']];
            $additional += self::PROFESSIONAL_YEAR[$data['professionalyear']];
        }
        if (isset($data['nomination']) && $data['nomination'] != '') {
            $score190 = $score + self::NOMINATION[$data['nomination']];
            $additional +=  self::NOMINATION[$data['nomination']];
        }
        if (isset($data['nominationsponsorship']) && $data['nominationsponsorship'] != '') {
            $score491 = $score + self::NOMINATION_SPONSORSHIP[$data['nominationsponsorship']];
            $additional += self::NOMINATION_SPONSORSHIP[$data['nominationsponsorship']];
        }
        return ['score189' => $score, 'score190' => $score190, 'score491' => $score491, 'totalScore' => ($score + $score190 + $score491), 'basic' => $basic, 'workExp' => $workExp, 'eduQuali' => $eduQual, 'splLang' => $splLang, 'additional' => $additional];
    }
}
