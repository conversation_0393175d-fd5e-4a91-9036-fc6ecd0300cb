<?php

namespace common\services\gis;

use common\models\calculator\CrsCalculator as CalculatorCrsCalculator;
use common\services\interfaces\ICalculator;

class CrsCalculator implements ICalculator
{
    const AGE_SCORE_WITH_SPOUSE = [
        'under_17' => 0,
        18 => 90,
        19 => 95,
        '20_29' => 100,
        30 => 95,
        31 => 90,
        32 => 85,
        33 => 80,
        34 => 75,
        35 => 70,
        36 => 65,
        37 => 60,
        38 => 55,
        39 => 50,
        40 => 45,
        41 => 35,
        42 => 25,
        43 => 15,
        44 => 5,
        'above_45' => 0
    ];

    const AGE_SCORE_WITHOUT_SPOUSE = [
        'under_17' => 0,
        18 => 99,
        19 => 105,
        '20_29' => 110,
        30 => 105,
        31 => 99,
        32 => 94,
        33 => 88,
        34 => 83,
        35 => 77,
        36 => 72,
        37 => 66,
        38 => 61,
        39 => 55,
        40 => 50,
        41 => 39,
        42 => 28,
        43 => 17,
        44 => 6,
        'above_45' => 0
    ];

    const EDUCATION_WITH_SPOUSE = [
        'Less than secondary school (high school)' => 0,
        'Secondary diploma (high school graduation)' => 28,
        'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute' => 84,
        'Two-year program at a university, college, trade or technical school, or other institute' => 91,
        "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute" => 112,
        'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years' => 119,
        "Master's degree, OR professional degree needed to practice in a licensed profession" => 126,
        'Doctoral level university degree (Ph.D.)' => 140
    ];

    const EDUCATION_WITHOUT_SPOUSE = [
        'Less than secondary school (high school)' => 0,
        'Secondary diploma (high school graduation)' => 30,
        'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute' => 90,
        'Two-year program at a university, college, trade or technical school, or other institute' => 98,
        "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute" => 120,
        'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years' => 128,
        "Master's degree, OR professional degree needed to practice in a licensed profession" => 135,
        'Doctoral level university degree (Ph.D.)' => 150
    ];

    const SPOUSE_EDUCATION = [
        'Less than secondary school (high school)' => 0,
        'Secondary diploma (high school graduation)' => 2,
        'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute' => 6,
        'Two-year program at a university, college, trade or technical school, or other institute' => 7,
        "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute" => 8,
        'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years' => 9,
        "Master's degree, OR professional degree needed to practice in a licensed profession" => 10,
        'Doctoral level university degree (Ph.D.)' => 10
    ];

    const EDUCATION_CLB_7_OR_MORE = [
        'Less than secondary school (high school)' => 0,
        'Secondary diploma (high school graduation)' => 0,
        'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute' => 13,
        'Two-year program at a university, college, trade or technical school, or other institute' => 13,
        "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute" => 13,
        'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years' => 25,
        "Master's degree, OR professional degree needed to practice in a licensed profession" => 25,
        'Doctoral level university degree (Ph.D.)' => 25
    ];

    const EDUCATION_CLB_9_OR_MORE = [
        'Less than secondary school (high school)' => 0,
        'Secondary diploma (high school graduation)' => 0,
        'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute' => 25,
        'Two-year program at a university, college, trade or technical school, or other institute' => 25,
        "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute" => 25,
        'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years' => 50,
        "Master's degree, OR professional degree needed to practice in a licensed profession" => 50,
        'Doctoral level university degree (Ph.D.)' => 50
    ];

    const EDUCATION_ONE_YR_EXP = [
        'Less than secondary school (high school)' => 0,
        'Secondary diploma (high school graduation)' => 0,
        'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute' => 13,
        'Two-year program at a university, college, trade or technical school, or other institute' => 13,
        "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute" => 13,
        'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years' => 25,
        "Master's degree, OR professional degree needed to practice in a licensed profession" => 25,
        'Doctoral level university degree (Ph.D.)' => 50
    ];

    const EDUCATION_TWO_OR_MORE_YR_EXP = [
        'Less than secondary school (high school)' => 0,
        'Secondary diploma (high school graduation)' => 0,
        'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute' => 25,
        'Two-year program at a university, college, trade or technical school, or other institute' => 25,
        "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute" => 25,
        'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years' => 50,
        "Master's degree, OR professional degree needed to practice in a licensed profession" => 50,
        'Doctoral level university degree (Ph.D.)' => 50
    ];

    const EDUCATION_ADDITIONALPOINTS = [
        'Less than secondary school (high school)' => 0,
        'Secondary diploma (high school graduation)' => 0,
        'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute' => 15,
        'Two-year program at a university, college, trade or technical school, or other institute' => 15,
        "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute" => 30,
        'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years' => 30,
        "Master's degree, OR professional degree needed to practice in a licensed profession" => 30,
        'Doctoral level university degree (Ph.D.)' => 30
    ];

    const IELTS_READING_BAND = [
        'Band 8.0 - 9.0' => 10,
        'Band 7 - 7.5' => 9,
        'Band 6.5' => 8,
        'Band 6' => 7,
        'Band 5 - 5.5' => 6,
        'Band 4 - 4.5' => 5,
        'Band 3.5' => 4,
        'Band 0 - 3.5' => 4
    ];

    const IELTS_WRITING_BAND = [
        'Band 7.5 - 9.0' => 10,
        'Band 7' => 9,
        'Band 6.5' => 8,
        'Band 6' => 7,
        'Band 5.5' => 6,
        'Band 5' => 5,
        'Band 4 - 4.5' => 4,
        'Band 0 - 3.5' => 4
    ];

    const IELTS_LISTENING_BAND = [
        'Band 8.5 - 9.0' => 10,
        'Band 8' => 9,
        'Band 7.5' => 8,
        'Band 6 - 7' => 7,
        'Band 5.5' => 6,
        'Band 5' => 5,
        'Band 4.5' => 4,
        'Band 0 - 4' => 4
    ];

    const IELTS_SPEAKING_BAND = [
        'Band 7.5 - 9.0' => 10,
        'Band 7' => 9,
        'Band 6.5' => 8,
        'Band 6' => 7,
        'Band 5.5' => 6,
        'Band 5' => 5,
        'Band 4 - 4.5' => 4,
        'Band 0 - 3.5' => 4
    ];

    const CELPIP_READING_BAND = [
        'Band 10 - 12' => 10,
        'Band 9' => 9,
        'Band 8' => 8,
        'Band 7' => 7,
        'Band 6' => 6,
        'Band 5' => 5,
        'Band 4' => 4,
        'Band 0 - 3' => 4
    ];

    const CELPIP_WRITING_BAND = [
        'Band 10 - 12' => 10,
        'Band 9' => 9,
        'Band 8' => 8,
        'Band 7' => 7,
        'Band 6' => 6,
        'Band 5' => 5,
        'Band 4' => 4,
        'Band 0 - 3' => 4
    ];

    const CELPIP_LISTENING_BAND = [
        'Band 10 - 12' => 10,
        'Band 9' => 9,
        'Band 8' => 8,
        'Band 7' => 7,
        'Band 6' => 6,
        'Band 5' => 5,
        'Band 4' => 4,
        'Band 0 - 3' => 4
    ];

    const CELPIP_SPEAKING_BAND = [
        'Band 10 - 12' => 10,
        'Band 9' => 9,
        'Band 8' => 8,
        'Band 7' => 7,
        'Band 6' => 6,
        'Band 5' => 5,
        'Band 4' => 4,
        'Band 0 - 3' => 4
    ];

    const TEF_READING_BAND = [
        'Band 263 - 300' => 10,
        'Band 248 - 262' => 9,
        'Band 233 - 247' => 8,
        'Band 207 - 232' => 7,
        'Band 181 - 206' => 6,
        'Band 151 - 180' => 5,
        'Band 121 - 150' => 4,
        'Band 0 - 120' => 4
    ];

    const TEF_WRITING_BAND = [
        'Band 393 - 450' => 10,
        'Band 371 - 392' => 9,
        'Band 349 - 370' => 8,
        'Band 310 - 348' => 7,
        'Band 271 - 309' => 6,
        'Band 226 - 270' => 5,
        'Band 181 - 225' => 4,
        'Band 0 - 180' => 4
    ];

    const TEF_LISTENING_BAND = [
        'Band 316 - 360' => 10,
        'Band 298 - 315' => 9,
        'Band 280 - 297' => 8,
        'Band 249 - 279' => 7,
        'Band 217 - 248' => 6,
        'Band 181 - 216' => 5,
        'Band 145 - 180' => 4,
        'Band 0 - 144' => 4
    ];

    const TEF_SPEAKING_BAND = [
        'Band 393 - 450' => 10,
        'Band 371 - 392' => 9,
        'Band 349 - 370' => 8,
        'Band 310 - 348' => 7,
        'Band 271 - 309' => 6,
        'Band 226 - 270' => 5,
        'Band 181 - 225' => 4,
        'Band 0 - 180' => 4
    ];

    const TCF_READING_BAND = [
        'Band 549 - 699' => 10,
        'Band 524 - 548' => 9,
        'Band 499 - 523' => 8,
        'Band 453 - 498' => 7,
        'Band 406 - 452' => 6,
        'Band 375 - 405' => 5,
        'Band 342 - 374' => 4,
        'Band 0 - 341' => 4
    ];

    const TCF_WRITING_BAND = [
        'Band 16 - 20' => 10,
        'Band 14 - 15' => 9,
        'Band 12 - 13' => 8,
        'Band 10 - 11' => 7,
        'Band 7 - 9' => 6,
        'Band 6' => 5,
        'Band 4 - 5' => 4,
        'Band 0 - 3' => 4
    ];

    const TCF_LISTENING_BAND = [
        'Band 549 - 699' => 10,
        'Band 523 - 548' => 9,
        'Band 503 - 522' => 8,
        'Band 458 - 502' => 7,
        'Band 398 - 457' => 6,
        'Band 369 - 397' => 5,
        'Band 331 - 368' => 4,
        'Band 0 - 330' => 4
    ];

    const TCF_SPEAKING_BAND = [
        'Band 16 - 20' => 10,
        'Band 14 - 15' => 9,
        'Band 12 - 13' => 8,
        'Band 10 - 11' => 7,
        'Band 7 - 9' => 6,
        'Band 6' => 5,
        'Band 4 - 5' => 4,
        'Band 0 - 3' => 4
    ];

    const PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE = [
        10 => 32,
        9 => 29,
        8 => 22,
        7 => 16,
        6 => 8,
        5 => 6,
        4 => 6
    ];

    const PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE = [
        10 => 34,
        9 => 31,
        8 => 23,
        7 => 17,
        6 => 9,
        5 => 6,
        4 => 6
    ];

    const SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE = [
        10 => 6,
        9 => 6,
        8 => 3,
        7 => 3,
        6 => 1,
        5 => 1,
        4 => 0
    ];

    const SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE = [
        10 => 6,
        9 => 6,
        8 => 3,
        7 => 3,
        6 => 1,
        5 => 1,
        4 => 0
    ];

    const SPOUSE_PRIMARY_LANGUAGE_PROFICIENCY_SCORE = [
        10 => 5,
        9 => 5,
        8 => 3,
        7 => 3,
        6 => 1,
        5 => 1,
        4 => 0
    ];

    const SKILLED_EXP_WITH_SPOUSE = [
        '1_less' => 0,
        '1_year' => 35,
        '2_year' => 46,
        '3_year' => 56,
        '4_year' => 63,
        '5_more' => 70
    ];

    const SKILLED_EXP_WITHOUT_SPOUSE = [
        '1_less' => 0,
        '1_year' => 40,
        '2_year' => 53,
        '3_year' => 64,
        '4_year' => 72,
        '5_more' => 80
    ];

    const SPOUSE_SKILLED_EXP = [
        '1_less' => 0,
        '1_year' => 5,
        '2_year' => 7,
        '3_year' => 8,
        '4_year' => 9,
        '5_more' => 10
    ];

    const FOREIGN_EXP_CLB_7_OR_MORE = [
        '1_less' => 0,
        '1_year' => 13,
        '2_year' => 13,
        '3_more' => 25
    ];

    const FOREIGN_EXP_CLB_9_OR_MORE = [
        '1_less' => 0,
        '1_year' => 25,
        '2_year' => 25,
        '3_more' => 50
    ];

    const FOREIGN_EXP_ONE_YR_EXP = [
        '1_less' => 0,
        '1_year' => 13,
        '2_year' => 13,
        '3_more' => 25
    ];

    const FOREIGN_EXP_TWO_OR_MORE_YR_EXP = [
        '1_less' => 0,
        '1_year' => 25,
        '2_year' => 25,
        '3_more' => 50
    ];

    const CERTIFICATE_SCORE_CLB_5_OR_MORE = 25;
    const CERTIFICATE_SCORE_CLB_7_OR_MORE = 50;

    const RELATIVE_IN_CANADA_SCORE = [
        CalculatorCrsCalculator::YES => 15,
        CalculatorCrsCalculator::NO => 0,
    ];

    const NOMINATION_SCORE = [
        CalculatorCrsCalculator::YES => 600,
        CalculatorCrsCalculator::NO => 0,
    ];

    const VALID_JOB_SCORE = [
        'NOC Skill Type 00' => 200,
        'NOC Skill Level A or B or any other Type 0 other than 00' => 50,
        'NOC Skill Level C or D' => 0
    ];

    /**
     * CRS calculator
     * maximum achievable points 1200
     * A - Core Factors
     * B - Spouse Core Factor
     * C - Transferability factors
     * D - Additional Points
     * A+B+C+D = 1200
     */
    public function calculate($data)
    {
        $score = $coreFactorScore = $spouseCoreFactor = $transferabilityScore = $additionalPoints = $clbScore = 0;
        $ageeduQuali = $maritial = $officialLang = $workExp = $additional = 0;

        if ($data['maritalStatus'] == '') {
            return ['score' => $score, 'maritial' => $maritial, 'ageeduQuali' => $ageeduQuali,  'officialLang' => $officialLang, 'workExp' => $workExp, 'additional' => $additional];
        }
        if (CalculatorCrsCalculator::MARITAL_STATUS_VALUE[$data['maritalStatus']]) {
            $coreFactorScore = $this->calculateCoreFactorsWithSpouse($data);
            $spouseCoreFactor = $this->calculateSpouseCoreFactors($data);
        } else {
            $coreFactorScore = $this->calculateCoreFactorsWithoutSpouse($data);
        }


        $transferabilityScore = $this->calculateTransferabilityScore($data);

        $additionalPoints = $this->calculateAdditionalScore($data);
        $score += $coreFactorScore + $spouseCoreFactor + $transferabilityScore + $additionalPoints;
        return ['score' => $score, 'maritial' => $maritial, 'ageeduQuali' => $ageeduQuali,  'officialLang' => $officialLang, 'workExp' => $workExp, 'additional' => $additional];
        // return $score;
    }

    /**
     * calculate core points - Age, Level of education, Official languages proficiency, Canadian work experience
     * max achievable points is 460 + 40
     */
    protected function calculateCoreFactorsWithSpouse($data)
    {
        $score = 0;

        // Age score
        if (isset($data['age']) && $data['age'] != '') {
            $score += self::AGE_SCORE_WITH_SPOUSE[$data['age']];
        }
        // Level of education score
        if (isset($data['education']) && $data['education'] != '') {
            $score += self::EDUCATION_WITH_SPOUSE[$data['education']];
        }

        // Official languages proficiency score
        if (($data['latestResult'] > 0) && $data['latestResult'] != '') {
            if ($data['primaryExam'] != '') {
                $primaryExam = $data['primaryExam'];
            }
            if ($data['primaryReadingScore'] != '') {
                $primaryReadingCLB = constant('self::' . $primaryExam . '_READING_BAND')[$data['primaryReadingScore']];
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE[$primaryReadingCLB];
            }

            if ($data['primaryWritingScore'] != '') {
                $primaryWritingCLB = constant('self::' . $primaryExam . '_WRITING_BAND')[$data['primaryWritingScore']];
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE[$primaryWritingCLB];
            }

            if ($data['primaryListeningScore'] != '') {
                $primaryListeningCLB = constant('self::' . $primaryExam . '_LISTENING_BAND')[$data['primaryListeningScore']];
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE[$primaryListeningCLB];
            }

            if ($data['primarySpeakingScore'] != '') {
                $primarySpeakingCLB = constant('self::' . $primaryExam . '_SPEAKING_BAND')[$data['primarySpeakingScore']];
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE[$primarySpeakingCLB];
            }
        }

        if (isset($data['secondaryLanguage']) && $data['secondaryLanguage'] != '' && $data['secondaryLanguage'] != 'NA') {
            $secondaryLanguageScore = 0;
            if ($data['secondaryLanguage'] != '') {
                $secondaryExam = $data['secondaryLanguage'];
            }

            if ($data['secondaryReadingScore'] != '') {
                $secondaryReadingClb = constant('self::' . $secondaryExam . '_READING_BAND')[$data['secondaryReadingScore']];
                $secondaryLanguageScore += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE[$secondaryReadingClb];
            }

            if ($data['secondaryWritingScore'] != '') {
                $secondaryWritingClb = constant('self::' . $secondaryExam . '_WRITING_BAND')[$data['secondaryWritingScore']];
                $secondaryLanguageScore += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE[$secondaryWritingClb];
            }

            if ($data['secondaryListeningScore'] != '') {
                $secondaryListeningClb = constant('self::' . $secondaryExam . '_LISTENING_BAND')[$data['secondaryListeningScore']];
                $secondaryLanguageScore += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE[$secondaryListeningClb];
            }

            if ($data['secondarySpeakingScore'] != '') {
                $secondarySpeakingClb = constant('self::' . $secondaryExam . '_SPEAKING_BAND')[$data['secondarySpeakingScore']];
                $secondaryLanguageScore += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITH_SPOUSE[$secondarySpeakingClb];
            }
            $score += ($secondaryLanguageScore > 22) ? 22 : $secondaryLanguageScore;
        }

        // Canadian work experience score
        if (isset($data['skilledLastTenYearExp']) && $data['skilledLastTenYearExp'] != '') {
            $score += self::SKILLED_EXP_WITH_SPOUSE[$data['skilledLastTenYearExp']];
        }
        return $score;
    }

    /**
     * calculate core points - Age, Level of education, Official languages proficiency, Canadian work experience
     * max achievable points is 500
     */
    protected function calculateCoreFactorsWithoutSpouse($data)
    {
        $score = 0;
        // Age score
        if (isset($data['age']) && $data['age'] != '') {
            $score += self::AGE_SCORE_WITHOUT_SPOUSE[$data['age']];
        }

        // Level of education score
        if (isset($data['education']) && $data['education'] != '') {
            $score += self::EDUCATION_WITHOUT_SPOUSE[$data['education']];
        }

        // Official languages proficiency score
        if ($data['latestResult'] != '' && $data['latestResult'] > 0) {
            if ($data['primaryExam'] != '') {
                $primaryExam = $data['primaryExam'];
            }

            if ($data['primaryReadingScore'] != '') {
                $primaryReadingCLB = constant('self::' . $primaryExam . '_READING_BAND')[$data['primaryReadingScore']];
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE[$primaryReadingCLB];
            }

            if ($data['primaryWritingScore'] != '') {
                $primaryWritingCLB = constant('self::' . $primaryExam . '_WRITING_BAND')[$data['primaryWritingScore']];
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE[$primaryWritingCLB];
            }

            if ($data['primaryListeningScore'] != '') {
                $primaryListeningCLB = constant('self::' . $primaryExam . '_LISTENING_BAND')[$data['primaryListeningScore']];
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE[$primaryListeningCLB];
            }

            if ($data['primarySpeakingScore'] != '') {
                $primarySpeakingCLB = constant('self::' . $primaryExam . '_SPEAKING_BAND')[$data['primarySpeakingScore']];
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE[$primarySpeakingCLB];
            }
        }

        if ($data['secondaryLanguage'] != '' && $data['secondaryLanguage'] != 'NA' && $data['secondaryLanguage'] > 0) {
            $secondaryExam = $data['secondaryLanguage'];
            if ($data['secondaryReadingScore'] != '') {
                $secondaryReadingClb = constant('self::' . $secondaryExam . '_READING_BAND')[$data['secondaryReadingScore']];
                $score += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE[$secondaryReadingClb];
            }

            if ($data['secondaryWritingScore'] != '') {
                $secondaryWritingClb = constant('self::' . $secondaryExam . '_WRITING_BAND')[$data['secondaryWritingScore']];
                $score += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE[$secondaryWritingClb];
            }

            if ($data['secondaryListeningScore'] != '') {
                $secondaryListeningClb = constant('self::' . $secondaryExam . '_LISTENING_BAND')[$data['secondaryListeningScore']];
                $score += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE[$secondaryListeningClb];
            }

            if ($data['secondarySpeakingScore'] != '') {
                $secondarySpeakingClb = constant('self::' . $secondaryExam . '_SPEAKING_BAND')[$data['secondarySpeakingScore']];
                $score += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE_WITHOUT_SPOUSE[$secondarySpeakingClb];
            }
        }

        // Canadian work experience score
        if (isset($data['skilledLastTenYearExp']) && $data['skilledLastTenYearExp'] != '') {
            $score += self::SKILLED_EXP_WITHOUT_SPOUSE[$data['skilledLastTenYearExp']];
        }
        return $score;
    }

    /*
    * spouse factors calculation
    * Level of education, Official language proficiency, Canadian Work Experience
    * */
    protected function calculateSpouseCoreFactors($data)
    {
        $score = 0;
        $exam = $readingClb = $writingClb = $listeningClb = $speakingClb = 0;

        // Level of education score
        if (isset($data['spouseEducation']) && $data['spouseEducation'] != '') {
            $score += self::SPOUSE_EDUCATION[$data['spouseEducation']];
        }

        // Official languages proficiency score
        if (($data['spouseLatestResult'] > 0) && $data['spouseLatestResult'] != '') {
            if ($data['spousePrimaryExam'] != '') {
                $exam = $data['spousePrimaryExam'];
            }
            if ($data['spousePrimaryReadingScore'] != '') {
                $readingClb = constant('self::' . $exam . '_READING_BAND')[$data['spousePrimaryReadingScore']];
                $score += self::SPOUSE_PRIMARY_LANGUAGE_PROFICIENCY_SCORE[$readingClb];
            }

            if (isset($data['spousePrimaryWritingScore']) && $data['spousePrimaryWritingScore'] != '') {
                $writingClb = constant('self::' . $exam . '_WRITING_BAND')[$data['spousePrimaryWritingScore']];
                $score += self::SPOUSE_PRIMARY_LANGUAGE_PROFICIENCY_SCORE[$writingClb];
            }

            if ($data['spousePrimaryListeningScore'] != '') {
                $listeningClb = constant('self::' . $exam . '_LISTENING_BAND')[$data['spousePrimaryListeningScore']];
                $score += self::SPOUSE_PRIMARY_LANGUAGE_PROFICIENCY_SCORE[$listeningClb];
            }

            if ($data['spousePrimarySpeakingScore'] != '') {
                $speakingClb = constant('self::' . $exam . '_SPEAKING_BAND')[$data['spousePrimarySpeakingScore']];
                $score += self::SPOUSE_PRIMARY_LANGUAGE_PROFICIENCY_SCORE[$speakingClb];
            }
        }

        // Canadian Work Experience score
        if (isset($data['spouseSkilledLastTenYearExp']) && $data['spouseSkilledLastTenYearExp'] != '') {
            $score += self::SPOUSE_SKILLED_EXP[$data['spouseSkilledLastTenYearExp']];
        }
        return $score;
    }

    /*
    * Transferability factors calculation
    * Education, Foreign work experience, Certificate of qualification
    * maximum achievable points 100
    * */
    protected function calculateTransferabilityScore($data)
    {
        $score = $educationScore = $foreignWorkExpScore = $certificateOfQualificationScore = 0;
        $primaryReadingCLB = $primaryWritingCLB = $primaryListeningCLB = $primarySpeakingCLB = 0;
        if ($data['latestResult'] && $data['latestResult'] != '') {
            if ($data['primaryExam'] != '') {
                $primaryExam = $data['primaryExam'];
            }
            if ($data['primaryReadingScore'] != '') {
                $primaryReadingCLB = constant('self::' . $primaryExam . '_READING_BAND')[$data['primaryReadingScore']];
            }
            if ($data['primaryWritingScore'] != '') {
                $primaryWritingCLB = constant('self::' . $primaryExam . '_WRITING_BAND')[$data['primaryWritingScore']];
            }
            if ($data['primaryListeningScore'] != '') {
                $primaryListeningCLB = constant('self::' . $primaryExam . '_LISTENING_BAND')[$data['primaryListeningScore']];
            }
            if ($data['primarySpeakingScore'] != '') {
                $primarySpeakingCLB = constant('self::' . $primaryExam . '_SPEAKING_BAND')[$data['primarySpeakingScore']];
            }

            if ($primaryReadingCLB >= 9 && $primaryWritingCLB >= 9 && $primaryListeningCLB >= 9 && $primarySpeakingCLB >= 9) {
                if ($data['education'] != '') {
                    $educationScore += self::EDUCATION_CLB_9_OR_MORE[$data['education']];
                }
                if ($data['foreignSkilledLastTenYearExp'] != '') {
                    $foreignWorkExpScore += self::FOREIGN_EXP_CLB_9_OR_MORE[$data['foreignSkilledLastTenYearExp']];
                }
            } else if ($primaryReadingCLB >= 7 && $primaryWritingCLB >= 7 && $primaryListeningCLB >= 7 && $primarySpeakingCLB >= 7) {
                if ($data['education'] != '') {
                    $educationScore += self::EDUCATION_CLB_7_OR_MORE[$data['education']];
                }
                if ($data['foreignSkilledLastTenYearExp'] != '') {
                    $foreignWorkExpScore += self::FOREIGN_EXP_CLB_7_OR_MORE[$data['foreignSkilledLastTenYearExp']];
                }
            }
        }

        if ($data['skilledLastTenYearExp'] != '' && $data['skilledLastTenYearExp'] == '1_year') {
            if ($data['education'] != '') {
                $educationScore += self::EDUCATION_ONE_YR_EXP[$data['education']];
            }
            if ($data['foreignSkilledLastTenYearExp'] != '') {
                $foreignWorkExpScore += self::FOREIGN_EXP_ONE_YR_EXP[$data['foreignSkilledLastTenYearExp']];
            }
        } else if (($data['skilledLastTenYearExp'] != '') && (in_array($data['skilledLastTenYearExp'], ['2_year', '3_year', '4_year', '5_more']))) {
            if ($data['education'] != '') {
                $educationScore += self::EDUCATION_TWO_OR_MORE_YR_EXP[$data['education']];
            }
            if ($data['foreignSkilledLastTenYearExp'] != '') {
                $foreignWorkExpScore += self::FOREIGN_EXP_TWO_OR_MORE_YR_EXP[$data['foreignSkilledLastTenYearExp']];
            }
        }
        if ($data['certificate'] && $data['certificate'] != '') {
            if ($primaryReadingCLB >= 7 && $primaryWritingCLB >= 7 && $primaryListeningCLB >= 7 && $primarySpeakingCLB >= 7) {
                $certificateOfQualificationScore += self::CERTIFICATE_SCORE_CLB_7_OR_MORE;
            } else if ($primaryReadingCLB >= 5 && $primaryWritingCLB >= 5 && $primaryListeningCLB >= 5 && $primarySpeakingCLB >= 5) {
                $certificateOfQualificationScore += self::CERTIFICATE_SCORE_CLB_5_OR_MORE;
            }
        }
        $educationScore = ($educationScore < 50) ? $educationScore : 50;
        $foreignWorkExpScore = ($foreignWorkExpScore < 50) ? $foreignWorkExpScore : 50;
        $certificateOfQualificationScore = ($certificateOfQualificationScore < 50) ? $certificateOfQualificationScore : 50;

        $score += $educationScore + $foreignWorkExpScore + $certificateOfQualificationScore;
        return ($score < 100) ? $score : 100;
    }

    /**
     * Additional Points calculations
     * maximum achievable points 600
     */
    protected function calculateAdditionalScore($data)
    {
        $score = 0;
        if ($data['relativeInCanada'] > 0 && $data['relativeInCanada'] != '') {
            $score += self::RELATIVE_IN_CANADA_SCORE[$data['relativeInCanada']];
        }

        if ($data['validJob'] != '' && $data['validJob'] > 0 && $data['noc'] != '' && isset($data['noc'])) {
            $score += self::VALID_JOB_SCORE[$data['noc']];
        }

        if ($data['nomination'] > 0 && $data['nomination'] != '') {
            $score += self::NOMINATION_SCORE[$data['nomination']];
        }

        if ($data['canadianDegree'] > 0 && $data['canadianEducation'] != '') {
            $score += self::EDUCATION_ADDITIONALPOINTS[$data['canadianEducation']];
        }

        return ($score < 600) ? $score : 600;
    }

    /**
     * calculate CLB score required for CRS calculation
     */
    protected function calculateCLBScore($examType, $reading, $writing, $listening, $speaking)
    {
        $request = [];
        $request['examType'] = $examType;
        $request['readingScore'] = $reading;
        $request['writingScore'] = $writing;
        $request['listeningScore'] = $listening;
        $request['speakingScore'] = $speaking;

        $clbConverter = new ClbConverter();
        return $clbConverter->calculate($request);
    }
}
