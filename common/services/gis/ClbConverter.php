<?php

namespace common\services\gis;

use common\services\interfaces\ICalculator;

class ClbConverter implements ICalculator
{
    public function calculate($data)
    {

        $reading = $data['readingScore'];
        $writing = $data['writingScore'];
        $listening = $data['listeningScore'];
        $speaking = $data['speakingScore'];
        $score = 0;

        switch ($data['examType']) {
            case 'IELTS':
                return $this->calculateIeltsScore($reading, $writing, $listening, $speaking);
                break;

            case 'CELPIP':
                return $this->calculateIeltsScore($reading, $writing, $listening, $speaking);
                break;

            case 'TCF':
                return $this->calculateIeltsScore($reading, $writing, $listening, $speaking);
                break;

            case 'TEF':
                return $this->calculateIeltsScore($reading, $writing, $listening, $speaking);
                break;

            default:
                break;
        }
    }

    public function calculateIeltsScore($reading, $writing, $listening, $speaking)
    {
        $score = 0;
        if ($listening <= '4.0' || $reading <= '3.0' || $speaking <= '3.5' || $writing <= '3.5') {
            $score = '3 or less';
        } else if ($listening <= '4.5' || $reading <= '3.5' || $speaking <= '4.5' || $writing <= '4.5') {
            $score = 4;
        } else if ($listening <= '5.0' || $reading <= '4.5' || $speaking <= '5.0' || $writing <= '5.0') {
            $score = 5;
        } else if ($listening <= '5.5' || $reading <= '5.5' || $speaking <= '5.5' || $writing <= '5.5') {
            $score = 6;
        } else if ($listening <= '7.0' || $reading <= '6.0' || $speaking <= '6.0' || $writing <= '6.0') {
            $score = 7;
        } else if ($listening <= '7.5' || $reading <= '6.5' || $speaking <= '6.5' || $writing <= '6.5') {
            $score = 8;
        } else if ($listening <= '9.0' || $reading <= '9.0' || $speaking <= '9.0' || $writing <= '9.0') {
            $score = 9;
        }
        return $score;
    }

    public function calculateCelpipScore($reading, $writing, $listening, $speaking)
    {
        $score = 0;
        if ($listening == '3' || $reading == '3' || $speaking == '3' || $writing == '3') {
            $score = '3 or less.';
        } else if ($listening == '4' || $reading == '4' || $speaking == '4' || $writing == '4') {
            $score = 4;
        } else if ($listening == '5' || $reading == '5' || $speaking == '5' || $writing == '5') {
            $score = 5;
        } else if ($listening == '6' || $reading == '6' || $speaking == '6' || $writing == '6') {
            $score = 6;
        } else if ($listening == '7' || $reading == '7' || $speaking == '7' || $writing == '7') {
            $score = 7;
        } else if ($listening == '8' || $reading == '8' || $speaking == '8' || $writing == '8') {
            $score = 8;
        } else if ($listening == '9' || $reading == '9' || $speaking == '9' || $writing == '9') {
            $score = '9+';
        }
        return $score;
    }
}
