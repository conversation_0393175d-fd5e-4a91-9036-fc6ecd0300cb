<?php

namespace common\services\gis;

use common\models\calculator\SaskatchewanCalculator as CalculatorSaskatchewanCalculator;
use common\services\interfaces\ICalculator;

class SaskatchewanCalculator implements ICalculator
{
    const EDUCATION_SCORE = [
        "PhD or Master's degree" => 23,
        "Bachelor's degree OR at least a three-year degree at a university or college" => 20,
        'Trade certification equivalent to journeyperson status in Saskatchewan' => 20,
        '2 Year Post secondary Diploma' => 15,
        '1 Year post secondary diploma' => 12
    ];

    const WORK_EXP_FIVE_YR_SCORE = [
        '5_years' => 10,
        '4_years' => 8,
        '3_years' => 6,
        '2_years' => 4,
        '1_years' => 2,
        'less_than_1' => 0,
    ];

    const WORK_EXP_TEN_YR_SCORE = [
        '5_years' => 5,
        '4_years' => 4,
        '3_years' => 3,
        '2_years' => 2,
        '1_years' => 0,
        'less_than_1' => 0,
    ];

    const AGE_SCORE = [
        'under_18' => 0,
        '18-21' => 8,
        '22-34' => 12,
        '35-45' => 10,
        '46-50' => 8,
        'above_50' => 0
    ];

    const PRIMARY_LANGUAGE_PROFICIENCY_SCORE = [
        CalculatorSaskatchewanCalculator::CLB_8_OR_HIGHER => 20,
        CalculatorSaskatchewanCalculator::CLB_7 => 18,
        CalculatorSaskatchewanCalculator::CLB_6 => 16,
        CalculatorSaskatchewanCalculator::CLB_5 => 14,
        CalculatorSaskatchewanCalculator::CLB_4 => 12,
        CalculatorSaskatchewanCalculator::CLB_NOT_APPLICABLE => 0
    ];

    const SECONDARY_LANGUAGE_PROFICIENCY_SCORE = [
        CalculatorSaskatchewanCalculator::CLB_8_OR_HIGHER => 10,
        CalculatorSaskatchewanCalculator::CLB_7 => 8,
        CalculatorSaskatchewanCalculator::CLB_6 => 6,
        CalculatorSaskatchewanCalculator::CLB_5 => 4,
        CalculatorSaskatchewanCalculator::CLB_4 => 2,
        CalculatorSaskatchewanCalculator::CLB_NOT_APPLICABLE => 0
    ];

    const EMPLOYMENT_OFFER_SCORE = [
        CalculatorSaskatchewanCalculator::YES => 30,
        CalculatorSaskatchewanCalculator::NO => 0
    ];

    const RELATIVE_IN_SASKATCHEWAN_SCORE = [
        CalculatorSaskatchewanCalculator::YES => 20,
        CalculatorSaskatchewanCalculator::NO => 0
    ];

    const STUDY_EXP_IN_SASKATCHEWAN_SCORE = [
        CalculatorSaskatchewanCalculator::YES => 5,
        CalculatorSaskatchewanCalculator::NO => 0
    ];

    const WORK_EXP_IN_SASKATCHEWAN_SCORE = [
        CalculatorSaskatchewanCalculator::YES => 5,
        CalculatorSaskatchewanCalculator::NO => 0
    ];

    public function calculate($data)
    {
        $score = $eduQual = $workExp = $additional = 0;
        if (isset($data['education']) && $data['education'] != '') {
            $score += self::EDUCATION_SCORE[$data['education']];
            $eduQual += self::EDUCATION_SCORE[$data['education']];
        }

        if (isset($data['workExperienceFiveYrPrior']) && $data['workExperienceFiveYrPrior'] != '') {
            $score += self::WORK_EXP_FIVE_YR_SCORE[$data['workExperienceFiveYrPrior']];
            $workExp += self::WORK_EXP_FIVE_YR_SCORE[$data['workExperienceFiveYrPrior']];
        }

        if (isset($data['workExperienceTenYrPrior']) && $data['workExperienceTenYrPrior'] != '') {
            $score += self::WORK_EXP_TEN_YR_SCORE[$data['workExperienceTenYrPrior']];
            $workExp += self::WORK_EXP_TEN_YR_SCORE[$data['workExperienceTenYrPrior']];
        }

        if (isset($data['age']) && $data['age'] != '') {
            $score += self::AGE_SCORE[$data['age']];
            $additional += self::AGE_SCORE[$data['age']];
        }

        /*
        * score for language proficiency
        * whichever langauge score is more that will be considered as primary langauge
        * */

        if ($data['englishProficiency'] != '' && $data['frenchProficiency'] != '') {
            if ($data['englishProficiency'] > $data['frenchProficiency']) {
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE[$data['englishProficiency']];
                $additional += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE[$data['englishProficiency']];
                $score += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE[$data['frenchProficiency']];
                $additional += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE[$data['frenchProficiency']];
            } else {
                $score += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE[$data['frenchProficiency']];
                $additional += self::PRIMARY_LANGUAGE_PROFICIENCY_SCORE[$data['frenchProficiency']];
                $score += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE[$data['englishProficiency']];
                $additional += self::SECONDARY_LANGUAGE_PROFICIENCY_SCORE[$data['englishProficiency']];
            }
        }


        // connection to saskatchewan score (max achievable points is 30)
        $connectionToSaskatchewanScore = 0;
        if (isset($data['employmentOffer']) && $data['employmentOffer'] != '') {
            $connectionToSaskatchewanScore += self::EMPLOYMENT_OFFER_SCORE[$data['employmentOffer']];
        }
        if (isset($data['relativeInSaskatchewan']) && $data['relativeInSaskatchewan'] != '') {
            $connectionToSaskatchewanScore += self::RELATIVE_IN_SASKATCHEWAN_SCORE[$data['relativeInSaskatchewan']];
        }
        if (isset($data['studyExperienceInSaskatchewan']) && $data['studyExperienceInSaskatchewan'] != '') {
            $connectionToSaskatchewanScore += self::STUDY_EXP_IN_SASKATCHEWAN_SCORE[$data['studyExperienceInSaskatchewan']];
        }
        if (isset($data['workExperienceInSaskatchewan']) && $data['workExperienceInSaskatchewan'] != '') {
            $connectionToSaskatchewanScore += self::WORK_EXP_IN_SASKATCHEWAN_SCORE[$data['workExperienceInSaskatchewan']];
        }

        $calculatedConnectionToSaskatchewanScore = ($connectionToSaskatchewanScore < 30) ? $connectionToSaskatchewanScore : 30;
        $score += $calculatedConnectionToSaskatchewanScore;
        $additional += $calculatedConnectionToSaskatchewanScore;
        return ['score' => $score, 'eduQuali' => $eduQual,  'workExp' => $workExp, 'additional' => $additional];
    }
}
