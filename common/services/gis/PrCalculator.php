<?php

namespace common\services\gis;

use common\models\calculator\PrCalculator as CalculatorPrCalculator;
use common\services\interfaces\ICalculator;

class PrCalculator implements ICalculator
{
    const AGE_SCORE = [
        'under_18' => 0,
        '18-35' => 12,
        '36' => 11,
        '37' => 10,
        '38' => 9,
        '39' => 8,
        '40' => 7,
        '41' => 6,
        '42' => 5,
        '43' => 4,
        '44' => 3,
        '45' => 2,
        '46' => 1,
        'above_47' => 0
    ];

    const EDUCATION_SCORE = [
        'University degree at the Doctoral (PhD) level or equal' => 25,
        "University degree at the Master's level or equal" => 23,
        'Bachelors degree theee or more years' => 21,
        'Prefessional degree needed to practice in licensed profession' => 23,
        'Two or more Canadian post-secondary degrees or diplomas or equal (at least one must be for a program of at least three years)' => 22,
        'Canadian post-secondary degree or diploma for a program of three years or longer, or equal' => 21,
        'Canadian post-secondary degree or diploma for a two-year program, or equal' => 19,
        'Canadian post-secondary degree or diploma for a one-year program, or equal' => 15,
        'Canadian high school diploma, or equal' => 5
    ];

    const WORK_EXP_SCORE = [
        '1 year' => 9,
        '2 - 3 years' => 11,
        '4 - 5 years' => 13,
        '6 or more' => 15
    ];

    const ENGLISH_READING_SCORE = [
        '8' => 6,
        '7' => 6,
        '6.5' => 5,
        '6' => 4
    ];
    const ENGLISH_WRITING_SCORE = [
        '7.5' => 6,
        '7' => 6,
        '6.5' => 5,
        '6' => 4
    ];
    const ENGLISH_LISTENING_SCORE = [
        '8.5' => 6,
        '8' => 6,
        '7.5' => 5,
        '6' => 4
    ];
    const ENGLISH_SPEAKING_SCORE = [
        '7.5' => 6,
        '7' => 6,
        '6.5' => 5,
        '6' => 4
    ];

    const YOUR_FRENCH_PROFICIENCY = [
        CalculatorPrCalculator::YES => 4,
        CalculatorPrCalculator::NO => 0
    ];

    const ARRANGE_EMPLOYMENT = [
        CalculatorPrCalculator::YES => 10,
        CalculatorPrCalculator::NO => 0
    ];

    const ADAPTABILTITY = [
        'youWorkedInCanada' => [
            CalculatorPrCalculator::YES => 10,
            CalculatorPrCalculator::NO => 0,
        ],
        'youStudiedInCanada' => [
            CalculatorPrCalculator::YES => 5,
            CalculatorPrCalculator::NO => 0,
        ],
        'relativeInCanada' => [
            CalculatorPrCalculator::YES => 5,
            CalculatorPrCalculator::NO => 0,
        ],
        'arrangedEmployment' => [
            CalculatorPrCalculator::YES => 5,
            CalculatorPrCalculator::NO => 0,
        ],
        // const ADAPTABILITY_MARRIED = [
        'spouseLanguageProficiency' => [
            CalculatorPrCalculator::YES => 5,
            CalculatorPrCalculator::NO => 0,
        ],
        'spouseWorkedInCanada' => [
            CalculatorPrCalculator::YES => 10,
            CalculatorPrCalculator::NO => 0,
        ],
        'spouseStudiedInCanada' => [
            CalculatorPrCalculator::YES => 10,
            CalculatorPrCalculator::NO => 0,
        ]
    ];

    public function calculate($data)
    {
        $score = $maritial = $additional = $lang = $basic = 0;
        if (isset($data['age']) && $data['age'] != '') {
            $score += self::AGE_SCORE[$data['age']];
            $basic += self::AGE_SCORE[$data['age']];
        }
        if (isset($data['education']) && $data['education'] != '') {
            $score += self::EDUCATION_SCORE[$data['education']];
            $basic += self::EDUCATION_SCORE[$data['education']];
        }
        if (isset($data['workExperience']) && $data['workExperience'] != '') {
            $score += self::WORK_EXP_SCORE[$data['workExperience']];
            $basic += self::WORK_EXP_SCORE[$data['workExperience']];
        }

        /*
        * score for language proficiency
        * */

        if (isset($data['englishReading']) && $data['englishReading'] != '') {
            $score += self::ENGLISH_READING_SCORE[$data['englishReading']];
            $lang += self::ENGLISH_READING_SCORE[$data['englishReading']];
        }
        if (isset($data['englishWriting']) && $data['englishWriting'] != '') {
            $score += self::ENGLISH_WRITING_SCORE[$data['englishWriting']];
            $lang += self::ENGLISH_WRITING_SCORE[$data['englishWriting']];
        }
        if (isset($data['englishListening']) && $data['englishListening'] != '') {
            $score += self::ENGLISH_LISTENING_SCORE[$data['englishListening']];
            $lang += self::ENGLISH_LISTENING_SCORE[$data['englishListening']];
        }
        if (isset($data['englishSpeaking']) && $data['englishSpeaking'] != '') {
            $score += self::ENGLISH_SPEAKING_SCORE[$data['englishSpeaking']];
            $lang += self::ENGLISH_SPEAKING_SCORE[$data['englishSpeaking']];
        }
        if (isset($data['yourFrenchProficiency']) && $data['yourFrenchProficiency'] != '') {
            $score += self::YOUR_FRENCH_PROFICIENCY[$data['yourFrenchProficiency']];
            $lang += self::YOUR_FRENCH_PROFICIENCY[$data['yourFrenchProficiency']];
        }
        if (isset($data['arrangedEmployment']) && $data['arrangedEmployment'] != '') {
            $score += self::ARRANGE_EMPLOYMENT[$data['arrangedEmployment']];
            $additional += self::ARRANGE_EMPLOYMENT[$data['arrangedEmployment']];
        }
        // adaptability score (max achievable points is 10)
        $adaptabilityScore = 0;
        if (isset($data['youWorkedInCanada']) && $data['youWorkedInCanada'] != '') {
            $adaptabilityScore += self::ADAPTABILTITY['youWorkedInCanada'][$data['youWorkedInCanada']];
        }
        if (isset($data['youStudiedInCanada']) && $data['youStudiedInCanada'] != '') {
            $adaptabilityScore += self::ADAPTABILTITY['youStudiedInCanada'][$data['youStudiedInCanada']];
        }
        if (isset($data['relativeInCanada']) && $data['relativeInCanada'] != '') {
            $adaptabilityScore += self::ADAPTABILTITY['relativeInCanada'][$data['relativeInCanada']];
        }
        if (isset($data['arrangedEmployment']) && $data['arrangedEmployment'] != '') {
            $adaptabilityScore += self::ADAPTABILTITY['arrangedEmployment'][$data['arrangedEmployment']];
        }

        $maritialAbilityScore = 0;
        if (isset($data['maritalStatus']) && $data['maritalStatus'] != '') {
            if (isset($data['spouseLanguageProficiency']) && $data['spouseLanguageProficiency'] != '') {
                $adaptabilityScore += self::ADAPTABILTITY['spouseLanguageProficiency'][$data['spouseLanguageProficiency']];
                $maritialAbilityScore += self::ADAPTABILTITY['spouseLanguageProficiency'][$data['spouseLanguageProficiency']];
            }
            if (isset($data['spouseWorkedInCanada']) && $data['spouseWorkedInCanada'] != '') {
                $adaptabilityScore += self::ADAPTABILTITY['spouseWorkedInCanada'][$data['spouseWorkedInCanada']];
                $maritialAbilityScore += self::ADAPTABILTITY['spouseWorkedInCanada'][$data['spouseWorkedInCanada']];
            }
            if (isset($data['spouseStudiedInCanada']) && $data['spouseStudiedInCanada'] != '') {
                $adaptabilityScore += self::ADAPTABILTITY['spouseStudiedInCanada'][$data['spouseStudiedInCanada']];
                $maritialAbilityScore += self::ADAPTABILTITY['spouseStudiedInCanada'][$data['spouseStudiedInCanada']];
            }
        }

        $calculatedAdaptabilityScore = ($adaptabilityScore < 10) ? $adaptabilityScore : 10;
        $maritialAbilityScores = ($maritialAbilityScore < 10) ? $maritialAbilityScore : 10;

        $score += $calculatedAdaptabilityScore;
        $maritial += $maritialAbilityScores;
        if ($maritial == 0 && (isset($data['arrangedEmployment']) && $data['arrangedEmployment'] != '')) {
            $additional += $calculatedAdaptabilityScore;
        } else if ($maritial > 0) {
            if (isset($data['arrangedEmployment']) && $data['arrangedEmployment'] != '') {
                $additional += $calculatedAdaptabilityScore;
                $additional = $additional - $maritial;
            }
        }
        return ['score' => $score, 'basic' => $basic,  'lang' => $lang, 'additional' => $additional, 'maritial' => $maritial];
    }
}
