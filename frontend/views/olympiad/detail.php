<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\helpers\OlympiadHelper;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use common\models\LiveUpdate;
use frontend\helpers\Ad;
use common\models\Olympiad;
use frontend\helpers\Freestartads;

// utils
$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = $author ? ContentHelper::getUserProfilePic($author->slug) : '';

// meta key
$defaultMeta = OlympiadHelper::getDefaultSeoInfo($page, $olympiad->name);

$this->title = !empty($content->meta_title) ? $content->meta_title : $defaultMeta['title'];
$this->context->description = empty($content->meta_description) ? $defaultMeta['description'] : $content->meta_description;

$this->context->ogType = 'olympiad';


if (!empty($article->cover_image)) {
    $this->registerMetaTag(['property' => 'og:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'og:image:height', 'content' => '667']);
    $this->registerMetaTag(['property' => 'og:image:alt', 'content' => $content->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:alt', 'content' => $content->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:type', 'content' => 'image/jpeg']);
    $this->registerMetaTag(['property' => 'twitter:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'twitter:image:height', 'content' => '667']);
    $this->registerLinkTag(['href' => OlympiadHelper::getImage($olympiad->cover_image), 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => OlympiadHelper::getImage($article->cover_image) . ' 300w', 'imagesizes' => '50vw']);
    $this->context->ogImage = OlympiadHelper::getImage($olympiad->cover_image);
}

if (!empty($authorImage)) {
    $this->registerLinkTag(['href' => $authorImage, 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => $authorImage . ' 300w', 'imagesizes' => '50vw']);
}

$this->registerMetaTag(['name' => 'robots', 'content' => 'max-image-preview:large']);
$this->registerMetaTag(['property' => 'olympiad:published_time', 'content' => date(DATE_ATOM, strtotime($content->created_at))]);
$this->registerMetaTag(['property' => 'olympiad:modified_time', 'content' => date(DATE_ATOM, strtotime($content->updated_at))]);

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'Olympiad', 'url' => Url::to('/olympiad'), 'title' => 'Olympiad'];
if ($page != 'overview') {
    $this->params['breadcrumbs'][] = ['label' => $olympiad->name, 'url' => [Url::to($olympiad->slug)], 'title' => $olympiad->name];
}
$this->params['breadcrumbs'][] = $page == 'overview' ? $olympiad->name : (DataHelper::$olympiadSubPages[$page] ?? '');

$this->params['entity_name'] = $olympiad->name ?? '';
$this->params['entity_id'] = $olympiad->id ?? 0;
$this->params['entity'] = Olympiad::ENTITY_OLYMPIAD;
$this->params['entitySlug'] = $olympiad->slug;
$this->params['product_mapping_entity'] = empty($entityName) ? 'olympiad' : $entityName;
$this->params['product_mapping_entity_id'] = empty($entityId) ? 0 : $entityId;
$this->params['canonicalUrl'] = $currentUrl;
$this->params['pageName'] = $page;
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'subpage-navbar.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'career-detail.css', ['depends' => [AppAsset::class]]);


// schema
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}

$this->params['schema'] = \yii\helpers\Json::encode([[
    '@context' => 'http://schema.org',
    '@type' => 'Article',
    'mainEntityOfPage' => [
        '@type' => 'WebPage',
        '@id' => $currentUrl,
    ],
    'headline' => $this->title,
    'image' => [OlympiadHelper::getImage($olympiad->cover_image)],
    'datePublished' => date(DATE_ATOM, strtotime($content->created_at)),
    'dateModified' => date(DATE_ATOM, strtotime($content->updated_at)),
    'author' => [
        '@type' => 'Person',
        'name' => $author ? $author->name : ''
    ],
    'publisher' => [
        '@type' => 'Organization',
        'name' => 'Getmyuni',
        'logo' => [
            '@type' => 'ImageObject',
            'url' => Yii::$app->params['gmuLogo']
        ],
    ],
    'description' => $content->meta_description
]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);


?>

<div class="containerMargin">
    <div class="row">
        <div class="col-md-12">
            <div class="articleHeader">
                <section class="pageDescription">
                    <div class="row">
                        <div class="col-md-12">
                            <h1><?= empty($content->h1) ? $defaultMeta['h1'] : $content->h1 ?></h1>
                            <div class="authorInfoAndTranslateBtn">
                                <div class="updated-info row">
                                    <img class="lazyload" loading="lazy" width="60" height="60" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author ? $author->name . ' Image' : '' ?>" />
                                    <span class="updatedDetails">
                                        <?php if ($author): ?>
                                            <div class="updatedBy">
                                                <p><a href="<?= $author ? '/author/' . $author->slug : '#' ?>"><?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?></a>, </p>
                                            </div>
                                            <p><span><?= Yii::$app->formatter->asDate($content->updated_at ?? 'today') ?> </span>
                                        <?php endif; ?>
                                            </p>
                                            <ul>
                                                <p>Share it on:</p>
                                                <li>
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $currentUrl ?>" target="_blank" rel="noopener nofollow" class="spriteIcon greyFbIcon"></a>
                                                </li>
                                                <li>
                                                    <a href="https://twitter.com/share?url=<?= $currentUrl ?>" class="spriteIcon greyTwitterIcon" rel="noopener nofollow" target="_blank"></a>
                                                </li>
                                            </ul>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            <?php if (!empty($menus)): ?>
                <?= $this->render('partials/_menu-card', [
                    'isMobile' => $isMobile,
                    'menus' => $menus,
                    'pageName' => $page,
                    'olympiad' => $olympiad
                ]);
                ?>
            <?php endif; ?>
        </div>
        <div class="col-md-8">
            <main>
                <article>
                    <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                        <div class="horizontalRectangle">
                            <div class="appendAdDiv" style="<?= $isMobile ? 'height: 50px;' : '' ?>background:#EAEAEA;">
                                <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                                ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="articleInfo">
                        <?= ContentHelper::removeStyleTag(stripslashes(
                            html_entity_decode(DataHelper::parseDomainUrlInContent($content->content))
                        )) ?>
                    </div>

                </article>
                <?php if (!empty($faqs)): ?>
                    <section class="faq_section">
                        <h2>FAQs</h2>
                        <div class="faqDiv">

                            <?php foreach ($faqs as $faq): ?>
                                <div>
                                    <p class="faq_question">
                                        <?= ContentHelper::htmlDecode($faq->question, true) ?>
                                    </p>
                                    <div class="faq_answer" style="display: none;">
                                        <?= 'A: ' . ContentHelper::htmlDecode($faq->answer, false) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                        </div>
                    </section>
                <?php endif; ?>

            </main>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280') ?>
                        <?php else: ?>
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

        </div>
        <div class="col-md-4">
            <aside>
                <?= $this->render('partials/_lead', [
                    'olympiad' => $olympiad,
                    'page' => $page
                ]) ?>

                <?php if (!empty($otherExplore) && count($otherExplore) > 1): ?>
                    <?= $this->render('partials/_other-olympiad-list', [
                        'defaultMeta' => $defaultMeta,
                        'quickLinks' => $otherExplore,
                        'olympiad' => $olympiad,
                        'page' => $page
                    ]); ?>
                <?php endif; ?>
                <?php /**
                <?php if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>
                <?php if (!empty($trendingArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif; ?>
                 */ ?>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="squareDiv">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__200x600')
                                ?>
                            <?php else: ?>
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x600')
                                ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php if (!$isMobile): ?>
                        <div class="verticleRectangle">
                            <div class="appendAdDiv" style="background:#EAEAEA;">
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__300x600')
                                ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </aside>
        </div>
    </div>
    <?php /**
    <section class="commentSection">
        <?= $this->render('/partials/comment/_form', [
            'model' => $commentModel,
            'entity' => Olympiad::ENTITY_OLYMPIAD,
            'entity_id' => $olympiad->id
        ]) ?>
        <?= $this->render('/partials/comment/_comment', [
            'comments' => $comments,
            'entity' => Olympiad::ENTITY_OLYMPIAD,
            'entityId' => $olympiad->id
        ]) ?>
    </section>
     */ ?>

    <!-- related article -->
    <?php if (!empty($relatedOlympiad) && count($relatedOlympiad) > 1): ?>
        <?= $this->render('partials/_related-olympiad-list', [
            'relatedArticles' => $relatedOlympiad,
            'article' => $olympiad
        ]); ?>
    <?php endif; ?>

    <?php if (!empty($olympiad->article) && count($olympiad->article) > 1): ?>
        <?= $this->render('partials/_article-list', [
            'relatedArticles' => $olympiad->article,
            'article' => $olympiad
        ]); ?>
    <?php endif; ?>

    <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
        <div id="comment-reply-form-js"></div>
    <?php endif; ?>
</div>