<?php

use common\models\Article;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use yii\widgets\ListView;
use frontend\helpers\Lead as HelpersLead;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;

// utils
$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
if ($country->name == 'United Kingdom') {
    $countryName = 'UK';
} elseif ($country->name == 'United States') {
    $countryName = 'USA';
} else {
    $countryName = $country->name ?? $country->slug;
}



$articles->prepare();

// meta key
$this->title = 'Study in ' . $countryName . ' Articles | GetMyUni';
$this->context->description = 'Get articles on visa, careers, student life, courses, scholarships, academic support for studying in ' . $countryName;

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
if (Yii::$app->request->get('page') > 1) {
    $this->params['breadcrumbs'][] = [
        'label' => $countryName ?? '',
        'url' => Url::toDomain() . $country->slug,
        'title' => $countryName ?? '',
    ];
    $this->params['breadcrumbs'][] = ['label' => $countryName . Yii::t('app', 'Articles'), 'url' => Url::canonical(), 'title' => $countryName . ' Articles'];
    $this->params['breadcrumbs'][] = 'Page ' . Yii::$app->request->get('page');
} else {
    $this->params['breadcrumbs'][] = [
        'label' => $countryName ?? '',
        'url' => Url::toDomain() . $country->slug,
        'title' => $countryName ?? ''
    ];
    $this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Articles')];
}
$this->params['entity_name'] = $countryName . ' Articles' ?? '';
$this->params['entity'] = Article::ENTITY_ARTICLE;
$this->params['links'] = $articles->pagination->getLinks(true);

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'article-category.css', ['depends' => [AppAsset::class]]);


?>

<div class="">
    <div class="pageHeading">
        <h1><?= $countryName ?> Articles
            <?= Yii::$app->request->get('page') > 1 ? ' - Page ' . Yii::$app->request->get('page') : '' ?>
        </h1>
    </div>
    <div class="row">
        <div class="col-md-8">
            <div class="categoryArticlesList">
                <div class="horizontalRectangle">
                    <div class="appendAdDiv xs-h50" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__320x100')
                            ?>
                        <?php else: ?>
                            <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                            ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php
                echo ListView::widget([
                    'dataProvider' => $articles,
                    'afterItem' => function ($model, $key, $index) {
                        // append the Add after very 4 card
                        if ($index % 4 == 0 && $index != 0) {
                            if (\Yii::$app->devicedetect->isMobile()) {
                                return "<div class='horizontalRectangle'><div class='appendAdDiv' style='background:#EAEAEA;'>" .
                                    Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__240x400 __336x280') . '</div></div>';
                            } else {
                                return "<div class='horizontalRectangle'><div class='appendAdDiv' style='background:#EAEAEA;'>" .
                                    Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90') . '</div></div>';
                            }
                        }
                    },
                    'itemView' => 'partials/_study-abroad-article-list',
                    'pager' => [
                        'class' => 'LinkPager',
                    ],
                    'viewParams' => [
                        'fullView' => true,
                        'context' => 'main-page',
                        'isMobile' => $isMobile,
                    ],
                    'layout' => "{items}\n{pager}",
                    'pager' => [
                        'class' => '\justinvoelker\separatedpager\LinkPager',
                        'maxButtonCount' => 7,
                        'prevPageLabel' => 'Previous',
                        'nextPageLabel' => 'Next',
                        'prevPageCssClass' => 'prev hidden-xs',
                        'nextPageCssClass' => 'next hidden-xs',
                        'activePageAsLink' => false,
                    ]
                ]);
                ?>
            </div>
        </div>
        <div class="col-md-4">
            <aside>
                <div class="registerLatestArticle">
                    <div class="row">
                        <img class="lazyload" loading="lazy" data-src="/yas/images/get-support.png" src="/yas/images/get-support.png" alt="Get Updates on Latest Articles" />
                        <p>Get Updates on Latest Articles</p>
                    </div>
                    <?= frontend\helpers\Html::leadButton(
                        'SUBSCRIBE',
                        [
                            'entity' => Article::ENTITY_STUDY_ABROAD,
                            'entityId' => null,
                            'ctaLocation' => HelpersLead::getCTAsName(Article::ENTITY_STUDY_ABROAD . '.' . $country->slug, 'study_abroad_articles_category_desktop_1'),
                            'ctaText' => 'SUBSCRIBE',
                            'leadformtitle' => 'SUBSCRIBE NOW TO GET UPDATES',
                        ],
                        ['class' => 'primaryBtn registerNow'],
                        'sa-js-open-lead-form'
                    ) ?>
                </div>

                <?= $this->render('partials/_sidebar-study-abroad-articles', [
                    'trendings' => $trendings,
                    'recentArticles' => $latestArticle,
                ]) ?>
            </aside>
            <aside>
                <?php if (!$isMobile): ?>
                    <div class="verticleRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if ($isMobile): ?>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                            ?>

                        </div>
                    </div>
                <?php endif; ?>
            </aside>
        </div>
    </div>
</div>