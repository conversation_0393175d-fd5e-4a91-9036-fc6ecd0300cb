<?php

use frontend\helpers\Url;
use frontend\assets\AppAsset;
use yii\widgets\ListView;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;

// utils
$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();

// meta key
$articles->prepare();
if (Yii::$app->request->get('page') > 1) {
    $this->title = $tag->name . ' Articles Archives -  Page ' . Yii::$app->request->get('page') . ' of ' . ($articles->pagination->pageCount);
    $this->context->description = 'Find latest ' . $tag->name . ' related articles by Getmyuni - Renowned source of free, accurate, & insightful college admissions, exam updates and student mentorship information. Page ' . Yii::$app->request->get('page') . ' of ' . ($articles->pagination->pageCount);
} else {
    $this->title = 'Find latest articles on ' . $tag->name . ' - <PERSON><PERSON><PERSON>';
    $this->context->description = 'Find latest ' . $tag->name . ' related articles by <PERSON>myuni - Renowned source of free, accurate, & insightful college admissions, exam updates and student mentorship information.';
}

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Articles'), 'url' => '/articles', 'title' => 'Articles'];

if (Yii::$app->request->get('page') > 1) {
    $this->params['breadcrumbs'][] = [
        'label' => $tag->name ?? '',
        'url' => Url::canonical(),
        'title' => $tag->name
    ];
    $this->params['breadcrumbs'][] = 'Page ' . Yii::$app->request->get('page');
} else {
    $this->params['breadcrumbs'][] = $tag->name;
}

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'article-category.css', ['depends' => [AppAsset::class]]);


?>

<div class="">
    <div class="pageHeading">
        <h1><?= $tag->name ?></h1>
    </div>
    <div class="row">
        <div class="col-md-8">
            <div class="categoryArticlesList">

                <?php
                echo ListView::widget([
                    'dataProvider' => $articles,
                    'itemView' => 'partials/_article-list',
                    'viewParams' => [
                        'fullView' => true,
                        'context' => 'main-page',
                        'isMobile' => $isMobile,
                    ],
                    'layout' => "{items}\n{pager}",
                    'pager' => [
                        'prevPageCssClass' => '',
                        'maxButtonCount' => 5,
                    ]
                ]);
                ?>

            </div>
        </div>

        <div class="col-md-4">
            <aside>
                <div class="registerLatestArticle">
                    <div class="row">
                        <img class="lazyload" loading="lazy" data-src="/yas/images/get-support.png" src="/yas/images/get-support.png" alt="" />
                        <p>Interested in Latest
                            Educational Articles?</p>
                    </div>
                    <a href="javascript:;" class="primaryBtn registerNow">REGISTER NOW</a>
                </div>

                <?= $this->render('partials/_sidebar-articles', [
                    'trendings' => $trendings,
                    'recentArticles' => $recentArticles
                ])
?>

            </aside>

            <aside>
                <div class="verticleRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                            ?>
                        <?php else: ?>
                            <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                        <?php endif; ?>
                    </div>
                </div>
            </aside>
        </div>
    </div>
</div>