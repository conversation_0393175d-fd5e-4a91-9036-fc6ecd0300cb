<?php

use common\helpers\ArticleDataHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\Article;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use common\models\Lead;
use frontend\helpers\Lead as HelpersLead;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use yii\helpers\BaseStringHelper;

// utils
$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = isset($author->profile->image) ? Yii::getAlias('@profileDPFrontend') . '/' . $author->profile->image : '/yas/images/usericon.png';
if ($country->name == 'United Kingdom') {
    $countryName = 'UK';
} elseif ($country->name == 'United States') {
    $countryName = 'USA';
} else {
    $countryName = $country->name;
}

// meta key
$this->title = $detail->title . ' - Getmyuni' ?? '';
$this->context->description = ContentHelper::htmlDecode(stripslashes($detail->meta_description), false) ?? '';
$this->context->ogType = 'Article';

if (!empty($detail->cover_image)) {
    $this->registerMetaTag(['property' => 'og:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'og:image:height', 'content' => '667']);
    $this->registerMetaTag(['property' => 'og:image:alt', 'content' => $detail->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:alt', 'content' => $detail->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:type', 'content' => 'image/jpeg']);
    $this->registerMetaTag(['property' => 'twitter:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'twitter:image:height', 'content' => '667']);
    $this->registerLinkTag(['href' => Url::getStudyAbroadImage($detail->cover_image), 'rel' => 'preload', 'as' => 'image']);
    $this->context->ogImage = Url::getStudyAbroadImage($detail->cover_image);
}
$this->registerMetaTag(['property' => 'article:published_time', 'content' => date(DATE_ATOM, strtotime($detail->created_at))]);
$this->registerMetaTag(['property' => 'article:modified_time', 'content' => date(DATE_ATOM, strtotime($detail->created_at))]);

$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => $countryName, 'url' => Url::toDomain() . $detail->country_slug, 'title' => $countryName];
$this->params['breadcrumbs'][] = ['label' => $countryName . Yii::t('app', 'Articles'), 'url' => Url::toStudyAbroadArticles($detail->country_slug), 'title' => $countryName . ' Articles'];
$this->params['breadcrumbs'][] = ContentHelper::htmlDecode(stripslashes($detail->h1), false);
$this->params['entity_name'] = $countryName . ' Articles' ?? '';
$this->params['entity_id'] = $detail->id ?? 0;
$this->params['entity'] = Article::ENTITY_ARTICLE;
$this->params['identifier'] = 'study-abroad-article';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'article-detail.css', ['depends' => [AppAsset::class]]);


// schema
$this->params['schema'] = \yii\helpers\Json::encode([[
    '@context' => 'http://schema.org',
    '@type' => 'Article',
    'mainEntityOfPage' => [
        '@type' => 'WebPage',
        '@id' => $currentUrl,
    ],
    'headline' => $this->title,
    'image' => [Url::getStudyAbroadImage($detail->cover_image)],
    'datePublished' => date(DATE_ATOM, strtotime($detail->created_at)),
    'dateModified' => date(DATE_ATOM, strtotime($detail->updated_at)),
    'author' => [
        '@type' => 'Person',
        'name' => $author ? $author->name : ''
    ],
    'publisher' => [
        '@type' => 'Organization',
        'name' => 'Getmyuni',
        'logo' => [
            '@type' => 'ImageObject',
            'url' => Yii::$app->params['gmuLogo']
        ],
    ],
    'description' => $detail->meta_description
]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
?>

<div class="">
    <div class="row">
        <div class="col-md-8">
            <main>
                <article>
                    <section class="bannerDiv">
                        <h1><?= ContentHelper::htmlDecode(stripslashes($detail->h1), false) ?></h1>
                        <div class="updated-info row">
                            <div class="updatedBy">
                                <img height="36" width="36" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author ? $author->name . ' Image' : '' ?>" />
                                <a href="<?= $author ? Url::toAllAuthorPost($author->slug) : '' ?>" class="authorName" title="<?= $author ? $author->name : '' ?>"> <?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?>
                                </a>
                            </div>
                            <p><span><?= Yii::$app->formatter->asDate($detail->updated_at ?? 'today') ?></span> |
                                <?= ContentHelper::calculateReadTime($detail->description) ?> read</span></p>
                            <ul>
                                <p>Share: </p>
                                <li>
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $currentUrl ?>" target="_blank" rel="noopener nofollow" class="spriteIcon greyFbIcon"></a>
                                </li>
                                <li>
                                    <a href="https://twitter.com/share?url=<?= $currentUrl ?>" class="spriteIcon greyTwitterIcon" rel="noopener nofollow" target="_blank"></a>
                                </li>
                            </ul>
                        </div>
                    </section>

                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h50" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__320x100')
                                ?>
                            <?php else: ?>
                                <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                                ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="articelNote">
                        <p><?= $detail->meta_description ?></p>
                    </div>
                    <?php if (!empty($detail->cover_image)): ?>
                        <div class="bannerImg">
                            <img data-src="<?= Url::getStudyAbroadImage($detail->cover_image) ?>" src="<?= Url::getStudyAbroadImage($detail->cover_image) ?>" title="<?= $detail->h1 ?>" alt="<?= $detail->h1 ?>" />
                        </div>
                    <?php endif; ?>
                    <div class="articleInfo">
                        <?= ContentHelper::removeStyleTag(stripslashes(
                            html_entity_decode(DataHelper::parseDomainUrlInContent($detail->description))
                        )) ?>
                        <?php if (!empty($tags)): ?>
                            <div class="tagsDiv">
                                <ul>
                                    <li>TAGS</li>
                                    <?php foreach ($tags as $tag): ?>
                                        <li><a href="<?= Url::toTag($tag->slug) ?>" title="<?= $tag->name ?>"><?= strtoupper($tag->name) ?></a></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </article>
            </main>

            <!-- faqs -->
            <?php if (!empty($faqs)): ?>
                <?= $this->render('partials/_faq', ['faqs' => $faqs]) ?>
            <?php endif; ?>

            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                        ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90')
                        ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <aside>
                <div class="registerLatestArticle">
                    <div class="row">
                        <img class="lazyload" loading="lazy" data-src="/yas/images/get-support.png" src="/yas/images/get-support.png" alt="" />
                        <p>Get Updates on Latest Articles</p>
                    </div>
                    <?= frontend\helpers\Html::leadButton(
                        'SUBSCRIBE',
                        [
                            'entity' => 'study-abroad-article' ?? 'study-abroad-article',
                            'entityId' => $detail->id ?? null,
                            'ctaLocation' => HelpersLead::getCTAsName(Lead::ENTITY_STUDY_ABROAD . '.' . $country->slug, 'study_abroad_articles_detail_desktop_1'),
                            'ctaText' => 'SUBSCRIBE',
                            'leadformtitle' => 'SUBSCRIBE NOW TO GET UPDATES'
                        ],
                        ['class' => 'primaryBtn registerNow'],
                        'sa-js-open-lead-form'
                    ) ?>
                </div>

                <?= $this->render('partials/_sidebar-study-abroad-articles', [
                    'trendings' => $trendings,
                    'recentArticles' => $latestArticle,
                    'country' => $country,
                    'detail' => $detail,
                ]) ?>

                <div class="squareDiv">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__200x600')
                            ?>
                        <?php else: ?>
                            <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x250')
                            ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php if (!$isMobile): ?>
                    <div class="verticleRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__300x600')
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            </aside>
        </div>
    </div>

    <section class="commentSection">
        <?= $this->render('/partials/comment/_form', [
            'model' => $commentModel,
            'entity' => Article::ENTITY_STUDY_ABROAD,
            'entity_id' => $detail->id
        ]) ?>
        <?= $this->render('/partials/comment/_comment', [
            'comments' => $comments,
            'entity' => Article::ENTITY_STUDY_ABROAD,
            'entityId' => $detail->id
        ]) ?>
    </section>

    <?php if (!empty($relatedArticles)): ?>
        <section class="relatedArticles">
            <h2 class="row">RELATED ARTICLES</h2>

            <div class="articleList row">
                <?php foreach ($relatedArticles as $relatedArticle): ?>
                    <?php if ($detail->slug == $relatedArticle->slug): ?>
                        <?php continue ?>
                    <?php endif; ?>
                    <article class="articleDiv">
                        <figure>
                            <img class="lazyload" loading="lazy" data-src="<?= $relatedArticle->cover_image ? Url::getStudyAbroadImage($relatedArticle->cover_image) : Url::getStudyAbroadImage() ?>" src="<?= $relatedArticle->cover_image ? Url::getStudyAbroadImage($relatedArticle->cover_image) : Url::getStudyAbroadImage() ?>" alt="" />
                        </figure>
                        <div class="articleTxt">
                            <a href="<?= Url::toCountryDetail($relatedArticle->country_slug, $relatedArticle->slug) ?>" title="<?= $relatedArticle->h1 ?>"><?= BaseStringHelper::truncateWords($relatedArticle->h1, 7) ?></a>
                            <p><?= $relatedArticle->author ? $relatedArticle->author->name : '' ?></p>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
        </section>
    <?php endif; ?>
    <div class="setAlarmDiv mobileOnly">
        <?= frontend\helpers\Html::leadButton(
            '<i class="spriteIcon alarmIcon"></i> ARTICLE ALERTS',
            [
                'entity' => 'study-abroad-article',
                'entityId' => $detail->id,
                'ctaLocation' => HelpersLead::getCTAsName(Lead::ENTITY_STUDY_ABROAD . '.' . $country->slug, 'study_abroad_articles_detail_mobile_1'),
                'ctaText' => 'ARTICLE ALERTS',
                'leadformtitle' => 'SUBSCRIBE NOW TO GET UPDATES'
            ],
            ['class' => 'primaryBtn setExamAlert'],
            'sa-js-open-lead-form'
        ) ?>
    </div>
</div>
<!-- </main> -->

<div id="comment-reply-form-js"></div>