<?php

use frontend\helpers\Url;
use frontend\assets\AppAsset;
use yii\widgets\ListView;
use common\models\Lead;
use frontend\helpers\Lead as HelpersLead;
use frontend\helpers\Ad;
use justinvoelker\separatedpager\LinkPager;
use common\models\Article;
use common\helpers\DataHelper;
use frontend\helpers\Freestartads;

// utils
$currentUrl = Url::base(true) . Url::current();


$isMobile = \Yii::$app->devicedetect->isMobile();

$articles->prepare();

// meta key
if (Yii::$app->request->get('page') > 1) {
    $this->title = $category->name . ' Articles Archives -  Page ' . Yii::$app->request->get('page') . ' of ' . ($articles->pagination->pageCount);
    $this->context->description = 'Find Latest ' . $category->name . ' Related Articles by Getmyuni - Renowned source of free, accurate, & insightful college admissions, exam updates and student mentorship information. Page ' . Yii::$app->request->get('page') . ' of ' . ($articles->pagination->pageCount);
} else {
    $this->title = $category->name . ' Articles - Getmyuni';
    $this->context->description = 'Find Latest ' . $category->name . ' Related Articles by Getmyuni - Renowned source of free, accurate, & insightful college admissions, exam updates and student mentorship information.';
}

if ($category->slug == 'others') {
    $this->registerMetaTag(['name' => 'robots', 'content' => 'noindex, nofollow']);
}

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Articles'), 'url' => Url::toArticles(DataHelper::getLangCode($category->lang_code)), 'title' => 'Articles'];
$this->params['previous_url'] = Yii::$app->request->referrer;
$this->params['page_url'] = Yii::$app->request->url;
$this->params['entity_name'] = $category->name . Yii::t('app', 'Articles') ?? '';
$this->params['entity_id'] = $category->id  ?? 0;
$this->params['entity'] = Article::ENTITY_ARTICLE;

if (Yii::$app->request->get('page') > 1) {
    $this->params['breadcrumbs'][] = [
        // 'label' => $category->name . ' ' . Yii::t('app', 'Articles') ?? '',
        'label' => $category->name,
        'url' => Url::canonical(),
        'title' => $category->name . ' ' . Yii::t('app', 'Articles')
    ];
    $this->params['breadcrumbs'][] = 'Page ' . Yii::$app->request->get('page');
    $canonicalUrl = Url::base(true) . '/' . \Yii::$app->request->getPathInfo() . '?page=' . Yii::$app->request->get('page');
    ;
} else {
    // $this->params['breadcrumbs'][] = $category->name . Yii::t('app', 'Articles');
    $this->params['breadcrumbs'][] = $category->name;
    $canonicalUrl = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();
}
$this->params['canonicalUrl'] = $canonicalUrl;
$this->params['links'] = $articles->pagination->getLinks(true);

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'article-category.css', ['depends' => [AppAsset::class]]);

?>

<div class="">
    <div class="pageHeading">
        <h1>
            <?= $category->name ?> <?= Yii::t('app', 'Articles'); ?>
            <?= Yii::$app->request->get('page') > 1 ? ' - Page ' . Yii::$app->request->get('page') : '' ?>
        </h1>
    </div>
    <div class="row">
        <div class="col-md-8">
            <div class="categoryArticlesList">
                <?php
                echo ListView::widget([
                    'dataProvider' => $articles,
                    'itemView' => 'partials/_article-list',
                    'pager' => [
                        'class' => 'LinkPager',
                    ],
                    'viewParams' => [
                        'fullView' => true,
                        'context' => 'main-page',
                        'isMobile' => $isMobile,
                    ],
                    'layout' => "{items}\n{pager}",
                    'pager' => [
                        'class' => '\justinvoelker\separatedpager\LinkPager',
                        'maxButtonCount' => 7,
                        'prevPageLabel' => 'Previous',
                        'nextPageLabel' => 'Next',
                        'prevPageCssClass' => 'prev hidden-xs',
                        'nextPageCssClass' => 'next hidden-xs',
                        'activePageAsLink' => false,
                    ]
                ]);

                ?>

            </div>
        </div>

        <div class="col-md-4">
            <aside>
                <?= $this->render('partials/_sidebar-articles', [
                    'trendings' => $trendings,
                    'recentArticles' => $recentArticles
                ])
?>

            </aside>
            <aside>
                <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="verticleRectangle">
                        <div class="appendAdDiv" style="<?= $isMobile ? 'height: 600px;' : '' ?>background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                                ?>
                            <?php else: ?>
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x600') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </aside>
        </div>
    </div>
</div>
<?= $this->render('/layouts/_global-hello-bar'); ?>