<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;
use common\helpers\DataHelper;
use common\models\Article;

if (empty($model->author)) {
    $author = $model->defaultuser;
} else {
    $author = $model->author;
}

?>
<article class="catgegoryArticle">
    <div class="row">
        <div class="articleBanner">
            <figure>
                <?php $cardImage = $model->cover_image ? ArticleDataHelper::getImage($model->cover_image) : ArticleDataHelper::getImage() ?>
                <?php if ($model->country_slug != null && $model->entity == Article::ENTITY_STUDY_ABROAD): ?>
                    <img onclick="gmu.url.goto('<?= Url::toCountryDetail($model->country_slug, $model->slug) ?>')" data-src="<?= $model->cover_image ? Url::getStudyAbroadImage($model->cover_image) : Url::getStudyAbroadImage() ?>" src="<?= $model->cover_image ? Url::getStudyAbroadImage($model->cover_image) : Url::getStudyAbroadImage() ?>" alt="<?= $model->h1 ?>" />
                <?php else: ?>
                    <img onclick="gmu.url.goto('<?= Url::toArticleDetail($model->slug, DataHelper::getLangCode($model->lang_code)) ?>')" data-src="<?= $cardImage ?>" src="<?= $cardImage ?>" alt="<?= $model->h1 ?>" />
                <?php endif; ?>
            </figure>
        </div>
        <div class="articleText">
            <h2>
                <?php if ($model->country_slug != null): ?>
                    <a href="<?= Url::toCountryDetail($model->country_slug, $model->slug) ?>" title="<?= $model->h1 ?>"> <?= BaseStringHelper::truncateWords($model->h1, 9) ?></a>
                <?php else: ?>
                    <a href="<?= Url::toArticleDetail($model->slug, DataHelper::getLangCode($model->lang_code)) ?>" title="<?= $model->h1 ?>"> <?= BaseStringHelper::truncateWords($model->h1, 9) ?></a>
                <?php endif; ?>
            </h2>

            <p><?= $model->meta_description ?></p>
            <div class="updated-info row">

                <?php if ($author):
                    if ($model->lang_code != 1) {
                        $author_name = isset($model->transAuthor) ? $model->transAuthor->user_name : '';
                    } else {
                        $author_name =  isset($author) ? $author->name : '';
                    } ?>
                    <div class="updatedBy">
                        <?php $authorImage = isset($author->profile->image) ? Yii::getAlias('@profileDPFrontend') . '/' . $author->profile->image : '/yas/images/usericon.png' ?>
                        <img height="60" width="60" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author->name . ' Image' ?>">
                        <?php if ($model->lang_code != 1) { ?>
                            <p class="authorName"><a class="author-link" href="<?= $author && !empty($author->profile) ? '/hi/author/' . $author->slug : '#' ?>"><?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?></a>, </p>
                        <?php } else { ?>
                             <p class="authorName"><a class="author-link" href="<?= $author && !empty($author->profile) ? '/author/' . $author->slug : '#' ?>"><?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?></a>, </p>
                        <?php } ?>
                    </div>
                <?php endif; ?>
                <p><?= Yii::$app->formatter->asDate($model->updated_at ?? 'today') ?></p>

            </div>
        </div>
    </div>
</article>