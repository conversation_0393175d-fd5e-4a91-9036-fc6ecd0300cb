<?php

use common\services\UserService;

$isMobile = \Yii::$app->devicedetect->isMobile();
$defaultEmptyCondition0 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_0']) || empty(array_filter($dynamicCta['cta_position_0'])));
$defaultEmptyCondition1 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])));
$defaultEmptyCondition2 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_2']) || empty(array_filter($dynamicCta['cta_position_2'])));
$defaultEmptyCondition3 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_3']) || empty(array_filter($dynamicCta['cta_position_3'])));
// dd($dynamicCta, $defaultEmptyCondition2);
?>

<?php if (isset($lead_cta) && $lead_cta == 1):
    $cta_slug1 = $defaultEmptyCondition0 ? 'Free Counseling' : (!empty($dynamicCta['cta_position_0']['cta_text']) ? $dynamicCta['cta_position_0']['cta_text'] : 'Free Counseling');
    $cta_slug2 = $defaultEmptyCondition1 ? 'Get ₹1 Lakh Scholarship' : (!empty($dynamicCta['cta_position_1']['cta_text']) ? $dynamicCta['cta_position_1']['cta_text'] : 'Get ₹1 Lakh Scholarship'); ?>
    <?= frontend\helpers\Html::leadButton(
        $cta_slug1,
        [
            'entity' => $data_entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition0 ? UserService::parseDynamicCta('articles_{slug}_detail_wap_bottom_left_sticky_cta2', '', $slug) : ($dynamicCta['cta_position_0']['wap'] ?? null)) : ($defaultEmptyCondition0 ? UserService::parseDynamicCta('articles_{slug}_detail_web_lead_capture_panel_left_cta2', '', $slug) : ($dynamicCta['cta_position_0']['web'] ?? null)),
            'ctaText' => $cta_slug1,
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER FOR EXPERT GUIDANCE' : (!empty($dynamicCta['cta_position_0']['lead_form_title']) ? $dynamicCta['cta_position_0']['lead_form_title'] : 'REGISTER FOR EXPERT GUIDANCE'),
            'subheadingtext' => $defaultEmptyCondition0 ? 'Register and talk to our experts.' : (!empty($dynamicCta['cta_position_0']['lead_form_description']) ? $dynamicCta['cta_position_0']['lead_form_description'] : 'Register and talk to our experts.'),
            'image' => '/yas/images/defaultcardbanner.png',
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition0 ? null : ($dynamicCta['cta_position_0']['page_link'] ?? null)) : null,
            'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
        ],
        ['class' => 'talkToExpert registerNow courseCategory articleLeadValue cta_impression CTA_click'],
        'js-open-lead-form-new'
    )
    ?>
    <?= frontend\helpers\Html::leadButton(
        $cta_slug2,
        [
            'entity' => $data_entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition1 ? UserService::parseDynamicCta('articles_{slug}_detail_wap_bottom_right_sticky_cta2', '', $slug) : ($dynamicCta['cta_position_1']['wap'] ?? null)) : ($defaultEmptyCondition1 ? UserService::parseDynamicCta('articles_{slug}_detail_web_lead_capture_panel_right_cta2', '', $slug) : ($dynamicCta['cta_position_1']['web'] ?? null)),
            'ctaText' => $defaultEmptyCondition1 ? 'Get 1 Lakh Scholarship' : (!empty($dynamicCta['cta_position_1']['cta_text']) ? $dynamicCta['cta_position_1']['cta_text'] : 'Get 1 Lakh Scholarship'),
            'ctaTitle' => $defaultEmptyCondition1 ? '' : (!empty($dynamicCta['cta_position_1']['cta_title']) ? $dynamicCta['cta_position_1']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition1 ? 'APPLY NOW' : (!empty($dynamicCta['cta_position_1']['lead_form_title']) ? $dynamicCta['cta_position_1']['lead_form_title'] : 'APPLY NOW'),
            'subheadingtext' => $defaultEmptyCondition1 ? 'Fill in your details' : (!empty($dynamicCta['cta_position_1']['lead_form_description']) ? $dynamicCta['cta_position_1']['lead_form_description'] : 'Fill in your details'),
            'image' => '/yas/images/defaultcardbanner.png',
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition1 ? null : ($dynamicCta['cta_position_1']['page_link'] ?? null)) : null,
            'alternateMedia' => $defaultEmptyCondition1 ? '' : $dynamicCta['cta_position_1']['media'],
            'alternatePageRedirectSlug' =>  $defaultEmptyCondition1 ? '' : $dynamicCta['cta_position_1']['page_redirect_slug']
        ],
        ['class' => 'articleScholarship applyNow registerNow courseCategory articleLeadValue cta_impression CTA_click'],
        'js-open-lead-form-new'
    )
    ?>
<?php endif; ?>

<?php if (isset($lead_cta) && $lead_cta == 0):
    $cta_slug1 = $defaultEmptyCondition2 ? 'Register' : (!empty($dynamicCta['cta_position_2']['cta_text']) ? $dynamicCta['cta_position_2']['cta_text'] : 'Register');
    $cta_slug2 = $defaultEmptyCondition3 ? 'Apply Now <span class="spriteIcon applyRedIcon"></span>' : (!empty($dynamicCta['cta_position_3']['cta_text']) ? $dynamicCta['cta_position_3']['cta_text'] : 'Apply Now'); ?>
    <?= frontend\helpers\Html::leadButton(
        $cta_slug1,
        [
            'entity' => $data_entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition2 ? UserService::parseDynamicCta('articles_{slug}_detail_wap_top_left_sticky_cta1', '', $slug) : ($dynamicCta['cta_position_2']['wap'] ?? null)) : ($defaultEmptyCondition2 ? UserService::parseDynamicCta('articles_{slug}_detail_web_lead_capture_panel_left_cta1', '', $slug) : ($dynamicCta['cta_position_2']['web'] ?? null)),
            'ctaText' => $cta_slug1,
            'ctaTitle' => $defaultEmptyCondition2 ? '' : (!empty($dynamicCta['cta_position_2']['cta_title']) ? $dynamicCta['cta_position_2']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition2 ? 'REGISTER FOR EXPERT GUIDANCE' : (!empty($dynamicCta['cta_position_2']['lead_form_title']) ? $dynamicCta['cta_position_2']['lead_form_title'] : 'REGISTER FOR EXPERT GUIDANCE'),
            'subheadingtext' => $defaultEmptyCondition2 ? 'Register and talk to our experts.' : (!empty($dynamicCta['cta_position_2']['lead_form_description']) ? $dynamicCta['cta_position_2']['lead_form_description'] : 'Register and talk to our experts.'),
            'image' => '/yas/images/defaultcardbanner.png',
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition2 ? null : ($dynamicCta['cta_position_2']['page_link'] ?? null)) : null,
            'alternateMedia' => $defaultEmptyCondition2 ? '' : $dynamicCta['cta_position_2']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition2 ? '' : $dynamicCta['cta_position_2']['page_redirect_slug']
        ],
        ['class' => 'freeScholarship primaryBtn applyNowButton getLeadForm articleLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null],
        'js-open-lead-form-new'
    )
    ?>
    <?= frontend\helpers\Html::leadButton(
        $cta_slug2,
        [
            'entity' => $data_entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition3 ? UserService::parseDynamicCta('articles_{slug}_detail_wap_top_right_sticky_cta1', '', $slug) : ($dynamicCta['cta_position_3']['wap'] ?? null)) : ($defaultEmptyCondition3 ? UserService::parseDynamicCta('articles_{slug}_detail_web_lead_capture_panel_right_cta1', '', $slug) : ($dynamicCta['cta_position_3']['web'] ?? null)),
            'ctaText' => $cta_slug2,
            'ctaTitle' => $defaultEmptyCondition3 ? '' : (!empty($dynamicCta['cta_position_3']['cta_title']) ? $dynamicCta['cta_position_3']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition3 ? 'APPLY NOW' : (!empty($dynamicCta['cta_position_3']['lead_form_title']) ? $dynamicCta['cta_position_3']['lead_form_title'] : 'APPLY NOW'),
            'subheadingtext' => $defaultEmptyCondition3 ? 'Fill in your details' : (!empty($dynamicCta['cta_position_3']['lead_form_description']) ? $dynamicCta['cta_position_3']['lead_form_description'] : 'Fill in your details'),
            'image' => '/yas/images/defaultcardbanner.png',
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition3 ? null : ($dynamicCta['cta_position_3']['page_link'] ?? null)) : null,
            'alternateMedia' =>  $defaultEmptyCondition3 ? '' : $dynamicCta['cta_position_3']['media'],
            'alternatePageRedirectSlug' =>  $defaultEmptyCondition3 ? '' : $dynamicCta['cta_position_3']['page_redirect_slug']

        ],
        ['class' => 'primaryBtn articleLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null],
        'js-open-lead-form-new'
    )
    ?>
<?php endif;
