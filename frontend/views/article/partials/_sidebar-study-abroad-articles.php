<?php

use common\helpers\ArticleDataHelper;
use common\models\Article;
use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;

?>

<div class="articleSidebarSection sideBarWidget">
    <ul>
        <?php if (!empty($trendings)): ?>
            <li data-tab="trendingArtilce" class="activeLink"><?= Yii::t('app', 'Trending Articles') ?></li>
        <?php endif; ?>
        <?php if (!empty($recentArticles)): ?>
            <li data-tab="recentArticles" class="<?= empty($trendings) ? 'activeLink' : '' ?>"><?= Yii::t('app', 'Recent Articles') ?></li>
        <?php endif; ?>
    </ul>

    <?php if (!empty($trendings)): ?>
        <div class="trendingArtilce tab-content activeLink trendingArtilerList" id="trendingArtilce">
                <?php foreach ($trendings as $trending): ?>
                    <?php if (isset($detail) && $detail->slug == $trending->slug): ?>
                        <?php continue; ?>
                    <?php endif; ?>
                    <a class="listCard" href="<?= Url::toCountryDetail($trending->country_slug, $trending->slug) ?>" title="<?= $trending->title ?>">
                        <div class="trendingArtilerDiv row">
                            <div class="sidebarImgDiv">
                                <img src="<?= Url::getStudyAbroadImage($trending->cover_image) ?>" alt="<?= $trending->title ?>">
                            </div>
                            <div class="trendingArtileText">
                                <p class="sidebarTextLink"><?= BaseStringHelper::truncateWords($trending->title, 7) ?></p>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($recentArticles)): ?>
        <div class=" recentArticlesList recentArticles tab-content <?= empty($trendings) ? 'activeLink' : '' ?>" id="recentArticles">
                <?php foreach ($recentArticles as $recentArticle): ?>
                    <?php if (isset($detail) && $detail->slug == $recentArticle->slug): ?>
                        <?php continue; ?>
                    <?php endif; ?>
                    <a class="listCard" href="<?= Url::toCountryDetail($recentArticle->country_slug, $recentArticle->slug) ?>" title="<?= $recentArticle->title ?? '' ?>">
                        <div class="recentArticlesDiv row">
                            <div class="sidebarImgDiv">
                                <?php if ($recentArticle->country_slug != null && $recentArticle->entity == Article::ENTITY_STUDY_ABROAD): ?>
                                    <img src="<?= $recentArticle->cover_image ? Url::getStudyAbroadImage($recentArticle->cover_image) : Url::getStudyAbroadImage() ?>" alt="">
                                <?php else: ?>
                                    <img src="<?= Url::getStudyAbroadImage() ?>" alt="">
                                <?php endif; ?>
                            </div>
                            <div class="recentArticlesDivText">
                                <p class="sidebarTextLink"><?= BaseStringHelper::truncateWords($recentArticle->title ?? '', 7) ?></p>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>