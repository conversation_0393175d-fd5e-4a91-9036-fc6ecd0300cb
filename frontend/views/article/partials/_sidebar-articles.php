<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use common\helpers\DataHelper;

?>

<div class="articleSidebarSection sideBarWidget">
    <ul>
        <?php if (((count($recentArticles) == 1) && ($recentArticles[0]->slug !== $article->slug)) || (count($recentArticles) > 1)): ?>
            <li data-tab="recentArticles" class="<?= !empty($recentArticles) ? 'activeLink' : '' ?>"><?= Yii::t('app', 'Recent Articles'); ?></li>
        <?php endif; ?>
        <?php if (((count($trendings) == 1) && ($trendings[0]->slug !== $article->slug)) || (count($trendings) > 1)): ?>
            <li data-tab="trendingArtilceList"><?= Yii::t('app', 'Popular Articles'); ?></li>
        <?php endif; ?>
    </ul>

    <?php if (!empty($recentArticles)):?>
        <div class=" recentArticlesList recentArticles tab-content <?= !empty($recentArticles) ? 'activeLink' : '' ?>" id="recentArticles">
            <?php foreach ($recentArticles as $recentArticle):
                ?>
                <?php if (isset($article) && $article->slug == $recentArticle['slug']): ?>
                    <?php continue; ?>
                <?php endif; ?>
                <a class="listCard" href="<?= Url::toArticleDetail($recentArticle['slug'], DataHelper::getLangCode($recentArticle['lang_code'])) ?>" title="<?= $recentArticle['title'] ?>">
                    <div class="recentArticlesDiv row">
                        <div class="sidebarImgDiv">
                            <img class="lazyload" loading="lazy" src="<?= $recentArticle['cover_image'] ? ArticleDataHelper::getImage($recentArticle['cover_image']) : ArticleDataHelper::getImage() ?>" width="60px" height="60px" alt="">
                        </div>
                        <div class="recentArticlesDivText">
                            <p class="sidebarTextLink"><?= $recentArticle['title'] ?? '' ?></p>
                        </div>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($trendings)): ?>
        <div id="trendingArtilceList" class="trendingArtilce tab-content trendingArtilerList <?= empty($recentArticles) ? 'activeLink' : '' ?>">
            <?php foreach ($trendings as $trending): ?>
                <?php if (isset($article) && $article->slug == $trending['slug']): ?>
                    <?php continue; ?>
                <?php endif; ?>
                <a class="listCard" href="<?= Url::toArticleDetail($trending['slug'], DataHelper::getLangCode($trending['lang_code'])) ?>" title="<?= $trending['title'] ?>">
                    <div class="trendingArtilerDiv row">
                        <div class="sidebarImgDiv">
                            <img class="lazyload" loading="lazy" src="<?= ArticleDataHelper::getImage($trending['cover_image']) ?>" width="60px" height="60px" alt="<?= $trending['title'] ?>">
                        </div>
                        <div class="trendingArtileText">
                            <p class="sidebarTextLink"><?= $trending['title'] ?? '' ?></p>
                        </div>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>