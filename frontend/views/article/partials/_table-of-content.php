<?php
if (!empty($h2Content)):
    ?>
    <div class="table-content-ctn">
        <p class="table-content-heading-article">
            <span><?php if ($lang=='en') {
                ?> Table of Contents <?php
                  } else {
                        ?> विषयसूची  <?php
                  } ?></span>
            <span class="spriteIcon downArrowIcon"></span>
        </p>
        <ul class="table-content-article open" style="">
            <?php foreach ($h2Content as $key => $text):
                $string = htmlentities($key, null, 'utf-8');
                $content = str_replace('&nbsp;', '', $string);
                $key = strtolower(html_entity_decode($content));
                if ($key != ''):
                    ?>

                    <li><a target="_self" href="#<?= $key; ?>" attr-id="<?= $key; ?>" class="scrollh2"><?= $text; ?></a></li>

                    <?php
                endif;
            endforeach;
            ?>
        </ul>
    </div>
<?php endif;
