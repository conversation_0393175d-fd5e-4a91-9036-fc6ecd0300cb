<?php

use common\helpers\DataHelper;
use common\models\Lead;
use frontend\helpers\Url;
use frontend\helpers\Lead as HelpersLead;
use yii\helpers\ArrayHelper;
use common\helpers\BoardHelper;

$isMobile = \Yii::$app->devicedetect->isMobile();
?>
<ul>
    <?php if ($entityName == 'exams'): ?>
        <?php foreach (DataHelper::examContentList() as $key => $value): ?>
            <?php if (in_array($key, $menus)): ?>
                <li>

                    <a target="_blank" href="<?= $key == 'overview' ? Url::toExamDetail($entitySlug) : Url::toExamDetail($entitySlug, '', $key) ?>">
                        <?= Yii::t('app', $value) ?>
                    </a>
                </li>
            <?php endif; ?>
        <?php endforeach; ?>
    <?php elseif ($entityName == 'colleges'): ?>
        <?php foreach ($menus as $key => $value): ?>
            <?php if (is_string($value)): ?>
                <li>
                    <a target="_blank" href="<?= $key == 'info' ?  Url::toCollege($entitySlug, '') : Url::toCollege($entitySlug, $key) ?>">
                        <?= Yii::t('app', $value) ?></a>
                </li>

            <?php endif; ?>
        <?php endforeach; ?>
    <?php elseif ($entityName == 'courses'): ?>
        <?php foreach ($menus as $key => $val): ?>
            <li><a target="_blank" class="" href="<?= $key == 'about' ? Url::toCourseDetail($entitySlug) : Url::toCourseDetail($entitySlug, $key) ?>">
                    <?= ($val == 'About') ? Yii::t('app', $entityDisplayName) : Yii::t('app', $val)?>
                </a>
            </li>
        <?php endforeach; ?>
    <?php elseif ($entityName == 'boards'): ?>
        <?php $menus = array_column(ArrayHelper::toArray($menus), 'page_slug');
        ?>
        <?php foreach ($allBoardPage as $key => $value): ?>
            <?php if (in_array($key, $menus)): ?>
                <li>

                    <a target="_blank" href="<?= $key == 'overview' ? Url::toBoardDetail($entitySlug) : Url::toBoardDetail($entitySlug, '', $key) ?>">
                        <?= Yii::t('app', $value) ?>
                    </a>
                </li>
            <?php endif; ?>
        <?php endforeach; ?>
    <?php endif; ?>
</ul>