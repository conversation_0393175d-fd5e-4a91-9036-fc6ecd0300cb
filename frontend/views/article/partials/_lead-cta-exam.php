<?php

use common\models\Article;
use common\models\Lead;
use frontend\helpers\Lead as HelpersLead;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
?>
<div class="getSupport">
    <!-- <div class="row">
        <img src="<?= '/yas/images/get-support.png' ?>" width="80" height="80" alt="">
        <p><?= Yii::t('app', 'Get Exam Alerts and Guidance') ?></p>
    </div> -->
    <?= frontend\helpers\Html::leadButton(
        'CHECK ELIGIBILITY',
        [
            'entity' => Lead::ENTITY_EXAM,
            'entityId' => $exam->id,
            'ctaLocation' => !$isMobile ? HelpersLead::getCTAsName(Article::ENTITY_ARTICLE . '.' . 'exams', 'articles_detail_web_lead_capture_panel_left_cta1') : HelpersLead::getCTAsName(Article::ENTITY_ARTICLE . '.' . 'exams', 'articles_detail_wap_bottom_left_sticky_cta2'),
            'ctaText' => 'CHECK ELIGIBILITY',
            'leadformtitle' => 'REGISTER TO GET NOTIFICATIONS',
            'subheadingtext' => $exam->display_name,
            'image' => !empty($exam->cover_image) ? Url::toExamImage($exam->cover_image) : Url::defaultCollegeLogo()
        ],
        ['class' => 'freeScholarship getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
    <?= frontend\helpers\Html::leadButton(
        'TALK TO EXPERTS',
        [
            'entity' => Lead::ENTITY_EXAM,
            'entityId' => $exam->id,
            'ctaLocation' => !$isMobile ? HelpersLead::getCTAsName(Article::ENTITY_ARTICLE . '.' . 'exams', 'articles_detail_web_lead_capture_panel_right_cta2') : HelpersLead::getCTAsName(Article::ENTITY_ARTICLE . '.' . 'exams', 'articles_detail_wap_bottom_right_sticky_cta3'),
            'ctaText' => 'TALK TO EXPERTS',
            'leadformtitle' => 'REGISTER FOR EXPERT GUIDANCE',
            'subheadingtext' => $exam->display_name,
            'image' => !empty($exam->cover_image) ? Url::toExamImage($exam->cover_image) : Url::defaultCollegeLogo()
        ],
        ['class' => 'applyScholarship getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
</div>