<?php

use backend\models\RecentActivityTracker;
use common\helpers\DataHelper;

$isMobile = \Yii::$app->devicedetect->isMobile();
$totalLimit = RecentActivityTracker::LIMIT_RECORD;
$recent_activity_html = [];
?>
<?php if (!empty($recentActivity[0])): ?>
    <div class="latestUpdates <?php echo (!$isMobile) ? 'desktopOnly' : ''; ?>">
        <p class="cardHeading">Latest Updates for <?php echo $recentActivity[1]; ?></p>
        <ul>
            <?php $page = 0;
            $printId = [];
            $pageName = $this->params['pageName'] ?? '';
            foreach ($recentActivity[0] as $recActivity) {
                if ($page == $totalLimit) {
                    break;
                }
                if (!empty($recActivity['page'])  && $recActivity['page'] == $pageName &&  $recActivity['entity'] != 3) {
                    $page++;
                    $printId[] = $recActivity['id'];
                    $timeDiff = date('d F Y', strtotime($recActivity['created_at']));
                    $recent_activity_html[] = '<li><span>' . $timeDiff . ' : ' . $recActivity['text'] . '</span></li>';
                }
            } ?>
            <?php
            $nopage = 0;
            $totalLimit  = $totalLimit - $page;
            foreach ($recentActivity[0] as $recActivity) {
                if ($nopage >= $totalLimit || $page >= 4) {
                    break;
                }
                if (empty($recActivity['page']) && !in_array($recActivity['id'], $printId)  &&  $recActivity['entity'] != 3) {
                    $nopage++;
                    $printId[] = $recActivity['id'];
                    $timeDiff = date('d F Y', strtotime($recActivity['created_at']));
                    $recent_activity_html[] = '<li><span>' . $timeDiff . ' : ' . $recActivity['text'] . '</span></li>';
                }
            }
            $totalLimit = $totalLimit - $nopage;
            $examLimit = 0;
            $entity = $this->params['entity'] ?? '';
            $entityData = ['college', 'course'];
            if ($entity == 'college' || $entity == 'course') {
                $examData  = DataHelper::getExamType($recentActivity[0], $entity);
                $recentActivity[0] = $examData;
            }

            foreach ($recentActivity[0] as $recActivity) {
                if ($examLimit >= $totalLimit || $nopage >= 4 || $page >= 4 || $totalLimit <= 0) {
                    break;
                }
                if ($recActivity['entity'] == 3 && !in_array($recActivity['id'], $printId)) {
                    $examLimit++;
                    $printId[] = $recActivity['id'];
                    $timeDiff = date('d F Y', strtotime($recActivity['created_at']));
                    if (!in_array($entity, $entityData) && $recActivity['page'] == $pageName) {
                        $recent_activity_html[] = '<li><span>' . $timeDiff . ' : ' . $recActivity['text'] . '</span></li>';
                        continue;
                    } else {
                        $recent_activity_html[] = '<li><span>' . $timeDiff . ' : ' . $recActivity['text'] . '</span></li>';
                    }
                }
            }
            usort($recent_activity_html, function ($b, $a) {
                preg_match('/(\d{2} [A-Za-z]+ \d{4}) :/', $a, $matchesA);
                preg_match('/(\d{2} [A-Za-z]+ \d{4}) :/', $b, $matchesB);
            
                $timestampA = strtotime($matchesA[1]);
                $timestampB = strtotime($matchesB[1]);
            
                return $timestampA - $timestampB;
            });
            
            foreach ($recent_activity_html as $item) {
                echo $item ;
            }
            ?>
        </ul>
    </div>
<?php endif; ?>
