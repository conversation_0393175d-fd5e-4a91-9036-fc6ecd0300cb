<?php

use common\helpers\DataHelper;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
$recent_activity_html = [];
?>
<?php if (!empty($examContentTemplate[0])): ?>
    <div class="latestUpdates whatsNew <?php echo (!$isMobile) ? 'desktopOnly' : ''; ?>">
        <h2 class="cardHeading">What's New in <?php echo $examContentTemplate[1]; ?></h2>
        <ul>
            <?php
            foreach ($examContentTemplate[0] as $contentTemplate) { ?>
                <li>
                    <span><?php echo $contentTemplate['content'] ?? ''; ?></span>
                </li>
            <?php } ?>
        </ul>
    </div>
<?php endif; ?>
