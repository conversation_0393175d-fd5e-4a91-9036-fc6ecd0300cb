<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;
use common\helpers\DataHelper;

$cardCount = 0;
$totalCards = 10;
$title = isset($title) && !empty($title) ? $title : 'Related Articles';
?>
<style>
    .widgetAuthorName {
        position: relative;
        color: #989898 !important;
        font-weight: 400 !important;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 1;
        padding-left: 16px;
        font-size: 14px !important;
        padding-top: 0px;
        margin-bottom: 3px;
    }

    .latestArticleSection.row {
        height: auto;
    }

    .latestArticleSection .article1-view {
        position: relative;
    }

    .latestArticleSection .articleDisplay {
        flex-basis: calc(100% - 735px);
        height: 270px;
        border-right: 0;
        position: relative;
    }

    .latestArticleSection .articleDisplay figure {
        border: none;
        max-height: 270px;
    }

    .latestArticleSection .articleDisplay:after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, #282828 56%);
        border-radius: 4px;
    }

    .latestArticleSection .articleDisplay .aticleInfo {
        padding: 20px;
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        z-index: 1;
    }

    .latestArticleSection .articleDisplay .aticleInfo h2 {
        margin-bottom: 10px;
        -webkit-line-clamp: initial;
        line-height: 32px;
    }

    .latestArticleSection .articleDisplay .aticleInfo h2 a,
    .latestArticleSection .articleDisplay .aticleInfo .updated-info p {
        color: var(--color-white);
    }

    .latestArticleSection img {
        height: 270px;
    }

    .latestArticleSection .articleList {
        flex-basis: 735px;
        padding: 21px 31px;
        position: relative;
    }

    .latestArticleSection .articleList ul li {
        height: 57px;
        padding: 0;
        min-height: 57px;
        border: none;
    }

    .latestArticleSection .articleList ul li img {
        width: 95px;
        height: 45px;
        display: inline-block;
        border-radius: 0px;
    }

    .latestArticleSection .articleList ul li img.gifLive {
        height: 15px;
        display: block;
    }

    .latestArticleSection .articleList ul li a {
        display: flex;
        width: 100%;
        align-items: center;
        max-height: 57px;
    }

    .latestArticleSection .articleList ul li a:hover .articleName {
        color: var(--anchor-textclr);
    }

    .latestArticleSection .articleList ul li .articleName {
        padding-left: 10px;
        flex-basis: calc(100% - 155px);
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        align-items: center;
        line-height: 20px;
    }

    .latestArticleSection .viewAll {
        position: absolute;
        right: 16px;
        height: auto;
        display: block;
        bottom: 16px;
        min-height: auto;
    }

    .latestInfoList.row {
        flex-wrap: nowrap;
        overflow: auto;
    }

    .latestInfoList.row::-webkit-scrollbar {
        display: none;
    }

    .latestInfoList .latestInfoDiv .viewAllDiv {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .latestInfoList .latestInfoDiv .viewAllDiv a {
        font-size: 14px;
        line-height: 24px;
        color: var(--color-red);
        text-align: center;
        font-weight: 600;
    }

    .latestInfoListContainer {
        position: relative;
    }

    .latestInfoListContainer .scrollLeft {
        top: 50%;
        left: -20px;
    }

    .latestInfoListContainer .scrollRight {
        right: -20px;
    }

    .latestInfoSection .latestInfoDiv {
        min-width: 23.7%;
    }

    .latestInfoSection .latestInfoDiv:nth-of-type(4n) {
        margin-right: 20px;
    }

    .latestInfoSection .latestInfoDiv:last-child {
        margin-right: 0;
    }

    .latestArticleSection .articleList ul li.hoverbg {
        border-left: 0px;
    }

    .articleDisplay>div {
        position: relative;
    }

    .latestInfoSection .latestInfoDiv img,
    .articlesByCategory .latestInfoDiv img {
        width: auto;
        margin: 0 auto;
        align-self: center;
        height: 207px;
    }

    /* .latestInfoDiv a:hover p:first-child {
        color: var(--anchor-textclr);
        text-decoration: underline;
    } */

    .latestInfoSection .latestInfoTxt p:first-child,
    .latestInfoSection .latestInfoTxt a,
    .otherEntranceExams .latestInfoTxt p,
    .otherEntranceExams .latestInfoTxt a {
        -webkit-line-clamp: 3;
        min-height: 72px;
        max-height: 100%;
    }

    @media (max-width: 1023px) {
        .latestInfoSection .latestInfoDiv img {
            height: 168px;
            width: 100%;
        }

        .latestArticleSection .articleDisplay {
            flex-basis: auto;
            height: 276px;
        }

        .latestArticleSection .articleDisplay .aticleInfo {
            padding: 10px;
        }

        .latestArticleSection .articleDisplay .aticleInfo h2 {
            line-height: 18px;
            min-height: auto;
            -webkit-line-clamp: initial;
            overflow-wrap: break-word;
        }

        .latestArticleSection .articleDisplay:after {
            display: none;
        }

        .latestArticleSection .articleDisplay>div {
            position: relative;
        }

        .latestArticleSection .articleDisplay>div:after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, #282828 56%);
            border-radius: 4px;
        }

        .latestArticleSection .articleDisplay .mobileOnly {
            vertical-align: bottom;
        }

        .latestArticleSection .articleDisplay .mobileOnly:after {
            display: none;
        }

        .latestArticleSection .articleDisplay .aticleInfo p {
            line-height: 20px;
            font-weight: normal;
        }

        .latestArticleSection .aticleInfo img.gifLive {
            max-width: 70px;
            height: 30px;
            margin-bottom: 10px;
        }

        .latestInfoListContainer .scrollRight,
        .latestInfoListContainer .scrollLeft {
            display: none !important;
        }
    }
</style>
<?php if (count($relatedArticles) > 4) { ?>
    <section class="latestInfoSection">

        <h2 class="row">
            <?= Yii::t('app', $title); ?>
        </h2>

        <div class="latestInfoListContainer four-cardDisplay">
            <?php if (count($relatedArticles) >= 4): ?>
                <i class="spriteIcon scrollLeft over"></i>
                <i class="spriteIcon scrollRight"></i>
            <?php endif; ?>
            <div class="latestInfoList row">
                <?php foreach ($relatedArticles as $relatedArticle): ?>
                    <?php
                    // breaks when totalCard count match
                    if ($cardCount == $totalCards) {
                        break;
                    }

                    $cardCount++;
                    ?>

                    <?php $post = (object) $relatedArticle; ?>
                    <?php if (isset($article) && $article->slug == $post->slug): ?>
                        <?php continue ?>
                    <?php endif; ?>
                    <div class="latestInfoDiv">
                        <a href="<?= Url::toArticleDetail($post->slug, DataHelper::getLangCode($relatedArticle['lang_code'])) ?>" title="<?= $post->title ?? '' ?>">
                            <figure>
                                <img class="lazyload" loading="lazy" width="274" height="207" data-src="<?= $post->cover_image ? ArticleDataHelper::getImage($post->cover_image) : ArticleDataHelper::getImage() ?>" src="<?= $post->cover_image ? ArticleDataHelper::getImage($post->cover_image) : ArticleDataHelper::getImage() ?>" alt="<?= $post->h1 ?>">
                            </figure>
                            <div class="latestInfoTxt">
                                <p>
                                    <?= BaseStringHelper::truncate($post->title, 95) ?? '' ?>
                                </p>
                            </div>
                        </a>
                        <div class="authorAndDate">
                            <?php
                            $authorName = '';
                            $authorSlug = '';
                            $langCode = $relatedArticle->lang_code ?? '';
                            if ($post instanceof common\models\Article && !empty($post->author)) {
                                $authorName = $post->author->name ?? '';
                                $authorSlug = $post->author->slug ?? '';
                            } else {
                                $authorName = is_object($post) ? $post->name : 'Getmyuni Content Team';
                                $authorSlug = is_object($post) && !empty($post->user_slug) ? $post->user_slug : 'getmyuni-content-team';
                            }
                            ?>
                            <a class="authorName" href="<?= ($langCode == 1 ) ? Url::toAllAuthorPost($authorSlug) :  Url::toAllAuthorPost($authorSlug, $langCode) ?>" title="<?= $authorName ?>" title="<?= $authorName ?>">
                                <?= $authorName ?>
                            </a>
                            <?php if (!empty($relatedArticle['updated_at'])): ?>
                                <p class="widgetAuthorName"><?= Yii::$app->formatter->asDate(date('Y-m-d', strtotime($relatedArticle['updated_at']))); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach;
                ?>
                <?php /*if (count($relatedArticles) > 5) : ?>
               <div class="latestInfoDiv">
                   <div class="viewAllDiv">
                       <a href=""><i class="spriteIcon viewAllIcon"></i>
                           VIEW ALL</a>
                   </div>
               </div>
           <?php endif;*/ ?>

            </div>
        </div>
    </section>
<?php } ?>