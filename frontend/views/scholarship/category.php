<?php

use common\helpers\ScholarshipHelper;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use yii\widgets\ListView;
use common\models\Scholarship;

// utils
$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();

$scholarships->prepare();

$defaultMeta = ScholarshipHelper::getDefaultSeoInfo('category-page', $category->name);

$this->title = !empty($category->meta_title) ? $category->meta_title : $defaultMeta['title'];
$this->context->description = empty($category->meta_description) ? $defaultMeta['description'] : $category->meta_description;
if ($category->slug == 'others') {
    $this->registerMetaTag(['name' => 'robots', 'content' => 'noindex, nofollow']);
}

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'Scholarships', 'url' => Url::toScholarships(), 'title' => 'Scholarships'];
$this->params['previous_url'] = Yii::$app->request->referrer;
$this->params['page_url'] = Yii::$app->request->url;
$this->params['entity_name'] = $category->name . ' Scholarships' ?? '';
$this->params['entity_id'] = $category->id  ?? 0;
$this->params['entity'] = Scholarship::ENTITY_SCHOLARSHIP;

if (Yii::$app->request->get('page') > 1) {
    $this->params['breadcrumbs'][] = [
        'label' => $category->name . ' Scholarships' ?? '',
        'url' => Url::canonical(),
        'title' => $category->name . ' Scholarships'
    ];
    $this->params['breadcrumbs'][] = 'Page ' . Yii::$app->request->get('page');
} else {
    $this->params['breadcrumbs'][] = $category->name . ' Scholarships';
}

$this->params['links'] = $scholarships->pagination->getLinks(true);

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'career-category.css', ['depends' => [AppAsset::class]]);
?>

<div class="">
    <div class="pageHeading">
        <h1>
            <?= !empty($category->h1) ? $category->h1 :  $defaultMeta['h1'] ?>
            <?= Yii::$app->request->get('page') > 1 ? ' - Page ' . Yii::$app->request->get('page') : '' ?>
        </h1>
    </div>
    <div class="row">
        <div class="col-md-8">
            <div class="categoryArticlesList">
                <?php
                echo ListView::widget([
                    'dataProvider' => $scholarships,
                    'itemView' => 'partials/_scholarship-list',
                    'pager' => [
                        'class' => 'LinkPager',
                    ],
                    'viewParams' => [
                        'fullView' => true,
                        'context' => 'main-page',
                        'isMobile' => $isMobile,
                        'defaultUser' => $defaultUser
                    ],
                    'layout' => "{items}\n{pager}",
                    'pager' => [
                        'class' => '\justinvoelker\separatedpager\LinkPager',
                        'maxButtonCount' => 7,
                        'prevPageLabel' => 'Previous',
                        'nextPageLabel' => 'Next',
                        'prevPageCssClass' => 'prev hidden-xs',
                        'nextPageCssClass' => 'next hidden-xs',
                        'activePageAsLink' => false,
                    ]
                ]);

                ?>

            </div>
        </div>

        <div class="col-md-4">
            <?php /**
            <aside>
                <?php if (!empty($trendingArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif; ?>
            </aside>
            */ ?>
            <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
            <aside>
                <div class="verticleRectangle">
                    <div class="appendAdDiv" style="<?= $isMobile ? 'height: 600px;' : '' ?>background:#EAEAEA;">
                    </div>
                </div>
            </aside>
            <?php endif; ?>
        </div>
    </div>
</div>