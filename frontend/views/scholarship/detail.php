<?php

use common\helpers\ArticleDataHelper;
use common\helpers\ScholarshipHelper;
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use yii\helpers\ArrayHelper;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use common\models\LiveUpdate;
use frontend\helpers\Ad;
use common\models\Scholarship;
use common\models\Country;
use common\models\State;
use frontend\helpers\Freestartads;

// utils
if (empty($content->author)) {
    $author = $content->defaultuser;
} else {
    $author = $content->author;
}
$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = $author ? ContentHelper::getUserProfilePic($author->slug) : '';

// meta key
$defaultMeta = ScholarshipHelper::getDefaultSeoInfo($page, $scholarship->name);

$this->title = !empty($content->meta_title) ? $content->meta_title : $defaultMeta['title'];
$this->context->description = empty($content->meta_description) ? $defaultMeta['description'] : $content->meta_description;
$this->context->ogType = 'scholarship';

if (!empty($category->slug) && ($category->slug == 'others')) {
    $this->registerMetaTag(['name' => 'robots', 'content' => 'noindex, nofollow']);
}

if (!empty($article->cover_image)) {
    $this->registerMetaTag(['property' => 'og:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'og:image:height', 'content' => '667']);
    $this->registerMetaTag(['property' => 'og:image:alt', 'content' => $content->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:alt', 'content' => $content->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:type', 'content' => 'image/jpeg']);
    $this->registerMetaTag(['property' => 'twitter:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'twitter:image:height', 'content' => '667']);
    $this->registerLinkTag(['href' => ScholarshipHelper::getImage($scholarship->cover_image),  'fetchpriority' => 'high', 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => ScholarshipHelper::getImage($article->cover_image) . ' 300w', 'imagesizes' => '50vw']);
    $this->context->ogImage = ScholarshipHelper::getImage($scholarship->cover_image);
}
if (!empty($authorImage)) {
    $this->registerLinkTag(['href' => $authorImage, 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => $authorImage . ' 300w', 'imagesizes' => '50vw']);
}
$this->registerMetaTag(['name' => 'robots', 'content' => 'max-image-preview:large']);
$this->registerMetaTag(['property' => 'scholarship:published_time', 'content' => date(DATE_ATOM, strtotime($content->created_at))]);

$this->registerMetaTag(['property' => 'scholarship:modified_time', 'content' => date(DATE_ATOM, strtotime($content->updated_at))]);
// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'Scholarships', 'url' => Url::toScholarships(), 'title' => 'Scholarships'];
if (!empty($category->slug)) {
    $this->params['breadcrumbs'][] = ['label' => $category->name, 'url' => Url::toScholarshipCategory($category->slug), 'title' => $category->name . ' Scholarships'];
}
if ($page != 'overview') {
    $this->params['breadcrumbs'][] = ['label' => $scholarship->name, 'url' => [Url::toScholarshipDetail($scholarship->slug)], 'title' => $scholarship->name];
}
$this->params['breadcrumbs'][] = $page == 'overview' ? $scholarship->name : (ScholarshipHelper::$subPages[$page] ?? '');

// dd($scholarship->slug);
$this->params['entity_name'] = $scholarship->name ?? '';
$this->params['entity_id'] = $scholarship->id ?? 0;
$this->params['entity'] = Scholarship::ENTITY_SCHOLARSHIP;
$this->params['entitySlug'] = $scholarship->slug;
$this->params['product_mapping_entity'] = empty($entityName) ? 'scholarship' : $entityName;
$this->params['product_mapping_entity_id'] = empty($entityId) ? 0 : $entityId;
$this->params['canonicalUrl'] = $currentUrl;
$this->params['pageName'] = $page;
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'subpage-navbar.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'career-detail.css', ['depends' => [AppAsset::class]]);

// schema
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}

$this->params['schema'] = \yii\helpers\Json::encode([[
    '@context' => 'http://schema.org',
    '@type' => 'Article',
    'mainEntityOfPage' => [
        '@type' => 'WebPage',
        '@id' => $currentUrl,
    ],
    'headline' => $this->title,
    'image' => [ScholarshipHelper::getImage($scholarship->cover_image)],
    'datePublished' => date(DATE_ATOM, strtotime($content->created_at)),
    'dateModified' => date(DATE_ATOM, strtotime($content->updated_at)),
    'author' => [
        '@type' => 'Person',
        'name' => $author ? $author->name : ''
    ],
    'publisher' => [
        '@type' => 'Organization',
        'name' => 'Getmyuni',
        'logo' => [
            '@type' => 'ImageObject',
            'url' => Yii::$app->params['gmuLogo']
        ],
    ],
    'description' => $content->meta_description
]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

if (!empty($chartData)) {
    $this->params['scholarshipChartData'] = $chartData;
}
$country = ArrayHelper::map(Country::find('name')->where(['iso_code' => $scholarship->country_id])->all(), 'iso_code', 'name');
$state = ArrayHelper::map(State::find()->where(['id' => $scholarship->state_id])->all(), 'id', 'name') ?? '';
$region = $country[$scholarship->country_id] ?? '';
if (!empty($state) && !empty($region)) {
    $region = $state[$scholarship->state_id] . ', ' . $country[$scholarship->country_id];
}
$scholarshipKeyInfo = [
    'Conducted By' => $scholarship->conducted_by ?? '-',
    'Eligibility' => $scholarship->eligibility ?? '-',
    'Region' => $region ?? '-',
    'Rewards' => $scholarship->reward ?? '-',
    'Last Date of Application' => Yii::$app->formatter->asDatetime($scholarship->deadline, 'MMMM dd ,yyyy') ?? '-'
];

?>

<div class="containerMargin">
    <div class="row">
        <div class="col-md-12">
            <div class="articleHeader">
                <section class="pageDescription">
                    <div class="row">
                        <div class="col-md-12">
                            <h1><?= !empty($content->h1) ? ContentHelper::htmlDecode(stripslashes($content->h1), false) : $defaultMeta['title'] ?></h1>
                            <div class="authorInfoAndTranslateBtn">
                                <div class="updated-info row">
                                    <img class="lazyload" loading="lazy" width="60" height="60" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author ? $author->name . ' Image' : '' ?>" />
                                    <span class="updatedDetails">
                                        <?php if ($author): ?>
                                            <div class="updatedBy">
                                                <p><a href="<?= $author ? '/author/' . $author->slug : '#' ?>"><?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?></a>, </p>
                                            </div>
                                            <p><span><?= Yii::$app->formatter->asDate($content->updated_at ?? 'today') ?> </span>
                                        <?php endif; ?>
                                            </p>
                                            <ul>
                                                <p>Share it on:</p>
                                                <li>
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $currentUrl ?>" target="_blank" rel="noopener nofollow" class="spriteIcon greyFbIcon"></a>
                                                </li>
                                                <li>
                                                    <a href="https://twitter.com/share?url=<?= $currentUrl ?>" class="spriteIcon greyTwitterIcon" rel="noopener nofollow" target="_blank"></a>
                                                </li>
                                            </ul>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <?php if (!empty($menus)): ?>
            <?= $this->render('partials/_menu-card', [
                'menus' => $menus,
                'pageName' => $page,
                'scholarship' => $scholarship
            ]);
            ?>
        <?php endif; ?>
        <div class="col-md-8">
            <main>
                <article>
                    <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                        <div class="horizontalRectangle">
                            <div class="appendAdDiv" style="<?= $isMobile ? 'height: 50px;' : '' ?>background:#EAEAEA;">
                                <?php echo Ad::unit('GMU_ARTICLES_LANDING_WEB_728x90_ATF', '[728,90]') ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($scholarship->cover_image)): ?>
                        <div class="bannerImg">
                            <picture>
                                <source media="(max-width: 500px)" srcset="<?= ScholarshipHelper::getImage($scholarship->cover_image) ?>">
                                <img width="1200" height="675" src="<?= ScholarshipHelper::getImage($scholarship->cover_image) ?>" alt="<?= $content->h1 ?>" />
                            </picture>
                        </div><br />
                    <?php endif; ?>

                    <?php if ($page == 'overview'): ?>
                        <div class="examInfo featureDetail">
                            <div class="pageData examDetailsTable">
                                <h2><?= $scholarship->name ?> Highlights</h2>
                                <table>
                                    <tbody>
                                        <?php foreach ($scholarshipKeyInfo as $key => $value): ?>
                                            <tr>
                                                <td><b> <?= $key ?> </b></td>
                                                <td> <?= $value ?> </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="articleInfo">
                        <?= ContentHelper::removeStyleTag(stripslashes(
                            html_entity_decode(DataHelper::parseDomainUrlInContent($content->content))
                        )) ?>
                    </div>

                </article>
                <?php if (!empty($faqs)): ?>
                    <section class="faq_section">
                        <h2>FAQs</h2>
                        <div class="faqDiv">

                            <?php foreach ($faqs as $faq): ?>
                                <div>
                                    <p class="faq_question">
                                        <?= ContentHelper::htmlDecode($faq->question, true) ?>
                                    </p>
                                    <div class="faq_answer" style="display: none;">
                                        <?= 'A: ' . ContentHelper::htmlDecode($faq->answer, false) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                        </div>
                    </section>
                <?php endif; ?>

                <?php if (!empty($chartData) && $page != 'overview'): ?>
                    <div class="pageData">
                        <h2><?= $scholarship->name ?> Salary Structure</h2>
                        <div class="chartContainer">
                            <canvas class="scholarshipChartData" id="scholarshipChartData" width="400" height="200"></canvas>
                        </div>
                        <p><i>Note : The Salary is subject to change as per market conditions.</i></p>
                    </div>
                <?php endif; ?>
                <?php if (!empty($scholarshipExam)): ?>
                    <?= $this->render('../college/partials/_college-exam', [
                        'title' => 'Exams Accepted by ' . $scholarship->name,
                        'collegeExams' => $scholarshipExam
                    ]) ?>
                <?php endif; ?>
            </main>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                            ?>
                        <?php else: ?>
                            <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <div class="col-md-4">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <?= $this->render('partials/_lead', [
                        'scholarship' => $scholarship,
                        'page' => $page
                    ]) ?>
                <?php endif; ?>
                <?php if (!empty($otherExplore)): ?>
                    <?= $this->render('partials/_other-scholarship-list', [
                        'recentArticles' => $otherExplore,
                        'scholarship' => $scholarship,
                    ]); ?>
                <?php endif; ?>
                <?php /**
                <?php if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($trendingArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif; ?>
                <?php */ ?>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="squareDiv">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__200x600')
                                ?>
                            <?php else: ?>
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x250')
                                ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php if (!$isMobile): ?>
                        <div class="verticleRectangle">
                            <div class="appendAdDiv" style="background:#EAEAEA;">
                                <?php echo Ad::unit('GMU_ARTICLES_DETAIL_WEB_300x250_MTF_1', '[300,250]') ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </aside>
        </div>
    </div>
    <?php /**
    <section class="commentSection">
        <?= $this->render('/partials/comment/_form', [
            'model' => $commentModel,
            'entity' => Scholarship::ENTITY_SCHOLARSHIP,
            'entity_id' => $scholarship->id
        ]) ?>
        <?= $this->render('/partials/comment/_comment', [
            'comments' => $comments,
            'entity' => Scholarship::ENTITY_SCHOLARSHIP,
            'entityId' => $scholarship->id
        ]) ?>
    </section>
     */ ?>

    <!-- college widget -->
    <?php if (!empty($scholarship->college)): ?>
        <section class="pageData">
            <h2 class="row">
                College Offering Course
                <?php /*if (isset($viewAllUrl) && !$isMobile): ?>
                    <a href="<?= Url::toAllCollege($viewAllUrl) ?>">View All</a>
                <?php endif;*/ ?>
            </h2>
            <div class="customSlider four-cardDisplay">
                <?php /*if (count($scholarship->college) > 4): ?>
                    <i class="spriteIcon scrollLeft over"></i>
                    <i class="spriteIcon scrollRight"></i>
                <?php endif;*/ ?>

                <div class="customSliderCards">
                    <?php $count = 1;
                    foreach ($scholarship->college as $college):
                        if ($count < 5):
                            ?>
                            <div class="sliderCardInfo">
                                <div class="displayCard">
                                    <a href="<?= Url::toCollege($college->slug) ?>" title="<?= $college->name ?>">
                                        <figure>
                                            <img class="lazyload" width="275" height="207" loading="lazy" data-src="<?= !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner() ?>" src="<?= !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner() ?>" alt="">
                                        </figure>

                                        <div class="textDiv pb-0">
                                            <img class="collegeLogo lazyload" width="56" height="56" loading="lazy" data-src="<?= !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo() ?>" src="<?= !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo() ?>" alt="">
                                            <p class="widgetCardHeading"><?= $college->name ?></p>
                                        </div>
                                    </a>
                                    <p class="subText pt-0"><span class="spriteIcon locationIcon"></span> <?= $college->city->name ?>, <?= $college->city->state->name ?></p>
                                </div>
                            </div>
                        <?php endif;
                    endforeach;
                    $count++; ?>

                    <?php /*if (isset($viewAllUrl) && $isMobile): ?>
                        <div class="sliderCardInfo mobileOnly">
                            <div class="viewAllDiv">
                                <a href="<?= Url::toAllCollege($viewAllUrl) ?>"><i class="spriteIcon viewAllIcon"></i>VIEW ALL</a>
                            </div>
                        </div>
                    <?php endif;*/ ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- related article -->
    <?php /***
    <?php if (!empty($relatedScholarship) && count($relatedScholarship) > 1): ?>
        <?= $this->render('partials/_related-scholarship-list', [
            'relatedArticles' => $relatedScholarship,
            'article' => $scholarship
        ]); ?>
    <?php endif; ?>

    <?php if (!empty($scholarship->article) && count($scholarship->article) > 1): ?>
        <?= $this->render('partials/_article-list', [
            'relatedArticles' => $scholarship->article,
            'article' => $scholarship
        ]); ?>
    <?php endif; ?>
     */ ?>


    <div id="comment-reply-form-js"></div>
</div>

<?php
$this->registerJsFile('https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.1.4/Chart.min.js', ['depends' => [AppAsset::class]]);
?>