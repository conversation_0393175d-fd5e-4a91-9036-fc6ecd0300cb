<?php

use common\services\UserService;
use common\models\Lead;
use frontend\helpers\Url;
use common\helpers\DataHelper;

$name = !empty($model->display_name) ? $model->display_name : $model->name;
$examDates = $model->examDates;
$contents = $model->examContents;
foreach ($examDates as $examDate) {
    if ($examDate->status) {
        $dates[$examDate->slug] = $examDate->attributes;
    }
}

if (!empty($dates['exam-start']['start'])) {
    $year = explode('-', $dates['exam-start']['start']);
}

$ctaLocation = $isMobile ? UserService::parseDynamicCta('exam_listing_page_wap_{slug}_card_cta1', '', $model->slug) : UserService::parseDynamicCta('exam_listing_page_web_{slug}_card_cta1', '', $model->slug);
$ctaText = 'APPLY NOW';
?>
<div class="examTypeDiv">
    <div class="row">
        <div class="clgLogo">
            <img width="72" height="72" src="<?= !empty($model->cover_image) ? Url::toExamImage($model->cover_image) : '/yas/images/clgProfileIcon.png' ?>" alt="<?= $name ?>" onclick="gmu.url.goto('<?= Url::toExamDetail($model->slug, DataHelper::getLangCode($model->lang_code)) ?>')">
        </div>

        <div class="examTypeInfo">
            <h3><a href="<?= Url::toExamDetail($model->slug, DataHelper::getLangCode($model->lang_code)) ?>" title="<?= $name ?>"><?= $name . ' ' . (!empty($year[0]) ? $year[0] : date('Y')) ?></a></h3>

            <?php if (!$isMobile): ?>
                <div class="scheduledDates row desktopOnly">
                    <div class="col-md-4">
                        <p> <?= Yii::t('app', 'Application')?> <span class="desktopOnly"> <?= Yii::t('app', 'Form')?> </span> <?= Yii::t('app', 'Date')?></p>
                        <p><?= isset($dates['application-form'], $dates['application-form']['start']) ? Yii::$app->formatter->asDate(substr($dates['application-form']['start'], 0, 10)) : 'N/A' ?></p>
                    </div>
                    <div class="col-md-4">
                        <p><?= Yii::t('app', 'Exam Date')?></p>
                        <p><?= isset($dates['exam-start'], $dates['exam-start']['start']) ? Yii::$app->formatter->asDate(substr($dates['exam-start']['start'], 0, 10)) : 'N/A' ?></p>
                    </div>
                    <div class="col-md-4">
                        <p><?= Yii::t('app', 'Result Date')?></p>
                        <p><?= isset($dates['result-date'], $dates['result-date']['start']) ? Yii::$app->formatter->asDate(substr($dates['result-date']['start'], 0, 10)) : 'N/A' ?></p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php if ($isMobile): ?>
            <div class="mobileOnly">
                <div class="scheduledDates row">
                    <div class="col-md-4 col-5">
                        <p> <?= Yii::t('app', 'Application')?> <span class="desktopOnly"> <?= Yii::t('app', 'Form')?> </span> <?= Yii::t('app', 'Date')?></p>
                        <p><?= isset($dates['application-form'], $dates['application-form']['start']) ? Yii::$app->formatter->asDate(substr($dates['application-form']['start'], 0, 10)) : 'N/A' ?></p>
                    </div>
                    <div class="col-md-4 col-4">
                        <p><?= Yii::t('app', 'Exam Date')?></p>
                        <p><?= isset($dates['exam-start'], $dates['exam-start']['start']) ? Yii::$app->formatter->asDate(substr($dates['exam-start']['start'], 0, 10)) : 'N/A' ?></p>
                    </div>
                    <div class="col-md-4 col-3">
                        <p><?= Yii::t('app', 'Result Date')?></p>
                        <p><?= isset($dates['result-date'], $dates['result-date']['start']) ? Yii::$app->formatter->asDate(substr($dates['result-date']['start'], 0, 10)) : 'N/A' ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
            <div class="applyClg">
                <div class="lead-cta" id="<?= $model->id ?>" data-slug="<?= $model->slug . '-6' ?>" data-entity="exam" data-lead_cta="6" data-entityid="<?= $model->id ?>" data-location="<?= $ctaLocation ?>" data-image="<?= Url::toExamImage($model->cover_image) ?>" data-description="<?= $model->display_name ?>"></div>
            </div>
        <?php endif; ?>
    </div>
    <?php
    $subPages = [];
    $toDisplay = ['previous-year-paper-content', 'eligibility', 'exam-pattern', 'important-dates'];
    foreach ($contents as $content) {
        if (!in_array($content->slug, $toDisplay)) {
            continue;
        }
        $subPages[$content->slug] = [
            'name' => !empty($content->display_name) ? $content->display_name : $content->name,
        ];
    }
    if (!empty($subPages)) {
        ?>
        <div class="examCriteria">
            <ul>
                <?php foreach ($subPages as $pageSlug => $pageName): ?>
                    <li><a href="<?= Url::toExamDetail($model->slug, DataHelper::getLangCode($model->lang_code), $pageSlug) ?>" title="<?= $name . ' ' . $pageName['name'] ?>"><?= $pageName['name'] ?></a></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php } ?>
    <?php if ($isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
        <div class="lead-cta" id="<?= $model->id ?>" data-slug="<?= $model->slug . '-7' ?>" data-entity="exam" data-lead_cta="7" data-entityid="<?= $model->id ?>" data-location="<?= $ctaLocation ?>" data-image="<?= Url::toExamImage($model->cover_image) ?>" data-description="<?= $model->display_name ?>"></div>
    <?php endif; ?>
</div>