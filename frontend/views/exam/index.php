<?php

use common\helpers\DataHelper;
use common\helpers\ContentHelper;
use common\models\Exam;
use common\models\Lead;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Url;
use yii\helpers\Inflector;
use yii\helpers\StringHelper;
use frontend\services\ExamService;

$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->title = ($filterPageInfo == false) ? 'Entrance Exams In India - Getmyuni' : (!empty($filterPageInfo->title) ? DataHelper::parseMetaTopContent($filterPageInfo->title) : 'Entrance Exams In India - Getmyuni');
$this->context->description = ($filterPageInfo == false) ? 'Get detailed information on Top Entrance Exams In India.' : (!empty($filterPageInfo->description) ? DataHelper::parseMetaTopContent($filterPageInfo->description) : 'Get detailed information on Top Entrance Exams In India.');
$this->context->ogImage = '';

// breadcrumbs
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Exams');

$this->registerCssFile(Yii::$app->params['cssPath'] . 'exam_landing.css', ['depends' => [AppAsset::class]]);

// page specific assets
$this->params['entity'] = Exam::ENTITY_LANDING_EXAM;
$this->params['entity_id'] = $exam->id ?? 0;
$this->params['entity_name'] = 'Exams-Listing-Page';

//dynamic cta lead utils
$ctaText = 'GET EXAMS ALERT';
?>
<div class="heroSection">
    <div class="row">
        <div class="col-md-7">
            <h1><?= ($filterPageInfo == false) ? 'Entrance Exams in India' : (!empty($filterPageInfo->h1) ? DataHelper::parseMetaTopContent($filterPageInfo->h1) : 'Entrance Exams in India') ?></h1>

            <div class="searchBar">
                <input class="searchForExam search-autocomplete" data-type="exam" id="autoComplete" autocomplete="off" placeholder="<?= Yii::t('app', 'Search for Entrance Exams') ?>" type="text" tabindex="1">
                <div class="selection"></div>
            </div>

            <?php if (!empty($upcomingExams)): ?>
                <p><?= Yii::t('app', 'UPCOMING EXAMS') ?></p>
                <div class="upcomigExamDiv">
                    <button class="spriteIcon scrollLeft over"></button>
                    <button class="spriteIcon scrollRight"></button>
                    <div class="upcomigExamList">
                        <?php foreach ($upcomingExams as $date): ?>
                            <div class="upcomigExam">
                                <div class="row">
                                    <div class="dateLabel"><span> <?= Yii::$app->formatter->asDate($date->start, 'd') ?></span>
                                        <?= Yii::$app->formatter->asDate(substr($date->start, 0, 10), 'php:M') ?></div>
                                    <a href="<?= Url::toExamDetail($date->exam->slug, DataHelper::getLangCode($date->exam->lang_code)) ?>" title="<?= $date->exam->display_name ?>"><?= $date->exam->display_name ?></a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
            <div class="col-md-5">
                <div class="lead-cta" data-entity="exam" data-lead_cta="4"></div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php if ($isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
    <div class="lead-cta" data-entity="exam" data-lead_cta="5"></div>
<?php endif; ?>

<?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
    <aside class="horizontalRectangle desktopOnly">
        <div class="appendAdDiv">
            <?php //echo Ad::unit('GMU_EXAMLANDING_WEB_728x90_ATF', '[728,90]')
            ?>
            <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
            ?>
        </div>
    </aside>
<?php endif; ?>


<?php if ($filterPageInfo !== false): ?>
    <section class="browseByCategory pageInfo">
        <h2><?= Yii::t('app', 'BROWSE BY CATEGORY') ?></h2>
        <?= ContentHelper::htmlDecode(DataHelper::parseMetaTopContent(
            DataHelper::parseDomainUrlInContent($filterPageInfo->top_content)
        ) ?? '', false) ?>
    </section>
<?php endif; ?>

<div class="categoryList row">
    <?php foreach (DataHelper::$examsList as $discipline => $exams): ?>
        <?php $disciplineSlug = Inflector::slug(StringHelper::truncateWords($discipline, 1)); ?>
        <div class="categoryDiv">
            <div class="row">
                <figure>
                    <span title="<?= $discipline ?> Exam" onclick="gmu.url.goto('<?= Url::toDisciplineExam($disciplineSlug, 'india', $langCode) ?>')" class="examSprite <?= $disciplineSlug ?>"></span>
                </figure>

                <h3><a href="<?= Url::toDisciplineExam($disciplineSlug, 'india', $langCode) ?>" title="<?= $discipline ?> Entrance Exams in India"><?= Yii::t('app', $disciplineSlug) ?></a></h3>
            </div>

            <ul class="listImage">
                <?php foreach ($exams as $exam):
                    if (ExamService::checkExamLanguage($exam['slug'], $langCode)): ?>
                        <li><a href="<?= Url::toExamDetail($exam['slug'], $langCode) ?>" title="<?= $exam['title'] ?>"><?= Yii::t('app', StringHelper::truncate($exam['title'], 40)) ?></a></li>
                    <?php endif;
                endforeach; ?>
            </ul>
            <a href="<?= Url::toDisciplineExam($disciplineSlug, 'india', $langCode) ?>" title="<?= $discipline ?> Entrance Exams in India" class="viewExam"><?= Yii::t('app', 'VIEW ALL') ?></a>
        </div>
    <?php endforeach; ?>
</div>

<?php if (count($articles)): ?>
    <section class="latestInfoSection">
        <h2 class="row"> <?= Yii::t('app', 'Explore Articles on Entrance Exams in India') ?>
            <a href="<?= Url::toArticles($langCode) ?>" class="viewAll"> <?= Yii::t('app', 'VIEW ALL') ?></a>
        </h2>
        <div class="latestInfoList row">
            <?php $articlesCount = 0 ?>
            <?php foreach ($articles as $article):
                ?>

                <?php if ($articlesCount == 4) {
                    break;
                }
                $articlesCount++; ?>
                <article class="latestInfoDiv">
                    <figure>
                        <img src="<?= DataHelper::s3Path(null, 'board_article', 'path') . '/' . $article->cover_image ?>" alt="">
                    </figure>
                    <div class="latestInfoTxt">
                        <p><a href="<?= Url::toArticleDetail($article->slug, DataHelper::getLangCode($article->lang_code)) ?>"><?= $article->h1 ?></a></p>
                        <p><?= $article->user->name ?? '' ?></p>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>
    </section>
<?php endif; ?>
<?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
    <aside class="horizontalRectangle">
        <div class="appendAdDiv">
            <?php if ($isMobile): ?>
                <?php //echo Ad::unit('GMU_EXAMLANDING_WAP_300x250_BTF', '[300,250]')
                ?>
                <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                ?>

            <?php else: ?>
                <?php //echo Ad::unit('GMU_EXAMLANDING_WEB_728x90_BTF', '[728,90]')
                ?>
                <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90')
                ?>

            <?php endif; ?>
        </div>
    </aside>
<?php endif; ?>