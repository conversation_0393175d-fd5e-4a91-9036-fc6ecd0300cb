<?php

use common\helpers\DataHelper;
use common\models\Lead;

$isMobile = \Yii::$app->devicedetect->isMobile();
$platform = $isMobile ? 'wap' : 'web';
$examKeys = array_keys($downloadableResource);

if (isset($downloadableResource[$exam_name])) {
    $examKeys = array_diff($examKeys, [$exam_name]);
    array_unshift($examKeys, $exam_name);
}

$firstExam = reset($examKeys);

$bannerImages = [
    '/yas/images/exam_download_resource/image_1.png',
    '/yas/images/exam_download_resource/image_2.png',
    '/yas/images/exam_download_resource/image_3.png',
    '/yas/images/exam_download_resource/image_4.png',
    '/yas/images/exam_download_resource/image_5.png'
];

$imageCount = count($bannerImages); // Get total number of images
$cardIndex = 0; // Track card index
?>
<div class="examDownload">
    <h2>Downloadable Resources for <?= $exam_name ?>s</h2>
    <ul class="tabsExamDownload">
        <?php foreach ($examKeys as $index => $exam): ?>
            <li class="tabExamDownload <?= $exam === $firstExam ? 'current' : '' ?>" data-tab="tab-<?= $index ?>">
                <?= $exam ?>
            </li>
        <?php endforeach; ?>
    </ul>

    <?php foreach ($examKeys as $index => $examName): ?>
        <?php
        // Get the first available category for this exam
        $firstCategory = !empty($downloadableResource[$examName]) ? array_keys($downloadableResource[$examName])[0] : null;
        ?>
        <div id="tab-<?= $index ?>" class="tab-content <?= $index === 0 ? 'current' : '' ?>">
            <div class="exam-res-inner-tab">
                <?php if (!empty($downloadableResource[$examName])): ?>
                    <?php foreach ($downloadableResource[$examName] as $category => $years): ?>
                        <button class="category-btn <?= $category === $firstCategory ? 'active' : '' ?>"
                            onclick="showCategory('<?= $index ?>', '<?= $category ?>')">
                            <?= $category ?>
                        </button>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p>No resources available.</p>
                <?php endif; ?>
            </div>

            <div class="customSlider custom-cardDisplay">
                <?php if (!$isMobile): ?>
                    <i class="spriteIcon scrollLeft"></i>
                    <i class="spriteIcon scrollRight"></i>
                <?php endif; ?>
                <div class="customSliderCards" id="content-<?= $index ?>">
                    <?php foreach ($downloadableResource[$examName] as $category => $years): ?>
                        <div class="category-content <?= $category === $firstCategory ? 'activeCategory' : '' ?>"
                            id="content-<?= $index ?>-<?= $category ?>">
                            <?php $samplePaperCount = 1;
                            foreach ($years as $year => $documents): ?>
                                <?php
                                $imageIndex = $cardIndex % $imageCount;
                                $cardIndex++;
                                $inArrayText = [
                                    'Paper Analysis',
                                    'Sample Papers',
                                    'Previous Year Question Paper'
                                ];
                                ?>
                                <?php
                                $cardName = DataHelper::getCardNameExamDownloadResource(
                                    $examName,
                                    $year,
                                    $category,
                                    $samplePaperCount
                                );
                                ?>
                                <div class="sliderCardInfo">
                                    <div class="cardInfo">
                                        <img src="<?= $bannerImages[$imageIndex] ?>" alt="Resource Image" />
                                        <div class="getUnivSolution">
                                            <span> <?= $cardName ?> </span>
                                            <?= in_array($category, $inArrayText) ? 'with Solutions' : '' ?>
                                        </div>
                                    </div>
                                    <?= frontend\helpers\Html::leadButton(
                                        '<span class="spriteIcon whiteDownloadIcon redDownloadIcon"></span> Download',
                                        [
                                            'entity' => Lead::ENTITY_EXAM,
                                            'entityId' => $exam_id ?? null,
                                            'ctaLocation' => 'exam_' . $exam_name . '_downloadable_resource_' . $platform,
                                            'ctaText' => 'Download',
                                            'leadformtitle' => 'Register Now To Download' . $category,
                                            'files' => implode(',', array_column($documents, 'file_name')),
                                            'category' => $examName . '-' . $year . '-' . $category
                                        ],
                                        ['class' => 'download-btn examLeadValue'],
                                        'js-open-lead-form-new'
                                    ) ?>
                                    <!-- <button class="download-btn"
                                        data-entity="<?php //$examName . '-' . $category
                                        ?>"
                                        data-files="<?php //implode(',', array_column($documents, 'file_name'))
                                        ?>">
                                        <span class="spriteIcon whiteDownloadIcon redDownloadIcon"></span>Download
                                    </button> -->
                                    <!-- <p>
                                        <span class="spriteIcon whiteDownloadIcon redDownloadIcon updownArrow"></span>
                                        <span>33</span> Downloads Today
                                    </p> -->
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

</div>

<div class="examPdfonEmail" style="display: none;">
    <div class="examRow">
        <p id="examDownloadText">Get All Downloads in One Click</p>
        <?= frontend\helpers\Html::leadButton(
            '<span class="spriteIcon whiteDownloadIcon redDownloadIcon getDownload"></span> Get PDF',
            [
                'entity' => Lead::ENTITY_EXAM,
                'entityId' => $exam_id ?? null,
                'ctaLocation' => 'exam_' . $exam_name . '_downloadable_resource_all_pdf_' . $platform,
                'ctaText' => 'Get PDF',
                'leadformtitle' => 'Register Now To Download',
                'files' => '',
                'category' => '',
            ],
            ['class' => 'download-btn examLeadValue'],
            'js-open-lead-form-new'
        ) ?>
        <!-- <button type="button">
            <span class="spriteIcon whiteDownloadIcon redDownloadIcon getDownload"></span>
            Get PDF
        </button> -->
    </div>
</div>