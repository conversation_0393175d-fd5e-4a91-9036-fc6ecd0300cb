<style>
    span.select2.select2-container.select2-container--default.select2-container--focus,
    span.select2.select2-container.select2-container--default.select2-container--below.select2-container--open,
    span.select2.select2-container.select2-container--default.select2-container--below,
    span.select2.select2-container.select2-container--default {
        width: 100% !important;
    }
</style>
<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\helpers\DataHelper;
use frontend\models\LeadForm;
use yii\helpers\ArrayHelper;

$model = new LeadForm();

$cities = $model->city;
$cities = ArrayHelper::map($cities, 'id', 'name', 'state_slug');
$rankTitle = ($pageName == 'rank-predictor') ? sprintf('Predict your rank for %s %s here', $displayName, $localizeYear) : sprintf('Predict your percentile for %s %s here', $displayName, $localizeYear);
$rankType = ($pageName == 'rank-predictor') ? 'rank' : 'percentile';
?>

<section class="predictorFromSection" id="rankPredictorLandingForm">
    <div class="predict__rank__input__section">
        <h2><?= $rankTitle ?></h2>
        <input type="number" placeholder="Enter Marks" id="rankPredictormarks" min="1" required>
        <input type="hidden" id="rankPredictorexamId" value="<?= $this->params['entity_id'] ?>">
        <span id="errorMessage" class="help-block"></span>
        <p>If you dont have your actual marks, you can enter expected marks</p>
        <button type="submit" class="primaryBtn examLeadValue" data-platform="<?= \Yii::$app->devicedetect->isMobile() ? 'wap' : 'web' ?>" data-entity="exam" id="rankPredictButton" onclick="rankPredictorInputVerify('<?= $rankType?>')" data-entityId=<?= $this->params['entity_id']; ?> 
        data-ctaLocation=<?= \Yii::$app->devicedetect->isMobile() ? 'rank-predictor_wap' : 'rank-predictor_web' ?> data-ctaText="Rank Predictor" data-subheadingtext="Please fill in the details to receive a detailed report of your <?= $rankType ?>" data-leadformtitle="<?= $rankTitle ?>">Next</button>
    </div>
    <div class="predict__rank__form__section" style="display: none;">
        <?= $this->render('_rank_predictor_lead_form') ?>
    </div>
</section>

<section class="predictorFromSection" id="rankPredictorResult" style="display: none;">
    <div class="predictionResultDiv">
        <h3 class="predictionHeaading">
            <?= ($pageName == 'rank-predictor') ? sprintf('Your %s %s rank should fall approximately in the range of', $displayName, $localizeYear) : sprintf('Your %s %s Percentile approximately  should be around', $displayName, $localizeYear) ?> </h3>
        <p class="result" id="rankPredictResult">Oops! Something went wrong.</p>
        <button class="primaryBtn" style="display:none">Predict Colleges</button>
        <button class="primaryBtn backToForm" id="rankPredictStartOver">Back to <?= $rankType ?> Predictor</button>
    </div>
</section>