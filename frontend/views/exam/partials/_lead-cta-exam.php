<?php

use common\services\UserService;
use common\models\Lead;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();

//article (product mapping) cta location
$cta_location1 = isset($articleCtaLocation) ? UserService::parseDynamicCta($articleCtaLocation, '', 'left') : '';
$cta_location2 = isset($articleCtaLocation) ? UserService::parseDynamicCta($articleCtaLocation, '', 'right') : '';
$defaultEmptyCondition1 = empty($dynamicCta) && empty($dynamicCta['cta_position_2']) || empty(array_filter($dynamicCta['cta_position_2']));
?>

<div class="getSupport">
    <!-- <div class="row">
        <img src="<?= '/yas/images/bulbIcon.svg' ?>" width="80" height="80" alt="">
        <p>Get Exam Alerts and Guidance</p>
    </div> -->
    <?= frontend\helpers\Html::leadButton(
        empty($dynamicCta) && empty($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])) ? 'CHECK ELIGIBILITY' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'CHECK ELIGIBILITY'),
        [
            'entity' => Lead::ENTITY_EXAM,
            'entityId' => $exam->id,
            'ctaLocation' => isset($articleCtaLocation) ? $cta_location1 : ($isMobile ? (empty($dynamicCta) ? UserService::parseDynamicCta('exam_{slug}_wap_bottom_left_sticky_cta2', '', $page) : $dynamicCta['cta_position_1']['wap']) : (empty($dynamicCta) ? UserService::parseDynamicCta('exam_{slug}_web_lead_capture_panel_left_cta2', '', $page) : $dynamicCta['cta_position_1']['web'])),
            'ctaText' => empty($dynamicCta) && empty($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])) ? 'CHECK ELIGIBILITY' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'CHECK ELIGIBILITY'),
            'leadformtitle' => empty($dynamicCta) && empty($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])) ? 'REGISTER TO GET EXAM ALERTS' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS'),
            'subheadingtext' => empty($dynamicCta) && empty($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])) ? $exam->display_name : ($dynamicCta['cta_position_1']['lead_form_description'] ?? $exam->display_name),
            'image' => !empty($exam->cover_image) ? Url::toExamImage($exam->cover_image) : Url::defaultCollegeLogo(),
            'redirection' => empty($sponsorClientUrl) ? (empty($dynamicCta) && empty($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])) ? null : $dynamicCta['cta_position_1']['page_link']) : null
        ],
        ['class' => 'freeScholarship getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition1 ? 'TALK TO EXPERTS' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'TALK TO EXPERTS'),
        [
            'entity' => Lead::ENTITY_EXAM,
            'entityId' => $exam->id,
            'ctaLocation' => isset($articleCtaLocation) ? $cta_location2 : ($isMobile ? ($defaultEmptyCondition1 ? UserService::parseDynamicCta('exam_{slug}_wap_bottom_right_sticky_cta2', '', $page ?? '') : $dynamicCta['cta_position_2']['wap']) : ($defaultEmptyCondition1 ? UserService::parseDynamicCta('exam_{slug}_web_lead_capture_panel_right_cta2', '', $page ?? '') : $dynamicCta['cta_position_2']['web'])),
            'ctaText' => $defaultEmptyCondition1 ? 'TALK TO EXPERTS' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'TALK TO EXPERTS'),
            'leadformtitle' => $defaultEmptyCondition1 ? 'REGISTER TO GET EXAM ALERTS' : ($dynamicCta['cta_position_2']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS'),
            'subheadingtext' => $defaultEmptyCondition1 ? $exam->display_name : ($dynamicCta['cta_position_2']['lead_form_description'] ?? $exam->display_name),
            'image' => !empty($exam->cover_image) ? Url::toExamImage($exam->cover_image) : Url::defaultCollegeLogo(),
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition1 ? null : $dynamicCta['cta_position_2']['page_link']) : null
        ],
        ['class' => 'applyScholarship getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
</div>