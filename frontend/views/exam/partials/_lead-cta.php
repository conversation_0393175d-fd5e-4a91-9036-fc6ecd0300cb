<?php

use common\models\Lead;
use common\services\UserService;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
$defaultEmptyCondition0 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_0']) || empty(array_filter($dynamicCta['cta_position_0'])));
$defaultEmptyCondition1 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])));
$defaultEmptyCondition2 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_2']) || empty(array_filter($dynamicCta['cta_position_2'])));
$defaultEmptyCondition3 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_3']) || empty(array_filter($dynamicCta['cta_position_3'])));
$defaultEmptyCondition4 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_4']) || empty(array_filter($dynamicCta['cta_position_4'])));
$ctaExamAlertText = 'Get Exams Alerts';
?>

<?php if (isset($lead_cta) && $lead_cta == 0): ?>
    <?php $cta_text = $defaultEmptyCondition0 ? $ctaExamAlertText : $dynamicCta['cta_position_0']['cta_text']; ?>
    <?= frontend\helpers\Html::leadButton(
        $cta_text,
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition0 ? UserService::parseDynamicCta('exam_{slug}_wap_top_left_banner_cta', '', $pageName) : $dynamicCta['cta_position_0']['wap']) : ($defaultEmptyCondition0 ? UserService::parseDynamicCta('exam_{slug}_web_top_left_banner_cta', '', $pageName) : ($dynamicCta['cta_position_0']['web'])),
            'ctaText' => $cta_text,
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO GET EXAM ALERTS' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS'),
            'subheadingtext' => $defaultEmptyCondition0 ? $name : ($dynamicCta['cta_position_0']['lead_form_description'] ?? $name),
            'image' => !empty($exam->cover_image) ? Url::toExamImage($exam->cover_image) : 'https://media.getmyuni.com/yas/images/defaultcardbanner.png',
            'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'] ?? null,
            'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
        ],
        ['class' => 'primaryBtn applyNowButton getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition1 ? 'Check Eligibility' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Check Eligibility'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition1 ? UserService::parseDynamicCta('exam_{slug}_wap_top_right_banner_cta', '', $pageName) : $dynamicCta['cta_position_1']['wap']) : ($defaultEmptyCondition1 ? UserService::parseDynamicCta('exam_{slug}_web_top_right_banner_cta', '', $pageName) : ($dynamicCta['cta_position_1']['web'])),
            'ctaText' => $defaultEmptyCondition1 ? 'Check Eligibility' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Check Eligibility'),
            'ctaTitle' => $defaultEmptyCondition1 ? '' : (!empty($dynamicCta['cta_position_1']['cta_title']) ? $dynamicCta['cta_position_1']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition1 ? 'REGISTER TO GET EXAM ALERTS' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS'),
            'subheadingtext' => $defaultEmptyCondition1 ? $name : ($dynamicCta['cta_position_1']['lead_form_description'] ?? $name),
            'image' => !empty($image) ? Url::toExamImage($image) : '/yas/images/defaultcardbanner.png',
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition1 ? null : $dynamicCta['cta_position_1']['page_link']) : null,
            'alternateMedia' => $defaultEmptyCondition1 ? '' : $dynamicCta['cta_position_1']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition1 ? '' : $dynamicCta['cta_position_1']['page_redirect_slug']
        ],
        ['class' => 'primaryBtn examLeadValue js-open-lead-form-new', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($isMobile && isset($lead_cta) && $lead_cta == 1): ?>
    <div class="setAlarmDiv mobileOnly brochureBtn">
        <!-- <button href="javascript:;" class="primaryBtn setExamAlert getLeadForm"><i class="spriteIcon alarmPstudentIcon"></i>GET EXAM ALERTS</button> -->
        <?= frontend\helpers\Html::leadButton(
            '<i class="spriteIcon alarmIcon"></i>' . $defaultEmptyCondition0 ?  $ctaExamAlertText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaExamAlertText),
            [
                'entity' => $entity,
                'entityId' => $entity_id,
                'ctaLocation' => $defaultEmptyCondition0 ? UserService::parseDynamicCta('exam_{slug}_wap_top_left_banner_cta1', '', $pageName) : $dynamicCta['cta_position_0']['wap'],
                'ctaText' => $defaultEmptyCondition0 ? $ctaExamAlertText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaExamAlertText),
                'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
                'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO GET EXAM ALERTS' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS'),
                'subheadingtext' => $defaultEmptyCondition0 ? $name : ($dynamicCta['cta_position_0']['lead_form_description'] ?? $name),
                'image' => !empty($image) ? Url::toExamImage($image) : '/yas/images/defaultcardbanner.png',
                'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'] ?? null,
                'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
                'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
            ],
            ['class' => 'primaryBtn setExamAlert getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
        )
        ?>
    </div>
<?php endif; ?>

<?php if (isset($lead_cta) && $lead_cta == 2): ?>
    <div class="getSupport">
        <!--div class="row">
            <img src="<?= '/yas/images/get-support.png' ?>" width="80" height="80" alt="">
            <p>Get Exam Alerts and Guidance</p>
        </div-->
        <!-- <p class="getSupport__subheading">Get Exam Alerts and Guidance</p> -->
        <div class="button__row__container">
            <?= frontend\helpers\Html::leadButton(
                $defaultEmptyCondition2 ? 'Talk To Experts' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'Talk To Experts'),
                [
                    'entity' => $entity,
                    'entityId' => $entity_id,
                    'ctaLocation' => $isMobile ? ($defaultEmptyCondition2 ? UserService::parseDynamicCta('exam_{slug}_wap_bottom_left_cta', '', $pageName ?? '') : $dynamicCta['cta_position_2']['wap']) : ($defaultEmptyCondition2 ? UserService::parseDynamicCta('exam_{slug}_web_bottom_left_cta', '', $pageName ?? '') : ($dynamicCta['cta_position_2']['web'] ?? null)),
                    'ctaText' => $defaultEmptyCondition2 ? 'Talk To Experts' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'Talk To Experts'),
                    'ctaTitle' => $defaultEmptyCondition2 ? '' : (!empty($dynamicCta['cta_position_2']['cta_title']) ? $dynamicCta['cta_position_2']['cta_title'] : null),
                    'leadformtitle' => $defaultEmptyCondition2 ? 'REGISTER TO GET EXAM ALERTS' : ($dynamicCta['cta_position_2']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS'),
                    'subheadingtext' => $defaultEmptyCondition2 ? $name : ($dynamicCta['cta_position_2']['lead_form_description'] ?? $name),
                    'image' => !empty($image) ? Url::toExamImage($image) : '/yas/images/defaultcardbanner.png',
                    'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition2 ? null : $dynamicCta['cta_position_2']['page_link']) : null,
                    'alternateMedia' => $defaultEmptyCondition2 ? '' : $dynamicCta['cta_position_2']['media'],
                    'alternatePageRedirectSlug' => $defaultEmptyCondition2 ? '' : $dynamicCta['cta_position_2']['page_redirect_slug']
                ],
                ['class' => 'freeScholarship getLeadForm examLeadValue js-open-lead-form-new', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
            ) ?>
            <?= frontend\helpers\Html::leadButton(
                $defaultEmptyCondition3 ? 'Download' : ($dynamicCta['cta_position_3']['cta_text'] ?? 'Download'),
                [
                    'entity' => $entity,
                    'entityId' => $entity_id,
                    'ctaLocation' => $isMobile ? ($defaultEmptyCondition3 ? UserService::parseDynamicCta('exam_{slug}_wap_bottom_right_cta', '', $pageName ?? '') : $dynamicCta['cta_position_3']['wap']) : ($defaultEmptyCondition3 ? UserService::parseDynamicCta('exam_{slug}_web_bottom_right_cta', '', $pageName ?? '') : $dynamicCta['cta_position_3']['web'] ?? null),
                    'ctaText' => $dynamicCta['cta_position_3']['cta_text'] ?? 'Download',
                    'ctaTitle' => $defaultEmptyCondition3 ? '' : (!empty($dynamicCta['cta_position_3']['cta_title']) ? $dynamicCta['cta_position_3']['cta_title'] : null),
                    'leadformtitle' => $defaultEmptyCondition3 ? 'REGISTER TO DOWNLOAD SAMPLE PAPER' : ($dynamicCta['cta_position_3']['lead_form_title'] ?? 'REGISTER TO DOWNLOAD SAMPLE PAPER'),
                    'subheadingtext' => $defaultEmptyCondition3 ? $name ?? '' : ($dynamicCta['cta_position_3']['lead_form_description'] ?? $name ?? ''),
                    'image' => !empty($image) ? Url::toExamImage($image) : '/yas/images/defaultcardbanner.png',
                    'durl' => !empty($pdf->uploaded_file_name) ? 'https://d13mk4zmvuctmz.cloudfront.net/assets/pdf/' . $pdf->uploaded_file_name : '',
                    'alternateMedia' => $defaultEmptyCondition3 ? '' : $dynamicCta['cta_position_3']['media'],
                    'alternatePageRedirectSlug' => $defaultEmptyCondition3 ? '' : $dynamicCta['cta_position_3']['page_redirect_slug']
                ],
                ['class' => 'primaryBtn getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
            ) ?>
        </div>
    </div>
<?php endif; ?>

<?php if (isset($lead_cta) && $lead_cta == 3): ?>
    <?= frontend\helpers\Html::leadButton(
        empty($dynamicCta) && empty($dynamicCta['cta_position_4']) || empty(array_filter($dynamicCta['cta_position_4'])) ?  'Apply Now <span class="spriteIcon applyRedIcon"></span>' : ($dynamicCta['cta_position_4']['cta_text']  ?? $ctaExamAlertText),
        [
            'entity' => Lead::ENTITY_EXAM,
            'entityId' => $entity_id,
            'ctaLocation' => $defaultEmptyCondition4  ? $defaultEmptyCondition4 : ($isMobile ? ($defaultEmptyCondition4 ? UserService::parseDynamicCta('exam_{slug}_wap_top_banner_cta2', '', $pageName) : $dynamicCta['cta_position_4']['wap']) : ($defaultEmptyCondition2 ? UserService::parseDynamicCta('exam_{slug}__web_top_banner_cta2', '', $pageName) : $dynamicCta['cta_position_4']['web'])),
            //'ctaLocation' => empty($dynamicCta) && empty($dynamicCta['cta_position_4']) || empty(array_filter($dynamicCta['cta_position_4'])) ? UserService::parseDynamicCta('exam_{slug}_web_top_banner_cta2', '', $pageName) : $dynamicCta['cta_position_4']['web'],
            'ctaText' => empty($dynamicCta) && empty($dynamicCta['cta_position_4']) || empty(array_filter($dynamicCta['cta_position_4'])) ? $ctaExamAlertText    : ($dynamicCta['cta_position_4']['cta_text']   ?? $ctaExamAlertText),
            'ctaTitle' => $defaultEmptyCondition4 ? '' : (!empty($dynamicCta['cta_position_4']['cta_title']) ? $dynamicCta['cta_position_4']['cta_title'] : null),
            'leadformtitle' => empty($dynamicCta) && empty($dynamicCta['cta_position_4']) || empty(array_filter($dynamicCta['cta_position_4'])) ? 'REGISTER TO GET EXAM ALERTS' : ($dynamicCta['cta_position_4']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS'),
            'subheadingtext' => empty($dynamicCta) && empty($dynamicCta['cta_position_4']) || empty(array_filter($dynamicCta['cta_position_4'])) ? $exam->display_name : ($dynamicCta['cta_position_4']['lead_form_description'] ?? $exam->display_name),
            'image' => !empty($exam->cover_image) ? Url::toExamImage($exam->cover_image) : 'https://media.getmyuni.com/yas/images/defaultcardbanner.png',
            'redirection' => empty($dynamicCta) && empty($dynamicCta['cta_position_4']) || empty(array_filter($dynamicCta['cta_position_4'])) ? null : $dynamicCta['cta_position_4']['page_link'] ?? null,
            'alternateMedia' => $defaultEmptyCondition4 ? '' : $dynamicCta['cta_position_4']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition4 ? '' : $dynamicCta['cta_position_4']['page_redirect_slug']
        ],
        ['class' => 'primaryBtn applyNowButton getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if (isset($lead_cta) && $lead_cta == 4): ?>
    <?= frontend\helpers\Html::leadButton(
        '<i class="spriteIcon alarmIcon"></i>' . $defaultEmptyCondition0 ? $ctaExamAlertText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaExamAlertText),
        [
            'entity' => $entity,
            'entityId' => '',
            'ctaLocation' => $defaultEmptyCondition0 ? 'exam_landing_web_banner_cta1' : $dynamicCta['cta_position_0']['web'],
            'ctaText' => $defaultEmptyCondition0 ? $ctaExamAlertText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaExamAlertText),
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO GET EXAM ALERTS' : $dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS',
            'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'] ?? null,
            'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
        ],
        ['class' => 'desktopOnly primaryBtn setExamAlert getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>

<?php endif; ?>

<?php if (isset($lead_cta) && $lead_cta == 5 && $isMobile): ?>
    <div class="setAlarmDiv mobileOnly">
        <?= frontend\helpers\Html::leadButton(
            '<i class="spriteIcon alarmIcon"></i>' . $defaultEmptyCondition0 ? $ctaExamAlertText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaExamAlertText),
            [
                'entity' => $entity,
                'entityId' => '',
                'ctaLocation' => $defaultEmptyCondition0 ? 'exam_landing_wap_bottom_sticky_cta1' : $dynamicCta['cta_position_0']['wap'],
                'ctaText' => $defaultEmptyCondition0 ? $ctaExamAlertText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaExamAlertText),
                'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
                'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO GET EXAM ALERTS' : $dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS',
                'redirection' =>  $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'] ?? null,
                'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
                'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
            ],
            ['class' => 'primaryBtn setExamAlert getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
        ) ?>
    </div>
<?php endif; ?>

<?php if (isset($lead_cta) && $lead_cta == 6): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition0 ?  'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $defaultEmptyCondition0 ? UserService::parseDynamicCta('exam_listing_page_web_{slug}_card_cta1', '', $slug) : UserService::parseDynamicCta($dynamicCta['cta_position_0']['web'], '', $slug),
            'ctaText' => $defaultEmptyCondition0 ? 'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaExamAlertText),
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO GET EXAM ALERTS' : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['lead_form_title'], $name) ?? 'REGISTER TO GET EXAM ALERTS'),
            'subheadingtext' => $defaultEmptyCondition0 ? $name : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['lead_form_description'], $name) ?? $name),
            'image' => !empty($image) ? Url::toExamImage($image) : Url::toExamImage(),
            'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'] ?? null,
            'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
        ],
        ['class' => 'primaryBtn applyNow desktopOnly getLeadForm examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if (isset($lead_cta) && $lead_cta == 7): ?>
    <div class="mobileApplyNow mobileOnly">
        <?= frontend\helpers\Html::leadButton(
            $defaultEmptyCondition0 ?  'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now'),
            [
                'entity' => $entity,
                'entityId' => $entity_id,
                'ctaLocation' => $defaultEmptyCondition0 ? UserService::parseDynamicCta('exam_listing_page_wap_{slug}_card_cta1', '', $slug) : UserService::parseDynamicCta($dynamicCta['cta_position_0']['wap'], '', $slug),
                'ctaText' => $defaultEmptyCondition0 ? 'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaExamAlertText),
                'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
                'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO GET EXAM ALERTS' : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['lead_form_title'], $name) ?? 'REGISTER TO GET EXAM ALERTS'),
                'subheadingtext' => $defaultEmptyCondition0 ? $name : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['lead_form_description'], $name) ?? $name),
                'image' => !empty($image) ? Url::toExamImage($image) : Url::toExamImage(),
                'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'] ?? null,
                'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
                'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
            ],
            ['class' => 'primaryBtn btn-block applyNow loadLeadModelNew examLeadValue', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
        ) ?>
    </div>
<?php endif; ?>