<?php

use common\helpers\DataHelper;
use frontend\helpers\Url;

$currentUrl = $pageUrl;
$userCity = DataHelper::getUserLocation();
$student = \Yii::$app->user->identity;
if (isset($_SERVER['HTTP_REFERER'])) {
    $refererParts = parse_url($_SERVER['HTTP_REFERER']);
    if (isset($refererParts['query'])) {
        parse_str($refererParts['query'], $queryStringArray);
    }
}
?>
<form class="addInputFirstScreenClass" id="addInputFirstScreen">
    <div class="predict-college" id="firstDiv">
        <ul id="collegePredictorFormExam">
            <li><label>Rank <span>*</span> </label>
                <input type="text" id="college_predictor_rank" name="college_predictor_rank" class="college_predictor_rank" placeholder="Enter Your Rank" oninput="this.value = this.value.replace(/[^0-9]/g, '')" />
            </li>
            <li><label>Category <span>*</span></label>
                <!-- <input type="text" placeholder="Enter Marks" /> -->
                <select class="inputContainerField select2HookClass cut-off-category" name="cut-off-category" id="cut-off-category">
                    <option></option>
                </select>
            </li>
            <li>
                <button type="button" id="addInputFirstScreenSubmit" disabled>Predict My College</button>
            </li>
        </ul>

        <div class="field-complete">
            Complete all the required fields to achieve more accurate results.
        </div>
    </div>
</form>


<form class="addInputSecondScreenClass" id="addInputSecondScreen" style="display: none;">
    <div class="predict-college" id="secondDiv">
        <ul id="collegePredictorFormExam">
            <li><label>Name <span>*</span></label>
                <input type="text" name="name" id="leadform-name_college_predictor" value="<?= $student->name ?? '' ?>" placeholder="Name" />
            </li>
            <li><label>Email <span>*</span> </label>
                <input type="text" name="email" id="leadform-email_college_predictor" value="<?= $student->email ?? '' ?>" placeholder="Email" />
                <p class="error errorMsg errorMsgEmailCollegePredictor"></p>
            </li>
            <li><label>Phone Number <span>*</span> </label>
                <input type="text" name="phone" id="leadform-phone_college_predictor" value="<?= $student->phone ?? '' ?>" placeholder="Phone Number" maxlength="10" />
            </li>
            <li>
                <button type="button" id="addInputSecondScreenSubmit" disabled>Predict My College</button>
            </li>
        </ul>

        <div class="field-complete">
            Complete all the required fields to achieve more accurate results.
        </div>
    </div>
    <input type="hidden" name="current_city_ip" id="current_city_ip_college_predictor" value="<?= $userCity['cityId'] ?>">
    <input type="hidden" name="current_state_ip" id="current_state_ip_college_predictor" value="<?= $userCity['stateId'] ?>">
    <input type="hidden" id="leadform-url_college_predictor" name="url" value="<?= empty($currentUrl) ? '' : $currentUrl ?>">
    <input type="hidden" name="source" value="<?= empty($_GET['source']) ? '' : DataHelper::$leadSource['organic'] ?>">
    <input type="hidden" id="leadform-utm_source_college_predictor" name="utm_source" value="<?= empty($queryStringArray) || empty($queryStringArray['utm_source']) ? '' : $queryStringArray['utm_source'] ?>">
    <input type="hidden" id="leadform-utm_medium_college_predictor" name="utm_medium" value="<?= empty($queryStringArray) || empty($queryStringArray['utm_medium']) ? '' : $queryStringArray['utm_medium'] ?>">
    <input type="hidden" id="leadform-utm_campaign_college_predictor" name="utm_campaign" value="<?= empty($queryStringArray) || empty($queryStringArray['utm_campaign']) ? '' : $queryStringArray['utm_campaign'] ?>">
    <input type="hidden" id="leadform-entity_college_predictor" name="entity" value=<?= $entity ?>>
    <input type="hidden" id="leadform-entity_id_college_predictor" name="entity_id" value=<?= $entityId ?>>
    <input type="hidden" name="platform" value="<?= \Yii::$app->devicedetect->isMobile() ? 'wap' : 'web' ?>">
    <input type="hidden" name="cta_location" id="cta_location_college_predictor" data-cta-location="college_predictor" value="college_predictor">
    <input type="hidden" name="cta_text" id="cta_text_college_predictor" value="College Predictor">
    <input type="hidden" name="cta_title" id="cta_title_college_predictor">
    <input type="hidden" id="college_predictor_rank_value" value="">
    <input type="hidden" id="college_predictor_rank_category" value="">
    <input type="hidden" id="entity_subtype_college_predictor" name="entity_subtype" value="<?= $entity_subtype ?>">
    <input type="hidden" id="entity_type_college_predictor" name="entity_type" value="<?= $entity_type ?>">
    <input type="hidden" id="page_name_college_predictor" name="page_name" value="<?= $pageName ?>">
    <input type="hidden" id="stream_id_college_predictor" name="inputStream" value="<?= $stream_id ?>">
    <input type="hidden" id="level_college_predictor" name="inputLevel" value="<?= $level ?>">
    <input type="hidden" id="course_id" name="course_id" value="<?= $course_id ?>">
</form>

<?php /*
<!-----engineering-exams------->
<?php if (!empty($getCollegePredictorExams)): ?>
    <div class="engineering-exams">
        <h2>Other Engineering Exams Predictors</h2>
        <div class="other-exams">
            <ul>
                <?php foreach ($getCollegePredictorExams as $key => $examValues): ?>
                    <?php if ($examValues['id'] == $exam->id) {
                        continue;
                    } ?>
                    <li>
                        <div class="college-logo">
                            <img src="<?= !empty($examValues['cover_image']) ? Url::toExamImage($examValues['cover_image']) : Url::defaultCollegeLogo(); ?>">
                        </div>
                        <span><?= $examValues['display_name'] ?? $examValues['name'] ?> College Predictor</span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        <div class="view-predicators">
            <a href="/predictor/college-predictor">View Predictors</a>
        </div>
    </div>
<?php endif; ?>
*/ ?>
<!-----engineering-exams------->

<!-- College Predictor Results Container -->

<div id="college-predictor-results-container" style="display: none;">
    <!-- Results will be loaded here via AJAX -->
</div>

<!-----share on whatsup------>
<div class="share-whatsup">
    <div class="share-flex">
        <h3>Help your friends by sharing this FREE tool in your friend and coaching groups</h3>
        <div class="share-btn">
            <button class="whatsup" onclick="shareOnWhatsApp()"><span class="spriteIcon whatsupicon"></span>Share on WhatsApp</button>
            <button class="copy-link" onclick="copyLink()"><span class="spriteIcon attachIcon"></span>Copy Link</button>
        </div>
    </div>
</div>

<!-----share on whatsup------>