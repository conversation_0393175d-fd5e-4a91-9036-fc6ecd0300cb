<?php

use Carbon\Carbon;
use common\helpers\ContentHelper;
use yii\helpers\StringHelper;

$now = Carbon::now();

// Define event slugs and labels
$eventSlugs = [
  'registration-date' => 'Registration',
  'admit-card' => 'Admit Card',
  'exam-start' => 'Exam Day',
  'final-answer-key-date' => 'Answer Key Release',
  'result-date' => 'Result',
];

// Initialize variables
$eventItems = [];
$dateItems = [];
$i = 1;
$showUpcoming = true;
$activeLabel = '';
$hasAnyDate = false;
$textDefault = 'To be announced';

// Get exam day event and set ribbon text
$examDay = ContentHelper::getEventBySlug($dates, 'exam-start');
$ribbonText = 'To be Updated Soon'; // Default value

if ($examDay && $examDay->start) {
    $examStart = Carbon::parse($examDay->start);
    $examEnd = $examDay->end ? Carbon::parse($examDay->end) : '';

    if ($now->lessThan($examStart)) {
        $diffDays = $now->diffInDays($examStart);
        $ribbonText = $diffDays > 0 ? $diffDays . ' Days Left' : 'Few Hours Left';
    } elseif ($now->between($examStart, $examEnd)) {
        $ribbonText = 'Exam Phase';
    } else {
        $ribbonText = 'Exam Completed';
    }
}

// Get the event dates from $dates
$registrationEvent = ContentHelper::getEventBySlug($dates, 'registration-date');
$admitCardEvent = ContentHelper::getEventBySlug($dates, 'admit-card');
$answerKeyEvent = ContentHelper::getEventBySlug($dates, 'final-answer-key-date');
$resultsOutEvent = ContentHelper::getEventBySlug($dates, 'result-date');

// Check if any event is currently ongoing
$ongoingEventFound = false;
$ongoingEventIndex = -1;
$lastCompletedEventIndex = -1;
$orderedEvents = [
  'registration-date',
  'admit-card',
  'exam-start',
  'final-answer-key-date',
  'result-date'
];

// Process events
foreach ($orderedEvents as $index => $eventSlug) {
    $event = ContentHelper::getEventBySlug($dates, $eventSlug);
    if (!$event) {
        continue;
    }

    $start = isset($event->start) ? Carbon::parse($event->start) : null;
    $end = isset($event->end) ? Carbon::parse($event->end) : ($start ? $start->copy()->endOfDay() : null);

  // Check if this event is ongoing
    if ($start && $end && $now->between($start, $end)) {
        $ongoingEventFound = true;
        $ongoingEventIndex = $index;
        break; // Found an ongoing event, no need to continue
    }

  // If not ongoing, check if it's completed
    if (($start && $now->gt($start) && (!$end || $now->gt($end))) ||
    ($end && $now->gt($end))
    ) {
        $lastCompletedEventIndex = $index;
    }
}

// If no ongoing event was found but we have a completed event
// that's not the last one, mark the next event as the priority event
if (!$ongoingEventFound && $lastCompletedEventIndex >= 0 &&
  $lastCompletedEventIndex < count($orderedEvents) - 1
) {
    $ongoingEventFound = true;
    $ongoingEventIndex = $lastCompletedEventIndex + 1;
}


// Check if result is completed
$resultStatus = false;
$resultDetails = ContentHelper::getEventStatus(($resultsOutEvent->start ?? null), ($resultsOutEvent->end ?? null), $now, $resultStatus, $hasAnyDate);
$resultCompleted = isset($resultDetails['status']) && $resultDetails['status'] === 'Completed';

// Check if exam is completed
$examStatus = false;
$examDetails = ContentHelper::getEventStatus(($examDay->start ?? null), ($examDay->end ?? null), $now, $examStatus, $hasAnyDate);
$examCompleted = isset($examDetails['status']) && $examDetails['status'] === 'Completed';

$hasPrimaryStatus = false;
$examReset = false;
$resultCompletedDate = !empty($resultsOutEvent) && !empty($resultsOutEvent->end) ? ($resultsOutEvent->end ? Carbon::parse($resultsOutEvent->end) : '') : '';

if ($resultCompleted && $resultCompletedDate && $resultCompletedDate->copy()->addDays(90)->lt($now)) {
    $examReset = true;
    $ribbonText = 'To be Updated Soon';
}

foreach ($eventSlugs as $slug => $label) {
    $event = ContentHelper::getEventBySlug($dates, $slug);
    $start = $event->start ?? null;
    $end = $event->end ?? null;

  // Get current event index in the ordered sequence
    $currentEventIndex = array_search($slug, $orderedEvents);

  // Parse dates once
    $startDate = $start ? Carbon::parse($start) : null;
    $endDate = $end ? Carbon::parse($end) : null;

  // Default details structure - initialize with empty values
    $details = [
    'status' => '',
    'startDate' => $startDate,
    'endDate' => $endDate,
    'useSimpleFormat' => false,
    'iconClass' => '',
    'liClass' => '',
    'statusHTML' => '',
    ];

  // Determine event status based on conditions
    if ($examReset) {
      // All events should go to "default"
        if (!$hasPrimaryStatus && ($start || $end || true)) {
            $details['status'] = 'Coming Soon';
            $details['liClass'] = 'coming-soon-text';
            $details['statusHTML'] = '<span class="coming-soon" style="display: flex;">Coming Soon</span>';
            $hasPrimaryStatus = true;
            $activeLabel = $label;
        }
    } elseif ($resultCompleted && $slug !== 'result-date') {
      // Force earlier events to Completed if result is already completed
        $details['status'] = 'Completed';
        $details['iconClass'] = 'tickicon';
        $details['liClass'] = 'coming-step';
    } elseif ($examCompleted && empty($registrationEvent) && empty($admitCardEvent) && empty($answerKeyEvent) && empty($resultsOutEvent)) {
      // Handle completed events if only exam date is present
        if (in_array($slug, ['exam-start', 'registration-date', 'admit-card'])) {
            $details['status'] = 'Completed';
            $details['iconClass'] = 'tickicon';
            $details['liClass'] = 'coming-step';
        } elseif ($slug == 'final-answer-key-date') {
            $details['status'] = 'Coming Soon';
            $details['liClass'] = 'coming-soon-text';
            $details['statusHTML'] = '<span class="coming-soon" style="display: flex;">Coming Soon</span>';
            $hasPrimaryStatus = true;
            $activeLabel = $label;
        }
    } else if ($ongoingEventFound) {
        if ($currentEventIndex === $ongoingEventIndex) {
          // This is the priority event after a completed event
          // Check if this event has dates and is currently happening
            $eventHasDates = $startDate && $endDate;
            $isCurrentlyHappening = $eventHasDates && $now->between($startDate, $endDate);

          // Set status based on whether the event is currently happening
            $status = $isCurrentlyHappening ? 'Ongoing' : 'Coming Soon';

            $details['status'] = $status;
            $details['liClass'] = 'coming-soon-text';
            $details['statusHTML'] = '<span class="coming-soon" style="display: flex;">' . $status . '</span>';
            $details['useSimpleFormat'] = true;

            if (!$hasPrimaryStatus) {
                $hasPrimaryStatus = true;
                $activeLabel = $label;
            }
        } elseif ($currentEventIndex < $ongoingEventIndex) {
          // This event is before the ongoing event, mark it as completed
            $details['status'] = 'Completed';
            $details['iconClass'] = 'tickicon';
            $details['liClass'] = 'coming-step';
        }
    } else {
      // Default status logic
        $rawDetails = ContentHelper::getEventStatus($start, $end, $now, $showUpcoming, $hasAnyDate);

        if ($hasPrimaryStatus && in_array($rawDetails['status'], ['Ongoing', 'Coming Soon', 'Completed'])) {
          // Clear status if we already have a primary status
            $rawDetails['status'] = '';
            $rawDetails['statusHTML'] = '';
            $rawDetails['iconClass'] = '';
            $rawDetails['liClass'] = '';
        }

      // Merge with default details
        foreach ($rawDetails as $key => $value) {
            $details[$key] = $value;
        }
    }

    extract($details);

  // First event with dynamic status gets the active label
    if (!$hasPrimaryStatus && in_array($status, ['Ongoing', 'Coming Soon'])) {
        $activeLabel = $label;
        $hasPrimaryStatus = true;
    }

    $text = ($startDate && !$examReset) ? "$label - " . ContentHelper::formatDateWithSuffix($startDate) . ($endDate ? ' - ' . ContentHelper::formatDateWithSuffix($endDate) : '') : "$label - $textDefault";

    $dateItems[] = ['text' => $text, 'label' => $label];

    $iconSpan = $iconClass ? "<span class=\"{$iconClass}\"></span>" : '';

    $stepCircleHTML = '';
    if (!$statusHTML) {
        $stepCircleHTML = "<span class=\"step-circle\"><span class=\"number\">{$i}</span>{$iconSpan}</span>";
    }

    $eventItems[] = "<li class=\"{$liClass}\">{$stepCircleHTML}<span class=\"step-name\">{$label}</span>{$statusHTML}</li>";

    $i++;
}
//CTA LOGIC
$ctaHtml = ContentHelper::renderEventsHtml($registrationEvent, $admitCardEvent, $examDay, $answerKeyEvent, $resultsOutEvent, 'exam', $exam->id, $exam->slug, $exam->display_name);

if (empty($ctaHtml)) {
    $ctaHtml = ContentHelper::renderCtaBlock('default', [
    'left' => 'Get Expert Help',
    'right' => 'Exam Dates {download}',
    ], $exam->slug, 'exam', $exam->id, $exam->display_name);
}
?>

<section class="exam-glance-wrap">
  <div class="exam_glance-inner">
    <div class="exam-glance-top">
      <div class="exam-glance-top-left">
        <div class="glance-days"><?= $ribbonText ?></div>
        <div class="glance-name">for <span><?= StringHelper::truncate($exam->display_name, 15, '....') ?></span></div>
      </div>
    </div>
    <div class="exam-glance-steps">
      <ul>
        <?= implode('', $eventItems) ?>
      </ul>
    </div>
    <div class="glance-options-container">
      <div class="glance-options-inner" id="myDiv">
        <ul>
          <?php
          // Build date items list more efficiently
            $dateItemsHtml = '';
            foreach ($dateItems as $text) {
                $activeClass = $text['label'] === $activeLabel ? 'active' : '';
                $dateItemsHtml .= "<li data-event-type=\"{$text['label']}\" class=\"{$activeClass}\">{$text['text']}</li>";
            }
            echo $dateItemsHtml;
            ?>
        </ul>
      </div>
      <?= $ctaHtml ?>
    </div>
  </div>
</section>