<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\Exam;
use frontend\helpers\Url;
use yii\helpers\Html;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\widgets\CustomSortLink;
use yii\helpers\Inflector;
use yii\widgets\LinkPager;
use yii\widgets\ListView;

$this->title = ($filterPageInfo !== false) ? (!empty($filterPageInfo->title) ? DataHelper::parseMetaTopContent($filterPageInfo->title) : ucwords(str_replace('-', ' ', $searchModel->stream)) . ' Exams in India') : ucwords(str_replace('-', ' ', $searchModel->stream)) . ' Exams in India';
$this->title .= Yii::$app->request->get('page') > 1 ? ' - Page ' . Yii::$app->request->get('page') : '';
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Exams'), 'url' => [Url::toExam()], 'title' => 'Entrance Exams in India'];

if (Yii::$app->request->get('page') > 1) {
    $this->params['breadcrumbs'][] = [
        'label' => Yii::t('app', ucwords(str_replace('-', ' ', $searchModel->stream))) . ' ' . Yii::t('app', 'Exams in India') ?? '',
        'url' => Url::canonical(),
        'title' => ucwords(str_replace('-', ' ', $searchModel->stream)) . ' Exams in India' ?? ''
    ];
    $this->params['breadcrumbs'][] = 'Page ' . Yii::$app->request->get('page');
} else {
    $this->params['breadcrumbs'][] =  Yii::t('app', ucwords(str_replace('-', ' ', $searchModel->stream))) . ' ' . Yii::t('app', 'Exams in India') ?? '';
}

$isMobile = Yii::$app->devicedetect->isMobile();

// page meta info
$this->context->description = ($filterPageInfo !== false) ? (!empty($filterPageInfo->description) ? DataHelper::parseMetaTopContent($filterPageInfo->description) : ucwords(str_replace('-', ' ', $searchModel->stream)) . ' Exams in India') : ucwords(str_replace('-', ' ', $searchModel->stream)) . ' Exams in India';

$dataProvider->prepare();
if (isset($dataProvider->pagination->getLinks()['prev'])) {
    $this->registerLinkTag(['rel' => 'prev', 'href' => Url::base(true) . $dataProvider->pagination->getLinks()['prev']]);
}
if (isset($dataProvider->pagination->getLinks()['next'])) {
    $this->registerLinkTag(['rel' => 'next', 'href' => Url::base(true) . $dataProvider->pagination->getLinks()['next']]);
}

$this->registerCssFile(Yii::$app->params['cssPath'] . 'exam_list.css', ['depends' => [AppAsset::class]]);

// page specific assets
$this->params['entity'] = Exam::ENTITY_EXAM;
$this->params['entity_id'] = $stream->id ?? 0;
$this->params['entity_name'] = '';
$this->params['entitySlug'] = '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = '';
$this->params['pageName'] = 'exam-category' ?? '';
?>
<div class="">
    <div class="row">
        <div class="col-md-3">

            <?php if (!$isMobile): ?>
                <?= $this->render('_search-form', ['model' => $searchModel]) ?>
            <?php endif ?>

        </div>

        <?php if ($isMobile): ?>
            <?= $this->render('_search-form-mobile', ['model' => $searchModel]) ?>
        <?php endif; ?>

        <div class="col-md-9">
            <div class="pageDescription pageInfo">
                <h1>
                    <?= $filterPageInfo !== false ? (!empty($filterPageInfo->h1) ? DataHelper::parseMetaTopContent($filterPageInfo->h1) : ucwords(str_replace('-', ' ', $searchModel->stream)) . ' Exams in India') : ucwords(str_replace('-', ' ', $searchModel->stream)) . ' Exams in India' ?>
                    <?= Yii::$app->request->get('page') > 1 ? ' - Page ' . Yii::$app->request->get('page') : '' ?>
                </h1>
                <?php if ($filterPageInfo !== false): ?>
                    <?php if (Yii::$app->request->get('page') <= 1): ?>
                        <?php if (!empty($filterPageInfo->top_content)): ?>
                            <?= ContentHelper::htmlDecode(stripslashes(
                                DataHelper::parseMetaTopContent(DataHelper::parseDomainUrlInContent($filterPageInfo->top_content)) ?? ''
                            ), false) ?>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                <aside class="horizontalRectangle">
                    <div class="appendAdDiv" style=" <?= $isMobile ? 'height: 250px' : '' ?>">
                        <?php /* if ($isMobile) : ?>
                            <?php echo Ad::unit('GMU_EXAMS_LISTING_PAGE_WAP_300x250_MTF_1', '[300,250]') ?>
                        <?php else : */ ?>
                        <?php //echo Ad::unit('GMU_EXAMS_LISTING_PAGE_WEB_728x90_MTF_1', '[728,90]')
                        ?>
                        <?php echo Ad::unit('GMU_EXAMS_LISTING_PAGE_WEB_728x90_MTF_1', '[728,90]') ?>
                        <?php //endif;
                        ?>
                    </div>
                </aside>
            <?php endif; ?>

            <!-- <div class="getFreeScholorship mobileOnly">
                <a href="javascript:;" class="primaryBtn">GET FREE SCHOLARSHIP</a>
            </div> -->
            <div class="filter-container">
                <div class="sortBySection">

                    <div class="row">
                        <div class="selectedFilters col-md-9">
                            <?php foreach ($searchModel->selectedFilters as $filters): ?>
                                <?php foreach ($filters as $filter): ?>
                                    <button data-slug="<?= $filter ?>"><?= Inflector::humanize($filter) ?><i class="spriteIcon closeIcon remove-filter"></i></button>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        </div>

                        <div class="col-md-3">
                            <div class="sortByOption">
                                <?php
                                echo ListView::widget([
                                    'dataProvider' => $dataProvider,
                                    'layout' => '{sorter}',
                                    'sorter' => [
                                        'class' => CustomSortLink::class,
                                    ],
                                ]);
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <?php

                echo ListView::widget([
                    'dataProvider' => $dataProvider,
                    'itemView' => '_exam-list',
                    'viewParams' => [
                        'fullView' => true,
                        'context' => 'main-page',
                        'isMobile' => $isMobile,
                        'dynamicCta' => $dynamicCta
                    ],
                    'layout' => "{summary}\n{items}\n{pager}",
                    'pager' => [
                        'prevPageCssClass' => '',
                        'maxButtonCount' => 5,
                    ]
                ]);
                ?>
            </div>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <aside class="horizontalRectangle">
                    <div class="appendAdDiv" style=" <?= $isMobile ? 'height: 250px' : '' ?>">
                        <?php if ($isMobile): ?>
                            <?php //echo Ad::unit('GMU_EXAMS_LISTING_PAGE_WAP_300x250_BTF', '[300,250]')
                            ?>
                            <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                            ?>


                        <?php else: ?>
                            <?php //echo Ad::unit('GMU_EXAMS_LISTING_PAGE_WEB_728x90_BTF', '[728,90]')
                            ?>
                            <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90')
                            ?>

                        <?php endif; ?>
                    </div>
                </aside>
            <?php endif; ?>
        </div>
    </div>
</div>