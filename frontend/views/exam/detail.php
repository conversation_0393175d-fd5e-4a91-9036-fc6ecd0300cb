<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\Exam;
use frontend\helpers\Url;
use yii\helpers\ArrayHelper;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use common\services\ExamService;
use common\services\CollegeService;
use frontend\helpers\Freestartads;

// utils
$collegeService = new CollegeService();

$currentUrl = Url::base(true) . Url::current();
$canonicalUrl = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();
$isMobile = \Yii::$app->devicedetect->isMobile();
$menus = array_column(ArrayHelper::toArray($pages), 'slug');
$menuFlip = array_flip($menus) ?? [];
$interestedInPages = ['overview', 'important-dates', 'syllabus', 'previous-years-papers', 'exam-pattern'];
$interestedInSubPages = ['important-dates', 'syllabus', 'application-form-details', 'cut-off', $pageName];
$upcomingDisciplinePages = ['application-form-details', 'admit-card', 'sample-test-paper-content', 'results'];
$popularExamsPages = ['syllabus', 'application-form-details', 'admit-card', 'results'];
if (empty($content->user)) {
    $user = $content->defaultuser;
} else {
    $user = !empty($updatebyAuth) ? $updatebyAuth : $content->user;
}
$menuOrderLink = $menuOrder ? $menuOrder : DataHelper::examContentList();

$authorImage = ContentHelper::getUserProfilePic($user->slug ?? '');
$coursesOfferedByExam = ExamService::getExamCourse($exam->id);
if (!empty($streams[0])) {
    $stateListbyCourse = $collegeService->getStateListByCourse($streams[0], '', 'stream');
    $cityListbyCourse = $collegeService->getCityListByCourse($streams[0], '', 'stream');
}

// title
if (empty($type)) {
    $defaultSeoInfo = ContentHelper::getExamDefaultSeoInfo(trim(preg_replace('/[0-9]/', '', $exam->display_name)), $pageName, $dates, $content->localize_year ?? ContentHelper::EXAM_YEAR, null, $parentPage ?? '');
} else {
    $defaultSeoInfo = ContentHelper::getExamDefaultSeoInfo(trim(preg_replace('/[0-9]/', '', $exam->display_name)), $pageName, $dates, $content->localize_year ?? ContentHelper::EXAM_YEAR, $pageName, $parentPage ?? '');
}

$this->title = empty($pageSeoInfo->title) ? (ContentHelper::parseExamContent($defaultSeoInfo['title'] ?? '', $dates)) : ContentHelper::parseExamContent($pageSeoInfo->title, $dates, $content->localize_year ?? ContentHelper::EXAM_YEAR);
$this->context->description = empty($pageSeoInfo->description) ? ContentHelper::parseExamContent($defaultSeoInfo['description'] ?? '', $dates, $content->localize_year ?? ContentHelper::EXAM_YEAR) : ContentHelper::parseExamContent($pageSeoInfo->description, $dates, $content->localize_year ?? ContentHelper::EXAM_YEAR);
$this->context->ogImage = !empty($exam->cover_image) ? Yii::getAlias('@getmyuniExamAsset/') . $exam->cover_image : '';

$displayName = $exam->display_name;
if (empty($displayName)) {
    $displayNameWords = explode('-', $exam->slug);

    $displayName = ucfirst($displayNameWords[0] . ' ' . (!empty($displayNameWords[1]) ? $displayNameWords[1] : ''));
}

// breadcrumbs
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
if ($pageName == 'college-predictor') {
    //     $this->params['breadcrumbs'][] = ['label' => 'Predictor', 'url' => ['predictor/college-predictor'], 'title' => 'College Predictor'];
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'college_predictor.css', ['depends' => [AppAsset::class]]);
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'college_predictor_filter.css', ['depends' => [AppAsset::class]]);
}

$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Exams'), 'url' => [Url::toExam()], 'title' => 'Entrance Exams in India'];
$breadCrumbsPageName = ['overview'];

if (!in_array($pageName, $breadCrumbsPageName)) {
    $this->params['breadcrumbs'][] = ['label' => Yii::t('app', $displayName), 'url' => [Url::toExam() . '/' . $exam->slug], 'title' => $displayName];
}

if (in_array($pageName, DataHelper::$examSeoSubPages) || !empty($type)) {
    foreach ($dropdowns as $key => $value) {
        if ($key != 'Syllabus') {
            foreach ($value as $dropdownValue) {
                if ($dropdownValue['slug'] == $pageName) {
                    $this->params['breadcrumbs'][] = Yii::t('app', $dropdownValue['name']);
                    break;
                }
            }
        } else {
            foreach ($value as $dropdownValue) {
                if ($dropdownValue['slug'] == $pageName) {
                    $this->params['breadcrumbs'][] = Yii::t('app', $dropdownValue['name']) . Yii::t('app', 'Syllabus');
                    break;
                }
            }
        }
    }
} elseif (isset($parentPage) && !empty($parentPage)) {
    $this->params['breadcrumbs'][] = (Yii::t('app', $content->name) ?? '') . ' ' . ucfirst(str_replace('-', ' ', $parentPage));
} else {
    $this->params['breadcrumbs'][] = $pageName == 'overview' ? Yii::t('app', $displayName) : (Yii::t('app', $content->name) ?? '');
}

$this->registerCssFile(Yii::$app->params['cssPath'] . 'exam_detail.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');
if ($pageName == 'rank-predictor' || $pageName == 'percentile-predictor') {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'rank-predictor.css', ['depends' => [AppAsset::class]]);
}

$this->registerCssFile(Yii::$app->params['cssPath'] . 'exam_download_resource.css', ['depends' => [AppAsset::class]]);

$autoPopUpTexts = array_filter(array_column($dynamicCta, 'auto_pop_up_text'));
$finalAutoPopUpText = !empty($autoPopUpTexts) ? array_values($autoPopUpTexts)[0] : '';
$autoPopUpTitle = array_filter(array_column($dynamicCta, 'auto_pop_up_title'));
$finalAutoPopUpTitle = !empty($autoPopUpTexts) ? array_values($autoPopUpTitle)[0] : '';

// gmu params

$this->params['entity'] = Exam::ENTITY_EXAM;
$this->params['entity_id'] = $exam->id ?? 0;
$this->params['entity_name'] = $exam->name ?? '';
$this->params['entitySlug'] = $exam->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $exam->display_name ?? '';
$this->params['canonicalUrl'] = $canonicalUrl;
$this->params['auto_pop_up_text'] = $finalAutoPopUpText;
$this->params['auto_popup_form_title'] = $finalAutoPopUpTitle;
$this->params['entity_subpage_name'] = $content->name ?? '';

$this->params['streamWebengage'] =  $exam->primary_stream_id ? $exam->stream->name : null;
$this->params['levelWebengage'] =  $exam->derived_lead_level ?  $exam->degree->name : null;
if (!empty($exam->name)) {
    $this->params['examWebengage'] = $exam->name;
} else {
    $this->params['examWebengage'] = null;
}
if (empty($type)) {
    $this->params['pageName'] = strtolower(str_replace('-', '_', $pageName)) ?? '';
} else {
    $this->params['pageName'] = $pageName ?? '';
}

$loadFaq = [];

// schema
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}

if (!empty($exam)) {
    $this->params['schema'] = \yii\helpers\Json::encode([[
        '@context' => 'http://schema.org',
        '@type' => 'Article',
        'mainEntityOfPage' => [
            '@type' => 'WebPage',
            '@id' => $canonicalUrl,
        ],
        'headline' => $this->title ?? '',
        'image' => !empty($exam->cover_image) ? [
            Url::toExamImage($exam->cover_image)
        ] : '',
        'datePublished' => !empty($content->published_at) ? date(DATE_ATOM, strtotime($content->published_at)) : date(DATE_ATOM, strtotime($content->created_at)),
        'dateModified' => !empty($content->updated_at) ? date(DATE_ATOM, strtotime($content->updated_at)) : '',
        'author' => [
            '@type' => 'Person',
            'name' => !empty($user) ? $user->name : ''
        ],
        'publisher' => [
            '@type' => 'Organization',
            'name' => 'Getmyuni',
            'logo' => [
                '@type' => 'ImageObject',
                'url' => Yii::$app->params['gmuLogo']
            ],
        ],
        'description' => $this->context->description ?? '',
    ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
}

// exam dates
$examDates = DataHelper::getExamDateByPageWise($pageName, $dates);

$ctaExamAlertText = 'GET EXAMS ALERT';

//code for translation of exams
if (!empty($translation_data)) {
    if (!empty($translation_data[0]['lang'])) {
        $msg = 'Switch to Hindi';
    } else {
        $msg = 'Switch to English';
    }
    if ($translation_data[0]['page'] == 'overview') {
        $url = Url::toExamDetail($translation_data[0]['slug'], $translation_data[0]['lang']);
    } else {
        $url = Url::toExamDetail($translation_data[0]['slug'], $translation_data[0]['lang'], $translation_data[0]['page']);
    }

    if (!empty($translation_data[0]['lang'])) {
        $this->registerLinkTag(['href' =>  Url::base(true) . $url, 'rel' => 'alternate', 'hreflang' => $translation_data[0]['lang']]);
    } else {
        $translation_data[0]['lang'] = 'en';
        $this->registerLinkTag(['href' =>  Url::base(true) . $url, 'rel' => 'alternate', 'hreflang' => $translation_data[0]['lang']]);
    }
} else {
    $url = '#';
}

$downloadableResourceSection = '';
if (!empty($downloadableResource)) {
    $downloadableResourceSection = '<div class="exam-download-section" data-exam-id="' . $exam->id . '" data-stream-id="' . $exam->primary_stream_id . '" data-entity="exam"></div>';
}

$h2Text = ContentHelper::getGenerateHtml($pageContent, '', $downloadableResourceSection);
$pageContent = $h2Text['content'];
?>

<div class="">
    <!-- header -->
    <header class="commonHeroSection examHeroSection">
        <div class="row">
            <div class="col-md-12">
                <div class="row heroHeader">
                    <div class="imgContainer">
                        <img class="<?= !empty($exam->cover_image) ? '' : 'defaultLogoImage'; ?>" src="<?= !empty($exam->cover_image) ? Url::toExamImage($exam->cover_image) : Url::defaultCollegeLogo(); ?>">
                    </div>
                    <div class="headingContainer">
                        <h1> <?= empty($pageSeoInfo->h1) ? ContentHelper::parseExamContent($defaultSeoInfo['h1'] ?? '', $dates) : ContentHelper::parseExamContent($pageSeoInfo->h1, $dates, $content->localize_year ?? ContentHelper::EXAM_YEAR); ?></h1>
                    </div>
                </div>
            </div>
            <div class="col-md-12 second-row-content">
                <div class="row  col-md-7 p-0 row-margin-zero">
                    <?php if (!empty($examDates)):
                        $i = 0;
                        ?>
                        <!--div class="row helpfulInfo">
                            <?php /* foreach ($examDates as $examDate): ?>
                                <div class="helpfulItem">
                                    <span class="spriteIcon listIcon"></span>
                                    <span><?= $examDate['name']; ?></span>
                                    <span>
                                        <?php if ($i > 0):
                                            ?><?= $examDate['date'] ?? '' ?>
                                        <?php else: ?>
                                            <?= $examDate['date'] ?? '' ?>
                                            <?php
                                        endif; ?>
                                    </span>
                                    <?php
                                    $i++; ?>
                                </div>
                                <?php
                            endforeach; */
                            ?>
                        </div-->
                    <?php endif; ?>
                </div>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="col-md-5 cta-mobile authorInfoAndTranslateBtn">
                        <div class="col-md-6 cta-btn-row-fix">
                            <div class="ctaColumn">
                                <div class="ctaRow">
                                    <div class="lead-cta" data-entity="exam" data-lead_cta='0' data-sponsor="<?= $sponsorClientUrl ?>"></div>
                                    <!--button class="primaryBtn applyNowButton">Apply Now <span class="spriteIcon applyRedIcon"></span></button>
                            <button class="primaryBtn brochureButton">Brochure<span class="spriteIcon brochureButtonIcon"></span></button--->
                                </div>
                            </div>
                        </div>
                        <?php
                        if (!empty($translation_data)) {
                            ?>
                            <div class="col-md-6 trans-btn-row-fix">
                                <a href="<?= $url ?>" class="translateBtn">
                                    <span class="webpSpriteIcon translateIcon1"></span>
                                    <?= $msg ?>
                                </a>
                            </div>
                            <?php
                        }
                        ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <!-- exams menu -->
    <?php if (!empty($pages)): ?>
        <nav class="stickyNavCls">
            <div class="examRelataedLinks">
                <?php if (count($menus) > 14 && !$isMobile): ?>
                    <p class="btn_left over">
                        <i class="spriteIcon left_angle"></i>
                    </p>
                <?php endif; ?>
                <?php if (count($menus) > 12 && !$isMobile): ?>
                    <p class="btn_right">
                        <i class="spriteIcon right_angle"></i>
                    </p>
                <?php endif; ?>
                <?php if (count($menus) > 3 && $isMobile): ?>
                    <!-- <p class="btn_right">
                        <i class="spriteIcon right_angle"></i>
                    </p> -->
                <?php endif; ?>

                <ul>
                    <?php foreach ($menuOrderLink as $key => $value):
                        $dataAttribute = str_replace(' ', '-', $value);

                        ?>
                        <?php if (in_array($key, $menus)): ?>
                            <li class="subNavDropDown mobileSubNavDropDown">
                                <?php if ($key == $pageName || (!empty($type) && $key == $type)): ?>
                                    <a <?php
                                    if (array_key_exists($value, $dropdowns)) {
                                        foreach ($dropdowns[$value] as $subValue) {
                                            if ($subValue['slug'] == $pageName) {
                                                ?> href="<?= $key == 'overview' ? Url::toExamDetail($exam->slug, DataHelper::getLangCode($exam->lang_code)) : Url::toExamDetail($exam->slug, DataHelper::getLangCode($exam->lang_code), $key) ?>" <?php
                                            }
                                        }
                                    } ?> class="activeLink" id="activeLinkScroll" title="<?= $exam->display_name . ' ' . $value ?>"><?= Yii::t('app', $value) ?></a>
                                <?php else: ?>
                                    <a class="<?= ($key == $pageName ? 'activeLink' : '') ?>" title="<?= $exam->display_name . ' ' . $value ?>" href="<?= $key == 'overview' ? Url::toExamDetail($exam->slug, DataHelper::getLangCode($exam->lang_code)) : Url::toExamDetail($exam->slug, DataHelper::getLangCode($exam->lang_code), $key) ?>">
                                        <?= Yii::t('app', $value) ?>
                                    </a>
                                <?php endif; ?>
                                <?php if (array_key_exists($value, $dropdowns)) { ?>
                                    <span class="spriteIcon caret" data-list="<?= $dataAttribute ?>"></span>
                                    <ul class="subNavDropDownMenu desktopOnly">
                                        <?php foreach ($dropdowns[$value] as $dropValue) {
                                            $slug = $dropValue['slug'] ?? null;
                                            $title = ($value == 'Syllabus') ? $exam->display_name . ' ' . $dropValue['name'] . ' ' . $value : $exam->display_name . ' ' . $dropValue['name'];
                                            ?>
                                            <li><a class="<?= $slug == $pageName ? 'subNavActive' : '' ?>" title="<?= $title ?>" href="<?= Url::toExamDetailSubPage($exam->slug, $key, $dropValue['name'], $slug, DataHelper::getLangCode($exam->lang_code)) ?>"><?= Yii::t('app', $dropValue['name']) ?></a></li>
                                        <?php } ?>

                                    </ul>
                                <?php } ?>
                            </li>
                            <?php if (array_key_exists($value, $dropdowns)) { ?>
                                <div class="mobileSubNavDropDownMenu" id="<?= $dataAttribute ?>">
                                    <div class="mobileSubNavDropDownDiv">
                                        <ul>
                                            <?php foreach ($dropdowns[$value] as $dropValue) {
                                                $slug = $dropValue['slug'] ?? null;
                                                $title = ($value == 'Syllabus') ? $exam->display_name . ' ' . $dropValue['name'] . ' ' . $value : $exam->display_name . ' ' . $dropValue['name'];
                                                ?>
                                                <li><a class="<?= $slug == $pageName ? 'subNavActive' : '' ?>" title="<?= $title ?>" href="<?= Url::toExamDetailSubPage($exam->slug, $key, $dropValue['name'], $slug, DataHelper::getLangCode($exam->lang_code)) ?>"><?= Yii::t('app', $dropValue['name']) ?></a></li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                            <?php } ?>

                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
        </nav>
    <?php endif; ?>

    <div class="row">

        <div class="col-md-8">
            <main>
                <?php if ($exam->is_google_ads == Exam::ADS_ACTIVE && !$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv" style="<?= $isMobile ? 'height: 50px;' : '' ?>">
                            <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                            ?>

                            <?php //endif;
                            ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- rank-predictor -->
                <?php if ($pageName == 'rank-predictor' || $pageName == 'percentile-predictor') { ?>
                    <?= $this->render('partials/_rank-predictor', ['displayName' => $displayName ?? '', 'pageName' => $pageName ?? '', 'localizeYear' => $content->localize_year ?? '']) ?>
                <?php } ?>

                <?php if (($pageName == 'overview') && !empty($features)): ?>
                    <div class="examInfo featureDetail">
                        <div class="pageData examDetailsTable">
                            <h2><?= $exam->display_name ?> Exam Details</h2>
                            <table>
                                <tbody>
                                    <?php foreach ($features as $key => $value): ?>
                                        <?php if (!empty($value[0]) && !empty(DataHelper::$featurArr[$key])): ?>
                                            <tr>
                                                <td><span class="spriteIcon tableIcon <?= DataHelper::$featurArr[$key] ?>"></span><span><?= $value[1] ?></span></td>
                                                <td>
                                                    <?= $value[0] ?>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- college predictor -->
                <?php if (!empty($menus) && isset($menuFlip['college-predictor']) && $pageName == 'college-predictor' && !empty($checkExamCollegePredictorMap)): ?>
                    <?= $this->render('partials/_college_predictor', [
                        'entity' => 'exam',
                        'entityId' => $exam->id,
                        'entity_type' => Yii::$app->controller->entityType ?? Yii::$app->controller->id,
                        'entity_subtype' => Yii::$app->controller->pageType ?? Yii::$app->controller->action->id,
                        'pageName' => $pageName,
                        'stream_id' => $exam->primary_stream_id,
                        'level' => $exam->derived_lead_level,
                        'course_id' => $exam->primary_course_id,
                        'pageUrl' => $currentUrl,
                        'exam' => $exam
                    ]) ?>
                <?php endif; ?>

                <!-- exam widget -->
                <div id="exam_widget_section" data-exam-id="<?= $exam->id ?>" data-overview="<?= !empty($pageName) && $pageName == 'overview' ? true : false ?>"></div>

                <div class="examInfo">
                    <div class="updated-info row">
                        <?php if (!empty($user->slug)): ?>
                            <div class="updatedBy">
                                <img onclick="gmu.url.goto('<?= Url::toAllAuthorPost($user->slug, DataHelper::getLangCode($exam->lang_code)) ?>')" src="<?= $authorImage ?>" alt="<?= $user->name ?>">
                            </div>
                        <?php endif; ?>
                        <div class="authorAndDate">
                            <?php if (!empty($user->slug)): ?>
                                <a class="authorName" href="<?= Url::toAllAuthorPost($user->slug, DataHelper::getLangCode($exam->lang_code)) ?>" title="<?= $user->name ?>"><?= $user->name ?></a>
                                <span class="spriteIcon verifiedBlueTickIcon"></span>
                            <?php endif; ?>
                            <p><?= Yii::t('app', 'Updated on') ?> - <?= Yii::$app->formatter->asDate($content->updated_at ?? 'today') ?></p>
                        </div>
                    </div>

                    <?php if (!empty($recentActivity) && Url::toDomain() !=  Url::toBridgeU()): ?>
                        <?= $this->render('../partials/_recentActivity', [
                            'recentActivity' => $recentActivity,
                            'title' => (!empty($exam->display_name) ? $exam->display_name : $exam->name) . ' About'
                        ]) ?>
                    <?php endif;
                    // $createdDate = new DateTime($content->created_at);
                    // $comparisonDate = new DateTime('2024-09-03 12:00:00');
                    // if (isset($h2Text['h2']) && !empty($h2Text['h2']) && $createdDate > $comparisonDate):
                    if (isset($h2Text['h2']) && !empty($h2Text['h2'])):
                        ?>
                        <?= $this->render('partials/_table-of-content', [
                            'h2Content' => $h2Text['h2'],
                        ]); ?>
                    <?php endif; ?>
                    <?= ContentHelper::removeStyleTag(stripslashes(
                        DataHelper::parseDomainUrlInContent($pageContent)
                    )) ?>

                </div>

                <?php /* <?php if (!empty($pdfs)): ?>
                    <section class="downloadPdfSection">
                        <?php $count = 1;  ?>
                        <?php foreach ($pdfs as $pdf):
                            ?>
                            <div class="downloadPdfCard">
                                <div class="row">
                                    <div class="pdfHeading">
                                        <span class="spriteIcon pdfIcon"></span>
                                        <p><?= $pdf->uploaded_file_name ?></p>
                                    </div>
                                    <div class="lead-cta" id="<?= $count ?>" data-entity="exam" data-lead_cta='3' data-durl="<?= $pdf->uploaded_file_name ?>"></div>
                                </div>
                            </div>
                            <?php $count++; ?>
                        <?php endforeach; ?>
                    </section>
                <?php endif; ?> */ ?>

                <!-- faqs -->
                <?php if (!empty($faqs)): ?>
                    <?= $this->render('partials/_faqs', [
                        'faqs' => $faqs,
                        'localize_year' => $content->localize_year ??  ContentHelper::EXAM_YEAR,
                        'displayName' => $displayName ?? '',
                        'pageName' => $content->name ?? ''
                    ]) ?>
                <?php endif; ?>
            </main>

            <?php if ($exam->is_google_ads == Exam::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <aside class="horizontalRectangle">
                    <div class="appendAdDiv">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280') ?>
                        <?php else: ?>
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                        <?php endif; ?>
                    </div>
                </aside>
            <?php endif; ?>

            <div class="removeFixedQuickLink"> </div>
        </div>

        <div class="col-md-4">
            <aside class="exam-aside">
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <?php if (!in_array($pageName, ['rank-predictor', 'percentile-predictor'])) { ?>
                        <div class="lead-cta lead-cta-exam-cls" data-image="<?= $exam->cover_image ?>" data-entity="exam" data-lead_cta="2" data-sponsor="<?= $sponsorClientUrl ?>"></div>
                    <?php } ?>
                <?php endif; ?>
                <?php if (!empty($collegeAcceptingExams)): ?>
                    <div class="trendingArtilce trendingArtilce-cls examCollegeAcceptingDiv trendingArtilerList">
                        <p><?= Yii::t('app', 'Colleges Accepting ' . $exam->display_name) ?></p>
                        <?php foreach ($collegeAcceptingExams['college'] as $colleges): ?>
                            <div class="trendingArtilerDiv row">
                                <div class="trendingArtileText">
                                    <a href="<?= Url::toCollege($colleges['college_url']) ?>" title="<?= $colleges['college_display_name'] ?>" class="listCard">
                                        <?= $colleges['college_display_name'] ?? $exam['college_name'] ?>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <?php if ($collegeAcceptingExams['filterPageStatus'] !== Exam::STATUS_INACTIVE): ?>
                            <div class="trendingArtilerDiv trendingArtilerLastDiv">
                                <div class="trendingArtileText">
                                    <a class="trendingArtileViewAll" href="/colleges-accepting-<?= $exam->slug ?>-score-in-india">View All</a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($coursesOfferedByExam)): ?>
                    <div class="trendingArtilce trendingArtilce-cls examCourseOffereDiv trendingArtilerList">
                        <p><?= Yii::t('app', 'Courses Offered through ' . $exam->display_name) ?></p>
                        <?php foreach ($coursesOfferedByExam as $courses): ?>
                            <div class="trendingArtilerDiv row">
                                <div class="trendingArtileText">
                                    <a href="<?= Url::toCourseDetail($courses['slug']) ?>" title="<?= $courses['name'] ?>" class="listCard">
                                        <?= $courses['short_name'] ?? $courses['name'] ?>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($upcomingExams)): ?>
                    <div class="trendingArtilce trendingArtilce-cls">
                        <p><?= Yii::t('app', 'UPCOMING EXAMS') ?></p>
                        <div class="trendingArtilerList">
                            <?php $upExamsCount = 0; ?>
                            <?php foreach ($upcomingExams as $upExams): ?>
                                <?php if ($exam->slug == $upExams->exam->slug) {
                                    continue;
                                } ?>
                                <?php if ($upExamsCount == 6) {
                                    break;
                                }
                                $upExamsCount++; ?>
                                <div class="trendingArtilerDiv row">
                                    <div class="dateLabel">
                                        <span><?= Yii::$app->formatter->asDate(substr($upExams->start, 0, 10), 'd') ?></span>
                                        <?= Yii::$app->formatter->asDate(substr($upExams->start, 0, 10), 'php:M') ?>
                                    </div>
                                    <div class="trendingArtileText">
                                        <a href="<?= Url::toExamDetail($upExams->exam->slug, DataHelper::getLangCode($upExams->exam->lang_code)) ?>" title="<?= $upExams->exam->display_name ?? $upExams->exam->name ?>"><?= $upExams->exam->display_name ?? $upExams->exam->name ?></a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif;*/ ?>
                <?php if ($exam->is_google_ads == Exam::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <aside class="squareDiv">
                        <div class="appendAdDiv">
                            <?php if ($isMobile): ?>
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__200x600')
                                ?>
                            <?php else: ?>
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x600')
                                ?>
                            <?php endif; ?>
                        </div>
                    </aside>
                <?php endif; ?>
                <?php if (!empty($articleSubPage)): ?>
                    <div class="quickLinks quickLinks-cls article-links">
                        <h2><?= Yii::t('app', 'Important Questions') ?></h2>
                        <ul>
                            <?php $j = 0;
                            foreach ($articleSubPage as $key => $value):
                                foreach ($value as $keyVal => $val):  ?>
                                    <li>
                                        <a title="<?= $val ?>" href="<?= Url::toArticleDetail($keyVal, '') ?>">
                                            <?= $val ?>
                                        </a>
                                    </li>
                                <?php endforeach;
                                $j++;
                            endforeach; ?>
                        </ul>
                    </div>
                <?php endif ?>
                <?php if (count($pages) > 1): ?>
                    <div class="quickLinks quickLinks-cls">
                        <h2><?= Yii::t('app', 'Quick Links') ?></h2>
                        <ul>
                            <?php foreach (DataHelper::examContentList() as $key => $value): ?>
                                <?php if (in_array($key, $menus)): ?>
                                    <li>
                                        <?php if ($key == $pageName): ?>
                                            <?php continue; ?>
                                        <?php endif; ?>

                                        <a title="<?= $exam->display_name . ' ' . ($key == 'overview' ? '' : $value) ?>" href="<?= $key == 'overview' ? Url::toExamDetail($exam->slug, DataHelper::getLangCode($exam->lang_code)) : Url::toExamDetail($exam->slug, DataHelper::getLangCode($exam->lang_code), $key) ?>">
                                            <?= $exam->display_name . ' ' . $value ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif ?>
            </aside>
        </div>
    </div>

    <?php if ($exam->is_google_ads == Exam::ADS_ACTIVE): ?>
        <?php if (!empty($liveApplicationForm)): ?>
            <section class="liveApllicationForms">
                <h2><?= Yii::t('app', 'LIVE APPLICATION FORMS') ?></h2>
                <div class="liveApllicationFormsInner">
                    <div class="row">
                        <?php foreach (array_slice($liveApplicationForm, 0, 5) as $application):
                            $link = $application['redirect_url'];
                            if (!isset($link) || empty($link)) {
                                continue;
                            }
                            ?>
                            <div class="applicationDiv">
                                <figure>
                                    <img width="80" height="80" src="<?= !empty($application['college_id']) && !empty($application['logo_image']) ? DataHelper::s3Path(null, 'college_logo', 'path') . '/' . $application['logo_image'] : Url::defaultCollegeLogo() ?>" alt="logo">
                                </figure>
                                <div>
                                    <!-- <p> -->
                                    <a href="<?= $link ?>" class="clgName" target="_blank" rel=nofollow><?= $application['college_name'] ?></a>
                                    <!-- </p> -->
                                    <p>
                                        <?= $application['course_name'] ?>
                                    </p>
                                    <p class="course-fees">
                                        <?php if (!empty($application['total_fees'])) { ?>
                                            (Total Fees) Rs. <?= CollegeHelper::feesFormat($application['total_fees']) ?>
                                        <?php } ?>
                                    </p>
                                </div>
                                <a class="primaryBtn applicaticationFormBtn" rel=nofollow href="<?= $link ?>" target="_blank"><?= Yii::t('app', 'APPLY NOW') ?> </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>
    <?php endif; ?>

    <!-- colleges accepting exams -->
    <?php /*
    <?php if (!empty($collegeAcceptingExams['college']) && count($collegeAcceptingExams['college'])): ?>
        <?=
        $this->render('partials/_college-accepting-exam', [
            'collegeAcceptingExams' => $collegeAcceptingExams,
            'exam' => $exam
        ])
        ?>
    <?php endif; ?>
    */ ?>

    <!-- QNA card -->
    <?php if (!empty($forumQna)): ?>
        <?= $this->render('../qna/_qna-card', [
            'qna' => $forumQna,
            'pageName' => $pageName,
            'entity' => Exam::ENTITY_EXAM,
            'entity_slug' => $exam->slug,
            'page_name' => $exam->display_name
        ]) ?>
    <?php endif; ?>

    <?php /*
    <?php if (!empty($forums)) : ?>
        <section class="discussionForumSection">
            <h2>DISCUSSION FORUM</h2>

            <?php foreach ($forums as $forum) : ?>
                <div class="discussionForum">

                    <div class="row">
                        <p class="questionLabel"></p>
                        <div class="forumqna">
                            <a class="question" href="/qna/<?= $forum['fq_slug'] ?>"><?= $forum['fq_title'] ?></a>
                            <p class="questionByUser">Asked By: <span> <?= $forum['fq_user_name'] ?? 'Anonymous' ?></span>
                                <span class="year"><?= $forum['fq_updated_at'] ?></span>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <p class="answerLabel"></p>
                        <div class="answerBox">
                            <div class="qnaAnswer">
                                <?= ContentHelper::removeStyleTag(html_entity_decode($forum['fa_description'])) ?>
                                <p class="answerBy">
                                    <span class="spriteIcon gmuLable"></span>
                                    Answer By: <span> <?= $forum['fa_user_name'] ?></span>
                                    <span class="year"><?= $forum['fa_updated_at'] ?></span>
                                </p>

                            </div>
                            <div class="desktopOnly">
                                <a href="javascript:;" class="writeAnswer">WRITE ANSWER</a>
                                <a href="/qna/<?= $forum['fq_slug'] ?>"" class=" moreAnswer">MORE ANSWERS</a>
                            </div>
                        </div>
                    </div>
                    <div class="mobileOnly">
                        <a href="javascript:;" class="writeAnswer">WRITE ANSWER</a>
                        <a href="/qna/<?= $forum['fq_slug'] ?>" class="moreAnswer">MORE ANSWERS</a>
                    </div>
                </div>
            <?php endforeach; ?>

            <a href="exams/<?= $exam->slug ?>/qna" class="primaryBtn viewAllQns">View ALL Questions</a>
        </section>
    <?php endif; ?>

    <section class="askUsQuestion">
        <h2>HAVE QUESTIONS ABOUT JEE MAIN?</h2>
        <p>Our 500+ subject matter experts welcome you to join our QnA community to get answers to all your
            questions within 24 hours for free. Learn the tips and tricks from JEE Main veterans, discover
            how
            to improve your exam preparations, important dates you need to remember, and more.</p>
        <a href="javascript:;" class="primaryBtn">ASK US</a>
    </section>
     **/ ?>
    <?php if (!empty($intrestedExams) && ($pageName == 'overview')) {
        $streamName = !empty($exam->primaryStream->display_name) ? $exam->primaryStream->display_name : $exam->primaryStream->name;
        ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => $exam->slug,
            'title' => 'Other ' . $streamName . ' Exams',
            'totalCards' => 8,
            'pageSlugCount' => 5,
            'pages' => $interestedInPages,
            'details' => $intrestedExams,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
        ])
        ?>
    <?php } else if (!empty($intrestedExams) && ($pageName !== 'overview')) {
        $streamName = !empty($exam->primaryStream->display_name) ? $exam->primaryStream->display_name : $exam->primaryStream->name;
        ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => $exam->slug,
            'title' => 'Other ' . $streamName . ' Exams',
            'totalCards' => 8,
            'pageSlugCount' => 4,
            'pages' => $interestedInSubPages,
            'details' => $intrestedExams,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
        ])
        ?>
    <?php } ?>

    <?php /*if (!empty($upcomingExamByStream)): ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => $exam->slug,
            'title' => Yii::t('app', 'Upcoming') . ' ' . Yii::t('app', $primaryStream->name) . ' ' . Yii::t('app', 'Exams'),
            'totalCards' => 4,
            'pageSlugCount' => 4,
            'pages' => $upcomingDisciplinePages,
            'details' => $upcomingExamByStream,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
            'viewAllUrl' => Url::toDisciplineExam($primaryStream->slug),
            'viewAllTitle' => $streams[0]['name'] . ' Entrance Exams in India'
        ])
        ?>
    <?php endif;*/ ?>

    <?php if (!empty($popularExams)): ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => $exam->slug,
            'title' => 'Popular Exams',
            'totalCards' => 4,
            'pageSlugCount' => 4,
            'details' => $popularExams,
            'pages' => $popularExamsPages,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
        ])
        ?>
    <?php endif; ?>

    <!-- related article -->
    <?php if (!empty($exam->article)): ?>
        <?= $this->render('../partials/_productArticleCard', [
            'relatedArticles' => $exam->article,
            'title' => 'Latest Articles on ' . $displayName,
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($exam->news)): ?>
        <?= $this->render('../partials/_productNewsCard', [
            'news' => $exam->news,
            'title' => 'Latest News on ' . $displayName,
        ]); ?>
    <?php endif; ?>

    <?php
    if (!empty($stateListbyCourse) || !empty($cityListbyCourse)): ?>
        <section class="examInfoSlider stateCityWidget">
            <div class="row">
                <div class="col-md-6 sidebarLinks">
                    <h2 class="row">Top <?= $streams[0]->display_name ?> Colleges by State</h2>
                    <?php foreach ($stateListbyCourse as $list): ?>
                        <a href="/<?= $streams[0]->slug ?>-colleges/<?= $list['stateSlug'] ?>" title="<?= $streams[0]->display_name ?> colleges in <?= $list['stateName'] ?>" class="listCard">
                            <div class="row trendingArtilerDiv">
                                <p class="trendingArtileText"><?= $streams[0]->display_name ?> Colleges In <?= $list['stateName'] ?></p>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
                <div class="col-md-6 sidebarLinks">
                    <h2 class="row">Top <?= $streams[0]->display_name ?> Colleges by City</h2>
                    <?php foreach ($cityListbyCourse as $list): ?>
                        <a href="/<?= $streams[0]->slug ?>-colleges/<?= $list['citySlug'] ?>" title="<?= $streams[0]->display_name ?> colleges in <?= $list['cityName'] ?>" class="listCard">
                            <div class="row trendingArtilerDiv">
                                <p class="trendingArtileText"><?= $streams[0]->display_name ?> Colleges In <?= $list['cityName'] ?></p>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>
</div>
</div>