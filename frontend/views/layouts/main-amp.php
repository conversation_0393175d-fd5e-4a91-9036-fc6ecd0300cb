<?php

use frontend\helpers\Schema;
use frontend\helpers\Url;

$currentUrl = isset($this->params['canonicalUrl']) ? $this->params['canonicalUrl'] : Url::base(true) . Yii::$app->request->getUrl();

$this->registerMetaTag(['name' => 'description', 'content' => $this->context->description]);
$this->registerMetaTag(['property' => 'og:type', 'content' => $this->context->ogType ?? 'website']);
$this->registerMetaTag(['property' => 'og:title', 'content' => $this->title]);
$this->registerMetaTag(['property' => 'og:url', 'content' => $currentUrl]);
$this->registerMetaTag(['property' => 'og:site_name', 'content' => 'Getmyuni']);
$this->registerMetaTag(['property' => 'og:description', 'content' => $this->context->description]);
$this->registerMetaTag(['property' => 'twitter:card', 'content' => 'summary_large_image']);
$this->registerMetaTag(['property' => 'twitter:site', 'content' => 'Getmyuni']);
$this->registerMetaTag(['property' => 'twitter:creator', 'content' => '@getmyuniedu']);
$this->registerMetaTag(['property' => 'twitter:url', 'content' => $currentUrl]);
$this->registerMetaTag(['property' => 'twitter:title', 'content' => $this->title]);
$this->registerMetaTag(['property' => 'twitter:description', 'content' => $this->context->description]);
$this->registerLinkTag(['rel' => 'canonical', 'href' => $currentUrl]);

if (!empty($this->context->ogImage)) {
    $this->registerMetaTag(['property' => 'og:image', 'content' => $this->context->ogImage]);
    $this->registerMetaTag(['property' => 'twitter:image', 'content' => $this->context->ogImage]);
}
?>

<?php $this->beginPage()?>
<!DOCTYPE html>
<html ⚡ lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1" />
    <meta name="theme-color" content="#545ebd">
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    <link rel="preconnect" href="https://www.googletagmanager.com/" crossorigin>
    <link rel="shortcut icon" type="image/png" href="<?= Url::toDomain() ?>favicon.png" />
    <link rel="icon" href="<?= Url::toDomain() ?>favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto">
    <?php $this->registerCsrfMetaTags()?>
    <title><?=Html::encode($this->title)?></title>
    <?php $this->head()?>

    <style amp-custom>
        <?=$this->context->ampCss?>
    </style>

    <script async custom-element="amp-ad" src="https://cdn.ampproject.org/v0/amp-ad-0.1.js"></script>
    <script async custom-element="amp-sidebar" src="https://cdn.ampproject.org/v0/amp-sidebar-0.1.js"></script>
    <script async custom-element="amp-bind" src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"></script>
    <script async custom-element="amp-lightbox" src="https://cdn.ampproject.org/v0/amp-lightbox-0.1.js"></script>
    <script async custom-element="amp-selector" src="https://cdn.ampproject.org/v0/amp-selector-0.1.js"></script>
    <script async custom-element="amp-accordion" src="https://cdn.ampproject.org/v0/amp-accordion-0.1.js"></script>
    <script async custom-element="amp-form" src="https://cdn.ampproject.org/v0/amp-form-0.1.js"></script>
   <script async custom-element="amp-anim" src="https://cdn.ampproject.org/v0/amp-anim-0.1.js"></script>
    <style amp-boilerplate>
        body {
            -webkit-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            -moz-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            -ms-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            animation: -amp-start 8s steps(1, end) 0s 1 normal both;
        }

        @-webkit-keyframes -amp-start {
            from {
                visibility: hidden;
            }

            to {
                visibility: visible;
            }
        }

        @-moz-keyframes -amp-start {
            from {
                visibility: hidden;
            }

            to {
                visibility: visible;
            }
        }

        @-ms-keyframes -amp-start {
            from {
                visibility: hidden;
            }

            to {
                visibility: visible;
            }
        }

        @-o-keyframes -amp-start {
            from {
                visibility: hidden;
            }

            to {
                visibility: visible;
            }
        }

        @keyframes -amp-start {
            from {
                visibility: hidden;
            }

            to {
                visibility: visible;
            }
        }
    </style>
    <noscript>
        <style amp-boilerplate>
            body {
                -webkit-animation: none;
                -moz-animation: none;
                -ms-animation: none;
                animation: none;
            }
        </style>
    </noscript>
    <script async src="https://cdn.ampproject.org/v0.js"></script>
    <?php if (isset($this->params['schema'])): ?>
        <?=\yii\helpers\Html::script($this->params['schema'], ['type' => 'application/ld+json'])?>
    <?php endif;?>

    <script async custom-element="amp-analytics" src="https://cdn.ampproject.org/v0/amp-analytics-0.1.js"></script>
</head>

<body>
    <?php $this->beginBody()?>
    <header class="page-header">

        <div class="topHeader">
            <div class="container">
                <div class="row">
                    <div class="">
                        <button on="tap:sidebar1" class="spriteIcon hambergerIcon"></button>
                    </div>
                    <a href="#" class="spriteIcon headerLogo"></a>
                    <a on="tap:AMP.setState({showLightbox: true})" class="spriteIcon searchIcon"></a>
                </div>
            </div>
        </div>
        <amp-sidebar id="sidebar1" layout="nodisplay" style="width:300px">
            <amp-nested-menu layout="fill" class="slider-menu-option">
                <div class="guestDisplay">
                    <p class="guestPic">
                        <amp-img src="<?= Url::toDomain() ?>yas/images/userIcon.png" alt="User Icon" width="36" height="36"></amp-img>
                    </p>
                    <p class="welcomeText">Welcome Guest!</p>
                    <p></p>
                    <div class="row">
                        <a class="writeReview" href="https://getmyuni.com/review/create" title="Write a Review">Write a
                            Review</a>
                    </div>
                </div>
                <ul>
                    <li><a href="<?= Url::toDomain() ?>" title="Home">Home</a></li>
                    <li>
                        <button class="slide_menu_courses" amp-nested-submenu-open="" aria-expanded="false">Colleges</button>
                        <div amp-nested-submenu="">

                            <ul>
                                <h4 class="backToMenu parentMenu" amp-nested-submenu-close>
                                    <span class="spriteIcon angle_left"> </span>Back to Menu
                                </h4>

                                <li>
                                    <ul class="no_padding">
                                        <li><a title="Engineering" href="<?= Url::toDomain() ?>engineering-colleges">Engineering</a>
                                        </li>
                                        <li><a title="Management" href="<?= Url::toDomain() ?>management-colleges">Management</a>
                                        </li>
                                        <li><a title="Medical" href="<?= Url::toDomain() ?>medical-colleges">Medical</a>
                                        </li>
                                        <li><a title="Science" href="<?= Url::toDomain() ?>science-colleges">Science</a>
                                        </li>
                                        <li><a title="Commerce" href="<?= Url::toDomain() ?>commerce-colleges">Commerce</a></li>
                                        <li><a title="Arts" href="<?= Url::toDomain() ?>arts-colleges">Arts</a>
                                        </li>
                                        <li><a title="Pharmacy" href="<?= Url::toDomain() ?>pharmacy-colleges">Pharmacy</a></li>
                                        <!-- <li><a title="Fashion" href="<?= Url::toDomain() ?>fashion-colleges">Fashion</a> -->
                                        </li>
                                        <li><a title="Design" href="<?= Url::toDomain() ?>design-colleges">Design</a>
                                        </li>
                                        <li><a title="Law" href="<?= Url::toDomain() ?>law-colleges">Law</a></li>
                                        <li><a title="Education" href="<?= Url::toDomain() ?>education-colleges">Education</a>
                                        </li>
                                        <li><a title="All Colleges" href="<?= Url::toDomain() ?>all-colleges">All
                                                Colleges &gt;&gt;</a></li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li><a href="<?= Url::toDomain() ?>college/admissions" title="College Admission in India">Admission</a></li>
                    <li>
                        <button class="slide_menu_courses" amp-nested-submenu-open="" aria-expanded="false">Exams</button>
                        <div amp-nested-submenu="">
                            <ul>
                                <h4 class="backToMenu parentMenu" amp-nested-submenu-close>
                                    <span class="spriteIcon angle_left"> </span>Back to Menu
                                </h4>
                                <li><a title="Engineering" href="<?= Url::toDomain() ?>exams/engineering-exams-in-india">Engineering</a>
                                </li>
                                <li><a title="Management" href="<?= Url::toDomain() ?>exams/management-exams-in-india">Management</a>
                                </li>
                                <li><a title="Medical" href="<?= Url::toDomain() ?>exams/medical-exams-in-india">Medical</a></li>
                                <li><a title="Science" href="<?= Url::toDomain() ?>exams/science-exams-in-india">Science</a></li>
                                <li><a title="Commerce" href="<?= Url::toDomain() ?>exams/commerce-exams-in-india">Commerce</a></li>
                                <li><a title="Architecture" href="<?= Url::toDomain() ?>exams/architecture-exams-in-india">Architecture</a>
                                </li>
                                <li><a title="Pharmacy" href="<?= Url::toDomain() ?>exams/pharmacy-exams-in-india">Pharmacy</a></li>
                                <li><a title="Law" href="<?= Url::toDomain() ?>exams/law-exams-in-india">Law</a></li>
                                <li><a title="Education" href="<?= Url::toDomain() ?>exams/education-exams-in-india">Education</a>
                                </li>
                                <li><a title="Dental" href="<?= Url::toDomain() ?>exams/dental-exams-in-india">Dental</a>
                                </li>
                                <li><a title="Government" href="<?= Url::toDomain() ?>exams/government-exams-in-india">Government</a>
                                </li>
                                <li><a title="Design" href="<?= Url::toDomain() ?>exams/design-exams-in-india">Design</a>
                                </li>
                                <li><a title="Paramedical" href="<?= Url::toDomain() ?>exams/paramedical-exams-in-india">Paramedical</a>
                                </li>
                                <li><a title="Agriculture" href="<?= Url::toDomain() ?>exams/agriculture-exams-in-india">Agriculture</a>
                                </li>
                                <li><a title="Arts" href="<?= Url::toDomain() ?>exams/arts-exams-in-india">Arts</a>
                                </li>
                                <li><a title="All Exams" href="<?= Url::toDomain() ?>exams">All Exams&gt;&gt;</a>
                                </li>

                            </ul>
                        </div>
                    </li>
                    <li>
                        <button class="slide_menu_courses" amp-nested-submenu-open="" aria-expanded="false">Boards</button>
                        <div amp-nested-submenu="">
                            <ul>
                                <h4 class="backToMenu parentMenu" amp-nested-submenu-close>
                                    <span class="spriteIcon angle_left"> </span>Back to Menu
                                </h4>
                                <li><a title="CBSE 10th Board" href="<?= Url::toDomain() ?>boards/cbse-10th-board">CBSE
                                        10th
                                        Board</a></li>
                                <li><a title="CBSE 12th Board" href="<?= Url::toDomain() ?>boards/cbse-12th-board">CBSE
                                        12th
                                        Board</a></li>
                                <li><a title="CISCE 10th Board" href="<?= Url::toDomain() ?>boards/icse-10th-board-cisce">CISCE 10th Board</a>
                                </li>
                                <li><a title="CISCE 12th Board" href="<?= Url::toDomain() ?>boards/isc-12th-cisce">CISCE
                                        12th
                                        Board</a></li>
                                <li><a title="Bihar 10th Board" href="<?= Url::toDomain() ?>boards/bihar-board-10th-matric-bseb">Bihar 10th
                                        Board</a>
                                </li>
                                <li><a title="Bihar 12th Board" href="<?= Url::toDomain() ?>boards/bihar-board-12th-intermediate-bseb">Bihar 12th
                                        Board</a></li>
                                <li><a title="All Boards" href="<?= Url::toDomain() ?>boards">All Boards &gt;&gt;</a>
                                </li>

                            </ul>
                        </div>
                    </li>
                    <li>
                        <button class="slide_menu_courses" amp-nested-submenu-open="" aria-expanded="false">Study
                            Abroad</button>
                        <div amp-nested-submenu="">
                            <ul>
                                <h4 class="backToMenu parentMenu" amp-nested-submenu-close>
                                    <span class="spriteIcon angle_left"> </span>Back to Menu
                                </h4>
                                <li><a href="<?= Url::toDomain() ?>canada" title="Study in Canada">Study in
                                        Canada</a>
                                </li>
                                <li><a href="<?= Url::toDomain() ?>uk" title="Study in UK">Study in UK</a></li>
                                <li><a href="<?= Url::toDomain() ?>usa" title="Study in USA">Study in USA</a></li>
                                <li><a href="<?= Url::toDomain() ?>australia" title="Study in USA">Study in
                                        Australia</a>
                                </li>
                                <li><a href="<?= Url::toDomain() ?>germany" title="Study in USA">Study in Germany</a>
                                </li>
                                <li><a href="https://ieltsmaterial.com/" target="_blank">IELTS Material</a></li>

                            </ul>
                        </div>
                    </li>
                    <li>
                        <button class="slide_menu_courses" amp-nested-submenu-open="" aria-expanded="false">Resources</button>
                        <div amp-nested-submenu="">
                            <ul>
                                <h4 class="backToMenu parentMenu" amp-nested-submenu-close>
                                    <span class="spriteIcon angle_left"> </span>Back to Menu
                                </h4>
                                <li><a title="Articles" href="<?= Url::toDomain() ?>articles">Articles</a></li>
                                <li><a title="Olympiads" href="<?= Url::toDomain() ?>olympiad">Olympiad</a></li>
                                <li><a title="Scholarships" href="<?= Url::toDomain() ?>scholarships">Scholarships</a>
                                </li>
                                <li><a title="Competitions" href="<?= Url::toDomain() ?>competitions/student-competitions">Competitions</a>
                                </li>
                                <li><a title="Compare Colleges" href="<?= Url::toDomain() ?>college-compare">Compare
                                        Colleges</a></li>
                                <li><a title="Sarkari Exam" href="<?= Url::toDomain() ?>sarkari-exam/">Sarkari
                                        Exam</a>
                                </li>

                            </ul>
                        </div>
                    </li>
                    <li><a href="<?= Url::toDomain() ?>reviews" title="Reviews">Reviews</a></li>
                    <li><a href="https://news.getmyuni.com" title="News">News</a></li>
                </ul>
            </amp-nested-menu>
        </amp-sidebar>

        <amp-lightbox id="my-bindable-lightbox" [open]="showLightbox" layout="nodisplay" on="lightboxClose:AMP.setState({showLightbox: false})">
            <div class="lightbox">
                <div class="advanceSearch">

                    <div class="container">
                        <div class="searchSection">
                            <div class="row search_heading"><span>Advanced Search</span>
                                <span role="button" tabindex="0" on="tap:my-bindable-lightbox.close" class="spriteIcon cancelIcon"></span>
                            </div>
                            <amp-selector class="tabs-with-flex" role="tablist" keyboard-select-mode="focus">
                                <div id="tab1" role="tab" aria-controls="tabpanel1" option selected>Colleges</div>
                                <div id="tabpanel1" role="tabpanel" aria-labelledby="tab1">
                                    <input type="text" id="coll-name-box" placeholder=" Enter College Name" autofocus="" spellcheck="false" autocomplete="off">
                                </div>
                                <div id="tab2" role="tab" aria-controls="tabpanel2" option>Exams</div>
                                <div id="tabpanel2" role="tabpanel" aria-labelledby="tab2">
                                    <input type="text" id="exam-name" placeholder="Enter Exam Name eg: JEE,CAT,XAT">

                                </div>
                                <div id="tab3" role="tab" aria-controls="tabpanel3" option>Courses</div>
                                <div id="tabpanel3" role="tabpanel" aria-labelledby="tab3">
                                    <input type="text" id="course-name" placeholder="Enter Course Name">

                                </div>
                            </amp-selector>
                        </div>

                    </div>
                </div>
            </div>
        </amp-lightbox>

    </header>

    <?php

    use yii\helpers\Html;
    use yii\widgets\Breadcrumbs;

    if (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs'])): ?>
            <?php echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
        <nav class="breadcrumbDiv">
            <div class="container">
                    <?=Breadcrumbs::widget([
                    'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                    'homeLink' => false,
    ])?>
            </div>
        </nav>
    <?php endif;?>

    <div class="blueBgDiv">
        <!-- do not delete this -->
    </div>

    <div class="container">
        <?=$content?>
    </div>

    <footer class="pageFooter">
        <div class="footerPrimarySection">
            <div class="container">
                <div class="row">
                    <a href="#" class="spriteIcon headerLogo"></a>

                    <div>
                        <ul class="socialMedia">
                            <li>Connect with us</li>
                            <li><a href="https://www.facebook.com/getmyuniedu" title="Facebook" rel="noopener nofollow" target="_blank" class="spriteIcon fbIcon"></a>
                            </li>
                            <li><a href="https://twitter.com/getmyuniedu" title="Twitter" rel="noopener nofollow" target="_blank" class="spriteIcon twitterIcon"></a></li>
                            <li><a href="https://www.instagram.com/getmyuni/" title="Instagram" rel="noopener nofollow" target="_blank" class="spriteIcon instaIcon"></a>
                            </li>
                            <li><a href="https://www.linkedin.com/company/getmyuni" title="Linkedin" rel="noopener nofollow" target="_blank" class="spriteIcon linkdIn"></a>
                            </li>
                            <li><a href="https://www.youtube.com/channel/UCvczFiMv9OZwYkFydoNdMCA" title="Youtube" rel="noopener nofollow" target="_blank" class="spriteIcon youtubeIcon"></a>
                            </li>
                        </ul>
                        <ul class="contactInfo">
                            <li><span class="spriteIcon phoneIcon"></span><a href="tel:+91 7969542200" title="+91 7969542200">+91
                            7969542200</a>
                            </li>
                            <li><span class="spriteIcon whiteMailIcon"></span> <a href="mailTo:<EMAIL>" title="<EMAIL>"><EMAIL></a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="footerSecondSection">
            <div class="container">
                <div class="row">
                    <ul>
                        <li><a href="<?= Url::toDomain() ?>about-us" title="About Us">About Us</a>
                        </li>
                        <li><a href="<?= Url::toDomain() ?>contact-us" title="Contact Us">Contact
                                Us</a></li>
                        <li><a href="<?= Url::toDomain() ?>privacy-policy" title="Privacy Policy">Privacy
                                Policy</a></li>
                        <li><a href="<?= Url::toDomain() ?>terms-and-conditions" title="Terms &amp; Conditions">Terms
                                &amp; Conditions</a></li>
                    </ul>
                    <p class="copyrightsText">
                        © 2021 Getmyuni.com. All Rights Reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <amp-lightbox id="leadform" layout="nodisplay">
        <div class="lightbox">

            <div class="leadFormContainer">

                <div class="leadFormHeader">
                    <div class="row">
                        <a href="/" class="spriteIcon headerLogo" title="Getmyuni"></a>
                        <i class="spriteIcon closeLeadForm" on="tap:leadform.close" role="button" tabindex="0"></i>
                    </div>
                </div>
                <div class="leadFormDiv">
                    <i class="spriteIcon closeLeadForm"></i>
                    <div class="userInputs">
                        <div class="formHeadingDiv row">
                            <div class="formImg">
                                <amp-img layout="responsive" height="32" width="32" src="<?=Url::defaultCollegeLogo()?>" alt="">
                                </amp-img>
                            </div>
                            <div class="formHeading">
                                <p class="headingText">Get Latest News Alert</p>
                                <p>Indian Institute of Management - [IIMB] , Bangalore</p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group userName">
                                    <i class="spriteIcon userIcon"></i>
                                    <input type="text" placeholder="Enter Name">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mobileNumber">
                                    <div class="mobileNumber row">
                                        <div class="countryCode">
                                            <div class="dialCodeDiv">
                                                <i class="spriteIcon flagIcon"></i>
                                                <span class="dialCode">+91</span>
                                            </div>
                                        </div>
                                        <div class="numberInput">
                                            <input type="text" maxlength="15" required="required">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group userMail">
                                    <i class="spriteIcon mailIcon"></i>
                                    <input type="email" placeholder="Email Address">
                                </div>
                            </div>


                            <div class="col-md-6">
                                <div class="form-group selectCity">
                                    <i class="spriteIcon locationIcon"></i>
                                    <select id="selectInterestedCity" name="selectCity" class="js-states form-control">
                                        <option value="" selected="">Select your city</option>
                                        <option value="Bangalore">Bangalore</option>
                                        <option value="Chennai">Chennai</option>
                                        <option value="Hyderabad">Hyderabad</option>
                                        <option value="Mumbai">Mumbai</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group courseCategory">
                                    <i class="spriteIcon bookIcon"></i>
                                    <select class="js-states" id="selectCategory" name="selectCategory">
                                        <option value="" selected="">Select Course Category
                                            interested
                                            in</option>

                                        <option value="engineering" data-gmu="engineering-btech-mtech">Engineering
                                            [BTech /
                                            MTech]</option>
                                        <option value="management" data-gmu="management-bba-mba">Management [BBA /
                                            MBA ]
                                        </option>
                                        <option value="distance-learning_correspondence" data-gmu="distance-learning-mba">
                                            Distance Learning MBA</option>
                                        <option value="computers_it" data-gmu="bca-mca-computers">BCA / MCA /
                                            Computers</option>
                                        <option value="design" data-gmu="fashion">Fashion
                                        </option>
                                        <option value="design" data-gmu="design">Design</option>
                                        <option value="architecture" data-gmu="architecture">
                                            Architecture</option>
                                        <option value="media_films_journalism" data-gmu="media-films">Media / Films
                                        </option>
                                        <option value="media_films_journalism" data-gmu="journalism">Journalism</option>
                                        <option value="law" data-gmu="law">Law</option>
                                        <option value="languages-arts-humanities" data-gmu="arts-humanities">Arts and
                                            Humanities
                                        </option>
                                        <option value="sciences" data-gmu="science-bsc-msc">
                                            Science [B.Sc / M.Sc]</option>
                                        <option value="animation_multimedia_web-design" data-gmu="animation-multimedia">
                                            Animation / Multimedia</option>
                                        <option value="banking_finance" data-gmu="finance-accounts-bcom-mcom">Finance /
                                            Accounts
                                            [B.Com / M.Com]</option>
                                        <option value="medicine_health-care" data-gmu="medicine-healthcare-mbbs-bds-bpharm">
                                            Medicine / Healthcare [MBBS / BDS / B.Pharm]
                                        </option>
                                        <option value="aviation_hospitality_tourism" data-gmu="hotel-management-hospitality">
                                            Hotel Management / Hospitality</option>
                                        <option value="aviation_hospitality_tourism" data-gmu="aviation">Aviation
                                        </option>
                                        <option value="design" data-gmu="fashion-design">Fashion
                                            Design</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group qualification">
                                    <i class="spriteIcon capIcon"></i>
                                    <select name="qualification" class="js-states form-control">
                                        <option value="" selected="">Select Hightest Qualification
                                        </option>

                                        <option value="option1">option1</option>
                                        <option value="option2">option2</option>
                                        <option value="option3">option3</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="checkbox-group">
                            <label for="connectUser" class="">By clicking on submit, I allow GetMyUni to
                                contact me via Whatsapp
                                and other channels with suitable college options
                            </label>
                        </div>
                        <div class="formSumbitBtn">
                            <button class="primaryBtn" type="submit">SUBMIT</button>
                        </div>
                    </div>

                    <div class="optSection">
                        <p class="headingText">Verify Mobile Number</p>
                        <p>OTP has been sent toy uor mobile number <span>9999999999</span></p>

                        <div class="numberInputs">
                            <input type="text" id="digit-1" name="digit-1" data-next="digit-2" maxlength="1">
                            <input type="text" id="digit-2" name="digit-2" data-next="digit-3" data-previous="digit-1" maxlength="1">
                            <input type="text" id="digit-3" name="digit-3" data-next="digit-4" data-previous="digit-2" maxlength="1">
                            <input type="text" id="digit-4" name="digit-4" data-next="digit-5" data-previous="digit-3" maxlength="1">
                        </div>
                        <div class="row">
                            <button class="primaryBtn verifyOtp">VERIFY OTP</button>
                            <a href="">Didn’t received the OTP? Resend it.</a>
                        </div>
                    </div>

                    <div class="thankYouMsg">
                        <i class="spriteIcon thankYouIcon"></i>
                        <p class="thankYouText"> Thanks you </p>
                    </div>
                </div>
            </div>
        </div>
    </amp-lightbox>
    <?php $this->endBody()?>
    <amp-analytics config="https://www.googletagmanager.com/amp.json?id=GTM-PGK7WHT&gtm.url=SOURCE_URL" data-credentials="include"></amp-analytics>
</body>

</html>
<?php $this->endPage()?>