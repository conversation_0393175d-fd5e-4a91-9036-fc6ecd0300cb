<?php

use frontend\helpers\Url;
use common\models\NcertArticles;
use frontend\assets\AppAsset;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;

$isMobile = \Yii::$app->devicedetect->isMobile();
//utils
$currentUrl = Url::base(true) . Url::current();
$this->title = 'Find Latest Articles on Colleges, Exams, Courses, Boards & more - Getmyuni';
$this->context->description = 'Find articles by Getmyuni - Renowned source of free, accurate, & insightful college admissions, exam updates and student mentorship information.';

$authorImage = $author ? ContentHelper::getUserProfilePic($author->slug) : '';
if (!empty($authorImage)) {
    $this->registerLinkTag(['href' => $authorImage, 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => $authorImage . ' 300w', 'imagesizes' => '50vw']);
}

$this->registerLinkTag(['href' =>  Url::base(true) . '/ncert', 'rel' => 'alternate', 'hreflang' => Yii::$app->language]);

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = 'NCERT';
$this->params['previous_url'] = Yii::$app->request->referrer;
$this->params['page_url'] = Yii::$app->request->url;
$this->params['entity_name'] = 'Ncert-Landing-Page';
$this->params['entity'] = NcertArticles::ENTITY_NCERT;
$this->params['entityDisplayName'] = !empty($content) ? $content->title : '';
$this->params['entitySlug'] = $content->slug ?? '';

$this->registerCssFile(Yii::$app->params['cssPath'] . 'ncert-article-details.css', ['depends' => [AppAsset::class]]);

// page specific assets
?>
<div class="containerMargin">
    <div class="row">
        <div class="col-md-12">
            <div class="articleHeader">
                <section class="pageDescription">
                    <div class="row">
                        <div class="col-md-12">
                            <?php if (!empty($content->h1)): ?>
                                <h1><?= ContentHelper::htmlDecode(stripslashes($content->h1), false) ?></h1>
                            <?php endif; ?>
                            <?php if (!empty($author)): ?>
                                <div class="authorInfoAndTranslateBtn">
                                    <div class="updated-info row">
                                        <img class="lazyload" loading="lazy" width="60" height="60" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author ? $author->name . ' Image' : '' ?>" />
                                        <span class="updatedDetails">
                                            <div class="updatedBy">
                                                <p><a href="<?= $author ? '/author/' . $author->slug : '#' ?>"><?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?></a>, </p>
                                            </div>
                                            <p><span><?= Yii::$app->formatter->asDate($content->updated_at ?? 'today') ?> </span>
                                            </p>
                                            <ul>
                                                <p>Share it on :</p>
                                                <li>
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $currentUrl ?>" target="_blank" rel="noopener nofollow" class="spriteIcon greyFbIcon"></a>
                                                </li>
                                                <li>
                                                    <a href="https://twitter.com/share?url=<?= $currentUrl ?>" class="spriteIcon greyTwitterIcon" rel="noopener nofollow" target="_blank"></a>
                                                </li>
                                            </ul>
                                        </span>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <div class="col-md-8">
            <main>
                <article>
                    <?php if (!empty($content->top_content)): ?>
                        <div class="articleInfo">
                            <?= DataHelper::parseDomainUrlInContent($content->top_content) ?>
                        </div>
                    <?php endif; ?>
                </article>
            </main>
        </div>
        <div class="col-md-4">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                    <div class="getSupport">
                        <!--<div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Get Expert Counseling and Student Scholarship</p>
                        </div>-->
                        <!-- <p class="getSupport__subheading">Are you Interested in this Ncert Article?</p> -->
                        <div class="button__row__container">
                            <div class="lead-cta" data-entity="ncert" data-lead_cta="0" data-entity="career" data-sponsor=""></div>
                        </div>
                    </div>
                <?php endif;?>
                <?= $this->render('partials/_sidebar-articles', [
                    'trendings' => $trendings,
                    'recentNcert' => $recentNcert,
                    'article' => $content
                ]) ?>
            </aside>
        </div>
    </div>
    <section class="commentSection">
        <?= $this->render('/partials/comment/_form', [
            'model' => $commentModel,
            'entity' => NcertArticles::ENTITY_NCERT,
            'entity_id' => $content->id ?? ''
        ]) ?>
        <?= $this->render('/partials/comment/_comment', [
            'comments' => $comments,
            'entity' => NcertArticles::ENTITY_NCERT,
            'entityId' => $content->id ?? ''
        ]) ?>
    </section>
    <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
        <div id="comment-reply-form-js"></div>
    <?php endif;?>
</div>