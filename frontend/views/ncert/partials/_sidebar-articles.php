<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;

?>

<div class="articleSidebarSection sideBarWidget">
    <ul>
        <?php if (!empty($trendings)):?>
            <li data-tab="trendingArtilceList" class="activeLink">Trending Ncert</li>
        <?php endif; ?>
        <?php if (!empty($recentNcert)): ?>
            <li data-tab="recentArticles" class="<?= empty($trendings) ? 'activeLink' : '' ?>">Recent Ncert</li>
        <?php endif; ?>
    </ul>

    <?php if (!empty($trendings)): ?>
        <div id="trendingArtilceList" class="trendingArtilce tab-content activeLink trendingArtilerList">
            
                <?php foreach ($trendings as $trending):?>
                    <?php if (isset($article) && $article->slug == $trending['slug']): ?>
                        <?php continue; ?>
                    <?php endif; ?>
                    <a class="listCard" href="<?=Url::toNcertDetail($trending['slug']) ?>" title="<?= $trending['title'] ?>">
                        <div class="trendingArtilerDiv row">
                            <div class="sidebarImgDiv">
                                <img class="lazyload" loading="lazy" src="<?= ArticleDataHelper::getImage($trending['cover_image']) ?>" width="60" height="60" alt="<?= $trending['title'] ?>">
                            </div>
                            <div class="trendingArtileText">
                                <p class="sidebarTextLink"><?= $trending['title'] ?? '' ?></p>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
           
        </div>
    <?php endif; ?>

    <?php if (!empty($recentNcert)): ?>
        <div class="recentArticlesList recentArticles tab-content <?= empty($trendings) ? 'activeLink' : '' ?>" id="recentArticles">

            
                <?php foreach ($recentNcert as $recentArticle):
                    ?>
                    <?php if (isset($article) && $article->slug == $recentArticle['slug']): ?>
                        <?php continue; ?>
                    <?php endif; ?>
                    <a class="listCard" href="<?=Url::toNcertDetail($recentArticle['slug']) ?>" title="<?= $recentArticle['title'] ?>">
                        <div class="recentArticlesDiv row">
                            <div class="sidebarImgDiv">
                                <img class="lazyload" loading="lazy" src="<?= $recentArticle['cover_image'] ? ArticleDataHelper::getImage($recentArticle['cover_image']) : ArticleDataHelper::getImage() ?>" width="60" height="60" alt="">
                            </div>
                            <div class="recentArticlesDivText">
                                <p class="sidebarTextLink"><?= $recentArticle['title'] ?? '' ?></p>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
           
        </div>
    <?php endif; ?>
</div>