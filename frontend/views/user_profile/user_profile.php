<?php

use common\helpers\DataHelper;
use frontend\assets\AppAsset;
use yii\helpers\ArrayHelper;

// page specific assets
$this->registerLinkTag(['rel' => 'stylesheet', 'href' => 'https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css']);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'user_profile.css', ['depends' => [AppAsset::class]]);
$emailValue = empty($user->email) ? '' : (preg_match("/\@gmail.com\b/", $user->email) == 1 ? strstr($user->email, '@', true) : '');
?>
<div class="pageMask"></div>
<div class="userProfilePage">
    <form class="basicDetailSectionForm" name="basicDetailSectionOne" id="userProfileFirstScreen">
        <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
        <input type="hidden" id="url" name="url" value="<?= empty($currentUrl) ? '' : $currentUrl ?>">
        <input type="hidden" name="source" value="<?= DataHelper::$leadSource['organic'] ?>">
        <input type="hidden" name="student_id" value="<?= empty($user) ? null : $user->id ?>">
        <input type="hidden" name="otpValue" value="">
        <input type="hidden" name="hiddenNumber" value=<?= empty($user) ? null : $user->phone ?>>
        <section class="basicDetailSection">
            <h3>Basic details and Contact Information</h3>
            <div class="basicDetailForm">
                <div class="nameDetail basicDetailField">
                    <!-- <input placeholder="Enter Name"> -->
                    <input class="formName" id="formName" type="text" name="name" placeholder="Name" value="<?= $user->name ?? '' ?>">
                </div>
                <div class="dobDetail basicDetailField">
                    <!-- <span class="spriteIcon calendarIcon"></span> -->
                    <input class="flatPickrLead dobUser" type="text" placeholder="YYYY/MM/DD" name="dob" value="<?= $user->date_of_birth ?? '' ?>" autocomplete="off">
                </div>
                <div class="genderDetail basicDetailField inputGenderContainer">
                    <select class="select2HookClass" name="gender">
                        <option></option>
                        <?php
                        foreach (ArrayHelper::map(DataHelper::userProfileGender(), 'value', 'name') as $key => $value) {
                            if (!empty($user) && !empty($user->gender) && $key == $user->gender) {
                                echo "<option value='$key' selected>$value</option>";
                            } else {
                                echo "<option value='$key'>$value</option>";
                            }
                        }
                        ?>
                    </select>
                </div>
                <div class="contactDetail basicDetailField inputMobileContainer modalInputContainer">
                    <div class="countryCode otherCountryCode">
                        <div class="dialCodeDiv">
                            <i class="spriteIcon flagIcon"></i>
                            <span class="dialCode">+91</span>
                        </div>
                    </div>
                    <label for="inputMobileNumber"></label>
                    <input class="inputContainerField mobileNumberField mobileNumberFieldUser" id="formMobile" type="tel" name="phone" placeholder="Mobile Number" maxlength="10" value="<?= $user->phone ?? '' ?>" />
                </div>
                <div class="emailDetail basicDetailField">
                    <input class="inputContainerField" id="formEmail" type="text" name="email" placeholder="Enter Email Address" value="<?= $emailValue ?? '' ?>" autocomplete="off" maxlength="50">
                    <!-- <span class="domainExtention">@gmail.com</span> -->
                </div>
                <div class="cityDetail basicDetailField inputCityContainer modalInputContainer">
                    <select class="inputContainerField select2HookClass city" name="current_city">
                        <option></option>
                        <?php
                        foreach ($cities as $key => $value) {
                            echo "<optgroup label='$key'>";
                            foreach ($value as $k => $v) {
                                if (empty($user) && !empty($userCity) && $k == $userCity['cityId']) {
                                    echo "<option value='$k' selected>$v</option>";
                                } elseif (!empty($user) && $k == $user->current_city) {
                                    echo "<option value='$k' selected>$v</option>";
                                } else {
                                    echo "<option value='$k'>$v</option>";
                                }
                            }
                            echo '</optgroup>';
                        }
                        ?>
                    </select>
                </div>
            </div>
            <div class="submitDetailButtonRow">
                <button class="btn-navigate-form-step stepOne" style="display: none;" id="userProfileFirstScreenSubmit" type="button">Save</button>
            </div>
        </section>
    </form>
    <form class="basicDetailSectionForm" name="basicDetailSectionTwo" id="userProfileSecondScreen">
        <input type="hidden" id="leadform-utm_source" name="utm_source" value="<?= empty($_GET['utm_source']) ? '' : $_GET['utm_source'] ?>">
        <input type="hidden" id="leadform-utm_medium" name="utm_medium" value="<?= empty($_GET['utm_medium']) ? '' : $_GET['utm_medium'] ?>">
        <input type="hidden" id="leadform-utm_campaign" name="utm_campaign" value="<?= empty($_GET['utm_campaign']) ? '' : $_GET['utm_campaign'] ?>">
        <input type="hidden" name="source" value="<?= DataHelper::$leadSource['organic'] ?>">
        <section class="courseDetailSection">
            <h3>Course interested in</h3>
            <div class="selectCourseDiv commonClassName">
                <ul class="noCourseSelections">
                    <p>No Courses selected</p>
                </ul>
                <div class="selectedCourseValues"></div>
                <div class="courseSelect2 inputCourseContainerUser modalInputContainer courseCategory">
                    <select name="userProfileCourse[]" multiple="multiple">
                        <option></option>
                    </select>
                </div>
            </div>
            <h3>College location preferred</h3>
            <div class="selectLocationDiv commonClassName">
                <ul class="noCitySelections">
                    <p>No City selected</p>
                </ul>
                <div class="selectedCityValues"></div>
                <div class="locationSelect2 inputPreferredCityContainer">
                    <select class="inputContainerField select2HookClass preferredCity" name="college_preferred_city[]" multiple="multiple">
                        <option></option>
                        <?php
                        foreach ($cities as $key => $value) {
                            echo "<optgroup label='$key'>";
                            foreach ($value as $k => $v) {
                                echo "<option value='$k'>$v</option>";
                            }
                            echo '</optgroup>';
                        }
                        ?>
                    </select>
                </div>
            </div>
            <div class="userProfileEntranceExams" style="display: none;">
                <h3>Entrance Exams Details</h3>
                <div class="examDetailGrid">
                    <div class="scheduledExamText modalInputLabelContainer">
                        <span class="spriteIcon questionListIcon"></span>
                        <label class="modalInputLabel">Have You Appeared or Scheduled For any
                            Entrance Exams?</label>
                    </div>
                    <div class="scheduledExamContainer modalInputContainer">
                        <div class="scheduledExamRow">
                            <div class="examItem">
                                <input checked class="scheduledExamRadio" type="radio" id="labelIDYes" name="scheduledExamOptions[]" value="yes">
                                <label class="scheduledExamOptionsLabel" for="labelIDYes">Yes</label>
                            </div>
                            <div class="examItem">
                                <input class="scheduledExamRadio" type="radio" id="labelIDNo" name="scheduledExamOptions[]" value="no">
                                <label class="scheduledExamOptionsLabel" for="labelIDNo">No</label>
                            </div>
                            <div class="examItem" style="display: none;">
                                <input class="scheduledExamRadio" type="radio" id="labelIDBooked" name="scheduledExamOptions[]" value="booked">
                                <label class="scheduledExamOptionsLabel" for="labelIDBooked">Booked</label>
                            </div>
                        </div>
                    </div>
                    <div class="examScoreContainer scoreInputContainer modalInputContainer fullWidthContainer">
                        <p class="enterScorePrompt">Please enter your exam score</p>
                        <div class="ajaxUserProfileExamLead"></div>
                        <p class="error errorExamMsg"></p>
                    </div>
                    <div class="examScoreContainer datepickerContainer modalInputContainer fullWidthContainer" style="display: none;">
                        <!-- <p class="enterScorePrompt">Please enter your exam booked date</p> -->
                        <div class="ajaxUserProfileExamLead"></div>
                        <p class="error errorExamMsg"></p>
                    </div>
                </div>
            </div>
            <h3>Education Budget</h3>
            <div class="selectBudgetDiv basicDetailForm">
                <div class="eduBudgetContainer modalInputContainer basicDetailField">
                    <select class="inputContainerField select2HookClass" name="educationBudget">
                        <option></option>
                        <?php
                        foreach (ArrayHelper::map(DataHelper::educationBudget(), 'value', 'name') as $key => $value) {
                            echo "<option value='$key'>$value</option>";
                        }
                        ?>
                    </select>
                    <span class="spriteIcon signupModalIcon courseIcon"></span>
                    <p class="error errorEducationMsg"></p>
                </div>
            </div>
            <div class="submitDetailButtonRow">
                <button class="btn-navigate-form-step" style="display: none;" id="userProfileSecondScreenSubmit" type="button">Save</button>
            </div>
        </section>
    </form>
    <form class="basicDetailSectionForm" name="basicDetailSectionThree" id="userProfilethirdScreen">
        <section class="educationDetailSection" style="display: none;">
            <h3>Education Details</h3>
            <ul class="educationDetailList">
                <li class="10thEducationDetailList" style="display: none;">
                    <input type="hidden" name="course10th" value="">
                    <input type="hidden" value="<?= 1 ?>" name="highest_qualification">
                    <input type="hidden" value=<?= 0 ?> name="qualification_level0">
                    <p class="detailItem">Add Class X Details</p>
                    <div class="detailContent">
                        <p class="accordianHeader">Class X Details
                            <span class="removeContent spriteIcon"></span>
                        </p>
                        <div class="detailContentGrid">
                            <div class="contentBox academicClasstenth">
                                <select class="inputContainerField inputBoardContainer educationRowItem" name="boardEntityTenth" required>
                                    <option value="" selected disabled></option>
                                </select>
                            </div>
                            <div class="contentBox yearClass">
                                <select class="select2 inputContainerField educationRowItem yearClass" name="yearValueTenth" required data-placeholder="Passing Year">
                                    <option></option>
                                    <?php
                                    for ($year = date('Y'); $year >= date('Y') - 10; $year--) {
                                        $sel = '';
                                        echo '<option value=' . $year . ' ' . $sel . '>' . date('Y', mktime(0, 0, 0, 0, 1, $year + 1)) . '</option>';
                                    }
                                    ?>
                                </select>
                                <p class="error errorMsgAcademic errortenthBoard"></p>
                            </div>
                            <div class="contentBox">
                                <input placeholder="Enter 10th Percentage" class="inputContainerField educationRowItem markClass" name="studentMarkTenth" type="number">
                            </div>
                        </div>
                        <div class="submitDetailButtonRow">
                            <button class="btn-navigate-form-step submit-btn userThirdScreenSubmit" style="display: none;" type="button">Save</button>
                        </div>
                    </div>
                </li>
                <li class="diplomaEducationDetailList" style="display: none;">
                    <input type="hidden" value="<?= 3 ?>" name="highest_qualification">
                    <input type="hidden" value=<?= 2 ?> name="qualification_level2">
                    <input type="hidden" name="courseDiploma" value="">
                    <p class="detailItem">Add Diploma Details</p>
                    <div class="detailContent">
                        <p class="accordianHeader">Diploma Details
                            <span class="removeContent spriteIcon"></span>
                        </p>
                        <div class="detailContentGrid">
                            <div class="contentBox educationDetailsRow academicClassDiploma modalInputContainer">
                                <select class="inputContainerField inputBoardContainer educationRowItem" name="boardEntityDiploma" required>
                                    <option value="" selected disabled></option>
                                </select>
                            </div>
                            <div class="contentBox yearClass">
                                <select class="select2 inputContainerField educationRowItem yearClass diploma" name="yearValueDiploma" id="errordiploma" required data-placeholder="Passing Year">
                                    <option></option>
                                    <?php
                                    for ($year = date('Y'); $year >= date('Y') - 10; $year--) {
                                        $sel = '';
                                        echo '<option value=' . $year . ' ' . $sel . '>' . date('Y', mktime(0, 0, 0, 0, 1, $year + 1)) . '</option>';
                                    }
                                    ?>
                                </select>
                                <p class="error errorMsgAcademic errordiploma"></p>
                            </div>
                            <div class="contentBox">
                                <input placeholder="Enter Diploma Percentage" class="inputContainerField educationRowItem markClass" name="studentMarkDiploma" type="number">
                            </div>
                        </div>
                        <div class="submitDetailButtonRow">
                            <button class="btn-navigate-form-step submit-btn userThirdScreenSubmit" style="display: none;" type="button">Save</button>
                        </div>
                    </div>
                </li>
                <li class="12thEducationDetailList" style="display: none;">
                    <input type="hidden" value="<?= 2 ?>" name="highest_qualification">
                    <input type="hidden" name="course12th" value="">
                    <input type="hidden" value=<?= 1 ?> name="qualification_level1">
                    <p class="detailItem">Add Class XII Details</p>
                    <div class="detailContent">
                        <p class="accordianHeader">12th Standard Details
                            <span class="removeContent spriteIcon"></span>
                        </p>
                        <div class="detailContentGrid">
                            <div class="contentBox educationDetailsRow academicClasstwelve modalInputContainer">
                                <select class="inputContainerField inputBoardContainer educationRowItem twelveth" name="boardEntityTwelve" required>
                                    <option value="" selected disabled></option>
                                </select>
                            </div>
                            <div class="contentBox yearClass">
                                <select class="select2 inputContainerField educationRowItem yearClass twelveth" id="errortwelve" name="yearValueTwelve" required data-placeholder="Passing Year">
                                    <option></option>
                                    <?php
                                    for ($year = date('Y'); $year >= date('Y') - 10; $year--) {
                                        $sel = '';
                                        echo '<option value=' . $year . ' ' . $sel . '>' . date('Y', mktime(0, 0, 0, 0, 1, $year + 1)) . '</option>';
                                    }
                                    ?>
                                </select>
                                <p class="error errorMsgAcademic errortwelveth"></p>
                            </div>
                            <div class="contentBox">
                                <input placeholder="Enter 12th Percentage" class="inputContainerField educationRowItem markClass" name="studentmarktweleve" type="number">
                            </div>
                            <div class="contentBox educationDetailsRow specializationClass">
                                <select class="select2 inputContainerField educationRowItem specializationClass twelveth" id="errortwelveSpecialization" name="tweleveSpecialization" required data-placeholder="Enter 12th Specialization">
                                    <option></option>
                                    <?php
                                    foreach (ArrayHelper::map(DataHelper::leadTwelveSpecialization(), 'value', 'name') as $key => $value) {
                                        echo "<option value='$key'>$value</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <div class="submitDetailButtonRow">
                            <button class="btn-navigate-form-step submit-btn userThirdScreenSubmit" style="display: none;" type="button">Save</button>
                        </div>
                    </div>
                </li>
                <li class="graduationEducationDetailList" style="display: none;">
                    <input type="hidden" value="<?= 4 ?>" name="highest_qualification">
                    <input type="hidden" name="courseGraduation" value="">
                    <input type="hidden" value=<?= 3 ?> name="qualification_level3">
                    <p class="detailItem">Add Graduation Details</p>
                    <div class="detailContent">
                        <p class="accordianHeader">Graduation Details
                            <span class="removeContent spriteIcon"></span>
                        </p>
                        <div class="detailContentGrid">
                            <div class="educationDetailsRow graduateClass contentBox">
                                <select class="inputContainerField educationRowItem" name="graduateCollege" required>
                                    <option value="" selected disabled></option>
                                </select>
                            </div>
                            <div class="contentBox yearClass">
                                <select class="select2 inputContainerField educationRowItem yearClass graduation" name="yearValueGraduation" id="errorgraduation" required data-placeholder="Passing Year">
                                    <option></option>
                                    <?php
                                    for ($year = date('Y'); $year >= date('Y') - 10; $year--) {
                                        $sel = '';
                                        echo '<option value=' . $year . ' ' . $sel . '>' . date('Y', mktime(0, 0, 0, 0, 1, $year + 1)) . '</option>';
                                    }
                                    ?>
                                </select>
                                <p class="error errorMsgAcademic errorgraduation"></p>
                            </div>
                            <div class="contentBox">
                                <input placeholder="Enter Percentage" class="inputContainerField educationRowItem" name="graduationMarks" type="number">
                            </div>
                            <div class="contentBox educationDetailsRow inputCourseGraduation modalInputContainer">
                                <select class="inputContainerField educationRowItem" name="graduationCourse" id="errorgraduationCourse" required>
                                    <option value="" selected disabled></option>
                                </select>
                            </div>
                        </div>
                        <div class="submitDetailButtonRow">
                            <button class="btn-navigate-form-step submit-btn userThirdScreenSubmit" style="display: none;" type="button">Save</button>
                        </div>
                    </div>
                </li>
                <li class="postEducationDetailList" style="display: none;">
                    <input type="hidden" value="<?= 5 ?>" name="highest_qualification">
                    <input type="hidden" name="coursePost" value="">
                    <input type="hidden" value=<?= 4 ?> name="qualification_level4">
                    <p class="detailItem">Add Post Graduation Details</p>
                    <div class="detailContent">
                        <p class="accordianHeader">Post Graduation Details
                            <span class="removeContent spriteIcon"></span>
                        </p>
                        <div class="detailContentGrid">
                            <div class="contentBox educationDetailsRow postGraduate modalInputContainer">
                                <select class="inputContainerField educationRowItem" name="postGraduateCollege" id="errorpostGraduationCollege" required>
                                    <option value="" selected disabled></option>
                                </select>
                            </div>
                            <div class="contentBox yearClass">
                                <select class="select2 inputContainerField educationRowItem yearClass postGraduation" name="yearValuePostGraduation" id="errorpostGraduation" required data-placeholder="Passing Year">
                                    <option></option>
                                    <?php
                                    for ($year = date('Y'); $year >= date('Y') - 10; $year--) {
                                        $sel = '';
                                        echo '<option value=' . $year . ' ' . $sel . '>' . date('Y', mktime(0, 0, 0, 0, 1, $year + 1)) . '</option>';
                                    }
                                    ?>
                                </select>
                                <p class="error errorMsgAcademic errorpostGraduation"></p>
                            </div>
                            <div class="contentBox">
                                <input placeholder="Enter Percentage" class="inputContainerField educationRowItem" name="postGraduationMarks" type="number">
                            </div>
                            <div class="educationDetailsRow inputCoursePostGraduation contentBox">
                                <select class="inputContainerField educationRowItem" name="postGraduationCourse" id="errorpostGraduationCourse" required>
                                    <option value="" selected disabled></option>
                                </select>
                            </div>
                        </div>
                        <div class="submitDetailButtonRow">
                            <button class="btn-navigate-form-step submit-btn userThirdScreenSubmit" style="display: none;" type="button">Save</button>
                        </div>
                    </div>
                </li>
            </ul>
        </section>
    </form>
    <form class="basicDetailSectionForm" name="basicDetailCommonSection" id="userProfileCommonScreen">
        <div class="submitDetailButtonRow">
            <button class="btn-navigate-form-step commonSaveButton" id="userProfileCommonSave" type="button">Save</button>
        </div>
    </form>
    <!-- <form class="basicDetailSectionForm" name="basicDetailSectionOtp" id="userProfileOtpScreen"> -->
    <div class="optSectionUser" style="display: none;">
        <i class="spriteIcon closeLeadFormUser"></i>
        <p class="headingTextUser"><?= 'Verify Mobile Number' ?></p>
        <p class="subHeadingUser"><?= 'OTP has been sent to your mobile number' ?> <span id="leadMobileOtpUser"></span></p>

        <div class="numberInputsUser">
            <div>
                <input onkeydown="return event.keyCode !== 69" id="otp-first-user" pattern="[0-9]*" class="otp-box-user" name="digitUser[]" type="number" min="0" max="9" step="1" aria-label="first digit" />
                <input onkeydown="return event.keyCode !== 69" id="otp-second-user" pattern="[0-9]*" class="otp-box-user" name="digitUser[]" type="number" min="0" max="9" step="1" aria-label="second digit" />
                <input onkeydown="return event.keyCode !== 69" id="otp-third-user" pattern="[0-9]*" class="otp-box-user" name="digitUser[]" type="number" min="0" max="9" step="1" aria-label="third digit" />
                <input onkeydown="return event.keyCode !== 69" id="otp-fourth-user" pattern="[0-9]*" class="otp-box-user" name="digitUser[]" type="number" min="0" max="9" step="1" aria-label="fourth digit" />
            </div>
            <p class="text-danger errorMsg" id="otpResponseText"></p>
        </div>
        <div class="row">
            <button class="primaryBtn verifyOtpUser"><?= 'VERIFY OTP' ?></button>
            <p class="pb-0">
                <?= 'Didn’t receive the OTP' ?>?
                <a href="javascript:void(0)" id="resendOtpUser"><?= 'Resend it' ?>.</a>
                <span id="resendTimer"></span>
            </p>
        </div>
    </div>
    <!-- </form> -->
</div>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>