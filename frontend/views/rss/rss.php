<?php
use frontend\helpers\Url;
?>
<?xml version='1.0' encoding='UTF-8'?>
<rss xmlns:atom="http://www.w3.org/2005/Atom" xmlns:slash="http://purl.org/rss/1.0/modules/slash/"  version='2.0'>
<channel>
<title>GetMyUni - Explore Top Colleges, Courses, Fees and Exams</title>
<link><?= Url::toDomain() ?></link>
<description>GetMyUni - Explore Top Colleges, Courses, Fees and Exams</description>
<language>en-us</language>
<image>
<url><?= Yii::$app->params['gmuLogo']?></url>
<title>GetMyUni - Explore Top Colleges, Courses, Fees and Exams</title>
<link><?= Url::toDomain() ?></link>
</image>
<copyright>© <?= date('Y'); ?> Getmyuni.com. All Rights Reserved.</copyright>
<atom:link rel="self" type="application/rss+xml" href="<?= Url::toDomain() ?>rss/news-feed.xml"/>
    <?php if (!empty($rssFeeds)):
        foreach ($rssFeeds as $rssFeed):
            ?>
    <item>
        <link><?= Url::toDomain() ?>news/<?= $rssFeed->slug; ?></link>
        <title><?= htmlspecialchars($rssFeed->name); ?></title>
        <puDate><?= isset($rssFeed->published_at) && !empty($rssFeed->published_at) ? date(DATE_RSS, strtotime($rssFeed->published_at)) : $rssFeed->created_at;  ?></puDate>
    </item>
            <?php
        endforeach;
    endif;
    ?>
</channel>
</rss>
