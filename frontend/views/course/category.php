<?php

use common\helpers\ContentHelper;
use common\helpers\CourseHelper;
use common\helpers\DataHelper;
use common\models\Course;
use common\models\Lead;
use common\models\LiveUpdate;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Url;
use frontend\helpers\Lead as HelpersLead;

$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = !empty($content->author->profile->image) ? Yii::getAlias('@profileDPFrontend') . '/' . $content->author->profile->image : '/yas/images/usericon.png';

$defaultMeta = CourseHelper::getDefaultSeoInfo('category-page', $stream->name);
$this->title = !empty($content->meta_title) ? $content->meta_title : $defaultMeta['title'];
$this->context->description = !empty($content->meta_description) ? $content->meta_description : $defaultMeta['description'];

// breadcrumbs
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'Courses', 'url' => [Url::toCourse()], 'title' => 'Courses'];
$this->params['breadcrumbs'][] = $stream->name . ' Courses';

//Css link
// $this->registerCssFile('/yas/css/version2/course_category.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'course_category.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', ['depends' => [AppAsset::class]]);
// $this->registerCssFile('/yas/css/version2/side_bar.css', ['depends' => [AppAsset::class]]);
// $this->registerCssFile('/yas/css/version2/side_bar.css', [
//     'depends' => [AppAsset::class],
//     'media' => 'print',
// 'onload' => 'this.media="all"'
// ], 'sidebar-css-theme');

$this->params['entity'] = Course::ENTITY_COURSE_STREAM;
$this->params['entity_id'] = $stream->id ?? 0;
$this->params['entity_name'] = $stream->name ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['pageName'] = 'course-category' ?? '';
$defaultEmptyCondition1 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])));
?>
<div class="courseCategoryPage">
    <?php if (!$isMobile): ?>
        <h1 class="desktopOnly"><?= !empty($content->h1) ? $content->h1 : $defaultMeta['h1'] ?></h1>
        <?php if (!empty($content)): ?>
            <div class="courseHeroSection pageData pageInfo">
                <?php if (isset($content->author->slug)): ?>
                    <div class="updated-info row">
                        <div class="updatedBy">
                            <img class="lazyload" width="36" height="36" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" loading="lazy" alt="<?= !empty($content->author) ? $content->author->name : '' ?>">
                            <p>
                                <a class="authorName" href="<?= Url::toAllAuthorPost($content->author->slug) ?>"><?= !empty($content->author) ? $content->author->name : '' ?></a>
                            </p>
                            <p class="desktopOnly">,
                                <?= Yii::$app->formatter->asDate($content->updated_at ?? 'today') ?>
                            </p>
                        </div>
                    </div>
                <?php endif; ?>
                <?= ContentHelper::htmlDecode(DataHelper::parseDomainUrlInContent($content->content)) ?>
            </div>
        <?php endif; ?>
    <?php else: ?>
        <div class="courseHeroSection pageData pageInfo">
            <h1 class="mobileOnly"><?= $content->h1 ?? $defaultMeta['h1'] ?></h1>
            <?php if (isset($content->author->slug)): ?>
                <div class="updated-info row">
                    <div class="updatedBy">
                        <img class="lazyload" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" width="36" height="36" loading="lazy" alt="<?= !empty($content->author) ? $content->author->name : '' ?>">
                        <p>
                            <a class="authorName" href="<?= Url::toAllAuthorPost($content->author->slug) ?>"><?= !empty($content->author) ? $content->author->name : '' ?>,</a>
                            <span class="mobileOnly">
                                Updated on <?= Yii::$app->formatter->asDate($content->updated_at ?? 'today') ?>
                            </span>
                        </p>
                    </div>
                </div>
            <?php endif; ?>
            <?php if (!empty($content)): ?>
                <?= ContentHelper::htmlDecode(DataHelper::parseDomainUrlInContent($content->content)) ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    <div class="row">
        <div class="col-md-8">
            <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php echo Ad::unit('GMU_COURSES_CATEGORY_PAGE_ATF_WEB_720x90', '[720,90]') ?>
                        </div>
                    </div>
                </aside>
            <?php endif; ?>

            <?php if (!empty($degreeCourse['tab'])): ?>
                <h2 class="desktopOnly">Browse <?= $stream->name ?> Courses</h2>
                <h2 class="text-center mobileOnly">Browse <?= $stream->name ?> Courses</h2>

                <div class="pageRedirectionLinks courseCategoryType">
                    <p class="btn_left over">
                        <i class="spriteIcon left_angle"></i>
                    </p>
                    <?php if (count($degreeCourse['section']) > 6  && !$isMobile): ?>
                        <p class="btn_right">
                            <i class="spriteIcon right_angle"></i>
                        </p>
                    <?php endif; ?>
                    <?php if (count($degreeCourse['section']) > 3 && $isMobile): ?>
                        <p class="btn_right">
                            <i class="spriteIcon right_angle"></i>
                        </p>
                    <?php endif; ?>
                    <ul class="tabList">
                        <li data-tab="all" class="activeLink">ALL</li>
                        <?php foreach ($degreeCourse['section'] as $key => $value): ?>
                            <li data-tab="<?= str_replace('+', '_', $key) ?>" class=""><?= $value ?></li>
                            <?php
                        endforeach; ?>
                    </ul>
                </div>

                <?php if ($isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="lead-cta" data-entity="course" data-lead_cta="5" data-description="<?= $stream->name ?>" data-location="<?= $defaultEmptyCondition1 ? 'courses_category_scholarship_wap_top_sticky_cta' : $dynamicCta['cta_position_1']['wap'] ?>" data-streamid="<?= $stream->id ?>"></div>
                <?php endif; ?>
                <?= $this->render('partials/_course-card', [
                    'courses' => $degreeCourse['tab']
                ]) ?>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <aside class="mobileOnly">
                        <div class="horizontalRectangle">
                            <div class="appendAdDiv" style="background:#EAEAEA;">
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x250')
                                ?>
                            </div>
                        </div>
                    </aside>
                <?php endif; ?>
                <div class="removeFixedQuickLink">
                    <!-- Do not Delete this -->
                </div>
            <?php endif; ?>
        </div>

        <div class="col-md-4">
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="lead-cta" data-entity="course" data-lead_cta="2" data-description="<?= $stream->name ?>" data-streamid="<?= $stream->id ?>"></div>
            <?php endif; ?>
            <aside class="desktopOnly">
                <?php if (!empty($colleges)): ?>
                    <?= $this->render('partials/_college-list.php', [
                        'collegeList' => $colleges,
                        'stream' => $stream
                    ]) ?>
                <?php endif; ?>

                <?php if (!empty($exams)): ?>
                    <?= $this->render('partials/_exam-list.php', [
                        'examList' => $exams,
                        'stream' => $stream
                    ]) ?>
                <?php endif; ?>

                <?php if (!$isMobile): ?>
                    <?php if (!empty($featuredNews) || !empty($recentNews)): ?>
                        <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                            'featured' => $featuredNews,
                            'recents' => $recentNews,
                            'isAmp'  => 0,
                            'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                            'smallIcone' => 1
                        ]); ?>
                    <?php endif; ?>

                    <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                        <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                            'trendings' => $trendingArticles,
                            'recentArticles' => $recentArticles,
                        ]); ?>
                    <?php endif; ?>
                <?php endif; ?>

            </aside>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <?= $this->render('partials/_sidebar-ads.php', [
                    'ads' => [
                        ['slot' => 'GMU_COURSES_CATEGORY_PAGE_MTF1_WEB_300x250', 'size' => '[300, 250]', 'isMobile' => false],
                        ['slot' => 'GMU_COURSES_CATEGORY_PAGE_MTF2_WEB_300x250', 'size' => '[300, 250]', 'isMobile' => false],
                        ['slot' => 'GMU_COURSES_CATEGORY_PAGE_MTF2_WAP_300x250', 'size' => '[300, 250]', 'isMobile' => true],
                        ['slot' => 'GMU_COURSES_CATEGORY_PAGE_MTF3_WAP_300x250', 'size' => '[300, 250]', 'isMobile' =>  true]
                    ]
                ]) ?>
            <?php endif; ?>
        </div>
    </div>
    <?php /**
    <?= $this->render('partials/_course-category.php') ?>
     */ ?>
    <?php if (!empty($articles)): ?>
        <?= $this->render('partials/_article.php', [
            'articles' => $articles,
            'title' => 'Latest ' . $stream->name . ' Articles',
            'url' => Url::toArticleDetail($articleCatSlug)
        ]) ?>
    <?php endif; ?>
    <aside class="mobileOnly">
        <?php if (!empty($colleges)): ?>
            <?= $this->render('partials/_college-list.php', [
                'colleges' => $colleges,
                'stream' => $stream
            ]) ?>
        <?php endif; ?>

        <?php if (!empty($exams)): ?>
            <?= $this->render('partials/_exam-list.php', [
                'exams' => $exams,
                'stream' => $stream
            ]) ?>
        <?php endif; ?>

        <?php if ($isMobile): ?>
            <?php if (!empty($featuredNews) || !empty($recentNews)): ?>
                <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                    'featured' => $featuredNews,
                    'recents' => $recentNews,
                    'isAmp'  => 0,
                    'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                    'smallIcone' => 1
                ]); ?>
            <?php endif; ?>

            <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                    'trendings' => $trendingArticles,
                    'recentArticles' => $recentArticles,
                ]); ?>
            <?php endif; ?>
        <?php endif; ?>

    </aside>
    <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                        ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90')
                        ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>
</div>