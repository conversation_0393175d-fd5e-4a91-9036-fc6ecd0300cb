<?php

use common\helpers\CollegeHelper;
use common\services\UserService;
use common\models\Lead;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
?>
<div class="courseCategoryList">
    <div id="all" class="tab-content activeLink">
        <?php $count = 1;
        foreach ($courses as $course):
            if ($count <= 30):
                ?>
                <div class="courseCategoryCard <?= str_replace('+', '_', $course['degree']) ?>_active">
                    <div class="row">
                        <h3>
                            <a href="<?= Url::toCourseDetail($course['slug']) ?>" title="<?= $course['name'] ?>"><?= $course['name'] ?></a>
                        </h3>
                        <div class="courseInfo mobileOnly">
                            <ul>
                                <?php if (!empty($course['avg_fees'])): ?>
                                    <li><span><i class="spriteIcon feesIcons"></i>Average fee</span><?= $course['avg_fees'] ?></li>
                                <?php endif;
                                if (!empty($course['duration'])):
                                    ?>
                                    <li><span><i class="spriteIcon courseDuratioIcon"></i>Duration</span><?= $course['duration'] ?></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <?php if (!$isMobile): ?>
                            <div class="applyClg desktopOnly">
                                <div class="lead-cta" id="<?= $course['id'] ?>" data-slug="<?= $course['slug'] . '-3' ?>" data-entity="course" data-lead_cta="3" data-entityid="<?= $course['id'] ?>" data-streamid="<?= $course['stream'] ?>" data-location="<?= UserService::parseDynamicCta('courses_{name}_{slug}_web_banner_cta1', $course['slug'], 'course-category') ?>" data-image="<?= Url::defaultCollegeLogo() ?>" data-description="<?= $course['name'] ?>"></div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="row">
                        <div class="courseInfo desktopOnly">
                            <ul>
                                <?php if (!empty($course['avg_fees'])): ?>
                                    <li><span><i class="spriteIcon feesIcons"></i>Average fee</span><?= $course['avg_fees'] ?></li>
                                <?php endif;
                                if (!empty($course['duration'])):
                                    ?>
                                    <li><span><i class="spriteIcon courseDuratioIcon"></i>Duration</span><?= CollegeHelper::yearsFormat($course['duration']) ?? '--' ?></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <ul>
                            <?php if (!empty($course['page'])):
                                foreach ($course['page'] as $key => $value): ?>
                                    <li><a href="<?= Url::toCourseDetail($course['slug'], $key) ?>" title="<?= $value ?>"><?= $value ?></a></li>
                                <?php endforeach;
                            endif; ?>
                            <?php if (!empty($course['college_count'])): ?>
                                <li><a href="<?= Url::toCourseFilter($course['college_count']) ?>" title="College Offering">College Offering</a></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <?php if ($isMobile): ?>
                        <div class="applyClg mobileOnly">
                            <div class="lead-cta" id="<?= $course['id'] ?>" data-slug="<?= $course['slug'] . '-4' ?>" data-entity="course" data-lead_cta="4" data-entityid="<?= $course['id'] ?>" data-streamid="<?= $course['stream'] ?>" data-location="<?= UserService::parseDynamicCta('courses_{name}_{slug}_wap_banner_cta1', $course['slug'], 'course-category') ?>" data-image="<?= Url::defaultCollegeLogo() ?>" data-description="<?= $course['name'] ?>"></div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="courseCategoryCard <?= str_replace('+', '_', $course['degree']) ?>_active hide">
                    <div class="row">
                        <h3>
                            <a href="<?= Url::toCourseDetail($course['slug']) ?>" title="<?= $course['name'] ?>"><?= $course['name'] ?></a>
                        </h3>
                        <div class="courseInfo mobileOnly">
                            <ul>
                                <?php if (!empty($course['avg_fees'])): ?>
                                    <li><span><i class="spriteIcon feesIcons"></i>Average fee</span><?= $course['avg_fees'] ?></li>
                                <?php endif;
                                if (!empty($course['duration'])):
                                    ?>
                                    <li><span><i class="spriteIcon courseDuratioIcon"></i>Duration</span><?= $course['duration'] ?></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <?php if (!$isMobile): ?>
                            <div class="applyClg desktopOnly">
                                <div class="lead-cta" id="<?= $course['id'] ?>" data-slug="<?= $course['slug'] . '-3' ?>" data-entity="course" data-lead_cta="3" data-entityid="<?= $course['id'] ?>" data-streamid="<?= $course['stream'] ?>" data-location="<?= UserService::parseDynamicCta('courses_{name}_{slug}_web_banner_cta1', $course['slug'], 'course-category') ?>" data-image="<?= Url::defaultCollegeLogo() ?>" data-description="<?= $course['name'] ?>"></div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="row">
                        <div class="courseInfo desktopOnly">
                            <ul>
                                <?php if (!empty($course['avg_fees'])): ?>
                                    <li><span><i class="spriteIcon feesIcons"></i>Average fee</span><?= $course['avg_fees'] ?></li>
                                <?php endif;
                                if (!empty($course['duration'])):
                                    ?>
                                    <li><span><i class="spriteIcon courseDuratioIcon"></i>Duration</span><?= CollegeHelper::yearsFormat($course['duration']) ?? '--' ?></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <ul>
                            <?php if (!empty($course['page'])):
                                foreach ($course['page'] as $key => $value): ?>
                                    <li><a href="<?= Url::toCourseDetail($course['slug'], $key) ?>" title="<?= $value ?>"><?= $value ?></a></li>
                                <?php endforeach;
                            endif; ?>
                            <?php if (!empty($course['college_count'])): ?>
                                <li><a href="<?= Url::toCourseFilter($course['college_count']) ?>" title="College Offering">College Offering</a></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <?php if ($isMobile): ?>
                        <div class="applyClg mobileOnly">
                            <div class="lead-cta" id="<?= $course['id'] ?>" data-slug="<?= $course['slug'] . '-4' ?>" data-entity="course" data-lead_cta="4" data-entityid="<?= $course['id'] ?>" data-streamid="<?= $course['stream'] ?>" data-location="<?= UserService::parseDynamicCta('courses_{name}_{slug}_wap_banner_cta1', $course['slug'], 'course-category') ?>" data-image="<?= Url::defaultCollegeLogo() ?>" data-description="<?= $course['name'] ?>"></div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            <?php $count++;
        endforeach; ?>
    </div>
    <?php if (count($courses) > 30): ?>
        <div class="col-12 p-0">
            <button class="viewAllStream" id="viewCourses">View All Courses</button>
        </div>
    <?php endif; ?>
    <aside>
        <div class="horizontalRectangle desktopOnly">
            <div class="appendAdDiv" style="background:#EAEAEA;">
            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
            </div>
        </div>
    </aside>
</div>