<?php

use common\services\UserService;
use common\models\Lead;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
//article (product mapping) cta location
$defaultEmptyCondition0 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_0']) || empty(array_filter($dynamicCta['cta_position_0'])));
$defaultEmptyCondition1 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])));
$defaultEmptyCondition2 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_2']) || empty(array_filter($dynamicCta['cta_position_2'])));
$defaultEmptyCondition3 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_3']) || empty(array_filter($dynamicCta['cta_position_3'])));
?>

<?php if (isset($lead_cta) && $lead_cta == 0): ?>
    <div class="ctaColumn">
        <div class="ctaRow">
                <?= frontend\helpers\Html::leadButton(
                    $defaultEmptyCondition0 ? 'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now'),
                    [
                        'entity' => $entity,
                        'entityId' => $entity_id,
                        'ctaLocation' => $isMobile ? ($defaultEmptyCondition0 ? UserService::parseDynamicCta('courses_{name}_{slug}_wap_top_left_cta', $slug, $pageName) : $dynamicCta['cta_position_0']['wap']) : ($defaultEmptyCondition0 ? UserService::parseDynamicCta('courses_{name}_{slug}_web_top_left_cta', $slug, $pageName) : $dynamicCta['cta_position_0']['web']),
                        'ctaText' => $defaultEmptyCondition0 ?  'Get ' . $name . ' Course Guide' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Get ' . $name . ' Course Guide'),
                        'leadformtitle' => $defaultEmptyCondition0 ? 'Register to Get Course Guide' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'Register to Get Course Guide'),
                        'subheadingtext' => $defaultEmptyCondition0 ? $name : ($dynamicCta['cta_position_0']['lead_form_description'] ?? $name),
                        'image' => Url::defaultCollegeLogo(),
                        'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'],
                    ],
                    ['class' => 'primaryBtn applyNowButton leadCourseCapture']
                ) ?>
                <?= frontend\helpers\Html::leadButton(
                    $defaultEmptyCondition1 ?  'Get ' . $name . ' Course Guide' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Get ' . $name . ' Course Guide'),
                    [
                        'entity' => Lead::ENTITY_COURSE,
                        'entityId' => $entity_id,
                        'ctaLocation' => $isMobile ? ($defaultEmptyCondition1 ? UserService::parseDynamicCta('courses_{name}_{slug}_wap_top_right_cta', $slug, $pageName) : $dynamicCta['cta_position_1']['wap']) : ($defaultEmptyCondition1 ? UserService::parseDynamicCta('courses_{name}_{slug}_web_top_right_cta', $slug, $pageName) : ($dynamicCta['cta_position_1']['web'] ?? null)),
                        'ctaText' => $defaultEmptyCondition1 ?  'Get ' . $name . ' Course Guide' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Get ' . $name . ' Course Guide'),
                        'leadformtitle' => $defaultEmptyCondition1 ? 'Register to Get Course Guide' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'Register to Get Course Guide'),
                        'subheadingtext' => $defaultEmptyCondition1 ? $name : ($dynamicCta['cta_position_1']['lead_form_description'] ?? $name),
                        'image' => Url::defaultCollegeLogo(),
                        'redirection' => $defaultEmptyCondition1 ? null : $dynamicCta['cta_position_1']['page_link'],
                    ],
                    ['class' => ' primaryBtn getLeadForm leadCourseCapture']
                ) ?>
        </div>
    </div>
<?php endif; ?>
<?php if ($isMobile && isset($lead_cta) && $lead_cta == 1): ?>
    <div class="mobileOnly primaryBtn brochureBtn">
        <?= frontend\helpers\Html::leadButton(
            $defaultEmptyCondition0 ?  'Get ' . $name . ' Course Guide' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Get ' . $name . ' Course Guide'),
            [
                'entity' => $entity,
                'entityId' => $entity_id,
                'ctaLocation' => $defaultEmptyCondition0 ? UserService::parseDynamicCta('courses_{name}_{slug}_wap_top_left_cta', $slug, $pageName) : $dynamicCta['cta_position_0']['wap'] ?? null,
                'ctaText' => $defaultEmptyCondition0 ?  'Get ' . $name . ' Course Guide' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Get ' . $name . ' Course Guide'),
                'leadformtitle' => $defaultEmptyCondition0 ? 'Register to Get Course Guide' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'Register to Get Course Guide'),
                'subheadingtext' => $defaultEmptyCondition0 ? $name : ($dynamicCta['cta_position_0']['lead_form_description'] ?? $name),
                'image' => Url::defaultCollegeLogo(),
                'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link']
            ],
            ['class' => 'primaryBtn getLeadForm leadCourseCapture']
        ) ?>
    </div>
<?php endif; ?>
<?php if (isset($lead_cta) && $lead_cta == 2): ?>
    <div class="getSupport <?= $class ?? '' ?>">
        <!-- <p class="getSupport__subheading">Are You Interested In This Course?</p> -->
        <div class="button__row__container">
            <?= frontend\helpers\Html::leadButton(
                $defaultEmptyCondition2 ? 'Apply Now' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'Apply Now'),
                [
                    'entity' => $entity,
                    'entityId' => $entity_id,
                    'ctaLocation' => $isMobile ? ($defaultEmptyCondition2 ? UserService::parseDynamicCta('course_{name}_wap_bottom_left_cta', $slug, $pageName) : $dynamicCta['cta_position_2']['wap']) : ($defaultEmptyCondition2 ? UserService::parseDynamicCta('course_{name}_{slug}_web_bottom_left_cta', $slug, $pageName) : $dynamicCta['cta_position_2']['web']),
                    'ctaText' => $defaultEmptyCondition2 ? 'Apply Now' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'Apply Now'),
                    'leadformtitle' => $defaultEmptyCondition2 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_2']['lead_form_title'] ?? 'REGISTER TO APPLY'),
                    'subheadingtext' => $defaultEmptyCondition2 ? $name : ($dynamicCta['cta_position_2']['lead_form_description'] ?? $name),
                    'image' => '/yas/images/defaultcardbanner.png',
                    'redirection' => $defaultEmptyCondition2 ? null : (!empty($dynamicCta['cta_position_2']['page_link']) ? $dynamicCta['cta_position_2']['page_link'] : '')
                ],
                ['class' => 'talkToExpert applyNowCourse getLeadForm leadCourseCapture']
            ) ?>
            <?= frontend\helpers\Html::leadButton(
                $defaultEmptyCondition3 ? 'CHECK ELIGIBILITY' : ($dynamicCta['cta_position_3']['cta_text'] ?? 'CHECK ELIGIBILITY'),
                [
                    'entity' => $entity,
                    'entityId' => $entity_id,
                    'ctaLocation' => $isMobile ? ($defaultEmptyCondition3 ? UserService::parseDynamicCta('course_{name}_wap_bottom_right_cta', $slug, $pageName) : $dynamicCta['cta_position_3']['wap']) : ($defaultEmptyCondition3 ? UserService::parseDynamicCta('course_{name}_{slug}_web_bottom_right_cta', $slug, $pageName) : $dynamicCta['cta_position_3']['web']),
                    'ctaText' => $defaultEmptyCondition3 ? 'CHECK ELIGIBILITY' : ($dynamicCta['cta_position_3']['cta_text'] ?? 'CHECK ELIGIBILITY'),
                    'leadformtitle' => $defaultEmptyCondition3 ? 'REGISTER TO GET EXAM ALERTS' : ($dynamicCta['cta_position_3']['lead_form_title'] ?? 'REGISTER TO GET EXAM ALERTS'),
                    'subheadingtext' => $defaultEmptyCondition3 ? $name : ($dynamicCta['cta_position_3']['lead_form_description'] ?? $name),
                    'image' => '/yas/images/defaultcardbanner.png',
                    'redirection' => $defaultEmptyCondition3 ? null : (!empty($dynamicCta['cta_position_3']['page_link']) ? $dynamicCta['cta_position_3']['page_link'] : '')
                ],
                ['class' => 'applyNow getLeadForm leadCourseCapture',]
            ) ?>
        </div>
    </div>
<?php endif; ?>

<?php if (!$isMobile && isset($lead_cta) && $lead_cta == 3):
    ;
    ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition0 ?  'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $defaultEmptyCondition0 ? UserService::parseDynamicCta('courses_{name}_{slug}_web_banner_cta1', $slug, $pageName) : $dynamicCta['cta_position_0']['web'],
            'ctaText' => $defaultEmptyCondition0 ?  'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now'),
            'leadformtitle' => $defaultEmptyCondition0 ? 'Register to Get Course Guide' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'Register to Get Course Guide'),
            'subheadingtext' => $defaultEmptyCondition0 ? $name : ($dynamicCta['cta_position_0']['lead_form_description'] ?? $name),
            'image' => Url::defaultCollegeLogo(),
            'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'],
        ],
        ['class' => 'primaryBtn applyNow getLeadForm leadCourseCapture removeCta']
    ) ?>
<?php endif; ?>

<?php if ($isMobile && isset($lead_cta) && $lead_cta == 4): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition0 ? 'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $defaultEmptyCondition0 ? UserService::parseDynamicCta('courses_category_wap_{slug}_card_mtf_cta1', '', $slug) : $dynamicCta['cta_position_0']['wap'],
            'ctaText' => $defaultEmptyCondition0 ? 'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now'),
            'leadformtitle' => $defaultEmptyCondition0 ? 'Register to Get Course Guide' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'Register to Get Course Guide'),
            'subheadingtext' => $defaultEmptyCondition0 ? $name : ($dynamicCta['cta_position_0']['lead_form_description'] ?? $name),
            'image' => Url::defaultCollegeLogo(),
            'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'],
        ],
        ['class' => 'primaryBtn applyNow getLeadForm leadCourseCapture removeCta',]
    ) ?>
<?php endif; ?>

<?php if ($isMobile && isset($lead_cta) && $lead_cta == 5): ?>
    <div class="mobileOnly primaryBtn brochureBtn get1Lakhs">
        <div class="applyClg mobileOnly">
            <?= frontend\helpers\Html::leadButton(
                $defaultEmptyCondition1 ? 'Get ₹1 Lakh scholarship' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Get ₹1 Lakh scholarship'),
                [
                    'entity' => $entity,
                    'entityId' => null,
                    'ctaLocation' => $defaultEmptyCondition1 ? 'courses_category_scholarship_wap_top_sticky_cta' : $dynamicCta['cta_position_1']['wap'],
                    'ctaText' => $defaultEmptyCondition1 ? 'Get 1 Lakh scholarship' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Get 1 Lakh scholarship'),
                    'leadformtitle' => $defaultEmptyCondition1 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'REGISTER TO APPLY'),
                    'subheadingtext' => $defaultEmptyCondition1 ? $name : ($dynamicCta['cta_position_1']['lead_form_description'] ?? $name),
                    'image' => Url::defaultCollegeLogo(),
                    'redirection' => $defaultEmptyCondition1 ? null : $dynamicCta['cta_position_1']['page_link']
                ],
                ['class' => 'primaryBtn getLeadForm leadCourseCapture',]
            ) ?>
        </div>
    </div>
<?php endif; ?>