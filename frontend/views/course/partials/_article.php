<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use common\helpers\DataHelper;
?>
<section class="pageData">
    <h2 class="row"><?= $title ?></h2>
    <div class="customSlider four-cardDisplay">
        <?php if (count($articles) > 4): ?>
            <i class="spriteIcon scrollLeft over"></i>
            <i class="spriteIcon scrollRight"></i>
        <?php endif; ?>
        <div class="customSliderCards">
            <?php foreach ($articles as $article):
                ?>
                <div class="sliderCardInfo">
                    <a href="<?= Url::toArticleDetail($article['slug'], DataHelper::getLangCode($article['lang_code'])) ?>" title="<?= $article['h1'] ?>">
                        <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toArticleDetail($article['slug'], DataHelper::getLangCode($article['lang_code'])) ?>')" data-src="<?= $article['cover_image'] ? ArticleDataHelper::getImage($article['cover_image']) : ArticleDataHelper::getImage() ?>" src="<?= $article['cover_image'] ? ArticleDataHelper::getImage($article['cover_image']) : ArticleDataHelper::getImage() ?>" alt="<?= $article['h1'] ?>" />
                        <div class="textDiv">
                            <p class="widgetCardHeading"><?= $article['h1'] ?></p>
                        </div>
                    </a>
                    <div class="authorAndDate">
                        <a class="authorName" href="<?= Url::toAllAuthorPost($article['user_slug'], DataHelper::getLangCode($article['lang_code'])) ?>" title="<?= $article['name'] ?>">
                            <?= isset($article['name']) && !empty($article['name']) ? $article['name'] : $article['username'] ?? '' ?>
                        </a>
                        <?php if (!empty($article['updated_at'])): ?>
                            <p class="widgetAUthorName"><?= Yii::$app->formatter->asDate($article['updated_at']) ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
            <div class="sliderCardInfo">
                <div class="viewAllDiv">
                    <a href="<?= $url ?>"><i class="spriteIcon viewAllIcon"></i>VIEW ALL</a>
                </div>
            </div>
        </div>
</section>