<?php

use common\helpers\ContentHelper;
use common\helpers\CourseHelper;
use common\helpers\DataHelper;
use common\models\Course;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Url;
use yii\helpers\Inflector;
use yii\helpers\StringHelper;

$defaultMeta = CourseHelper::getDefaultSeoInfo('ladning-page');
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->title = ($content == false) ? $defaultMeta['title'] : (!empty($content->title) ? DataHelper::parseMetaTopContent($content->title) : $defaultMeta['title']);
$this->context->description = ($content == false) ? $defaultMeta['description'] : (!empty($content->description) ? DataHelper::parseMetaTopContent($content->description) : $defaultMeta['description']);

// breadcrumbs
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = 'Courses';
$this->registerCssFile(Yii::$app->params['cssPath'] . 'course.css', ['depends' => [AppAsset::class]]);

$this->params['entity'] = Course::ENTITY_COURSE;
$this->params['entity_name'] = 'Courses-Listing-page';
?>

<style>
    @media (max-width: 1023px) {
        .pageFooter .footerSecondSection {
            padding-bottom: 20px;
        }

        .pageFooter {
            padding-bottom: 0px;
        }
    }
</style>
<header class="courseHeroSection  courseHeroSection-landing">
    <div class="row">
        <div class="col-md-7">
            <h1><?= ($content == false) ? $defaultMeta['h1'] : (!empty($content->h1) ? DataHelper::parseMetaTopContent($content->h1) : $defaultMeta['h1']); ?></h1>
            <div class="searchBar">
                <input class="searchForExam search-autocomplete" id="autoComplete" data-type="course" autocomplete="off" placeholder="Search for Courses, Degree or Certification" type="text" tabindex="1">
                <div class="selection"></div>
            </div>
            <?php if (!empty($popularCourse)): ?>
                <p>Trending Courses</p>
                <div class="poplarCourse">
                    <ul class="">
                        <?php foreach ($popularCourse as $course): ?>
                            <li><a href="<?= Url::toCourseDetail($course->slug) ?>" title="<?= $course->name ?>"><?= $course->short_name ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        <div class="col-md-5">
            <?php /*<?= frontend\helpers\Html::leadButton(
                '<i class="spriteIcon mirroringIcon"></i>COMPARE COURSES',
                [
                    'entity' => Lead::ENTITY_COURSE,
                    'entityId' => '',
                    'ctaLocation' => HelpersLead::getCTAsName(Lead::ENTITY_COURSE . '.course-compare'),
                    'leadformtitle' => 'Course Landing Page',
                    'subheadingtext' => 'todo', //to do
                    'image' => '/yas/images/defaultcardbanner.png'
                ],
                ['class' => ' desktopOnly primaryBtn getLeadForm ']
            ) ?>*/ ?>
        </div>
    </div>
</header>

<?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
    <aside>
        <div class="horizontalRectangle">
            <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                <?php echo Ad::unit('GMU_COURSES_LANDING_PAGE_ATF_WEB_720x90', '[720,90]') ?>
            </div>
        </div>
    </aside>
<?php endif; ?>
<?php /*<div class="mobileOnly primaryBtn brochureBtn">
    <?= frontend\helpers\Html::leadButton(
        '<i class="spriteIcon mirroringIcon"></i>COMPARE COURSES',
        [
            'entity' => Lead::ENTITY_COURSE,
            'entityId' => '',
            'ctaLocation' => HelpersLead::getCTAsName(Lead::ENTITY_COURSE . '.course-compare'),
            'leadformtitle' => 'Course Landing Page',
            'subheadingtext' => 'todo', //to do
            'image' => '/yas/images/defaultcardbanner.png'
        ],
        ['class' => 'primaryBtn getLeadForm ']
    ) ?>
</div>*/ ?>
<?php if (isset($content) && !empty($content->top_content)): ?>
    <div class="pageData pageInfo" id="pageInfo">
        <h2>Explore course by stream</h2>
        <?= ContentHelper::htmlDecode(DataHelper::parseMetaTopContent(
            DataHelper::parseDomainUrlInContent($content->top_content)
        )) ?>
    </div>
<?php endif; ?>
<div class="courseCardList row">
    <?php $i = 1;
    foreach (CourseHelper::$coursesList as $discipline => $courses): ?>
        <?php $disciplineSlug = Inflector::slug(StringHelper::truncateWords($discipline, 1)) ?>
        <div class="courseCard <?= ($i < 10) ? '' : 'hideStreamCard' ?>">
            <div class="row">
                <span onclick="gmu.url.goto('<?= Url::toCoursesStream($disciplineSlug) ?>')" title="<?= $discipline ?> Courses in india" class="courseSprite <?= $disciplineSlug ?>"></span>
                <h3>
                    <a class="courseTitle" href="<?= Url::toCoursesStream($disciplineSlug) ?>" title="<?= $discipline ?> Courses in india">
                        <?= $discipline ?>
                    </a>
                </h3>
            </div>

            <div class="row pb-0">
                <?php foreach ($courses as $course): ?>
                    <a href="<?= Url::toCourseDetail($course['slug']) ?>" title="<?= $course['title'] ?>" class="courseName"><?= $course['title'] ?></a>
                <?php endforeach; ?>
            </div>
            <a href="<?= Url::toCoursesStream($disciplineSlug) ?>" title="<?= $discipline ?> Courses in india" class="checkAllCourses">View All <?= $discipline ?> Courses</a>
        </div>
        <?php $i++; ?>
    <?php endforeach; ?>
    <div class="col-12 p-0">
        <button class="viewAllStream">View All Streams <span class="spriteIcon caret-red"></span></button>
    </div>
</div>
<?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
    <aside>
        <div class="horizontalRectangle">
            <div class="appendAdDiv" style="background:#EAEAEA;">
                <?php if ($isMobile): ?>
                    <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                    ?>
                <?php else: ?>
                    <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                <?php endif; ?>
            </div>
        </div>
    </aside>
<?php endif; ?>