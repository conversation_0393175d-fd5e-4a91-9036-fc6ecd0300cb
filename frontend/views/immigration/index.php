<?php

use common\services\ImmigrationService;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use frontend\helpers\Schema;

//utils
$this->title = $contentData['seoTags']['title'];
$this->context->ogTitle = $contentData['seoTags']['ogTitle'];
$this->context->description = $contentData['seoTags']['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->params['schema'] = Schema::getPageSchema($slug, $contentData, 'countryPage');

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Immigration');
$this->params['canonicalUrl'] = Url::base(true) . '/immigration';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'immigration_home.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'immigration_location.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'immigration_eligibilty_calclulator.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::getAlias('https://cdn.jsdelivr.net/gh/fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.css'), ['depends' => [AppAsset::class]]);

$images = [
    ['imgUrl' => '/yas/images/gis_abroadAus.jpeg'],
    ['imgUrl' => '/yas/images/gis_abroadAus.jpeg'],
    ['imgUrl' => '/yas/images/gis_abroadAus.jpeg'],
    ['imgUrl' => '/yas/images/gis_abroadAus.jpeg'],
    ['imgUrl' => '/yas/images/gis_abroadAus.jpeg'],
    ['imgUrl' => '/yas/images/gis_abroadAus.jpeg']
];

$international = [
    ['country' => 'Canada', 'url' => Url::toCanadaLink(), 'type' => 'Permanent Residency', 'imgUrl' => '/yas/images/gis_abroadCanada.webp', 'countryFLag' => 'canadaFlag'],
    ['country' => 'Australia', 'url' => Url::toAustraliaLink(), 'type' => 'Permanent Residency', 'imgUrl' => '/yas/images/gis_abroadAus.webp', 'countryFLag' => 'australiaFlag'],
    ['country' => 'Austria', 'url' => Url::toAustriaLink(), 'type' => 'Job Seeker Visa', 'imgUrl' => '/yas/images/gis_abroadAustria.webp', 'countryFLag' => 'austriaFlag'],
    ['country' => 'Sweden', 'url' => Url::toSwedenLink(), 'type' => 'Job Seeker Visa', 'imgUrl' => '/yas/images/gis_abroadCanada.webp', 'countryFLag' => 'swedenFlag'],
    ['country' => 'UAE', 'url' => Url::toUaeLink(), 'type' => 'Job Seeker Visa', 'imgUrl' => '/yas/images/gis_abroadAus.webp', 'countryFLag' => 'uaeFlag'],
    ['country' => 'Germany', 'url' => Url::toGermanyOppLink(), 'type' => 'Opportunity Card', 'imgUrl' => '/yas/images/gis_abroadAustria.webp', 'countryFLag' => 'germanyFlag'],
];



?>
<div class="gis__immigrationContainer">
    <!-------------------------------------------------------------------------------------------------------------->
    <!--------------------------------------------- Banner Section ------------------------------------------------->
    <!-------------------------------------------------------------------------------------------------------------->
    <div class="gis__immigrationBanner">
        <div class="container">
            <h1 class="gis__immigrationBanner__title"><b>GetMyUni:</b> Turning Your Immigration Dreams to Reality</h1>
            <p class="gis__immigrationBanner__subTitle">With our expert team and personalized services, step into the world where<br />you dream to <b>live, study or work abroad with us!</b></p>
            <div class="gis__immigrationBanner__subTitleEnd_class">
                <p class="gis__immigrationBanner__subTitleEnd"></p>
            </div>
            <!-- <button type="button" class="gis__immigrationBanner__getBtn">Get Started</button> -->
        </div>
    </div>
    <!-------------------------------------------------------------------------------------------------------------->
    <!---------------------------------------------One Step Section ------------------------------------------------>
    <!-------------------------------------------------------------------------------------------------------------->
    <div class="gis__immigrationStep">
        <div class="container">
            <div class="gis__immigrationStep__gridView">
                <div class="gis__stepCard">
                    <h1>Your All-in-One Solution for Immigration Success!</h1>
                </div>
                <div class="gis__stepCard">
                    <h1>Cutting Edge Technology</h1>
                </div>
                <div class="gis__stepCard">
                    <h1>Streamlined and transparent process</h1>
                </div>
                <div class="gis__stepCard">
                    <h1>Leading Immigration Advisor</h1>
                </div>
            </div>
        </div>
    </div>
    <!-------------------------------------------------------------------------------------------------------------->
    <!--------------------------------------------- Service Section ------------------------------------------------>
    <!-------------------------------------------------------------------------------------------------------------->
    <div class="gis__immigrationService">
        <div class="container">
            <h2 class="gis__immigrationTitle">Our Services</h2>
            <p class="gis__immigrationSubTitle">With services spanning from visa applications to employment assistance, we’ve got you covered at every step.</p>
            <div class="gis__immigrationContentWrap">
                <div class="gis__immigrationService__gridView">
                    <!--------------------------------- Item One ---------------------------------------->
                    <div class="gis__immigrationService__gridItems">
                        <div class="gis__immigrationService__img">
                            <img src="/yas/images/gis_service.webp" alt="service page" />
                        </div>
                        <div class="gis__immigrationService__body">
                            <h4>Permanent Residency (PR)</h4>
                            <p>Navigate the complexities of skilled migration and PR applications with our professional advice.</p>
                            <a href="<?= Url::toPRLink(); ?>" class="gis__immigrationService__learnBtn" target="_blank">
                                <span>Learn More</span>
                                <span class="gisSpriteIcon rightArrowIcon"></span>
                            </a>
                        </div>
                    </div>
                    <!--------------------------------- Item One ---------------------------------------->
                    <div class="gis__immigrationService__gridItems">
                        <div class="gis__immigrationService__img">
                            <img src="/yas/images/gis_service2.webp" alt="service page" />
                        </div>
                        <div class="gis__immigrationService__body">
                            <h4>Job seeker Visa</h4>
                            <p>Find your dream job abroad and obtain the necessary work permit with our comprehensive visa services.</p>
                            <a href="<?= Url::toJobSeekerLink(); ?>" class="gis__immigrationService__learnBtn" target="_blank">
                                <span>Learn More</span>
                                <span class="gisSpriteIcon rightArrowIcon"></span>
                            </a>
                        </div>
                    </div>
                    <!--------------------------------- Item One ---------------------------------------->
                    <div class="gis__immigrationService__gridItems">
                        <div class="gis__immigrationService__img">
                            <img src="/yas/images/gis_service3.webp" alt="service page" />
                        </div>
                        <div class="gis__immigrationService__body">
                            <h4>Study Visa</h4>
                            <p>Realize your academic aspirations with our personalized study visa assistance and guidance..</p>
                            <a href="https://admission.getmyuni.com/lp/study-abroad" class="gis__immigrationService__learnBtn" target="_blank">
                                <span>Learn More</span>
                                <span class="gisSpriteIcon rightArrowIcon"></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-------------------------------------------------------------------------------------------------------------->
    <!------------------------------------------ Fly Abroad Section ------------------------------------------------>
    <!-------------------------------------------------------------------------------------------------------------->
    <div class="gis__immigrationAbroad">
        <div class="container">
            <h2 class="gis__immigrationTitle">Go International</h2>
            <p class="gis__immigrationSubTitle">We simplify your journey abroad, from visa applications to job search assistance.</p>
            <div class="gis__immigrationContentWrap">
                <div class="gis__immigrationService__gridView">
                    <?php foreach ($international as $value): ?>
                        <a target="_blank" href="<?= $value['url'] ?>" class="gis__immigrationService__flyGridItem" style="background-image: linear-gradient(180deg, rgba(9, 102, 194, 0.3) 10.87%,
                         #0966C2 94.02%), url('<?php echo $value['imgUrl']; ?>');">
                            <p> <span class="gisSpriteIcon <?= $value['countryFLag'] ?>"></span> </p>
                            <p class="gis__immigrationService__flyGridBody__title">
                                <b><?= $value['country'] ?></b>
                                <span><?= $value['type'] ?></span>
                            </p>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <div class="gis__testimonialsCtn">
        <div class="container">
            <h2 class="gis__immigrationTitle">Join Us & Achieve Your Dreams</h2>
            <ul class="gis__testimonialTabList">
                <?php
                $flagIconClass = $title = '';
                foreach ($testimonialType as $testimonialtabID => $testimonialtab) {
                    if ($testimonialtabID ==  ImmigrationService::CANADA_PR):
                        $flagIconClass = 'canadaFlag';
                    elseif ($testimonialtabID ==  ImmigrationService::AUSTRALIA_PR):
                        $flagIconClass = 'australiaFlag';
                    elseif ($testimonialtabID ==  ImmigrationService::GERMANY_VISA || $testimonialtabID ==  ImmigrationService::GERMANY_OPP):
                        $flagIconClass = 'germanyFlag';
                    elseif ($testimonialtabID ==  ImmigrationService::AUSTRIA_VISA):
                        $flagIconClass = 'austriaFlag';
                    elseif ($testimonialtabID ==  ImmigrationService::SWEDEN_VISA):
                        $flagIconClass = 'swedenFlag';
                    elseif ($testimonialtabID ==  ImmigrationService::UAE_VISA):
                        $flagIconClass = 'uaeFlag';
                    else:
                        $flagIconClass = '';
                    endif;
                    $title = $testimonialtab;
                    // to adjust germany opp card
                    if ($testimonialtabID == ImmigrationService::GERMANY_OPP):
                        $testimonialtab = 'Germany Opp ..';
                    endif;
                    echo ' <li class="gis__testimonialTab">
                <button class="gis__testimonialTabBtn" data-tab="' . $testimonialtabID . '" title="' . $title . '">
                    <span class="gisSpriteIcon ' . $flagIconClass . '"></span>
                    ' . $testimonialtab . '
                    <span class="gisSpriteIcon closeIcon"></span>
                </button>
            </li>';
                } ?>
            </ul>
            <div class="gis__testimonialContentBox" id="testimonialContentBox">
                <?php
                foreach ($testimonials as $testimonialId => $testimonialValue):
                    ?>
                    <div class="gis__testimonialCard">
                        <div class="gis__testimonialCardTop">
                            <div class="gis__testimonialCardBadge">
                                <p><?= $testimonialValue['band_score']; ?></p>
                                <p>
                                    <?php
                                    // Display full stars
                                    $fullStars = floor($testimonialValue['rating']);
                                    for ($i = 0; $i < $fullStars; $i++) {
                                        echo '<span class="gisSpriteIcon fullStar"></span>';
                                    }

                                    // Display half star if rating is a decimal
                                    if ((int)$testimonialValue['rating'] - $fullStars > 0) {
                                        echo '<span class="gisSpriteIcon halfStar"></span>';
                                        $fullStars++; // Increment to account for the half star
                                    }

                                    // Display empty stars
                                    for ($i = $fullStars; $i < 5; $i++) {
                                        echo '<span class="gisSpriteIcon emptyStar"></span>';
                                    }
                                    ?>
                                </p>
                            </div>
                            <div class="gis__testimonialCardHeader">
                                <div class="gis__testimonialCardHeaderImg">
                                    <img src="<?= !empty($testimonialValue['image']) ? $testimonialValue['image'] : Url::getDefaultReviewImage() ?>" alt="">
                                    <span class="gisSpriteIcon"></span>
                                </div>
                                <div class="gis__testimonialCardDetail">
                                    <h2><?= $testimonialValue['author_name']; ?></h2>
                                    <p><?= $testimonialValue['short_desc'] ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="gis__testimonialCardBottom">
                            <p><span class="gisSpriteIcon doubleQuoteIcon"></span>&nbsp;&nbsp;<?= $testimonialValue['description'] ?></p>
                        </div>
                    </div>
                <?php endforeach; ?>
                <!-- Add more static testimonial cards as needed -->
            </div>
            <!-- ajaxdiv -->
            <div id="testimonialContentBoxAjax"></div>
            <!-- ajax Lod More div -->
            <div id="testimonialContentBoxAjaxLoadMore"></div>

            <div class="gis__testimonialBtnBox">
                <button class="button" id="loadmore" data-offset="3" data-total=<?= $testimonialsCount; ?> <?php if ($testimonialsCount < 3):
                                                                                                                echo "style='display:none'";
                                                                                endif; ?>>Load More</button>
                <input type="hidden" id="oiginalTotal" value="<?= $testimonialsCount; ?>">
            </div>
        </div>
    </div>

    <?php
    if (isset($contentData['yourJourneyWithUs']) && !empty($contentData['yourJourneyWithUs'])):
        echo $this->render('partials/yourJourneyWithUs', ['yourJourneyData' => $contentData['yourJourneyWithUs']]);
    endif; ?>
    <!-------------------------------------------------------------------------------------------------------------->
    <!------------------------------------------- Job Assistance Section ------------------------------------------->
    <!-------------------------------------------------------------------------------------------------------------->
    <div class="gis__jobCtn">
        <div class="container">
            <h2 class="gis__immigrationTitle">Job Search Assistance</h2>
            <p class="gis__immigrationSubTitle">"Your Pathway to Success Abroad with Expert Job Search Support"</p>
            <picture>
                <source media="(max-width: 1023px)" srcset="/yas/images/gis_journeyMob.svg" type="image/svg+xml" alt="job assistance" />
                <img src="/yas/images/gis_journey.svg" alt="job assistance" />
            </picture>
        </div>
    </div>
    <?php
    if (isset($contentData['calculators'])):
        if (count($contentData['calculators']['slides']) > 1):
            echo $this->render('partials/calculatorCard', ['calculators' => $contentData['calculators']]);
        endif;
    endif;
    // if (isset($contentData['expertGuidance']) && !empty($contentData['expertGuidance'])):
    //     echo $this->render('partials/expertGuidance', ['expertGuidanceData' => $contentData['expertGuidance']]);
    // endif;
    // echo $this->render('partials/immigrationNews', ['latestNewsArticles' => $latestNewsArticles]);
    if (isset($contentData['trendingArticles'])):
        echo $this->render('partials/immigrationBlog', ['trendingArticles' => $trendingArticles]);
    endif;
    echo $this->render('partials/address', ['officeLocation' => $contentData['officeLocation']]);
    ?>
</div>
<?php $this->registerJsFile(Yii::getAlias('https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js'), ['depends' => [AppAsset::class]]);
$this->registerJsFile(Yii::$app->params['jsPath'] . 'testimonial.js', ['depends' => [AppAsset::class]]);
