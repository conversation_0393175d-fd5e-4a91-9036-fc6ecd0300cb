<?php

use frontend\helpers\Url;
use frontend\assets\AppAsset;
use frontend\helpers\Schema;

//utils
$this->title = $contentData['seoTags']['title'];
$this->context->ogTitle = $contentData['seoTags']['ogTitle'];
$this->context->description = $contentData['seoTags']['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->params['schema'] = Schema::getPageSchema($slug, $contentData, 'countryPage');

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Immigration');
$this->params['canonicalUrl'] = Url::base(true) . '/immigration';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'immigration_countryPage.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'immigration_location.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'immigration_eligibilty_calclulator.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::getAlias('https://cdn.jsdelivr.net/gh/fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.css'), ['depends' => [AppAsset::class]]);
?>

<main class="gispages">
  <!-------------------------------------------------------------------------------------------------------------->
  <!--------------------------------------------- Banner Section ------------------------------------------------->
  <!-------------------------------------------------------------------------------------------------------------->
  <div class="gis__immigrationBanner <?php if (isset($contentData['bannerSection']['bannerImage'])):
                                        echo $contentData['bannerSection']['bannerImage'];
                                     endif;
                                     ; ?>">
    <div class="container">
      <h1 class="gis__immigrationBanner__title"><b><?= $contentData['bannerSection']['h1']; ?></b>
        <?php if (isset($contentData['bannerSection']['bannerFlagIcon'])):
            echo '<span class="spriteIcon__2 ' . $contentData['bannerSection']['bannerFlagIcon'] . '"></span>';
        endif; ?></h1>
      <p class="gis__immigrationBanner__subTitle"><?= $contentData['bannerSection']['intro']; ?></p>
      <!-- <button type="button" class="gis__immigrationBanner__getBtn">Talk to Expert</button> -->
    </div>
  </div>
  <section class="gis__section">
    <div class="container">
      <div class="gis__section__head">
        <h2><?= $contentData['whySettleInCountry']['heading']; ?></h2>
        <p><?= $contentData['whySettleInCountry']['description']; ?></p>
      </div>
      <div class="row gis__countryCard__list">
        <?php
        foreach ($contentData['whySettleInCountry']['tiles'] as $tiles): ?>
          <span class="gis__countryCard economy">
            <div class="gis__countryCard__iconContainer">
              <i class="spriteIcon__2 <?= $tiles['iconClass']; ?>"></i>
            </div>
            <p><?= $tiles['heading']; ?></p>
          </span>
        <?php endforeach; ?>
      </div>
    </div>
  </section>
  <section class="gis__section">
    <div class="container">
      <div class="gis__section__banner">
        <div class="gis__section__bannerContent">
          <?= $contentData['countryInfoSection']; ?>
        </div>
        <div class="gis__section__bannerReadMore">
          <button class="gis__readMoreBtn">Read More</button>
        </div>
      </div>
    </div>
  </section>
  <?php
    if (isset($contentData['salarySection']) && !empty($contentData['salarySection'])):
        echo $this->render('partials/salaryBarGraph', ['salarySectionData' => $contentData['salarySection'], 'slug' => $slug]);
    endif;
    if (isset($contentData['pathwaysToSettleInCountry']) && !empty($contentData['pathwaysToSettleInCountry'])):
        echo $this->render('partials/pathways', ['pathwayData' => $contentData['pathwaysToSettleInCountry']]);
    endif;
    if (isset($contentData['yourJourneyWithUs']) && !empty($contentData['yourJourneyWithUs'])):
        echo $this->render('partials/yourJourneyWithUs', ['yourJourneyData' => $contentData['yourJourneyWithUs']]);
    endif;
    if (isset($contentData['calculators'])):
      // if (count($contentData['calculators']['slides']) > 1):
        echo $this->render('partials/calculatorCard', ['calculators' => $contentData['calculators']]);
    // endif;
    endif;
    if (isset($contentData['ourJourneySoFar']) && !empty($contentData['ourJourneySoFar'])):
        echo $this->render('partials/ourJourneySoFar', ['ourJourneyData' => $contentData['ourJourneySoFar']]);
    endif;
  // if (isset($contentData['expertGuidance']) && !empty($contentData['expertGuidance'])):
  //     echo $this->render('partials/expertGuidance', ['expertGuidanceData' => $contentData['expertGuidance']]);
  // endif;
  // echo $this->render('partials/immigrationNews', ['latestNewsArticles' => $latestNewsArticles]);
    if (isset($contentData['trendingArticles'])):
        echo $this->render('partials/immigrationBlog', ['trendingArticles' => $trendingArticles]);
    endif;
    if (isset($contentData['mainFaqs']) && !empty($contentData['mainFaqs'])):
        echo $this->render('partials/mainFaqs', ['mainFaqs' => $contentData['mainFaqs']]);
    endif;
    echo $this->render('partials/address', ['officeLocation' => $contentData['officeLocation']]);
    ?>


</main>