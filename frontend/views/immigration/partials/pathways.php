<?php
$tabLists = $tabHtml = '';
$isCurrent = ' current';
$isTabs = (count($pathwayData['tabs']) > 1) ? true : false;
foreach ($pathwayData['tabs'] as $tabName => $questionAnswers):
    $tabId = str_replace([
        '\'', '"', ',', ';', '<', '>', '(', ')', '?', ' '
    ], '', $tabName);
    $tabLists .= '<li class="tab-link' . $isCurrent . '" data-tab="' . $tabId . '">' . $tabName . '</li>';
    $tabHtml .= '<div id="' . $tabId . '" class="tab-content' . $isCurrent . '"><div class="pathRow">';
    foreach ($questionAnswers['faqs'] as $questionAnswer):
        $tabHtml .= '<button class="pathAccordion"><span>' . $questionAnswer['question'] . '</span></button>
                        <div class="pathPanel">
                            <div class="pathAccordianContent">
                                <p>' . $questionAnswer['answer'] . '</p>
                            </div>                        
                    </div>';
        $isCurrent = '';
    endforeach;
    $tabHtml .= '</div></div>';
endforeach;
$tabLists = $tabHtml = '';
$isCurrent = ' current';
$isTabs = (count($pathwayData['tabs']) > 1) ? true : false;
foreach ($pathwayData['tabs'] as $tabName => $questionAnswers):
    $tabId = str_replace([
        '\'', '"', ',', ';', '<', '>', '(', ')', '?', ' '
    ], '', $tabName);
    $tabLists .= '<li class="tab-link' . $isCurrent . '" data-tab="' . $tabId . '">' . $tabName . '</li>';
    $tabHtml .= '<div id="' . $tabId . '" class="tab-content' . $isCurrent . '"><div class="pathRow">';
    foreach ($questionAnswers['faqs'] as $questionAnswer):
        $tabHtml .= '<button class="pathAccordion"><span>' . $questionAnswer['question'] . '</span></button>
                        <div class="pathPanel">
                            <div class="pathAccordianContent">
                                <p>' . $questionAnswer['answer'] . '</p>
                            </div>                        
                    </div>';
        $isCurrent = '';
    endforeach;
    $tabHtml .= '</div></div>';
endforeach; ?>
<section class="gis__section">
    <div class="container">
        <div class="gis__section__head">
            <h2><?= $pathwayData['heading']; ?></h2>
            <?php if (isset($pathwayData['description']) && $pathwayData['description'] != ''): ?>
                <p><?= $pathwayData['description']; ?></p><br>
            <?php endif; ?>
            <?php if ($isTabs === true): ?>
                <div class="gis__tabber__immigration">
                    <ul>
                        <?= $tabLists ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        <div class="gis__tabber__immigrationContent">
            <?= $tabHtml ?>
        </div>
    </div>
</section>