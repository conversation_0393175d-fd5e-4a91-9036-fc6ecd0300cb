<?php $ischecked = 'checked';
$desktopListElements = $desktopCityAddress = $mapIframe = $mobileOfficeAccordion = '';
foreach ($officeLocation as $office):
    $city = $office['city'];
    $address = $office['address'];
    $phone = $office['phone'];
    $email = $office['email'];
    $mapUrl = $office['mapUrl'];
    $desktopListElements .= '<li><input data-city="' . strtolower($city) . '" name="location" id="location' . $city . '" type="radio" ' . $ischecked . '><label for="location' . $city . '">' . $city . '</label></li>';
    $desktopCityAddress .= '<div class="cityLocationAddress" data-city="' . strtolower($city) . '"><h3>' . $city . '</h3>
    <div class="row"><div class="col-md-6 gis__ourOffices__mobileDivision">' . $address . '</div>
    <div class="col-md-6 gis__ourOffices__mobileDivision"><div class="locationPhone"><span class="spriteIcon__2 gis__teleIcon"></span><span>
    <a href="tel:' . $phone . '">' . $phone . '</a></span></div><div class="locationEmail"><span class="spriteIcon__2 gis__emailIcon"></span><span>
    <a href="mailto:' . $email . '">' . $email . '</a></span></div></div></div></div>';
    $mapIframe .= '<div class="selectedCityMap" data-city="' . strtolower($city) . '"><iframe src="' . $mapUrl . '" width="600" height="100%" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe></div>';
    $mobileOfficeAccordion .= '<button class="accordion">' . $city . '</button><div class="panel"><div class="innerPanel">
    <div class="cityLocationAddress" data-city="' . strtolower($city) . '"><h3>' . $city . '</h3><div class="row">
    <div class="col-md-6">' . $address . '</div><div class="col-md-6"><div class="locationPhone"><span class="spriteIcon__2 gis__teleIcon"></span>
    <span><a href="tel:' . $phone . '">' . $phone . '</a></span></div><div class="locationEmail"><span class="spriteIcon__2 gis__emailIcon"></span>
    <span><a href="mailto:' . $email . '">' . $email . '</a></span></div></div></div></div></div>
    <div class="selectedCityMap" data-city="' . strtolower($city) . '"><iframe src="' . $mapUrl . '" width="600" height="100%" style="border:0;" 
      allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe></div></div>';
    $ischecked = '';
endforeach;
?>
<section class="gis__section gis__ourOffices">
  <div class="container">
    <div class="row">
      <div class="col-md-6 mapTab">
        <?= $mapIframe; ?>
        <!-- <div class="selectedCityMap" data-city="bangalore">
          <iframe title="map"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3888.2263976932686!2d77.58367935044124!3d12.957359890820412!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bae159fcce5f969%3A0x2387cbcd2ed524d4!2sGetGIS%20(Global%20Immigration%20Services)!5e0!3m2!1sen!2sin!4v1679649610793!5m2!1sen!2sin" width="600" height="100%" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
        </div>
        <div class="selectedCityMap" data-city="kolkata">
          <iframe title="map"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3888.9566372548743!2d77.63020195044075!3d12.910508590851336!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bae153abe789aed%3A0x8b870044099e9668!2sGetMyUni!5e0!3m2!1sen!2sin!4v1679649792807!5m2!1sen!2sin" width="600" height="100%" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
        </div> -->
      </div>
      <div class="col-md-6 locationTab">
        <h2>Contact Us</h2>
        <ul class="locationTabCities">
          <?= $desktopListElements; ?>
          <!-- <li>
            <input data-city="bangalore" name="location" id="locationBanglore" type="radio" checked>
            <label for="locationBanglore">Bangalore</label>
          </li>
          <li>
            <input data-city="kolkata" name="location" id="locationKolkata" type="radio">
            <label for="locationKolkata">Kolkata</label>
          </li>
          <li>Delhi</li>
          <li>Hyderabad</li>
          <li>Pune</li>
          <li>Gurgaon</li>
          <li>Chennai</li> -->
        </ul>
        <div class="selectedCityArea">
          <?= $desktopCityAddress; ?>
          <!-- <div class="cityLocationAddress" data-city="bangalore">
            <h3>Bangalore</h3>
            <div class="row">
              <div class="col-md-6 gis__ourOffices__mobileDivision">
                IndiQube Orion, 24th Main Rd, 1st Sector, HSR Layout, Bengaluru, Karnataka,
                560102
              </div>
              <div class="col-md-6 gis__ourOffices__mobileDivision">
                <div class="locationPhone">
                  <span class="spriteIcon__2 gis__teleIcon"></span>
                  <span>+91 9559859995</span>
                </div>
                <div class="locationEmail">
                  <span class="spriteIcon__2 gis__emailIcon"></span>
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
          </div> 
          <div class="cityLocationAddress" data-city="kolkata">
            <h3>Kolkata</h3>
            <div class="row">
              <div class="col-md-6">
                IndiQube Orion, 24th Main Rd, 1st Sector, HSR Layout, Bengaluru, Karnataka,
                560102
              </div>
              <div class="col-md-6">
                <div class="locationPhone">
                  <span class="spriteIcon__2 gis__teleIcon"></span>
                  <span>+91 9559859995</span>
                </div>
                <div class="locationEmail">
                  <span class="spriteIcon__2 gis__emailIcon"></span>
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</section>