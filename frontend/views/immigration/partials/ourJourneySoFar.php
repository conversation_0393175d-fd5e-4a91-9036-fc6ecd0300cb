<section class="gis__section gis__hexagonSection">
    <div class="container">
        <div class="row">
            <div class="col-md-6 gis__verticalMidAlign">
                <div class="gis__section__head">
                    <h2><?= $ourJourneyData['heading'] ?></h2>
                    <p><?= $ourJourneyData['description']?></p>
                    <!-- <button type="button" class="gis__hexagonSection__ctaButton">Book a Free Consultation</button> -->
                </div>
            </div>
            <div class="col-md-6">
                <div class="hexagonContainer">
                    <div class="hexagon-gallery">
                        <?php
                        foreach ($ourJourneyData['journeyList']['first'] as $item): ?>
                            <div class="hex">
                                <div class="hexInside">
                                    <span class="spriteIcon__2 <?= $item['icon']; ?>"></span>
                                    <p><?= $item['heading']; ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="hex">
                            <div class="hexInside <?php if (isset($ourJourneyData['flagIcon'])):
                                echo $ourJourneyData['flagIcon'];
                                                  endif; ?>">
                            </div>
                        </div>
                        <?php foreach ($ourJourneyData['journeyList']['second'] as $item): ?>
                            <div class="hex">
                                <div class="hexInside">
                                    <span class="spriteIcon__2 <?= $item['icon']; ?>"></span>
                                    <p><?= $item['heading']; ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>