<?php

use frontend\assets\AppAsset;
use yii\web\View;

$defaultDesignation = $_REQUEST['designation'] ?? $salarySectionData['chart']['defaultDesignation'];
$chartData = $salarySectionData['chart']['data'];
$jobTitles = [];
foreach ($chartData as $row) {
    $jobTitles[$row['Job Title']] = $row['Job Title'];
}
sort($jobTitles);
$countryName = ucfirst($slug);
?>
<section class="gis__section">
    <div class="container">
        <div class="gis__section__head">
            <h2><?= $salarySectionData['heading']; ?></h2>
            <?php if (isset($salarySectionData['description']) && $salarySectionData['description'] != ''):
                ?><p><?= $salarySectionData['description']; ?></p><?php
            endif; ?>
        </div>
        <div class="row gis__salaryContainer">
            <div class="gis__salaryContainer__dropdown">
                <select class="checkSalarySelect" id="checkSalarySelect">
                    <?php foreach ($jobTitles as $jobTitle):
                        $selected = ($defaultDesignation == $jobTitle) ? 'selected' : ''; ?><option <?= $selected ?> value="<?= $jobTitle ?>"><?= $jobTitle ?></option><?php
                    endforeach; ?>
                </select>
                <button class="salaryFilterButton desktopOnly">Check</button>
            </div>
            <button class="salaryFilterButton mobileOnly">Check</button>

            <div id="gis__salaryContainer__chart"></div>
        </div>
        <div id="number_format_chart"></div>
    </div>
</section>
<?php
$this->registerJsFile(Yii::getAlias('https://www.gstatic.com/charts/loader.js'), ['depends' => [AppAsset::class]]);
$this->registerJsVar('slug', $slug, View::POS_HEAD);
$this->registerJsVar('countryName', $countryName, View::POS_HEAD);
$this->registerJsFile(Yii::$app->params['jsPath'] . 'salary_graph.js', ['depends' => [AppAsset::class]]); ?>