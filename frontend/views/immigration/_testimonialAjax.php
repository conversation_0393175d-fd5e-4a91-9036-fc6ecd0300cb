<div class="gis__testimonialContentBox">
    <?php

    use frontend\helpers\Url;

    foreach ($testimonials as $testimonial): ?>
        <div class="gis__testimonialCard">
            <div class="gis__testimonialCardTop">
                <div class="gis__testimonialCardBadge">
                    <p><?= $testimonial['band_score']; ?></p>
                    <p>
                        <?php
                        // Display full stars
                        $fullStars = floor($testimonial['rating']);
                        for ($i = 0; $i < $fullStars; $i++) {
                            echo '<span class="gisSpriteIcon fullStar"></span>';
                        }

                        // Display half star if rating is a decimal
                        if ($testimonial['rating'] - $fullStars > 0) {
                            echo '<span class="gisSpriteIcon halfStar"></span>';
                            $fullStars++; // Increment to account for the half star
                        }

                        // Display empty stars
                        for ($i = $fullStars; $i < 5; $i++) {
                            echo '<span class="gisSpriteIcon emptyStar"></span>';
                        }
                        ?>
                    </p>
                </div>
                <div class="gis__testimonialCardHeader">
                    <div class="gis__testimonialCardHeaderImg">
                        <img src="<?= !empty($testimonial['image']) ? $testimonial['image'] : Url::getDefaultReviewImage() ?>" alt="">
                        <span class="gisSpriteIcon"></span>
                    </div>
                    <div class="gis__testimonialCardDetail">
                        <h2><?= $testimonial['author_name'] ?></h2>
                        <p><?= $testimonial['short_desc'] ?></p>
                    </div>
                </div>
            </div>
            <div class="gis__testimonialCardBottom">
                <p><span class="gisSpriteIcon doubleQuoteIcon"></span>&nbsp;&nbsp;<?= $testimonial['description'] ?></p>
            </div>
        </div>
    <?php endforeach; ?>
    <input type="hidden" id="testimonailCount" value="<?= $testimonialsCount; ?>">
</div>