<?php

use frontend\helpers\Url;
use frontend\assets\AppAsset;
use frontend\helpers\Schema;

//utils
//utils
$this->title = $contentData['seoTags']['title'];
$this->context->ogTitle = $contentData['seoTags']['ogTitle'];
$this->context->description = $contentData['seoTags']['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->params['schema'] = Schema::getPageSchema($slug, $contentData, 'countryPage');

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Immigration');
$this->params['canonicalUrl'] = Url::base(true) . '/immigration';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'immigration_categoryPage.css', ['depends' => [AppAsset::class]]);

?>
<!-------------------------------------------------------------------------------------------------------------->
<!--------------------------------------------- Banner Section ------------------------------------------------->
<!-------------------------------------------------------------------------------------------------------------->
<div class="gis__immigrationBanner">
  <div class="container">
    <h1 class="gis__immigrationBanner__title"><b><?= $contentData['bannerSection']['h1']; ?></b></h1>
    <p class="gis__immigrationBanner__subTitle"><?= $contentData['bannerSection']['intro']; ?></p>
    <!-- <button type="button" class="gis__immigrationBanner__getBtn">Talk to Expert</button> -->
  </div>
</div>
<div class="container">
  <div class="gis__grid">
    <div class="gis__grid__left">
      <div class="gis__countryOptions">
        <h2 class="gis__countryOptions__heading"><?= $contentData['tileDetail']['heading']; ?></h2>
        <div class="gis__infoListContainer two-cardDisplay">
          <?php if (count($contentData['tileDetail']['tiles']) > 2): ?>
            <i class="spriteIcon scrollLeft over"></i>
            <i class="spriteIcon scrollRight"></i>
          <?php endif; ?>
          <div class="gis__infoList row">
            <?php foreach ($contentData['tileDetail']['tiles'] as $tiles => $tileContent):  ?>
              <a href="<?= $tileContent['href']; ?>" target="_blank">
                <div class="gis__countryOptions__card <?= $tileContent['bgImage']; ?>">
                  <div class="spriteIcon__2 gis__<?= $tileContent['flagIcon']; ?>"></div>
                  <span class="gis__countryOptions__name"><?= $tileContent['heading']; ?></span>
                  <span><?= $tileContent['subHeading']; ?></span>
                </div>
              </a>
            <?php endforeach ?>
          </div>
        </div>
      </div>
      <div class="gis__mainContent">
        <?php
        if (!empty($contentData['commonContent'])):
            echo '<p>' . $contentData['commonContent'] . '</p>';
        endif;

        if (!empty($contentData['countriesContent']['heading'])):
            echo '<h2 class="gis__mainContent__firstHeading">' . $contentData['countriesContent']['heading'] . '</h2>';
        endif;

        if (!empty($contentData['countriesContent']['description'])):
            echo '<p>' . $contentData['countriesContent']['description'] . '</p>';
        endif; ?>
        <div class="gis__mainContent__countryTabber">
          <div class="gis__countryTabber__countryRow">
            <ul>
              <?php
                $active = 'current';
                foreach ($contentData['countriesContent']['tabs'] as $tabs => $tabContent): ?>
                <li class="tab-link <?= $active; ?> gis__countryRow__<?= strtolower($tabs); ?>" data-tab="<?= $tabs; ?>"><?= $tabs; ?></li>
                    <?php $active = '';
                endforeach; ?>
            </ul>
          </div>
          <div class="gis__countryTabber__countryContainer">
            <?php
            $active = 'current';
            foreach ($contentData['countriesContent']['tabs'] as $tabs => $tabContent):
                ?>
              <div id="<?= $tabs; ?>" class="tab-content <?= $active; ?>">
                <?php if (isset($tabContent['desc'])): ?>
                  <div class="gis__countryContainer__keyHighlightsSection">
                    <h3>Key Highlights</h3>
                    <ul class="highlightsList">
                      <?php foreach ($tabContent['desc'] as $desc):
                            ?>
                        <li><a class="sectionTarget" href="#<?= $desc['id']; ?>"><?= $desc['heading']; ?></a></li>
                      <?php endforeach; ?>
                    </ul>
                  </div>
                    <?php foreach ($tabContent['desc'] as $desc):
                        if ($desc['id'] == '1'): ?>
                      <h3 id="<?= $desc['id']; ?>" class="gis__countryContainer__firstHeading"><?= $desc['heading']; ?></h3>
                      <p class="countrySubText"><?= $desc['description']; ?></p>
                        <?php else: ?>
                      <h3 id="<?= $desc['id']; ?>" class="gis__countryContainer__commonHeading"><?= $desc['heading']; ?></h3>
                            <?= $desc['description'];
                        endif;
                    endforeach; ?>

                <?php endif; ?>
                <!-- -->

                <?php if (isset($tabContent['accordian'])): ?>
                  <div class="gis__countryContainer__accordion">
                    <?php foreach ($tabContent['accordian'] as $accordian):
                        ?>
                      <h4 class="gis__accordion__heading">
                        <?= $accordian['heading']; ?>
                      </h4>
                        <?php if (!empty($accordian['description'])):
                        endif; ?>
                      <div class="gis__accordion__content">
                        <p><?= $accordian['description']; ?></p>
                      </div>
                    <?php endforeach; ?>
                  </div>
                    <?php
                endif; ?>


              </div>
                <?php
                $active = '';
            endforeach; ?>
          </div>
        </div>
      </div>
    </div>
    <?php //dd($contentData['calculators']['slides']);
    ?>
    <div class="gis__grid__right desktopOnly">
      <div class="gis__usefulLinkSideWidget">
        <h3>USEFUL LINKS</h3>
        <ul>
          <!-- <li>
              <a href=""> 
                <span class="spriteIcon__2 <= $calculator['flagIcon']; ?>"></span>
                <span><= $calculator['heading']; ?></span>
          </li> -->
          <li>
            <a href="<?= Url::toImmigrationLink(); ?>">
              Immigration Services
            </a>
          </li>
          <?php
            if ($slug == 'job-seeker-visa'): ?>
            <li>
              <a href="<?= Url::toPRLink(); ?>">
                Permanent Residency Services
              </a>
            </li>
                <?php
            else: ?>
            <li>
              <a href="<?= Url::toJobSeekerLink(); ?>">
                Job Seeker Visa
              </a>
            </li>
                <?php
            endif; ?>
          <li>
            <a href="<?= Url::toCanadaLink(); ?>">
              Canada Permanent Residency
            </a>
          </li>
          <li>
            <a href="<?= Url::toAustraliaLink(); ?>">
              Australia Permanent Residency
            </a>
          </li>
          <li>
            <a href="<?= Url::toGermanyOppLink(); ?>">
              Germany Job Seeker Visa
            </a>
          </li>
          <li>
            <a href="<?= Url::toAustriaLink(); ?>">
              Austria Job Seeker Visa
            </a>
          </li>
          <li>
            <a href="<?= Url::toUaeLink(); ?>">
              Dubai Job Seeker Visa
            </a>
          </li>
          <li>
            <a href="<?= Url::toSwedenLink(); ?>">
              Sweden Job Seeker Visa
            </a>
          </li>

        </ul>
      </div>
    </div>
  </div>
</div>
<!----------------------------------- Office Address ------------------------------------->
<?= $this->render('partials/address', ['officeLocation' => $contentData['officeLocation']]); ?>