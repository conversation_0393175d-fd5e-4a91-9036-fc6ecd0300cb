<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">
    <?php

    use frontend\helpers\Url;

    foreach ($data as $val): ?>
        <url>
            <loc><?= $val['loc'] ?></loc>

            <news:news>
                <news:publication>
                    <news:name>Getmyuni</news:name>
                    <news:language>en</news:language>
                </news:publication>
                   <?php if (isset($val['publication_date']) && !empty($val['publication_date'])): ?>
                    <news:publication_date><?= date(DATE_ATOM, strtotime($val['publication_date'])) ?></news:publication_date>
                   <?php endif; ?>

                   <?php if (isset($val['title']) && !empty($val['title'])): ?>
                    <news:title><?= htmlspecialchars($val['title']) ?></news:title>
                   <?php endif; ?>
                   <?php if (isset($val['keywords']) && !empty($val['keywords'])): ?>
                    <news:keywords><?= $val['keywords']; ?></news:keywords>
                   <?php endif; ?>
            </news:news>

               <?php if (isset($val['last_mod']) && !empty($val['last_mod'])): ?>
                <lastmod><?= date(DATE_ATOM, strtotime($val['last_mod'])) ?></lastmod>
               <?php endif; ?>

               <?php if (isset($val['image']) && !empty($val['image'])): ?>
                <image:image>
                    <image:loc><?= Url::toNewsImages($val['image']) ?></image:loc>
                </image:image>
               <?php endif; ?>
        </url>
    <?php endforeach; ?>
</urlset>