<?php

use frontend\helpers\Url; ?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
    <?php
    foreach ($data as $val):
        $needsCollegePrefix = in_array($val['sub_page'], ['ci', 'pi'], true)
        && strpos($val['slug'], 'college/') === false;
    
        $loc = $val['domain'] . '/' . ($needsCollegePrefix ? 'college/' : '') . $val['slug'];
        ?>
        <url>
            <loc><?= $loc ?></loc>
            <priority><?= $val['priority'] ?></priority>
            <changefreq><?= $val['change_freq'] ?></changefreq>
            <lastmod><?= date(DATE_ATOM, strtotime($val['lastmod'])) ?></lastmod>
        </url>
    <?php endforeach; ?>
</urlset>