<?php
    use frontend\helpers\Url;
    use common\helpers\DataHelper;
    use common\helpers\BoardHelper;
?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
<?php
foreach ($data as $val):
    $lang_code = array_search($val['lang_code'], DataHelper::$languageCode);
    // $slug = $val['slug'];
    // $boardSlug = str_replace('-' . $val['subPage'], '', $val['slug']);
    // $index = strpos($slug, $val['subPage']);
    // $stringAfterKey = '';
    // if ($index) {
    //     $startIndex = $index + strlen($val['subPage']);
    //     $stringAfterKey = substr($slug, $startIndex+1);
    //     if (strpos($slug, 'supplementary') && ($val['subPage'] !== 'supplementary')) {
    //         $stringAfterKey = 'supplementary';
    //     }
    //     if (!empty($stringAfterKey)) {
    //         $boardSlug = str_replace('-' . $stringAfterKey, '', $boardSlug);
    //         if (strpos($slug, 'supplementary') && ($val['subPage'] !== 'supplementary')) {
    //             $boardSlug = str_replace('-smy', '', $boardSlug);
    //         }
    //     }
    // }
    // if (array_key_exists($boardSlug, BoardHelper::$redirectBoards)) {
    //     $boardSlug = (!empty(BoardHelper::$redirectBoards[$boardSlug])) ? BoardHelper::$redirectBoards[$boardSlug] : $boardSlug;
    //     if ($val['subPage'] == 'overview') {
    //         $url = Url::base(true) . Url::toBoardDetail($boardSlug, DataHelper::getLangCode($lang_code));
    //     } else {
    //         if (trim($stringAfterKey) !== '') {
    //             $url = Url::base(true) . Url::toBoardDetailSubPage($boardSlug, $stringAfterKey, $val['subPage'], DataHelper::getLangCode($lang_code), false);
    //         } else {
    //             $url = Url::base(true) . Url::toBoardDetail($boardSlug, DataHelper::getLangCode($lang_code), $val['subPage']);
    //         }
    //     }
    // } else {
    //     $url =  Url::base(true) . '/' . $val['slug'] . '/b';
    // }
    if ($val['lang_code'] == 1) {
        $url =  Url::base(true) . '/' . 'boards/' . $val['slug'];
    } else {
        $url =  Url::base(true) . '/hi/' . 'boards/' . $val['slug'];
    }
 
    ?>
<url>
    <loc><?= $url ?></loc>
    <lastmod><?= date(DATE_ATOM, strtotime($val['lastModified'])) ?></lastmod>
    <changefreq><?= $val['changeFreq'] ?></changefreq>
    <priority><?= $val['priority'] ?></priority>
</url>
<?php endforeach; ?>
</urlset>