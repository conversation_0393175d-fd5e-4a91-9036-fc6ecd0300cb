<?php
use common\helpers\DataHelper;
use frontend\helpers\Url;
?>

<div class="quickLinksBoard">
    <p>
        <?= Yii::t('app', 'Top Science Courses After 12th') ?>
    </p>
    <ul>
        <?php foreach (DataHelper::$scienceCourses as $key => $value): ?>
            <li>
                <a title="<?= $value ?>" href="<?= Url::toCourseDetail($key) ?>">
                    <?= $value ?>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
</div>
<div class="quickLinksBoard">
    <p>
        <?= Yii::t('app', 'Top Arts Courses After 12th') ?>
    </p>
    <ul>
        <?php foreach (DataHelper::$artsCourses as $key => $value): ?>
            <li>
                <a title="<?= $value ?>" href="<?= Url::toCourseDetail($key) ?>">
                    <?= $value ?>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
</div>
<div class="quickLinksBoard">
    <p>
        <?= Yii::t('app', 'Top Commerce Courses After 12th') ?>
    </p>
    <ul>
        <?php foreach (DataHelper::$commerceCourses as $key => $value): ?>
            <li>
                <a title="<?= $value ?>" href="<?= Url::toCourseDetail($key) ?>">
                    <?= $value ?>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
</div>
<?php if ($board->type == 0): ?>
    <div class="quickLinksBoard">
        <p>
            <?= Yii::t('app', 'Stream Wise Top Colleges in India') ?>
        </p>
        <ul>
            <?php foreach (array_flip(DataHelper::$boardStreams) as $key => $value): ?>
                <li>
                    <a title="<?= $value ?>" href="<?= Url::toDisciplineCollege($value) ?>">
                        <?= 'Top ' . ucfirst($value) . ' Colleges in India' ?>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
    <div class="quickLinksBoard">
        <p>
            <?= Yii::t('app', 'Top States for Courses After 12th') ?>
        </p>
        <ul>
            <?php foreach (DataHelper::$boardStates as $key => $value): ?>
                <li>
                    <a title="<?= $value ?>" href="<?= Url::toAllCollege($key) ?>">
                        <?= 'Top Colleges in ' . ucfirst($value) ?>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif ?>
<?php
if (!empty($streamColleges)):
    if ($board->type == 1): ?>
        <div class="quickLinksBoard">
            <p>
                <?= 'Stream Wise Top Colleges in ' . $state->name ?>
            </p>
            <ul>
                <?php foreach (array_flip(DataHelper::$boardStreams) as $key => $value):
                    if (array_key_exists($value, $streamColleges)):
                        ?>
                        <li>
                            <a title="<?= $value ?>" href="<?= Url::toDisciplineCollege($value, $state->slug) ?>">
                                <?= 'Top ' . ucfirst($value) . ' Colleges in ' . $state->name ?>
                            </a>
                        </li>
                    <?php endif;
                endforeach; ?>
            </ul>
        </div>
    <?php endif;
endif;
?>