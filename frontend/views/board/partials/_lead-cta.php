<?php

use common\services\UserService;
use common\models\Board;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
$defaultEmptyCondition0 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_0']) || empty(array_filter($dynamicCta['cta_position_0'])));
$defaultEmptyCondition1 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])));
$defaultEmptyCondition2 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_2']) || empty(array_filter($dynamicCta['cta_position_2'])));
$defaultEmptyCondition3 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_3']) || empty(array_filter($dynamicCta['cta_position_3'])));
$ctaText = 'Get Board Alerts';
?>

<?php if ($lead_cta == 1): ?>
    <?php $cta_text1 = $defaultEmptyCondition0 ? $ctaText : $dynamicCta['cta_position_0']['cta_text'];
    $cta_text2 = $defaultEmptyCondition1 ? $ctaText : $dynamicCta['cta_position_1']['cta_text']; ?>
    <div class="cta-mobile">
        <div class="ctaColumn">
            <div class="ctaRow">
                <?= frontend\helpers\Html::leadButton(
                    $cta_text1,
                    [
                        'entity' => $entity,
                        'entityId' => $entity_id,
                        'ctaLocation' => $isMobile ? ($defaultEmptyCondition0 ? UserService::parseDynamicCta('boards_{slug}_wap_top_left_cta', '', $pageName) : $dynamicCta['cta_position_0']['wap']) : ($defaultEmptyCondition0 ? UserService::parseDynamicCta('boards_{slug}_web_top_left_cta', '', $pageName) : $dynamicCta['cta_position_0']['web']),
                        'ctaText' => $cta_text1,
                        'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
                        'leadformtitle' => $defaultEmptyCondition0 ? 'Apply Now' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO GET BOARD ALERTS'),
                        'redirection' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_link'],
                        'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
                        'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
                    ],
                    ['class' => 'primaryBtn applyNowButton leadBoardAutoCapture']
                ) ?>
                <?= frontend\helpers\Html::leadButton(
                    $cta_text2,
                    [
                        'entity' => $entity ?? Board::ENTITY_BOARD,
                        'entityId' => $entity_id,
                        'ctaLocation' => $isMobile ? ($defaultEmptyCondition1 ? UserService::parseDynamicCta('boards_{slug}_wap_top_right_cta', '', $pageName) : $dynamicCta['cta_position_1']['wap']) : ($defaultEmptyCondition1 ? UserService::parseDynamicCta('boards_{slug}_web_top_right_cta', '', $pageName) : $dynamicCta['cta_position_1']['web']),
                        'ctaText' => $cta_text2,
                        'ctaTitle' => $defaultEmptyCondition1 ? '' : (!empty($dynamicCta['cta_position_1']['cta_title']) ? $dynamicCta['cta_position_1']['cta_title'] : null),
                        'leadformtitle' => $defaultEmptyCondition1 ? 'REGISTER TO GET BOARD ALERTS' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'REGISTER TO GET BOARD ALERTS'),
                        'redirection' => $defaultEmptyCondition1 ? '' : $dynamicCta['cta_position_1']['page_link'],
                        'alternateMedia' => $defaultEmptyCondition1 ? '' : $dynamicCta['cta_position_1']['media'],
                        'alternatePageRedirectSlug' => $defaultEmptyCondition1 ? '' : $dynamicCta['cta_position_1']['page_redirect_slug']
                    ],
                    ['class' => 'primaryBtn setExamAlert getLeadForm leadBoardAutoCapture']
                ) ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php if ($isMobile && $lead_cta == 2): ?>
    <?php $cta_text1 = $defaultEmptyCondition0 ? $ctaText : $dynamicCta['cta_position_0']['cta_text']; ?>
    <div class="boardsDetail mobileOnly primaryBtn brochureBtn">
        <?= frontend\helpers\Html::leadButton(
            $cta_text1,
            [
                'entity' => $entity,
                'entityId' => $entity_id,
                'ctaLocation' => $defaultEmptyCondition0 ? UserService::parseDynamicCta('boards_{slug}_wap_top_left_cta1', '', $pageName) : $dynamicCta['cta_position_0']['wap'],
                'ctaText' => $cta_text1,
                'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
                'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO GET BOARD ALERTS' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO GET BOARD ALERTS'),
                'redirection' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_link'],
                'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
                'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
            ],
            ['class' => 'primaryBtn setExamAlert getLeadForm leadBoardAutoCapture']
        ) ?>
    </div>
<?php endif; ?>

<?php if ($lead_cta == 3): ?>
    <?php $cta_text1 = $defaultEmptyCondition2 ? 'Apply Now' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'Apply Now');
    $cta_text2 = $defaultEmptyCondition3 ? 'Talk To Experts' : ($dynamicCta['cta_position_3']['cta_text'] ?? 'Talk To Experts') ?>
    <?= frontend\helpers\Html::leadButton(
        $cta_text1,
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition2 ? UserService::parseDynamicCta('boards_{slug}_wap_bottom_left_cta', '', $pageName ?? '') : $dynamicCta['cta_position_2']['wap']) : ($defaultEmptyCondition2 ? UserService::parseDynamicCta('boards_{slug}_web_bottom_left_cta', '', $pageName ?? '') : ($dynamicCta['cta_position_2']['web'] ?? null)),
            'ctaText' => $cta_text1,
            'ctaTitle' => $defaultEmptyCondition2 ? '' : (!empty($dynamicCta['cta_position_2']['cta_title']) ? $dynamicCta['cta_position_2']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition2 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_2']['lead_form_title'] ?? 'REGISTER TO APPLY'),
            'subheadingtext' => $defaultEmptyCondition2 ? $name : ($dynamicCta['cta_position_2']['lead_form_description'] ?? $name),
            'image' => '/yas/images/defaultcardbanner.png',
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition2 ? null : ($dynamicCta['cta_position_2']['page_link'] ?? null)) : null,
            'alternateMedia' => $defaultEmptyCondition2 ? '' : $dynamicCta['cta_position_2']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition2 ? '' : $dynamicCta['cta_position_2']['page_redirect_slug']
        ],
        ['class' => 'primaryBtn talkToExpert getLeadForm leadBoardAutoCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
    <?= frontend\helpers\Html::leadButton(
        $cta_text2,
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition2 ? UserService::parseDynamicCta('boards_{slug}_wap_bottom_right_cta', '', $pageName ?? '') : $dynamicCta['cta_position_3']['wap']) : ($defaultEmptyCondition2 ? UserService::parseDynamicCta('boards_{slug}_web_bottom_right_cta', '', $pageName ?? '') : ($dynamicCta['cta_position_3']['web'] ?? null)),
            'ctaText' => $cta_text2,
            'ctaTitle' => $defaultEmptyCondition3 ? '' : (!empty($dynamicCta['cta_position_3']['cta_title']) ? $dynamicCta['cta_position_3']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition3 ? 'REGISTER FOR EXPERT GUIDANCE' : ($dynamicCta['cta_position_3']['lead_form_title'] ?? 'REGISTER FOR EXPERT GUIDANCE'),
            'subheadingtext' => $defaultEmptyCondition3 ? $name : ($dynamicCta['cta_position_3']['lead_form_description'] ?? $name),
            'image' => '/yas/images/defaultcardbanner.png',
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition3 ? null : ($dynamicCta['cta_position_3']['page_link'] ?? null)) : null,
            'alternateMedia' => $defaultEmptyCondition3 ? '' : $dynamicCta['cta_position_3']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition3 ? '' : $dynamicCta['cta_position_3']['page_redirect_slug']
        ],
        ['class' => 'primaryBtn applyNowButton leadBoardAutoCapture js-open-lead-form-new', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 4): ?>
    <?= frontend\helpers\Html::leadButton(
        'View Question',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? UserService::parseDynamicCta('board_{slug}_wap_view_question_cta_1', '', $pageName) : UserService::parseDynamicCta('board_{slug}_web_view_question_cta_1', '', $pageName),
            'leadformtitle' => 'REGISTER TO DOWNLOAD SAMPLE PAPER',
            'subheadingtext' => $name,
            'image' => '/yas/images/defaultcardbanner.png',
            'durl' => Url::toBoardSampleQuestion($durl),
        ],
        ['class' => 'primaryBtn getLeadForm']
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 5): ?>
    <?= frontend\helpers\Html::leadButton(
        'View Answer',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? UserService::parseDynamicCta('board_{slug}_wap_view_answer_cta_1', '', $pageName) : UserService::parseDynamicCta('board_{slug}_web_view_answer_cta_1', '', $pageName),
            'leadformtitle' => 'REGISTER TO DOWNLOAD SAMPLE PAPER',
            'subheadingtext' => $name,
            'image' => '/yas/images/defaultcardbanner.png',
            'durl' => Url::toBoardSampleQuestion($durl)
        ],
        ['class' => 'primaryBtn getLeadForm']
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 6): ?>
    <div class="col-md-5">
        <?= frontend\helpers\Html::leadButton(
            '<i class="spriteIcon alarmIcon"></i>' . $defaultEmptyCondition0  ? $ctaText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaText),
            [
                'entity' => $entity,
                'entityId' => '',
                'ctaLocation' => $defaultEmptyCondition0  ? 'boards_landing_web_banner_cta1' : $dynamicCta['cta_position_0']['web'],
                'ctaText' => $defaultEmptyCondition0  ? $ctaText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaText),
                'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
                'leadformtitle' => $defaultEmptyCondition0  ? 'REGISTER TO GET BOARD ALERTS' : ($dynamicCta['cta_position_0']['lead_form_title']) ?? 'REGISTER TO GET BOARD ALERTS',
                'redirection' => $defaultEmptyCondition0  ? null : $dynamicCta['cta_position_0']['page_link'],
                'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
                'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
            ],
            ['class' => 'desktopOnly primaryBtn setExamAlert getLeadForm leadBoardAutoCapture']
        ) ?>
    </div>
<?php endif; ?>

<?php if ($lead_cta == 7 && $isMobile): ?>
    <?= frontend\helpers\Html::leadButton(
        '<i class="spriteIcon alarmIcon"></i>' . $defaultEmptyCondition0 ? $ctaText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaText),
        [
            'entity' => $entity,
            'entityId' => '',
            'ctaLocation' => $defaultEmptyCondition0 ? 'boards_landing_wap_bottom_sticky_cta1' : $dynamicCta['cta_position_0']['wap'],
            'ctaText' => $defaultEmptyCondition0 ? $ctaText : ($dynamicCta['cta_position_0']['cta_text'] ?? $ctaText),
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO GET BOARD ALERTS' : ($dynamicCta['cta_position_0']['lead_form_title']) ?? 'REGISTER TO GET BOARD ALERTS',
            'redirection' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'],
            'alternateMedia' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['media'],
            'alternatePageRedirectSlug' => $defaultEmptyCondition0 ? '' : $dynamicCta['cta_position_0']['page_redirect_slug']
        ],
        ['class' => 'primaryBtn setExamAlert getLeadForm leadBoardAutoCapture']
    ) ?>
<?php endif; ?>