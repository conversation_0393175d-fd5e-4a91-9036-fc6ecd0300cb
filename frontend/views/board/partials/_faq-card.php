<?php

use common\helpers\BoardHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;

?>
<?php if (is_array($faqs) && !empty($faqs)): ?>
    <div class="faq_section<?= count($faqs) > 7 ? ' pageInfo' : '' ?>">
        <?php if (empty($pageName)) { ?>
            <h2>FAQs on <?= $displayName ?? '' ?></h2>
        <?php } else {
            $displayName = $displayName ?? '';
            $pageName =  $pageName ?? ''; ?>
            <h2>FAQs on <?= $displayName . ' ' . ucwords($pageName) ?></h2>
        <?php } ?>

        <div class="faqDiv">
            <?php foreach ($faqs as $faq): ?>
                <?php if (!empty($faq->question)): ?>
                    <div>
                        <p class="faq_question">
                            <?= 'Q: ' .
                                ContentHelper::htmlDecode($faq->question, true)
                            ?>
                        </p>
                        <div class="faq_answer" style="display: none;">
                            <?= 'A: ' .
                                ContentHelper::htmlDecode($faq->answer, false)
                            ?> 
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif; ?>