<?php

use common\helpers\ArticleDataHelper;
use common\helpers\BoardHelper;
use common\helpers\ContentHelper;
use common\models\Board;
use common\models\BoardSamplePaper;
use common\models\Lead;
use common\models\LiveUpdate;
use common\services\UserService;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Url;
use yii\helpers\ArrayHelper;
use common\helpers\DataHelper;
use frontend\helpers\Freestartads;

//utils
if (empty($subjectContent->author)) {
    $author = $subjectContent->defaultuser;
} else {
    $author = $subjectContent->author;
}
$currentUrl = Url::base(true) . Url::current();
$authorImage = !empty($author) ? ($author->profile->image ? Yii::getAlias('@profileDPFrontend') . '/' . $author->profile->image : '/yas/images/usericon.png') : '';
$subMenu = array_column(ArrayHelper::toArray($pages), 'page_slug');
$isMobile = \Yii::$app->devicedetect->isMobile();
$assetUrl = Yii::getAlias('@getmyuniExamAsset/');
$displayName = $board->display_name;
if (empty($displayName)) {
    $displayName = ucwords(str_replace('-', ' ', $board->slug));
}

//Board Date
if (!empty($board->dates[0]) || !empty($board->dates[1])) {
    $dates = [];
    if (empty($board->dates)) {
        return [];
    }
    foreach ($board->dates as $value) {
        if ($value['name'] != '-' && !empty($value['start-date'])) {
            $dates[$value['name']] = [
                'date' => $value['start-date'] . (empty($value['end-date']) ? '' : ' to ' . $value['end-date']),
                'type' => $value['type'],
            ];
        }
    }
}

//title
$this->title = BoardHelper::parseBoardContent($subjectContent->meta_title) ?? '';
$this->context->description = BoardHelper::parseBoardContent($subjectContent->meta_description) ?? '';
$this->context->ogImage = Yii::getAlias('@boardLogoFrontend/') . $board->logo;

// breadcrumbs
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'Boards', 'url' => [Url::toBoards()], 'title' => 'Boards'];
if ($pageName != 'overview') {
    $this->params['breadcrumbs'][] = ['label' => $displayName, 'url' => [Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code))], 'title' => $displayName];
}
$this->params['breadcrumbs'][] = ['label' => $page, 'url' => [Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code), $subjectContent->boardDetails->page_slug)], 'title' => $page];
$this->params['breadcrumbs'][] = ucwords(str_replace('-', ' ', $subjectContent->subject_slug));

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'board.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'board-detail.css', ['depends' => [AppAsset::class]]);

$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

//gmu params
$this->params['entity'] = BoardSamplePaper::ENTITY_BOARD_SAMPLE_PAPER;
$this->params['entity_id'] = $board->id ?? 0;
$this->params['entity_name'] = $board->name ?? '';
$this->params['entitySlug'] = $board->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $board->display_name ?? '';
$this->params['pageName'] = $pageName ?? '';

//schema
if (!empty($board)) {
    $this->params['schema'] = \yii\helpers\Json::encode([[
        '@context' => 'http://schema.org',
        '@type' => 'Article',
        'mainEntityOfPage' => [
            '@type' => 'WebPage',
            '@id' => $currentUrl,
        ],
        'headline' => $this->title ?? '',
        'image' => !empty($board->logo) ? [
            DataHelper::s3Path(null, 'board_genral', 'path') . '/' . $board->logo
        ] : '',
        'datePublished' => !empty($subjectContent->created_at) ? date(DATE_ATOM, strtotime($subjectContent->created_at)) : '',
        'dateModified' => !empty($subjectContent->updated_at) ? date(DATE_ATOM, strtotime($subjectContent->updated_at)) : '',
        'author' => [
            '@type' => 'Person',
            'name' => !empty($author) ? $author->name : ''
        ],
        'publisher' => [
            '@type' => 'Organization',
            'name' => 'Getmyuni',
            'logo' => [
                '@type' => 'ImageObject',
                'url' => Yii::$app->params['gmuLogo']
            ],
        ],
        'description' => $this->context->description,
    ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
}
?>
<style>
    @media (max-width: 1023px) {
        .cta-mobile {
            margin-top: 10px !important;
        }

    }

    table {
        table-layout: auto;
    }
</style>
<div class="">
    <header class="boardsheroSection commonHeroSection">
        <div class="row">
            <div class="col-md-12">
                <div class="row heroHeader">
                    <div class="imgContainer">
                        <img class="<?= $board->logo  ? '' : 'defaultLogoImage'; ?>" src="<?= $board->logo ? DataHelper::s3Path(null, 'board_genral', 'path') . '/' . $board->logo :  Url::defaultCollegeLogo(); ?>" alt="">
                    </div>
                    <div class="headingContainer">
                        <h1><?= empty($subjectContent->h1) ? ucwords($subjectContent->subject_slug) . ' Model Paper' : BoardHelper::parseBoardContent($subjectContent->h1)  ?></h1>

                    </div>
                </div>


            </div>
            <div class="col-md-12 second-row-date">
                <div class="examDates row m-0 col-md-8">
                    <?php if (!empty($dates)): ?>
                        <div class="row helpfulInfo">
                            <?php foreach ($dates as $key => $value): ?>
                                <div class="helpfuItem">
                                    <span class="spriteIcon calenderIcon"></span>
                                    <?php if (!empty($key)): ?>
                                        <span><?= $key == '-' ? '' : BoardHelper::$boardDates[$key] . ':' ?></span>
                                    <?php endif; ?>
                                    <?php $type = !empty($value['type'] == Board::MODE_TENTATIVE) ? ' (Tentative)' : '';
                                    $date = explode('to', $value['date']); ?>
                                    <?php if (!empty($date[0]) && !empty($date[1])): ?>
                                        <span><?= date('d', strtotime($date[0])) . ' ' . date('M', strtotime($date[0])) . " '" . date('y', strtotime($date[0])) . (empty($date[1]) ? '' : ' to ' . date('d', strtotime($date[1])) . ' ' . date('M', strtotime($date[1])) . " '" . date('y', strtotime($date[1]))) . $type ?></span>
                                    <?php else: ?>
                                        <span><?= date('d', strtotime($date[0])) . ' ' . date('M', strtotime($date[0])) . " '" . date('y', strtotime($date[0])) . $type ?></span>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="lead-cta" data-entity="board" data-lead_cta='1' data-sponsor="<?= $sponsorClientUrl ?>"></div>
            </div>

        </div>
    </header>
</div>

<!-- board menu -->
<?php if (!empty($pages)): ?>
    <nav class="stickyNavCls">
        <div class="pageRedirectionLinks">
            <p class="btn_left over">
                <i class="spriteIcon left_angle"></i>
            </p>
            <?php if (count($subMenu) > 12 && !$isMobile): ?>
                <p class="btn_right">
                    <i class="spriteIcon right_angle"></i>
                </p>
            <?php endif; ?>
            <?php if (count($subMenu) > 1 && $isMobile): ?>
                <p class="btn_right">
                    <i class="spriteIcon right_angle"></i>
                </p>
            <?php endif; ?>
            <ul>
                <?php foreach ($allBoardPages as $key => $value): ?>
                    <?php if (in_array($key, $subMenu)): ?>
                        <?php if ($key == $pageName): ?>
                            <li><span class="activeLink" title="<?= $board->display_name . ' ' . $value ?>"><?= $value ?></span></li>
                        <?php else: ?>
                            <li>
                                <?php if (($pageName == 'previous-year-question-papers') && $key == 'previous-year-question-papers'): ?>
                                    <a class="activeLink" title="<?= $board->display_name . ' ' . $value ?>" href="<?= $key == 'overview' ?  Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code)) : Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code), $key) ?>">
                                        <?= $value ?></a>
                                <?php elseif (($pageName == 'sample-papers') && $key == 'sample-papers'): ?>
                                    <a class="activeLink" title="<?= $board->display_name . ' ' . $value ?>" href="<?= $key == 'overview' ?  Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code)) : Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code), $key) ?>">
                                        <?= $value ?></a>
                                <?php elseif (($pageName == 'solved-question-papers') && $key == 'solved-question-papers'): ?>
                                    <a class="activeLink" title="<?= $board->display_name . ' ' . $value ?>" href="<?= $key == 'overview' ?  Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code)) : Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code), $key) ?>">
                                        <?= $value ?></a>
                                <?php else: ?>
                                    <a class="" title="<?= $board->display_name . ' ' . $value ?>" href="<?= $key == 'overview' ?  Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code)) : Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code), $key) ?>">
                                        <?= $value ?></a>
                                <?php endif;
                        endif; ?>
                            </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        </div>
    </nav>
<?php endif; ?>

<?php if ($isMobile): ?>
    <!--div class="lead-cta" data-entity="board" data-lead_cta='2'></div-->
<?php endif; ?>



<div class="row">
    <div class="col-md-8">
        <?php if (!$isMobile): ?>
            <aside>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                        <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                        ?>
                    </div>
                </div>
            </aside>
        <?php endif; ?>
        <div class="pageData">
            <?php if (isset($author->slug)): ?>
                <div class="updated-info row">
                    <div class="updatedBy">
                        <img data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= !empty($author) ? $author->name : '' ?>">
                    </div>
                    <div class="authorAndDate">
                        <a class="authorName" href="<?= Url::toAllAuthorPost($author->slug) ?>" title="<?= $author->name ?>"><?= !empty($author) ? $author->name : '' ?></a>
                        <span class="spriteIcon verifiedBlueTickIcon"></span>
                        <p>Updated on <?= Yii::$app->formatter->asDate($subjectContent->updated_at ?? 'today') ?></p>
                    </div>
                </div>
            <?php endif; ?>
            <?php if (!empty($recentActivity[0])): ?>
                <?= $this->render('../partials/_recentActivity', [
                    'recentActivity' => $recentActivity,
                    'title' => (!empty($board->display_name) ? $board->display_name : $board->name) . ' About'
                ]) ?>
            <?php endif; ?>
            <?= BoardHelper::parseBoardContent(ContentHelper::removeStyleTag(
                stripslashes(html_entity_decode(
                    DataHelper::parseDomainUrlInContent($subjectContent->content)
                ))
            )) ?>
        </div>

        <aside>
            <div class="horizontalRectangle mobileOnly">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__200x600')
                        ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>

        <?php if (!empty($pdfs)): ?>
            <div class="pageData">
                <?php foreach ($pdfs as $key => $value): ?>
                    <h3> <?= $key ?> </h3>
                    <?php foreach ($value as $ka => $va): ?>
                        <div class="questionPapersDiv row">
                            <p> <?= 'Set ' . '- ' . $ka ?> </p>
                            <div class="questionPapersLinks">
                                <?php if (!empty($va['question_paper'])): ?>
                                    <div class="lead-cta" data-entity="board" data-lead_cta='4' data-durl="<?= $va['question_paper'] ?>"></div>
                                <?php endif; ?>
                                <?php if (!empty($va['answer_paper'])): ?>
                                    <div class="lead-cta" data-entity="board" data-lead_cta='5' data-durl="<?= $va['answer_paper'] ?>"></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- faqs -->
        <?php /*if (!empty($faqs)): ?>
            <?= $this->render('partials/_faq-card', [
                'faqs' => $faqs,
            ]) ?>
        <?php endif; */ ?>

        <!-- college widget -->
        <?php if (!empty($stateColleges)): ?>
            <?= $this->render('partials/_colleges-card', [
                'stateColleges' => $stateColleges,
                'board' => $board,
                'assetUrl' => $assetUrl
            ]) ?>
        <?php endif; ?>

        <!-- exam widget -->
        <?= $this->render('partials/_exam-card', [
            'exams' => $exams,
            'board' => $board,
            'assetUrl' => $assetUrl
        ]) ?>

        <div class="removeFixedQuickLink">
            <!-- Do not Delete this -->
        </div>
    </div>

    <div class="col-md-4">
        <aside>
            <div class="getSupport">
                <!-- <div class="row">
                    <img src="<?= '/yas/images/get-support.png' ?>" alt="">
                    <p>Are you Interested in this Board?</p>
                </div> -->
                <div class="lead-cta" data-entity="board" data-lead_cta='3' data-sponsor="<?= $sponsorClientUrl ?>"></div>
            </div>

            <?php if (!empty($featuredNews) || !empty($recentNews)): ?>
                <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                    'featured' => $featuredNews,
                    'recents' => $recentNews,
                    'isAmp'  => 0,
                    'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                    'smallIcone' => 1
                ]); ?>
            <?php endif; ?>

            <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                    'trendings' => $trendingArticles,
                    'recentArticles' => $recentArticles,
                ]); ?>
            <?php endif; ?>

            <?php /* not needed for now
            <div class="sideBarSection desktopOnly">
                <p class="sidebarHeading"><span class="spriteIcon alarmIcon"></span> NOTIFICATIONS</p>
                <div class="sidebarLinks">
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                </div>
            </div>
            <?php */ ?>

        </aside>

        <?php /* not needed for now
        <div class="sideBarSection mobileOnly">
            <p class="sidebarHeading"><span class="spriteIcon alarmIcon"></span> NOTIFICATIONS</p>
            <div class="sidebarLinks">
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
            </div>
        </div>
        <?php */ ?>

        <aside>
            <div class="sidebarAds desktopOnly">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if (!$isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x250')
                        ?>
                </div>
                <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__300x250')
                        ?>
                </div>
                    <?php endif; ?>
            </div>
        </aside>
    </div>
</div>

<section class="commentSection">
    <?= $this->render('/partials/comment/_form', [
        'model' => $commentModel,
        'entity' => BoardSamplePaper::ENTITY_BOARD_SAMPLE_PAPER,
        'entity_id' => $subjectContent->id
    ]) ?>
    <?= $this->render('/partials/comment/_comment', [
        'comments' => $comments,
        'entity' => BoardSamplePaper::ENTITY_BOARD_SAMPLE_PAPER,
        'entityId' => $subjectContent->id
    ]) ?>
</section>

<!-- Board Article  -->
<?php if (!empty($articles)): ?>
    <?= $this->render('partials/_board-articles', [
        'articles' => $articles,
        'board' => $board,
    ]) ?>
<?php endif; ?>

<!-- Board News  -->
<?php if (!empty($boardExams)): ?>
    <?= $this->render('partials/_board-news', [
        'boardExamNews' => $boardExams,
        'board' => $board,
    ]) ?>
<?php endif; ?>

<!-- related article -->
<?php if (!empty($board->article)): ?>
    <?= $this->render('../partials/_productArticleCard', [
        'relatedArticles' => $board->article,
    ]); ?>
<?php endif; ?>

<!-- related News -->
<?php if (!empty($board->news)): ?>
    <?= $this->render('../partials/_productNewsCard', [
        'news' => $board->news,
    ]); ?>
<?php endif; ?>

<aside>
    <div class="horizontalRectangle">
        <div class="appendAdDiv" style="background:#EAEAEA; margin-bottom : 30px;">
            <?php if ($isMobile): ?>
                <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250')
                ?>
            <?php else: ?>
                <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90')
                ?>
            <?php endif; ?>
        </div>
    </div>
</aside>

<div id="comment-reply-form-js"></div>