<?php

use common\helpers\ArticleDataHelper;
use common\helpers\BoardHelper;
use common\helpers\DataHelper;
use common\models\Board;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Url;

//utils
$isMobile = \Yii::$app->devicedetect->isMobile();

//title
$defaultDescription = 'Get Complete Details about Time Table, Registration, Admit Card and Results for National Boards, State Boards and Open School Boards in India';
$this->title = ($homePageContent == false) ? 'Top Boards in India' : (!empty($homePageContent->title) ? DataHelper::parseMetaTopContent($homePageContent->title) : 'Top Boards in India');
$this->context->description =  ($homePageContent == false) ? $defaultDescription : (!empty($homePageContent->description) ? DataHelper::parseMetaTopContent($homePageContent->description) : $defaultDescription);
$this->context->ogImage = '';

// breadcrumbs
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Boards');

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'board.css', ['depends' => [AppAsset::class]]);


$this->params['entity'] = Board::ENTITY_BOARD;
$this->params['entity_id'] = $board->id ?? 0;
$this->params['entity_name'] = 'Boards-Listing-Page';

$lang_code = isset(DataHelper::$languageCode[Yii::$app->language]) ? Yii::$app->language : 'en';

$this->params['canonicalUrl'] = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();

?>
<style>
    .latestInfoSection .latestInfoTxt p:first-child,
    .latestInfoSection .latestInfoTxt a,
    .otherEntranceExams .latestInfoTxt p,
    .otherEntranceExams .latestInfoTxt a {
        -webkit-line-clamp: 2;
        min-height: 72px;
        max-height: 100%;
    }

    .latestInfoSection .latestInfoTxt,
    .otherEntranceExams .latestInfoTxt {
        padding: 16px 16px 0;
    }

    .authorAndDate {
        margin-bottom: 16px;
    }
</style>
<div class="">
    <header class="boardsheroSection">
        <div class="row">
            <div class="col-md-7">
                <h1> <?= ($homePageContent  == false) ? Yii::t('app', 'Search for Boards in India') : (!empty($homePageContent->h1) ? DataHelper::parseMetaTopContent($homePageContent->h1) : 'Search for Boards in India') ?></h1>
                <div class="searchBar">
                    <input class="searchForBoard search-autocomplete" data-type="board" id="autoComplete" autocomplete="off" placeholder="<?= Yii::t('app', 'Search National and State Boards in India') ?>" type="text" tabindex="1">
                    <div class="selection"></div>
                </div>
            </div>
            <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="lead-cta" data-entity="board" data-lead_cta='6'></div>
            <?php endif; ?>
        </div>
    </header>

    <?php if ($isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
        <div class="setAlarmDiv mobileOnly">
            <div class="lead-cta" data-entity="board" data-lead_cta='7'></div>
        </div>
    <?php endif; ?>

    <aside>
        <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
            <div class="horizontalRectangle desktopOnly">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if (!$isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                        ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </aside>

    <?php foreach ($boardList as $type => $value): ?>
        <div class="pageData">
            <h2 class="m-0"><?= Yii::t('app', BoardHelper::$boardType[$type]) ?></h2>
        </div>
        <div class="boardsList row">
            <?php foreach ($boardList[$type] as $boardName => $value):
                $logo = '';
                foreach ($value as $key => $val) {
                    if ($val['logo'] != '') {
                        $logo  = $val['logo'];
                        break;
                    }
                }
                //$logo = !empty($value[0]['logo']) ? $value[0]['logo'] : (!empty($value[1]['logo']) ? $value[1]['logo'] : (!empty($value[2]['logo']) ? $value[2]['logo'] : ''));
                ?>
                <div class="boardsListCard">
                    <div class="row">
                        <?php if (!empty($logo)): ?>
                            <div class="boardsLogo">
                                <img class="lazyload" width="64" height="64" loading="lazy" title="<?= $type == Board::TYPE_STATE ? (($boardName == 'Delhi NCR') ? 'Delhi Board' : $boardName . ' Board') : $boardName ?>" data-src="<?= DataHelper::s3Path(null, 'board_genral', 'path') . '/' . $logo ?>" src="<?= DataHelper::s3Path(null, 'board_genral', 'path') . '/' . $logo ?>" alt="">
                            </div>
                        <?php endif; ?>
                        <h3>
                            <p title="<?= $type == Board::TYPE_STATE ? (($boardName == 'Delhi NCR') ? Yii::t('app', 'Delhi Board') : Yii::t('app', $boardName) . ' ' . Yii::t('app', 'Board')) : Yii::t('app', $boardName) ?>"><?= $type == Board::TYPE_STATE ? (($boardName == 'Delhi NCR') ? Yii::t('app', 'Delhi Board') : Yii::t('app', $boardName) . ' ' . Yii::t('app', 'Board')) : Yii::t('app', $boardName) ?></p>
                        </h3>
                    </div>
                    <ul class="listImage">
                        <?php foreach ($value as $board):
                            $displayName = (strpos($board['display_name'], 'Board') !== false) ? $board['display_name'] : $board['display_name'] . ' Board';
                            if (empty($displayName)) {
                                $displayName = ucwords(str_replace('-', ' ', $board['slug']));
                            }
                            $boardSlug = (!empty(BoardHelper::$redirectBoards[$board['slug']])) ? BoardHelper::$redirectBoards[$board['slug']] : $board['slug'];
                            ?>
                            <li><a href="<?php echo Url::toBoardDetail($boardSlug, DataHelper::getLangCode($board['lang_code'])) ?>" title="<?= $displayName ?>"><?= $displayName ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endforeach; ?>
    <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280') ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>
    <?php if (count($articles)): ?>
        <section class="pageData">
            <h2 class="row"><?= Yii::t('app', 'Explore articles on Board Exams') ?>
                <a href="<?= Url::toArticleDetail('boards', $lang_code) ?>" class="viewAll"> <?= Yii::t('app', 'VIEW ALL') ?></a>
            </h2>
            <div class="customSlider four-cardDisplay">
                <div class="customSliderCards">
                    <?php $articlesCount = 0;
                    $s3Path = 'https://media.getmyuni.com/yas/images/defaultcardbanner.png'; ?>
                    <?php foreach ($articles as $article):
                        $board_source_img = $article['cover_image'] ? ArticleDataHelper::getImage($article['cover_image']) : ArticleDataHelper::getImage();
                        if ($articlesCount == 4) {
                            break;
                        }
                        $articlesCount++; ?>
                        <div class="sliderCardInfo">

                            <a href="<?= Url::toArticleDetail($article['slug'], DataHelper::getLangCode($article['lang_code'])) ?>" title="<?= $article['h1'] ?>">
                                <img class="lazyload" width="275" height="207" loading="lazy" onclick="gmu.url.goto('<?= Url::toArticleDetail($article['slug'], DataHelper::getLangCode($article['lang_code'])) ?>')" data-src="<?= $board_source_img  ?>" src="<?= $board_source_img  ?>" alt="<?= $article['h1'] ?>" />
                                <div class="textDiv">
                                    <p class="widgetCardHeading"><?= $article['h1'] ?? '' ?></p>
                                    <p class="subText"><?= isset($article['user_name']) && !empty($article['user_name']) ? $article['user_name'] : $article['name'] ?? '' ?></p>
                                </div>
                            </a>

                        </div>
                    <?php endforeach; ?>
                    <div class="sliderCardInfo mobileOnly">
                        <div class="viewAllDiv">
                            <a href="<?= Url::toArticleDetail('boards', $lang_code) ?>"><i class="spriteIcon viewAllIcon"></i><?= Yii::t('app', 'VIEW ALL') ?></a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <?php if (!empty($boardExams)): ?>
        <section class="pageData">
            <h2 class="row"><?= Yii::t('app', 'Latest NEWS ON BOARD EXAMS') ?>
                <a href="<?= Url::toNewsDetail('board-exams', $lang_code) ?>" class="viewAll"> <?= Yii::t('app', 'VIEW ALL') ?></a>
            </h2>
            <div class="customSlider four-cardDisplay">
                <div class="customSliderCards">
                    <?php $boardExamCount = 0 ?>
                    <?php foreach ($boardExams as $boardexam):
                        if (empty($boardexam)) {
                            return [];
                        }
                        if ($boardExamCount == 4) {
                            break;
                        }
                        $boardExamCount++; ?>
                        <div class="sliderCardInfo">
                            <a href="<?= $boardexam['slug'] ? Url::toNewsDetail($boardexam['slug']) : '' ?>" title="<?= $boardexam['title'] ?? '' ?>">
                                <img class="lazyload" width="275" height="207" loading="lazy" data-src="<?= $boardexam['banner_image'] ? Url::toNewsImages($boardexam['banner_image']) : Url::toNewsImages() ?>" src="<?= $boardexam['banner_image'] ? Url::toNewsImages($boardexam['banner_image']) : Url::toNewsImages() ?>" alt="<?= $boardexam['title'] ?? '' ?>">
                                <div class="textDiv">
                                    <p class="widgetCardHeading"><?= $boardexam['title'] ?? '' ?></p>
                                </div>
                            </a>
                            <div class="authorAndDate">
                                <a class="authorName" href="<?= Url::toAllAuthorPost($boardexam['authorSlug'], DataHelper::getLangCode($lang_code)) ?>" title="<?= $boardexam['user_name'] ?>">
                                    <?= isset($boardexam['user_name']) && !empty($boardexam['user_name']) ? $boardexam['user_name'] : $boardexam['author'] ?? '' ?>
                                </a>
                                <?php if (!empty($boardexam['updated_at'])): ?>
                                    <p class="widgetAUthorName"><?= Yii::$app->formatter->asDate($boardexam['updated_at']) ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <div class="sliderCardInfo mobileOnly">
                        <div class="viewAllDiv">
                            <a href="<?= Url::toNewsDetail('board-exams', $lang_code) ?>"><i class="spriteIcon viewAllIcon"></i><?= Yii::t('app', 'VIEW ALL') ?></a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA; margin-bottom : 30px;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>
</div>