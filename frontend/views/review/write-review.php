<style>
    @media (max-width: 1023px) {
        .blueBgDiv {
            background: var(--topheader-bg);
            width: 100%;
            height: 75px;
            margin-top: 1px
        }
    }
</style>
<?php

use frontend\assets\AppAsset;

//utils
$this->title = 'GetMyUni: The School Book Campaign';
$this->registerCssFile(Yii::$app->params['cssPath'] . 'review.css', ['depends' => [AppAsset::class]]);


$stepCount = count($categoryQuestions) + 3;
?>
<main>
    <div class="">
        <div class="reviewPageForm">
            <div class="formsStepsDiv">
                <div class="stepsBarDiv">
                    <div class="row m-0 justify-content-between">
                        <span>Step <span class="stepCount">1</span> of <?= $stepCount ?></span>
                        <span class="stepPercent">0%</span>
                    </div>
                    <p class="progressBar">
                        <span class="progressBarLine"></span>
                    </p>
                </div>
            </div>
            <div class="reviewFormDiv">
                <?= $this->render('partials/_write-review-form', [
                    'form_questions' => $form_questions,
                    'categoryQuestions' => $categoryQuestions,
                    'user' => $user,
                    'studentReferralCode' => $studentReferralCode,
                    'referralCode' => $referralCode ?? ''
                ]); ?>
            </div>

        </div>
    </div>
</main>
<div class="review__Error__Popup">
    <div class="review_center_align">
        <div class="spriteIcon close__review__error__popup"></div>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" fill="#ff4e53" />
            <path
                d="M15.536 8.464a1 1 0 0 0-1.414 0L12 10.586l-2.122-2.122a1 1 0 1 0-1.414 1.414L10.586 12l-2.122 2.122a1 1 0 1 0 1.414 1.414L12 13.414l2.122 2.122a1 1 0 0 0 1.414-1.414L13.414 12l2.122-2.122a1 1 0 0 0 0-1.414z"
                fill="white" />
        </svg>
        <p class="error__text"></p>
    </div>
</div>
<?php $this->registerJsFile(Yii::$app->params['jsPath'] . 'review.js', ['position' => \yii\web\View::POS_END, 'defer' => true, 'depends' => [AppAsset::class]]); ?>