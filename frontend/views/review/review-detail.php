<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\models\College;
use common\models\Lead;
use common\models\LiveUpdate;
use common\models\Review;
use frontend\assets\AppAsset;
use frontend\helpers\Lead as HelpersLead;
use frontend\helpers\Url;
use common\helpers\DataHelper;
use common\models\CollegeContent;

//utils
$currentUrl = Url::base(true);
$isMobile = \Yii::$app->devicedetect->isMobile();

//meta
$this->title = !empty($student) && !empty($student['name']) ? $student['name'] . '\'s' . ' Review On ' . $review['collegeName'] . ' - GetMyUni' ?? '' : '';
$this->context->description = !empty($student) && !empty($student['name']) ? 'Read ' . $student['name'] . '\'s' . ' full Review and Rating of ' . $review['courseName'] ?? '' . ' from ' . $review['collegeName'] ?? '' : '';

// breadcrumbs
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'Reviews', 'url' => ['/college/reviews'], 'title' => 'Reviews'];
if (!empty($reviewCollegeContentStatus) && $reviewCollegeContentStatus == CollegeContent::STATUS_ACTIVE) {
    $this->params['breadcrumbs'][] = ['label' => !empty($review) && !empty($review['collegeName']) ? $review['collegeName'] . ' Reviews' : '', 'url' => Url::toCollege($review['collegeSlug'], 'reviews'), 'title' => 'College Reviews'];
}
$this->params['breadcrumbs'][] = ['label' => !empty($student) && !empty($student['name']) ? $student['name'] . ' Review' : ''];

$this->registerCssFile(Yii::$app->params['cssPath'] . 'review.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

// page specific assets
$this->registerCssFile('https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css');


if (!empty($contentsByOrder)) {
    $reviewSchemaBody = [];
    foreach ($contentsByOrder as $key => $content) {
        if (empty($content['content'])) {
            continue;
        }
        $reviewSchemaBody[] = strip_tags(stripslashes(
            html_entity_decode(DataHelper::parseDomainUrlInContent($content['content']))
        ));
    }
}

// gmu params
$this->params['entity'] = Review::ENTITY_REVIEW;
$this->params['entity_id'] = $review->id ?? 0;

$ratingValue = (empty($categoryRating) || CollegeHelper::getTotalRating($categoryRating) == 0)
    ? ((empty($collegOverrallRating) || CollegeHelper::getTotalRating($collegOverrallRating) == 0)
        ? ''
        : CollegeHelper::getTotalRating($collegOverrallRating))
    : CollegeHelper::getTotalRating($categoryRating);

//schema
if (!empty($reviewSlug)) {
    $this->params['schema'] = \yii\helpers\Json::encode([[
        '@context' => 'http://schema.org',
        '@type' => 'Review',
        'itemReviewed' => [
            '@type' => 'CollegeOrUniversity',

            'name' => $review['collegeName'],

            'image' => DataHelper::s3Path(null, 'college_logo', 'path') . '/' . $review['collegeLogo'],
        ],
        'reviewRating' => [
            '@type' => 'Rating',
            'ratingValue' => (float) $ratingValue,
            'worstRating' => 1,
            'bestRating' => 5
        ],
        'name' => !empty($student) && !empty($student['name']) ? $student['name'] . '\'s' . ' Review On ' . $review['collegeName'] ?? '' : '',
        'author' => [
            '@type' => 'Person',
            'name' => !empty($student) && !empty($student['name']) ? $student['name'] : '',
        ],
        'reviewBody' => $reviewSchemaBody ? ContentHelper::removeStyleTag(stripslashes(html_entity_decode(join('', $reviewSchemaBody)))) : [],
        'publisher' => [
            '@type' => 'Organization',
            'name' => 'Getmyuni'
        ]
    ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
}
?>
<div class="reviewContainer">
    <!-- header -->
    <?= $this->render('partials/_review-detail-header', [
        'isMobile' => $isMobile,
        'student' => $student,
        'review' => $review,
        'categoryRating' => $categoryRating,
        'currentUrl' => $currentUrl,
        'college' => $college
    ]) ?>
    <div class="row mainReviewContainer">
        <?= $this->render('partials/_review-detail-card', [
            'contentsByOrder' => $contentsByOrder,
            'categoryRating' => $categoryRating,
            'reviewFormAnswers' => $reviewFormAnswers,
            'reviewImages' => $reviewImages,
            'review' => $review
        ]) ?>
        <aside class="col-md-4 sideWidgets">
            <div class="getSupport">
                <!-- <div class="supportSection">
                    <img class="lazyload" loading="lazy" data-src="https://media.getmyuni.com/yas/images/bulbIcon.svg" src="https://media.getmyuni.com/yas/images/bulbIcon.svg" alt="">
                    <p>Are you Interested in this College?</p>
                </div> -->
                <?php
                $cta1 = $isMobile ? '.talk_to_experts_wap' : '.talk_to_experts_web';
                $cta2 = $isMobile ? '.apply_now_wap' : '.apply_now_web';
                ?>
                <?= frontend\helpers\Html::leadButton(
                    'Talk To Experts',
                    [
                        'entity' => Lead::ENTITY_REVIEW,
                        'entityId' => $review['id'] ?? '',
                        'ctaLocation' => HelpersLead::getCTAsName(Review::ENTITY_REVIEW . $cta1),
                        'ctaText' => 'Talk To Experts',
                        'leadformtitle' => 'REGISTER FOR EXPERT GUIDANCE',
                        'subheadingtext' => '',
                        'image' => 'https://media.getmyuni.com/yas/images/defaultcardbanner.png',
                        'collegeId' => $review['college_id'] ?? ''
                    ],
                    ['class' => 'buttonOne collegeLeadAutoFetch']
                ) ?>
                <?= frontend\helpers\Html::leadButton(
                    'Apply Now',
                    [
                        'entity' => Lead::ENTITY_REVIEW,
                        'entityId' => $review['id'] ?? '',
                        'ctaLocation' => HelpersLead::getCTAsName(Lead::ENTITY_REVIEW . $cta2),
                        'ctaText' => 'Apply Now',
                        'leadformtitle' => 'REGISTER TO APPLY',
                        'subheadingtext' => '',
                        'image' => 'https://media.getmyuni.com/yas/images/defaultcardbanner.png',
                        'collegeId' => $review['college_id'] ?? ''
                    ],
                    ['class' => 'buttonTwo collegeLeadAutoFetch']
                ) ?>
            </div>

            <?php if (!$isMobile): ?>
                <?php if (isset($college['parent_college']) && !empty($college['parent_college'])): ?>
                    <?= $this->render('/review/partials/_main_college_campus', [
                        'college' => $college['parent_college'] ?? [],
                    ]) ?>
                <?php endif; ?>

                <?php if ($liveApplicationForm && !empty($college['college']) && $college['college']['is_sponsored'] == College::SPONSORED_NO): ?>
                    <?= $this->render('/college/partials/_live-application', [
                        'applications' => $liveApplicationForm
                    ]) ?>
                <?php endif; ?>

                <?php if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif; ?>
            <?php endif; ?>

            <!-- <div class="sidebarAds" style="margin-top: 0px;">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                </div>
                <div class="appendAdDiv" style="background:#EAEAEA;">
                </div>
            </div> -->
        </aside>

        <?php if (!empty($otherReviewsCard)): ?>
            <?= $this->render('partials/_other-reviews-card', [
                'otherReviews' => $otherReviewsCard,
                'isMobile' => $isMobile,
                'reviewSlug' => $reviewSlug,
                'reviewService' => $reviewService,
                'categoryRating' => $categoryRating,
            ]) ?>
        <?php endif; ?>
        <?php if ($isMobile): ?>
            <?php if (isset($college['parent_college']) && !empty($college['parent_college'])): ?>
                <?= $this->render('/review/partials/_main_college_campus', [
                    'college' => $college['parent_college'] ?? '',
                ]) ?>
            <?php endif; ?>
            <?php if ($liveApplicationForm && !empty($college['college']) && $college['college']['is_sponsored'] == College::SPONSORED_NO): ?>
                <?= $this->render('/college/partials/_live-application', [
                    'applications' => $liveApplicationForm
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($featuredNews) || !empty($recentNews)): ?>
                <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                    'featured' => $featuredNews,
                    'recents' => $recentNews,
                    'isAmp'  => 0,
                    'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                    'smallIcone' => 1
                ]); ?>
            <?php endif; ?>

            <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                    'trendings' => $trendingArticles,
                    'recentArticles' => $recentArticles,
                ]); ?>
            <?php endif; ?>
        <?php endif; ?>
        <div class="col-md-8 lowerWidgets">
            <?php if (!empty($collegesByCourseLocation) && count($collegesByCourseLocation) > 2): ?>
                <?= $this->render('partials/_college-card', [
                    'collegesByCourseLocation' => $collegesByCourseLocation,
                    'review' => $review
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($examsByCollege) && count($examsByCollege) > 2): ?>
                <?= $this->render('partials/_exam-card', [
                    'examsByCollege' => $examsByCollege,
                    'review' => $review
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($newsCards) && count($newsCards) > 2): ?>
                <?= $this->render('partials/_news-card', [
                    'newsCards' => $newsCards,
                    'review' => $review
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($recentArticles) && count($recentArticles) > 2): ?>
                <?= $this->render('partials/_articles-card', [
                    'articleCards' => $recentArticles,
                    'review' => $review
                ]) ?>
            <?php endif; ?>
        </div>
    </div>
    <div class="removeFixedQuickLink">
        <!-- Do not Delete this -->
    </div>
</div>