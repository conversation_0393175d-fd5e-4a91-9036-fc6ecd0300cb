<?php

use common\models\Review;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use yii\helpers\Html;

//utlis
$currentUrl = Url::base(true);
$isMobile = \Yii::$app->devicedetect->isMobile();

//meta data
$this->title = 'College Reviews - Explore Colleges based on Student Reviews';
$this->context->description = 'Find all the latest college reviews by students and alumni across the country for top colleges & Universities in India.';

// breadcrumbs
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'Reviews'];

$this->registerCssFile(Yii::$app->params['cssPath'] . 'review.css', ['depends' => [AppAsset::class]]);

$this->params['entity'] = Review::ENTITY_REVIEW;
$this->params['entity_id'] = 0;

?>

<div class="review reviewListBody">
    <header class="reviewsheroSection">
        <div class="row">
            <div class="col-md-7">
                <h1>Get Over <?= $totalCount ?>+ Student Reviews From Top Colleges</h1>
                <div class="searchBar">
                    <input class="searchForCollege search-autocomplete-review" data-type="college-review" id="autoComplete" autocomplete="off" placeholder="Search for College Reviews" type="text" tabindex="1">
                </div>
            </div>
            <div class="col-md-5">
                <?php if (!$isMobile): ?>
                    <a class="primaryBtn writeReviewRedirect" href="<?= $currentUrl . '/review/create' ?>" target="_blank">Write a Review</a>
                <?php elseif ($isMobile): ?>
                    <a class="primaryBtn mobileOnly writeReviewRedirect" href="<?= $currentUrl . '/review/create' ?>" target="_blank">Write a Review</a>
                <?php endif; ?>
            </div>
        </div>
    </header>
    <div class="row">
        <div class="col-md-3 lg-pr-0 desktopOnly">
            <?php if (!$isMobile): ?>
                <?= $this->render('partials/_review-filter-search', [
                    'filters' => $filters,
                    'model' => $searchModel,
                    'totalReviewCount' => $totalCount,
                    'selectedFilters' => $selectedFilters,
                ]) ?>
            <?php endif ?>
        </div>
        <?php if ($isMobile): ?>
            <?= $this->render('partials/_review-filter-search-mobile', [
                'filters' => $filters,
                'model' => $searchModel,
                'selectedFilters' => $selectedFilters,
                'totalCollegeCount' => $totalCount,
                'sort' => $dataProvider->sort
            ]) ?>
        <?php endif ?>
        <div class="col-md-9">
            <?php if (!$isMobile): ?>
                <div class="sortBySection row">
                    <div class="">
                    </div>
                    <div class="sortByList">
                        <?= Html::dropDownList('review-sort', 'sort', [
                            '' => 'Sort By',
                            'highest_rating' => 'Highest Rating',
                            'lowest_rating' => 'Lowest Rating',
                            'newest_rating' => 'Newest Rating',
                            'oldest_rating' => 'Oldest Rating',
                        ], ['id' => 'review-sort']) ?>
                    </div>
                </div>
            <?php endif; ?>
            <?php echo $this->render('partials/_review-filter-list', [
                'models' => $reviews,
                'hasNext' => $hasNext,
                'page' => $page,
                'isMobile' => $isMobile,
                'pagination' => $dataProvider->pagination,
                'reviewService' => $reviewService
            ]) ?>

            <!-- <aside>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                    </div>
                </div>
            </aside> -->
            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
        </div>
    </div>
</div>