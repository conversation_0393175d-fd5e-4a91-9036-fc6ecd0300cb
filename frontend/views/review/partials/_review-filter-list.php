<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\ReviewHelper;
use justinvoelker\separatedpager\LinkPager;

// page specific assets
$this->registerCssFile('https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css');
?>
<div class="moreReviews moreReviewsLanding">
    <?php foreach ($models as $model): ?>
        <?php if (isset($model) && empty($model['student_name'])): ?>
            <?php continue; ?>
        <?php endif ?>
        <?php
        $reviewImages = $reviewService->getReviewImages($model['review_id']);
        $categoryRating = $reviewService->getReviewCategoryRating($model['review_id']);
        $content = $reviewService->getOtherContent($model['review_id']);
        $review = $reviewService->getReviewById($model['review_id']); ?>
        <div class="reviewCard">
            <div class="reviewerDetailCardHeader">
                <?php $reviewImage = !empty($review) ? (!empty($review->user) ? ReviewHelper::getReviewProfileImage($review->user->profile_pic) : '') : ''; ?>
                <img class="lazyload reviewerIconCardHeader" loading="lazy" data-src="<?= $reviewImage ?? '/yas/images/defaultReviewImageSmall.png' ?>" src="<?= $reviewImage ?? '/yas/images/defaultReviewImageSmall.png' ?>" alt="" />
                <span class="verifiedSpan">
                    <span class="spriteIcon verifiedShieldIcon"></span>
                    Verified
                </span>
                <div class="reviewerHeaderContent reviewLandingHeader">
                    <?php if (!$isMobile): ?>
                        <div>
                            <span class="reviewerNameCardHeader"><?= $model['student_name'] ?? '' ?> </span>
                            <span><?= $model['course_name'] ?? '' ?> </span>
                            <span>Batch of <?= $model['batch'] ?? '' ?> </span>
                        </div>
                        <div class="reviewLandingCollegeInfo">
                            <span>Review on <?= $model['college_name'] ?? '' ?> </span>
                            <?php if (!empty($model['review_created_at'])): ?>
                                <span>Reviewed on <?= date('M d, Y', strtotime($model['review_created_at'])) ?> </span>
                            <?php endif; ?>
                            <?php if (!empty($model['review_overall_rating']) && $model['review_overall_rating'] > 0): ?>
                                <div class="starRating">
                                    <span><?= $model['review_overall_rating'] < 1 ? 1 :   $model['review_overall_rating'] ?></span>
                                    <ul class="stars">
                                        <?= CollegeHelper::getTotalStars($model['review_overall_rating'] ?? ''); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    <?php if ($isMobile): ?>
                        <span class="reviewerNameCardHeader"><?= $model['student_name'] ?? '' ?> </span>
                        <span><?= $model['course_name'] ?? '' ?> </span>
                        <span>Review on <?= $model['college_name'] ?? '' ?> </span>
                        <span>Batch of <?= $model['batch'] ?? '' ?> </span>
                        <?php if (!empty($model['review_created_at'])): ?>
                            <span>Reviewed on <?= date('M d, Y', strtotime($model['review_created_at'])) ?> </span>
                        <?php endif; ?>
                        <?php if (!empty($model['review_overall_rating']) && $model['review_overall_rating'] > 0): ?>
                            <div class="starRating reviewLandingStarRating">
                                <span><?= $model['review_overall_rating'] < 1 ? 1 :  $model['review_overall_rating'] ?></span>
                                <ul class="stars">
                                    <?= CollegeHelper::getTotalStars($model['review_overall_rating'] ?? ''); ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            <?php if (!empty($review->title) && $review->title !== 'null'): ?>
                <h4><?= "\"$review->title\"" ?></h4>
            <?php endif; ?>
            <?php if (!empty($categoryRating)): ?>
                <div class="ratingList">
                    <?php ReviewHelper::getUserCatgoryRating($categoryRating); ?>
                </div>
            <?php endif; ?>
            <?php if (isset($content) && !empty($content['content'])): ?>
                <p><?= $content['name'] ?> :<?= strip_tags(ContentHelper::removeStyleTag(stripslashes(html_entity_decode($content['content'])))); ?></p>
            <?php endif; ?>
            <?php if (isset($reviewImages) && !empty($reviewImages)): ?>
                <?= $this->render('_review-images', [
                    'reviewImages' => $reviewImages,
                    'isMobile' => $isMobile,
                ]) ?>
            <?php endif; ?>
            <div class="redirectreviewCard redirectionUrl">
                <a class="" href="/reviews/<?= $model['review_slug'] ?>" target="_blank">Read More <span class="spriteIcon urlIcon"></span></a>
                <!-- <p>Was this review helpful <span class="spriteIcon likeBtn"></span> 28 | <span class="spriteIcon unlikeBtn"></span>2</p> -->
            </div>
        </div>
    <?php endforeach; ?>
</div>
<div class="loadMoreReviews">
    <?php if ($hasNext): ?>
        <p class="col-12 loadMoreListReview" data-page="<?= $page ?>">LOAD MORE <span class="spriteIcon redCaret"></span></p>
    <?php endif; ?>
</div>