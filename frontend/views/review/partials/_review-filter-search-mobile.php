<?php

use frontend\helpers\Html;
use frontend\models\ReviewElasticSearch;
use yii\helpers\ArrayHelper;
use yii\helpers\Inflector;
use yii\widgets\ActiveForm;

$appliedFilterIndexing = ArrayHelper::index($selectedFilters, 'reviewFilterGroup_name');
?>
<section id="reviewFilterDSection">
    <div class="mobileSortandFilter">
        <div class="optionDiv">
            <button class="mobileSort"><i class="spriteIcon sortIcon"></i> SORT</button>
            <button class="mobileFilter"><i class="spriteIcon filterIcon"></i>
                FILTER</button>
        </div>
    </div>
    <div class="mobileSortSection">
        <div class="mobileReviewSortDiv">
            <p class="mobileSortHeading">SORT BY <i class="spriteIcon close_sortPopup"></i></p>
            <?php
            $items = [
                'highest_rating' => 'Highest Rating',
                'lowest_rating' => 'Lowest Rating',
                'newest_rating' => 'Newest Rating',
                'oldest_rating' => 'Oldest Rating',
            ];
            echo Html::checkboxList('review-sort', $sort, $items, [
                'tag' => 'ul',
                'item' => function ($index, $label, $name, $checked, $value) {
                    $html = ' <label for="' . $value . '">' . $label . '</label>';
                    $html .= Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                    return Html::tag('li', $html);
                }
            ]);
            ?>
        </div>
    </div>
    <div class="mobileOnly selectedResultsMobile">
        <p class="foundesults">Found <?= $totalCollegeCount ?? '' ?> Reviews</p>
        <?php if (!empty($selectedFilters)):
            $count = count($selectedFilters);
            ?>
            <div id="selectedReviewFilters" class="filterReviewDiv">
                <div class="foundReviews">
                    <?php foreach ($selectedFilters as $filter): ?>
                        <button id="<?= $filter['slug'] ?>"><?= $filter['name'] ?> <i class="spriteIcon closeIcon remove-review-filter"></i></button>
                    <?php endforeach; ?>
                    <?php if ($count > 2): ?>
                        <span class="showMoreReviews"><?= '+' . ($count - 2) . 'more' ?></span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <div class="mobileFilterSectionReview">
        <p id="clearAllReviews" class="mobileFilterHeading">FILTERS <span>RESET</span></p>
        <div class="filterTabReview">
            <ul class="tabsReview">
                <?php foreach ($filters as $key => $values):
                    foreach ($values as $k => $val):
                        if (key_exists($key, $appliedFilterIndexing)): ?>
                            <?php $appliedFilter = true; ?>
                        <?php else: ?>
                            <?php $appliedFilter = false; ?>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    <li class="tab-link <?= ($appliedFilter) ? 'appliedFilter' : '' ?>" data-tab="<?= Inflector::slug($key) ?>"><?= $key ?></li>
                <?php endforeach; ?>
            </ul>

            <div class="filterContentDiv">
                <?php $form = ActiveForm::begin([
                    'id' => 'review-filter-form'
                ]); ?>

                <?php foreach ($filters as $key => $items): ?>
                    <?php if (isset(ReviewElasticSearch::$propertyMapping[$key])): ?>
                        <div id="<?= Inflector::slug($key) ?>" class="tab-content current">
                            <div class="filterReviewSearch">
                                <input type="text" placeholder="Search options">
                                <?= $form->field($model, ReviewElasticSearch::$propertyMapping[$key])->checkboxList($items, [
                                    'tag' => 'ul',
                                    'item' => function ($index, $label, $name, $checked, $value) {
                                        $html = Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                                        $html .= $label;
                                        return Html::tag('li', $html);
                                    }
                                ])->label(false) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
                <?php ActiveForm::end(); ?>
            </div>
        </div>

        <div class="filterOptionDiv">
            <button class="closeFilter"> CLOSE</button>
            <button class="applyFilter"> APPLY FILTER</button>
        </div>
    </div>
</section>