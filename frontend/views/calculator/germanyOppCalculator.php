<?php

use common\models\calculator\GermanyOppCalculator;
use common\models\calculator\PrCalculator;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use yii\bootstrap\ActiveForm;

$ageList = PrCalculator::ageList();
$educationList = PrCalculator::educationQualificationList();
$workExperienceList = PrCalculator::workExperienceList();

//utils
$this->title = $seoTags['title'];
$this->context->description = $seoTags['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Immigration');
$this->params['canonicalUrl'] = Url::base(true) . '/immigration';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'prCalculator.css', ['depends' => [AppAsset::class]]);
$isFAQ = (isset($faqList->qnas) && !empty($faqList->qnas)) ?? $faqList->qnas;
$showform = 0;
if (Yii::$app->request->post()) {
    $showform = 1;
}
?>
<style>
    .container {
        max-width: 1353px !important;
    }
</style>
<main class="gis-main">
    <div class="gis-pr-main container">
        <h1 class="gis-pr-heading">Germany Global Opportunity Card Calculator</h1>
        <p class="gis-pr-desc">
            The Opportunity Card lets non-EU candidates enter Germany to find a job without needing a job contract. They must be skilled workers or score six points on a points system and show proof of funds that can support themselves while there.
        </p>
    </div>
    <?php $form = ActiveForm::begin(['id' => 'calculator']) ?>
    <div class="gis-pr-calculator container">
        <div class="gis-pr-timeline" id="gis-pr-timeline">
            <div class=" gis-pr-step">
                <div class="gis-pr-step-number">1</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Country of Citizenship</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">2</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Financial Proof</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">3</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Qualification</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">4</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Work Experience</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">5</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Language Skills</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">6</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Age</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">7</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Previous Stays in Germany</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">8</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Spouse's Application</div>
            </div>
        </div>
        <div class="gis-pr-form-container">
            <div class="gis-pr-forms">
                <div class="gis-pr-form <?php if ($score == 0  || $showform) {
                                            echo ' active ';
                                        } ?>" id="form1">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Country of Citizenship</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <button class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40"
                                        width="40" alt="unlock" />
                                    <span class="unlock-score">
                                        Unlock Score
                                    </span>
                                </button>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php //endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Are you a citizen of a country outside the EU?</span>
                            </p>
                            <div class="gis-pr-form-field citizen germany-opp">
                                <?= $form->field($model, 'countryCitizenship')->dropDownList(GermanyOppCalculator::YES_NO, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'citizen'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form2">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Financial Proof</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0' ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                How will you prove your financial resources for the visa application?
                            </p>
                            <div class="gis-pr-form-field financialPrf">
                                <?= $form->field($model, 'financialProof')->dropDownList(GermanyOppCalculator::FINANCIAL_PROOF_LIST, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'financialPrf'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form3">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Qualification</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Do you have a recognized Bachelor's degree or higher education/vocational training?
                            </p>
                            <div class="gis-pr-form-field qual">
                                <?= $form->field($model, 'qualification')->dropDownList(GermanyOppCalculator::QUALIFICATION_LIST, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'qual'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form4">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading"> Work Experience
                        </h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                How many years of work experience do you have in a relevant field?
                            </p>
                            <div class="gis-pr-form-field workExp">
                                <?= $form->field($model, 'workExperience')->dropDownList(GermanyOppCalculator::WORK_EXP_LIST, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'workExp'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form5">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading"> Language Skills
                        </h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <h3>German Proficiency</h3>
                            <p class="gis-pr-form-question">
                                <span>What is your level of German proficiency?</span>
                            </p>
                            <div class="gis-pr-form-field germany-lang">
                                <?= $form->field($model, 'germanyLanguage')->dropDownList(GermanyOppCalculator::GERMANY_LANGUAGE_LIST, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'germany-lang'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <h3>English Proficiency</h3>
                            <p class="gis-pr-form-question">
                                <span>What is your level of English proficiency?</span>
                            </p>
                            <div class="gis-pr-form-field eng-lang">
                                <?= $form->field($model, 'englishLanguage')->dropDownList(GermanyOppCalculator::ENGLISH_LANGUAGE_LIST, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'eng-lang'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form6">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading"> Age
                        </h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                What is your age?
                            </p>
                            <div class="gis-pr-form-field germany-age">
                                <?= $form->field($model, 'age')->dropDownList(GermanyOppCalculator::AGE_LIST, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'germany-age'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form7">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading"> Previous Stays in Germany
                        </h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Have you lived in Germany for at least 6 months in the last 5 years?
                            </p>
                            <div class="gis-pr-form-field germany-stay">
                                <?= $form->field($model, 'germanyPrevStay')->dropDownList(GermanyOppCalculator::YES_NO, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'germany-stay'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form8">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">
                            Spouse's Application
                        </h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40"
                                        alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score last-step">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                                <span class="gis-pr-form-score-reset" onclick="window.location.href = '<?= Url::toGermanyCalculator() ?>'">
                                    <span class="reset">Start Again</span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Is your spouse also applying and meets the requirements?
                            </p>
                            <div class="germany-spouse">
                                <?= $form->field($model, 'germanySpouseApplication')->dropDownList(GermanyOppCalculator::YES_NO, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'germany-spouse'])->label(false) ?>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
            <div class="gis-pr-form-btn">
                <span id="prev-form-btn">Previous</span>
                <?php if (isset(Yii::$app->user->identity)): ?>
                    <span id="next-form-btn" class="next-form-btn basicDetails initial-form">Next</span>
                <?php else: ?>
                    <span id="next-form-btn" class="next-form-btn basicDetails">Next</span>
                <?php endif; ?>
                <?php
                echo ' <input type="submit" id="getmyscore" class="submitForm" value="Show Detailed Report">
                   <span id="hideReport" class="hideReport">Hide Report</span>';
                // if (isset(Yii::$app->user->identity)):
                //     echo '<input type="hidden" id="userLoggedIn" value="1">
                //     <input type="submit" id="getmyscore" class="submitForm" value="Show Detailed Report">
                //     <span id="hideReport" class="hideReport">Hide Report</span>';
                // else:
                //     echo '<input type="hidden" id="userLoggedIn" value="0">
                //     <input type="button" id="getmyscore" class="submitForm" value="Show Detailed Report"
                //                      data-ctatext="Show Detailed Report" data-ctaposition="dynamic-cta-show-detailed-report">';
                // endif;
                ?>
            </div>

            <span id="gis-pr-form-score-reset" class="gis-pr-form-score-reset"
                onclick="window.location.href = '<?= Url::toGermanyCalculator() ?>'">Start Again</span>
            <input type="hidden" name="calculatorType" value="germanyoppCalculator">
            <?php ActiveForm::end(); ?>
            <?php
            // need to uncomment below once login concept envolved and also remove the showScore input
            // if (isset(Yii::$app->user->identity) && $showScore):
            echo '<input type="hidden" id="isshowScore" value="' . $showScore . '">';
            if ($showScore):
                ?>
                <div class="gis-pr-eligibility-score" id="showScore">
                    <div class="gis-pr-score-header">
                        <h3 class="gis-pr-score-heading">Your Eligibility</h3>
                        <p class="gis-pr-score-detail">
                            Germany Global Opportunity Card Score-<span>
                                Your Score <?php if (isset($score['score'])) {
                                                echo $score['score'];
                                           } ?>/18</span>
                        </p>
                        <p class="gis-pr-score-detail"><span><?php if ($score['score'] > 6):
                            echo 'Eligible for Germany Global Opportunity Card';
                                                             else:
                                                                 echo 'Ineligible for Germany Global Opportunity Card';
                                                             endif; ?></span></p>
                    </div>
                    <table class="gis-pr-score-card">
                        <thead class="gis-pr-score-card-head">
                            <tr class="gis-pr-score-name">
                                <th>Factors</th>
                                <th>Points</th>
                            </tr>
                        </thead>
                        <tbody class="gis-pr-score-card-body">
                            <tr class="gis-pr-score-number">
                                <td>Country of Citizenship</td>
                                <td><?php if (isset($score['citizenship'])) {
                                        echo $score['citizenship'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Financial Proof</td>
                                <td><?php if (isset($score['finance'])) {
                                        echo $score['finance'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Qualification</td>
                                <td><?php if (isset($score['qualification'])) {
                                        echo $score['qualification'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Work Experience</td>
                                <td><?php if (isset($score['work_exp'])) {
                                        echo $score['work_exp'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Language Skills</td>
                                <td><?php if (isset($score['language'])) {
                                        echo $score['language'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Age</td>
                                <td><?php if (isset($score['age'])) {
                                        echo $score['age'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Previous Stays in Germany</td>
                                <td><?php if (isset($score['germanyPrevStay'])) {
                                        echo $score['germanyPrevStay'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Spouse's Application</td>
                                <td><?php if (isset($score['germanySpouseApplication'])) {
                                        echo $score['germanySpouseApplication'];
                                    } ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

    </div>

    <div class="container express-cal">
        <div class="gis-pr-blog">
            <div class="gis-pr-blog-unlock">
                <h3 class="gis-pr-blog-title">
                    Germany Opportunity Card Calculator
                </h3>
                <p class="gis-pr-blog-desc">
                    From June 1, 2024, non-EU citizens are able to apply for the Germany opportunity card which facilitated job searches and made immigrating to Germany easier. As per the opportunity card, Germany points calculator, at least 6 points out of 14 had to be gained to be eligible for the Germany Chancenkarte visa. </p>
                <h3 class="gis-pr-blog-title">
                    Eligibility for the Germany Opportunity Card
                </h3>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-suffix">Educational Qualification: Hold a university degree or a vocational qualification with at least 2 years of vocational training (recognized in your home country). Obtain a positive report from the ZAB Germany portal.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-suffix">Language Proficiency: Pass a language test with at least a B2 level in English or an A1 level in German.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-suffix">Financial Support: Demonstrate financial capability to cover living costs in Germany. Show proof through a blocked bank account with at least €12,324 or a “Statement of Declaration” (verpflichtungserklärung) from a full-time worker in Germany. If you have a part-time job offer (20 hours/week) in Germany, you do not need to provide financial proof via the blocked account.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-suffix">Points Requirement: Ensure you score at least 6 points on the points calculator after meeting these basic requirements.</span>
                    </li>
                </ul>
                <h3 class="gis-pr-blog-title">Germany Opportunity Card Calculator Breakdown.</h3>
                <p class="gis-pr-blog-desc">Below is a detailed explanation of how points are awarded and distributed using the Germany opportunity card points calculator.</p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Age:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Applicants who are between 18 to 35 old receive the maximum points.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Age Range</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Between 18 to 35</td>
                                <td>2</td>
                            </tr>
                            <tr>
                                <td>Between 35 and 40</td>
                                <td>1</td>
                            </tr>
                            <tr>
                                <td>More than 40</td>
                                <td>0</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Qualification:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">With a fully recognized qualification or one earned in Germany, you are automatically eligible for an opportunity card.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Qualification</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Recognized Bachelor’s Degree</td>
                                <td>6</td>
                            </tr>
                            <tr>
                                <td>Recognized Higher Education/Vocational Training</td>
                                <td>6</td>
                            </tr>
                            <tr>
                                <td>Partially Recognized Vocational Training</td>
                                <td>4</td>
                            </tr>
                            <tr>
                                <td>No Recognized formal education</td>
                                <td>0</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Professional Work Experience:&nbsp;</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Work Experience</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2 years of relevant professional experience (within the last 5 years)</td>
                                <td>2</td>
                            </tr>
                            <tr>
                                <td>5 years of relevant professional experience (within the last 7 years)</td>
                                <td>3</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Qualification from Bottleneck Occupation (Occupation Shortage List)&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Bottleneck occupations are in high demand in Germany due to a lack of qualified workers. If you qualify for one of these fields (Natural Science, Mathematics, Engineering, Healthcare, Education sector, IT, or Managerial Occupation), you can earn extra points when applying for the Germany Opportunity Card.
                        </span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Qualification Relevance</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Qualification listed in Bottleneck occupations</td>
                                <td>1</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Language Skills&nbsp;</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Language Skills</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>German proficiency at CEFR A2</td>
                                <td>1</td>
                            </tr>
                            <tr>
                                <td>German proficiency at CEFR B1</td>
                                <td>2</td>
                            </tr>
                            <tr>
                                <td>German proficiency at CEFR B2</td>
                                <td>3</td>
                            </tr>
                            <tr>
                                <td>English proficiency at CEFR C1 or native speaker</td>
                                <td>1</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Skilled Worker Potential of Spouses or Life Partners&nbsp;</span>
                        <span class="gis-pr-blog-suffix">You get additional points if your partner or spouse wishes to enter Germany with you on an opportunity card.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Relation</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Partner or spouse with 6 points for the opportunity card</td>
                                <td>1</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Previous Stay in Germany&nbsp;</span>
                        <span class="gis-pr-blog-suffix">This includes periods such as au pair stays, language courses, exchange studies, work stays, research stays, internships, work & travel, and volunteering. Tourism stays are excluded.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Relation</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Continuous stay of 6 months in the last 5 years for work or study</td>
                                <td>1</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <h3 class="gis-pr-blog-title">
                    How to Improve Your Germany Opportunity Card Score?
                </h3>
                <p class="gis-pr-blog-desc">
                    Boosting your Germany Opportunity Card score can significantly enhance your chances of successfully immigrating. Explore these strategies to improve your prospects for moving to Germany.
                </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Enhance Your Qualifications:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">A Completing vocational training or obtaining a higher education degree recognized in Germany can significantly boost your score. If you lack German qualifications, relevant work experience can compensate.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Expand Your Work Experience:&nbsp;</span>
                        <span class="gis-pr-blog-suffix"> Gain as much professional experience in your field as possible. 5 years of experience in the last 7 years earns you the maximum points (3), while 2 years of experience can earn you 2 points.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Improve Your Language Skills:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Learning German can be highly beneficial. Good German skills can earn you 3 points. Even if you don’t reach that level, sufficient German skills or English proficiency at the C1 level can give you 1 point each.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Optimize Your Age:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Age also impacts your score. If you're under 35, you’ll get 3 points. If you’re between 36 and 39, you’ll still earn 1 point.</span>
                    </li>
                </ul>
            </div>
            <button aria-label="Continue Button" class="gis-pr-continue-btn">Continue Reading</button>
        </div>
    </div>
    <?php
    echo $this->render('../partials/visaApproval.php');
    if ($isFAQ): ?>
        <div class="gis-faq container-fluid">
            <div class="gis-faq-inner container">
                <div class="gis-faq-left">
                    <h1 class="gis-faq-heading">Frequently Asked Questions</h1>
                    <img loading="lazy" src="/yas/images/gis_faq.png" alt="..." class="mobileOnly" alt="faq image" />
                    <ul class="gis-accordion">
                        <?php foreach ($faqList->qnas as $faq): ?>
                            <li class="gis-accordion-section">
                                <div class="gis-accordion-header">
                                    <p><?= $faq['question'] ?></p>
                                </div>
                                <p class="gis-accordion-content">
                                    <?= $faq['answer'] ?>
                                </p>
                            </li>
                        <?php endforeach ?>
                    </ul>
                </div>
                <div class="gis-faq-right">
                    <img loading="lazy" src="/yas/images/gis_faq-right.webp" alt="..." class="desktopOnly" alt="faq image" />
                </div>
            </div>
        </div>
    <?php endif; ?>
</main>
<?php
$this->registerJsFile(Yii::$app->params['jsPath'] . 'calculator.js', ['depends' => [AppAsset::class], 'defer' => true]);
