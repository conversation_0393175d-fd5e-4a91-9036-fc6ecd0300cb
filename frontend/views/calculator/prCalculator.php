<?php

use common\models\calculator\PrCalculator;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use yii\bootstrap\ActiveForm;

$ageList = PrCalculator::ageList();
$educationList = PrCalculator::educationQualificationList();
$workExperienceList = PrCalculator::workExperienceList();

//utils
$this->title = $seoTags['title'];
$this->context->description = $seoTags['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Immigration');
$this->params['canonicalUrl'] = Url::base(true) . '/immigration';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'prCalculator.css', ['depends' => [AppAsset::class]]);
$isFAQ = (isset($faqList->qnas) && !empty($faqList->qnas)) ?? $faqList->qnas;
$showform = 0;
if (Yii::$app->request->post()) {
    $showform = 1;
}
?>

<main class="gis-main">
    <div class="gis-pr-main container">
        <h1 class="gis-pr-heading">67 Points Calculator for Canada PR (Express Entry) Eligibility</h1>
        <p class="gis-pr-desc">
            A PR calculator or an immigration point calculator is designed to evaluate
            eligibility criteria for the express entry immigration program. This tool
            helps applicants find out whether they qualify for immigration. This tool allows
            applicants to decide whether they have scored a minimum of 67 points to qualify
        </p>
    </div>
    <?php $form = ActiveForm::begin(['id' => 'calculator']) ?>
    <div class="gis-pr-calculator container">
        <div class="gis-pr-timeline" id="gis-pr-timeline">
            <div class=" gis-pr-step">
                <div class="gis-pr-step-number">1</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Basic Details</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">2</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Language Skills</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">3</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Additional Information</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">4</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Marital Information</div>
            </div>
        </div>
        <div class="gis-pr-form-container">
            <div class="gis-pr-forms">
                <div class="gis-pr-form <?php if ($score == 0  || $showform) {
                                            echo ' active ';
                                        } ?>" id="form1">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Basic Details</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <button class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40"
                                        width="40" alt="unlock" />
                                    <span class="unlock-score">
                                        Unlock Score
                                    </span>
                                </button>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0';?></span>
                                </span>
                            </div>
                            <!-- <?php //endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Your Age</span>
                            </p>
                            <div class="gis-pr-form-field prAge">
                                <?= $form->field($model, 'age')->dropDownList(
                                    $ageList,
                                    ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'prAge']
                                )
                                    ->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Your Education</span>
                            </p>
                            <div class="gis-pr-form-field prEducation">
                                <?= $form->field($model, 'education')->dropDownList(
                                    $educationList,
                                    ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'prEducation']
                                )
                                    ->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Your Work Experience</span>
                            </p>
                            <div class="gis-pr-form-field prWorkExperience">
                                <?= $form->field($model, 'workExperience')->dropDownList(
                                    $workExperienceList,
                                    ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'prWorkExperience']
                                )
                                    ->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form2">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Language Skills</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Reading
                            </p>
                            <div class="gis-pr-form-field prEnglishReading">
                                <?= $form->field($model, 'englishReading')->dropDownList(
                                    PrCalculator::ENGLISH_READING,
                                    ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'prEnglishReading']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Writing
                            </p>
                            <div class="gis-pr-form-field prEnglishWriting">
                                <?= $form->field($model, 'englishWriting')->dropDownList(
                                    PrCalculator::ENGLISH_WRITING,
                                    ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'prEnglishWriting']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Listening
                            </p>
                            <div class="gis-pr-form-field prEnglishListening">
                                <?= $form->field($model, 'englishListening')->dropDownList(
                                    PrCalculator::ENGLISH_LISTENING,
                                    ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'prEnglishListening']
                                )
                                    ->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Speaking
                            </p>
                            <div class="gis-pr-form-field prEnglishSpeaking">
                                <?= $form->field($model, 'englishSpeaking')->dropDownList(
                                    PrCalculator::ENGLISH_SPEAKING,
                                    ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'prEnglishSpeaking']
                                )
                                    ->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Proficiency in French*. Have you scored CLB 5 or above in all 4 modules?
                            </p>
                            <div class="gis-pr-form-field prFrenchProficiency">
                                <?= $form->field($model, 'yourFrenchProficiency')->dropDownList(
                                    PrCalculator::YES_NO,
                                    ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'prFrenchProficiency']
                                )
                                    ->label(false) ?>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="gis-pr-form" id="form3">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Additional Information</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0';?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Have you worked in Canada for 1 or more years?
                            </p>
                            <div class="prWorkedInCanada">
                                <?= $form->field($model, 'youWorkedInCanada')->radioList(
                                    PrCalculator::YES_NO,
                                    ['class' => 'gis-pr-form-radio-options', 'id' => 'prWorkedInCanada']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Have you Studied in Canada for 2 or more years?
                            </p>
                            <div class="prStudiedInCanada">
                                <?= $form->field($model, 'youStudiedInCanada')->radioList(
                                    PrCalculator::YES_NO,
                                    ['class' => 'gis-pr-form-radio-options', 'id' => 'prStudiedInCanada']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Do you have a blood relative in Canada on a PR visa/is a citizen?
                            </p>
                            <div class="prRelativeInCanada">
                                <?= $form->field($model, 'relativeInCanada')->radioList(
                                    PrCalculator::YES_NO,
                                    ['class' => 'gis-pr-form-radio-options', 'id' => 'prRelativeInCanada']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Do you have an arranged employment in Canada?
                            </p>
                            <div class="prArrangedEmployment">
                                <?= $form->field($model, 'arrangedEmployment')->radioList(
                                    PrCalculator::YES_NO,
                                    ['class' => 'gis-pr-form-radio-options', 'id' => 'prArrangedEmployment']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="gis-pr-form" id="form4">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">
                            Marital Information
                        </h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40"
                                        alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score last-step">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                                <span class="gis-pr-form-score-reset" onclick="window.location.href = '<?= Url::toPrCalculator() ?>'">
                                    <span class="reset">Start Again</span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Are you married?
                            </p>
                            <div class="prMaritalStatus">
                                <?= $form->field($model, 'maritalStatus')->radioList(
                                    PrCalculator::YES_NO,
                                    ['class' => 'gis-pr-form-radio-options', 'id' => 'prMaritalStatus']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions spouseData">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Is your spouse/partner proficient in English/French?
                            </p>
                            <div class="prSpouseLanguageProficiency">
                                <?= $form->field($model, 'spouseLanguageProficiency')->radioList(
                                    PrCalculator::YES_NO,
                                    ['class' => 'gis-pr-form-radio-options', 'id' => 'prSpouseLanguageProficiency']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions spouseData">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Does your spouse/partner have atleast 1 year of Canadian work experience?
                            </p>
                            <div class="prSpouseWorkedInCanada">
                                <?= $form->field($model, 'spouseWorkedInCanada')->radioList(
                                    PrCalculator::YES_NO,
                                    ['class' => 'gis-pr-form-radio-options', 'id' => 'prSpouseWorkedInCanada']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions spouseData">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Has your spouse/partner studied in Canada for 2 or more years?
                            </p>
                            <div class="prSpouseStudiedInCanada">
                                <?= $form->field($model, 'spouseStudiedInCanada')->radioList(
                                    PrCalculator::YES_NO,
                                    ['class' => 'gis-pr-form-radio-options', 'id' => 'prSpouseStudiedInCanada']
                                )->label(false) ?>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
            <div class="gis-pr-form-btn">
                <span id="prev-form-btn">Previous</span>
                <?php if (isset(Yii::$app->user->identity)): ?>
                    <span id="next-form-btn" class="next-form-btn basicDetails initial-form">Next</span>
                <?php else: ?>
                    <span id="next-form-btn" class="next-form-btn basicDetails">Next</span>
                <?php endif; ?>
                <?php
                echo ' <input type="submit" id="getmyscore" class="submitForm" value="Show Detailed Report">
                   <span id="hideReport" class="hideReport">Hide Report</span>';
                // if (isset(Yii::$app->user->identity)):
                //     echo '<input type="hidden" id="userLoggedIn" value="1">
                //     <input type="submit" id="getmyscore" class="submitForm" value="Show Detailed Report">
                //     <span id="hideReport" class="hideReport">Hide Report</span>';
                // else:
                //     echo '<input type="hidden" id="userLoggedIn" value="0">
                //     <input type="button" id="getmyscore" class="submitForm" value="Show Detailed Report"
                //                      data-ctatext="Show Detailed Report" data-ctaposition="dynamic-cta-show-detailed-report">';
                // endif;
                ?>
            </div>

            <span id="gis-pr-form-score-reset" class="gis-pr-form-score-reset"
                onclick="window.location.href = '<?= Url::toPrCalculator() ?>'">Start Again</span>
            <input type="hidden" name="calculatorType" value="prCalculator">
            <?php ActiveForm::end(); ?>
            <?php
            // need to uncomment below once login concept envolved and also remove the showScore input
            // if (isset(Yii::$app->user->identity) && $showScore):
            echo '<input type="hidden" id="isshowScore" value="' . $showScore . '">';
            if ($showScore):
                ?>
                <div class="gis-pr-eligibility-score" id="showScore" style="display:block">
                    <div class="gis-pr-score-header">
                        <h3 class="gis-pr-score-heading">Your Eligibility</h3>
                        <p class="gis-pr-score-detail">
                            PR Score -<span id="score189">
                                Your Score <?php if (isset($score['score'])) {
                                                echo $score['score'];
                                           } ?>/100</span>
                        </p>
                    </div>
                    <table class="gis-pr-score-card">
                        <thead class="gis-pr-score-card-head">
                            <tr class="gis-pr-score-name">
                                <th>Factors</th>
                                <th>Points</th>
                            </tr>
                        </thead>
                        <tbody class="gis-pr-score-card-body">
                            <tr class="gis-pr-score-number">
                                <td>Basic Details</td>
                                <td><?php if (isset($score['basic'])) {
                                        echo $score['basic'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Language Skills</td>
                                <td><?php if (isset($score['lang'])) {
                                        echo $score['lang'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Additional Information</td>
                                <td><?php if (isset($score['additional'])) {
                                        echo $score['additional'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Marital Information</td>
                                <td><?php if (isset($score['maritial'])) {
                                        echo $score['maritial'];
                                    } ?></td>
                            </tr>
                        </tbody>
                    </table></span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="container express-cal">
        <div class="gis-pr-blog">
            <div class="gis-pr-blog-unlock">
                <h3 class="gis-pr-blog-title">
                    Canada PR Eligibility Points Calculator
                </h3>
                <p class="gis-pr-blog-desc">
                    The Canadian immigration system to <b>calculate eligibility for Canada PR,</b>
                    employs a 67 Points System which assesses applicants for the Federal Skilled Workers
                    Program. To be eligible, individuals must achieve a minimum score of 67 points on the
                    <b>PR Canada eligibility calculator.</b>
                    .<br />Achieving a score of 67 points or higher makes you eligible for the Federal
                    Skilled Worker Program, enabling you to submit your profile to the Express Entry pool.<br />
                    For Federal Skilled Workers, the 67 Points Immigration Canada system is used to
                    streamline the PR process. Discover the concept of this system and learn how to maximize
                    your scores for a successful application.
                </p>
                <h3 class="gis-pr-blog-title">
                    Canada PR Eligibility Calculator
                </h3>
                <p class="gis-pr-blog-desc">
                    With a vision of welcoming tens of thousands of immigrants, Canada constantly updates
                    its <a aria-label="Link" href="https://getgis.org/blog/new-immigration-rules-of-canada"
                        target="_blank">immigration policies</a> to facilitate settlement in this magnificent
                    country. Express Entry applicants are categorized into Federal Skilled Workers Program,
                    Federal Skilled Trades Program, and Canadian Experience Class.
                </p>
                <h3 class="gis-pr-blog-title">
                    How to calculate eligibility for Canada PR?
                </h3>
                <p class="gis-pr-blog-desc">Canada PR eligibility calculator accounts for certain factors
                    which are awarded points based on the applicant's candidature. The six selection factors
                    used to calculate eligibility for Canada PR through the Federal Skilled Worker Program have
                    been mentioned below along with the maximum score that you could earn for each factor.</p>
                <p class="table-responsive">
                    <span class="table-responsive">
                        <table>
                            <thead>
                                <th>Six Selection Factors</th>
                                <th>Maximum Point</th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Language skills (English & French)</td>
                                    <td>28</td>
                                </tr>
                                <tr>
                                    <td>Educational Qualifications</td>
                                    <td>25</td>
                                </tr>
                                <tr>
                                    <td>Work experience</td>
                                    <td>15</td>
                                </tr>
                                <tr>
                                    <td>Age</td>
                                    <td>12</td>
                                </tr>
                                <tr>
                                    <td>Arranged employment</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <td>Adaptability</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <th>Total Points</th>
                                    <th>100</th>
                                </tr>
                            </tbody>
                        </table>
                    </span>
                </p>
                <p class="gis-pr-blog-desc">You can now access the <b>Canada PR visa eligibility
                        calculator,</b> here, for you to self-assess your eligibility or possible PR score
                    that your candidature could earn in the Canada PR eligibility calculator. All you need
                    to do is to answer the below questions, in the mentioned order.</p>
                <h3 class="gis-pr-blog-title">
                    Calculate Eligibility Score for Canada PR
                </h3>
                <p class="gis-pr-blog-desc">
                    To calculate the accurate score on the <b>Canada PR eligibility score calculator,</b>
                    kindly ensure to answer the questions to the best of your ability.
                </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Age Points:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Your age at the time of visa application submission
                            plays a critical role in immigration processes, as some programs may have age
                            restrictions or assign points based on the applicant's age group.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Below 18 years: 0 points</td>
                                <td>00 points</td>
                            </tr>
                            <tr>
                                <td>18 years - 35 years</td>
                                <td>12 points</td>
                            </tr>
                            <tr>
                                <td>36 years</td>
                                <td>11 points</td>
                            </tr>
                            <tr>
                                <td>37 years</td>
                                <td>10 points</td>
                            </tr>
                            <tr>
                                <td>38 years</td>
                                <td>09 points</td>
                            </tr>
                            <td>39 years</td>
                            <td>08 points</td>
                            </tr>
                            </tr>
                            <td>40 years</td>
                            <td>07 points</td>
                            </tr>
                            </tr>
                            <td>41 years</td>
                            <td>06 points</td>
                            </tr>
                            </tr>
                            <td>42 years</td>
                            <td>05 points</td>
                            </tr>
                            </tr>
                            <td>43 years</td>
                            <td>04 points</td>
                            </tr>
                            </tr>
                            <td>44 years</td>
                            <td>03 points</td>
                            </tr>
                            </tr>
                            <td>45 years</td>
                            <td>02 points</td>
                            </tr>
                            </tr>
                            <td>46 years</td>
                            <td>01 points</td>
                            </tr>
                            </tr>
                            <td>47 years and older</td>
                            <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Educational Qualification:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Education qualification is essential for immigration
                            eligibility, allowing authorities to assess applicants' educational background
                            (from or outside Canada) and potential contributions to the country's workforce
                            and society.</span>
                    </li>
                </ul>
                <p class="gis-pr-blog-desc cal-sub-heading"><b>a. Your highest educational qualification at
                        the time of visa application submission?</b>
                </p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Doctoral Degree (equal to Canadian Equivalency)</td>
                                <td>25 points</td>
                            </tr>
                            <tr>
                                <td>Master’s Degree (equal to Canadian Equivalency)</td>
                                <td>23 points</td>
                            </tr>
                            <tr>
                                <td>Two or more post-secondary degrees (at least one is 3 years or more)</td>
                                <td>22 points</td>
                            </tr>
                            <tr>
                                <td>Bachelor’s Degree or a 3 years degree program at a university level</td>
                                <td>21 points</td>
                            </tr>
                            <tr>
                                <td>Bachelor’s Degree or a 3 years degree program at a university</td>
                                <td>20 points</td>
                            </tr>
                            <tr>
                                <td>Diploma Degrees of 2 years or more but less than 3 years</td>
                                <td>19 years</td>
                            </tr>
                            <tr>
                                <td>Diploma degree of 1 year or more but less than 2 years</td>
                                <td>15 years</td>
                            </tr>
                            <tr>
                                <td>Post Secondary</td>
                                <td>05 years</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list customBullet">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Work Experience:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">EYou can earn points based on the duration of your
                            full-time paid work experience, in last 10 years which requires a minimum of 30
                            hours per week (or an equivalent amount of part-time work, i.e., 15 hours per week
                            for 24 months) in a skilled occupation listed under the TEER categories 0, 1, 2, or 3.</span>
                    </li>
                </ul>
                <p class="gis-pr-blog-desc cal-sub-heading"><b>a. Your highest educational qualification at
                        the time of visa application submission?</b>
                </p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>6 years or more</td>
                                <td>15 points</td>
                            </tr>
                            <tr>
                                <td>4 - 5 years</td>
                                <td>13 points</td>
                            </tr>
                            <tr>
                                <td>2 -3 years</td>
                                <td>11 points</td>
                            </tr>
                            <tr>
                                <td>1 year</td>
                                <td>09 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item customBullet">
                        <span class="gis-pr-blog-prefix">Language Ability :</span>
                        <span class="gis-pr-blog-suffix">To demonstrate your language proficiency, an
                            approved language test score is required. While English levels are measured
                            using Canadian Language Benchmarks (CLB) the French levels are assessed using
                            Niveaux de compétence linguistique canadiens (NCLC)</span>
                    </li>
                </ul>
                <br />
                <span class="gis-pr-blog-prefix cal-sub-heading">a. What best describes your English
                    language proficiency in sections of reading, writing, listening, and speaking?</span><br /> <br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CLB 9 and above</td>
                                <td>24 points</td>
                            </tr>
                            <tr>
                                <td>CLB 8</td>
                                <td>20 points</td>
                            </tr>
                            <tr>
                                <td>CLB 7</td>
                                <td>16 points</td>
                            </tr>
                            <tr>
                                <td>CLB 6 and below</td>
                                <td>NA</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading">b. What best describes your second
                    official language (French) proficiency in sections of reading, writing, listening,
                    and speaking?</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CLB 5 and above</td>
                                <td>04 points</td>
                            </tr>
                            <tr>
                                <td>CLB 4 and below</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item customBullet">
                        <span class="gis-pr-blog-prefix">Arranged Employment :</span>
                        <span class="gis-pr-blog-suffix">If you have a job offer from a Canadian employer
                            lasting at least one year, you can earn points. It is important to obtain the job
                            offer prior to applying as a Federal Skilled Worker.
                            LMIA applications and confirmation of valid job offers are only applicable to
                            occupations listed in TEER categories 0, 1, 2, or 3 of the National Occupational
                            Classification (NOC).
                        </span>
                    </li>
                </ul>
                <br />
                <span class="gis-pr-blog-prefix cal-sub-heading">a. Are you currently working or have an active
                    job offer in Canada?</span>
                <br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>10 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item customBullet">
                        <span class="gis-pr-blog-prefix">Adaptability :</span>
                        <span class="gis-pr-blog-suffix">Both you and your spouse or common-law partner, who
                            will be immigrating to Canada with you, have the opportunity to earn points for
                            adaptability (maximum of 10 points). These elements evaluate your likelihood of
                            successfully integrating and settling in Canada.
                        </span>
                    </li>
                </ul>
                <br />
                <span class="gis-pr-blog-prefix cal-sub-heading">a. Spouse or partner abilities</span><br /><br />
                <span class="gis-pr-blog-prefix cal-sub-heading">Does your spouse or partner's language skills,
                    score at least CLB 4 and above?</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading">Does your spouse or common-law partner have
                    a full-time study degree from Canada?</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading">Does your spouse or common-law partner
                    have previous work experience in Canada?</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading"> b. Applicant Adaptability Skills</span><br /><br />
                <span class="gis-pr-blog-prefix cal-sub-heading">Did you complete your full-time education
                    qualification in Canada?</span>
                <br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading">Do you have full-time previous work experience
                    in Canada?</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading"> c. Relatives in Canada</span><br /><br />
                <span class="gis-pr-blog-prefix cal-sub-heading">This factor assesses if the applicant or the
                    accompanying spouse or common-law partner has a family relative who holds Canadian citizenship
                    or permanent residency and resides in Canada.</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <h3 class="gis-pr-blog-title">
                    How to Improve Your PR Eligibility Score?
                </h3>
                <p class="gis-pr-blog-desc">If your PR eligibility score falls below 67 points, you mightl
                    not qualify for the program. However, there are a couple of strategies you can consider to
                    potentially increase your score and improve your eligibility:</p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Enhancing Language Skills:</span>
                        <span class="gis-pr-blog-suffix">Investing time and effort in improving your language
                            proficiency in either English or French can significantly boost your PR score. Achieving
                            higher scores in language proficiency tests will earn you more points and enhance your
                            overall eligibility.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Pursuing Additional Educational Qualifications:</span>
                        <span class="gis-pr-blog-suffix">Obtaining further educational qualifications, such as a
                            postgraduate degree or professional certification, can increase your score as higher
                            qualification ensures higher scores. </span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Obtaining an Offer of Arranged Employment in Canada:</span>
                        <span class="gis-pr-blog-suffix">Securing a valid offer of arranged employment can
                            contribute valuable points towards your overall score, making it a favorable option
                            for improving your eligibility.</span>
                    </li>
                </ul>
                <h3 class="gis-pr-blog-title">
                    How is the Canada PR eligibility Calculator different from the Comprehensive Ranking Score
                    Calculator?
                </h3>
                <p class="gis-pr-blog-desc">The Canada PR eligibility calculator is a tool that uses a 100-point
                    grid to assess eligibility under the Express Entry Program. On the other hand, the Comprehensive
                    Ranking System (CRS) score tool utilizes a 1200-point grid to rank applicants in the Express Entry
                    Pool.<br />The Canada eligibility for PR points calculator is used to calculate points specifically
                    for the Federal Skilled Workers, while the CRS point calculator is used for CRS point calculation.<br />
                    It is advisable to seek assistance from an Immigration consultant to accurately <a aria-label="Link" href="https://getgis.org/immigration/canada/crs-calculator">calculate your CRS score.</a></p>
            </div>
            <button aria-label="Continue Button" class="gis-pr-continue-btn">Continue Reading</button>
        </div>
    </div>
    <?php
    echo $this->render('../partials/visaApproval.php');
    if ($isFAQ): ?>
        <div class="gis-faq container-fluid">
            <div class="gis-faq-inner container">
                <div class="gis-faq-left">
                    <h1 class="gis-faq-heading">Frequently Asked Questions</h1>
                    <img loading="lazy" src="/yas/images/gis_faq.png" alt="..." class="mobileOnly" alt="faq image" />
                    <ul class="gis-accordion">
                        <?php foreach ($faqList->qnas as $faq): ?>
                            <li class="gis-accordion-section">
                                <div class="gis-accordion-header">
                                    <p><?= $faq['question'] ?></p>
                                </div>
                                <p class="gis-accordion-content">
                                    <?= $faq['answer'] ?>
                                </p>
                            </li>
                        <?php endforeach ?>
                    </ul>
                </div>
                <div class="gis-faq-right">
                    <img loading="lazy" src="/yas/images/gis_faq-right.webp" alt="..." class="desktopOnly" alt="faq image" />
                </div>
            </div>
        </div>
    <?php endif; ?>
</main>
<?php
$this->registerJsFile(Yii::$app->params['jsPath'] . 'calculator.js', ['depends' => [AppAsset::class], 'defer' => true]);
