<?php

use common\models\calculator\AustraliaPrPointsCalculator;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use yii\bootstrap\ActiveForm;

$ageList = AustraliaPrPointsCalculator::ageList();
$englishProficiencyList = AustraliaPrPointsCalculator::englishProficiency();
$workExperienceAbList = AustraliaPrPointsCalculator::workExperienceAbList();
$workExperienceAusList = AustraliaPrPointsCalculator::workExperienceAusList();
$overseasEducationQualificationList = AustraliaPrPointsCalculator::overseasEducationQualificationList();
$partnercategoryList = AustraliaPrPointsCalculator::partnercategoryList();
$nominationsponsorshipList = AustraliaPrPointsCalculator::nominationsponsorshipList();

//utils
$this->title = $seoTags['title'];
$this->context->description = $seoTags['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Immigration');
$this->params['canonicalUrl'] = Url::base(true) . '/immigration';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'prCalculator.css', ['depends' => [AppAsset::class]]);
$isFAQ = (isset($faqList->qnas) && !empty($faqList->qnas)) ?? $faqList->qnas;
$showform = 0;
if (Yii::$app->request->post()) {
    $showform = 1;
}
?>

<main class="gis-main">
    <div class="gis-pr-main container">
        <h1 class="gis-pr-heading">Australia PR Points Calculator for 189 & 190 Skilled Immigration Visas</h1>
        <p class="gis-pr-desc">
            The tool calculates your eligibility and generates an estimated score for ​various Australian immigration programs by evaluating factors such as Visas, age, education, language proficiency, work experience, and Adaptability factors.
        </p>
    </div>
    <?php $form = ActiveForm::begin(['id' => 'calculator']) ?>
    <div class="gis-pr-calculator container">
        <div class="gis-pr-timeline" id="gis-pr-timeline">
            <div class=" gis-pr-step">
                <div class="gis-pr-step-number">1</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Basic Details</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">2</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Working Experience</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">3</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Educational Qualification</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">4</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Specialization & Language Test</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">4</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Additional Information</div>
            </div>
        </div>
        <div class="gis-pr-form-container">
            <div class="gis-pr-forms">
                <div class="gis-pr-form <?php if ($score == 0  || $showform) {
                                            echo ' active ';
                                        } ?>" id="form1">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Basic Details</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)): ?>
                                <button class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40"
                                        width="40" alt="unlock" />
                                    <span class="unlock-score">
                                        Unlock Score
                                    </span>
                                </button>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['totalScore'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php //endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>What is your age at the time of visa application
                                    submission?</span>
                            </p>
                            <div class="gis-pr-form-field select-1">
                                <?= $form->field($model, 'age')->dropDownList($ageList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'select-1'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>What is your English Language proficiency level?</span>

                                <span data-text="To qualify for an Australian immigration visa, applicants must achieve a satisfactory score in Listening, Reading, Writing, and Speaking in each of the test components. Having functional or vocational English proficiency does not meet eligibility. Please note that the below scores should be obtained in each component and it is NOT the overall score.
                                Competent: IELTS - 6.0 | PTE 50 | OET B | TOEFL 12, 13, 21, 18 | CAE 169
                                Proficient: IELTS - 7.0 | PTE 65 | OET B | TOEFL 24, 24, 27, 23 | CAE 185
                                Superior: IELTS - 8.0 | PTE 79 | OET A | TOEFL 28, 29, 30, 26 | CAE 200" class="infoIcon2 gis-tooltip"></span>

                            </p>
                            <div class="gis-pr-form-field select-2">
                                <?= $form->field($model, 'englishProficiency')->dropDownList($englishProficiencyList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'select-2'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form2">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Work Experience</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['totalScore'] ?? '0' ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Do you have experience in a skilled occupation listed outside Australia?
                            </p>
                            <div class="gis-pr-form-field select-3">
                                <?= $form->field($model, 'workExperienceAbroad')->dropDownList($workExperienceAbList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'select-3'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Do you have experience in a skilled occupation listed Inside Australia?
                                <span data-text="​Applicants are required to exhibit a minimum of 3 to 5 years of experience in the occupation they have applied for as listed in the Skilled Occupation List" class="infoIcon2 gis-tooltip"></span>

                            </p>
                            <div class="gis-pr-form-field select-4">
                                <?= $form->field($model, 'workExperienceAus')->dropDownList($workExperienceAusList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'select-4'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form3">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Educational Qualification</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['totalScore'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                What is the highest level of education you have completed?
                            </p>
                            <div class="gis-pr-form-field select-5">
                                <?= $form->field($model, 'overseaseducation')->dropDownList($overseasEducationQualificationList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'select-5'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Have you completed an Australian degree, diploma, advanced diploma, or trade qualification that took at least 2 years of full-time study in English instruction?
                            </p>
                            <div class="select-6">
                                <?= $form->field($model, 'auseducationqualification')->radioList(AustraliaPrPointsCalculator::YES_NO, ['class' => 'gis-pr-form-radio-options', 'id' => 'select-6'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Have you completed an Australian degree, diploma, advanced diploma, or trade qualification in a regional area? (Australia Sydney, Melbourne, and Brisbane are not counted as Regional)
                            </p>
                            <div class="select-7">
                                <?= $form->field($model, 'regeducationqualification')->radioList(AustraliaPrPointsCalculator::YES_NO, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form4">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">
                            Specialization & Language Test
                        </h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40"
                                        alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['totalScore'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Have you completed an Australian research-based Master's or Doctoral degree in STEM background and ICT?
                            </p>
                            <div class="select-8">
                                <?= $form->field($model, 'specialization')->radioList(AustraliaPrPointsCalculator::YES_NO, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Have you cleared the Credential Community Language (CCL) test offered by NAATI?</span>
                                <span data-text="Select if you are accredited paraprofessional or above, certified provisional or above, or hold a CCL for interpreting/translating by NAATI(National Accreditation Authority for Translators and Interpreters)" class="infoIcon2 gis-tooltip"></span>

                            </p>
                            <div class="select-9">
                                <?= $form->field($model, 'languagetest')->radioList(AustraliaPrPointsCalculator::YES_NO, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form5">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Additional Information</h1>
                        <div class="gis-pr-form-action">
                            <!--  <?php // if (!isset(Yii::$app->user->identity)) :
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score" data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="../../images/calculator/unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php //else :
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score last-step  <?php if ($showScore) {
                                                                                echo 'auspr';
                                                                          } ?>">
                                    <?php
                                    $setScore = 0;
                                    if (isset($score['score189']) && isset($score['score491']) && $score['score190']) {
                                        $setScore = $score['score189'] . ' - ' . $score['score491'] . ' - ' . $score['score190'];
                                    }
                                    ?>
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $setScore ?? '0'; ?></span>
                                </span>
                                <span class="gis-pr-form-score-reset" onclick="window.location.href = '<?= Url::toAusCalculator() ?>'">
                                    <span class="reset">Start Again</span>
                                </span>
                            </div>
                            <!-- <?php //endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Does your spouse or de facto spouse fulfill any of the following criteria?</span>
                                <span data-text="​Additional points can be claimed for your spouse's age, language proficiency, and a positive skill assessment." class="infoIcon2 gis-tooltip"></span>

                            </p>
                            <div class="gis-pr-form-field select-10">
                                <?= $form->field($model, 'partnercategory')->dropDownList($partnercategoryList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'select-10'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-radio-ctn">
                            <p class="gis-pr-form-question">
                                <span>Have you finished a professional year program in Australia?</span>
                                <span data-text="​Choose if you have completed an Australian professional year program in your nominated skilled occupation for at least 12 months in the past 48 months before the visa application invitation." class="infoIcon2 gis-tooltip"></span>

                            </p>
                            <div class="select-11">
                                <?= $form->field($model, 'professionalyear')->radioList(AustraliaPrPointsCalculator::YES_NO, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-radio-ctn">
                            <p class="gis-pr-form-question">
                                Have you received an invitation for a subclass 190 visa, and
                                the nominating State/Territory agency hasn't withdrawn the
                                nomination?
                            </p>
                            <div class="select-12">
                                <?= $form->field($model, 'nomination')->radioList(AustraliaPrPointsCalculator::YES_NO, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Were you nominated or sponsored to apply for this visa?</span>
                                <span data-text=" Extra points are awarded to eligible applicants whose occupation is in high
                                demand in a specific state or territory."></span>
                            </p>
                            <div class="gis-pr-form-field select-13">
                                <?= $form->field($model, 'nominationsponsorship')->dropDownList($nominationsponsorshipList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'select-13'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="gis-pr-form-btn">
                <span id="prev-form-btn">Previous</span>
                <?php if (isset(Yii::$app->user->identity)): ?>
                    <span id="next-form-btn" class="next-form-btn basicDetails initial-form">Next</span>
                <?php else: ?>
                    <span id="next-form-btn" class="next-form-btn basicDetails">Next</span>
                <?php endif; ?>
                <?php
                echo ' <input type="submit" id="getmyscore" class="submitForm" value="Show Detailed Report">
                   <span id="hideReport" class="hideReport">Hide Report</span>';
                // if (isset(Yii::$app->user->identity)):
                //     echo '<input type="hidden" id="userLoggedIn" value="1">
                //     <input type="submit" id="getmyscore" class="submitForm" value="Show Detailed Report">
                //     <span id="hideReport" class="hideReport">Hide Report</span>';
                // else:
                //     echo '<input type="hidden" id="userLoggedIn" value="0">
                //     <input type="button" id="getmyscore" class="submitForm" value="Show Detailed Report"
                //                      data-ctatext="Show Detailed Report" data-ctaposition="dynamic-cta-show-detailed-report">';
                // endif;
                ?>
            </div>

            <span id="gis-pr-form-score-reset" class="gis-pr-form-score-reset"
                onclick="window.location.href = '<?= Url::toAusCalculator() ?>'">Start Again</span>
            <input type="hidden" name="calculatorType" value="australiaPrPointsCalculator">
            <?php ActiveForm::end(); ?>
            <?php
            // need to uncomment below once login concept envolved and also remove the showScore input
            // if (isset(Yii::$app->user->identity) && $showScore):
            echo '<input type="hidden" id="isshowScore" value="' . $showScore . '">';
            if ($showScore):
                ?>
                <div class="gis-pr-eligibility-score" id="showScore">
                    <div class="gis-pr-score-header">
                        <h3 class="gis-pr-score-heading">Your Eligibility</h3>
                        <p class="gis-pr-score-detail">
                            Skilled Independent Visa (subclass 189) -<span id="score189">
                                Your Score <?php if (isset($score['score189'])) {
                                                echo $score['score189'];
                                           } ?>/145</span>
                        </p>
                        <p class="gis-pr-score-detail">
                            Skilled Work Regional Visa (subclass 491) -
                            <span id="score491">Your Score <?php if (isset($score['score491'])) {
                                                                echo $score['score491'];
                                                           } ?>/160</span>
                        </p>
                        <p class="gis-pr-score-detail">
                            Skilled Nominated Visa (subclass 190) -
                            <span id="score190">Your Score <?php if (isset($score['score190'])) {
                                                                echo $score['score190'];
                                                           } ?>/150</span>
                        </p>
                    </div>
                    <table class="gis-pr-score-card">
                        <thead class="gis-pr-score-card-head">
                            <tr class="gis-pr-score-name">
                                <th>Factors</th>
                                <th>Points</th>
                            </tr>
                        </thead>
                        <tbody class="gis-pr-score-card-body">
                            <tr class="gis-pr-score-number">
                                <td>Basic Details</td>
                                <td><?php if (isset($score['basic'])) {
                                        echo $score['basic'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Work Experience</td>
                                <td><?php if (isset($score['workExp'])) {
                                        echo $score['workExp'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Educational Qualification</td>
                                <td><?php if (isset($score['eduQuali'])) {
                                        echo $score['eduQuali'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Specialization & Community Language Test</td>
                                <td><?php if (isset($score['splLang'])) {
                                        echo $score['splLang'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Additional Information</td>
                                <td><?php if (isset($score['additional'])) {
                                        echo $score['additional'];
                                    } ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="container express-cal">
        <div class="gis-pr-blog">
            <div class="gis-pr-blog-unlock">
                <h3 class="gis-pr-blog-title">
                    Australia PR Points Calculator: Check Your Australia PR Eligibility!
                </h3>
                <p class="gis-pr-blog-desc">
                    Explore Your Australian Permanent Residency Eligibility with the PR Points Calculator. From Subclass Visa 189 to 190 and 491 this calculator tool assesses your qualifications for all the Australia PR visas. <br />Use this calculator to Discover your chances of obtaining Australian PR.
                </p>
                <h3 class="gis-pr-blog-title">
                    How are Australian PR Points Calculated?
                </h3>
                <p class="gis-pr-blog-desc">
                    Australia PR (Permanent Residency) points are based on various factors that assess an individual's eligibility for skilled migration to Australia.<br>The points-based system is used to determine the suitability of applicants for different visa subclasses. The key factors considered for Australia PR points include:
                </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Age:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Points are awarded based on the applicant's age at the time
                            of application, with higher points awarded to younger individuals.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">English Language Proficiency:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Points are allocated based on the applicant's English language skills, as assessed through tests like the International English Language Testing System (IELTS).</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Education:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Points are awarded based on the applicant's educational qualifications, with higher points for higher levels of education.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Skilled Employment:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Points are allocated based on the applicant's work experience in a nominated occupation, with more points awarded for a longer duration of skilled employment.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Australian Work Experience:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Additional points can be earned for having work experience in Australia in a nominated occupation.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Regional Study:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Points can be awarded for studying in regional Australia.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Nomination by State or Territory Government:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Points can be earned if an applicant is nominated by a state or territory government agency.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Specialist Education Qualification:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Additional points can be awarded for having a specialized qualification in a particular field.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Professional Year:&nbsp;</span>
                        <span class="gis-pr-blog-suffix"> Points can be claimed for completing a Professional Year program in Australia.</span>
                    </li>
                </ul>
                <p class="gis-pr-blog-desc">
                    The total points scored by an applicant determine their eligibility for an invitation to apply for a skilled migration visa. The minimum points required can vary depending on the occupation and the demand for skilled workers in Australia.
                </p>
                <h3 class="gis-pr-blog-title">Australia PR Points Breakdown</h3>
                <p class="gis-pr-blog-desc">The table below presents the factors and corresponding maximum points under the Australian Immigration Points System.<br />These points are used to determine the eligibility of skilled workers for various visa subclasses, such as the General Skilled Migration (GSM) visas.</p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Factor</th>
                            <th>Maximum Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Age (25-33 years)</td>
                                <td>30</td>
                            </tr>
                            <tr>
                                <td>English proficiency (8 bands)</td>
                                <td>20</td>
                            </tr>
                            <tr>
                                <td>Work Experience outside Australia (8-10 years)</td>
                                <td>15</td>
                            </tr>
                            <tr>
                                <td>Work Experience in Australia (8-10 years)</td>
                                <td>20</td>
                            </tr>
                            <tr>
                                <td>Education (outside Australia) – Doctorate</td>
                                <td>20</td>
                            </tr>
                            <tr>
                                <td>Doctorate or master’s degree by research in Australia or similar skilled degrees</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>Study in a regional area</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>Proficient in community language</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>Professional year in a skilled program in Australia</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>State sponsorship (190 visas)</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>Skilled spouse (Age, Skills & English language requirements to be met)</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>Spouse or de facto partner with competent English (No need to meet Skills requirement or age factor)</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>Applicants without a spouse or de facto partner or where a spouse is an Australian citizen or PR holder</td>
                                <td>10</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <h3 class="gis-pr-blog-title">Australia PR Points Under Each Category</h3>
                <p class="gis-pr-blog-desc">The total number of Australia PR points you can receive depends on the points available in each category. Your points can increase or decrease depending on how well your credentials align with each specific category.<br />Here’s a breakdown of Australia PR Points under each category to help you understand better: </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Age:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">If your age falls within the range of 25 to 32 years, you can earn a maximum of 30 points. Anyone over the age of 45 will get 0 points.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Age</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>18-24 years</td>
                                <td>25</td>
                            </tr>
                            <tr>
                                <td>25-32 years</td>
                                <td>30</td>
                            </tr>
                            <tr>
                                <td>33-39 years</td>
                                <td>25</td>
                            </tr>
                            <tr>
                                <td>40-44 years</td>
                                <td>15</td>
                            </tr>
                            <tr>
                                <td>45 and above</td>
                                <td>0</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">English Language Scores:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Scoring 8 bands in the IELTS exam can grant you a maximum of 20 points. Australian immigration authorities accept various English proficiency tests, including IELTS, PTE, and TOEFL, among others. So, you have the flexibility to attempt any of these tests to achieve the required score for earning points.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Criteria</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Superior (8/79 on each band in IELTS/PTE Academic)</td>
                                <td>20</td>
                            </tr>
                            <tr>
                                <td>Proficient (7/65 on each band in IELTS/PTE Academic)</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>Competent (6/50 on each band in IELTS/PTE Academic)</td>
                                <td>0</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Work experience outside Australia:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Having 8 to 10 years of skilled employment experience outside Australia, counted from the date of your PR application, will grant you 15 points. If you possess fewer years of experience, you will receive a reduced number of points accordingly.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Skilled employment outside Australia</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Less than 3 years</td>
                                <td>0</td>
                            </tr>
                            <tr>
                                <td>3-4 years</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>5-7 years</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>More than 8 years</td>
                                <td>15</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Work experience in Australia:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Accumulating 8 to 10 years of skilled employment experience in Australia, starting from the date of application, will reward you with a maximum of 20 points.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Skilled employment in Australia</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Less than 1 year</td>
                                <td>0</td>
                            </tr>
                            <tr>
                                <td>1-2 years</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>3-4 years</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>5-7 years</td>
                                <td>15</td>
                            </tr>
                            <tr>
                                <td>More than 8 years</td>
                                <td>20</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Education:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Points awarded for the education criteria depend on the type of educational qualification. The highest points are granted for a doctorate obtained from an Australian university or a recognized university outside Australia, endorsed by the Australian government.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Qualifications</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>A Doctorate from an Australian university or institute outside Australia</td>
                                <td>20</td>
                            </tr>
                            <tr>
                                <td>A Bachelor's (or Master's) degree from an Australian university or institute outside Australia</td>
                                <td>15</td>
                            </tr>
                            <tr>
                                <td>Diploma or trade qualification completed in Australia</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>Any qualification or award recognized by the relevant assessing authority for your nominated skilled occupation</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>Niche skills such as Doctorate or master’s degree in Australia</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>Specialist Education qualification (Master's degree in research or a Doctorate from an Australian educational institution)</td>
                                <td>10</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Spouse application:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">By having your spouse as an applicant for the PR visa, you become eligible for a maximum of 10 extra points.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Spouse qualification</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Spouse has a PR visa or is an Australian citizen</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>Spouse has competent English and has a Positive Skill Assessment</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>Spouse has only competent English</td>
                                <td>5</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Other application:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">You are eligible to earn some additional points if you meet any of the following criteria.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Criteria</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Study in a regional area</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>Accredited in community language</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>Professional years in a skilled program in Australia</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>State sponsorship (190 visas)</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>Minimum 2 years full-time (Australian study requirement)</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td>Specialist Education qualification (Master's degree in research or a Doctorate from an Australian educational institution)</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>Relative or regional sponsorship (491 visas)</td>
                                <td>15</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <p class="gis-pr-blog-desc">
                    <span class="gis-pr-blog-prefix">Note :</span>
                    <span class="gis-pr-blog-suffix">Points mentioned above are subject to change based on Australian immigration policy updates.</span>
                    Want to go to Australia without a job offer? Read this: <a aria-label="Link" href="https://getgis.org/blog/how-to-immigrate-to-australia-from-india-without-job-offer" target="_blank">How to Immigrate to Australia From India Without a Job Offer</a>
                </p>
                <h3 class="gis-pr-blog-title">
                    Tips to Increase Australia PR Points
                </h3>
                <p class="gis-pr-blog-desc">
                    Getting enough Australia PR Points can be tricky, but you can aim for the best by considering some important points. Here are 10 tips mentioned below to help you improve your Australia PR Points:
                </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Improve your English proficiency:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Aim for higher scores in English language tests such as IELTS, PTE Academic, or TOEFL, as it can significantly boost your points.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Enhance your qualifications:&nbsp;</span>
                        <span class="gis-pr-blog-suffix"> Obtain higher educational degrees or qualifications to earn more points. A master's degree or Ph.D. can provide additional points.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Gain work experience :&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Accumulate relevant work experience in your nominated occupation, both in Australia and outside. Longer work experience can increase your points.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Seek state or territory nomination:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Consider applying for a state or territory nomination, as it can add valuable points to your overall score.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Study in regional areas:&nbsp;</span>
                        <span class="gis-pr-blog-suffix"> Choosing to study in regional areas can grant you extra points. Look for universities or institutions located in these regions.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Obtain a skilled spouse:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">If your spouse possesses eligible skills, language proficiency, or qualifications, include them in your application to earn additional points.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Complete a professional year:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Undertake a professional year program in Australia, specifically in accounting, ICT, or engineering, to gain extra points.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Improve NAATI language skills:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Clearing the NAATI test in a local community language can provide additional points.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Explore regional opportunities:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Consider applying for regional visas like Subclass 491, as they offer additional points and have different eligibility criteria.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Stay updated with immigration policies:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">Regularly check for updates in immigration policies and requirements, as they may change over time.</span>
                    </li>

                </ul>
                <p class="gis-pr-blog-desc">
                    It is important to note that these tips are general suggestions, and the specific points allocation may vary depending on individual circumstances and changes in immigration regulations.<br />Read this to Immigrate to Australia after studies: <a aria-label="Link" href="https://getgis.org/blog/how-to-get-pr-in-australia-for-indian-students" target="_blank">How To Get PR in Australia For Indian Students After Study</a>
                </p>
                <h3 class="gis-pr-blog-title">
                    Streamline Australia PR Process with GetGIS!
                </h3>
                <p class="gis-pr-blog-desc">
                    Are you looking to make the Australia PR process smoother and more efficient? Look no further than
                    <a aria-label="Link" href="http://getgis.org" target="_blank">GetGIS</a> (Global Immigration Service), your trusted partner in
                    navigating the complexities of Australian immigration. With our expertise and comprehensive knowledge of the PR application process,
                    we can help you streamline your journey toward permanent residency in Australia.<br />Don't let the complexities of the Australia
                    PR process overwhelm you. Book a
                    <a aria-label="Link" href="https://immigration.getgis.org/immigration-services/" target="_blank">free consultation</a>
                    with GetGIS to take the first step towards your dream of living and working in Australia by contacting GetGIS today!

                </p>
            </div>
            <button aria-label="Continue Button" class="gis-pr-continue-btn">Continue Reading</button>
        </div>
    </div>
    <?php
    echo $this->render('../partials/visaApproval.php');
    if ($isFAQ): ?>
        <div class="gis-faq container-fluid">
            <div class="gis-faq-inner container">
                <div class="gis-faq-left">
                    <h1 class="gis-faq-heading">Frequently Asked Questions</h1>
                    <img loading="lazy" src="/yas/images/gis_faq.png" alt="..." class="mobileOnly" alt="faq image" />
                    <ul class="gis-accordion">
                        <?php foreach ($faqList->qnas as $faq): ?>
                            <li class="gis-accordion-section">
                                <div class="gis-accordion-header">
                                    <p><?= $faq['question'] ?></p>
                                </div>
                                <p class="gis-accordion-content">
                                    <?= $faq['answer'] ?>
                                </p>
                            </li>
                        <?php endforeach ?>
                    </ul>
                </div>
                <div class="gis-faq-right">
                    <img loading="lazy" src="/yas/images/gis_faq-right.webp" alt="..." class="desktopOnly" alt="faq image" />
                </div>
            </div>
        </div>
    <?php endif; ?>
</main>
<?php
$this->registerJsFile(Yii::$app->params['jsPath'] . 'calculator.js', ['depends' => [AppAsset::class], 'defer' => true]);
