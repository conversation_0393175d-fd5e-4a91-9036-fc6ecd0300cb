<?php

use common\helpers\DataHelper;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use yii\widgets\ActiveForm;

$sectionValues = DataHelper::ieltsBand();

//utils
$this->title = $seoTags['title'];
$this->context->description = $seoTags['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Immigration');
$this->params['canonicalUrl'] = Url::base(true) . '/immigration';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'prCalculator.css', ['depends' => [AppAsset::class]]);
$isFAQ = (isset($faqList->qnas) && !empty($faqList->qnas)) ?? $faqList->qnas;
$showform = 0;
if (Yii::$app->request->post()) {
    $showform = 1;
}
?>

<main class="gis-main">
    <div class="gis-pr-main container">
        <h1 class="gis-pr-heading">IELTS To CLB Score Calculator</h1>
        <p class="gis-pr-desc">
            SaskThe Canadian Language Benchmark (CLB) is the national benchmark for assessing Canadian immigration applications based on English or French language skill description, measurement, and recognition.</p>
    </div>
    <div class="detailsBox cblConvertBox">
        <?php $form = ActiveForm::begin(['id' => 'calculator', 'action' => false]) ?>
        <div class="gis-pr-calculator container">
            <div class="gis-pr-form-container">
                <div class="gis-pr-forms">
                    <div class="formWrap gis-pr-form" id="form1">
                        <div class="gis-pr-form-header">
                            <h1 class="gis-pr-form-heading">IELTS</h1>
                            <div class="gis-pr-form-action">
                                <!-- <?php // if (!isset(Yii::$app->user->identity)):
                                ?>
                                <button class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40"
                                        width="40" alt="unlock" />
                                    <span class="unlock-score">
                                        Unlock Score
                                    </span>
                                </button>
                            <?php // else:
                            ?> -->
                                <div class="gis-pr-form-unlock-ctn">
                                    <span class="gis-pr-form-score">
                                        <span class="myscore">My Score&nbsp;:&nbsp;<?= $score ?></span>
                                    </span>
                                </div>
                                <!-- <?php //endif;
                                ?> -->
                            </div>
                        </div>
                        <div class="clb-form">
                            <div class="inputField clbReadingScore">
                                <?= $form->field($model, 'readingScore')->dropDownList($sectionValues, ['prompt' => 'Select', 'class' => 'form-control', 'id' => 'clbReadingScore'])->label('Reading') ?>
                            </div>
                            <div class="inputField clbWritingScore">
                                <?= $form->field($model, 'writingScore')->dropDownList($sectionValues, ['prompt' => 'Select', 'class' => 'form-control', 'id' => 'clbWritingScore'])->label('Writing') ?>
                            </div>
                            <div class="inputField clbListeningScore">
                                <?= $form->field($model, 'listeningScore')->dropDownList($sectionValues, ['prompt' => 'Select', 'class' => 'form-control', 'id' => 'clbListeningScore'])->label('Listening') ?>
                            </div>
                            <div class="inputField clbSpeakingScore">
                                <?= $form->field($model, 'speakingScore')->dropDownList($sectionValues, ['prompt' => 'Select', 'class' => 'form-control', 'id' => 'clbSpeakingScore'])->label('Speaking') ?>
                            </div>
                            <input type="hidden" name="calculatorType" value="clbConverter">
                            <?= $form->field($model, 'examType')->hiddenInput(['value' => 'IELTS'])->label(false) ?>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form-btn">
                    <span class="gis-pr-form-score-reset clbMobile" onclick="window.location.href = '<?= Url::toClbConverter() ?>'">Start Again</span>
                    <!-- <?php if (!isset(Yii::$app->user->identity)): ?>
                        <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score" data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                            <img src="../../images/calculator/unlock.gif" height="40" width="40" alt="unlock" />
                            <span class="unlock-score">Unlock Score</span>
                        </span>
                         <?php else: ?>
                        <div class="gis-pr-form-unlock-ctn">
                             <?= $submitButton; ?>
                        </div>
                         <?php endif; ?> -->
                    <input type="hidden" id="userLoggedIn" value="0">
                    <input type="submit" class="cta_button clb_submit" value="GET MY CLB" data-ctatext="GET MY CLB" data-ctaposition="dynamic-cta-get-my-clb">
                    <span class="gis-pr-form-score-reset" onclick="window.location.href = '<?= Url::toClbConverter() ?>'">
                        <span class="reset">Start Again</span>
                    </span>

                </div>

                <input type="hidden" name="calculatorType" value="clbConverter">
                <?php ActiveForm::end(); ?>
                <?php
                // need to uncomment below once login concept envolved and also remove the showScore input
                // if (isset(Yii::$app->user->identity) && $showScore):
                echo '<input type="hidden" id="isshowScore" value="' . $showScore . '">';
                ?>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="gis-pr-blog">
            <div class="gis-pr-blog-unlock">
                <h3 class="gis-pr-blog-title">
                    IELTS to CLB calculator
                </h3>
                <p class="gis-pr-blog-desc">
                    The IELTS to CLB calculator is an online tool that helps people in understanding how their International English Language Testing System (IELTS) scores align with the Canadian Language Benchmarks(CLB).
                </p>
                <h3 class="gis-pr-blog-title">
                    What is CLB?: Canadian Language Benchmark
                </h3>
                <p class="gis-pr-blog-desc">
                    The CLB is a standard measurement used in Canada to assess language proficiency in English for immigration, education, and employment purposes. You can determine the corresponding CLB level by inputting your IELTS scores into the IELTS to CLB calculator
                    <br>With the Convert IELTS to CLB Calculator, you can determine your level of language proficiency in relation to Canadian requirements and standards.
                </p>
                <h3 class="gis-pr-blog-title">
                    IELTS to CLB Converter: How to Convert IELTS Score to CLB?
                </h3>
                <p class="gis-pr-blog-desc">
                    IELTS to CLB converter involves understanding the score bands of the IELTS test and correlating them with the corresponding CLB levels.<br><b>For Example,</b> let’s say you have scored an overall band score of 8.0 in your IELTS test with individual scores in
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Listening:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">8</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Speaking:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">7</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Reading:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">7.5</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Writing:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">8.0</span>
                    </li>
                </ul>
                <b>Then your CLB score will be 9.</b> This indicates a high level of language proficiency and can be beneficial for immigration.<br /><br />
                </p>
                <h3 class="gis-pr-blog-title">
                    IELTS to CLB Conversion Table
                </h3>
                <p class="gis-pr-blog-desc">
                    This calculator uses the language test equivalency chart provided by IRCC. Below is the table.
                </p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>CLB Level</th>
                            <th>Listening</th>
                            <th>Reading</th>
                            <th>Writing</th>
                            <th>Speaking</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>10.0</td>
                                <td>8.5 - 9.0</td>
                                <td>8.0 - 9.0</td>
                                <td>7.5 - 9.0</td>
                                <td>7.5 - 9.0</td>
                            </tr>
                            <tr>
                                <td>9.0</td>
                                <td>8.0</td>
                                <td>7.0</td>
                                <td>7.0</td>
                                <td>7.0</td>
                            </tr>
                            <tr>
                                <td>8.0</td>
                                <td>7.5</td>
                                <td>6.5</td>
                                <td>6.5</td>
                                <td>6.5</td>
                            </tr>
                            <tr>
                                <td>7.0</td>
                                <td>6.0</td>
                                <td>6.0</td>
                                <td>6.0</td>
                                <td>6.0</td>
                            </tr>
                            <tr>
                                <td>6.0</td>
                                <td>5.5</td>
                                <td>5.0</td>
                                <td>5.5</td>
                                <td>5.5</td>
                            </tr>
                            <tr>
                                <td>5.0</td>
                                <td>5.0</td>
                                <td>4.0</td>
                                <td>5.0</td>
                                <td>5.0</td>
                            </tr>
                            <tr>
                                <td>4.0</td>
                                <td>4.5</td>
                                <td>3.5</td>
                                <td>4.0</td>
                                <td>4.0</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <p class="gis-pr-blog-desc"><b>Let’s decode the table above with examples</b></p>
                <p class="gis-pr-blog-desc"><b>Example 1: IELTS to CLB conversion for 8777 </b></p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Ability</th>
                            <th>IELTS Score</th>
                            <th>CLB Levels</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Listening</td>
                                <td>8</td>
                                <td>9</td>
                            </tr>
                            <tr>
                                <td>Reading</td>
                                <td>7</td>
                                <td>9</td>
                            </tr>
                            <tr>
                                <td>Writing</td>
                                <td>7</td>
                                <td>9</td>
                            </tr>
                            <tr>
                                <td>Speaking</td>
                                <td>7</td>
                                <td>9</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <p class="gis-pr-blog-desc"><b>Example 2: IELTS to CLB conversion for 7.5,6,6,6 </b></p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Ability</th>
                            <th>IELTS Score</th>
                            <th>CLB Levels</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Listening</td>
                                <td>7.5</td>
                                <td>8</td>
                            </tr>
                            <tr>
                                <td>Reading</td>
                                <td>6</td>
                                <td>7</td>
                            </tr>
                            <tr>
                                <td>Writing</td>
                                <td>6</td>
                                <td>7</td>
                            </tr>
                            <tr>
                                <td>Speaking</td>
                                <td>6</td>
                                <td>7</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <h3 class="gis-pr-blog-title">
                    Minimum CLB Score for Canada PR
                </h3>
                <p class="gis-pr-blog-desc">
                    The minimum Canadian Language Benchmark (CLB) score required for Canada PR is determined by immigration principles and standards. The CLB levels range from CLB 1 (basic) to CLB 12 (advanced fluency). <br />A target CLB score of at least 7 is generally recommended, which corresponds to an IELTS score of 6. Candidates with a CLB 5 IELTS score may still be eligible for certain universities based on cutoffs.
                </p>
                <h3 class="gis-pr-blog-title">
                    Why Should You Target a CLB Score of 4, 7, or 9?
                </h3>
                <p class="gis-pr-blog-desc">
                    When aiming for a CLB (Canadian Language Benchmark) score in your IELTS preparation, it's crucial to understand the significance of different CLB levels for Canadian immigration. Here's why you should consider targeting CLB scores of 4, 7, or 9.
                    <br /><br /><b>CLB Level 4</b>
                </p>
                <p class="gis-pr-blog-desc">
                    If you're applying for immigration as a spouse, the English proficiency requirements are relatively lower. To be eligible for this route, a minimum CLB level 4 is required.
                    <br /><br /><b>CLB Level 7</b>
                </p>
                <p class="gis-pr-blog-desc">
                    The Federal Skilled Worker Program (FSWP) demands a higher language proficiency level. To qualify for the program, you'll need a minimum CLB level of 7 across all IELTS skills (reading, writing, listening, speaking). This program is designed for skilled workers pursuing professional career paths that necessitate a strong command of English. <br /><br /><b>CLB Level 9+</b>
                </p>
                <p class="gis-pr-blog-desc">
                    Achieving a CLB level of 9 or higher provides a significant advantage in your Canada immigration application. It has a notable impact on the assessment of your "transferable skills" section, demonstrating your advanced language proficiency to the Canadian government. Moreover, a high CLB score can even increase your chances of obtaining a permanent residency card.
                </p>
                <h3 class="gis-pr-blog-title">
                    Thinking of Moving Abroad? Here's How We Can Help
                </h3>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Point calculation:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">In GetGIS, we calculate your points on your behalf to eliminate any chances of rejection or failure.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Visa application:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">GetGIS helps you to complete your visa application properly and we even train you for your visa interview process, to make your immigration journey easier.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Preparation for Language Test:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">GetGIS offers you expert guidance for your preparation for language tests like IELTS/PTE/CELPIP.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Expert guidance for each step of your immigration:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">With GetGIS, you rest assured about your immigration journey. We help you to take every step in the right direction to make your immigration journey smooth.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Job Search assistance:&nbsp;</span>
                        <span class="gis-pr-blog-suffix">At GetGIS you receive complete job search assistance, from CV optimization, to mock interviews everything is covered by GetGIS. And we won’t leave you alone until you get your job in your dream country.</span>
                    </li>
                </ul>
                <p class="gis-pr-blog-desc">So, don’t waste your time thinking about the best time to visit Canada. Start your career in Canada with GetGIS by <a aria-label="Link" href="https://getgis.org/immigration/" target="_blank">Booking a Free Consultation Today!</a></p>
            </div>
            <button aria-label="Continue Button" class="gis-pr-continue-btn">Continue Reading</button>
        </div>
    </div>
    <?php
    echo $this->render('../partials/visaApproval.php');
    if ($isFAQ): ?>
        <div class="gis-faq container-fluid">
            <div class="gis-faq-inner container">
                <div class="gis-faq-left">
                    <h1 class="gis-faq-heading">Frequently Asked Questions</h1>
                    <img loading="lazy" src="/yas/images/gis_faq.png" alt="..." class="mobileOnly" alt="faq image" />
                    <ul class="gis-accordion">
                        <?php foreach ($faqList->qnas as $faq): ?>
                            <li class="gis-accordion-section">
                                <div class="gis-accordion-header">
                                    <p><?= $faq['question'] ?></p>
                                </div>
                                <p class="gis-accordion-content">
                                    <?= $faq['answer'] ?>
                                </p>
                            </li>
                        <?php endforeach ?>
                    </ul>
                </div>
                <div class="gis-faq-right">
                    <img loading="lazy" src="/yas/images/gis_faq-right.webp" alt="..." class="desktopOnly" alt="faq image" />
                </div>
            </div>
        </div>
    <?php endif; ?>
</main>
<?php
$this->registerJsFile(Yii::$app->params['jsPath'] . 'calculator.js', ['depends' => [AppAsset::class], 'defer' => true]);
