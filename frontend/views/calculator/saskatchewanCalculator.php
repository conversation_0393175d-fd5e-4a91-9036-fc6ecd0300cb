<?php

use common\models\calculator\SaskatchewanCalculator;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use yii\widgets\ActiveForm;

$educationList = SaskatchewanCalculator::EDUCATION_LIST;
$workExpList = SaskatchewanCalculator::WORK_EXP_LIST;
$ageList = SaskatchewanCalculator::AGE_LIST;
$languageProficiency = SaskatchewanCalculator::PROFICIENCY_LIST;
$yesNoList = SaskatchewanCalculator::YES_NO;

//utils
$this->title = $seoTags['title'];
$this->context->description = $seoTags['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Immigration');
$this->params['canonicalUrl'] = Url::base(true) . '/immigration';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'prCalculator.css', ['depends' => [AppAsset::class]]);
$isFAQ = (isset($faqList->qnas) && !empty($faqList->qnas)) ?? $faqList->qnas;
$showform = 0;
if (Yii::$app->request->post()) {
    $showform = 1;
}
?>

<main class="gis-main">
    <div class="gis-pr-main container">
        <h1 class="gis-pr-heading">SINP Points Calculator - Saskatchewan Expression of Interest (EOI)</h1>
        <p class="gis-pr-desc">
            Saskatchewan Immigration Points Calculator uses a unique Expression of Interest (EOI) points grid to rank candidates in its Saskatchewan Express Entry and Occupations In-Demand sub-categories. Candidates must receive at least 60 points out of a possible 110 on the SINP(Saskatchewan Immigration Nominee program) point assessment grid in order to be considered.
        </p>
    </div>
    <?php $form = ActiveForm::begin(['id' => 'calculator']) ?>
    <div class="gis-pr-calculator container">
        <div class="gis-pr-timeline" id="gis-pr-timeline">
            <div class=" gis-pr-step">
                <div class="gis-pr-step-number">1</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Educational Qualification</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">2</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Working Experience</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">3</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Additional Information</div>
            </div>
        </div>
        <div class="gis-pr-form-container">
            <div class="gis-pr-forms">
                <div class="gis-pr-form <?php if ($score == 0  || $showform) {
                                            echo ' active ';
                                        } ?>" id="form1">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Educational Qualification</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <button class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40"
                                        width="40" alt="unlock" />
                                    <span class="unlock-score">
                                        Unlock Score
                                    </span>
                                </button>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php //endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>What is your Education?</span>
                            </p>
                            <div class="gis-pr-form-field sinpEducation">
                                <?= $form->field($model, 'education')->dropDownList($educationList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'sinpEducation'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form2">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Work Experience</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Your work experience during the most recent five years prior to application
                            </p>
                            <div class="gis-pr-form-field sinpWorkExperienceFiveYrPrior">
                                <?= $form->field($model, 'workExperienceFiveYrPrior')->dropDownList($workExpList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'sinpWorkExperienceFiveYrPrior'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                Your work experience during the six to ten year period prior to application
                            </p>
                            <div class="gis-pr-form-field sinpWorkExperienceTenYrPrior">
                                <?= $form->field($model, 'workExperienceTenYrPrior')->dropDownList($workExpList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'sinpWorkExperienceTenYrPrior'])->label(false) ?>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="gis-pr-form" id="form3">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">
                            Additional Information
                        </h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php // if (!isset(Yii::$app->user->identity)):
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score"
                                    data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="/yas/images/gis_unlock.gif" height="40" width="40"
                                        alt="unlock" />
                                    <span class="unlock-score">Unlock Score</span>
                                </span>
                            <?php // else:
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score last-step">
                                    <span class="myscore">My Score&nbsp;:&nbsp;<?= $score['score'] ?? '0'; ?></span>
                                </span>
                                <span class="gis-pr-form-score-reset" onclick="window.location.href = '<?= Url::toSaskatchewanCalculator() ?>'">
                                    <span class="reset">Start Again</span>
                                </span>
                            </div>
                            <!-- <?php // endif;
                            ?> -->
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>What is your Age?</span>
                            </p>
                            <div class="gis-pr-form-field sinpAge">
                                <?= $form->field($model, 'age')->dropDownList($ageList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'sinpAge'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Proficiency in English</span>
                            </p>
                            <div class="gis-pr-form-field sinpEnglishProficiency">
                                <?= $form->field($model, 'englishProficiency')->dropDownList($languageProficiency, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'sinpEnglishProficiency'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Proficiency in French</span>
                            </p>
                            <div class="gis-pr-form-field sinpFrenchProficiency">
                                <?= $form->field($model, 'frenchProficiency')->dropDownList($languageProficiency, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'sinpFrenchProficiency'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-radio-ctn">
                            <p class="gis-pr-form-question">
                                <span>Do you have High skilled employment offer from a Saskatchewan employer?</span>
                            </p>
                            <div class="sinpEmploymentOffer">
                                <?= $form->field($model, 'employmentOffer')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-radio-ctn">
                            <p class="gis-pr-form-question">
                                <span>Do you have a close relative in Saskatchewan?</span>
                            </p>
                            <div class="sinpRelativeInSaskatchewan">
                                <?= $form->field($model, 'relativeInSaskatchewan')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-radio-ctn">
                            <p class="gis-pr-form-question">
                                <span>Do you have any previous study experience in Saskatchewan?</span>
                            </p>
                            <div class="sinpStudyExperienceInSaskatchewan">
                                <?= $form->field($model, 'studyExperienceInSaskatchewan')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-radio-ctn">
                            <p class="gis-pr-form-question">
                                <span>Do you have any previous work experience in Saskatchewan?</span>
                            </p>
                            <div class="sinpWorkExperienceInSaskatchewan">
                                <?= $form->field($model, 'workExperienceInSaskatchewan')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
            <div class="gis-pr-form-btn">
                <span id="prev-form-btn">Previous</span>
                <?php if (isset(Yii::$app->user->identity)): ?>
                    <span id="next-form-btn" class="next-form-btn basicDetails initial-form">Next</span>
                <?php else: ?>
                    <span id="next-form-btn" class="next-form-btn basicDetails">Next</span>
                <?php endif; ?>
                <?php
                echo ' <input type="submit" id="getmyscore" class="submitForm" value="Show Detailed Report">
                   <span id="hideReport" class="hideReport">Hide Report</span>';
                // if (isset(Yii::$app->user->identity)):
                //     echo '<input type="hidden" id="userLoggedIn" value="1">
                //     <input type="submit" id="getmyscore" class="submitForm" value="Show Detailed Report">
                //     <span id="hideReport" class="hideReport">Hide Report</span>';
                // else:
                //     echo '<input type="hidden" id="userLoggedIn" value="0">
                //     <input type="button" id="getmyscore" class="submitForm" value="Show Detailed Report"
                //                      data-ctatext="Show Detailed Report" data-ctaposition="dynamic-cta-show-detailed-report">';
                // endif;
                ?>
            </div>

            <span id="gis-pr-form-score-reset" class="gis-pr-form-score-reset resetMobile"
                onclick="window.location.href = '<?= Url::toSaskatchewanCalculator() ?>'">Start Again</span>
            <input type="hidden" name="calculatorType" value="saskatchewanCalculator">
            <?php ActiveForm::end(); ?>
            <?php
            // need to uncomment below once login concept envolved and also remove the showScore input
            // if (isset(Yii::$app->user->identity) && $showScore):
            echo '<input type="hidden" id="isshowScore" value="' . $showScore . '">';
            if ($showScore):
                ?>
                <div class="gis-pr-eligibility-score" id="showScore" style="display:block">
                    <div class="gis-pr-score-header">
                        <h3 class="gis-pr-score-heading">Your Eligibility</h3>
                        <p class="gis-pr-score-detail">
                            Saskatchewan Score -<span id="score189">
                                Your Score <?php if (isset($score['score'])) {
                                                echo $score['score'];
                                           } ?>/110</span>
                        </p>
                    </div>
                    <table class="gis-pr-score-card">
                        <thead class="gis-pr-score-card-head">
                            <tr class="gis-pr-score-name">
                                <th>Factors</th>
                                <th>Points</th>
                            </tr>
                        </thead>
                        <tbody class="gis-pr-score-card-body">
                            <tr class="gis-pr-score-number">
                                <td>Educational Qualification</td>
                                <td><?php if (isset($score['eduQuali'])) {
                                        echo $score['eduQuali'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Work Experience</td>
                                <td><?php if (isset($score['workExp'])) {
                                        echo $score['workExp'];
                                    } ?></td>
                            </tr>
                            <tr class="gis-pr-score-number">
                                <td>Additional Information</td>
                                <td><?php if (isset($score['additional'])) {
                                        echo $score['additional'];
                                    } ?></td>
                            </tr>
                        </tbody>
                    </table></span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="container express-cal">
        <div class="gis-pr-blog">
            <div class="gis-pr-blog-unlock">
                <h3 class="gis-pr-blog-title">
                    Saskatchewan SINP Points Calculator</h3>
                <p class="gis-pr-blog-desc">
                    Saskatchewan Immigrant Nominee Program (SINP) uses a unique approach of Expression of Interest (EOI) points calculation for ranking applicants in Saskatchewan Express Entry and Saskatchewan Occupation In-Demand Streams.</p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">The immigration streams under SINP include</li>
                    <li class="gis-pr-blog-list-item">Saskatchewan Experience Class</li>
                    <li class="gis-pr-blog-list-item">Entrepreneur and Farm category</li>
                </ul><br />
                <h3 class="gis-pr-blog-title">
                    How Many Points are Required for Saskatchewan PNP?
                </h3>
                <p class="gis-pr-blog-desc">
                    Applicants need to score a minimum of 60 out of 110 on the <b>Saskatchewan PNP points calculator</b> to qualify for Saskatchewan.<br />
                    The Saskatchewan EOI score is determined based on two factors
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">Labour Market Success (age, work experience, educational qualification, language proficiency)</li>
                    <li class="gis-pr-blog-list-item">Connection to Saskatchewan Labour Market and Adaptability</li>
                </ul>
                <b>SINP Canada immigration points calculator,</b> below helps you to self-evaluate the SINP score that your candidature could earn.
                All you need to do is to answer the below questions, in the mentioned order. </p>
                <h3 class="gis-pr-blog-title">How are points calculated for SINP?</h3>
                <p class="gis-pr-blog-desc">
                    For applicants wondering how to calculate SINP points, you can now do it from home by following these steps:
                </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">Determine your stream and check eligibility.</li>
                    <li class="gis-pr-blog-list-item">Assess factors: education, work experience, language, age, and adaptability.</li>
                    <li class="gis-pr-blog-list-item">Refer to the SINP point grid for point allocation.</li>
                    <li class="gis-pr-blog-list-item">Calculate points for each factor based on qualifications.</li>
                    <li class="gis-pr-blog-list-item">Total points obtained.</li>
                    <li class="gis-pr-blog-list-item">Check the minimum points requirement.</li>
                    <li class="gis-pr-blog-list-item">Refer to official SINP sources or seek professional guidance for accuracy.</li>
                </ul>
                <h3 class="gis-pr-blog-title">
                    SINP Program Points Calculator
                </h3>
                <p class="gis-pr-blog-desc">
                    To calculate the accurate score on the <b>SINP point assessment grid calculator,</b> kindly ensure to answer the questions to the best of your ability.
                </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item"> <span class="gis-pr-blog-prefix">Age :</span>
                        <span class="gis-pr-blog-suffix"> Age is often a crucial factor in immigration processes, as certain programs may have age limits or award points based on the applicant's age range.</span>
                    </li>
                </ul>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Below 18 years</td>
                                <td>00 points</td>
                            </tr>
                            <tr>
                                <td>At least 18 years but less than 21 years</td>
                                <td>08 points</td>
                            </tr>
                            <tr>
                                <td>At least 22 years but less than 34 years</td>
                                <td>12 points</td>
                            </tr>
                            <tr>
                                <td>At least 35 years but less than 45 years</td>
                                <td>10 points</td>
                            </tr>
                            <tr>
                                <td>At least 46 years but less than 50 years</td>
                                <td>08 points</td>
                            </tr>
                            <tr>
                                <td>50 years and above</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item customBullet">
                        <span class="gis-pr-blog-prefix">Education and Training :</span>
                        <span class="gis-pr-blog-suffix">Education qualification is yet another fundamental aspect of immigration eligibility in understanding the educational background of applicants and helping immigration authorities assess their potential contribution to the country's workforce and society.</span>
                    </li>
                </ul>
                <br />
                <span class="gis-pr-blog-prefix cal-sub-heading">a. Your highest educational qualification at the time of visa application submission</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Doctoral Degree (equal to Canadian Equivalency)</td>
                                <td>23 points</td>
                            </tr>
                            <tr>
                                <td>Master’s Degree (equal to Canadian Equivalency)</td>
                                <td>23 points</td>
                            </tr>
                            <tr>
                                <td>Bachelor’s Degree or a 3 years degree program at a university level</td>
                                <td>20 points</td>
                            </tr>
                            <tr>
                                <td>Trade Certification (equivalent to journeyperson status in Saskatchewan)</td>
                                <td>20 points</td>
                            </tr>
                            <tr>
                                <td>Diploma Degree that requires a minimum of 2 years but less than 3 years at a university/college/post-secondary institution</td>
                                <td>15 points</td>
                            </tr>
                            <tr>
                                <td>Diploma Degree that requires a minimum of 2 years but less than 3 years at a university/college/post-secondary institution</td>
                                <td>12 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading">b. Did you complete your full-time education qualification in a Saskatchewan province?</span>
                <br />
                <p class="gis-pr-blog-desc cal-sub-heading">This factor in the <b>SINP immigration points calculator</b> assesses your connectivity to the province and scope of adaptability. The applicant needs to have pursued a full-time academic year at a recognized Saskatchewan post-secondary institution on a valid student visa.
                </p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>

                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Skilled Work Experience :</span>
                        <span class="gis-pr-blog-suffix">The applicant needs to have relevant work experience as per the current job mentioned in the application. One year or 12 months of full-time work experience is mandatory.</span>
                    </li>
                </ul>
                <br>
                <span class="gis-pr-blog-prefix cal-sub-heading">a. How many years of work experience do you have in the last 5 years as of the date of visa application submission?</span>
                <br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>5 years</td>
                                <td>10 points</td>
                            </tr>
                            <tr>
                                <td>4 years</td>
                                <td>08 points</td>
                            </tr>
                            <tr>
                                <td>3 years</td>
                                <td>06 points</td>
                            </tr>
                            <tr>
                                <td>2 years</td>
                                <td>04 points</td>
                            </tr>
                            <tr>
                                <td>1 years</td>
                                <td>02 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading">b. How many years of work experience do you have in the last 6 -10 years as of the date of visa application submission?</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>5 years</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>4 years</td>
                                <td>04 points</td>
                            </tr>
                            <tr>
                                <td>3 years</td>
                                <td>03 points</td>
                            </tr>
                            <tr>
                                <td>2 years</td>
                                <td>02 points</td>
                            </tr>
                            <tr>
                                <td>1 years</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading">c. Have you worked for at least 12 months in the last 5 years on a work permit in Saskatchewan?</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>

                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item customBullet">
                        <span class="gis-pr-blog-prefix">Language Ability :</span>
                        <span class="gis-pr-blog-suffix">Applicants need to demonstrate their language proficiency to gain points in the SINP points calculator. Candidates must prove their English or French language proficiency through a language test acknowledged by the Government of Saskatchewan to be eligible for language points.</span>
                    </li>
                </ul>
                <br />
                <span class="gis-pr-blog-prefix cal-sub-heading">a. What best describes your English language proficiency in sections of reading, writing, listening, and speaking?</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CLB 8 and above</td>
                                <td>20 points</td>
                            </tr>
                            <tr>
                                <td>CLB 7</td>
                                <td>18 points</td>
                            </tr>
                            <tr>
                                <td>CLB 6</td>
                                <td>16 points</td>
                            </tr>
                            <tr>
                                <td>CLB 5</td>
                                <td>14 points</td>
                            </tr>
                            <tr>
                                <td>CLB 4</td>
                                <td>12 points</td>
                            </tr>
                            <tr>
                                <td>English speaker without language test results</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <span class="gis-pr-blog-prefix cal-sub-heading">b. What best describes your French language proficiency in sections of reading, writing, listening, and speaking?</span><br /><br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CLB 8 and above</td>
                                <td>10 points</td>
                            </tr>
                            <tr>
                                <td>CLB 7</td>
                                <td>08 points</td>
                            </tr>
                            <tr>
                                <td>CLB 6</td>
                                <td>06 points</td>
                            </tr>
                            <tr>
                                <td>CLB 5</td>
                                <td>04 points</td>
                            </tr>
                            <tr>
                                <td>CLB 4</td>
                                <td>02 points</td>
                            </tr>
                            <tr>
                                <td>Does not Speak French</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>

                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item customBullet">
                        <span class="gis-pr-blog-prefix">Close family members or relatives currently living in Saskatchewan :</span>
                        <span class="gis-pr-blog-suffix">If the applicant of the accompanying spouse or common-law partner has a family relative who holds Canadian citizenship or permanent residency, currently residing in Saskatchewan. The relative should be supporting any of the other family members/relatives during your application process.</span>
                    </li>
                </ul>
                <br />
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Particular</th>
                            <th>Points</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>YES</td>
                                <td>05 points</td>
                            </tr>
                            <tr>
                                <td>NO</td>
                                <td>00 points</td>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <h3 class="gis-pr-blog-title">
                    How to Improve Your SINP Score?
                </h3>
                <p class="gis-pr-blog-desc">
                    Boosting your SINP (Saskatchewan Immigrant Nominee Program) score can enhance your chances of successfully immigrating to Saskatchewan, Canada. Explore these strategies to improve your immigration prospects to Saskatchewan:
                </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Secure a job offer in Saskatchewan :</span>
                        <span class="gis-pr-blog-suffix">If you find an employer in Saskatchewan willing to sponsor you, you may be eligible to come to the province on a work permit.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix"> Obtain another graduate degree :</span>
                        <span class="gis-pr-blog-suffix">Consider pursuing a Ph.D., Master's, or other professional credential, as candidates with higher education can earn maximum points.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Enhance your language scores :</span>
                        <span class="gis-pr-blog-suffix">If you haven't achieved the highest language proficiency marks, retake the language exam to aim for a higher CLB (Canadian Language Benchmark) level and earn more points.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Apply with your spouse :</span>
                        <span class="gis-pr-blog-suffix">If your spouse or common-law partner possesses a degree, has taken a language proficiency test, or has Canadian work experience, you could earn additional points through the Spousal Sponsorship category.</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Have a sibling in Canada :</span>
                        <span class="gis-pr-blog-suffix">If you have a direct sibling (blood, adoption, marriage, or common-law partnership) who is a permanent resident or Canadian citizen, you can claim extra points in the immigration process.</span>
                    </li>
                </ul>

            </div>
            <button aria-label="Continue Button" class="gis-pr-continue-btn">Continue Reading</button>
        </div>
    </div>
    <?php
    echo $this->render('../partials/visaApproval.php');
    if ($isFAQ): ?>
        <div class="gis-faq container-fluid">
            <div class="gis-faq-inner container">
                <div class="gis-faq-left">
                    <h1 class="gis-faq-heading">Frequently Asked Questions</h1>
                    <img loading="lazy" src="/yas/images/gis_faq.png" alt="..." class="mobileOnly" alt="faq image" />
                    <ul class="gis-accordion">
                        <?php foreach ($faqList->qnas as $faq): ?>
                            <li class="gis-accordion-section">
                                <div class="gis-accordion-header">
                                    <p><?= $faq['question'] ?></p>
                                </div>
                                <p class="gis-accordion-content">
                                    <?= $faq['answer'] ?>
                                </p>
                            </li>
                        <?php endforeach ?>
                    </ul>
                </div>
                <div class="gis-faq-right">
                    <img loading="lazy" src="/yas/images/gis_faq-right.webp" alt="..." class="desktopOnly" alt="faq image" />
                </div>
            </div>
        </div>
    <?php endif; ?>
</main>
<?php
$this->registerJsFile(Yii::$app->params['jsPath'] . 'calculator.js', ['depends' => [AppAsset::class], 'defer' => true]);
