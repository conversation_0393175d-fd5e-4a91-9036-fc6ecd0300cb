<?php

use common\helpers\DataHelper;
use common\models\calculator\CrsCalculator;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use yii\bootstrap\ActiveForm;
use yii\web\View;

$maritalStatus = CrsCalculator::MARITAL_STATUS;
$yesNoList = CrsCalculator::YES_NO;
$ageList = CrsCalculator::ageList();
$educationList = CrsCalculator::EDUCATION_LIST;
$canadianEducationList = CrsCalculator::CANADIAN_EDUCATION_LIST;
$examTypes = CrsCalculator::EXAM_TYPE;
$sectionValues = DataHelper::ieltsBand();
$lastTenYearExp = CrsCalculator::LAST_10_YR_EXP;
$foreignYearExp = CrsCalculator::FOREIGN_YEAR_EXP;
$nocList = CrsCalculator::NOC_TYPE;

//utils
$this->title = $seoTags['title'];
$this->context->description = $seoTags['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Immigration');
$this->params['canonicalUrl'] = Url::base(true) . '/immigration';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'prCalculator.css', ['depends' => [AppAsset::class]]);
$isFAQ = (isset($faqList->qnas) && !empty($faqList->qnas)) ?? $faqList->qnas;
$showform = 0;
if (Yii::$app->request->post()) {
    $showform = 1;
}
?>

<main class="gis-main">
    <div class="gis-pr-main container">
        <h1 class="gis-pr-heading">Canada CRS Points Calculator for Skilled Immigration - Calculate CRS Score Instantly with Free Tool</h1>
        <p class="gis-pr-desc">
            CRS (Comprehensive Ranking System), is used to calculate points under the Canada Skilled Worker Points System. We evaluate and score your profile using the CRS, a points-based methodology used to rank your application in the Express Entry pool. The maximum point in the CRS system is 1200. CRS scores of just over 450 and above is a good score.
        </p>
    </div>
    <?php $form = ActiveForm::begin(['id' => 'calculator']) ?>
    <div class="gis-pr-calculator container">
        <div class="gis-pr-timeline" id="gis-pr-timeline">
            <div class=" gis-pr-step">
                <div class="gis-pr-step-number">1</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Marital Status</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">2</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Age and Education</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">3</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Official Language</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">4</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Work Experience</div>
                <div class="gis-pr-timeline-line"></div>
            </div>
            <div class="gis-pr-step">
                <div class="gis-pr-step-number">5</div>
                <div class="wrapper gis-pr-step-check">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div class="gis-pr-step-title">Additional Information</div>
            </div>
        </div>
        <div class="gis-pr-form-container gis-crs-calculator">
            <div class="gis-pr-forms">
                <div class="gis-pr-form <?php if ($score == 0 || $showform) {
                                            echo 'active';
                                        } ?>" id="form1">
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Marital Status</h1>
                        <div class="gis-pr-form-action">
                            <!-- <?php //if (!isset(Yii::$app->user->identity)) :
                            ?>
                                <span class="gis-pr-form-unlock getLeadForm" data-ctatext="Unlock-score" data-ctaposition="dynamic-cta-basic-detail-unlock-score">
                                    <img loading="lazy" src="../../images/calculator/unlock.gif" height="40" width="40" alt="unlock" />
                                    <span class="unlock-score">
                                        Unlock Score
                                    </span>
                                </span>
                            <?php // else :
                            ?> -->
                            <div class="gis-pr-form-unlock-ctn">
                                <span class="gis-pr-form-score">
                                    <span class="myscore">My Score&nbsp;:&nbsp;0</span>
                                </span>
                            </div>
                            <?php //endif;
                            ?>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>What is your marital status?</span>
                            </p>
                            <div class="gis-pr-form-field CrsMaritalStatus">
                                <?= $form->field($model, 'maritalStatus')->dropDownList($maritalStatus, ['prompt' => 'Select', 'id' => 'CrsMaritalStatus', 'class' => 'gis-pr-form-select-2'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions spouseData">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Is your spouse or common-law partner a citizen or permanent resident of Canada?</span>
                            </p>
                            <div class="CrsCitizenship">
                                <?= $form->field($model, 'citizenship')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions spouseData accompanyYou">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Will your spouse accompany you to Canada?</span>
                            </p>
                            <div class="CrsAccompaniedBySpouse">
                                <?= $form->field($model, 'accompaniedBySpouse')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Age and Education</h1>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>How old are you?</span>
                            </p>
                            <div class="gis-pr-form-field CrsAge">
                                <?= $form->field($model, 'age')->dropDownList($ageList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'CrsAge'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Whats your education? (Note: For “professional degree,” the degree program must have been in: medicine, veterinary medicine, dentistry, optometry, law, chiropractic medicine, or pharmacy.)</span>
                            </p>
                            <div class="gis-pr-form-field CrsEducation">
                                <?= $form->field($model, 'education')->dropDownList($educationList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'CrsEducation'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Do you have a Canadian degree, diploma or certificate?</span>
                            </p>
                            <div class="CrsCanadianDegree">
                                <?= $form->field($model, 'canadianDegree')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions canadianEducation">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Describe your Canadian education</span>
                            </p>
                            <div class="gis-pr-form-field CrsCanadaEducation">
                                <?= $form->field($model, 'canadianEducation')->dropDownList($educationList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'CrsCanadaEducation'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-header">
                        <h1 class="gis-pr-form-heading">Official Language</h1>
                    </div>
                    <div class="gis-pr-form-questions">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Are your test results less than 2 years old?</span>
                            </p>
                            <div class="CrsLatestResult">
                                <?= $form->field($model, 'latestResult')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options', 'id' => 'latestResult'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div class="gis-pr-form-questions langBox primaryTestResult">
                        <div class="gis-pr-form-select">
                            <p class="gis-pr-form-question">
                                <span>Which language test did you take for your first official language?</span>
                            </p>
                            <div class="gis-pr-form-field crsPrimaryExam">
                                <?= $form->field($model, 'primaryExam')->dropDownList($examTypes, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsPrimaryExam'])->label(false) ?>
                            </div>
                        </div>
                    </div>
                    <div id="seperateDiv1">
                        <div class="gis-pr-form-questions primaryTestResult">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Reading Score</span>
                                </p>
                                <div class="gis-pr-form-field crsPrimaryReadingScore">
                                    <?= $form->field($model, 'primaryReadingScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsPrimaryReadingScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions primaryTestResult">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Writing Score</span>
                                </p>
                                <div class="gis-pr-form-field crsPrimaryWritingScore">
                                    <?= $form->field($model, 'primaryWritingScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsPrimaryWritingScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions primaryTestResult">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Listening Score</span>
                                </p>
                                <div class="gis-pr-form-field crsPrimaryListeningScore">
                                    <?= $form->field($model, 'primaryListeningScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsPrimaryListeningScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions primaryTestResult">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Speaking Score</span>
                                </p>
                                <div class="gis-pr-form-field crsPrimarySpeakingScore">
                                    <?= $form->field($model, 'primarySpeakingScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsPrimarySpeakingScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions primaryTestResult">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Do you have other language results?</span>
                                </p>
                                <div class="gis-pr-form-field crsSecondaryLanguage">
                                    <?= $form->field($model, 'secondaryLanguage')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSecondaryLanguage'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="seperateDiv">
                        <div class="gis-pr-form-questions secondaryScore">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Reading Score</span>
                                </p>
                                <div class="gis-pr-form-field crsSecondaryReadingScore">
                                    <?= $form->field($model, 'secondaryReadingScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSecondaryReadingScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions secondaryScore">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Writing Score</span>
                                </p>
                                <div class="gis-pr-form-field crsSecondaryWritingScore">
                                    <?= $form->field($model, 'secondaryWritingScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSecondaryWritingScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions secondaryScore">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Listening Score</span>
                                </p>
                                <div class="gis-pr-form-field crsSecondaryListeningScore">
                                    <?= $form->field($model, 'secondaryListeningScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSecondaryListeningScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions secondaryScore">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Speaking Score</span>
                                </p>
                                <div class="gis-pr-form-field crsSecondarySpeakingScore">
                                    <?= $form->field($model, 'secondarySpeakingScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSecondarySpeakingScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="skipForm">
                        <div class="gis-pr-form-header">
                            <h1 class="gis-pr-form-heading">Work Experience</h1>
                        </div>
                        <div class="gis-pr-form-questions">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>In the last ten years, how many years of skilled work experience do you have in Canada?</span>
                                </p>
                                <div class="gis-pr-form-field CrsSkilled">
                                    <?= $form->field($model, 'skilledLastTenYearExp')->dropDownList($lastTenYearExp, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'CrsSkilled'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>In the last ten years, how many total years of foreign skilled work experience do you have?</span>
                                </p>
                                <div class="gis-pr-form-field CrsForeignSkilled">
                                    <?= $form->field($model, 'foreignSkilledLastTenYearExp')->dropDownList($foreignYearExp, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'CrsForeignSkilled'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Do you have a certificate of qualification from a Canadian province, territory or federal body?</span>
                                </p>
                                <div class="CrsCertificate">
                                    <?= $form->field($model, 'certificate')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="skipForm">
                        <div class="gis-pr-form-header">
                            <h1 class="gis-pr-form-heading">Additional Information</h1>
                        </div>
                        <div class="gis-pr-form-questions">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Do you have a valid job offer supported by a Labour Market Impact Assessment?</span>
                                </p>
                                <div class="CrsValidJob">
                                    <?= $form->field($model, 'validJob')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions noc">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Which NOC skill type or level is the job offer?</span>
                                </p>
                                <div class="gis-pr-form-field CrsNoc">
                                    <?= $form->field($model, 'noc')->dropDownList($nocList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'CrsNoc'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Do you have a nomination certificate from a province or territory?</span>
                                </p>
                                <div class="CrsNomination">
                                    <?= $form->field($model, 'nomination')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Do you or your spouse or common law partner (if they will come with you to Canada) have at least one brother or sister living in Canada who is a citizen or permanent resident?</span>
                                </p>
                                <div class="CrsRelativeInCanada">
                                    <?= $form->field($model, 'relativeInCanada')->radioList($yesNoList, ['class' => 'gis-pr-form-radio-options'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions spouseData">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Highest level of education for which your spouse or common-law partner's has</span>
                                </p>
                                <div class="gis-pr-form-field CrsSpouseEducation">
                                    <?= $form->field($model, 'spouseEducation')->dropDownList($educationList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'CrsSpouseEducation'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions spouseData">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>In the last ten years, how many years of skilled work experience in Canada does your spouse/common-law partner have?</span>
                                </p>
                                <div class="gis-pr-form-field CrsSpouseSkilled">
                                    <?= $form->field($model, 'spouseSkilledLastTenYearExp')->dropDownList($lastTenYearExp, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'CrsSpouseSkilled'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions spouseData">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Did your spouse or common-law partner take a language test? (Test results must be less than two years old)</span>
                                </p>
                                <div class="gis-pr-form-field CrsSpouseLatestResult">
                                    <?= $form->field($model, 'spouseLatestResult')->dropDownList($yesNoList, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'CrsSpouseLatestResult'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions spouseLanguageTest">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>If so, which one?</span>
                                </p>
                                <div class="gis-pr-form-field crsSpousePrimaryExam">
                                    <?= $form->field($model, 'spousePrimaryExam')->dropDownList($examTypes, ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSpousePrimaryExam'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions langBox spouseLanguageTest">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Spouse IELTS Reading Scores</span>
                                </p>
                                <div class="gis-pr-form-field crsSpousePrimaryReadingScore">
                                    <?= $form->field($model, 'spousePrimaryReadingScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSpousePrimaryReadingScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions langBox spouseLanguageTest">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Spouse IELTS Writing Scores</span>
                                </p>
                                <div class="gis-pr-form-field crsSpousePrimaryWritingScore">
                                    <?= $form->field($model, 'spousePrimaryWritingScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSpousePrimaryWritingScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions langBox spouseLanguageTest">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Spouse IELTS Listening Scores</span>
                                </p>
                                <div class="gis-pr-form-field crsSpousePrimaryListeningScore">
                                    <?= $form->field($model, 'spousePrimaryListeningScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSpousePrimaryListeningScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                        <div class="gis-pr-form-questions langBox spouseLanguageTest">
                            <div class="gis-pr-form-select">
                                <p class="gis-pr-form-question">
                                    <span>Spouse IELTS Speaking Scores</span>
                                </p>
                                <div class="gis-pr-form-field crsSpousePrimarySpeakingScore">
                                    <?= $form->field($model, 'spousePrimarySpeakingScore')->dropDownList([], ['prompt' => 'Select', 'class' => 'gis-pr-form-select-2', 'id' => 'crsSpousePrimarySpeakingScore'])->label(false) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="gis-pr-form-btn">
                <span class="gis-pr-form-score-reset crsMobile" onclick="window.location.href = '<?= Url::toCrsCalculator() ?>'">Start Again</span>
                <input type="button" class="clb_submit crsSubmit submitForm" value="Show Detailed Report">
                <span class="gis-pr-form-score-reset" style="" onclick="window.location.href = '<?= Url::toCrsCalculator() ?>'">Start Again</span>
                <input type="hidden" name="calculatorType" value="crsCalculator">
            </div>

            <?php ActiveForm::end();
            ?>

            <div class="gis-pr-eligibility-score" id="showScore">
                <div class="gis-pr-score-header">
                    <h3 class="gis-pr-score-heading">Your Eligibility</h3>
                    <p class="gis-pr-score-detail">
                        Comprehensive System Ranking formula Grand Total is :<span id="score189">
                            Your Score <?php if (isset($score['score'])) {
                                            echo $score['score'];
                                       } ?>/1200</span>
                    </p>
                </div>

            </div>
            <div class=" gis-pr-eligibility-score notEligible" id="notEligible">You do not appear to be eligible for Express Entry at this time. Your test results are more than 2 years old</div>
        </div>
    </div>

    <div class="container express-cal">
        <div class="gis-pr-blog">
            <div class="gis-pr-blog-unlock">
                <h3 class="gis-pr-blog-title">
                    CRS Score Calculator for Canada PR
                </h3>
                <p class="gis-pr-blog-desc">
                    This user-friendly calculator provides a comprehensive assessment, enabling candidates to gauge their eligibility for Canadian immigration. By calculating their CRS score through this CRS calculator, applicants can strategize and enhance their profiles to increase their CRS scores.
                </p>
                <h3 class="gis-pr-blog-title">
                    What is the CRS Cut-off Score for 2023?
                </h3>
                <p class="gis-pr-blog-desc">
                    The average cut-off score stands at 470. IRCC employs a CRS draw as a method to choose Express Entry applicants whose CRS scores surpass a pre-defined threshold in each draw. These draws typically occur every two weeks on Wednesdays, although IRCC may occasionally deviate from this pattern.
                </p>
                <h3 class="gis-pr-blog-title">
                    How is Canada's CRS Score Calculated?
                </h3>
                <p class="gis-pr-blog-desc">
                    The Canada Comprehensive Ranking System (CRS) score is calculated based on various factors that assess an individual's eligibility for immigration to Canada through the Express Entry system.
                    Here’s an overview of factors that contribute to the CRS Score:
                    <b>Core Human Capital Factors :</b> These factors include age, level of education, official language proficiency (English or French), and Canadian work experience
                </p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Factors</th>
                            <th>With Spouse/ common-law partner</th>
                            <th>Without a Spouse/ common-law partner</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Age</td>
                                <td>110</td>
                                <td>100</td>
                            </tr>
                            <tr>
                                <td>Education level</td>
                                <td>150</td>
                                <td>140</td>
                            </tr>
                            <tr>
                                <td>Official languages proficiency (IELTS)</td>
                                <td>160</td>
                                <td>150</td>
                            </tr>
                            <tr>
                                <td>Canadian working experience</td>
                                <td>80</td>
                                <td>70</td>
                            </tr>
                            <tr>
                                <td>Spouse - Level of education</td>
                                <td>10</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>Spouse - Official language proficiency</td>
                                <td>20</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>Spouse Required IELTS Score (General)</td>
                                <td>10</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <th>Total</th>
                                <th>540</th>
                                <th>460</th>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <p class="gis-pr-blog-desc">
                    <b>Skill Transferability Factors: </b> These factors assess the combination of an individual's education, language proficiency, and work experience to determine their potential for success in the Canadian labor market
                </p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Factors</th>
                            <th>Maximum Points per Factor</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Education and Canadian Work Experience</td>
                                <td>50</td>
                            </tr>
                            <tr>
                                <td>Language Ability and (Foreign Work Experience)<br />Canadian Work Experience and Foreign Work Experience<br />Certificate of Qualification in a Trade and Language Ability </td>
                                <td>50</td>
                            </tr>
                            <tr>
                                <th>Total</th>
                                <th>100</th>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <p class="gis-pr-blog-desc">
                    Additional Factors: Additional points can be earned for factors such as having a sibling in Canada, having strong French language skills, or completing post-secondary education in Canada.
                </p>
                <span class="table-responsive">
                    <table>
                        <thead>
                            <th>Factors</th>
                            <th>Max. Points per Factor</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Brother or sister in Canada</td>
                                <td>15</td>
                            </tr>
                            <tr>
                                <td>Post-secondary education (1-2 years) in Canada</td>
                                <td>15</td>
                            </tr>
                            <tr>
                                <td>Post-secondary education (3 years or longer) in Canada</td>
                                <td>30</td>
                            </tr>
                            <tr>
                                <td>Arranged employment (NOC 00)</td>
                                <td>200</td>
                            </tr>
                            <tr>
                                <td>Arranged employment (NOC 0, A, or B)</td>
                                <td>50</td>
                            </tr>
                            <tr>
                                <td>Provincial Nomination</td>
                                <td>600</td>
                            </tr>
                            <tr>
                                <th>Maximum Points to be claimed</th>
                                <th>600</th>
                            </tr>
                        </tbody>
                    </table>
                </span>
                <h3 class="gis-pr-blog-title">
                    CRS Score Breakdown
                </h3>
                <p class="gis-pr-blog-desc">
                    The CRS score can vary based on many different factors. Each factor can give you more points if you would be eligible for it. Here’s a breakdown of the maximum points that you can get based on different factors:
                </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-suffix">Human capital or Core factor + Common-law partner or spouse factor = 500 points</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-suffix">Human capital or Core factor + Common-law partner or spouse factor + Transferability factors = 600 points (maximum)</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-suffix">Human capital or Core factor + Common-law partner or spouse factor + Transferability factors + Additional points = 1200 points (maximum)</span>
                    </li>
                </ul>
                <h3 class="gis-pr-blog-title">
                    CRS Score Under Each Factor
                </h3>
                <p class="gis-pr-blog-desc">
                    Each of the factors that affect the CRS score can further help you get different points depending on different criteria. Here is a detailed breakdown of points under each of these factors to help you understand better:
                </p>
                <h3 class="gis-pr-blog-title">
                    How to Improve Your CRS Score?
                </h3>
                <p class="gis-pr-blog-desc">
                    Every Canada PR applicant aims to get the right CRS score but only a few are able to do so. Here are 7 points that you should focus on to improve your CRS score and be among those few:
                </p>
                <ul class="gis-pr-blog-list">
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Enhance Language Proficiency:</span>
                        <span class="gis-pr-blog-suffix">Human capital or Core factor + Common-law partner or spouse factor = 500 points</span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Further Your Education:</span>
                        <span class="gis-pr-blog-suffix">Pursuing additional education, such as a diploma, degree, or certification, can earn you valuable points. Focus on programs that align with the Canadian labor market and qualify for higher points.
                        </span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Gain Additional Work Experience:</span>
                        <span class="gis-pr-blog-suffix">PAcquiring more skilled work experience can positively impact your CRS score. Aim for relevant employment in a high-demand occupation or target provinces with specific labor market needs.
                        </span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Secure a Job Offer:</span>
                        <span class="gis-pr-blog-suffix">Obtaining a valid job offer from a Canadian employer can significantly boost your CRS score. Explore opportunities through job boards, networking, or connecting with Canadian recruitment agencies.
                        </span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Improve Your Adaptability:</span>
                        <span class="gis-pr-blog-suffix">Focus on enhancing factors that demonstrate your adaptability to life in Canada. Consider factors like family ties in Canada, previous study experience in Canada, or securing a provincial nomination.
                        </span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Maximize Points for Spouse/Common-law Partner:</span>
                        <span class="gis-pr-blog-suffix">If applicable, ensure your spouse or common-law partner also takes language tests and maximizes their education and work experience points. Optimizing their CRS score can positively impact your overall score.
                        </span>
                    </li>
                    <li class="gis-pr-blog-list-item">
                        <span class="gis-pr-blog-prefix">Reassess Your Express Entry Profile:</span>
                        <span class="gis-pr-blog-suffix">Regularly review and update your Express Entry profile to accurately reflect any changes in your circumstances, such as gaining additional work experience, achieving higher education, or improving language test results.
                        </span>
                    </li>
                </ul>
                <h3 class="gis-pr-blog-title">
                    Streamline Your Canada PR Process With GetGIS!
                </h3>
                <p class="gis-pr-blog-desc">
                    Looking to simplify and expedite your Canada PR journey? Look no further than <a aria-label="Link" href="https://getgis.org/">GetGIS!</a> Our comprehensive platform is designed to streamline the entire process, making it easier and more efficient for you to navigate the complexities of Canadian immigration.<br /><br />

                    With GetGIS, you can confidently assess your eligibility, calculate your CRS score, and access valuable resources and guidance every step of the way. Don't let the intricate procedures overwhelm you – <a aria-label="Link" href="https://immigration.getgis.org/immigration-services/">Book a free consultation</a> and let GetGIS empower your Canada PR success!
                </p>
            </div>
            <button aria-label="Continue Button" class="gis-pr-continue-btn">Continue Reading</button>
        </div>
    </div>
    <?php
    echo $this->render('../partials/visaApproval.php');
    if ($isFAQ): ?>
        <div class="gis-faq container-fluid">
            <div class="gis-faq-inner container">
                <div class="gis-faq-left">
                    <h1 class="gis-faq-heading">Frequently Asked Questions</h1>
                    <img loading="lazy" src="/yas/images/gis_faq.png" alt="..." class="mobileOnly" alt="faq image" />
                    <ul class="gis-accordion">
                        <?php foreach ($faqList->qnas as $faq): ?>
                            <li class="gis-accordion-section">
                                <div class="gis-accordion-header">
                                    <p><?= $faq['question'] ?></p>
                                </div>
                                <p class="gis-accordion-content">
                                    <?= $faq['answer'] ?>
                                </p>
                            </li>
                        <?php endforeach ?>
                    </ul>
                </div>
                <div class="gis-faq-right">
                    <img loading="lazy" src="/yas/images/gis_faq-right.webp" alt="..." class="desktopOnly" alt="faq image" />
                </div>
            </div>
        </div>
    <?php endif; ?>
</main>
<?php

$this->registerJsVar('examType', CrsCalculator::EXAM_TYPE, View::POS_BEGIN);
$this->registerJsVar('examSection', CrsCalculator::EXAM_SECTION, View::POS_BEGIN);
$this->registerJsFile(Yii::$app->params['jsPath'] . 'crs-calculator.js', ['depends' => [AppAsset::class], 'defer' => true]);
