<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;

?>
<?php if (is_array($faqs)): ?>
    <div class="faq_section <?= count($faqs) > 6 ? 'pageInfo' : '' ?>showMoreScroll">
        <?php

        if ($pageName == 'info') { ?>
            <h2><?= ' FAQs on ' . $displayName ?? '' ?></h2>
        <?php } else {
            $displayName = $displayName ?? '';
            $pageName = $pageName ?? '';
            ?>
            <h2><?= ' FAQs on ' . $displayName . ' ' . $pageName ?></h2>
        <?php  } ?>
        <div class="faqDiv">
            <?php foreach ($faqs as $faq): ?>
                <div>
                    <p class="faq_question"><?= 'Q: ' . stripslashes(ContentHelper::htmlDecode(
                        DataHelper::parseDomainUrlInContent($faq->question),
                        true
                    )) ?> </p>
                    <div class="faq_answer"> <?= 'A: ' . stripslashes(ContentHelper::htmlDecode(
                        DataHelper::parseDomainUrlInContent($faq->answer),
                        false
                    )) ?> </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif; ?>