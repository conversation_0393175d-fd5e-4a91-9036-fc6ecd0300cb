<?php

use common\helpers\CollegeHelper;
use common\models\LeadBucketTagging;
use common\services\CollegeService;
use common\services\UserService;
use frontend\helpers\Url;

$sponsorCollegesLists = array_chunk($models, 2);
$loopTime = ceil(count($models) / 2);

if (!empty($sponsorCollegesLists)) {
    ?>
    <div class="college__carousel">
        <h3 class="carousel__heading" <?php echo $loopTime; ?> <?php echo count($models); ?>>
            Top College Accepting Applications
        </h3>
        <div class="carousel-container">
            <div class="carousel-track">
                <?php foreach ($sponsorCollegesLists as $key => $sponsorCollegesList): ?>
                    <?php foreach ($sponsorCollegesList as $key => $model):
                        $dynamicCta = UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGE_LISTING, 'college-listing', $model['display_name'] ?? $model['name'], $model['slug'], $model['city_name']);
                        $this->params['dynamicCta'] = $dynamicCta ?? [];
                        $ctaLocation1 = $isMobile ? $dynamicCta['cta_position_0']['wap'] : $dynamicCta['cta_position_0']['web'];
                        $ctaLocation2 = $isMobile ? $dynamicCta['cta_position_1']['wap'] : $dynamicCta['cta_position_1']['web'];
                        $collegeContentPageStatus = CollegeService::getStatus($model['college_id']);
                        $streamSlug = '';

                        if (!empty($request['course']) && $request['stream']) {
                            $collegeId = $model['college_id'] ?? '';
                            $courseSlug = $request['course'] ?? '';
                            $streamSlug = $request['stream'] ?? '';
                        } elseif (!empty($request['stream'])) {
                            $collegeId = $model['college_id'] ?? '';
                            $streamSlug = $request['stream'] ?? '';
                        } else {
                            $collegeId = $model['college_id'];
                        }
                        ?>

                        <div class="carousel-item carousel__college__card">
                            <h3 class="carousel__card__heading"><a href="<?= Url::toCollege($model['slug']) ?>" title="<?= $model['display_name'] ?? $model['name'] ?>"><?= $model['display_name'] ?? $model['name'] ?></a></h3>


                            <img class="lazyload carousel__card__image" loading="lazy" src="<?= !empty($model['logo']) ? Url::getCollegeLogo($model['logo']) : Url::defaultCollegeLogo() ?>" onclick="gmu.url.goto('<?= Url::toCollege($model['slug']) ?>')" alt="">
                            <p class="carousel__card__location">
                                <?= $model['city_name'] ?>, <?= $model['state_name'] ?>
                            </p>
                            <span class="carousel__card__accredition"><?= $model['type'] ? ucfirst($model['type']) : '' ?></span>
                            <?php if ($model['rating'] > 0) { ?>
                                <div class="carousel__card__rating">
                                    <span class="spriteIcon__2 review__star__icon"></span><?= $model['rating']; ?>
                                </div>
                            <?php } ?>
                            <div class="carousel__highlights">
                                <div class="tution">
                                    <p class="label">Tuition
                                        Fees</p>
                                    <p class="value">
                                        <?php if (isset($model['courseCount']) && $model['courseCount'] != 0) {
                                            if (isset($model['avgFees'])) {
                                                echo '₹' . CollegeHelper::feesFormatListing($model['avgFees']);
                                            } else {
                                                echo '-/-';
                                            };
                                        } else {
                                            echo '-/-';
                                        } ?>

                                    </p>
                                </div>
                                <div class="package">
                                    <p class="label">Avg.
                                        Placement</p>
                                    <p class="value"><?= !empty($model['median_salary']) ? '₹' . CollegeHelper::feesFormatListing(floor($model['median_salary'])) : '-/-' ?>
                                    </p>
                                </div>
                            </div>
                            <div class="lead-cta-college-filter-2 leadFilterData primaryBtn" data-entity="college" data-stream="<?= $streamSlug ?>"
                                data-lead_cta="11" data-filter="college-listing" data-slug="<?= $model['college_id'] ?>" data-stateId="<?= $model['state_id'] ?>"
                                data-cityId="<?= $model['city_id'] ?>" data-course="<?= $courseSlug ?? '' ?>" data-ctaLocation="<?= $ctaLocation2 ?>"
                                data-title="<?= $dynamicCta['cta_position_1']['lead_form_title'] ?>" data-description="<?= $dynamicCta['cta_position_1']['lead_form_description'] ?>"
                                data-image="<?= Url::getCollegeLogo($model['logo']) ?>"></div>

                        </div>
                    <?php endforeach; ?> 
                <?php endforeach; ?>
            </div>
        </div>
        <?php if ($loopTime >= 2) { ?>
            <div class="carousel-dots">
                <?php for ($i = 0; $i < $loopTime; $i++) { ?>
                    <span class="dot active" data-slide="<?= $i ?>"></span>
                <?php } ?>

            </div>
        <?php     } ?>

    </div>
<?php  } ?>