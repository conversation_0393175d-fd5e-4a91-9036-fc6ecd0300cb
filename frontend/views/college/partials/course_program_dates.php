<?php

use common\helpers\CollegeRanksDataHelper;
use common\helpers\DataHelper;
?>
<?php if (!empty($dates)): ?>
    <div class="pageData collegeRankings <?= count($dates) > 2 ? 'pageInfo' : '' ?>">
        <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= $title ?> Dates</h2>
        <table class="courseFeesTable rankTable">
            <thead>
                <tr>
                    <td>Name</td>
                    <td>Title</td>
                    <td>Range</td>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($dates as $date):
                    if (empty($date->start)) {
                        continue;
                    }
                    $start = !empty($date->start) ? Yii::$app->formatter->asDate($date->start) : '';
                    $end = !empty($date->end) ? Yii::$app->formatter->asDate($date->end) : '';

                    ?>
                    <tr>
                        <td><?= $title ?></td>
                        <td><?= $date->name ?></td>
                        <td><?= $start . ' - ' . $end ?></td>
                    </tr>
                <?php endforeach; ?>

            </tbody>
        </table>
    </div>
<?php endif; ?>