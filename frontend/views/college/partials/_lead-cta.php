<?php

use common\helpers\CollegeHelper;
use common\services\UserService;
use common\services\CollegeService;
use common\models\College;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
$stateName = UserService::getCityId('', '', $state);
$isSponsored = !empty($entity_id) ? CollegeService::getSponsoredStatus($entity_id) : '';

$defaultEmptyCondition0 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_0']) || empty(array_filter($dynamicCta['cta_position_0'])));
$defaultEmptyCondition1 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])));
$defaultEmptyCondition2 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_2']) || empty(array_filter($dynamicCta['cta_position_2'])));
$defaultEmptyCondition3 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_3']) || empty(array_filter($dynamicCta['cta_position_3'])));
$defaultEmptyCondition4 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_4']) || empty(array_filter($dynamicCta['cta_position_4'])));
$defaultEmptyCondition5 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_5']) || empty(array_filter($dynamicCta['cta_position_5'])));
$defaultEmptyCondition6 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_6']) || empty(array_filter($dynamicCta['cta_position_6'])));
$defaultEmptyCondition7 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_7']) || empty(array_filter($dynamicCta['cta_position_7'])));
$defaultEmptyCondition8 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_8']) || empty(array_filter($dynamicCta['cta_position_8'])));
$defaultEmptyCondition9 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_9']) || empty(array_filter($dynamicCta['cta_position_9'])));
?>

<?php if ($lead_cta == 0): ?>
    <div class="button__row__container">
        <!-- <p class="getSupport__subheading">Are You Interested In This College?</p> -->
        <?= frontend\helpers\Html::leadButton(
            $defaultEmptyCondition2 ? 'Talk To Experts' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'Talk To Experts'),
            [
                'entity' => $entity,
                'entityId' => $entity_id,
                'ctaLocation' => $isMobile ? ($defaultEmptyCondition2 ? UserService::parseDynamicCta('colleges_{slug}_wap_lead_capture_panel_left_cta3', '', $pageName) : $dynamicCta['cta_position_2']['wap']) : ($defaultEmptyCondition2 ? UserService::parseDynamicCta('colleges_{slug}_web_lead_capture_panel_left_cta3', '', $pageName) : $dynamicCta['cta_position_2']['web']),
                'ctaText' => $defaultEmptyCondition2 ? 'Talk To Experts' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'Talk To Experts'),
                'ctaTitle' => $defaultEmptyCondition2 ? '' : (!empty($dynamicCta['cta_position_2']['cta_title']) ? $dynamicCta['cta_position_2']['cta_title'] : null),
                'stateId' => $state ?? null,
                'stateName' => $stateName->name ?? null,
                'interestedLocation' => $city ?? null,
                'leadformtitle' => $defaultEmptyCondition2 ? 'REGISTER FOR EXPERT GUIDANCE' : ($dynamicCta['cta_position_2']['lead_form_title'] ?? 'REGISTER FOR EXPERT GUIDANCE'),
                'subheadingtext' => $defaultEmptyCondition2 ? $name : ($dynamicCta['cta_position_2']['lead_form_description'] ?? $name),
                'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
                'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition2 ? null : $dynamicCta['cta_position_2']['page_link'] ?? null) : null,
            ],
            ['class' => 'talkToExpert getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
        ) ?>
        <?= frontend\helpers\Html::leadButton(
            ($isSponsored == College::SPONSORED_NO) ? 'Shortlist' : ($defaultEmptyCondition3 ? 'Apply Now' : ($dynamicCta['cta_position_3']['cta_text'] ?? 'Apply Now')),
            [
                'entity' => $entity,
                'entityId' => $entity_id,
                'ctaLocation' => $isMobile ? ($defaultEmptyCondition3 ? UserService::parseDynamicCta('colleges_{slug}_wap_lead_capture_panel_right_cta4', '', $pageName) : $dynamicCta['cta_position_3']['wap']) : ($defaultEmptyCondition3 ? UserService::parseDynamicCta('colleges_{slug}_web_lead_capture_panel_right_cta4', '', $pageName) : $dynamicCta['cta_position_3']['web']),
                'ctaText' => ($isSponsored == College::SPONSORED_NO) ? 'Shortlist' : ($defaultEmptyCondition3 ? 'Apply Now' : ($dynamicCta['cta_position_3']['cta_text'] ?? 'Apply Now')),
                'ctaTitle' => $defaultEmptyCondition3 ? '' : (!empty($dynamicCta['cta_position_3']['cta_title']) ? $dynamicCta['cta_position_3']['cta_title'] : null),
                'stateId' => $state ?? null,
                'stateName' => $stateName->name ?? null,
                'interestedLocation' => $city ?? null,
                'leadformtitle' => ($isSponsored == College::SPONSORED_NO) ? 'Register to Shortlist' : ($defaultEmptyCondition3 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_3']['lead_form_title'] ?? 'REGISTER TO APPLY')),
                'subheadingtext' => $defaultEmptyCondition3 ? $name : ($dynamicCta['cta_position_3']['lead_form_description'] ?? $name),
                'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
                'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition3 ? null : $dynamicCta['cta_position_3']['page_link'] ?? null) : null,
            ],
            ['class' => 'applyNow getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
        ) ?>
    </div>
<?php endif; ?>

<?php if ($lead_cta == 20): ?>
    <div class="getSupport">
        <p class="getSupport__subheading">Are You Interested In This College?</p>
        <div class="button__row__container">
            <?= frontend\helpers\Html::leadButton(
                $defaultEmptyCondition2 ? 'Talk To Experts' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'Talk To Experts'),
                [
                    'entity' => $entity,
                    'entityId' => $entity_id,
                    'ctaLocation' => $isMobile ? ($defaultEmptyCondition2 ? UserService::parseDynamicCta('colleges_{slug}_wap_lead_capture_panel_left_cta3', '', $pageName) : $dynamicCta['cta_position_2']['wap'] ?? null) : ($defaultEmptyCondition2 ? UserService::parseDynamicCta('colleges_{slug}_web_lead_capture_panel_left_cta3', '', $pageName) : $dynamicCta['cta_position_2']['web'] ?? null),
                    'ctaText' => $defaultEmptyCondition2 ? 'Talk To Experts' : ($dynamicCta['cta_position_2']['cta_text'] ?? 'Talk To Experts'),
                    'ctaTitle' => $defaultEmptyCondition2 ? '' : (!empty($dynamicCta['cta_position_2']['cta_title']) ? $dynamicCta['cta_position_2']['cta_title'] : null),
                    'stateId' => $state ?? null,
                    'stateName' => $stateName->name ?? null,
                    'interestedLocation' => $city ?? null,
                    'leadformtitle' => $defaultEmptyCondition2 ? 'REGISTER FOR EXPERT GUIDANCE' : ($dynamicCta['cta_position_2']['lead_form_title'] ?? 'REGISTER FOR EXPERT GUIDANCE'),
                    'subheadingtext' => $defaultEmptyCondition2 ? $name : ($dynamicCta['cta_position_2']['lead_form_description'] ?? $name),
                    'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
                    'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition2 ? null : $dynamicCta['cta_position_2']['page_link'] ?? null) : null,
                ],
                ['class' => 'talkToExpert getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
            ) ?>
            <?= frontend\helpers\Html::leadButton(
                ($isSponsored == College::SPONSORED_NO) ? 'Shortlist' : ($defaultEmptyCondition3 ? 'Apply Now' : ($dynamicCta['cta_position_3']['cta_text'] ?? 'Apply Now')),
                [
                    'entity' => $entity,
                    'entityId' => $entity_id,
                    'ctaLocation' => $isMobile ? ($defaultEmptyCondition3 ? UserService::parseDynamicCta('colleges_{slug}_wap_lead_capture_panel_right_cta4', '', $pageName) : $dynamicCta['cta_position_3']['wap'] ?? null) : ($defaultEmptyCondition3 ? UserService::parseDynamicCta('colleges_{slug}_web_lead_capture_panel_right_cta4', '', $pageName) : $dynamicCta['cta_position_3']['web'] ?? null),
                    'ctaText' => ($isSponsored == College::SPONSORED_NO) ? 'Shortlist' : ($defaultEmptyCondition3 ? 'Apply Now' : ($dynamicCta['cta_position_3']['cta_text'] ?? 'Apply Now')),
                    'ctaTitle' => $defaultEmptyCondition3 ? '' : (!empty($dynamicCta['cta_position_3']['cta_title']) ? $dynamicCta['cta_position_3']['cta_title'] : null),
                    'stateId' => $state ?? null,
                    'stateName' => $stateName->name ?? null,
                    'interestedLocation' => $city ?? null,
                    'leadformtitle' => ($isSponsored == College::SPONSORED_NO) ? 'Register to Shortlist' : ($defaultEmptyCondition3 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_3']['lead_form_title'] ?? 'REGISTER TO APPLY')),
                    'subheadingtext' => $defaultEmptyCondition3 ? $name : ($dynamicCta['cta_position_3']['lead_form_description'] ?? $name),
                    'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
                    'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition3 ? null : $dynamicCta['cta_position_3']['page_link'] ?? null) : null,
                ],
                ['class' => 'applyNow getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
            ) ?>
        </div>
    </div>
<?php endif; ?>
<?php if ($lead_cta == 1): ?>
    <?= frontend\helpers\Html::leadButton(
        ($isSponsored == College::SPONSORED_NO) ? 'Shortlist <span class="spriteIcon applyRedIcon"></span>' : ($defaultEmptyCondition0 ? 'Apply Now <span class="spriteIcon applyRedIcon"></span>' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now <span class="spriteIcon applyRedIcon"></span>')),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $defaultEmptyCondition0 ? UserService::parseDynamicCta('colleges_{slug}_wap_banner_cta1', '', ($pageName)) : $dynamicCta['cta_position_0']['wap'],
            'ctaText' => ($isSponsored == College::SPONSORED_NO) ? 'Shortlist' : ($defaultEmptyCondition0 ? 'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now')),
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'leadformtitle' => ($isSponsored == College::SPONSORED_NO) ? 'Register to Shortlist' : ($defaultEmptyCondition0 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO APPLY')),
            'subheadingtext' => $defaultEmptyCondition0 ? ($name ?? '') : ($dynamicCta['cta_position_0']['lead_form_description'] ?? ($name ?? '')),
            'image' => Url::defaultCollegeLogo(),
            'durl' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'] ?? null,
        ],
        ['class' => 'primaryBtn writeReview getLeadForm mobileCollegeCta leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition1 ? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $defaultEmptyCondition1 ? UserService::parseDynamicCta('colleges_{slug}_wap_banner_cta2', '', ($pageName)) : $dynamicCta['cta_position_1']['wap'],
            'ctaText' => $defaultEmptyCondition1 ? 'Brochure' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Brochure'),
            'ctaTitle' => $defaultEmptyCondition1 ? '' : (!empty($dynamicCta['cta_position_1']['cta_title']) ? $dynamicCta['cta_position_1']['cta_title'] : null),
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'leadformtitle' => $defaultEmptyCondition1 ? 'REGISTER TO DOWNLOAD BROCHURE' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'REGISTER TO DOWNLOAD BROCHURE'),
            'subheadingtext' => $defaultEmptyCondition1 ? ($name ?? '') : ($dynamicCta['cta_position_1']['lead_form_description'] ?? ($name ?? '')),
            'image' => Url::defaultCollegeLogo(),
            'durl' => !empty($durl) ? $durl : '',
        ],
        ['class' => 'primaryBtn brochureBtn getLeadForm mobileCollegeCta leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 2): ?>
    <?= frontend\helpers\Html::leadButton(
        ($isSponsored == College::SPONSORED_NO) ? 'Shortlist <span class="spriteIcon applyRedIcon"></span>' : ($defaultEmptyCondition0 ? 'Apply Now <span class="spriteIcon applyRedIcon"></span>' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now <span class="spriteIcon applyRedIcon"></span>')),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $defaultEmptyCondition0 ? UserService::parseDynamicCta('colleges_{slug}_web_banner_cta1', '', ($pageName)) : $dynamicCta['cta_position_0']['web'],
            'ctaText' => ($isSponsored == College::SPONSORED_NO) ? 'Shortlist' : ($defaultEmptyCondition0 ? 'Apply Now' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Apply Now')),
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'leadformtitle' => ($isSponsored == College::SPONSORED_NO) ? 'Register to Shortlist' : ($defaultEmptyCondition0 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO APPLY')),
            'subheadingtext' => $defaultEmptyCondition0 ? $college->name ?? '' : ($dynamicCta['cta_position_0']['lead_form_description'] ?? $college->name ?? ''),
            'image' => Url::defaultCollegeLogo(),
            'durl' => $defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link'] ?? '',
        ],
        ['class' => 'primaryBtn writeReview newHeroButtons getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition1 ? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'ctaLocation' => $defaultEmptyCondition1 ? UserService::parseDynamicCta('colleges_{slug}_web_banner_cta2', '', ($pageName)) : $dynamicCta['cta_position_1']['web'],
            'ctaText' => $defaultEmptyCondition1 ? 'Brochure' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Brochure'),
            'ctaTitle' => $defaultEmptyCondition1 ? '' : (!empty($dynamicCta['cta_position_1']['cta_title']) ? $dynamicCta['cta_position_1']['cta_title'] : null),
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'leadformtitle' => $defaultEmptyCondition1 ? 'REGISTER TO DOWNLOAD BROCHURE' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'REGISTER TO DOWNLOAD BROCHURE'),
            'subheadingtext' => $defaultEmptyCondition1 ? $college->name ?? '' : ($dynamicCta['cta_position_1']['lead_form_description'] ?? $college->name ?? ''),
            'image' => Url::defaultCollegeLogo(),
            'durl' => !empty($durl) ? $durl : '',
        ],
        ['class' => 'primaryBtn brochureBtn newHeroButtons getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 3): ?>
    <?= frontend\helpers\Html::leadButton(
        'Get Detailed Fees',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition6 ? UserService::parseDynamicCta('colleges_info_wap_{slug}_course_card_topright_cta6', '', $programSlug) : $dynamicCta['cta_position_6']['wap']) : ($defaultEmptyCondition6 ? UserService::parseDynamicCta('colleges_info_web_{slug}_course_card_topright_cta6', '', $programSlug) : $dynamicCta['cta_position_6']['web']),
            'ctaText' => $defaultEmptyCondition6 ? 'REGISTER TO GET FEE DETAILS' : ($dynamicCta['cta_position_6']['cta_text'] ?? 'REGISTER TO GET FEE DETAILS'),
            'ctaTitle' => $defaultEmptyCondition6 ? '' : (!empty($dynamicCta['cta_position_6']['cta_title']) ? $dynamicCta['cta_position_6']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition6 ? 'REGISTER TO GET ADMISSION DETAILS' : ($dynamicCta['cta_position_6']['lead_form_title'] ?? 'REGISTER TO GET ADMISSION DETAILS'),
            'subheadingtext' => $defaultEmptyCondition6 ? ($name ?? null) : ($dynamicCta['cta_position_6']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => $defaultEmptyCondition6 ? null : $dynamicCta['cta_position_6']['page_link'] ?? null,
        ],
        ['class' => 'applyNow getLeadForm textBlue getFees leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 4): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition4 ? 'Admission ' . date('Y') . ' <span class="spriteIcon whiteInterestedIcon"></span>' : ($dynamicCta['cta_position_4']['cta_text'] ?? 'Admission ' . date('Y') . ' <span class="spriteIcon whiteInterestedIcon"></span>'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition4 ? UserService::parseDynamicCta('colleges_info_wap_{slug}_course_card_left_cta5', '', $programSlug) : $dynamicCta['cta_position_4']['wap']) : ($defaultEmptyCondition4 ? UserService::parseDynamicCta('colleges_info_web_{slug}_course_card_left_cta5', '', $programSlug) : $dynamicCta['cta_position_4']['web']),
            'ctaText' => $defaultEmptyCondition4 ? 'Admission ' . date('Y') : ($dynamicCta['cta_position_4']['cta_text'] ?? 'Admission ' . date('Y')),
            'ctaTitle' => $defaultEmptyCondition4 ? '' : (!empty($dynamicCta['cta_position_4']['cta_title']) ? $dynamicCta['cta_position_4']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition4 ? 'REGISTER TO GET ADMISSION DETAILS' : ($dynamicCta['cta_position_4']['lead_form_title'] ?? 'REGISTER TO GET ADMISSION DETAILS'),
            'subheadingtext' => $defaultEmptyCondition4 ? ($name ?? null) : ($dynamicCta['cta_position_4']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => $defaultEmptyCondition4 ? null : $dynamicCta['cta_position_4']['page_link'] ?? null,
        ],
        ['class' => 'primaryBtn getLeadForm admissionButton leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition5 ? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>' : ($dynamicCta['cta_position_5']['cta_text'] ?? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition5 ? UserService::parseDynamicCta('colleges_info_wap_{slug}_course_card_right_cta5', '', $programSlug) : $dynamicCta['cta_position_5']['wap']) : ($defaultEmptyCondition5 ? UserService::parseDynamicCta('colleges_info_web_{slug}_course_card_right_cta5', '', $programSlug) : $dynamicCta['cta_position_5']['web']),
            'ctaText' => $defaultEmptyCondition5 ? 'Brochure' : ($dynamicCta['cta_position_5']['cta_text'] ?? 'Brochure'),
            'ctaTitle' => $defaultEmptyCondition5 ? '' : (!empty($dynamicCta['cta_position_5']['cta_title']) ? $dynamicCta['cta_position_5']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition5 ? 'REGISTER TO DOWNLOAD BROCHURE' : ($dynamicCta['cta_position_5']['lead_form_title'] ?? 'REGISTER TO DOWNLOAD BROCHURE'),
            'subheadingtext' => $defaultEmptyCondition5 ? ($name ?? null) : ($dynamicCta['cta_position_5']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => !empty($durl) ? $durl : '',
        ],
        ['class' => 'primaryBtn getLeadForm infoBroucherButton leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 5): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition4 ? 'Interested <span class="spriteIcon interestedIcon"></span>' : ($dynamicCta['cta_position_4']['cta_text'] ?? 'Interested <span class="spriteIcon interestedIcon"></span>'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $defaultEmptyCondition4 ? UserService::parseDynamicCta('college_course_fees_web_lead_{slug}_card_right_cta5', '', $programSlug) : ($dynamicCta['cta_position_4']['web'] ?? null),
            'ctaText' => $defaultEmptyCondition4 ? 'Interested' : ($dynamicCta['cta_position_4']['cta_text'] ?? 'Interested'),
            'ctaTitle' => $defaultEmptyCondition4 ? '' : (!empty($dynamicCta['cta_position_4']['cta_title']) ? $dynamicCta['cta_position_4']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition4 ? 'REGISTER TO SUBMIT YOUR APPLICATION' : ($dynamicCta['cta_position_4']['lead_form_title'] ?? 'REGISTER TO SUBMIT YOUR APPLICATION'),
            'subheadingtext' => $defaultEmptyCondition4 ? ($name ?? null) : ($dynamicCta['cta_position_4']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => !empty($durl) ? $durl : '',
        ],
        ['class' => 'primaryBtn interestedButton getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition5 || empty(array_filter($dynamicCta['cta_position_5'])) ? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>' : ($dynamicCta['cta_position_5']['cta_text'] ?? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $defaultEmptyCondition5 ? UserService::parseDynamicCta('college_course_fees_web_lead_{slug}_card_right_cta6', '', $programSlug) : ($dynamicCta['cta_position_5']['web'] ?? null),
            'ctaText' => $defaultEmptyCondition5 || empty(array_filter($dynamicCta['cta_position_5'])) ? 'Brochure' : ($dynamicCta['cta_position_5']['cta_text'] ?? 'Brochure'),
            'ctaTitle' => $defaultEmptyCondition5 ? '' : (!empty($dynamicCta['cta_position_5']['cta_title']) ? $dynamicCta['cta_position_5']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition5 || empty(array_filter($dynamicCta['cta_position_5'])) ? 'REGISTER TO DOWNLOAD BROCHURE' : ($dynamicCta['cta_position_5']['lead_form_title'] ?? 'REGISTER TO DOWNLOAD BROCHURE'),
            'subheadingtext' => $defaultEmptyCondition5 || empty(array_filter($dynamicCta['cta_position_5'])) ? ($name ?? null) : ($dynamicCta['cta_position_5']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => !empty($durl) ? $durl : '',
        ],
        ['class' => 'primaryBtn brochureBtn getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 6): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition4 ? 'Interested <span class="spriteIcon interestedIcon"></span>' : ($dynamicCta['cta_position_4']['cta_text'] ?? 'Interested <span class="spriteIcon interestedIcon"></span>'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $defaultEmptyCondition4 ? UserService::parseDynamicCta('college_course_fees_wap_lead_{slug}_card_left_cta5', '', $programSlug) : ($dynamicCta['cta_position_4']['wap'] ?? null),
            'ctaText' => $defaultEmptyCondition4 ? 'Interested' : ($dynamicCta['cta_position_4']['cta_text'] ?? 'Interested'),
            'ctaTitle' => $defaultEmptyCondition4 ? '' : (!empty($dynamicCta['cta_position_4']['cta_title']) ? $dynamicCta['cta_position_4']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition4 ? 'REGISTER TO SUBMIT YOUR APPLICATION' : ($dynamicCta['cta_position_4']['lead_form_title'] ?? 'REGISTER TO SUBMIT YOUR APPLICATION'),
            'subheadingtext' => $defaultEmptyCondition4 ? ($name ?? '') : ($dynamicCta['cta_position_4']['lead_form_description'] ?? ($name ?? '')),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => !empty($durl) ? $durl : '',
        ],
        ['class' => 'primaryBtn interestedButton getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition5 ? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>' : ($dynamicCta['cta_position_5']['cta_text'] ?? 'Brochure <span class="spriteIcon whiteDownloadIcon"></span>'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $defaultEmptyCondition5 ? UserService::parseDynamicCta('college_course_fees_wap_lead_{slug}_card_right_cta6', '', $programSlug) : ($dynamicCta['cta_position_5']['wap'] ?? null),
            'ctaText' => $defaultEmptyCondition5 ? 'Brochure' : ($dynamicCta['cta_position_5']['cta_text'] ?? 'Brochure'),
            'ctaTitle' => $defaultEmptyCondition5 ? '' : (!empty($dynamicCta['cta_position_5']['cta_title']) ? $dynamicCta['cta_position_5']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition5 ? 'REGISTER TO DOWNLOAD BROCHURE' : ($dynamicCta['cta_position_5']['lead_form_title'] ?? 'REGISTER TO DOWNLOAD BROCHURE'),
            'subheadingtext' => $defaultEmptyCondition5 ? ($name ?? '') : ($dynamicCta['cta_position_5']['lead_form_description'] ?? ($name ?? '')),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => !empty($durl) ? $durl : '',
        ],
        ['class' => 'primaryBtn brochureBtn getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 7): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition8 ? 'View Eligibility Criteria' : ($dynamicCta['cta_position_8']['cta_text'] ?? 'View Eligibility Criteria'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition8 ? UserService::parseDynamicCta('college_course_fees_wap_lead_{slug}_card_cta9', '', $programSlug) : ($dynamicCta['cta_position_8']['wap'] ?? null)) : ($defaultEmptyCondition8 ? UserService::parseDynamicCta('college_course_fees_web_{slug}_card_left_cta9', '', $programSlug) : ($dynamicCta['cta_position_8']['web'] ?? null)),
            'ctaText' => $defaultEmptyCondition8 ? 'View Eligibility Criteria' : ($dynamicCta['cta_position_8']['cta_text'] ?? 'View Eligibility Criteria'),
            'ctaTitle' => $defaultEmptyCondition8 ? '' : (!empty($dynamicCta['cta_position_8']['cta_title']) ? $dynamicCta['cta_position_8']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition8 ? 'REGISTER TO CHECK ELIGIBILITY' : ($dynamicCta['cta_position_8']['lead_form_title'] ?? 'REGISTER TO CHECK ELIGIBILITY'),
            'subheadingtext' => $defaultEmptyCondition8 ? ($name ?? null) : ($dynamicCta['cta_position_8']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
        ],
        ['class' => 'applyNow getLeadForm textBlue getFees leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 8 || $lead_cta == 24): ?>
    <?= frontend\helpers\Html::leadButton(
        $lead_cta == 8 ? 'View Detailed Fees' : 'Get Detailed Fees',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition7 ? UserService::parseDynamicCta('colleges_course_fees_wap_lead_{slug}_card_cta8', '', $programSlug ?? '') : (isset($dynamicCta['cta_position_7']['wap']) ? $dynamicCta['cta_position_7']['wap'] : '')) : ($defaultEmptyCondition7 ? UserService::parseDynamicCta('colleges_course_fees_web_lead_{slug}_card_center_cta8', '', $programSlug ?? '') : (isset($dynamicCta['cta_position_7']['web']) ? $dynamicCta['cta_position_7']['web'] : '')),
            'ctaText' => $defaultEmptyCondition7 ? 'View Detailed Fee' : ($dynamicCta['cta_position_7']['cta_text'] ?? 'View Detailed Fee'),
            'ctaTitle' => $defaultEmptyCondition7 ? '' : (!empty($dynamicCta['cta_position_7']['cta_title']) ? $dynamicCta['cta_position_7']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition7 ? 'REGISTER TO GET FEE DETAILS' : ($dynamicCta['cta_position_7']['lead_form_title'] ?? 'REGISTER TO GET FEE DETAILS'),
            'subheadingtext' => $defaultEmptyCondition7 ? ($name ?? null) : ($dynamicCta['cta_position_7']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
        ],
        ['class' => 'applyNow getLeadForm textBlue getFees viewDetailedFee leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 9): ?>
    <?= frontend\helpers\Html::leadButton(
        ($isSponsored == College::SPONSORED_NO) ? 'Shortlist <span class="spriteIcon applyWhiteIcon"></span>' : ($defaultEmptyCondition6 ? 'Apply Now <span class="spriteIcon applyWhiteIcon"></span>' : ($dynamicCta['cta_position_6']['cta_text'] ?? 'Apply Now <span class="spriteIcon applyWhiteIcon"></span>')),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition6 ? UserService::parseDynamicCta('colleges_course_fees_wap_lead_{slug}_card_cta7', '', $programSlug) : $dynamicCta['cta_position_6']['wap']) : ($defaultEmptyCondition6 ? UserService::parseDynamicCta('olleges_course_fees_web_lead_{slug}_card_right_cta7', '', $programSlug) : $dynamicCta['cta_position_6']['web']),
            'ctaText' => ($isSponsored == College::SPONSORED_NO) ? 'Shortlist' : ($defaultEmptyCondition6 ? 'Apply Now' : ($dynamicCta['cta_position_6']['cta_text'] ?? 'Apply Now')),
            'ctaTitle' => $defaultEmptyCondition6 ? '' : (!empty($dynamicCta['cta_position_6']['cta_title']) ? $dynamicCta['cta_position_6']['cta_title'] : null),
            'leadformtitle' => ($isSponsored == College::SPONSORED_NO) ? 'Register to Shortlist' : ($defaultEmptyCondition6 ? 'REGISTER TO APPLY FOR THIS PROGRAM' : ($dynamicCta['cta_position_6']['lead_form_title'] ?? 'REGISTER TO APPLY FOR THIS PROGRAM')),
            'subheadingtext' => $defaultEmptyCondition6 ? ($name ?? null) : ($dynamicCta['cta_position_6']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
        ],
        ['class' => 'applyNow getLeadForm textBlue getFees leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<!-- CTA for college lisiting sponsoredu cards starts -->
<?php if ($lead_cta == 11): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition1 ? '<span class="spriteIcon redDownloadIcon"></span> Brochure' : (UserService::parseDynamicCta($dynamicCta['cta_position_1']['cta_text'], '', $name) ?? '<span class="spriteIcon redDownloadIcon"></span> Brochure'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition1 ? UserService::parseDynamicCta('colleges_listing_{slug}_wap_card_left_cta', '', $slug) : $dynamicCta['cta_position_1']['wap']) : ($defaultEmptyCondition1 ? UserService::parseDynamicCta('colleges_listing_{slug}_web_card_left_cta', '', $slug) : $dynamicCta['cta_position_1']['web']),
            'ctaText' => $defaultEmptyCondition1 ? 'Brochure' : (UserService::parseDynamicCta($dynamicCta['cta_position_1']['cta_text'], $name, '') ?? 'Brochure'),
            'ctaTitle' => $defaultEmptyCondition1 ? '' : (!empty($dynamicCta['cta_position_1']['cta_title']) ? $dynamicCta['cta_position_1']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition1 ? 'REGISTER TO DOWNLOAD BROCHURE' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'REGISTER TO DOWNLOAD BROCHURE'),
            'subheadingtext' => $defaultEmptyCondition1 ? $name : (UserService::parseDynamicCta($dynamicCta['cta_position_1']['lead_form_description'], $name, '') ?? $name),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition1 ? null : $dynamicCta['cta_position_1']['page_link']) : null,
            'course' => $course ?? '',
        ],
        [
            'class' => 'primaryBtn getLeadForm collegeFilterLeadTwo collegeFilterLead',
            'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null,
        ]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 10): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition0 ? 'Apply Now' : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['cta_text'], '', $name) ?? 'Apply Now'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition0 ? UserService::parseDynamicCta('colleges_listing_{slug}_wap_card_right_cta', '', $slug) : $dynamicCta['cta_position_0']['wap']) : ($defaultEmptyCondition0 ? UserService::parseDynamicCta('colleges_listing_{slug}_web_card_right_cta', '', $slug) : $dynamicCta['cta_position_0']['web']),
            'ctaText' => $defaultEmptyCondition0 ? 'Apply Now' : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['cta_text'], $name, '') ?? 'Apply Now'),
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO APPLY NOW' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO APPLY NOW'),
            'subheadingtext' => $defaultEmptyCondition0 ? $name : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['lead_form_description'], $name, '') ?? $name),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link']) : null,
            'course' => $course ?? '',
        ],
        [
            'class' => 'textRed getLeadForm brochureRed collegeFilterLeadOne collegeFilterLead',
            'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null,

        ]
    ) ?>
<?php endif; ?>
<!-- CTA for college lisiting sponsoredu cards ends -->

<?php if ($lead_cta == 12): ?>
    <?= frontend\helpers\Html::leadButton(
        'Get ₹1 Lakh Scholarship',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => UserService::parseDynamicCta('colleges_{slug}_wap_top_sticky_cta3', '', $slug),
            'ctaText' => 'Get 1 Lakh Scholarship',
            'leadformtitle' => 'APPLY NOW TO GET ONE LAKH SCHOLARSHIP',
            'subheadingtext' => 'Fill in your details and stand a chance to get our student scholarship',
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo()
        ],
        ['class' => 'primaryBtn getLeadForm leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 14 || $lead_cta == 16): ?>
    <?= frontend\helpers\Html::leadButton(
        $lead_cta == 14 ? 'View Detailed Fees' : 'Get Detailed Fees',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $isMobile ? 'colleges_course_information_wap_lead_card_left_cta8' : 'colleges_course_information_web_lead_card_center_cta8',
            'ctaText' => $lead_cta == 14 ? 'View Detailed Fees' : 'Get Detailed Fees',
            'leadformtitle' => $lead_cta == 14 ? 'REGISTER TO GET FEE DETAILS' : 'Register now to get Detailed Fees',
            'subheadingtext' => $name ?? '',
            'plpCourseSlug' => $programSlug,
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo()
        ],
        ['class' => 'applyNow getLeadForm textBlue getFees viewDetailedFee leadCourseCapture']
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 15): ?>
    <?= frontend\helpers\Html::leadButton(
        ($isSponsored == College::SPONSORED_NO) ? 'Shortlist <span class="spriteIcon applyWhiteIcon"></span>' : 'Apply Now <span class="spriteIcon applyWhiteIcon"></span>',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $isMobile ? 'colleges_course_information_wap_lead_card_right_cta7' : 'colleges_course_information_web_lead_card_right_cta7',
            'ctaText' => ($isSponsored == College::SPONSORED_NO) ? 'Shortlist' : 'Apply Now',
            'leadformtitle' => ($isSponsored == College::SPONSORED_NO) ? 'Register to Shortlist' : 'REGISTER TO APPLY FOR THIS PROGRAM',
            'subheadingtext' => $name ?? '',
            'plpCourseSlug' => $programSlug,
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo()
        ],
        ['class' => 'applyNow getLeadForm textBlue getFees leadCourseCapture']
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 17 || $lead_cta == 18 || $lead_cta == 19): ?>
    <?= frontend\helpers\Html::leadButton(
        CollegeHelper::$leadCtaTextLocation[$lead_cta]['cta_text'],
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city ?? null,
            'ctaLocation' => $isMobile ? CollegeHelper::$leadCtaTextLocation[$lead_cta]['ctaLocationMobile'] : CollegeHelper::$leadCtaTextLocation[$lead_cta]['ctaLocationDesktop'],
            'ctaText' => CollegeHelper::$leadCtaTextLocation[$lead_cta]['cta_text'],
            'leadformtitle' => CollegeHelper::$leadCtaTextLocation[$lead_cta]['title'],
            'subheadingtext' => $name ?? '',
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => empty($sponsorClientUrl) ? null : $sponsorClientUrl,
        ],
        ['class' => 'downloadButton getLeadForm leadCourseCapture']
    ) ?>
<?php endif; ?>

<!-- CTA for college lisiting normal cards starts -->
<?php if ($lead_cta == 21): ?>
    <?= frontend\helpers\Html::leadButton(
        'Shortlist',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition1 ? UserService::parseDynamicCta('colleges_listing_{slug}_wap_card_right_cta', '', $slug) : $dynamicCta['cta_position_1']['wap']) : ($defaultEmptyCondition1 ? UserService::parseDynamicCta('colleges_listing_{slug}_web_card_right_cta', '', $slug) : $dynamicCta['cta_position_1']['web']),
            'ctaText' => 'Shortlist',
            'leadformtitle' => 'REGISTER TO SHORTLIST',
            'subheadingtext' => $defaultEmptyCondition1 ? $name : (UserService::parseDynamicCta($dynamicCta['cta_position_1']['lead_form_description'], $name, '') ?? $name),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition1 ? null : $dynamicCta['cta_position_1']['page_link']) : null,
            'course' => $course ?? '',
        ],
        [
            'class' => 'textRed getLeadForm brochureRed collegeFilterLeadOne collegeFilterLead',
            'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null,

        ]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 23): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition2 ? '<span class="spriteIcon redDownloadIcon"></span> Brochure' : (UserService::parseDynamicCta($dynamicCta['cta_position_2']['cta_text'], '', $name) ?? '<span class="spriteIcon redDownloadIcon"></span> Brochure'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? UserService::parseDynamicCta('colleges_listing_{slug}_wap_card_left_cta', '', $slug) : UserService::parseDynamicCta('colleges_listing_{slug}_web_card_left_cta', '', $slug),
            'ctaText' => $defaultEmptyCondition2 ? 'Brochure' : (UserService::parseDynamicCta($dynamicCta['cta_position_2']['cta_text'], $name, '') ?? 'Brochure'),
            'ctaTitle' => $defaultEmptyCondition2 ? '' : (!empty($dynamicCta['cta_position_2']['cta_title']) ? $dynamicCta['cta_position_2']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition2 ? 'REGISTER TO DOWNLOAD BROCHURE' : ($dynamicCta['cta_position_2']['lead_form_title'] ?? 'REGISTER TO DOWNLOAD BROCHURE'),
            'subheadingtext' => $defaultEmptyCondition2 ? $name : (UserService::parseDynamicCta($dynamicCta['cta_position_2']['lead_form_description'], $name, '') ?? $name),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition2 ? null : $dynamicCta['cta_position_2']['page_link']) : null,
            'course' => $course ?? '',
        ],
        [
            'class' => 'primaryBtn getLeadForm collegeFilterLeadTwo collegeFilterLead',
            'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null,
        ]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 22): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition4 ? 'Get ₹1 Lakh Scholarship' : (UserService::parseDynamicCta($dynamicCta['cta_position_4']['cta_text'], '', $name)),
        [
            'entity' => 'all-colleges',
            'entityId' => 0,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition4 ? 'colleges_listing_wap_top_sticky_cta' : $dynamicCta['cta_position_4']['wap']) : ($defaultEmptyCondition4 ? 'colleges_listing_web_top_sticky_cta' : $dynamicCta['cta_position_4']['web']),
            'ctaText' => $defaultEmptyCondition4 ? 'Get 1 Lakh Scholarship' : ($dynamicCta['cta_position_4']['cta_text'] ?? 'Get 1 Lakh Scholarship'),
            'ctaTitle' => $defaultEmptyCondition4 ? '' : (!empty($dynamicCta['cta_position_4']['cta_title']) ? $dynamicCta['cta_position_4']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition4 ? 'Get 1 Lakh Scholarship' : ($dynamicCta['cta_position_4']['lead_form_title'] ?? 'Get 1 Lakh Scholarship'),
            'subheadingtext' => $defaultEmptyCondition4 ? $name : (UserService::parseDynamicCta($dynamicCta['cta_position_4']['lead_form_description'], $name, '') ?? $name),
            'redirection' => $defaultEmptyCondition4 ? null : $dynamicCta['cta_position_4']['page_link'],
            'courseSlug' => $course ?? '',
            'stream' => $stream ?? '',
        ],
        ['class' => 'applyNow primaryBtn registerNow collegeFilterLead CollegeFilterLeadScholer'],
        'js-open-lead-form-new'
    )
    ?>
<?php endif; ?>

<?php if ($lead_cta == 26): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition0 ? 'Apply Now' : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['cta_text'], '', $name) ?? 'Apply Now'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? UserService::parseDynamicCta('colleges_admission_{slug}_wap_card_right_cta', '', $slug) : UserService::parseDynamicCta('colleges_admission_{slug}_web_card_right_cta', '', $slug),
            'ctaText' => $defaultEmptyCondition0 ? 'Apply Now' : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['cta_text'], $name, '') ?? 'Apply Now'),
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO APPLY'),
            'subheadingtext' => $defaultEmptyCondition0 ? $name : (UserService::parseDynamicCta($dynamicCta['cta_position_0']['lead_form_description'], $name, '') ?? $name),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition0 ? null : $dynamicCta['cta_position_0']['page_link']) : null,
        ],
        [
            'class' => 'primaryBtn getLeadForm collegeFilterLeadTwo collegeFilterLead',
            'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null,
        ]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 25): ?>
    <?= frontend\helpers\Html::leadButton(
        $defaultEmptyCondition1 ? 'Get Fees Details' : (UserService::parseDynamicCta($dynamicCta['cta_position_1']['cta_text'], '', $name) ?? 'Get Fees Details'),
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? UserService::parseDynamicCta('colleges_admission_{slug}_wap_card_left_cta', '', $slug) : UserService::parseDynamicCta('colleges_admission_{slug}_web_card_left_cta', '', $slug),
            'ctaText' => $defaultEmptyCondition1 ? 'Get Fees Details' : (UserService::parseDynamicCta($dynamicCta['cta_position_1']['cta_text'], $name, '') ?? 'Get Fees Details'),
            'ctaTitle' => $defaultEmptyCondition1 ? '' : (!empty($dynamicCta['cta_position_1']['cta_title']) ? $dynamicCta['cta_position_1']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition1 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'REGISTER TO APPLY'),
            'subheadingtext' => $defaultEmptyCondition1 ? $name : (UserService::parseDynamicCta($dynamicCta['cta_position_1']['lead_form_description'], $name, '') ?? $name),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => empty($sponsorClientUrl) ? ($defaultEmptyCondition1 ? null : $dynamicCta['cta_position_1']['page_link']) : null,
        ],
        [
            'class' => 'secondaryBtn getLeadForm brochureRed collegeFilterLeadOne collegeFilterLead js-open-lead-form-new',
            'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null,
        ]
    ) ?>
<?php endif; ?>

<!-- Fee Structure And List Of Courses Summary Cta -->
<?php if ($lead_cta == 27): ?>
    <?= frontend\helpers\Html::leadButton(
        'Apply Now',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition9 ? UserService::parseDynamicCta('college_course_fees_web_{slug}_card_top_cta', '', $slug ?? '') : $dynamicCta['cta_position_9']['wap']) : ($defaultEmptyCondition9 ? UserService::parseDynamicCta('college_course_fees_wap_{slug}_card_top_cta', '', $slug ?? '') : $dynamicCta['cta_position_9']['web']),
            'ctaText' => $defaultEmptyCondition9 ? 'Apply Now' : ($dynamicCta['cta_position_9']['cta_text'] ?? 'Apply Now'),
            'ctaTitle' => $defaultEmptyCondition9 ? '' : (!empty($dynamicCta['cta_position_9']['cta_title']) ? $dynamicCta['cta_position_9']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition9 ? 'Register to get details' : ($dynamicCta['cta_position_9']['lead_form_title'] ?? 'Register to get details'),
            'subheadingtext' => $defaultEmptyCondition9 ? ($name ?? null) : ($dynamicCta['cta_position_9']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => $defaultEmptyCondition9 ? null : $dynamicCta['cta_position_9']['page_link'] ?? null,
        ],
        ['class' => 'primaryBtn interestedButton applyNow getLeadForm js-open-lead-form-new leadCourseCapture', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>

<?php if ($lead_cta == 28): ?>
    <?= frontend\helpers\Html::leadButton(
        'Apply Now',
        [
            'entity' => $entity,
            'entityId' => $entity_id,
            'stateId' => $state ?? null,
            'stateName' => $stateName->name ?? null,
            'interestedLocation' => $city,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition7 ? UserService::parseDynamicCta('colleges_info_wap_{slug}_card_top_cta', '', $slug ?? '') : $dynamicCta['cta_position_7']['wap']) : ($defaultEmptyCondition7 ? UserService::parseDynamicCta('colleges_info_web_{slug}_card_top_cta', '', $slug ?? '') : $dynamicCta['cta_position_7']['web']),
            'ctaText' => $defaultEmptyCondition7 ? 'Apply Now' : ($dynamicCta['cta_position_7']['cta_text'] ?? 'Apply Now'),
            'ctaTitle' => $defaultEmptyCondition7 ? '' : (!empty($dynamicCta['cta_position_7']['cta_title']) ? $dynamicCta['cta_position_7']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition7 ? 'Register to get details' : ($dynamicCta['cta_position_7']['lead_form_title'] ?? 'Register to get details'),
            'subheadingtext' => $defaultEmptyCondition7 ? ($name ?? null) : ($dynamicCta['cta_position_7']['lead_form_description'] ?? ($name ?? null)),
            'image' => !empty($image) ? Url::getCollegeLogo($image) : Url::defaultCollegeLogo(),
            'durl' => $defaultEmptyCondition7 ? null : $dynamicCta['cta_position_7']['page_link'] ?? null,
        ],
        ['class' => 'primaryBtn writeReview applyNow getLeadForm leadCourseCapture js-open-lead-form-new', 'onclick' => !empty($sponsorClientUrl) ? $sponsorClientUrl : null]
    ) ?>
<?php endif; ?>