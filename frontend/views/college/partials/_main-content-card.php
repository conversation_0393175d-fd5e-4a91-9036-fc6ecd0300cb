<?php

use common\helpers\ContentHelper;
use common\helpers\CollegeHelper;
use common\helpers\DataHelper;

$h2Text = ContentHelper::getGenerateHtml($content, '');
$content = $h2Text['content'];

?>
<div class="pageData <?= isset($removeShowMore) && !empty($removeShowMore) ? '' : 'pageInfo' ?>">
    <?php if (!isset($showAuthor)) { ?>
        <?= $this->render('_author-detail-mobile', [
            'content' => $contentAuthor,
            'author' => $author,
            'profile' => $profile,
            'recentActivity' => $recentActivity,
            'title' => $title
        ])
        ?>
    <?php } elseif (isset($showAuthor) && ($showAuthor)) { ?>
        <?= $this->render('_author-detail-mobile', [
            'content' => $contentAuthor,
            'author' => $author,
            'profile' => $profile,
            'recentActivity' => $recentActivity,
            'title' => $title
        ])
        ?>
    <?php } ?>
 
    <?php if (!empty($examContentTemplate[0])) { ?>
        <?= $this->render('../../partials/_examContentTemplate', [
            'examContentTemplate' => $examContentTemplate,
            'collegeNotificationUpdate' => $collegeNotificationUpdate,
            'isShowTitle'=>true
        ]) ?>
    <?php } elseif (!empty($recentActivity) &&  isset(($recentActivity[0])) && (!empty($recentActivity[0]))) { ?>
        <?= $this->render('../../partials/_recentActivity', [
            'recentActivity' => $recentActivity,
            'collegeNotificationUpdate' => $collegeNotificationUpdate,
            'title' => $title,
            'isShowTitle'=>false
        ]) ?>
    <?php }  ?>

    <?php /*if (isset($title)): ?>
        <h2><?= $title ?></h2>
    <?php endif;*/ ?>
    <?php if (isset($facilities) && $facilities != ''):
        $facilityData = explode(',', $facilities);
        ?>
        <div class="facilities">
            <ul>
                <?php foreach ($facilityData as $facilitie):
                    if (isset(CollegeHelper::$facilities[trim($facilitie)])):
                        ?>
                        <li><span class="spriteIcon <?= trim($facilitie) ?>"></span> <?= CollegeHelper::$facilities[trim($facilitie)] ?></li>
                    <?php endif;
                endforeach;
                ?>
            </ul>
        </div>
    <?php endif; ?>
    <?= ContentHelper::removeStyleTag(stripslashes(DataHelper::parseDomainUrlInContent($content))) ?>
</div>