<?php if (!empty($dataArr['data'])): ?>
    <div class="cutOffCardHeading">
        <?php /*if (!empty($dataArr['data']['specName'])): ?>
            <?php /*<h3><?= $dataArr['data']['specName'] ?></h3>
        <?php endif; */ ?>
        <div class="selectOptionsDiv">
            <?php /*if (!empty($dataArr['examArr'])):?>
                <select id="<?= $specArr['specId'] . '_' . $specArr['specSlug'] . '_' . $teamplate . 'Exam' ?>" onchange="cutOffExamFilter('<?= $specArr['specId'] . '_' . $specArr['specSlug'] . '_' . $teamplate ?>')">
                    <?php foreach ($dataArr['examArr'] as $k => $v): ?>
                        <option value="<?= $k ?>"><?= $v ?></option>
                    <?php endforeach;?>
                    </select>
                <?php endif; */ ?>
            <?php /*if (!empty($dataArr['examArr']) && !empty($dataArr['yearArr'])):
                    //sort($dataArr['yearArr']);
                    ?>
                    <select id="<?= $specArr['specId'] . '_' . $specArr['specSlug'] . '_' . $teamplate . 'Round' ?>" onchange="cutOffExamFilter('<?= $specArr['specId'] . '_' . $specArr['specSlug'] . '_' . $teamplate ?>')">
                    <?php foreach ($dataArr['yearArr'] as $k1 => $v1):?>
                        <option value="<?= $v1 ?>">Year <?= $v1 ?></option>
                    <?php  endforeach;?>
                    </select>
                <?php endif;*/ ?>
            <?php /*if (!empty($dataArr['examArr']) && !empty($dataArr['roundArr'])):
                    sort($dataArr['roundArr']);
                    ?>
                    <select id="<?= $specArr['specId'] . '_' . $specArr['specSlug'] . '_' . $teamplate . 'Round' ?>" onchange="cutOffExamFilter('<?= $specArr['specId'] . '_' . $specArr['specSlug'] . '_' . $teamplate ?>')">
                    <?php foreach ($dataArr['roundArr'] as $k1 => $v1):?>
                        <option value="<?= $v1 ?>">Round <?= $v1 ?></option>
                    <?php  endforeach;?>
                    </select>
                <?php endif;*/ ?>
        </div>
    </div>
    <?php $countSpecialization = !empty($dataArr['data']['courseValueArr']) ? count($dataArr['data']['courseValueArr']) : count($dataArr['data']['valueArr']); ?>
    <?php if (isset($teamplate) && $teamplate == 'opncl'): ?>
        <div class="<?= $countSpecialization > 6 ? 'cutOffTable' : '' ?> cutoffTableMargin">
            <?= $this->render('_cut-off-score-card', [
                'data' => $dataArr['data'],
                'type' => $teamplate,
                'college' => $college,
                'state' => $state,
                'specArr' => $specArr,
                'id' => $id
            ]); ?>
        </div>
    <?php endif; ?>
    <?php if (isset($teamplate) && $teamplate == 'percentile'): ?>
        <div class="<?= $countSpecialization > 6 ? 'cutOffTable' : '' ?> cutoffTableMargin">
            <?= $this->render('_cut-off-score-card', [
                'data' => $dataArr['data'],
                'type' => $teamplate,
                'college' => $college,
                'state' => $state,
                'specArr' => $specArr,
                'id' => $id
            ]); ?>
        </div>
    <?php endif; ?>
    <?php if (isset($teamplate) && $teamplate == 'score'): ?>
        <div class="<?= $countSpecialization > 6 ? 'cutOffTable' : '' ?> cutoffTableMargin">
            <?= $this->render('_cut-off-score-card', [
                'data' => $dataArr['data'],
                'type' => $teamplate,
                'college' => $college,
                'state' => $state,
                'specArr' => $specArr,
                'id' => $id
            ]); ?>
        </div>
    <?php endif; ?>
<?php endif; ?>