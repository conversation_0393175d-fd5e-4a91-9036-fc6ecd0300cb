<?php

use common\helpers\CollegeHelper;
use common\models\College;
use common\models\CollegeContent;
use common\models\Lead;
use common\models\LeadBucketTagging;
use common\services\CollegeService;
use common\services\UserService;
use frontend\helpers\Url;

$sponsorCollegesLists = array_chunk($models, 2);

?>
<?php foreach ($sponsorCollegesLists as $key => $sponsorCollegesList): ?>
    <div class="collegeSponsoredInfoCards countCards">
        <?php foreach ($sponsorCollegesList as $key => $model):
            $dynamicCta = UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGE_LISTING, 'college-listing', $model['display_name'] ?? $model['name'], $model['slug'], $model['city_name']);
            $this->params['dynamicCta'] = $dynamicCta ?? [];
            $ctaLocation1 = $isMobile ? $dynamicCta['cta_position_0']['wap'] : $dynamicCta['cta_position_0']['web'];
            $ctaLocation2 = $isMobile ? $dynamicCta['cta_position_1']['wap'] : $dynamicCta['cta_position_1']['web'];
            $collegeContentPageStatus = CollegeService::getStatus($model['college_id']);
            $streamSlug = '';
            
            if (!empty($request['course']) && $request['stream']) {
                $collegeId = $model['college_id'] ?? '';
                $courseSlug = $request['course'] ?? '';
                $streamSlug = $request['stream'] ?? '';
            } elseif (!empty($request['stream'])) {
                $collegeId = $model['college_id'] ?? '';
                $streamSlug = $request['stream'] ?? '';
            } else {
                $collegeId = $model['college_id'];
            }
            ?>
            <div class="sponsorCard">
                <div class="sponsorContainer">
                    <p class="sponsorLabel"><span>Sponsored</span></p>
                    <div class="row sponsoredRow">
                        <div class="collegeLogo">
                            <img height="56" width="56" class="lazyload" loading="lazy" src="<?= !empty($model['logo']) ? Url::getCollegeLogo($model['logo']) : Url::defaultCollegeLogo() ?>" onclick="gmu.url.goto('<?= Url::toCollege($model['slug']) ?>')" alt="">
                        </div>
                        <div class="sponsoredBanner">
                            <h3 class="collegeName"><a href="<?= Url::toCollege($model['slug']) ?>" title="<?= $model['display_name'] ?? $model['name'] ?>"><?= $model['display_name'] ?? $model['name'] ?></a></h3>
                            <p class="collegeLocation">
                                <i class="spriteIcon locationIconBlue"></i>
                                <?= $model['city_name'] ?>, <?= $model['state_name'] ?>
                            </p>
                        </div>
                    </div>
                    <div class="collegeDetails">
                        <div class="collegeRating">
                            <?php if (!empty($model['rating'])): ?>
                                <?= ($model['rating'] == 5) ? '5.0 ' : (empty($model['rating']) ? '' : $model['rating'] . ' '); ?>
                                <?= empty($model['rating']) ? CollegeHelper::getTotalStars(0) : CollegeHelper::getTotalStars($model['rating']) ?>
                                <?php if (!empty($model['rev_category_rating'])): ?>
                                    <span class="spriteIcon tooltipIcon tooltipAngle">
                                        <span class="tooltipIconText">
                                            <?php foreach ($model['rev_category_rating'] as $key => $value): ?>
                                                <div class="row">
                                                    <span class="col-md-6 col-7"><?= $key ?></span>
                                                    <span class="col-md-6 col-5"><?= $value ?></span>
                                                </div>
                                            <?php endforeach; ?>
                                        </span>
                                    </span>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if (!empty($model['review_count'])): ?>
                                <?php if (!empty($collegeContentPageStatus) && isset($collegeContentPageStatus['reviews']) && $collegeContentPageStatus['reviews'] == CollegeContent::STATUS_ACTIVE): ?>
                                    (<a href="<?= Url::toCollege($model['slug'], 'reviews') ?>"><?= $model['review_count'] ?></a>)
                                <?php else: ?>
                                    <?= $model['review_count'] ?>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <div class="row collegeKeyInfo">
                            <div class="keyInfo">
                                <p>Average Fees</p>
                                <p><?php if (isset($model['courseCount']) && $model['courseCount']!=0) {
                                    if (isset($model['avgFees'])) {
                                        echo '₹' . CollegeHelper::feesFormat($model['avgFees']);
                                    } else {
                                        echo '--';
                                    } ;
                                   } else {
                                       echo '--';
                                   } ?>
                                        </p>
                            </div>
                            <div class="keyInfo">
                            <?php if (!empty($model['course']) &&  isset($model['courseCount']) && $model['courseCount']!=0): ?>
                                    <p>No. of Courses Offered</p>
                                    <?php if (!empty($collegeContentPageStatus) && isset($collegeContentPageStatus['courses-fees']) && $collegeContentPageStatus['courses-fees'] == CollegeContent::STATUS_ACTIVE): ?>
                                        <p> <a title="<?= ($model['display_name'] ?? $model['name']) . ' Courses & Fee Details' ?>" href=" <?= Url::toCollege($model['slug'], 'courses-fees') ?>">
                                                <?= isset($model['courseCount']) ? $model['courseCount'] : count($model['course']) ?> Courses
                                            </a>
                                        </p>
                                    <?php else: ?>
                                        <p title="<?= ($model['display_name'] ?? $model['name']) . ' Courses & Fee Details' ?>"><?= isset($model['courseCount']) ? $model['courseCount'] : count($model['course']) ?> Courses</p>
                                    <?php endif; ?>
                            <?php else: ?>
                                    <p>No. of Courses Offered</p>
                                    <p>--</p>
                            <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="clgInfoCardfooter">
                    <div class="lead-cta-college-filter-1 leadFilterData" data-stateId="<?= $model['state_id'] ?>" data-cityId="<?= $model['city_id'] ?>" 
                    data-entity="college" data-filter="college-listing" data-lead_cta="10" data-slug="<?= $model['college_id'] ?>" 
                    data-course="<?= $courseSlug ?? '' ?>" data-ctaLocation="<?= $ctaLocation1 ?>" 
                    data-title="<?= $dynamicCta['cta_position_0']['lead_form_title'] ?>" data-stream="<?= $streamSlug ?>" data-description="<?= $dynamicCta['cta_position_0']['lead_form_description'] ?>" 
                    data-image="<?= Url::getCollegeLogo($model['logo']) ?>" data-sponsored="1"></div>
                    <div class="lead-cta-college-filter-2 leadFilterData" data-entity="college" data-stream="<?= $streamSlug ?>" 
                    data-lead_cta="11" data-filter="college-listing" data-slug="<?= $model['college_id'] ?>" data-stateId="<?= $model['state_id'] ?>" 
                    data-cityId="<?= $model['city_id'] ?>" data-course="<?= $courseSlug ?? '' ?>" data-ctaLocation="<?= $ctaLocation2 ?>" 
                    data-title="<?= $dynamicCta['cta_position_1']['lead_form_title'] ?>" data-description="<?= $dynamicCta['cta_position_1']['lead_form_description'] ?>" 
                    data-image="<?= Url::getCollegeLogo($model['logo']) ?>"></div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endforeach; ?>