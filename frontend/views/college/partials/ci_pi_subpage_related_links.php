<?php if (!empty($getCiSubpage)): ?>
    <div class="pageData">
        <h2><?= $college_name . ' ' . $page_name . ' Related Pages' ?></h2>
        <ul>
            <?php foreach ($getCiSubpage as $subpageData): ?>
                <?php
                $subpageName = $subpageData['subpage'];
                $year = $subpageData['year'];

                // Format subpage name
                $formattedName = ($subpageName === 'cut-off') ? 'Cut Off' : ucwords(str_replace('-', ' ', $subpageName));

                // Append year only for "cut-off" if year exists
                $yearSuffix = !empty($year) ? "-$year" : '';
                ?>
                <li>
                    <a href="/college/<?= $college_slug ?>-courses-fees/<?= $page_slug ?>/<?= $page ?>-<?= $subpageName . $yearSuffix ?>-<?= $page_id ?>"
                        class="">
                        <?= $college_name . ' ' . $page_name . ' ' . $formattedName . ' ' . $year ?>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>