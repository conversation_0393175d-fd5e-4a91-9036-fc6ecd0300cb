<?php

use yii\helpers\Html;

/** @var array $cutOffData */
/** @var string $type */
/** @var string $latestValue */
/** @var string $programName */
/** @var string $courseName */
/** @var string $collegeName */

$typeLabel = [
    'closing_rank' => 'Rank',
    'percentile' => 'Percentile',
    'score' => 'Score',
];

$label = $typeLabel[$type] ?? ucfirst($type);
?>

<h3 style="text-align:left;">
    Category - <?= $category ? Html::encode($category) : 'All India/ General' ?>
    <br>
    Program Name - <?= Html::encode($programName) ?>
    <br>
    See a comparison of <?= Html::encode($courseName) ?> program, offered by <?= Html::encode($collegeName) ?> cutoff 2025 and previous year against other colleges.
</h3>

<table border="1" cellpadding="5" cellspacing="0">
    <thead>
        <tr>
            <th>College Name</th>
            <th>Latest Round</th>
            <th>Latest Round-1</th>
            <th>Latest Round-2</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($cutOffData as $collegeName => $data): ?>
            <tr>
                <td><?= Html::encode($collegeName) ?></td>
                <?php
                // Sort rounds by closing value DESC
                $sortedRounds = $data['rounds'];
                uasort($sortedRounds, function ($a, $b) {
                    return ($b['closing'] ?? -INF) <=> ($a['closing'] ?? -INF);
                });
                $sortedRounds = array_slice($sortedRounds, 0, 3, true);
                $i = 0;
                foreach ($sortedRounds as $round => $values):
                    $opening = $values['opening'] ?? null;
                    $closing = $values['closing'] ?? null;
                    ?>
                    <td><?= ($opening && $closing) ? "{$opening} - {$closing}" : (($closing) ? "Closing: {$closing}" : (($opening) ? "Opening: {$opening}" : '--')) ?></td>
                    <?php $i++;
                endforeach; ?>
                <?php for (; $i < 3; $i++): ?>
                    <td>--</td>
                <?php endfor; ?>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>

<?php if (!empty($otherPrograms)): ?>
    <h3 style="margin-top:30px;text-align:left;">
        Looking for more, here is the list of all <?= Html::encode($courseName) ?> Programs offered by <?= Html::encode($collegeName) ?> and their cutoffs
    </h3>

    <table border="1" cellpadding="5" cellspacing="0">
        <thead>
            <tr>
                <th>Program Name</th>
                <th>Latest Round</th>
                <th>Latest Round-1</th>
                <th>Latest Round-2</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($otherPrograms as $programName => $rounds): ?>
                <tr>
                    <td><?= Html::encode($programName) ?></td>
                    <?php
                    krsort($rounds); // Descending
                    $rounds = array_slice($rounds, 0, 3, true);
                    $i = 0;
                    foreach ($rounds as $round => $values):
                        $opening = $values['opening'];
                        $closing = $values['closing'];
                        ?>
                        <td><?= ($opening && $closing) ? "{$opening} - {$closing}" : (($closing) ? "Closing: {$closing}" : (($opening) ? "Opening: {$opening}" : '--')) ?>
                        </td>
                        <?php $i++;
                    endforeach; ?>
                    <?php for (; $i < 3; $i++): ?>
                        <td>--</td>
                    <?php endfor; ?>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
<?php endif; ?>