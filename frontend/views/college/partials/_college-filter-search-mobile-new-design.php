<?php

use common\services\UserService;
use common\models\Lead;
use frontend\helpers\Lead as HelpersLead;
use frontend\models\CollegeSearch;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Inflector;
use yii\widgets\ActiveForm;
use frontend\helpers\Url;
use common\models\LeadBucketTagging;

$appliedFilterIndexing = ArrayHelper::index($selectedFilters, 'filterGroup_name');
$dynamicCtaFilter = UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGE_LISTING, 'college-listing', $model['display_name'] ?? $model['name'], $model['slug'], $model['city_name']);
?>

<section id="collegeFilterDSection" class="qwwde">
  
    <div class="mobileFilterSection">
        <p id="clearAllClg" class="mobileFilterHeading">FILTERS <span>RESET</span></p>
        <div class="filterTab">
            <ul class="tabs">
                <?php foreach ($filters as $key => $values):
                    foreach ($values as $k => $val):
                        if (key_exists($key, $appliedFilterIndexing)): ?>
                            <?php $appliedFilter = true; ?>
                        <?php else: ?>
                            <?php $appliedFilter = false; ?>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    <li class="tab-link <?= ($appliedFilter) ? 'appliedFilter' : '' ?>" data-tab="<?= Inflector::slug($key) ?>"><?= $key ?></li>
                <?php endforeach; ?>
            </ul>

            <div class="filterContentDiv">
                <?php $form = ActiveForm::begin([
                    'id' => 'college-filter-form'
                ]); ?>

                <?php foreach ($filters as $key => $items): ?>
                    <?php if (isset(CollegeSearch::$propertyMapping[$key])): ?>
                        <div id="<?= Inflector::slug($key) ?>" class="tab-content current">
                            <div class="filterSearch">
                                <input type="text" placeholder="Search options">
                                <?= $form->field($model, CollegeSearch::$propertyMapping[$key])->checkboxList($items, [
                                    'tag' => 'ul',
                                    'item' => function ($index, $label, $name, $checked, $value) {
                                        $html = Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                                        $html .= $label;
                                        return Html::tag('li', $html);
                                    }
                                ])->label(false) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
                <?php ActiveForm::end(); ?>
            </div>
        </div>

        <div class="filterOptionDiv">
            <button class="closeFilter"> CLOSE</button>
            <button class="applyFilter"> APPLY FILTER</button>
        </div>
    </div>
</section>