<?php

use common\models\Lead;
use common\services\UserService;
use frontend\helpers\Url;

$exludeCollege = [1093, 6182, 1092, 1091, 1090, 1094, 5115, 1088, 1089, 4499, 12841, 5184, 4497, 4496, 7597, 4498, 10972];
$defaultEmptyCondition1 = empty($dynamicCta) && empty($dynamicCta['cta_position_2']) || empty(array_filter($dynamicCta['cta_position_2']));
foreach ($models as $key => $data):
    ?>
    <?php if (!empty($data['specArr'])):
        asort($data['specArr']);
        ?>

        <?php foreach ($data['specArr'] as $ke => $va):
            if (!empty($va['opclArr']) || !empty($va['scoreArr']) || !empty($va['percentileArr'])):
                ?>

                <div class="pageData cutOffCard">
                    <h2 class="cutOffMainHeading">
                        <?php //if (!in_array($college->id, $exludeCollege)):
                        $round = isset($va['roundName']) && !empty($va['roundName']) ? $va['roundName'] : ''; ?>
                            <?= isset($va['seoInfo']) && !empty($va['seoInfo']) ? $va['seoInfo']['h1']
                                : $college->display_name . ' ' . $va['courseShortName'] . ' ' . $va['examName'] . ' ' . $round . ' Cutoff' ?>
                        <?php //endif ?>
                    </h2>
                    <p style="text-align: left;">
                        <?= isset($va['seoInfo']) && !empty($va['seoInfo']) ? $va['seoInfo']['description'] : '' ?>
                    </p>

                    <div class="cutoffMatserType">
                        <?php if (!empty($va['opclArr'])): ?>
                            <?php /*<input type="hidden" value=<?= $va['specId']?> id="<?= $va['specId'] . '_' . $va['specSlug'] . '_opncl_specDiv'?>">*/ ?>
                            <?= $this->render(
                                '_cut-off-card',
                                [
                                    'dataArr' => $va['opclArr'],
                                    'specArr' => $data['specArr'][$ke],
                                    'college' => $college,
                                    'state' => $state,
                                    'teamplate' => 'opncl',
                                    'id' => 'opclArr',
                                ]
                            );
                            ?>
                        <?php endif;
                        if (!empty($va['percentileArr'])): ?>
                            <?php /*<input type="hidden" value=<?= $va['specId']?> id="<?= $va['specId'] . '_' . $va['specSlug'] . '_percentile_specDiv'?>">*/ ?>
                            <?= $this->render('_cut-off-card', [
                                'dataArr' => $va['percentileArr'],
                                'specArr' => $data['specArr'][$ke],
                                'college' => $college,
                                'state' => $state,
                                'teamplate' => 'percentile',
                                'id' => 'percentileArr',
                            ]);
                            ?>
                        <?php endif;
                        if (!empty($va['scoreArr'])): ?>
                            <?php /*<input type="hidden" value=<?= $va['specId']?> id="<?= $va['specId'] . '_' . $va['specSlug'] . '_score_specDiv'?>">*/ ?>
                            <?= $this->render('_cut-off-card', [
                                'dataArr' => $va['scoreArr'],
                                'specArr' => $data['specArr'][$ke],
                                'college' => $college,
                                'state' => $state,
                                'teamplate' => 'score',
                                'id' => 'scoreArr',
                            ]);
                            ?>
                        <?php endif; ?>

                        <?php if (isset($text) && !empty($text)): ?>
                            <a href="<?= $collegeurl ?>" class="primaryBtn applyNow getLeadForm textBlue getFees" style = "color: #3d8ff2;background:none;" target="_blank"><?= $text ?></a>
                        <?php else: ?>
                            <?= frontend\helpers\Html::leadButton(
                                ' Download Detailed Report <span class="spriteIcon whiteDownloadIcon"></span>',
                                [
                                    'entity' => Lead::ENTITY_COLLEGE,
                                    'entityId' => $college->id,
                                    'stateId' => $state->id,
                                    'interestedLocation' => $college->city_id,
                                    // 'ct aLocation' => HelpersLead::getCTAsName(Lead::ENTITY_COLLEGE . '.' . ($va['courseSlug'] ?? ''), 'college_course_fees_right_cta5'),
                                    'ctaLocation' => isset($articleCtaLocation) ? $cta_location1 : ($isMobile ? ($defaultEmptyCondition1 ? UserService::parseDynamicCta('colleges_{slug}_wap_lead_capture_panel_left_cta3', '', 'cut-off' ?? $va['courseSlug']) : $dynamicCta['cta_position_2']['wap']) : ($defaultEmptyCondition1 ? UserService::parseDynamicCta('colleges_{slug}_web_lead_capture_panel_left_cta3', '', 'cut-off' ?? $va['courseSlug']) : $dynamicCta['cta_position_2']['web'])),
                                    'ctaText' => 'Download Detailed Report',
                                    'leadformtitle' => 'Register to Download Detailed Cutoff',
                                    'subheadingtext' => $college->name,
                                    'image' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(),
                                ],
                                ['class' => 'primaryBtn getLeadForm ' . $va['courseId'] . '_' . $va['courseSlug']]
                            ) ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
        <!-- </div> -->
        <!-- </div> -->
    <?php endif;
endforeach;
