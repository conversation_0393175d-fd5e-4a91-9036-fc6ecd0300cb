<?php

use common\helpers\ContentHelper;
use common\helpers\CollegeHelper;
use common\helpers\DataHelper;

?>
<div class="pageData pageInfo"?>
    
<?php /*<h1><?=$college->display_name;?> Ranking in India</h1>*/?>
         <p><?=$college->display_name;?> ranking is out now. The institute has been ranked under various
            categories/streams by ranking organizations including <?= implode(',', array_keys($collegeRank)) ?>, etc. Category or
            stream-wise latest and previous year rankings of <?=$college->display_name;?> are given in the article below.
         </p>
         <?php foreach ($collegeRank as $keyRank => $col) { ?>
           <h2> <?=$college->display_name;?> <?= $keyRank ?> Ranking </h2>
           <p>
                <?=$college->display_name;?> <?= $keyRank ?> ranking is out now. Latest and previous year  
                <?= $keyRank ?> rankings of the <?=$college->display_name;?> are given in the table below.
        </p>
        <div class="table-responsive">
            <table class="courseFeesTable">
            <thead>
                <tr>
                    <td>Stream/Category</td>
                    <?php $maxTd =0;
                    $yearIndex = [];
                    foreach ($col as $yearRankNumber => $yearRankValue) {
                        $yearIndex[] = $yearRankNumber ;   ?>
                    <td><?= $keyRank ?> Rank <?=  $yearRankNumber; ?></td>
                                        <?php $maxTd++;
                    } ?>
                </tr>
            </thead>
            <tbody>
                <?php
                foreach ($collegeRankCriteria[$keyRank] as $yearData => $yearRank) {    ?>                
                 <tr>
                   <td><?= $yearData ?> </td> 
                    <?php
                    foreach ($yearIndex as $yearValue) {
                        if (!empty($yearRank[$yearValue])) {
                            ?>
                     <td><?= $yearRank[$yearValue]['rank'] ?></td>
                            <?php
                        } else {?>
                            <td></td>
                        <?php }
                    } ?>
             </tr>
                <?php }?>
            </tbody>
        </table>
    </div>
         <?php } ?>
</div>