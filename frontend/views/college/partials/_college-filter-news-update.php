<?php

use frontend\helpers\Url;
use common\helpers\DataHelper;
use common\helpers\ArticleDataHelper;
use yii\helpers\Inflector;

?>
<h2>News & Article</h2>
<ul class="latest__news__tabs">
    <li data-tab="latestNews" class="current__tab">Latest News</li>
    <li data-tab="recentNews">Latest Article</li>
</ul>
<div id="latestNews" class="current__tab recent__latest__section">
    <?php foreach ($latestNews as $latest): ?>
        <div class="news__card">
            <img class="news__card__image lazyload" onclick="gmu.url.goto('<?= Url::toNewsDetail($latest['slug'], DataHelper::getLangCode($latest['lang_code'])) ?>')" loading="lazy" data-src="<?= isset($latest['banner_image']) ? Url::toNewsImages($latest['banner_image']) : Url::toNewsImages(); ?>" alt="<?= $latest['title'] ?? '' ?>">


            <div class="news__text">
                <a class="latest__title" href="<?= Url::toNewsDetail($latest['slug'], DataHelper::getLangCode($latest['lang_code'])) ?>" title="<?= $latest['title'] ?? '' ?>"> <?= $latest['title'] ?? '' ?></a>

                </a>
                <?php $athorslug = Inflector::slug($latest['author']) ?>
                <p><a href="<?= Url::toAllAuthorPost($athorslug, DataHelper::getLangCode($latest['lang_code'])) ?>"><?= $latest['author'] ?? ''  ?></a> I <?= Yii::$app->formatter->asDate($latest['updated_at']) ?></p>
            </div>
        </div>
    <?php endforeach; ?>

</div>
<div id="recentNews" class="recent__latest__section">
    <?php foreach ($latestArticles as $article) {  ?>
        <div class="news__card">
            <?php if ($article->country_slug == null): ?>
                <img class="news__card__image" loading="lazy" onclick="gmu.url.goto('<?= Url::toArticleDetail($article->slug, DataHelper::getLangCode($article->lang_code)) ?>')" data-src="<?= $article->cover_image ? ArticleDataHelper::getImage($article->cover_image) : ArticleDataHelper::getImage(); ?>" src="<?= $article->cover_image ? ArticleDataHelper::getImage($article->cover_image) : ArticleDataHelper::getImage(); ?>" alt="<?= $article->title ?? '' ?>" />
            <?php else: ?>
                <img class="news__card__image" loading="lazy" onclick="gmu.url.goto('<?= Url::toCountryDetail($article->country_slug, $article->slug) ?>')" data-src="<?= $article->cover_image ? Url::getStudyAbroadImage($article->cover_image) : Url::getStudyAbroadImage() ?>" src="<?= $article->cover_image ? Url::getStudyAbroadImage($article->cover_image) : Url::getStudyAbroadImage() ?>" alt="<?= $article->title ?? '' ?>" />
            <?php endif; ?>
            <div class="news__text">
                <a class="latest__title" href="<?= empty($article->country_slug) ? Url::toArticleDetail($article->slug, DataHelper::getLangCode($article->lang_code)) : Url::toCountryDetail($article['country_slug'], $article['slug']) ?>" title="<?= $article->title ?>"> <?= $article->title ?? '' ?></a>
                <p><a href= "<?= Url::toAllAuthorPost($article->author->slug, DataHelper::getLangCode($latest['lang_code'])) ?>"><?= $article->author->name ?? '' ?></a> I <?= Yii::$app->formatter->asDate($article->updated_at ?? 'today') ?></p>
            </div>
        </div>
    <?php } ?>

    <!--div class="load__more__row">
         <div class="load__more__button">
            Load More <span class="spriteIcon__2 red__angle__icon"></span>
          </div>
   </div-->
</div>