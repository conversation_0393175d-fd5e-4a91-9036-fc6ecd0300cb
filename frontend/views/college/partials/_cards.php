<?php

use frontend\helpers\Url;
use common\helpers\DataHelper;

$isMobile = \Yii::$app->devicedetect->isMobile();
$cardCount = 0;
?>
<section class="examInfoSlider" style="margin-top:20px;">

    <h2 class="row"> <?= yii::t('app', $title) ?>
        <?php if (isset($viewAllUrl, $viewAllTitle)): ?>
            <a href="<?= $viewAllUrl ?>" class="viewAll" title="<?= $viewAllTitle ?>"> <?= yii::t('app', 'VIEW ALL') ?></a>
        <?php endif; ?>
    </h2>


    <div class="examSliderCardsCtn">
        <?php if (count($details) > 3): ?>
            <button class="spriteIcon scrollLeft over"></button>
            <button class="spriteIcon scrollRight"></button>
        <?php endif; ?>

        <div class="row examSliderCards">

            <?php foreach ($details as $detail): ?>
                <?php
                // skip iteration when match current exam
                if ($detail['slug'] == $examSlug) {
                    continue;
                }

                // breaks when totalCard count match
                if ($cardCount == $totalCards) {
                    break;
                }

                $cardCount++;
                ?>

                <?php if (!empty($detail['dates'])) {
                    foreach ($detail['dates'] as $value) {
                        if ($value['slug'] == 'exam-start') {
                            $year = explode('-', $value['start']);
                        }
                    }
                } ?>
                <div class="col-md-4 examSliders">
                    <div class="examCategoryInfo">
                        <div class="row">
                            <img width="64" height="64" onclick="gmu.url.goto('<?= Url::toExamDetail($detail['slug'], DataHelper::getLangCode($detail['lang_code'])) ?>')" src="<?= $detail['cover_image'] ? $assetUrl . $detail['cover_image'] : 'https://media.getmyuni.com/yas/images/defaultcardbanner.png' ?>" alt="<?= $detail['display_name'] ?>" class="clgLogo lazyload" loading="lazy">
                            <div>
                                <p><a title="<?= $detail['display_name'] ?>" href="<?= Url::toExamDetail($detail['slug'], DataHelper::getLangCode($detail['lang_code'])) ?>"><?= $detail['display_name'] . ' ' . (!empty($year[0]) ? $year[0] : date('Y')) ?></a></p>
                                <?php if (!empty($detail['dates'])): ?>
                                    <?php foreach ($detail['dates'] as $date): ?>
                                        <?php if ($date['slug'] == 'exam-start'): ?>
                                            <p><?= yii::t('app', 'Exam Date') ?>: <span><?= Yii::$app->formatter->asDate($date['start']) ?? 'N/A' ?></span></p>
                                        <?php endif; ?>

                                        <?php if ($date['slug'] == 'result-date'): ?>
                                            <p><?= yii::t('app', 'Result Date') ?>: <span><?= Yii::$app->formatter->asDate($date['start']) ?? 'N/A' ?></span></p>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if (isset($detail['pages'])): ?>
                            <div class="examCriteria">
                                <ul class="examCardBtn">
                                    <?php $cardPageCount = 0; ?>
                                    <?php foreach ($detail['pages'] as $page): ?>
                                        <?php
                                        if ($cardPageCount == $pageSlugCount) {
                                            break;
                                        }
                                        ?>
                                        <?php if (isset($pages)): ?>
                                            <?php if (in_array($page['slug'], $pages)): ?>
                                                <?php
                                                $titleDate = isset($date['start']) ? date('Y', strtotime($date['start'])) : '';
                                                $examTitle = ($page['slug'] == 'overview') ? $detail['display_name'] : $detail['display_name'] . ' ' . $page['name'];
                                                ?>
                                                <li><a href="<?= ($page['slug'] == 'overview') ? Url::toExamDetail($detail['slug']) : Url::toExamDetail($detail['slug'], DataHelper::getLangCode($detail['lang_code']), $page['slug']) ?>" title="<?= $examTitle; ?>"><?= $page['name'] ?></a></li>
                                                <?php $cardPageCount++; ?>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <li><a href="<?= Url::toExamDetail($detail['slug'], DataHelper::getLangCode($detail['lang_code']), $page['slug']) ?>" title="<?= $page['name'] ?>"><?= $page['name'] ?></a></li>
                                            <?php $cardPageCount++; ?>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
            <?php if (isset($viewAllUrl, $viewAllTitle) && $isMobile): ?>
                <div class="col-md-4 mobileOnly">
                    <div class="examCategoryInfo">
                        <div class="viewAllDiv">
                            <a href="<?= $viewAllUrl ?>"><i class="spriteIcon viewAllIcon" title="<?= $viewAllTitle ?>"></i><?= yii::t('app', 'VIEW ALL') ?></a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>