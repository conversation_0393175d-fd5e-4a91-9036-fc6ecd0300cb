<?php

use common\helpers\CollegeHelper;
use common\models\Lead;
use common\services\CollegeService;
use frontend\helpers\Url;

$category = array_flip(CollegeService::getCategory());
$gender = array_flip(CollegeHelper::$cutoffGender);
$typeData = [
    'opncl' => 'Closing Rank', // closing_rank
    'percentile' => 'Percentile', // percentile
    'score' => 'Closing Score' // closing_score
];
$years = array_slice(array_unique($data['yearArr']), 0, 3);
$typeValue = isset($typeData[$type]) ? $typeData[$type] : $type;

?>
<table>
    <thead>
        <tr>
            <td><?= !empty($data['Cname']) ? $data['Cname'] : 'Category' ?></td>
            <?php foreach ($years as $year):
                 $round = isset($specArr['roundName']) && !empty($specArr['roundName']) ? $specArr['roundName'] : '';
                ?>
                <td><?= ' Round ' . $round . ' ' . '(' . $typeValue . ')' ?></td>
            <?php endforeach; ?>
        </tr>
    </thead>
    <tbody>
        <?php foreach (['valueArr' => 'valueArr_programIdMap', 'courseValueArr' => 'courseValueArr_programIdMap'] as $arrKey => $programKey): ?>
            <?php if (!empty($data[$arrKey])): ?>
                <?php foreach ($data[$arrKey] as $key => $val):
                    $typeMap = [
                        'opncl' => 'closing_rank',
                        'percentile' => 'percentile',
                        'score' => 'closing_score'
                    ];
                    $programId = isset($specArr[$id]['data'][$programKey]) && !empty($specArr[$id]['data'][$programKey][$key]) ? $specArr[$id]['data'][$programKey][$key] : '';

                    $getProgramUrl = CollegeHelper::getProgramSlug($programId, $college);
                    $programUrl = $getProgramUrl['programUrl'];
                    $checkUrl = $getProgramUrl['checkUrl'];

                    $typeValue = isset($typeMap[$type]) ? $typeMap[$type] : $type;
                    ?>
                    <?php
                    // Prepare value for button (e.g., latest available year from $years)
                    $latestYear = null;
                    $latestValue = '--';
                    foreach ($years as $yr) {
                        if (isset($val[$yr])) {
                            $latestYear = $yr;
                            $latestValue = is_array($val[$yr]) ? max($val[$yr]) : $val[$yr];
                            if ($type == 'percentile') {
                                $latestValue .= '%';
                            }
                            break; // take the first match (latest available year)
                        }
                    }
                    ?>
                    <tr>
                        <td class="cutoffTableMarginTd">
                            <div class="course-fee-wrapper">
                                <div class="course-title">
                                    <?php if (!empty($programUrl)): ?>
                                        <a href="<?= $programUrl ?>" title="<?= $college->display_name ?? $college->name ?> <?= $key ?>">
                                            <div class="course-name"><?= $key ?></div>
                                        </a>
                                    <?php else: ?>
                                        <div class="course-name"><?= $key ?></div>
                                    <?php endif; ?>
                                    <?php if (!empty($specArr[$id]['data'][$programKey][$key])): ?>
                                        <?= frontend\helpers\Html::leadButton(
                                            'Get Cutoff Comparison',
                                            [
                                                'entity' => Lead::ENTITY_COLLEGE,
                                                'entityId' => $college->id,
                                                'stateId' => $state->id,
                                                'round' => $specArr['roundName'] ?? '',
                                                'latestValue' => $latestValue,
                                                'type' => $typeValue,
                                                'programId' => $specArr[$id]['data'][$programKey][$key],
                                                'programName' => $key,
                                                'courseName' => $data['Cname'],
                                                'interestedLocation' => $college->city_id,
                                                'ctaLocation' => 'cut_off_table_' . strtolower(str_replace(' ', '_', $key)) . '_program_card_comparison_cta',
                                                'ctaText' => 'Get Cutoff Comparison',
                                                'leadformtitle' => 'Register and Download Cutoff Comparison',
                                                'subheadingtext' => $college->name,
                                                'clickButton' => 'getComparisionCutoff',
                                                'image' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(),
                                            ],
                                            ['class' => 'applyNow getLeadForm textBlue getFees leadCourseCapture js-open-lead-form-new getComparisionCutoff']
                                        ) ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>

                        <?php foreach ($val as $k => $v): ?>
                            <?php if (in_array($k, $years)): ?>
                                <td>
                                    <?php
                                    $value = is_array($v) ? max($v) : $v;
                                    echo $type == 'percentile' ? $value . '%' : $value;
                                    ?>
                                </td>
                            <?php endif; ?>
                        <?php endforeach; ?>

                        <?php $diff = count($years) - count($val); ?>
                        <?php if ($diff > 0): ?>
                            <?php for ($i = 0; $i < $diff; $i++): ?>
                                <td>--</td>
                            <?php endfor; ?>
                        <?php endif; ?>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        <?php endforeach; ?>
    </tbody>
</table>