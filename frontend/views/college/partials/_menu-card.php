<?php

use common\helpers\CollegeHelper;
use common\models\Lead;
use frontend\helpers\Lead as HelpersLead;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
$currentUrl = Url::base(true);
//will move
$arr = [
    'qna' => Url::toDomain() . 'college/' . $college->slug . '/questions',
    'college-under-' . $college->slug => Url::toDomain() . 'college/colleges-under-' . $college->slug,
    'compare-college' => Url::toDomain() . 'college-compare?college1=' . $college->slug,
];
?>
<nav class="stickyNavCls">
    <div class="collegeRelataedLinks">
        <?php if (count($menus) > 14 && !$isMobile): ?>
            <p class="btn_left over">
                <i class="spriteIcon left_angle"></i>
            </p>
        <?php endif; ?>
        <?php if (count($menus) > 14 && !$isMobile): ?>
            <p class="btn_right">
                <i class="spriteIcon right_angle"></i>
            </p>
        <?php endif; ?>
        <?php if (count($menus) > 3 && $isMobile): ?>
            <!-- <p class="btn_right">
                <i class="spriteIcon right_angle"></i>
            </p> -->
        <?php endif; ?>
        <ul>
            <?php foreach ($menus as $key => $value):
                $dataAttribute = str_replace(' ', '-', $value);
                ?>
                <?php if (is_string($value)):
                    $title = CollegeHelper::subPageTitle($college, $key);
                    ?>
                    <?php if ($key == $pageName): ?>
                        <li class="subNavDropDown mobileSubNavDropDown"><a <?php
                        if (array_key_exists(strtolower(str_replace(' ', '-', $value)), $dropdown)) {
                            foreach ($dropdown[strtolower(str_replace(' ', '-', $value))] as $subValue) {
                                if ($subValue['sub_page'] == $type) {
                                    ?> href="<?= $key == 'info' ? Url::toCollege($college->slug, '') : Url::toCollege($college->slug, $key) ?>" <?php
                                }
                            }
                        } ?> class="activeLink" id="activeLinkScroll" title="<?= $title ?>"><?= $value ?></a>
                            <?php if (array_key_exists(strtolower(str_replace(' ', '-', $value)), $dropdown)) { ?>
                                <span class="spriteIcon caret" data-list="<?= $dataAttribute ?>"></span>
                                <ul class="subNavDropDownMenu desktopOnly">
                                    <?php foreach ($dropdown[strtolower(str_replace(' ', '-', $value))] as $dropValue) {
                                        $slug = $dropValue['slug'] ?? null;
                                        $titleSub = CollegeHelper::subPageTitle($college, $key, $dropValue['sub_page']);
                                        ?>
                                        <li><a class="<?= $dropValue['sub_page'] == $type ? 'subNavActive' : '' ?>" href="<?= Url::toCollegeDetailSubPage($college->slug, $key, $dropValue['sub_page'], $slug) ?>" title="<?= $titleSub ?>"><?= $dropValue['sub_page'] ?></a></li>
                                    <?php } ?>
                                </ul>
                            <?php } ?>
                        </li>
                        <?php if (array_key_exists(strtolower(str_replace(' ', '-', $value)), $dropdown)) { ?>
                            <div class="mobileSubNavDropDownMenu" id='<?= $dataAttribute ?>'>
                                <div class="mobileSubNavDropDownDiv">
                                    <ul>
                                        <?php foreach ($dropdown[strtolower(str_replace(' ', '-', $value))] as $dropValue) {
                                            $slug = $dropValue['slug'] ?? null; ?>
                                            <li><a class="<?= $dropValue['sub_page'] == $type ? 'subNavActive' : '' ?>" href="<?= Url::toCollegeDetailSubPage($college->slug, $key, $dropValue['sub_page'], $slug) ?>"><?= $dropValue['sub_page'] ?></a></li>
                                        <?php } ?>
                                    </ul>
                                </div>
                            </div>
                        <?php } ?>
                    <?php else: ?>
                        <li class="subNavDropDown mobileSubNavDropDown">
                            <?php if (array_key_exists($key, $arr)): ?>
                                <a class="" title="<?= $title ?>" href="<?= $key == 'info' ? Url::toCollege($college->slug, '') : $arr[$key] ?>">
                                    <?= $value ?></a>
                            <?php else: ?>
                                <?php if (($pageName == 'ci' || $pageName == 'program') && $key == 'courses-fees'): ?>
                                    <a class="activeLink" title="<?= $title ?>" href="<?= $key == 'info' ? Url::toCollege($college->slug, '') : Url::toCollege($college->slug, $key) ?>">
                                        <?= $value ?></a>
                                <?php else: ?>
                                    <a class="" title="<?= $title ?>" href="<?= $key == 'info' ? Url::toCollege($college->slug, '') : Url::toCollege($college->slug, $key) ?>">
                                        <?= $value ?></a>
                                <?php endif;
                            endif; ?>
                            <?php if (array_key_exists(strtolower(str_replace(' ', '-', $value)), $dropdown)) { ?>
                                <span class="spriteIcon caret" data-list="<?= $dataAttribute ?>"></span>
                                <ul class="subNavDropDownMenu desktopOnly">
                                    <?php foreach ($dropdown[strtolower(str_replace(' ', '-', $value))] as $dropValue) {
                                        $slug = $dropValue['slug'] ?? null; ?>
                                        <li><a href="<?= Url::toCollegeDetailSubPage($college->slug, $key, $dropValue['sub_page'], $slug) ?>"><?= $dropValue['sub_page'] ?></a></li>
                                    <?php } ?>
                                </ul>
                            <?php } ?>
                        </li>
                        <?php if (array_key_exists(strtolower(str_replace(' ', '-', $value)), $dropdown)) { ?>
                            <div class="mobileSubNavDropDownMenu" id='<?= $dataAttribute ?>'>
                                <div class="mobileSubNavDropDownDiv">
                                    <ul>
                                        <?php foreach ($dropdown[strtolower(str_replace(' ', '-', $value))] as $dropValue) {
                                            $slug = $dropValue['slug'] ?? null; ?>
                                            <li><a href="<?= Url::toCollegeDetailSubPage($college->slug, $key, $dropValue['sub_page'], $slug) ?>"><?= $dropValue['sub_page'] ?></a></li>
                                        <?php } ?>
                                    </ul>
                                </div>
                            </div>
                        <?php } ?>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </div>
</nav>
<?php

if ($pageName == 'scholarships' && $isMobile): ?>
    <div class="mobileOnly primaryBtn brochureBtn scholarshipBtn scholarshipBtnCls">
        <div class="lead-cta" data-lead_cta="12" data-image="<?= $college->logo_image ?>" data-entity="college"></div>
    </div>
<?php endif; ?>