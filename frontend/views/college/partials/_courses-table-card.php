<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();

$programSlug = array_column($courses, 'slug');
$invalidValues = array_diff($programSlug, ['diploma', 'certificate', 'fellowship-programme']);
$programSlugIsNotValid = empty($invalidValues);

?>
<style>
    .courseFeesTable .interestedButton {
        width: 80%;
        border-radius: 3px;
        border: solid 1.3px #ff4e53;
        background-color: rgba(255, 78, 83, 0.1);
        font-size: 14px;
        font-weight: 700;
        color: #ff4e53;
    }
</style>

<?php if ($programSlugIsNotValid == false): ?>
    <div class="courseAndFeeDiv pageData <?= count($courses) > 7 ? 'pageInfo' : '' ?>">
        <?php if (isset($content->content) && !empty($content->content)) {  ?>
            <?= $this->render('_author-detail-mobile', [
                'content' => $content ?? [],
                'author' => $author ?? [],
                'profile' => $profile ?? []
            ])
            ?>
        <?php } ?>


        <h2 class="courseAndFeeDivHeading"><?= (!empty($college->display_name)  ? $college->display_name : $college->name) . ' Fee Structure And List Of Courses Summary' ?></h2>
        <table class="courseFeesTable">
            <thead>
                <tr>
                    <td>Course</td>
                    <td>Average Fees</td>
                    <td>Duration</td>
                    <td>Action</td>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($courses as $key => $value):
                    if (in_array($value['slug'], ['diploma', 'certificate', 'fellowship-programme'])) {
                        continue;
                    } ?>
                    <tr>
                        <?php if (!empty($value['coursePage'])): ?>
                            <td>
                                <a
                                    href="<?= Url::base() . $value['coursePage'] ?>"
                                    title="<?= !empty($college->display_name) ? $college->display_name : $college->name ?>  <?= $value['short_name'] ?? $value['name'] ?>">
                                    <?= $value['short_name'] ?? $value['name'] ?>
                                </a>
                            </td>
                        <?php else: ?>
                            <td><a><?= $value['short_name'] ?? $value['name'] ?></a></td>
                        <?php endif; ?>
                        <?php if (!empty($value['avgFees'])): ?>
                            <td>
                                <p><?= '₹' . ContentHelper::indMoneyFormat($value['avgFees']) ?></p>
                            </td>
                        <?php else: ?>
                            <td>--</td>
                        <?php endif; ?>
                        <td><?= !empty($value['course_duration']) ? CollegeHelper::yearsFormat($value['course_duration']) : '--' ?></td>
                        <td>
                            <div class="lead-cta" id="<?= $value['course_id'] ?>" data-slug="<?= $value['slug'] . '-27' ?>" data-lead_cta="27" data-image="<?= $college->logo_image ?? '' ?>" data-entity="college"> </div>
                        </td>
                    </tr>
                    <?php
                endforeach; ?>

            </tbody>
        </table>
    </div>
<?php endif; ?>