<?php

use common\helpers\CollegeHelper;
use common\services\CollegeService;

$category = array_flip(CollegeService::getCategory());
$gender = array_flip(CollegeHelper::$cutoffGender);
$years = array_slice(array_unique($data['yearArr']), 0, 3);

?>
<table>
    <thead>
        <tr>
            <?php if (!empty($data['Cname'])): ?>
                <td><?= $data['Cname']; ?></td>
            <?php else: ?>
                <td>Category</td>
            <?php endif ?>
            <?php foreach ($years as $da): ?>
                <td><?= $da ?></td>
            <?php endforeach; ?>
        </tr>
    </thead>
    <tbody>
        <?php if (!empty($data['valueArr'])): ?>
            <?php foreach ($data['valueArr'] as $key => $val): ?>
                <tr>
                    <td class="cutoffTableMarginTd">
                        <div class="course-fee-wrapper">
                            <div class="course-title">
                                <div class="course-name"><?= $key ?></div>
                            </div>
                            <button type="button" class="applyNow getLeadForm textBlue getFees leadCourseCapture js-open-lead-form-new"
                                data-platform="web" data-entity="college" data-entityid="520" data-stateid="2"
                                data-statename="Punjab" data-interestedlocation="565"
                                data-ctalocation="colleges_info_lovely-professional-university-lpu-jalandhar_course_card_right_cta1_web"
                                data-ctatext="View Detailed Fees" data-leadformtitle="Register now to Get Fee Details"
                                data-subheadingtext="LPU, Phagwara"
                                data-image="https://media.getmyuni.com/azure/college-image/small/lovely-professional-university-lpu-jalandhar.jpg"
                                data-durl="">
                                Get Detailed Fees
                            </button>
                        </div>
                    </td>
                    <?php
                    foreach ($val as $k => $v):
                        if (in_array($k, $years)): ?>
                            <td><?= max($v) ?>
                            </td>
                        <?php endif;
                    endforeach; ?>
                    <?php if (count($val) !== count($years)):
                        for ($count = 1; $count <= count($years) - count($val); $count++): ?>
                            <td>--</td>
                        <?php endfor;
                    endif; ?>
                </tr>
            <?php endforeach; ?>
        <?php endif;
        if (!empty($data['courseValueArr'])):
            ?>
            <?php foreach ($data['courseValueArr'] as $key => $val): ?>
                <tr>
                    <td class="cutoffTableMarginTd">
                        <div class="course-fee-wrapper">
                            <div class="course-title">
                                <div class="course-name">
                                    <?= $key ?>
                                </div>
                            </div>
                            <button type="button" class="applyNow getLeadForm textBlue getFees leadCourseCapture js-open-lead-form-new"
                                data-platform="web" data-entity="college" data-entityid="520" data-stateid="2"
                                data-statename="Punjab" data-interestedlocation="565"
                                data-ctalocation="colleges_info_lovely-professional-university-lpu-jalandhar_course_card_right_cta1_web"
                                data-ctatext="View Detailed Fees" data-leadformtitle="Register now to Get Fee Details"
                                data-subheadingtext="LPU, Phagwara"
                                data-image="https://media.getmyuni.com/azure/college-image/small/lovely-professional-university-lpu-jalandhar.jpg"
                                data-durl="">
                                Get Detailed Fees
                            </button>
                        </div>
                    </td>
                    <?php
                    foreach ($val as $k => $v):
                        if (in_array($k, $years)): ?>
                            <td><?= max($v) ?></td>
                        <?php endif;
                    endforeach; ?>
                    <?php if (count($val) !== count($years)):
                        for ($count = 1; $count <= count($years) - count($val); $count++): ?>
                            <td>--</td>
                        <?php endfor;
                    endif; ?>
                </tr>
            <?php endforeach; ?>
        <?php endif; ?>
    </tbody>
</table>