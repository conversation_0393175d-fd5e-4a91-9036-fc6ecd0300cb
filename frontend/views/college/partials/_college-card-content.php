<?php
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\services\CourseService;
?>
  <style>
                  .more{display: none;}
                   .read-more {
                        background: none!important;
                        border: none;
                        padding: 0!important;
                        /*optional*/
                        font-family: arial, sans-serif;
                        /*input has OS specific font-family*/
                        color: red;
                        text-decoration: none;
                        cursor: pointer;
                        font-size:15px;
                    }
                   .template-text {
                        font-size: 14px;
                        font-weight: 400;
                        color: #282828;
                        font-family: 'roboto';
                    }
                    .summary-text{
                        margin-top:10px; 
                        word-wrap:break-word;
                    }
                    .template-textZ .more-text{
                       display: none;
                     }
                     .add-read-more.show-less-content .second-section,
                        .add-read-more.show-less-content .read-less {
                        display: none;
                        }

                        .add-read-more.show-more-content .read-more {
                           display: none;
                        }

                        .add-read-more .read-more, .add-read-more .read-less {
                        font-weight: bold;
                        margin-left: 2px;
                        color: red;
                        cursor: pointer;
                        }

                        .add-read-more{
                            max-width: 600px;
                            width: 100%;
                            margin: 0 auto;
                        }
  
                 </style>
<?php
if ($isLocation==1 || $urlFilter=='/all-colleges' || $urlFilter=='all-colleges') {
           $courstType = [];
           $courstName = [];
    if (empty($model['course'])) {
        $model['course'] = [];
    }
    foreach ($model['course'] as $course) {
        $courseDegree = str_replace('-', ' ', $course['degree']);
        $courstType[$course['degree']]=  ucwords($courseDegree);
        $courstName[$course['course_name']]= ucfirst($course['course_short_name']);
    }
     
           $cityName =  $model['city_name'];
           $stateName =  $model['state_name'];
    if (strpos($model['state_name'], $model['city_name']) !== false) {
                 $cityName = '' ;
                 $stateName = $model['state_name'];
    }
                   
    ?>
               
                 <div class="summary-text">
                 <p class="template-text1 template-text add-read-more show-less-content">
                 <?= $model['display_name'] ?? $model['name'] ?> is a <?= $model['type'] ? ucfirst($model['type']) . ' ' . $educationBody : '' ?>, located in <?= $cityName ? $cityName . ',' : '' ?> <?= $stateName ?>.
                 <?php  if ((isset($model['courseCount']) && $model['courseCount'] > 0) ||  count($model['course']) > 0) { ?> 
                        <?= $model['display_name'] ?? $model['name'] ?> offers <?= (isset($model['courseCount']) && $model['courseCount'] > 0) ? $model['courseCount'] : count($model['course']) ?>  courses at the  
                        <?php $i=1;
                        foreach ($courstType as $courseValue) {
                            if (count($courstType) == $i) {
                                echo  $courseValue . ' ';
                            } else {
                                echo  $courseValue . ', ';
                            }
                            $i++;
                        }
                        ?> Levels such as 
                        <?php $j=1;
                        foreach ($courstName as $courstNameValue) {
                            if (count($courstName) == $j) {
                                echo  $courstNameValue . ' ';
                            } else {
                                echo  $courstNameValue . ', ';
                            }
                             $j++;
                        }
                        ?>
                   in multiple disciplines. 
                 <?php } ?>
                 <?php if ($model['avgFees']>0) { ?>
                        <?= $model['display_name'] ?? $model['name'] ?> average tuition fee is <?=    '₹' . CollegeHelper::feesFormatListing($model['avgFees'], 'all-colleges');?>.<span></p>
                 <?php } ?> 
                </div>
<?php } elseif ($isStream==1) {
    $stremCourse = [];
    $courstName = [];
    $streamFilterName = '';
    $courstId = [];
    $courseDurationValue = [];
    $minDuration =0;
    $maxDuration = 0;
    $minFeesRange = [];
    $maxFeesRange = [];
    $minFees = 0;
    $maxFees = 0;
    if (empty($model['course'])) {
        $model['course'] = [];
    }
    foreach ($model['course'] as $course) {
        if ($course['stream_slug']==$streamName) {
            $courstName[$course['course_name']]= ucfirst($course['course_short_name']);
            $courstId[$course['course_id']]= ucfirst($course['course_id']);
            $stremCourse[$course['stream_slug']]=  ucwords($course['stream_slug']);
            $feesExpload = explode('-', $course['fees_range']);
            if (count($feesExpload)==1) {
                $maxFeesRange[$feesExpload[0]] = $feesExpload[0];
            } else {
                if (isset($feesExpload[0]) && !empty($feesExpload[0])) {
                    $minFeesRange[$feesExpload[0]] = $feesExpload[0];
                }
    
                if (isset($feesExpload[1]) && !empty($feesExpload[1])) {
                    $maxFeesRange[$feesExpload[1]] = $feesExpload[1];
                }
            }
           
            $feesRange[]=  $course['fees_range'];
            $streamFilterName = $course['stream_name'];
        }
    }
     
    foreach ($courstId as $id) {
        $courseDuration =    Courseservice::getDurationFeature($id, 'course', null);
        if (!empty($courseDuration)) {
            $courseDurationValue[] = CollegeHelper::yearsFormat($courseDuration['value']);
        }
    }
    if (!empty($courseDurationValue)) {
        $minDuration = min($courseDurationValue);
    }
    if (!empty($courseDurationValue)) {
        $maxDuration = max($courseDurationValue);
    }
    if (!empty($minFeesRange)) {
        $minFees = min($minFeesRange);
    }
    if (!empty($maxFeesRange)) {
        $maxFees = max($maxFeesRange);
    }
    $fees='No Fees';
    if ($minFees >0 && $maxFees >0) {
        if ($minFees != $maxFees) {
            $fees = '₹' . CollegeHelper::feesFormatListing($minFees, 'all-colleges') . ' to ' . '₹' . CollegeHelper::feesFormatListing($maxFees, 'all-colleges');
        } elseif ($minFees == $maxFees) {
            $fees = '₹' . CollegeHelper::feesFormatListing($maxFees, 'all-colleges');
        }
    }
    if ($minDuration >0 && $maxDuration >0) {
        if ($minDuration != $maxDuration) {
            $duration = $minDuration . ' - ' . $maxDuration;
        } elseif ($minDuration == $maxDuration) {
            $duration = $maxDuration;
        }
    } else {
        $duration = 0;
    }
   
          
           $cityName =  $model['city_name'];
           $stateName =  $model['state_name'];
    if (strpos($model['state_name'], $model['city_name']) !== false) {
                 $cityName = '' ;
                 $stateName = $model['state_name'];
    }
                   
    ?>
                 
                <div class="summary-text">
                 <p class="template-text1 template-text add-read-more show-less-content" data-college-id="<?= $model['college_id'] ?>">
                 <?= $model['display_name'] ?? $model['name'] ?> is one of best <?= $streamFilterName  ?> colleges in <?= $cityName ?>, <?= $stateName ?>.  <?= $model['display_name'] ?? $model['name'] ?>
                    offers <?php $i=1;
                    foreach ($courstName as $courseValue) {
                        if (count($courstName) == $i) {
                            echo  $courseValue . ' ';
                        } else {
                            echo  $courseValue . ', ';
                        }
                        $i++;
                    }
                    ?> in the stream of <?= $streamFilterName  ?> for a duration of <?=   $duration ?>.
                         <?php if ($fees!='No Fees') { ?>
                         <span class="hasFees">
                                <?= $model['display_name'] ?? $model['name'] ?>  fees for <?= $streamFilterName  ?> courses range from <?=  $fees ?>.
                         </span>
                         <?php } ?>  
                     </p>
                </div>

<?php } elseif ($isDegree==1) {
    $courstId = [];
    $courseDurationValue = [];
    $minDuration =0;
    $maxDuration = 0;
    $courseType = '';
    $feesRange = '';
    $minFees = 0;
    $maxFees = 0;
    if (empty($model['course'])) {
        $model['course'] = [];
    }
    foreach ($model['course'] as $course) {
       // echo "<pre>"; print_r($course);
        if ($courseFieldName=='Courses') {
            if ($course['course_slug']==$courseSlug) {
                $courseDegree = str_replace('-', ' ', $course['degree']);
                $courseType =  ucwords($courseDegree);
                $courstId[$course['course_id']]= ucfirst($course['course_id']);
                $feesExpload = explode('-', $course['fees_range']);
                if (count($feesExpload)==1) {
                    $maxFees = $feesExpload[0];
                } else {
                    if (isset($feesExpload[0]) && !empty($feesExpload[0])) {
                        $minFees = $feesExpload[0];
                    }
        
                    if (isset($feesExpload[1]) && !empty($feesExpload[1])) {
                        $maxFees = $feesExpload[1];
                    }
                }
            }
        } elseif ($courseFieldName=='Specialization') {
            if (!empty($course['specialization'])) {
                foreach ($course['specialization'] as $spec) {
                    if ($courseSlug== $spec) {
                        $courseDegree = str_replace('-', ' ', $course['degree']);
                        $courseType =  ucwords($courseDegree);
                        $courstId[$course['course_id']]= ucfirst($course['course_id']);
                        $feesRange = $course['fees_range'];
                    }
                }
            }
        }
    }
    foreach ($courstId as $id) {
        $courseDuration =    Courseservice::getDurationFeature($id, 'course', null);
        if (!empty($courseDuration)) {
            $courseDurationValue[] = CollegeHelper::yearsFormat($courseDuration['value']);
        }
    }
    if (!empty($courseDurationValue)) {
        $minDuration = min($courseDurationValue);
    }
    if (!empty($courseDurationValue)) {
        $maxDuration = max($courseDurationValue);
    }
   
    if ($minDuration >0 && $maxDuration >0) {
        if ($minDuration != $maxDuration) {
            $duration = $minDuration . ' - ' . $maxDuration;
        } elseif ($minDuration == $maxDuration) {
            $duration = $maxDuration;
        }
    } else {
        $duration = 0;
    }
    $fees='No Fees';
    if ($minFees >0 || $maxFees >0) {
        if ($minFees != $maxFees  && $minFees > 0) {
            $fees = '₹' . CollegeHelper::feesFormatListing($minFees, 'all-colleges') . ' - ' . '₹' . CollegeHelper::feesFormatListing($maxFees, 'all-colleges');
        } elseif ($minFees == $maxFees) {
            $fees = '₹' . CollegeHelper::feesFormatListing($maxFees, 'all-colleges');
        } elseif ($maxFees>0) {
            $fees = '₹' . CollegeHelper::feesFormatListing($maxFees, 'all-colleges');
        }
    }
   
    $cityName =  $model['city_name'];
    $stateName =  $model['state_name'];
    if (strpos($model['state_name'], $model['city_name']) !== false) {
                 $cityName = '' ;
                 $stateName = $model['state_name'];
    }
    $cityName =  $model['city_name'];
    $stateName =  $model['state_name'];
    if (strpos($model['state_name'], $model['city_name']) !== false) {
            $cityName = '' ;
            $stateName = $model['state_name'];
    }
                   
    ?>
                 
                <div class="summary-text">
              <?php  if ($courseFieldName=='Courses') { ?>
                 <p class="template-text1 template-text add-read-more show-less-content">
                    <?= $model['display_name'] ?? $model['name'] ?>  is very popular for  <?=  $courseName ?? $courseName ?> in <?= $cityName ?>, <?= $stateName ?>. 
                    <?=  $courseName ?? $courseName ?> at <?= $model['display_name'] ?? $model['name'] ?> is a <?= $courseType ?> program offered in multiple specializations  for a duration of <?= $duration ?> .
                    <?php if ($fees!='No Fees') { ?>
                        <?= $model['display_name'] ?? $model['name'] ?>  <?=  $courseName ?? $courseName ?> fees is <?= $fees ?>.
                    <?php } ?>
                </p>
              <?php } elseif ($courseFieldName=='Specialization') { ?>
                <p class="template-text1 template-text add-read-more show-less-content">
                  <?= $model['display_name'] ?? $model['name'] ?>  is very popular for  <?=  $courseName ?? $courseName ?> in <?= $cityName ?>, <?= $stateName ?>. 
                  <?=  $courseName ?? $courseName ?> at <?= $model['display_name'] ?? $model['name'] ?> is a <?= $courseType ?> program offered  for a duration of <?= $duration ?> .
                  <?php if ($fees!='No Fees') { ?>
                    <span class="hasFees">
                        <?= $model['display_name'] ?? $model['name'] ?>  <?=  $courseName ?? $courseName ?> fees is <?= $fees ?>.
                  <?php } ?>
                </span>
                </p>
              <?php } ?>
                </div>

<?php }  ?>