<?php

use frontend\models\CollegeSearch;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

//echo "<pre>";print_r($model); die;
?>

<section class="filterSidebarSection" id="collegeFilterDSection">
    <p class="foundesults row 2345">Found <?= $totalCollegeCount ?? '' ?> Colleges
        <?php if (!empty($selectedFilters)):
            ?><span id="clearAllClg" class="clearAll">Clear All</span><?php
        endif; ?>
    </p>
    <?php if (!empty($selectedFilters)): ?>
        <div class="filterCategory">
            <p class="filterCategoryName">Selected Filters</p>
            <div id="selectedFilters" class="filterDiv">
                <?php foreach ($selectedFilters as $filter): ?>
                    <button id="<?= $filter['slug'] ?>" data-attr="<?= $filter['mapped_field'] ?>"><?= $filter['name'] ?> <i class="spriteIcon closeIcon remove-college-filter"></i></button>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php $form = ActiveForm::begin([
        'id' => 'college-filter-form'
    ]); ?>

    <?php foreach ($filters as $key => $items): ?>
        <?php if (isset(CollegeSearch::$propertyMapping[$key])): ?>
            <div class="filterCategory">
                <p class="filterCategoryName"><?= $key ?></p>
                <div class="filterDiv">
                    <div class="filterSearch">
                        <input type="text" placeholder="Search options">
                        <?= $form->field($model, CollegeSearch::$propertyMapping[$key])->checkboxList($items, [
                            'tag' => 'ul',
                            'item' => function ($index, $label, $name, $checked, $value) {
                                $html = Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                                $html .= $label;
                                return Html::tag('li', $html);
                            }
                        ])->label(false) ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endforeach; ?>

    <?php ActiveForm::end(); ?>

</section>