<?php
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\FilterHelper;
use common\models\CollegeContent;
use common\models\Lead;
use common\models\LeadBucketTagging;
use common\services\CollegeService;
use common\services\UserService;
use frontend\helpers\Ad;
use frontend\helpers\Url;
?>
<?php
$noFilterOne = 0;
$stateFilterOne = 0;
$streamFilterOne =0;
$cityFilterOne =0;
$specilazitionFIlterOne =0;
$stream = '';
$FilterTextWidgetOne = 'Filter By City';
$widgetOne = $filters['City'] ?? [];
$url=Url::base(true) . '/' . \Yii::$app->controller->action->id;
if (\Yii::$app->controller->action->id !='all-colleges') {
        $url = Url::base(true) . '/all-colleges';
}
$urlParams='';
if (empty($selectedFilters)) {
    $noFilterOne =0;
} else {
    foreach ($selectedFilters as $keyValue => $selectFilter) {
        if (isset($selectFilter['filterGroup_name']) && $selectFilter['filterGroup_name'] ==  'City') {
                $cityFilterOne = 1;
                $url = Url::base(true) . \Yii::$app->request->url ;
                $urlParams = $keyValue;
                break;
        }
        if (isset($selectFilter['filterGroup_name']) && $selectFilter['filterGroup_name'] ==  'State') {
                $stateFilterOne = 1;
                $FilterTextWidgetOne =  'Filter By Stream'  ;
            if ($streamFilterOne == 0) {
                $widgetOne = $filters['Streams'];
            }
                $url = Url::base(true) ;
            if ($cityFilterOne ==0) {
                    $urlParams = $keyValue;
            }
                break;
        }
    }
} ?> 
<div class="filter__by__exam hide__widget">
    <div class="heading__row">
            <h3 class="exam__row__heading"><?=  $FilterTextWidgetOne?></h3>
            <!--div class="view__all__exams">View All
                    <span class="spriteIcon__2 blue__angle__icon"></span>
            </div-->
    </div>
    <ul class="exam__list">
            <?php
                $k=1;
            foreach ($widgetOne as $key => $value) {
                if ($k>8) {
                    break;
                }
                if ($stateFilterOne ==1 || $streamFilterOne==1):
                    ?>
               <a  target="_blank" style="text-decoration:none; cursor:pointer; " href="<?php echo $url ; ?>/<?php echo $key . '-colleges' . '/' . $urlParams; ?>"><li><?php echo strip_tags($value); ?></li></a>
                <?php elseif ($cityFilterOne==1): ?>
                <a  target="_blank" style="text-decoration:none; cursor:pointer; " href="<?php echo $url ; ?>"><li><?php echo strip_tags($value); ?></li></a> 
                <?php else: ?>
                <a  target="_blank" style="text-decoration:none; cursor:pointer; " href="<?php echo $url ; ?>/<?php echo $key; ?>"><li><?php echo strip_tags($value); ?></li></a>
                <?php endif; ?>
                <?php $k++;
            } ?>
    </ul>
</div>