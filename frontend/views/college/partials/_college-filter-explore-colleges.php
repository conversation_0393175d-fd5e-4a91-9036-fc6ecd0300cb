<?php
$noFilter = 0;
$stateFilter = 0;
$streamFilter = 0;
$cityFilter = 0;
$stateFIlter = 0;
$specilazitionFIlter = 0;
$stream = '';
$FilterTextWidgetSecond = '';
$widgetSecond = $filters['Streams'] ?? [];
$widgetThird = $filters['Courses'] ?? [];
$widgetFour = $filters['Specialization'] ?? [];
if (empty($selectedFilters)) {
    $noFilter = 0;
} else {
    foreach ($selectedFilters as $selectFilter) {
        if (isset($selectFilter['filterGroup_name']) && $selectFilter['filterGroup_name'] ==  'Streams') {
            $streamFilter = 1;
            $FilterTextWidgetSecond =  $selectFilter['name'];
            $widgetSecond = $filters['Streams'];
        }
        if (isset($selectFilter['filterGroup_name']) && $selectFilter['filterGroup_name'] ==  'State') {
            $stateFilter = 1;
            $FilterTextWidgetSecond =  'Courses ' . $selectFilter['name'] . ' Colleges in India';
            if ($streamFilter == 0 && $cityFilter == 0) {
                $widgetSecond = $filters['Courses'];
            }
        }
        if (isset($selectFilter['filterGroup_name']) && $selectFilter['filterGroup_name'] ==  'City') {
            $cityFilter = 1;
            $FilterTextWidgetSecond =  $selectFilter['name'];
            if ($streamFilter == 0  &&  $stateFilter == 0) {
                $widgetSecond = $filters['Streams'];
            }
        }
        if (isset($selectFilter['filterGroup_name']) && $selectFilter['filterGroup_name'] ==  'Specialization') {
            $cityFilter = 1;
            $city =  $selectFilter['name'];
        }
        if (isset($selectFilter['filterGroup_name']) && $selectFilter['filterGroup_name'] ==  'Course') {
            $courseFilter = 1;
            $course =  $selectFilter['name'];
        }
    }
} ?>

<h2>You can also explore</h2>
<ul class="tab__list">
    <li class="current__tab" data-tab="popularExam"><?= ($FilterTextWidgetSecond != '') ? $FilterTextWidgetSecond : 'Stream'; ?></li>
    <li data-tab="engineeringExam">Course Colleges in India</li>
    <li data-tab="specilization">Specialisation Colleges in India</li>
</ul>
<div class="tab__sections">
    <div id="popularExam" class="tab__section current__tab">
        <div class="tab__subsection__list">
            <p>By <?= ($FilterTextWidgetSecond != '') ? $FilterTextWidgetSecond : 'Stream'; ?> </p>
            <ul>
                <?php $i = 1;
                foreach ($widgetSecond as $key => $filter):
                    if ($i > 10) {
                        break;
                    } ?>
                    <li><a target="_blank" href="<?= $key . '-colleges' ?>"><?php echo strip_tags($filter); ?> Colleges in India</a></li>
                    <?php $i++;
                endforeach; ?>
            </ul>
        </div>

    </div>
    <div id="engineeringExam" class="tab__section">
        <div class="tab__subsection__list">
            <p>By Course</p>
            <ul>
                <?php $j = 1;
                foreach ($widgetThird as $filterCourse):
                    if ($j > 10) {
                        break;
                    } ?>
                    <li><?php echo $filterCourse ?> Colleges in India</li>
                    <?php $j++;
                endforeach; ?>

            </ul>
        </div>
    </div>

    <div id="specilization" class="tab__section">
        <div class="tab__subsection__list">
            <p>By Course</p>
            <ul>
                <?php $j = 1;
                foreach ($widgetThird as $filterCourse):
                    if ($j > 10) {
                        break;
                    } ?>
                    <li><?php echo $filterCourse ?> Colleges in India</li>
                    <?php $j++;
                endforeach; ?>

            </ul>
        </div>
    </div>
</div>