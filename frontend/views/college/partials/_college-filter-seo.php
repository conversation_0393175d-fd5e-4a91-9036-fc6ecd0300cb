<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;

$isMobile = Yii::$app->devicedetect->isMobile();

?>


<div class="college__Landing__Hero__Section pageData pageInfo">
<?php if (!empty($collegeListingNotification)): ?>
    <div class="latestUpdates <?php echo (!$isMobile) ? 'desktopOnly' : ''; ?>">
    <h2 class="cardHeading">Latest Update</h2>
    <ul>
    <?php
    foreach (array_values($collegeListingNotification) as $keys => $updateNotification) {
        ?>
        <li>
            <span style="color:red;"><?= date('Y-m-d', strtotime($updateNotification['start_date'])) ?></span> <span style="color:#000000"><?= $updateNotification['text'];  ?></span><p><?= $updateNotification['content'];?></p> 
        </li>
        <?php
    }
    ?>
    </ul>
</div>
     
<?php endif; ?>
    <?php if (!empty($seoInfo['h1']) && !empty($seoInfo['content'])): ?>
        <h1 class="college__Landing__Hero__Section__Heading"><?= $seoInfo['h1'] ?? '' ?></h1>

        <?php if (!empty($seoInfo['content'])): ?>
            <?= !empty($seoInfo['content']) ? html_entity_decode(ContentHelper::removeStyleTag(
                stripslashes(DataHelper::parseDomainUrlInContent($seoInfo['content']))
            )) : '' ?>

        <?php endif;
    elseif (!empty($seoInfo['h1'])): ?>
        <h1 class="college__Landing__Hero__Section__Heading"><?= $seoInfo['h1'] ?? '' ?></h1>
    <?php else: ?>
        <h1 class="college__Landing__Hero__Section__Heading">Top Colleges in India <?= $year ?> </h1>
    <?php endif; ?>
</div>