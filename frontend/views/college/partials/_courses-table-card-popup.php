
<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();

$programSlug = array_column($courses, 'slug');
$invalidValues = array_diff($programSlug, ['diploma', 'certificate', 'fellowship-programme']);
$programSlugIsNotValid = empty($invalidValues);

?>
<?php if ($programSlugIsNotValid == false): ?>
    <div class="courseAndFeeDiv pageData <?= count($courses) > 7 ? 'pageInfo' : '' ?>">
        <?php if (isset($content->content) && !empty($content->content)) {  ?>
            <?= $this->render('_author-detail-mobile', [
                'content' => $content ?? [],
                'author' => $author ?? [],
                'profile' => $profile ?? []
            ])
            ?>
        <?php } ?>


        <h2 class="courseAndFeeDivHeading"><?= (!empty($college->display_name)  ? $college->display_name : $college->name) . ' Fee Structure And List Of Courses Summary' ?></h2>
        <table class="courseFeesTable">
            <thead>
                <tr>
                    <td>Course</td>
                    <td>Average Fees</td>
                    <td>Eligibility</td>
                </tr>
            </thead>
            <tbody>
                <?php  foreach ($courses as $key => $value):
                    if (in_array($value['slug'], ['diploma', 'certificate', 'fellowship-programme'])) {
                        continue;
                    } ?>
                    <tr>
                        <?php if (!empty($value['coursePage'])): ?>
                            <td>
                                <a 
                                    href="<?= Url::base() . $value['coursePage'] ?>"
                                    title="<?= !empty($college->display_name) ? $college->display_name : $college->name ?>  <?= $value['short_name'] ?? $value['name'] ?>"        
                                >
                                    <?= $value['short_name'] ?? $value['name'] ?>
                                </a>
                            </td>
                        <?php else: ?>
                            <td><a><?= $value['short_name'] ?? $value['name'] ?></a></td>
                        <?php endif; ?>
                        <?php if (!empty($value['avgFees'])): ?>
                            <td>
                                <a title=""  class="showTooltip showFees<?=  $value['course_id'] ?>" data-course-id="<?=  $value['course_id']?>" data-college-id="<?= $college->id ?>"><?= '₹' . ContentHelper::indMoneyFormat($value['avgFees']) ?> 
                                   </a>
                            </td>
                        <?php else: ?>
                            <td>--</td>
                        <?php endif; ?>
                        <td><?php
                        if (!empty($value['course_content_eligibility'])) {
                              echo  $value['course_content_eligibility']  ;
                        }
                        if (!empty($value['course_content_eligibility']) && !empty($value['exams'])) {
                            echo ' +';
                        }
                        if (!empty($value['exams'])) {
                            foreach ($value['exams'] as $key => $exam) {
                                $count=1;
                                foreach ($exam as $keys => $keyExam) {
                                    if (count($exam)==1) {
                                                            echo  '<span><a target="_blank" href="' . Url::home() . 'exams/' . $keyExam . '">' . $keys . '</a> </span>';
                                                            break;
                                    }
                                    if ($count<2) {
                                        echo  '<span><a target="_blank" href="' . Url::home() . 'exams/' . $keyExam . '">' . $keys . ',</a> </span>';
                                    }
                                    $count++;
                                }
                            }
                            if ($count > 2): ?>
                               <span style="color:#3d8ff2" class="FexamMoreFilter <?= $value['slug'] ?>Exam tooltip" title=""  data-id="<?= $value['slug'] ?>"  >+<?= ($count - 3 == 0) ? 1 : $count - 2  ?>
                              <span class="tooltiptext">
                                <?php foreach ($value['exams'] as $key => $exam) {
                                    $countTooltip =1; foreach ($exam as $keys => $keyExam) {
                                        if ($countTooltip>1) {
                                               echo   '<span ><a   style="display: inline;"  class="hideExamFilter ' . $value['slug'] . '_hideExamFilter" target="_blank" href="' . Url::home() . 'exams/' . $keyExam . '">' . $keys . '</a></span>';
                                        }
                                        $countTooltip++;
                                    }
                                }
                                ?>
                              </span>
                            </span>
                            <?php endif;
                        }  ?>  
                        
                       
                        </td>
                    </tr>
                    <?php
                endforeach; ?>

            </tbody>
        </table>
    </div>

<?php endif; ?>
<dialog class="timelineModal">
    <h2 class="timelineModal--heading" id="headingmodelFess"></h2>
    <span class="spriteIcon timelineModal--closeIcon"></span>
    <div id="bodymodelFess"></div>
</dialog>