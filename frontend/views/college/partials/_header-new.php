<?php

use frontend\helpers\Url;
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;

$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = !empty($profile->image) ? (Yii::getAlias('@profileDPFrontend') . '/' . $profile->image) : '/yas/images/usericon.png';
$subPage = ['reviews', 'images-videos', 'program', 'pi'];
$subPageWihoutContent = ['ci'];
?>

<header class="clgInfoHeroSection">
    <div class="collegeInfo" style="background-image: url(<?php echo !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner() ?>);">
        <div class="row collegeInfo__flexContent">
            <div class="heroSection__leftSide">
                <a href="<?= Url::toCollege($college->slug) ?>" title="<?= $college->name ?>">
                    <img class="collegeLogo" width="72" height="72" data-src="<?= !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo() ?>" src="<?= !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo() ?>" alt="<?= $college->name ?>">
                </a>
                <div class="collegeIntro">
                    <h1><?= $heading ?></h1>
                    <ul>
                        <?php if (!empty($city) || !empty($state)): ?>
                            <li><span class="spriteIcon locationPinIcon"></span>
                                <?= !empty($city) ? $city->name : '' ?> <?= !empty($state) ? ', ' . $state->name : '' ?></li>
                        <?php endif; ?>
                        <?php if (!empty($rating) && !empty($menus) && !is_numeric($menus['reviews'])):
                            if (CollegeHelper::getTotalRating($categoryRating) != 0): ?>
                                <li class="desktopOnly">
                                    <span class="ratingValue"><?= CollegeHelper::getTotalRating($categoryRating)  ?></span>
                                    <?= (CollegeHelper::getTotalRating($categoryRating) == 1) ? CollegeHelper::getTotalStars(1) : CollegeHelper::getTotalStars(CollegeHelper::getTotalRating($categoryRating)); ?>
                                    <?php
                                    if (!empty($reviewCount)):
                                        ?>
                                        (<a href="<?= Url::toCollege($college->slug, 'reviews') ?>"><?= $reviewCount ?> Reviews</a>)
                                    <?php endif; ?>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
            <div class="heroSection__rightSide">
                <!-- <span class="spriteIcon compareIcon"></span> -->
                <div class="spriteIconTwo compareIcon">
                </div>

                <div class="compareText">Compare</div>
            </div>
        </div>
    </div>
    <div class="collegeInfoNew mobileOnly">
        <ul>
            <li>
                <?php if (!empty($rating) && !empty($menus) && !is_numeric($menus['reviews'])): ?>
                    <?php if (CollegeHelper::getTotalRating($categoryRating) != 0): ?>
                        <?= CollegeHelper::getTotalRating($categoryRating) ?>
                        <?= (CollegeHelper::getTotalRating($categoryRating) == 1) ? CollegeHelper::getTotalStars(1) : CollegeHelper::getTotalStars(CollegeHelper::getTotalRating($categoryRating)); ?>
                        <?php
                        if (!empty($reviewCount)): ?>
                            (<a href="<?= Url::toCollege($college->slug, 'reviews') ?>"><?= $reviewCount ?> Reviews</a>)
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </li>
        </ul>
        <?php if ($isMobile): ?>
            <div class="btnDiv" style="height: 34px">
                <div class="lead-cta" data-lead_cta="1" data-durl="<?= !empty($brochure) ? Url::toCollegeBroucher($brochure->pdf) : '' ?>" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl ?? '' ?>" data-course="<?= !empty($courseId) ? $courseId : null ?>" data-program="<?= !empty($programSlug) ? $programSlug : null ?>"></div>
            </div>
        <?php endif; ?>
    </div>

    <div class="heroSectionSubDiv row desktopOnly">
        <div class="updated-info">
            <?php
            $updatedDate = $content->updated_at ?? '';
            $contentData = ContentHelper::getDateUpdateCollegePage($updatedDate);
            if (!empty($content) && !in_array($pageName, $subPage) && !empty($content->content) && !empty($author)): ?>
                <img class="lazyload" width="36" height="36" loading="lazy" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= !empty($author) ? $author->name : '' ?>">
                <a class="authorName" href="<?= Url::toAllAuthorPost($author->slug) ?>" title="<?= !empty($author) ? $author->name : '' ?>"><?= !empty($author) ? $author->name : '' ?></a>
                <span class="spriteIcon verifiedIcon"></span>
                <?php
                if ($contentData) { ?>
                    | <span> <?= $contentData; ?></span>
                <?php }
                ?>
            <?php elseif (!in_array($pageName, $subPage) && empty($author) && !empty($content) && !empty($content->content)): ?>
                <?php if ($contentData) { ?>
                    <span> Updated on - <?= Yii::$app->formatter->asDate($content->updated_at ?? date('M D, Y')) ?></span>
                <?php } ?>
            <?php elseif (in_array($pageName, $subPageWihoutContent)): ?>
                <span> Updated on - <?= Yii::$app->formatter->asDate($content->updated_at ?? date('M D, Y')) ?></span>
            <?php endif; ?>

        </div>
        <div class="btnDiv desktopOnly">
            <div class="lead-cta" data-lead_cta="2" data-durl="<?= !empty($brochure) ? Url::toCollegeBroucher($brochure->pdf) : '' ?>" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl ?? '' ?>" data-course="<?= !empty($courseId) ? $courseId : null ?>" data-program="<?= !empty($programSlug) ? $programSlug : null ?>"></div>
        </div>
    </div>
</header>
<div id="college_compare_header_select_panel"></div>