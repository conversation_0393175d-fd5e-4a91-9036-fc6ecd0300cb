<?php

$pages = ['info', 'admission', 'ranking', 'placement', 'course-fee'];

if (!empty($cutOff['dataProvider']->allModels)): ?>
    <?php if (!in_array($page, $pages)): ?>
        <section class="filterSection filtersAll cutoffFilterSection collegeCourseCutOff">
            <?= $this->render('_cut-off-filter', [
                'model' => $cutOff['searchModel'],
                'college' => $college,
                'course_id' => $course_id
            ]) ?>
        </section>
    <?php endif; ?>

    <section class="cutOffDetailSection">
        <div class="courseCutOffTypeList">
            <?php if (!empty($cutOff['dataProvider']->allModels)): ?>
                <?= $this->render(
                    '_cut-off-list',
                    [
                        'models' => $cutOff['dataProvider']->allModels,
                        'college' => $college,
                        'state' => $state,
                        'isMobile' => $isMobile,
                        'text' => in_array($page, $pages) ? 'View Detailed Cutoff' : '',
                        'collegeurl' => in_array($page, $pages) ? $college->slug . '-cut-off' : ''
                    ]
                ); ?>
            <?php else: ?>
                <div> NO Result Found</div>
            <?php endif; ?>
        </div>
    </section>
<?php endif; ?>