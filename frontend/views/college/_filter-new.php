<?php

use frontend\helpers\Html;
use frontend\helpers\Url;
use yii\helpers\Inflector;
use yii\widgets\ActiveForm;

?>
<div class="searchBar">
    <input type="text" autocomplete="off" id="filterCourseSeach" onkeyup="getCourses()" placeholder="Search B.Tech Civil, B.Tech Mechanical" class="course-search" tabindex="1" value="">
    <div class="courseSelection" style="display:none;">
        <ul id="search-program">
            <?php foreach ($programList as $k => $v): ?>
                <li class="mainTuple">
                    <a class="programLink" href="<?= Url::toCollegeCourseProgram($college->slug, $v['slug'], $v['pageIndex'] ?? ''); ?>" data-program="<?= $v['slug'] ?>"><?= $v['name'] ?></a>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
    <span class="spriteIcon filterSearch"></span>
</div>
<?php $form = ActiveForm::begin(['id' => 'college-search-form']);
$courseCheckbox['courseCheckbox'] = $model->selectedFilters['course'] ?? [];
$courseCheckbox['courseCount'] = count($model->courses);
$streamCheckbox = $model->selectedFilters['stream'] ?? [];
// $branchCheckbox['branchCheckbox'] = $model->selectedFilters['branch'] ?? [];
// $branchCheckbox['branchesCount'] = count($model->branches);
$typeCheckbox = $model->selectedFilters['type'] ?? [];
$levelCheckbox = $model->selectedFilters['degree'] ?? [];
// $totalFeesCheckbox = $model->selectedFilters['totalFee'] ?? [];
?>
<div class="row filterRow">
    <span>Filter By:</span>
    <?php if (!empty($model->selectedFilters)): ?>
        <a id="resetAll">Reset</a>
    <?php endif; ?>
</div>
<div class="filterSectionForm">
    <ul class="tabs">
        <li class="tab-link current" data-tab="course">Course</li>
        <?php if (!empty($model->streams)): ?>
            <li class="tab-link " data-tab="stream">Stream</li>
        <?php endif; ?>
        <?php /*if (!empty($model->branches)): ?>
                    <li class="tab-link " data-tab="branch">Branch</li>
                <?php endif;*/ ?>
        <li class="tab-link " data-tab="type">Type</li>
        <li class="tab-link " data-tab="degree">Course Level</li>
        <?php /*<li class="tab-link " data-tab="totalFee">Total Fees</li>*/ ?>
    </ul>

    <div class="filterContentDiv">
        <div id="course" class="filterCheckButtons tab-content current">
            <?=
            $form->field($model, 'course[]')->checkboxList($model->courses, [
                'tag' => 'ul',
                'item' => function ($index, $label, $name, $checked, $value) use ($courseCheckbox) {

                    $checked = (in_array($value, $courseCheckbox['courseCheckbox'])) ? true : false;
                    $input = Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                    $label = Html::label($label, $value);
                    $addClass = $index > 9 ? 'hideCourse' : '';

                    $html = "<li class='filterCheckContainer {$addClass} {$index}'>{$input}{$label}</li>";
                    if ($index == 9) {
                        $count = $courseCheckbox['courseCount'] - $index;
                        $html .= "<li class='filterCheckContainer courseMore'>+$count More</li>";
                    }
                    if ($index == $courseCheckbox['courseCount'] - 1) {
                        $html .= "<li class='filterCheckContainer' id='courseViewLess' style='display: none;'>- Less</li>";
                    }
                    return $html;
                },

            ])
                ->label(false);
            ?>
        </div>
        <div id="stream" class="filterCheckButtons tab-content">
            <?=
            $form->field($model, 'stream[]')->checkboxList($model->streams, [
                'tag' => 'ul',
                'item' => function ($index, $label, $name, $checked, $value) use ($streamCheckbox) {
                    $checked = (in_array($value, $streamCheckbox)) ? true : false;
                    $input = Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                    $label = Html::label($label, $value);
                    return "<li class='filterCheckContainer'>{$input}{$label}</li>";
                },
            ])->label(false);
            ?>
        </div>
        <?php /*<div id="branch" class="filterCheckButtons tab-content">
                    <p id="branchViewLess" style="display: none;">-Less</p>
                    <?=
                    $form->field($model, 'branch[]')->checkboxList($model->branches, [
                        'tag' => 'ul',
                        'item' => function ($index, $label, $name, $checked, $value) use ($branchCheckbox) {
                            $checked = (in_array($value, $branchCheckbox['branchCheckbox'])) ? true : false;
                            $input = Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                            $label = Html::label($label, $value);
                            $addClass = $index > 10 ? 'hideBranch' : '';
                            $html ="<li class='filterCheckContainer {$addClass}'>{$input}{$label}</li>";
                            if ($index == 10) {
                                $count = $branchCheckbox['branchesCount'] - $index;
                                $html .="<li class='filterCheckContainer branchMore'>+$count More</li>";
                            }
                            return $html;
                            return "<li class='filterCheckContainer'>{$input}{$label}</li>";
                        },
                    ])->label(false);
                    ?>
                </div>*/
        ?>
        <div id="type" class="filterCheckButtons tab-content">
            <?= $form->field($model, 'type[]')->checkboxList($model->types, [
                'tag' => 'ul',
                'item' => function ($index, $label, $name, $checked, $value) use ($typeCheckbox) {
                    $checked = (in_array($value, $typeCheckbox)) ? true : false;
                    $html = Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                    $html .= Html::label($label, $value);
                    return Html::tag('li', $html, ['class' => 'filterCheckContainer']);
                }
            ])->label(false) ?>
        </div>
        <div id="degree" class="filterCheckButtons tab-content">
            <?= $form->field($model, 'degree[]')->checkboxList($model->degrees, [
                'tag' => 'ul',
                'item' => function ($index, $label, $name, $checked, $value) use ($levelCheckbox) {
                    $checked = (in_array($value, $levelCheckbox)) ? true : false;
                    $html = Html::checkbox($name, $checked, ['value' => $label, 'id' => $label]);
                    $html .= Html::label(ucwords(str_replace('-', ' ', $label)), $label);
                    return Html::tag('li', $html, ['class' => 'filterCheckContainer']);
                }
            ])->label(false) ?>
        </div>
        <?php /*<div id="totalFee" class="filterCheckButtons tab-content">
                    <?= $form->field($model, 'totalFee[]')->checkboxList($model->totalFees, [
                        'tag' => 'ul',
                        'item' => function ($index, $label, $name, $checked, $value) use ($totalFeesCheckbox) {
                            $checked = (in_array($value, $totalFeesCheckbox)) ? true : false;
                            $html = Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                            $html .= Html::label($label, $value);
                            return Html::tag('li', $html, ['class' => 'filterCheckContainer']);
                        }
                    ])->label(false) ?>
                </div>
                */ ?>
    </div>

</div>
<?php if (!empty($model->selectedFilters)): ?>
    <div class="filterSectionSelection">
        <?php foreach ($model->selectedFilters as $filters): ?>
            <?php foreach ($filters as $filter): ?>
                <?php
                $filterName = '';

                /*if (key_exists($filter, $model->totalFees)) {
                    $filterName = $model->totalFees[$filter];
                } <else>*/
                if (key_exists($filter, $model->courses)) {
                    $filterName = $model->courses[$filter];
                } else if (key_exists($filter, $model->types)) {
                    $filterName = $model->types[$filter];
                } else if (key_exists($filter, $model->streams)) {
                    $filterName = $model->streams[$filter];
                } else if (key_exists($filter, $model->degrees)) {
                    $filterName = $model->degrees[$filter];
                    $filterName = Inflector::titleize(preg_replace('/[- 0-9]/', ' ', $filterName), true);
                }
                /*else if (key_exists($filter, $model->branches)) {
                    $filterName = $model->branches[$filter];
                } */ ?>
                <button data-slug="<?= $filter ?>"><span><?= $filterName ?? $filter ?><i class="spriteIcon closeIcon removeFilter"></i></button>
            <?php endforeach; ?>
        <?php endforeach; ?>
    </div>
<?php endif; ?>
<?php ActiveForm::end(); ?>