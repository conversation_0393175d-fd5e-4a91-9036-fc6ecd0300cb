<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\SeoExperimentHelper;
use frontend\helpers\Url;
use yii\helpers\Html;

//utils
$isMobile = Yii::$app->devicedetect->isMobile();
?>

<?php if ($isMobile): ?>
    <?= $this->render('partials/_college-filter-search-mobile-new-design', [
        'filters' => $filters,
        'model' => $searchModel,
        'selectedFilters' => $selectedFilters,
        'totalCollegeCount' => $totalCount,
        'sort' => $sort,
        'isContent' => empty($seoInfo['content']) ? false : true,

    ]) ?>
<?php endif ?>
   
<?php if (!$isMobile): ?>
    <?= $this->render('partials/_college-filter-search', [
        'filters' => $filters,
        'model' => $searchModel,
        'selectedFilters' => $selectedFilters,
        'totalCollegeCount' => $totalCount,
    ]) ?>
<?php endif ?>
 <!-- <aside> -->
 <?= $this->render('partials/_sidebar-ads.php', [
            'class' => 'desktopOnly',
            'ads' => [
                ['slot' => 'getmyuni-com_siderail_right', 'size' => '__300x250', 'isMobile' => false],
                ['slot' => 'getmyuni-com_siderail_right_2', 'size' => '__300x250', 'isMobile' => false],
            ]
        ]) ?>
        <!-- </aside> -->

       
   
