<?php

use common\models\College;
use common\helpers\ContentHelper;
use common\helpers\CollegeHelper;
use common\models\LiveUpdate;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use yii\helpers\Inflector;
use yii\widgets\ListView;
use yii\helpers\StringHelper;

$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'courses-fees');

$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : 'Browse ' . count($collegeCourses['searchModel']->courses) . ' courses across ' . $programCount . ' programs and their fees at ' . (!empty($college->display_name) ? $college->display_name : $college->name) . '. Get fee structure along with course reviews, seats & eligibility.';
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();

$isMobile = \Yii::$app->devicedetect->isMobile();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $college->city->name, 'url' => ['/all-colleges/' . $college->city->slug], 'title' => 'Colleges in ' . $college->city->name];
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' =>  !empty($college->display_name) ? $college->display_name : $college->name];
$this->params['breadcrumbs'][] = 'Courses and Fees';

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image']);
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $college->city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';

// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revRating, $revCategoryRating);
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}

$collegeUniversity = CollegeHelper::getUniversity($college->id);

?>

<div class="courseAndFeePage">

    <?= $this->render('partials/_header', [
        'college' => $college,
        'content' => $content,
        'pageName' => 'courses-fees',
        'accredited' => $accredited,
        'recognised' => $recognised,
        'rating' => $revRating,
        'type' => $type,
        'approval' => $approval,
        'qna' => $qna,
        'brochure' => $brochure,
        'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
        'categoryRating' => $revCategoryRating
    ]) ?>

    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'courses-fees',
    ]) ?>

    <div class="row">
        <div class="col-md-3 lg-pr-0">
            <?php if (!$isMobile): ?>
                <?= $this->render('filter', [
                    'model' => $collegeCourses['searchModel'],
                    'college' => $college,
                    'pageName' => 'courses-fees',
                    'programCount' => $programCount
                ]) ?>
            <?php endif;
            if ($college->is_google_ads == College::ADS_ACTIVE): ?>
                <aside>
                    <?= $this->render('partials/_sidebar-ads.php', [
                        'class' => 'desktopOnly',
                        'ads' => [
                            ['slot' => 'GMU_COLLEGE_COURSE_AND_FEES_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false],
                            ['slot' => 'GMU_COLLEGE_COURSE_AND_FEES_WEB_300x250_MTF_3', 'size' => '[300, 250]', 'isMobile' => false]
                        ]
                    ]) ?>
                </aside>
            <?php endif; ?>
        </div>

        <div class="col-md-9">
            <?php if ($college->is_google_ads == College::ADS_ACTIVE): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Ad::unit('GMU_COLLEGE_COURSE_AND_FEES_WAP_300x100_ATF', '[300,100]') ?>
                            <?php else: ?>
                                <?php echo Ad::unit('GMU_COLLEGE_COURSE_AND_FEES_WEB_728x90_ATF', '[728,90]') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </aside>
            <?php endif; ?>

            <?php if (!empty($collegeCourses['courses'])): ?>
                <?= $this->render('partials/_courses-table-card', [
                    'courses' => $collegeCourses['courses'],
                    'college' => $college,
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($content->content)): ?>
                <?= $this->render('partials/_main-content-card', [
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content->content))
                ]) ?>
                <?php if ($college->is_google_ads == College::ADS_ACTIVE): ?>
                    <aside>
                        <div class="horizontalRectangle desktopOnly">
                            <div class="appendAdDiv" style="background:#EAEAEA;">
                                <?php echo Ad::unit('GMU_COLLEGE_COURSE_AND_FEES_WEB_728x90_MTF_4', '[728,90]') ?>
                            </div>
                        </div>
                    </aside>
                <?php endif;
            endif; ?>

            <?php if ($isMobile): ?>
                <?= $this->render('_filter-mobile', [
                    'model' => $collegeCourses['searchModel'],
                    'college' => $college,
                    'pageName' => 'courses-fees',

                ]) ?>
            <?php endif; ?>
            <div class="courseTypeList">
                <div class="selectedFiltersDisplay row">
                    <?php if (!empty($collegeCourses['totalPrograms'])): ?>
                        <div class="summary">Total <b><?= $collegeCourses['totalPrograms']; ?></b> <?= (($collegeCourses['totalPrograms']) == 1) ? 'Item.' : 'Items.' ?></div>
                    <?php else: ?>
                        <div class="summary">No Results found.</div>
                    <?php endif; ?>
                    <?php foreach ($collegeCourses['searchModel']['selectedFilters'] as $filters): ?>
                        <div class="selectedFilters">
                            <?php
                            $filterCount = count($filters);
                            foreach ($filters as $filter):
                                $filterName = '';
                                if (key_exists($filter, $collegeCourses['searchModel']->totalFees)) {
                                    $filterName = $collegeCourses['searchModel']->totalFees[$filter];
                                } else if (key_exists($filter, $collegeCourses['searchModel']->courses)) {
                                    $filterName = $collegeCourses['searchModel']->courses[$filter];
                                } else if (key_exists($filter, $collegeCourses['searchModel']->types)) {
                                    $filterName = $collegeCourses['searchModel']->types[$filter];
                                } else if (key_exists($filter, $collegeCourses['searchModel']->streams)) {
                                    $filterName = $collegeCourses['searchModel']->streams[$filter];
                                } else if (key_exists($filter, $collegeCourses['searchModel']->degrees)) {
                                    $filterName = $collegeCourses['searchModel']->degrees[$filter];
                                    $filterName = Inflector::titleize(preg_replace('/[- 0-9]/', ' ', $filterName), true);
                                } else if (key_exists($filter, $collegeCourses['searchModel']->branches)) {
                                    $filterName = $collegeCourses['searchModel']->branches[$filter];
                                } ?>
                                <button data-slug="<?= $filter ?>"><?= $filterName ?? $filter ?><i class="spriteIcon closeIcon removeFilter"></i></button>
                            <?php endforeach; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php
                echo ListView::widget([
                    'dataProvider' => $collegeCourses['dataProvider'],
                    // 'searchModel' => $collegeCourses['searchModel'],selectedFilters
                    'itemView' => function ($model, $key, $index, $widget) use ($collegeCourses, $collegeUniversity, $brochure, $college, $isMobile, $sponsorClientUrl) {
                        $itemContent = $this->render(
                            'partials/_course-list',
                            [
                                'models' => $model,
                                'course' => $key,
                                'fullView' => true,
                                'context' => 'main-page',
                                'college' => $collegeUniversity,
                                'brochure' => $brochure,
                                'stateId' => $college->city->state->old_id,
                                'isMobile' => $isMobile,
                                'sponsorClientUrl' => $sponsorClientUrl,
                                'courseList' => $collegeCourses['courses'],
                            ]
                        );
                        return $itemContent;
                    },
                    'layout' => '{items}',
                    'emptyText' => 'No Results Found',
                ]);

                ?>
            </div>
            <?php if (!empty($coursePage)): ?>
                <?= $this->render('partials/_course-card', [
                    'courseList' => $coursePage,
                    'college' => $college
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($faqs)): ?>
                <?= $this->render('partials/_faq-card', [
                    'faqs' => $faqs,
                ]) ?>
            <?php endif; ?>

            <aside>
                <?php if ($college->is_google_ads == College::ADS_ACTIVE): ?>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Ad::unit('GMU_COLLEGE_COURSE_AND_FEES_WAP_300x250_MTF', '[300,250]') ?>
                            <?php else: ?>
                                <?php echo Ad::unit('GMU_COLLEGE_COURSE_AND_FEES_WEB_728x90_MTF_1', '[728,90]') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?= $this->render('partials/_lead-cta', [
                    'college' => $college,
                    'sponsorClientUrl' => $sponsorClientUrl
                ]) ?>

                <?php if (!empty($college->parent_id)): ?>
                    <?= $this->render('partials/_main-campus', [
                        'college' => $parentCollege,
                    ]) ?>
                <?php endif; ?>
            </aside>
            <!-- reviews widget -->
            <?php if (!empty($reviews)): ?>
                <?= $this->render('partials/_review-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                    'reviews' => $reviews,
                    'rating' => $revRating,
                    'distrbutionRating' => $revDistributionRating,
                    'categoryRating' => $revCategoryRating,
                    'viewAllURL' => Url::toCollege($college->slug, 'reviews')
                ]) ?>
            <?php endif; ?>

            <!-- other colleges under university -->
            <?php if (!empty($affiliatedCollege)): ?>
                <?= $this->render('partials/_college-card', [
                    'title' => 'Other Colleges under ' . (!empty($parentCollege->display_name) ? $parentCollege->display_name : $parentCollege->name),
                    'colleges' => $affiliatedCollege
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
                <?= $this->render('partials/_similar-college-card', [
                    'collegeByDiscipline' => $collegeByDiscipline['colleges'],
                    'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
                    'college' => $college
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
                <?= $this->render('partials/_college-card', [
                    'title' => 'Explore Nearby Colleges',
                    'colleges' => $nearByCollege,
                    'viewAllUrl' => "{$college->city->slug}"   //to do URL
                ]) ?>
            <?php endif; ?>

            <!-- Popular Colleges -->
            <?php if (!empty($popularCollege)): ?>
                <?= $this->render('partials/_college-card', [
                    'title' => 'Popular Colleges',
                    'colleges' => $popularCollege
                ])
                ?>
            <?php endif; ?>

            <!-- related article -->
            <?php if (!empty($college->article)): ?>
                <?= $this->render('../partials/_productArticleCard', [
                    'relatedArticles' => $college->article,
                ]); ?>
            <?php endif; ?>

            <!-- related News -->
            <?php if (!empty($news)): ?>
                <?= $this->render('../partials/_productNewsCard', [
                    'news' => $news,
                ]); ?>
            <?php endif;

            if ($college->is_google_ads == College::ADS_ACTIVE): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Ad::unit('GMU_COLLEGE_COURSE_AND_FEES_WAP_300x250_BTF', '[300,250]') ?>
                            <?php else: ?>
                                <?php echo Ad::unit('GMU_COLLEGE_COURSE_AND_FEES_WEB_728x90_BTF', '[728,90]') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </aside>
            <?php endif; ?>

            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
        </div>
    </div>

</div>