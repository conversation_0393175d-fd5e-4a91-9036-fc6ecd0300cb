<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\SeoExperimentHelper;
use frontend\helpers\Url;
use yii\helpers\Html;

//utils
$isMobile = Yii::$app->devicedetect->isMobile();
$this->title = !empty($seoInfo['meta_title']) ? $seoInfo['meta_title'] : $seoInfo['title'];
$this->context->description = !empty($seoInfo['meta_description']) ? $seoInfo['meta_description'] : '';
$this->context->ogImage = Url::toDefaultCollegeBanner();
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;

// breadcrumb
if (!Yii::$app->request->isAjax) {
    $this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
    if (!empty($seoInfo['h1'])) {
        $this->params['breadcrumbs'][] = ['label' => 'Colleges', 'url' => '/all-colleges', 'title' => 'Colleges'];
        $this->params['breadcrumbs'][] = $seoInfo['h1'];
    } else {
        $this->params['breadcrumbs'][] = 'Colleges';
    }
}
$this->params['entity_name'] = 'College-Listing-Page';
$this->params['entity'] = 'college-listing';

if (!empty($sponsorParams)) {
    $this->params['sponsorParams'] = $sponsorParams;
}
// page specific assets
$this->params['canonicalUrl'] =  Url::base(true) . '/' . Yii::$app->request->getPathInfo();
// $this->registerCssFile('/yas/css/version2/college.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-filter.css', ['depends' => [AppAsset::class]]);
// $this->registerCssFile('/yas/css/version2/college-filter.css', ['depends' => [AppAsset::class]]);
$this->registerCss('.blueBgDiv.mobileOnly { display: none!important; }');

$year = \Yii::$app->params['Year'];
?>

<div class="collegeListpageBody">
    <?php
    if (Yii::$app->request->isAjax):
        ?>
        <div class="ajaxBreadCrumbDiv">
            <?= $this->render('partials/_college-search-breadcrumbs', [
                'seoInfo' => $seoInfo
            ]) ?>
        </div>
    <?php endif; ?>
    <?php if (!$isMobile): ?>
        <div class="clgListHeroSection">
            <div class="h1_tooltip">
                <?php if (!empty($seoInfo['h1'])): ?>
                    <h1><?= $seoInfo['h1'] ?? '' ?></h1>
                <?php else: ?>
                    <h1>Top Colleges in India <?= $year ?> </h1>
                <?php endif; ?>
                <?php //if (!empty($currentUrl) && SeoExperimentHelper::getCollegeFilterH1Tooltip($currentUrl)) :
                ?>
                <!-- <span class="tooltiptext"><?php //SeoExperimentHelper::getCollegeFilterH1Tooltip($currentUrl)
                ?></span> -->
                <?php //endif;
                ?>
            </div>
            <?php if (!empty($seoInfo['content'])): ?>
                <div class="topContent">

                    <div class="pageData pageInfo">
                        <?= !empty($seoInfo['content']) ? html_entity_decode(ContentHelper::removeStyleTag(
                            stripslashes(DataHelper::parseDomainUrlInContent($seoInfo['content']))
                        )) : '' ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <div class="clgListHeroSection">
            <div class="topContent">
                <div class="pageData pageInfo">
                    <?php if (!empty($seoInfo['h1'])): ?>
                        <h1><?= $seoInfo['h1'] ?? '' ?></h1>
                    <?php else: ?>
                        <h1>Top Colleges in India <?= $year ?> </h1>
                    <?php endif; ?>
                    <?= !empty($seoInfo['content']) ? html_entity_decode(ContentHelper::removeStyleTag(
                        stripslashes(DataHelper::parseDomainUrlInContent($seoInfo['content']))
                    )) : '' ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!$isMobile): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                    <?php /* if ($isMobile) : ?>
                        <?php echo Ad::unit('GMU_NEW_LISTING_PAGE_WAP_300x100_ATF', '[300,100]') ?>
                    <?php else : */ ?>
                    <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                    ?>
                    <?php //endif;
                    ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>
    <div class="row">
        <div class="all-college-ajax col-md-3 lg-pr-0" <?php if (!$isMobile) {
                                                            echo  'style="min-height: 100vh;"';
                                                       } ?>>
        </div>
        <div class="col-md-9">
            <div class="sortBySection row">
                <div class="">
                </div>
                <div class="sortByList">
                    <?= Html::dropDownList('college-sort', 'rank', [
                        'position' => 'Popularity',
                        'rank' => 'Ranking',
                        'highest_fee' => 'Highest Fees',
                        'lowest_fee' => 'Lowest Fees',
                    ], ['id' => 'college-sort']) ?>
                </div>
            </div>
            <?php
            echo $this->render('partials/_college-filter-list', [
                'models' => $colleges,
                // 'elasticmodels' => $elasticData,
                // 'liveAppModels' => $liveAppColleges,
                'iRank' => $iRank ?? 1,
                'hasNext' => $hasNext,
                'page' => $page,
                'isMobile' => $isMobile,
                'searchModel' => $searchModel
            ]);
            ?>
            <div class="filter-faq">
                <?php if (!empty($faqs)): ?>
                    <?= $this->render('partials/_faq-card', [
                        'faqs' => $faqs,
                        'pageName' => '',
                        'displayName' => ''
                    ]) ?>
                <?php endif; ?>
            </div>
            <aside>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                        <?php else: ?>
                            <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                        <?php endif; ?>
                    </div>
                </div>
            </aside>
            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
        </div>
    </div>
</div>
<?php
$filCou = $searchModel->course ?? '';
$filStr = $searchModel->stream ?? '';
$ctaName = 'colleges_listing_card_predict_my_college';
$preCta = 'colleges_listing_scholarship_wap_top_sticky_cta';

?>
<?php if ($isMobile): ?>
    <div class="mobileOnly primaryBtn brochureBtn filter-college-scholership leadFilterData predict-my-college-cta" data-filter="college-listing" data-entity="college" data-lead_cta="22" data-courseslug="<?= $model->course ?? ''; ?>" data-stream="<?= $model->stream ?? ''; ?>" data-ctalocation="<?= empty($dynamicCtaFilter) && empty($dynamicCtaFilter['cta_position_3']) || empty(array_filter($dynamicCtaFilter['cta_position_3'])) ? $preCta : ($dynamicCtaFilter['cta_position_3']['wap'] ?? $preCta) ?>"></div>

<?php else: ?>
    <div class="desktopOnly getSupport college-filter-get-support">
        <!-- <p class="getSupport__subheading">Your Dream College Awaits!</p> -->
        <div class="primaryBtn brochureBtn filter-college-scholership leadFilterData predict-my-college-cta" data-filter="college-listing" data-entity="college" data-lead_cta="22" data-courseslug="<?= $filCou ?? ''; ?>" data-stream="<?= $filStr ?? ''; ?>" data-ctalocation="<?= empty($dynamicCtaFilter) && empty($dynamicCtaFilter['cta_position_3']) || empty(array_filter($dynamicCtaFilter['cta_position_3'])) ? $ctaName : ($dynamicCtaFilter['cta_position_3']['web'] ?? $ctaName) ?>"></div>
    </div>
<?php endif; ?>