<?php

use common\models\College;
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;

$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'scholarships');
$defaultTitle = $college->display_name . ' Scholarships: Eligibility & Amount';
$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();
$authorImage = !empty($content->author->profile->image) ? (Yii::getAlias('@profileDPFrontend') . '/' . $content->author->profile->image) : '/yas/images/usericon.png';

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $city->name, 'url' => [$url], 'title' => 'Colleges in ' . $city->name];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' => !empty($college->display_name) ? $college->display_name : $college->name];
$this->params['breadcrumbs'][] = 'Scholarships';

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image',  'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image',  'fetchpriority' => 'high']);
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');


//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $college->city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'scholarships';

// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);
$courses = !empty($courses) ? CollegeHelper::getNonEmptyCourse($courses, 'scholarships') : [];

?>

<!-- header -->
<?= $this->render('partials/_header-new', [
    'menus' => $menus ?? [],
    'college' => $college,
    'content' => $content,
    'pageName' => 'scholarships',
    'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
    'type' => $type,
    'approval' => $approval,
    'brochure' => $brochure,
    'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
    'categoryRating' => $revCategoryRating,
    'defaultTitle' => $defaultTitle,
    'reviewCount' => $reviewCount,
    'city' => $city,
    'state' => $state,
    'author' => $authorDetail,
    'profile' => $profile,
    'dynamicCta' => $dynamicCta,
    'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
]) ?>

<?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_notification', [
    'sponsorClientUrl' => $sponsorClientUrl,
    'college' => $college,
]) ?>
<?php endif;*/ ?>
<!-- page specific navigation -->
<?= $this->render('partials/_menu-card', [
    'menus' => $menus,
    'college' => $college,
    'pageName' => 'scholarships',
    'dropdown' => $dropdowns
]) ?>

<div class="subPage">
    <div class="row">
        <div class="col-md-8">
            <?php if (!empty($collegeNotificationUpdate)): ?>
                <div class="pageData <?= count($collegeNotificationUpdate) > 5  ? 'pageInfo' : '' ?>">
                    <div class="infoPage">
                        <?= $this->render('../partials/_collegeNotificationUpdate', [
                            'collegeNotificationUpdate' => $collegeNotificationUpdate,
                            'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Scholarships',
                            'isShowTitle' => true
                        ]) ?>
                    </div>
                </div>
            <?php endif; ?>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && !$isMobile): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php echo Ad::unit('GMU_COLLEGE_SCHOLARSHIP_WEB_728x90_ATF', '[728,90]') ?>
                        </div>
                    </div>
                </aside>
            <?php endif;
            if (!empty($content->content)): ?>
                <?php
                echo $this->render('partials/_main-content-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Scholarships',
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content->content)),
                    'contentAuthor' => $content,
                    'author' => $authorDetail,
                    'profile' => $profile,
                    'removeShowMore' => true,
                    'recentActivity' => $recentActivity,
                    'examContentTemplate' => $examContentTemplate,
                    'collegeNotificationUpdate' => $collegeNotificationUpdate,

                ]) ?>
            <?php endif;
            if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && !$isMobile): ?>
                <aside>
                    <div class="horizontalRectangle desktopOnly">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                        </div>
                    </div>
                </aside>
            <?php endif;
            if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
                <div id="liveApplicationForm"></div>
            <?php endif; ?>

            <?php if (!empty($faqs)): ?>
                <?= $this->render('partials/_faq-card', [
                    'faqs' => $faqs,
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Scholarship FAQs',
                    'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                    'pageName' => 'Scholarships'
                ]) ?>
                <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <aside>
                        <div class="horizontalRectangle">
                            <div class="appendAdDiv" style="background:#EAEAEA;">
                                <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280') ?>
                            </div>
                        </div>
                    </aside>
                <?php endif;
            endif; ?>
            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
            <?php if (!empty($scholarshipCasteInfo)): ?>
                <div class="pageData collegeRankings">
                    <h2><?= $college->display_name ?? $college->name ?> Scholarship Details</h2>
                    <div class="table-responsive">
                        <table class="courseFeesTable rankTable">
                            <thead>
                                <tr>
                                    <td>Gender</td>
                                    <?php foreach ($scholarshipCasteInfo['header'] as $category): ?>
                                        <th><?= $category ?></th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($scholarshipCasteInfo['data'] as $kgen => $vgen): ?>
                                    <tr class="categoryCount" style="border: 1px solid #eaeaea;">
                                        <td><?= $kgen ?></td>
                                        <?php
                                        $leftCol = count($scholarshipCasteInfo['header']) - count($vgen);
                                        if ($leftCol == 0):
                                            foreach ($vgen as $k => $v): ?>
                                                <td><?= $v ?></td>
                                            <?php endforeach;
                                        else:
                                            for ($i = 0; $i < $leftCol; $i++):
                                                ?>
                                                <td style="text-align: center;">-</td>
                                            <?php endfor;
                                            foreach ($vgen as $k => $v): ?>
                                                <td><?= $v ?></td>
                                            <?php endforeach;
                                        endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>
            <?php if (!empty($courses)): ?>
                <div class="pageData scholarshipTable">
                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Check Fees Details</h2>
                    <table class="courseFeesTable">
                        <thead>
                            <tr>
                                <td>Course Name</td>
                                <td>Avg Course Fees</td>
                                <td>Exams Accepted</td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1;
                            foreach ($courses as $key => $value):
                                if ($count < 11):
                                    ?>
                                    <tr>
                                        <?php if (!empty($value['coursePage'])): ?>
                                            <td>
                                                <a href="<?= Url::base() . $value['coursePage'] ?>"><?= $value['short_name'] ?? $value['name'] ?></a>
                                            </td>
                                        <?php else: ?>
                                            <td>
                                                <p><?= $value['short_name'] ?? $value['name'] ?></p>
                                            </td>
                                        <?php endif; ?>
                                        <?php if (!empty($value['avgFees'])): ?>
                                            <td>
                                                <?= '₹' . ContentHelper::indMoneyFormat($value['avgFees']) ?> <?= ($isMobile) ? '<br/>' : '' ?>
                                                <span style="color:#787878!important;font-weight: 400;<?= $isMobile ? 'font-size:12px' : '' ?>">(<?= !empty($value['duration']) ? CollegeHelper::yearsFormat($value['duration']) : '--' ?>)</span>
                                            </td>
                                        <?php else: ?>
                                            <td>--</td>
                                        <?php endif ?>
                                        <?php if (!empty($value['exams'])): ?>
                                            <td>
                                                <?php foreach (array_keys($value['exams']) as $key => $val):  ?>
                                                    <?php $exams = explode(',', $val);
                                                    $arr = [];
                                                    foreach ($exams as $exm) {
                                                        $arr[] = $exm;
                                                    }
                                                    $examArr = array_unique($arr);
                                                endforeach;
                                                if (!empty($examArr)): ?>
                                                    <span class="moreCourse">
                                                        <?php foreach ($examArr as $key => $val): ?>
                                                            <span class="<?= $key > 5 ? 'hideExam ' . $value['slug'] . '_hideExam' : '' ?>"><?= $val ?></span>
                                                        <?php endforeach; ?>
                                                    </span>
                                                    <?php if (count($examArr) > 5): ?>
                                                        <span class="examMore <?= $value['slug'] ?>Exam" data-id="<?= $value['slug'] ?>">+<?= count($examArr) - 4 ?></span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </td>
                                        <?php else: ?>
                                            <td>-</td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endif;
                                $count++;
                            endforeach; ?>
                        </tbody>
                    </table>
                    <?php if (!is_numeric($menus['courses-fees'])): ?>
                        <div class="moreCoursesLinkContainer">
                            <a class="moreCoursesLink" title="<?= CollegeHelper::subPageTitle($college, 'courses-fees') ?>" href="<?= Url::toCollege($college->slug, 'courses-fees') ?>">
                                More Courses<span class="spriteIcon urlIcon"></span>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- reviews widget -->
            <?php if (!empty($reviews)): ?>
                <?= $this->render('partials/_review-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                    'reviews' => $reviews,
                    'distrbutionRating' => $revDistributionRating,
                    'categoryRating' => $revCategoryRating,
                    'collegeId' => $college->id,
                    'viewAllURL' => !empty($menus) && !is_numeric($menus['reviews']) ? Url::toCollege($college->slug, 'reviews') : '',
                    'reviewCount' => $reviewCount ?? '',
                    'category' => 2
                ]) ?>
            <?php endif; ?>
        </div>

        <div class="col-md-4 noSticky">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="getSupport">
                        <!-- <div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Are You Interested In This College?</p>
                        </div> -->
                        <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
                    </div>
                <?php endif; ?>
                <?php if (!empty($college->parent_id)): ?>
                    <?= $this->render('partials/_main-campus', [
                        'college' => $parentCollege,
                    ]) ?>
                <?php endif; ?>

                <?php if ($liveApplicationForm && !$isMobile && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div id="liveApplicationForm"></div>
                <?php endif; ?>

                <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif;*/ ?>
            </aside>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <?= $this->render('partials/_sidebar-ads.php', [
                    'class' => 'desktopOnly',
                    'ads' => [
                        ['slot' => 'GMU_COLLEGE_SCHOLARSHIP_WEB_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => false],
                        ['slot' => 'GMU_COLLEGE_SCHOLARSHIP_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false]
                    ]
                ]) ?>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!empty($forums)): ?>
        <?= $this->render('partials/_forum-card', [
            'forums' => $forums,
            'viewAllUrl' => $college->slug
        ]) ?>
    <?php endif; ?>

    <!-- other colleges under university -->
    <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
            'colleges' => $affiliatedCollege,
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
        <?= $this->render('partials/_similar-college-card', [
            'collegeByDiscipline' => $collegeByDiscipline['colleges'],
            'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
            'college' => $college
        ]) ?>
    <?php endif; ?>

    <!-- Nearby colleges -->
    <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Explore Nearby Colleges',
            'colleges' => $nearByCollege,
            'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>

    <!-- Popular Colleges -->
    <?php if (!empty($popularCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Popular Colleges',
            'colleges' => $popularCollege,
            'city' => $city,
            'state' => $state
        ])
        ?>
    <?php endif; ?>

    <!-- related article -->
    <?php if (!empty($article)): ?>
        <?= $this->render('../partials/_productArticleCard', [
            'relatedArticles' => $article,
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($news)): ?>
        <?= $this->render('../partials/_productNewsCard', [
            'news' => $news,
        ]); ?>
    <?php endif; ?>

    <!-- advertisement -->
    <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>
</div>
<?php /*if (!empty($sponsorClientUrl->redirection_link)): ?>
    <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college
    ]) ?>
<?php  endif;*/ ?>