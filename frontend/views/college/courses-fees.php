<?php

use common\models\College;
use common\helpers\ContentHelper;
use common\helpers\CollegeHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use yii\widgets\ListView;

$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(
    !empty($college->display_name) ? $college->display_name : $college->name,
    'courses-fees',
    [],
    [
        'fees' => $collegeCourses['courses'],
        'course-count' => (!empty($collegeCourses['courses']) && count($collegeCourses['courses']) !== 0 ? count($collegeCourses['courses']) : ''),
        'program-count' => (!empty($collegeCourses['totalPrograms']) && $collegeCourses['totalPrograms'] !== 0 ? $collegeCourses['totalPrograms'] : ''),
    ]
);

$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();


$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = !empty($content->author->profile->image) ? (Yii::getAlias('@profileDPFrontend') . '/' . $content->author->profile->image) : '/yas/images/usericon.png';
// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], (!empty($college->city) ? $college->city->slug : ''), '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($college->city) ? $college->city->slug : '') : '';
if (!empty($url) && !empty($college->city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $college->city->name, 'url' => ['/all-colleges/' . $college->city->slug], 'title' => 'Colleges in ' . $college->city->name];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' =>  !empty($college->display_name) ? $college->display_name : $college->name];
$this->params['breadcrumbs'][] = 'Courses and Fees';

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['applyNowDiv'] = !empty($sponsorClientUrl->redirection_link) ? 'courses-fees' : '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'courses-fees';

// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}

$collegeUniversity = CollegeHelper::getUniversity($college->id);
$courses = !empty($collegeCourses['courses']) ? CollegeHelper::getNonEmptyCourse($collegeCourses['courses'], 'courses-fees') : [];

?>
<div class="courseAndFeePage">
    <?= $this->render('partials/_header-new', [
        'menus' => $menus ?? [],
        'college' => $college,
        'content' => $content,
        'pageName' => 'courses-fees',
        'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
        'type' => $type,
        'approval' => $approval,
        'brochure' => $brochure,
        'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
        'categoryRating' => $revCategoryRating,
        'city' => $city,
        'state' => $state,
        'reviewCount' => $reviewCount,
        'author' => $authorDetail,
        'profile' => $profile,
        'dynamicCta' => isset($dynamicCta) && !empty($dynamicCta) ? $dynamicCta : [],
        'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
    ]) ?>

    <?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
            <?= $this->render('partials/_notification', [
            'sponsorClientUrl' => $sponsorClientUrl,
            'college' => $college,
        ]) ?>

        <?php endif;*/ ?>

    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'courses-fees',
        'dropdown' => $dropdowns
    ]) ?>

    <div class="row">
        <div class="col-md-8">
            <?php if (!empty($collegeNotificationUpdate)): ?>
                <div class="pageData <?= count($collegeNotificationUpdate) > 5  ? 'pageInfo' : '' ?>">
                    <div class="infoPage">
                        <?= $this->render('../partials/_collegeNotificationUpdate', [
                            'collegeNotificationUpdate' => $collegeNotificationUpdate,
                            'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                            'isShowTitle' => true
                        ]) ?>
                    </div>
                </div>
            <?php endif; ?>
            <?php //if (in_array($college->slug, CollegeHelper::$_collegeSlugCourseFeesTablePage)) {
            ?>
            <?php //if (!empty($courses) && $coursesAvgFeesCount != 0):
            ?>
            <?php /*$this->render('partials/_courses-table-card-popup', [
                        'courses' => $courses,
                        'college' => $college,
                        'content' => $content,
                        'author' => $authorDetail,
                        'profile' => $profile,
                        'showAuthor' => !empty($courses && $coursesAvgFeesCount != 0) ? false : true
                    ]) */ ?>
            <?php //endif;
            ?>
            <?php // } else {
            ?>
            <?= $this->render('partials/_courses-table-card', [
                'courses' => $courses,
                'college' => $college,
                'content' => $content,
                'author' => $authorDetail,
                'profile' => $profile,
                'showAuthor' => !empty($courses && $coursesAvgFeesCount != 0) ? false : true
            ]) ?>
            <?php //}
            ?>
            <?php if (!empty($content->content)): ?>
                <?= $this->render('partials/_main-content-card', [
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content->content)),
                    'recentActivity' => $recentActivity,
                    'contentAuthor' => $content,
                    'author' => $authorDetail,
                    'profile' => $profile,
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                    'showAuthor' => !empty($courses && $coursesAvgFeesCount != 0) ? false : true,
                    'examContentTemplate' => $examContentTemplate,
                    'collegeNotificationUpdate' => $collegeNotificationUpdate,

                ]) ?>
                <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && !$isMobile): ?>
                    <aside>
                        <div class="horizontalRectangle desktopOnly">
                            <div class="appendAdDiv" style="background:#EAEAEA;">
                                <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                            </div>
                        </div>
                    </aside>
                <?php endif;
            endif; ?>

            <?php if (!empty($contentTemplate)): ?>
                <?= $this->render('partials/_main-content-card', [
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($contentTemplate)),
                    'recentActivity' => $recentActivity,
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                    'examContentTemplate' => $examContentTemplate,
                    'collegeNotificationUpdate' => $collegeNotificationUpdate,

                ]) ?>
                <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && !$isMobile): ?>
                    <aside>
                        <div class="horizontalRectangle desktopOnly">
                            <div class="appendAdDiv" style="background:#EAEAEA;">
                                <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__728x90 __336x280') ?>
                            </div>
                        </div>
                    </aside>
                <?php endif;
            endif; ?>

            <section class="filterSection filtersAll">
                <h2 class="filterHeading"><?= (!empty($college->display_name) ? $college->display_name : $college->name) ?> Courses and Fee Structure</h2>
                <?= $this->render('_filter-new', [
                    'model' => $collegeCourses['searchModel'],
                    'college' => $college,
                    'pageName' => 'courses-fees',
                    'programList' => $collegeCourses['programList'],
                ]) ?>
            </section>
            <div class="courseTypeList">
                <?php
                echo ListView::widget([
                    'dataProvider' => $collegeCourses['dataProvider'],
                    'itemView' => function ($model, $key, $index, $widget) use ($collegeCourses, $collegeUniversity, $brochure, $college, $isMobile, $sponsorClientUrl, $dynamicCta) {
                        $itemContent = $this->render(
                            'partials/_course-list-new',
                            [
                                'models' => $model,
                                'course' => $key,
                                'fullView' => true,
                                'context' => 'main-page',
                                'college' => $collegeUniversity,
                                'brochure' => $brochure,
                                'stateId' => !empty($college->city) ? ($college->city->state->old_id ?? '') : '',
                                'isMobile' => $isMobile,
                                'courseList' => $collegeCourses['courses'],
                                'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
                                'dynamicCta' => $dynamicCta
                            ]
                        );
                        return $itemContent;
                    },
                    'layout' => '{items}',
                    'emptyText' => 'No Results Found',
                ]);
                ?>
            </div>

            <?php if ($cutOff['dataProvider']->allModels): ?>
                <?= $this->render('partials/_college_course_cutoff', [
                    'cutOff' => $cutOff,
                    'college' => $college,
                    'models' => $cutOff['dataProvider']->allModels,
                    'state' => $state,
                    'isMobile' => $isMobile,
                    'page' => 'course-fee'
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($faqs)): ?>
                <?= $this->render('partials/_faq-card', [
                    'faqs' => $faqs,
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Courses & Fees FAQs',
                    'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                    'pageName' => 'Courses and Fees'
                ]) ?>
            <?php endif; ?>

            <aside>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                    </div>
                </div>
            </aside>
            <?php if (!empty($reviews)): ?>
                <?= $this->render('partials/_review-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                    'reviews' => $reviews,
                    'distrbutionRating' => $revDistributionRating,
                    'collegeId' => $college->id,
                    'categoryRating' => $revCategoryRating,
                    'viewAllURL' => !empty($menus) && !is_numeric($menus['reviews']) ? Url::toCollege($college->slug, 'reviews') : '',
                    'reviewCount' => $reviewCount,
                    'category' => 2
                ]) ?>
            <?php endif; ?>
        </div>

        <aside class="col-md-4 sideWidgets">
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="lead-cta lead-cta-cls" data-lead_cta="20" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
            <?php endif; ?>
            <?php if (!empty($college->parent_id)): ?>
                <?= $this->render('partials/_main-campus', [
                    'college' => $parentCollege,
                ]) ?>
            <?php endif; ?>

            <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                    'featured' => $featuredNews,
                    'recents' => $recentNews,
                    'isAmp'  => 0,
                    'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                    'smallIcone' => 1
                ]); ?>
            <?php endif; ?>

            <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                    'trendings' => $trendingArticles,
                    'recentArticles' => $recentArticles,
                ]); ?>
            <?php endif;*/ ?>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <?= $this->render('partials/_sidebar-ads.php', [
                    'class' => 'desktopOnly',
                    'ads' => [
                        ['slot' => 'GMU_COLLEGE_CUT_OFF_WEB_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => false],
                        ['slot' => 'GMU_COLLEGE_CUT_OFF_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false]
                    ]
                ]) ?>
            <?php endif; ?>
        </aside>

        <div class="col-md-12">

            <!-- other colleges under university -->
            <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
                <?= $this->render('partials/_college-card', [
                    'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
                    'colleges' => $affiliatedCollege,
                    'city' => $city,
                    'state' => $state
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
                <?= $this->render('partials/_similar-college-card', [
                    'collegeByDiscipline' => $collegeByDiscipline['colleges'],
                    'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
                    'college' => $college
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
                <?= $this->render('partials/_college-card', [
                    'title' => 'Explore Nearby Colleges',
                    'colleges' => $nearByCollege,
                    'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
                    'city' => $city,
                    'state' => $state
                ]) ?>
            <?php endif; ?>

            <!-- Popular Colleges -->
            <?php if (!empty($popularCollege)): ?>
                <?= $this->render('partials/_college-card', [
                    'title' => 'Popular Colleges',
                    'colleges' => $popularCollege,
                    'city' => $city,
                    'state' => $state
                ])
                ?>
            <?php endif; ?>

            <!-- related article -->
            <?php if (!empty($article)): ?>
                <?= $this->render('../partials/_productArticleCard', [
                    'relatedArticles' => $article,
                ]); ?>
            <?php endif; ?>

            <!-- related News -->
            <?php if (!empty($news)): ?>
                <?= $this->render('../partials/_productNewsCard', [
                    'news' => $news,
                ]); ?>
            <?php endif;

            if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                            <?php else: ?>
                                <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </aside>
            <?php endif; ?>

            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
        </div>
    </div>
</div>
<?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college
    ]) ?>
<?php  endif;*/ ?>