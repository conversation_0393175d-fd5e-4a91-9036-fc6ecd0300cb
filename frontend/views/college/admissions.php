<?php

use common\models\College;
use common\helpers\ContentHelper;
use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;

$courses = !empty($collegeCourseHighlights) ? CollegeHelper::getNonEmptyCourse($collegeCourseHighlights, 'admission') : [];

if (empty($subPageType)) {
    $defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'admission', $menus, ['courses-offered' => !empty($courses) ? $courses : []]);
} else {
    $defaultSeoInfo = ContentHelper::getCollegeDefaultSubPageSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'admission', $subPageType);
}
$defaultTitle = $college->display_name . ' Admission ' . date('Y') . ': Eligibility, Application Process & Dates';
$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();
$isMobile = \Yii::$app->devicedetect->isMobile();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . (!empty($city) ? $city->name : ''), 'url' => [$url], 'title' => 'All Colleges ' . ucfirst($city->name)];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' => !empty($college->display_name) ? $college->display_name : $college->name];

if (!empty($subPageType)) {
    $admissionSlug = 'college/' . $college->slug . '/admission';
    $this->params['breadcrumbs'][] = ['label' => 'Admission', 'url' => [$admissionSlug], 'title' => 'Admission'];
    $this->params['breadcrumbs'][] = $faqPageName = $subPageType . ' Admission';
} else {
    $this->params['breadcrumbs'][] = $faqPageName = 'Admission';
}

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
if (!empty($subPageType)) {
    $this->params['pageName'] = 'admission_' . strtolower($subPageType);
} else {
    $this->params['pageName'] = 'admission';
}


$loadFaq = [];

// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}

?>

<?= $this->render('partials/_header-new', [
    'menus' => $menus ?? [],
    'college' => $college,
    'content' => $content,
    'pageName' => 'admission',
    'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
    'type' => $type,
    'approval' => $approval,
    'brochure' => $brochure,
    'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
    'defaultTitle' => $defaultTitle,
    'categoryRating' => $revCategoryRating,
    'state' => $state,
    'city' => $city,
    'reviewCount' => $reviewCount,
    'author' => $authorDetail,
    'profile' => $profile,
    'dynamicCta' => isset($dynamicCta) && !empty($dynamicCta) ? $dynamicCta : [],
    'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : ''
]) ?>
<?php /* if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_notification', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college,
        ]) ?>
<?php endif; */ ?>
<?= $this->render('partials/_menu-card', [
    'menus' => $menus,
    'college' => $college,
    'pageName' => 'admission',
    'dropdown' => $dropdowns,
    'type' => empty($subPageType) ? null : $subPageType
]) ?>

<div class="row">
    <div class="col-md-8">
        <?php if ($college->is_google_ads == College::ADS_ACTIVE && !$isMobile): ?>
            <aside>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                        <?php echo Ad::unit('GMU_COLLEGE_ADMISSIONS_WEB_728x90_ATF', '[728,90]') ?>
                    </div>
                </div>
            </aside>
        <?php endif;
        if (!empty($collegeNotificationUpdate)) { ?>
            <div class="pageData <?= count($collegeNotificationUpdate) > 5  ? 'pageInfo' : '' ?>">
                <div class="infoPage">
                    <?= $this->render('../partials/_collegeNotificationUpdate', [
                        'collegeNotificationUpdate' => $collegeNotificationUpdate,
                        'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                        'isShowTitle' => true
                    ]) ?>
                </div>
            </div>
        <?php }

        if (!empty($content->content)): ?>
            <div class="pageData admissionPageData">
                <?= $this->render('partials/_author-detail-mobile', [
                    'content' => $content ?? [],
                    'author' => $authorDetail ?? [],
                    'profile' => $profile ?? []
                ]);
                ?>
                <?php /*<h2><?= (!empty($college->display_name) ? $college->display_name : $college->name) . ' Admission' ?></h2>*/ ?>
                <?php if (!empty($examContentTemplate[0])) { ?>
                    <?= $this->render('../partials/_examContentTemplate', [
                        'examContentTemplate' => $examContentTemplate,
                        'collegeNotificationUpdate' => $collegeNotificationUpdate,
                        'isShowTitle' => true
                    ]) ?>
                <?php } elseif (!empty($recentActivity) && isset($recentActivity[0]) && !empty($recentActivity[0])) { ?>
                    <?= $this->render('../partials/_recentActivity', [
                        'recentActivity' => $recentActivity,
                        'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                        'collegeNotificationUpdate' => $collegeNotificationUpdate,
                        'isShowTitle' => false
                    ]) ?>
                <?php }  ?>

                <?= CollegeHelper::parseContent(stripslashes(
                    ContentHelper::htmlDecode(DataHelper::parseDomainUrlInContent($content->content))
                )); ?>
            </div>
        <?php endif; ?>

        <?php /* if ($college->is_google_ads == College::ADS_ACTIVE && $isMobile): ?>
            <aside>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                        <?php echo Ad::unit('GMU_COLLEGE_ADMISSIONS_WAP_300x100_ATF', '[300,100]') ?>
                    </div>
                </div>
            </aside>
        <?php endif; */ ?>

        <!-- Admission Highlights section starts -->
        <?php if ($subPageType != 'UG' && $subPageType != 'PG' && !empty($courses)): ?>
            <?= $this->render('partials/_admission-highlights', [
                'collegeCourses' => $courses,
                'college' => $college,
                'menus' => $menus
            ]) ?>
            <!-- Admission Highlights section ends -->
            <?php
            if ($college->is_google_ads == College::ADS_ACTIVE && $isMobile): ?>
                <aside>
                    <div class="horizontalRectangle mobileOnly">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280') ?>
                        </div>
                    </div>
                </aside>
            <?php endif;
        endif;
        if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO): ?>
            <div id="liveApplicationForm"></div>
        <?php endif; ?>

        <?php if (!empty($cutOff['dataProvider']->allModels)): ?>
            <?= $this->render('partials/_college_course_cutoff', [
                'cutOff' => $cutOff,
                'college' => $college,
                'models' => $cutOff['dataProvider']->allModels,
                'state' => $state,
                'isMobile' => $isMobile,
                'page' => 'admission'
            ]) ?>
        <?php endif; ?>

        <?php if (!empty($faqs)): ?>
            <?= $this->render('partials/_faq-card', [
                'faqs' => $faqs,
                'college' => $college,
                'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Admission FAQs',
                'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                'pageName' => $faqPageName
            ]) ?>
        <?php endif;
        if ($college->is_google_ads == College::ADS_ACTIVE && !$isMobile): ?>
            <aside>
                <div class="horizontalRectangle desktopOnly">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                    </div>
                </div>
            </aside>
        <?php endif; ?>
        <div class="removeFixedQuickLink">
            <!-- Do not Delete this -->
        </div>
        <!-- reviews widget -->
        <?php if (!empty($reviews)): ?>
            <?= $this->render('partials/_review-card', [
                'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                'reviews' => $reviews,
                'distrbutionRating' => $revDistributionRating,
                'categoryRating' => $revCategoryRating,
                'collegeId' => $college->id,
                'viewAllURL' => !empty($menus) && !is_numeric($menus['reviews']) ? Url::toCollege($college->slug, 'reviews') : '',
                'reviewCount' => $reviewCount ?? '',
                'category' => 1
            ]) ?>
        <?php endif; ?>
    </div>

    <div class="col-md-4">
        <aside>
            <div class="getSupport">
                <!-- <div class="row">
                    <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                    <p>Are You Interested In This College?</p>
                </div> -->
                <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
            </div>

            <?php if (!empty($college->parent_id)): ?>
                <?= $this->render('partials/_main-campus', [
                    'college' => $parentCollege,
                ]) ?>
            <?php endif; ?>

            <?php if ($liveApplicationForm && !$isMobile && $college->is_sponsored == College::SPONSORED_NO): ?>
                <div id="liveApplicationForm"></div>
            <?php endif; ?>

            <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                    'featured' => $featuredNews,
                    'recents' => $recentNews,
                    'isAmp'  => 0,
                    'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                    'smallIcone' => 1
                ]); ?>
            <?php endif; ?>

            <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                    'trendings' => $trendingArticles,
                    'recentArticles' => $recentArticles,
                ]); ?>
            <?php endif; */ ?>
        </aside>
        <?php if ($college->is_google_ads == College::ADS_ACTIVE): ?>
            <?= $this->render('partials/_sidebar-ads.php', [
                'ads' => [
                    ['slot' => 'GMU_COLLEGE_ADMISSIONS_WEB_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => false],
                    ['slot' => 'GMU_COLLEGE_ADMISSIONS_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false],
                    ['slot' => 'GMU_COLLEGE_ADMISSIONS_WAP_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => true],
                    //['slot' => 'GMU_COLLEGE_ADMISSIONS_WAP_300x250_MTF_2', 'size' => '[300, 250]','isMobile'=>  true]
                ]
            ]) ?>
        <?php endif; ?>
    </div>
</div>

<?php if (!empty($forums)): ?>
    <?= $this->render('partials/_forum-card', [
        'forums' => $forums,
        'viewAllUrl' => $college->slug
    ]) ?>
<?php endif; ?>

<?php if (!empty($college->latitude) && !empty($college->longitude)): ?>
    <?= $this->render('partials/_contact-card', [
        'college' => $college,
    ]) ?>
<?php endif; ?>

<!-- other colleges under university -->
<?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
    <?= $this->render('partials/_college-card', [
        'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
        'colleges' => $affiliatedCollege,
        'city' => $city,
        'state' => $state
    ]) ?>
<?php endif; ?>

<?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
    <?= $this->render('partials/_similar-college-card', [
        'collegeByDiscipline' => $collegeByDiscipline['colleges'],
        'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
        'college' => $college
    ]) ?>
<?php endif; ?>

<?php if ($college->is_google_ads == College::ADS_ACTIVE && $isMobile): ?>
    <?=
    $this->render('partials/_sidebar-ads.php', [
        'ads' => [
            ['slot' => 'GMU_COLLEGE_ADMISSIONS_WAP_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' =>  true]
        ]
    ])
    ?>
<?php endif; ?>

<?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
    <?= $this->render('partials/_college-card', [
        'title' => 'Explore Nearby Colleges',
        'colleges' => $nearByCollege,
        'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
        'city' => $city,
        'state' => $state
    ]) ?>
<?php endif; ?>

<!-- Popular Colleges -->
<?php if (!empty($popularCollege)): ?>
    <?= $this->render('partials/_college-card', [
        'title' => 'Popular Colleges',
        'colleges' => $popularCollege,
        'city' => $city,
        'state' => $state
    ])
    ?>
<?php endif; ?>

<!-- related article -->
<?php if (!empty($article)): ?>
    <?= $this->render('../partials/_productArticleCard', [
        'relatedArticles' => $article,
    ]); ?>
<?php endif; ?>

<!-- related News -->
<?php if (!empty($news)): ?>
    <?= $this->render('../partials/_productNewsCard', [
        'news' => $news,
    ]); ?>
<?php endif;

if ($college->is_google_ads == College::ADS_ACTIVE): ?>
    <aside>
        <div class="horizontalRectangle">
            <div class="appendAdDiv" style="background:#EAEAEA;">
                <?php if ($isMobile): ?>
                    <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                <?php else: ?>
                    <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                <?php endif; ?>
            </div>
        </div>
    </aside>
<?php endif; ?>
<?php /* if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college
    ]) ?>
<?php  endif;*/ ?>