<?php

use common\models\College;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use common\helpers\CollegeHelper;
use frontend\helpers\Url;
use common\services\UserService;
use common\models\Lead;
use common\models\LeadBucketTagging;
use justinvoelker\separatedpager\LinkPager;

$this->title = 'College Admission 2025 - Find Top Colleges & Universities in India';
$this->context->description = 'Get detailed information on college admission process for top colleges & universities in India.';
$this->context->ogImage = Url::toDefaultCollegeBanner();
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;

$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'Admissions', 'title' => 'Admissions'];

$this->params['entity_name'] = 'College-Admission-Page';
$this->params['entity'] = 'college-admissions';

$this->registerCssFile(Yii::$app->params['cssPath'] . 'admission-list.css', ['depends' => [AppAsset::class]]);

$isMobile = \Yii::$app->devicedetect->isMobile();
$i = !empty($iRank) ? $iRank : 1;
$firstSix = 0;
?>

    <div class="admission__landing__hero">
        <div class="admission__search__container">
            <h1>College Admission</h1>
            <h2>Search for Colleges in India</h2>
            <div class="searchBar">
                <input class="searchForBoard search-autocomplete-admission" data-type="college" id="autoComplete" autocomplete="off"
                    placeholder="Search Colleges in India" type="text" tabindex="1">
                <div class="selection">
                    <ul id="searchResults" class="ui-menu ui-widget ui-widget-content ui-autocomplete ui-front category"></ul>
                </div>
            </div>
        </div>
    </div>
    <main class="admission__landing__container">
        <?php foreach ($collegeList as $key => $college):
                $firstSix++;
                $dynamicCta = UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGE_ADMISSION, 'college-admission', $college['display_name'] ?? $college['name'], $college['college_slug'], $college['city_name']);
                $default1 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_0']) || empty(array_filter($dynamicCta['cta_position_0'])));
                $default2 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])));
            ?>
        <div class="collegeInfoCard">
            <div class="clgInfoCardHeader <?= ($firstSix > 6) ? 'lazyload' : '' ?>" style="background-image: url(<?php echo !empty($college['cover_image']) ? Url::getCollegeBannerImage($college['cover_image']) : Url::toDefaultCollegeBanner() ?>);" onclick="gmu.url.goto('<?= Url::toRoute($college['college_slug']) . '/admission' ?>')">
                <div class="row">
                    <div class="collegeLogo">
                        <img width="56" height="56" class="<?= ($firstSix > 6) ? 'lazyload' : '' ?>" loading="<?= ($firstSix > 7) ? 'lazy' : '' ?>" src="<?= !empty($college['logo_image']) ? Url::getCollegeLogo($college['logo_image']) : Url::defaultCollegeLogo() ?>" onclick="gmu.url.goto('<?= Url::toRoute($college['college_slug']) . '/admission' ?>')" alt="">
                    </div>
                    <?php if (!$isMobile): ?>
                        <h3 class="collegeName"><a href="<?= Url::toRoute($college['college_slug']) . '/admission' ?>" title="<?= $college['display_name'] . ' Admission 2025' ?? $college['name'] . ' Admission 2025' ?>"><?= $college['display_name'] . ' Admission 2025' ?? $college['name'] . ' Admission 2025' ?></a></h3>
                    <?php endif; ?>
                </div>
            </div>
            <div class="aboutCollege">
                <?php if ($isMobile): ?>
                    <h3 class="collegeName"><a href="<?= Url::toRoute($college['college_slug']) . '/admission' ?>" title="<?= $college['display_name'] . ' Admission 2025' ?? $college['name'] . ' Admission 2025' ?>"><?= $college['display_name'] . ' Admission 2025' ?? $college['name'] . ' Admission 2025' ?></a></h3>
                <?php endif; ?>
                <p class="collegeLocation"><i class="spriteIcon locationIconBlue"></i><?= $college['city_name'] ?>, <?= $college['state_name'] ?></p>
            </div>
            <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                <div class="clgInfoCardfooter">
                    <div class="lead-cta-college-filter-1 leadFilterData" data-lead_cta="25" data-entity="college" data-filter="college-admission" data-entity_id="<?= $college['college_id'] ?>" data-stateId="<?= $college['state_id'] ?>" data-cityId="<?= $college['city_id'] ?>"  
                        data-slug="<?= $college['college_id'] ?>" data-ctaLocation="<?= $isMobile ? UserService::parseDynamicCta('colleges_admission_{slug}_wap_card_left_cta', '', $college['college_slug']) : UserService::parseDynamicCta('colleges_admission_{slug}_web_card_left_cta', '', $college['college_slug']) ?>" 
                        data-title="<?= $default1 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO APPLY') ?>" data-description="<?= $default1 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_0']['lead_form_description'] ?? 'REGISTER TO APPLY') ?>" data-image="<?= Url::getCollegeLogo($college['logo_image']) ?>">
                    </div>
                    <div class="lead-cta-college-filter-2 leadFilterData" data-lead_cta="26" data-entity="college" data-filter="college-admission" data-entity_id="<?= $college['college_id'] ?>" data-stateId="<?= $college['state_id'] ?>" data-cityId="<?= $college['city_id'] ?>"
                        data-slug="<?= $college['college_id'] ?>" data-ctaLocation="<?= $isMobile ? UserService::parseDynamicCta('colleges_admission_{slug}_wap_card_right_cta', '', $college['college_slug']) : UserService::parseDynamicCta('colleges_admission_{slug}_web_card_right_cta', '', $college['college_slug']) ?>" 
                        data-title="<?= $default2 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'REGISTER TO APPLY') ?>" data-description="<?= $default2 ? 'REGISTER TO APPLY' : ($dynamicCta['cta_position_1']['lead_form_description'] ?? 'REGISTER TO APPLY') ?>" data-image="<?= Url::getCollegeLogo($college['logo_image']) ?>">
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php endforeach; ?>
    </main>
    <div class='admission-pagination'>
        <?= LinkPager::widget([
            'pagination' => $pagination,
            'options' => ['class' => 'pagination'],
            'maxButtonCount' => 7,
            'prevPageLabel' => 'PREV',
            'nextPageLabel' => 'NEXT',
            'prevPageCssClass' => 'prev hidden-xs',
            'nextPageCssClass' => 'next hidden-xs',
            'activePageCssClass' => 'active',
        ]) ?>
    </div>

</html>