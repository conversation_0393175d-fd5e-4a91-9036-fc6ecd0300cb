<?php

/* @var $this yii\web\View */
/* @var $name string */
/* @var $message string */
/* @var $exception Exception */

use yii\helpers\Html;
use yii\helpers\Url;
use Yii;

$this->title = $name;
$this->context->description = '';
$code = $exception instanceof \yii\web\HttpException ? $exception->statusCode : 500;
?>
<style>
    .error404 {
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 4px;
        border: solid 1px #d8d8d8;
        background-color: #fff;
        margin-top: 40px;
        margin-bottom: 80px;
    }

    .error404 h1 {
        font-size: 78px;
        font-weight: 500;
        color: #ff4e53;
        margin-top: 40px;
    }

    .error404 h3 {
        font-size: 24px;
        font-weight: 500;
        color: #686868;
        margin-top: -10px;
    }

    .error404 p {
        font-size: 16px;
        font-weight: normal;
        color: #282828;
    }

    .errorMessage {
        text-align: center;
        margin-top: 20px;
        margin-bottom: 12px;
    }

    .imageContainer {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .backToHome {
        padding: 6px 24px;
        border-radius: 3px;
        background-color: #ff4e53;
        position: absolute;
        top: 12px;
        left: calc(50% - 85px);
        cursor: pointer;
    }

    .backToHome:hover {
        text-decoration: none;
    }

    .backToHome span {
        font-size: 14px;
        font-weight: bold;
        color: #fff;
    }

    .backToHome:hover {
        text-decoration: none;
    }

    .backWhiteIcon {
        height: 30px;
        width: 25px;
        vertical-align: middle;
        background-position: -342px -401px;
        transform: rotate(-90deg) scale(0.7);
    }

    @media (max-width: 1023px) {
        .backToHome {
            position: static;
        }

        .error404 {
            margin-top: 10px;
            margin-bottom: 40px;
            height: 694px;
        }

        .error404 .errorCode {
            font-size: 48px;
            font-weight: 500;
            margin-top: 127px;
        }

        .error404 .errorDescription {
            font-size: 18px;
            font-weight: 500;
            margin-top: 5px;
        }

        .error404 .errorMessage {
            margin-bottom: 22px;
        }

        .error404 .errorMessage p {
            font-size: 14px;
            font-weight: normal;
            line-height: 2;
        }

        .error404 .imageContainer {
            margin-bottom: 127px;
        }

        .blueBgDiv {
            height: 0px;
        }

        .pageFooter {
            padding-bottom: 0px !important;
        }
    }
</style>
<main class="container">
    <div class="error404">
        <h1 class="errorCode"><?= Html::encode($code) ?></h1>

        <?php if ($code == 404): ?>
            <h3 class="errorDescription">Page Not Found</h3>
            <div class="errorMessage">
                <p>Sorry, we couldn’t find the page you’re looking for.</p>
                <p>Try going back or head to the homepage.</p>
            </div>

        <?php elseif ($code == 500): ?>
            <h3 class="errorDescription">Internal Server Error</h3>
            <div class="errorMessage">
                <p>An internal server error has occurred.</p>
            </div>
        <?php endif; ?>

        <div class="imageContainer">
            <a class="backToHome" href="<?= Url::home(true); ?>">
                <span class="spriteIcon backWhiteIcon"></span>
                <span>Back to Home</span>
            </a>
            <img src="/yas/images/Error404-Desktop.webp" class="desktopOnly">
            <img src="/yas/images/Error404-Mobile.webp" class="mobileOnly">
        </div>
    </div>
</main>
<!--div class="site-error">

    <h1><!?= //Html::encode($this->title) ?></h1>

    <div class="alert alert-danger">
        <!?= //nl2br(Html::encode($message)) ?>
    </div>

    <p>
        The above error occurred while the Web server was processing your request.
    </p>
    <p>
        Please contact us if you think this is a server error. Thank you.
    </p>

</div-->