<?php

use frontend\assets\AppAsset;
use yii\helpers\Inflector;
use yii\widgets\ListView;
use frontend\helpers\Url;

$pageNumber = Yii::$app->request->get('page') > 1 ? ' - Page ' . Yii::$app->request->get('page') : '';
$this->title = ucfirst($user['name']) . ', Author at Getmyuni' . $pageNumber;
$this->registerMetaTag(['name' => 'description', 'content' => $seoInfo['description'] ?? '']);

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'author.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'bottom-widget.css', ['depends' => [AppAsset::class]]);
$articles->prepare();
$news->prepare();
if (!empty($exams->author_id)) {
    $exams->prepare();
}
if (Yii::$app->request->get('page') > 1) {
    $this->registerMetaTag(['name' => 'robots', 'content' => 'noindex, follow']);
}
$this->params['canonicalUrl'] = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();

// $examCount = $exams->pagination->totalCount;

?>

<div class="container">
    <section class="aboutAuthor">
        <div class="row">
            <div class="authorImg">
                <figure><img src="<?= isset($user->profile->image) && !empty($user->profile->image) ? Yii::getAlias('@profileDPFrontend') . '/' . $user->profile->image : '/yas/images/userIcon.png' ?>" alt="<?= $user['name'] ?>">
                </figure>
            </div>
            <div class="authorInfo" data-slug="<?= $user['slug']; ?>">
                <h1><?= $user['name'] . $pageNumber ?></h1>
                <p class="degree"><?= $user->profile->job_role ?? '' ?></p>
                <?php if (Yii::$app->request->get('page') < 2): ?>
                    <p><?= $user->profile->about ?? '' ?></p>
                <?php endif; ?>
                <?php if (isset($user->profile->facebook_url) || isset($user->profile->twitter_url) || isset($user->profile->linkedin_url)): ?>
                    <ul>
                        <p><?= Yii::t('app', 'Follow') ?>:</p>
                        <?php if ($user->profile->facebook_url): ?>
                            <li><a href="<?= $user->profile->facebook_url ?>" class="spriteIcon greyFbIcon" target="_blank"></a></li>
                        <?php endif; ?>
                        <?php if ($user->profile->twitter_url): ?>
                            <li><a href="<?= $user->profile->twitter_url ?>" class="spriteIcon greyTwitterIcon" target="_blank"></a></li>
                        <?php endif; ?>
                        <?php if ($user->profile->linkedin_url): ?>
                            <li><a href="<?= $user->profile->linkedin_url ?>" class="spriteIcon linkdIn" target="_blank"></a></li>
                        <?php endif; ?>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <?php if (!empty($categories)): ?>
        <div class="browseArticleSection">
            <h2><?= Yii::t('app', 'BROWSE ARTICLES'); ?></h2>
            <div class="articleTypes">
                <ul>
                    <?php $catCount = 0; ?>
                    <?php foreach ($categories as $category => $value): ?>
                        <li><a href="javascript:;" data-tab="<?= $value ?>" class="<?= $catCount == 0 ? 'activeLink' : '' ?> scrollClick <?= $value ?>scroll " data-scroll="true" data-offset="20"><?= Yii::t('app', Inflector::titleize($value)) ?></a></li>
                        <?php $catCount++ ?>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="articlesDisplay">
                <?php $listCount = 0 ?>
                <?php foreach ($categories as $cat => $val): ?>
                    <?php if ($val == 'exams' || $val == 'articles' || $val == 'news'): ?>
                        <?php if ($val == 'exams'): ?>
                            <?php $data = $exams;
                            $file = '/partials/_authorExamCard'; ?>
                        <?php elseif ($val == 'articles'): ?>
                            <?php $data = $articles;
                            $file = '/partials/_authorArticleCard'; ?>
                        <?php else: ?>
                            <?php $data = $news;
                            $file = '/partials/_authorNewsCard'; ?>
                        <?php endif; ?>
                        <div id="<?= $val ?>" data-list-id="w<?php echo $listCount; ?>" class="tab-content <?= $listCount == 0 ? 'activeLink' : '' ?>">
                            <div class="row">
                                <?php
                                $data->pagination  = false;
                                echo ListView::widget([
                                    'dataProvider' => $data,
                                    'itemView' => $file,
                                    'layout' => "\n{items}\n{pager}",
                                    'itemOptions' => ['tag' => null],

                                ]); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php $listCount++ ?>
                <?php endforeach; ?>
            </div>

            <!-- <div class="pagination">
                <ul>
                    <li><a href="javascript:;">PREV</a></li>
                    <li class="active"><a href="javascript:;">1</a></li>
                    <li><a href="javascript:;">2</a></li>
                    <li>...</li>
                    <li><a href="javascript:;">9</a></li>
                    <li><a href="javascript:;">NEXT</a></li>

                </ul>
            </div> -->
        </div>
    <?php endif; ?>
</div>