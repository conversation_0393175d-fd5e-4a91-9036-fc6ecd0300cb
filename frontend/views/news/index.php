<?php

use common\models\LiveUpdate;
use common\models\News;
use common\services\v2\NewsService;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\assets\AppAsset;
use common\helpers\DataHelper;

$this->title = 'Latest Education News: Exam Results, Board Exams , Admit Card, Admissions &  more';
$this->context->description = 'GetMyUni News offers the latest education news, updates and information on Admit Cards, Exam Results, Application forms, Time tables, Admissions, Boards & more';
$isMobile = \Yii::$app->devicedetect->isMobile();
$liveTagID = LiveUpdate::LIVE_NEWS_TAG_ID;

if (Yii::$app->language == 'en') {
    $url = '/news';
} else {
    $url = '/' . Yii::$app->language . '/news';
}
$this->registerLinkTag(['hrefs' =>  Url::base(true) . $url, 'rel' => 'alternate', 'hreflang' => Yii::$app->language]);

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'News');

if ($isMobile) {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'news_lead.css', ['depends' => [AppAsset::class]]);
}


$this->registerLinkTag(['rel' => 'amphtml', 'href' => Url::toAmp(Url::toNews())]);
//top search
if (!empty($topsearchNews)) {
    $this->params['topsearchNews'] = $topsearchNews;
}

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'article_landing.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'news.css', ['depends' => [AppAsset::class]]);

$ctaText = 'SUBSCRIBE';
$this->params['entity'] = News::ENTITY_NEWS;
$this->params['entity_id'] = 0;
$this->params['dynamicCta'] = $dynamicCta ?? [];
?>

<main>
    <div class="bannerSection">
        <h1><?= Yii::t('app', 'Latest Education Articles'); ?></h1>
        <div class="latestArticleSection row">
            <article class="articleDisplay">
                <?php $latestNewsCardCount = 1; ?>
                <?php foreach ($latestNews as $latest):
                    $news_banner_img = !empty($latest['banner_image']) ? Url::toNewsImages($latest['banner_image']) : Url::toNewsImages();
                    if (empty($latest)) {
                        return '';
                    } ?>
                    <div class="<?= 'article' . $latestNewsCardCount . '-view' ?> <?= $latestNewsCardCount > 1 ? 'display_none' : '' ?>">
                        <figure>
                            <img onclick="gmu.url.goto('<?= Url::toNewsDetail($latest['slug'], DataHelper::getLangCode($latest['lang_code'])) ?>')" class="lazyload" width="475" height="270" loading="lazy" data-src="<?= $news_banner_img ?>" src="<?= $news_banner_img ?>" alt="<?= $latest['title'] ?? '' ?>">
                        </figure>
                        <div class="aticleInfo">
                            <?php $tags = (new NewsService)->liveTagExpiredAt($latest['expired_at'] ?? '', $latest['tag_id']); ?>
                            <?php if (!empty($tags) && $tags == News::IS_LIVE_YES): ?>
                                <?= $this->render('partials/_live-updates-icon', [
                                    'models' => $latest['tag_id'],
                                    'isAmp'  => 0,
                                    'liveTagID' => $liveTagID,
                                    'smallIcone' => 0
                                ]); ?>
                            <?php endif; ?>
                            <h2>
                                <a href="<?= Url::toNewsDetail($latest['slug'], DataHelper::getLangCode($latest['lang_code'])) ?>" title="<?= $latest['title'] ?? '' ?>">
                                    <?= $latest['title'] ?? '' ?>
                                </a>
                            </h2>
                            <div class="updated-info row">
                                <?php if (!empty($latest['author'])): ?>
                                    <div class="updatedBy">
                                        <p class="authorName" title="<?= $latest['author'] ?? '' ?>"><?= $latest['author'] ?? '' ?></p>
                                    </div>
                                <?php endif; ?>
                                <p><?= Yii::$app->formatter->asDate($latest['updated_at']) ?></p>
                            </div>
                        </div>
                    </div>
                    <?php $latestNewsCardCount++ ?>
                <?php endforeach; ?>

                <div class="article mobileOnly">
                    <div class="viewAllDiv">
                        <a href="<?= Url::toNewsDetail('latest', Yii::$app->language) ?>" title=" Latest Articles"><i class="spriteIcon viewAllIcon"></i><?= Yii::t('app', 'VIEW ALL'); ?></a>
                    </div>
                </div>
            </article>
            <div class="articleList">
                <ul>
                    <?php $latestLinkCount = 1 ?>
                    <?php foreach ($latestNews as $latestLink):
                        if (empty($latestLink)) {
                            return '';
                        } ?>
                        <li class="article<?= $latestLinkCount ?>">
                            <?php
                            $imageLink = !empty($latestLink['banner_image']) ? $latestLink['banner_image'] : '';
                            $news_data_source = !empty($imageLink) ? Url::toNewsImages($imageLink) : Url::toNewsImages();
                            ?>
                            <a href="<?= Url::toNewsDetail($latestLink['slug'], DataHelper::getLangCode($latestLink['lang_code'])) ?>" title="<?= !empty($latestLink['title']) ? $latestLink['title'] : '' ?>">
                                <img onclick="gmu.url.goto('<?= Url::toNewsDetail($latestLink['slug'], DataHelper::getLangCode($latestLink['lang_code'])) ?>')" class="lazyload" width="95" height="45" loading="lazy" data-src="<?= $news_data_source ?>" src="<?= $news_data_source ?>" alt="<?= $latestLink['title'] ?>">
                                <div class="articleName">
                                    <?php $tags = (new NewsService)->liveTagExpiredAt($latestLink['expired_at'], $latestLink['tag_id']); ?>
                                    <?php if (!empty($tags) && $tags == News::IS_LIVE_YES): ?>
                                        <?= $this->render('partials/_live-updates-icon', [
                                            'models' => $latestLink['tag_id'],
                                            'isAmp'  => 0,
                                            'liveTagID' => $liveTagID,
                                            'smallIcone' => 1
                                        ]); ?>
                                    <?php endif; ?>
                                    <?= !empty($latestLink['title']) ? $latestLink['title'] : '' ?>
                                </div>
                            </a>
                        </li>
                        <?php $latestLinkCount++ ?>
                    <?php endforeach; ?>
                    <p class="viewAll">
                        <a href="<?= Url::toNewsDetail('latest', Yii::$app->language) ?>" title="Latest News"><?= Yii::t('app', 'VIEW ALL'); ?> <span class="spriteIcon arrowIcon"></span></a>
                    </p>
                </ul>
            </div>
        </div>
    </div>

    <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php echo Ad::unit('GMU_NEWS_LANDING_PAGE_WEB_ATF_728x90', '[728,90]') ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>

    <!-- <aside>
        <div class="horizontalRectangle">
            <div class="appendAdDiv" style="background:#EAEAEA;<?php //$isMobile ? 'height: 50px;' : ''
            ?>">
                <?php /* if ($isMobile): ?>
                    <?php echo Ad::unit('GMU_NEWS_LANDING_PAGE_WAP_ATF_300x50', '[300,50]') ?>
                <?php else: ?>
                    <?php echo Ad::unit('GMU_NEWS_LANDING_PAGE_WEB_ATF_728x90', '[728,90]') ?>
                <?php endif; */ ?>
            </div>
        </div>
    </aside> -->

    <?php if (!empty($featured)): ?>
        <?= $this->render('partials/_post-list-cards', [
            'posts' => $featured,
            'title' => Yii::t('app', 'Featured'),
            'categorySlug' => 'featured'
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($allCategory)): ?>
        <?= $this->render('partials/_category-post-list-cards', [
            'posts' => $allCategory,
        ]) ?>
    <?php endif; ?>
    
    <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Ad::unit('GMU_NEWS_LANDING_PAGE_WAP_BTF_300x250', '[300,250]') ?>
                    <?php else: ?>
                        <?php echo Ad::unit('GMU_NEWS_LANDING_PAGE_WEB_BTF_728x90', '[728,90]') ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>
    <?php endif;?>
    <?php /*<div class="setAlarmDiv mobileOnly">
        <?= frontend\helpers\Html::leadButton(
            '<i class="spriteIcon alarmIcon"></i> SUBSCRIBE',
            [
                'entity' => Lead::ENTITY_NEWS,
                'entityId' => '',
                'ctaLocation' => HelpersLead::getCTAsName(Lead::ENTITY_NEWS . '.news-landing-page-sticky'),
                'leadformtitle' => 'Get Latest News Alert'
            ],
            ['class' => 'primaryBtn setExamAlert getLeadForm']
        ) ?>

        </div>*/ ?>
</main>