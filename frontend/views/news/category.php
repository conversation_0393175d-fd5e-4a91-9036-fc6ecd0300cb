<?php

use frontend\helpers\Url;
use frontend\assets\AppAsset;
use common\models\LiveUpdate;
use common\models\News;
use frontend\helpers\Ad;
use yii\widgets\ListView;

$isMobile = \Yii::$app->devicedetect->isMobile();
$liveTagID = LiveUpdate::LIVE_NEWS_TAG_ID;
$posts->prepare();


//top search
if (!empty($topsearchNews)) {
    $this->params['topsearchNews'] = $topsearchNews;
}

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'News'), 'url' => [Url::toNews(Yii::$app->language)], 'title' => 'News'];
if (Yii::$app->request->get('page') > 1) {
    $this->params['breadcrumbs'][] = [
        'label' => $category->name ?? '',
        'url' => Url::canonical(),
        'title' => $category->name . ' News'
    ];
    $currentPageNum = $this->params['breadcrumbs'][] = 'Page ' . Yii::$app->request->get('page');
    $this->registerMetaTag(['name' => 'robots', 'content' => 'noindex, follow']);
} else {
    $this->params['breadcrumbs'][] = $category->name;
}

$this->title = ($category->slug == 'latest' ? 'Latest' : 'Latest ' . $category->name) . ' News in India ' . (isset($currentPageNum) ? ' - ' . $currentPageNum : '');
$this->context->description = "{$category->name} News in India: Find the latest {$category->name} and detailed information on notification, application form, admit card, result, cut off etc.";
$isMobile = \Yii::$app->devicedetect->isMobile();

$this->registerLinkTag(['rel' => 'amphtml', 'href' => Url::toAmp(Url::toNewsDetail($category->slug))]);

$this->params['links'] = $posts->pagination->getLinks(true);

$this->registerCssFile(Yii::$app->params['cssPath'] . 'article-category.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'news.css', ['depends' => [AppAsset::class]]);
if ($isMobile) {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'news_lead.css', ['depends' => [AppAsset::class]]);
}

if (Yii::$app->request->get('page') > 1) {
    $this->params['canonicalUrl'] = Url::base(true) . Url::toNewsDetail($category->slug, Yii::$app->language) . '?page=' . Yii::$app->request->get('page');
} else {
    $this->params['canonicalUrl'] = Url::base(true) . Url::toNewsDetail($category->slug, Yii::$app->language);
}


$ctaText = 'SUBSCRIBE';
$this->params['entity'] = News::ENTITY_NEWS;
$this->params['page_category'] = 'news-no-cta';
?>

<div class="pageHeading">
    <h1><?= $category->name ?> <?= Yii::t('app', 'News') ?> <?= isset($currentPageNum) ? ' - ' . $currentPageNum : '' ?></h1>
</div>
<div class="row">
    <div class="col-md-8">
    <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
        <aside>
            <div class="horizontalRectangle">
                <?php if (!$isMobile): ?>
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php echo Ad::unit('GMU_NEWS_CATEGORY_NEWS_WEB_ATF_728x90', '[728,90]') ?>
                    </div>
                <?php endif; ?>
            </div>
        </aside>
    <?php endif; ?>
        <div class="categoryArticlesList">
            <?php
            echo ListView::widget([
                'dataProvider' => $posts,
                'itemView' => 'partials/_news-lists',
                'pager' => [
                    'class' => 'LinkPager',
                ],
                'viewParams' => [
                    'fullView' => true,
                    'context' => 'main-page',
                    'isMobile' => $isMobile,
                ],
                'layout' => "{items}\n{pager}",
                'pager' => [
                    'class' => '\justinvoelker\separatedpager\LinkPager',
                    'maxButtonCount' => $isMobile ? 4 : 7,
                    // 'prevPageLabel' => 'Previous',
                    // 'nextPageLabel' => 'Next',
                    'prevPageCssClass' => 'prev hidden-xs',
                    'nextPageCssClass' => 'next hidden-xs',
                    'activePageAsLink' => false,
                ]
            ]);

            ?>
        </div>
    </div>
    <div class="col-md-4">
        <aside>
            <?php /*
        <div class="registerLatestArticle">
                <div class="row">
                    <img class="lazyload" loading="lazy" data-src="/yas/images/get-support.png" src="/yas/images/get-support.png" alt="Get Updates on Latest Articles" />
                    <p>Get Latest News Updates</p>
                </div>
                <?= frontend\helpers\Html::leadButton(
                    $ctaText,
                    [
                        'entity' => Lead::ENTITY_NEWS,
                        'entityId' => '',
                        'ctaLocation' => HelpersLead::getCTAsName(Lead::ENTITY_NEWS . '.news_category_web_lead_capture_panel_cta1'),
                        'ctaText' => $ctaText,
                        'leadformtitle' => 'SUBSCRIBE NOW TO GET UPDATES'
                    ],
                    ['class' => 'primaryBtn registerNow']
                )
?>
            </div>
            */ ?>
            <?php if (!empty($featuredNews) || !empty($recentNews)): ?>
                <?php
                echo $this->render('partials/_sidebar-tab-news', [
                    'featured' => $featuredNews,
                    'recents' => $recentNews,
                    'isAmp'  => 0,
                    'liveTagID' => $liveTagID,
                    'smallIcone' => 1

                ]);
                ?>
            <?php endif; ?>
        </aside>
        <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
            <aside>
            <div class="verticleRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Ad::unit('GMU_NEWS_CATEGORY_NEWS_WAP_BTF_300x250', '[300,250]') ?>
                    <?php else: ?>
                        <?php echo Ad::unit('GMU_NEWS_CATEGORY_NEWS_WEB_MTF_300x600', '[300,600]') ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>    
        <?php endif; ?>
    </div>
</div>
<?php /*
<div class="setAlarmDiv mobileOnly">
    <?= frontend\helpers\Html::leadButton(
        '<i class="spriteIcon alarmIcon"></i>' . $ctaText,
        [
            'entity' => Lead::ENTITY_NEWS,
            'entityId' => '',
            'ctaLocation' => HelpersLead::getCTAsName(Lead::ENTITY_NEWS . '.news_category_wap_bottom_sticky_cta1'),
            'ctaText' => $ctaText,
            'leadformtitle' => 'Get Latest News Alert'
        ],
        ['class' => 'primaryBtn setExamAlert']
    ) ?>

</div>
*/ ?>