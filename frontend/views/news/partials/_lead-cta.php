<?php

use common\models\Lead;

$isMobile = \Yii::$app->devicedetect->isMobile();
$defaultEmptyCondition0 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_0']) || empty(array_filter($dynamicCta['cta_position_0'])));
$defaultEmptyCondition1 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])));
$defaultEmptyCondition2 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_2']) || empty(array_filter($dynamicCta['cta_position_2'])));
$defaultEmptyCondition3 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_3']) || empty(array_filter($dynamicCta['cta_position_3'])));

?>

<?php if (empty($dynamicCta)): ?>
    <div class="headerCTAPair ">
        <?= frontend\helpers\Html::leadButton(
            '<i class="spriteIcon alarmIcon"></i>' . 'GET NEWS ALERT',
            [
                'entity' => Lead::ENTITY_NEWS,
                'entityId' => $entity_id,
                'ctaLocation' => !$isMobile ? 'news_detail_lead_capture_panel_rigth_cta1' : 'news_detail_wap_top_sticky_right_cta1',
                'ctaText' => 'GET NEWS ALERT',
                'leadformtitle' => 'REGISTER TO GET NEWS ALERTS',
                'subheadingtext' => $name ?? '',
            ],
            ['class' => 'newsLeadValue leadpopup'],
            $isMobile ? 'js-open-lead-form-news' : 'js-open-lead-form-new'
            // 'js-open-lead-form'
        ) ?>
        <?= frontend\helpers\Html::leadButton(
            'SUBSCRIBE',
            [
                'entity' => Lead::ENTITY_NEWS,
                'entityId' => $entity_id,
                'ctaLocation' => !$isMobile ? 'news_detail_lead_capture_panel_left_cta1' : 'news_detail_wap_top_sticky_left_cta1',
                'ctaText' => 'SUBSCRIBE',
                'leadformtitle' => 'REGISTER TO GET NEWS ALERTS',
                'subheadingtext' => $name ?? '',
            ],
            ['class' => 'newsLeadValue leadpopuptwo'],
            $isMobile ? 'js-open-lead-form-news' : 'js-open-lead-form-new'
            // 'js-open-lead-form'
        ) ?>
    </div>
<?php endif; ?>

<?php if (!empty($dynamicCta) && ($lead_cta == 6)): ?>
    <div class="headerCTAPair ">
        <?= frontend\helpers\Html::leadButton(
            $defaultEmptyCondition0 ? '<i class="spriteIcon alarmIcon"></i>' . 'SUBSCRIBE' : (!empty($dynamicCta['cta_position_0']['cta_text']) ? $dynamicCta['cta_position_0']['cta_text'] : '<i class="spriteIcon alarmIcon"></i>' . 'SUBSCRIBE'),
            [
                'entity' => Lead::ENTITY_NEWS,
                'entityId' => $entity_id,
                'ctaLocation' => $isMobile ? ($defaultEmptyCondition0 ? 'news_detail_lead_capture_panel_left_cta1' : $dynamicCta['cta_position_0']['wap']) : ($defaultEmptyCondition0 ? 'news_detail_wap_top_sticky_left_cta1' : $dynamicCta['cta_position_0']['web']),
                'ctaText' => $defaultEmptyCondition0 ?  'SUBSCRIBE' : (!empty($dynamicCta['cta_position_0']['cta_text']) ? $dynamicCta['cta_position_0']['cta_text'] : 'SUBSCRIBE'),
                'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER TO GET NEWS ALERTS' : (!empty($dynamicCta['cta_position_0']['lead_form_title']) ? $dynamicCta['cta_position_0']['lead_form_title'] : 'REGISTER TO GET NEWS ALERTS'),
                'subheadingtext' => $defaultEmptyCondition0 ? $name : (!empty($dynamicCta['cta_position_0']['lead_form_description']) ? $dynamicCta['cta_position_0']['lead_form_description'] : $name),
                'redirection' => ($defaultEmptyCondition0 || $dynamicCta['cta_position_0']['page_event'] == 0 ? null : $dynamicCta['cta_position_0']['page_link'])
            ],
            ['class' => 'newsLeadValue leadpopup'],
            $isMobile ? 'js-open-lead-form-news' : 'js-open-lead-form-new'
            // 'js-open-lead-form'
        ) ?>
        <?= frontend\helpers\Html::leadButton(
            $defaultEmptyCondition1  ? '<i class="spriteIcon alarmIcon"></i>' . 'GET NEWS ALERT' : (!empty($dynamicCta['cta_position_1']['cta_text']) ? $dynamicCta['cta_position_1']['cta_text'] : '<i class="spriteIcon alarmIcon"></i>' . 'GET NEWS ALERT'),
            [
                'entity' => Lead::ENTITY_NEWS,
                'entityId' => $entity_id,
                'ctaLocation' => $isMobile ? ($defaultEmptyCondition1  ? 'news_detail_lead_capture_panel_rigth_cta1' : $dynamicCta['cta_position_1']['wap']) : ($defaultEmptyCondition1  ? 'news_detail_wap_top_sticky_right_cta1' : $dynamicCta['cta_position_1']['web']),
                'ctaText' => $defaultEmptyCondition1  ? 'GET NEWS ALERT' : (!empty($dynamicCta['cta_position_1']['cta_text']) ? $dynamicCta['cta_position_1']['cta_text'] : 'GET NEWS ALERT'),
                'leadformtitle' => $defaultEmptyCondition1  ? 'REGISTER TO GET NEWS ALERTS' : (!empty($dynamicCta['cta_position_1']['lead_form_title']) ? $dynamicCta['cta_position_1']['lead_form_title'] : 'REGISTER TO GET NEWS ALERTS'),
                'subheadingtext' => $defaultEmptyCondition1  ? $name : (!empty($dynamicCta['cta_position_1']['lead_form_description']) ? $dynamicCta['cta_position_1']['lead_form_description'] : $name),
                'redirection' => ($defaultEmptyCondition1 || $dynamicCta['cta_position_0']['page_event'] == 0 ? null : $dynamicCta['cta_position_1']['page_link'])
            ],
            ['class' => 'newsLeadValue leadpopuptwo'],
            $isMobile ? 'js-open-lead-form-news' : 'js-open-lead-form-new'
            // 'js-open-lead-form'
        ) ?>
    </div>
<?php endif; ?>

<?php if (!empty($dynamicCta) && ($lead_cta == 0)): ?>
    <div class="headerCTAPair ">
        <?= frontend\helpers\Html::leadButton(
            $defaultEmptyCondition2 ? '<i class="spriteIcon alarmIcon"></i>' . 'SUBSCRIBE' : (!empty($dynamicCta['cta_position_2']['cta_text']) ? $dynamicCta['cta_position_2']['cta_text'] : '<i class="spriteIcon alarmIcon"></i>' . 'SUBSCRIBE'),
            [
                'entity' => Lead::ENTITY_NEWS,
                'entityId' => $entity_id,
                'ctaLocation' => $isMobile ? ($defaultEmptyCondition2 ? 'news_detail_lead_capture_panel_left_cta1' : $dynamicCta['cta_position_2']['wap']) : ($defaultEmptyCondition2 ? 'news_detail_wap_top_sticky_left_cta1' : $dynamicCta['cta_position_2']['web']),
                'ctaText' => $defaultEmptyCondition2 ?  'SUBSCRIBE' : (!empty($dynamicCta['cta_position_2']['cta_text']) ? $dynamicCta['cta_position_2']['cta_text'] : 'SUBSCRIBE'),
                'leadformtitle' => $defaultEmptyCondition2 ? 'REGISTER TO GET NEWS ALERTS' : (!empty($dynamicCta['cta_position_2']['lead_form_title']) ? $dynamicCta['cta_position_2']['lead_form_title'] : 'REGISTER TO GET NEWS ALERTS'),
                'subheadingtext' => $defaultEmptyCondition2 ? $name : (!empty($dynamicCta['cta_position_2']['lead_form_description']) ? $dynamicCta['cta_position_2']['lead_form_description'] : $name),
                'redirection' => ($defaultEmptyCondition2 || $dynamicCta['cta_position_2']['page_event'] == 0 ? null : $dynamicCta['cta_position_2']['page_link'])
            ],
            ['class' => 'newsLeadValue leadpopup'],
            $isMobile ? 'js-open-lead-form-news' : 'js-open-lead-form-new'
            // 'js-open-lead-form'
        ) ?>
        <?= frontend\helpers\Html::leadButton(
            $defaultEmptyCondition3  ? '<i class="spriteIcon alarmIcon"></i>' . 'TALK TO EXPERT' : (!empty($dynamicCta['cta_position_3']['cta_text']) ? $dynamicCta['cta_position_3']['cta_text'] : '<i class="spriteIcon alarmIcon"></i>' . 'TALK TO EXPERT'),
            [
                'entity' => Lead::ENTITY_NEWS,
                'entityId' => $entity_id,
                'ctaLocation' => $isMobile ? ($defaultEmptyCondition3  ? 'news_detail_lead_capture_panel_rigth_cta1' : $dynamicCta['cta_position_3']['wap']) : ($defaultEmptyCondition3  ? 'news_detail_wap_top_sticky_right_cta1' : $dynamicCta['cta_position_3']['web']),
                'ctaText' => $defaultEmptyCondition3  ? 'TALK TO EXPERT' : (!empty($dynamicCta['cta_position_3']['cta_text']) ? $dynamicCta['cta_position_3']['cta_text'] : 'TALK TO EXPERT'),
                'leadformtitle' => $defaultEmptyCondition3  ? 'REGISTER TO TALK TO EXPERT' : (!empty($dynamicCta['cta_position_3']['lead_form_title']) ? $dynamicCta['cta_position_3']['lead_form_title'] : 'REGISTER TO TALK TO EXPERT'),
                'subheadingtext' => $defaultEmptyCondition3  ? $name : (!empty($dynamicCta['cta_position_3']['lead_form_description']) ? $dynamicCta['cta_position_3']['lead_form_description'] : $name),
                'redirection' => ($defaultEmptyCondition3 || $dynamicCta['cta_position_3']['page_event'] == 0 ? null : $dynamicCta['cta_position_3']['page_link'])
            ],
            ['class' => 'newsLeadValue leadpopuptwo'],
            $isMobile ? 'js-open-lead-form-news' : 'js-open-lead-form-new'
            // 'js-open-lead-form'
        ) ?>
    </div>
<?php endif; ?>

<?php if (!$isMobile && !empty($dynamicCta) && isset($dynamicCta['cta_position_2']) && ($dynamicCta['cta_position_2']['template'] == 1)): ?>
    <div class="headerCTAPair ">
        <?= frontend\helpers\Html::leadButton(
            'SUBSCRIBE',
            [
                'entity' => Lead::ENTITY_NEWS,
                'entityId' => $entity_id,
                'ctaLocation' => !$isMobile ? 'news_detail_lead_capture_panel_web_left_cta1' : 'news_detail_wap_top_sticky_left_cta1',
                'ctaText' => 'SUBSCRIBE',
                'leadformtitle' => 'REGISTER TO GET NEWS ALERTS',
                'subheadingtext' => $name ?? '',
            ],
            ['class' => 'newsLeadValue leadpopup'],
            $isMobile ? 'js-open-lead-form-news' : 'js-open-lead-form-new'
            // 'js-open-lead-form'
        ) ?>
        <?= frontend\helpers\Html::leadButton(
            'GET NEWS ALERT',
            [
                'entity' => Lead::ENTITY_NEWS,
                'entityId' => $entity_id,
                'ctaLocation' => !$isMobile ? 'news_detail_lead_capture_panel_web_right_cta1' : 'news_detail_wap_top_sticky_right_cta1',
                'ctaText' => 'GET NEWS ALERT',
                'leadformtitle' => 'REGISTER TO GET NEWS ALERTS',
                'subheadingtext' => $name ?? '',
            ],
            ['class' => 'newsLeadValue leadpopuptwo'],
            $isMobile ? 'js-open-lead-form-news' : 'js-open-lead-form-new'
            // 'js-open-lead-form'
        ) ?>
    </div>
<?php endif; ?>

<?php /* if ($isMobile && !empty($dynamicCta) && !empty($dynamicCta['cta_position_0']) && $dynamicCta['cta_position_0']['template'] == 1): ?>
    <div class="headerToolDiv ">
        <div class="container">
            <div class="row">
                <h2><?= $dynamicCta['cta_position_0']['cta_title'] ?></h2>
                <div class="headerToolCTADiv">
                    <p><?= $dynamicCta['cta_position_0']['cta_description'] ?></p>
                    <?= frontend\helpers\Html::leadButton(
                        empty($dynamicCta) || empty(array_filter($dynamicCta['cta_position_0'])) ? 'SUBSCRIBE' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'SUBSCRIBE'),
                        [
                            'entity' => Lead::ENTITY_NEWS,
                            'entityId' => $entityId,
                            'ctaLocation' => $isMobile ? (empty($dynamicCta) || empty(array_filter($dynamicCta['cta_position_0'])) ? 'news_detail_lead_capture_panel_left_cta1' : $dynamicCta['cta_position_0']['wap']) : (empty($dynamicCta) || empty(array_filter($dynamicCta['cta_position_0'])) ? 'news_detail_wap_top_sticky_left_cta1' : $dynamicCta['cta_position_0']['web']),
                            'ctaText' => empty($dynamicCta) || empty(array_filter($dynamicCta['cta_position_0'])) ? 'SUBSCRIBE' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'SUBSCRIBE'),
                            'leadformtitle' => empty($dynamicCta) || empty(array_filter($dynamicCta['cta_position_0'])) ? 'REGISTER TO GET NEWS ALERTS' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER TO GET NEWS ALERTS'),
                            'subheadingtext' => empty($dynamicCta) || empty(array_filter($dynamicCta['cta_position_0'])) ? $name : ($dynamicCta['cta_position_0']['lead_form_description'] ?? $name),
                            'redirection' => (empty($dynamicCta) || empty(array_filter($dynamicCta['cta_position_0'])) ? null : $dynamicCta['cta_position_0']['page_link'])
                        ],
                        ['class' => ''],
                        // $isMobile ? 'js-open-lead-form-news' : 'js-open-lead-form'
                        'js-open-lead-form'
                    ) ?>
                </div>
            </div>
        </div>
    </div>
<?php endif; */ ?>