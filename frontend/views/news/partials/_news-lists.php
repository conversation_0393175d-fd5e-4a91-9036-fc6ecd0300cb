<?php

use common\helpers\ContentHelper;
use common\models\LiveUpdate;
use common\models\News;
use common\services\v2\NewsService;
use frontend\helpers\Url;
use common\helpers\DataHelper;

$currentUrl = parse_url(Yii::$app->request->url);
if (empty($model)) {
    return [];
}
if (strpos($currentUrl['path'], 'featured') == true) {
    $slug = $model['slug'] ?? '';
    $name = !empty($model['meta_title']) ? $model['meta_title'] : $model['name'] ?? '';
    $bannerImage = $model['banner_image'] ?? '';
    $tags = $model['is_live'] ?? '';
    $date = !empty($model['published_at']) ? $model['published_at'] : $model['created_at'];
    $content = $model['meta_description'] ?? '';
    $authorSlug = $model['authorSlug'] ?? '';
    $authorName = $model['authorName'] ?? '';
    $expired_at = $model['expired_at'] ?? '';
    $lang_code = $model['lang_code'] ?? '';
} else {
    if (isset($model->newsContent->transAuthor->user_name) && !empty($model->newsContent->transAuthor->user_name) && $model->lang_code != 1) {
        $authorName = $model->newsContent->transAuthor->user_name;
    } else {
        $authorName = !empty($model->newsContent) ? (!empty($model->newsContent->author) ? $model->newsContent->author->name : '') : '';
    }
    $slug = $model->slug ?? '';
    $name = !empty($model->newsContent) && !empty($model->newsContent->meta_title) ? $model->newsContent->meta_title : $model->name;
    $bannerImage = $model->banner_image ?? '';
    $tags = $model->is_live ?? '';
    $date = !empty($model->published_at) ? $model->published_at : $model->created_at;
    $content = !empty($model->newsContent) ? $model->newsContent->meta_description : '';
    $authorSlug = !empty($model->newsContent) ? (!empty($model->newsContent->author) ? $model->newsContent->author->slug : '') : '';
    $expired_at = $model->expired_at ?? '';
    $lang_code = $model->lang_code ?? '';
}

?>
<article class="catgegoryArticle">
    <div class="row">
        <div class="articleBanner">
            <figure>
                <img onclick="gmu.url.goto('<?= Url::toNewsDetail($slug, DataHelper::getLangCode($lang_code)) ?>')" class="lazyload" loading="lazy" data-src="<?= !empty($bannerImage) ? Url::toNewsImages($bannerImage) : Url::toNewsImages() ?>" src="<?= !empty($bannerImage) ? Url::toNewsImages($bannerImage) : Url::toNewsImages() ?>" alt="<?= $name ?>">
            </figure>
        </div>
        <div class="articleText">
            <h2>
                <a href="<?= Url::toNewsDetail($slug, DataHelper::getLangCode($lang_code)) ?>" title="<?= $name ?>">
                    <?php $tag = (new NewsService)->liveTagExpiredAt($expired_at, $tags); ?>
                    <?php
                    if (!empty($tag) && ($tag == News::IS_LIVE_YES)) {
                        echo  $this->render('_live-updates-icon', [
                            'models' => $tags,
                            'isAmp'  => 0,
                            'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                            'smallIcone' => 1
                        ]);
                    }
                    ?>
                    <?= $name ?> </a>
            </h2>
            <p><?= $content ?? '' ?></p>
            <div class="updated-info row">
                <div class="updatedBy">
                    <img src="<?= ContentHelper::getUserProfilePic($authorSlug) ?>" alt="<?= $authorName ?? '' ?>">
                    <p class="authorName"><?= $authorName ?? '' ?></p>
                </div>
                <p><?= Yii::$app->formatter->asDate($date) ?></p>

            </div>
        </div>
    </div>
</article>