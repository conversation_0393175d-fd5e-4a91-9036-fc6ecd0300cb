<?php

use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;
use common\helpers\DataHelper;

$isAmp = isset($type) && $type == 'amp' ? true : false;
?>
<section class="latestInfoSection">

    <h2 class="row">
        <?= $title ?>
    </h2>

    <div class="latestInfoListContainer four-cardDisplay">
        <i class="spriteIcon scrollLeft over"></i>
        <i class="spriteIcon scrollRight"></i>
        <div class="latestInfoList row">
            <?php foreach ($posts as $post):
                if (isset($post['user_name']) && !empty($post['user_name']) && $post['lang_code'] != 1) {
                    $authorName = $post['user_name'];
                } else {
                    $authorName = $post['author'] ?? '';
                } ?>
                <?php if (empty($post)) {
                    return [];
                } ?>
                <div class="latestInfoDiv">
                    <a href="<?= !empty($post['slug']) ? Url::toNewsDetail($post['slug'], DataHelper::getLangCode($post['lang_code'])) : '' ?>" title="<?= $post['title'] ?? '' ?>">
                        <figure>
                            <?php if ($isAmp): ?>
                                <amp-img layout="responsive" width="323" height="231" src="<?= !empty($post['banner_image']) ? Url::toNewsImages($post['banner_image']) : Url::toNewsImages() ?>" alt="<?= $post['title'] ?? '' ?>"></amp-img>
                            <?php else: ?>
                                <img class="lazyload" width="274" height="207" loading="lazy" data-src="<?= !empty($post['banner_image']) ? Url::toNewsImages($post['banner_image']) : Url::toNewsImages() ?>" src="<?= !empty($post['banner_image']) ? Url::toNewsImages($post['banner_image']) : Url::toNewsImages() ?>" alt="<?= $post['title'] ?? '' ?>">
                            <?php endif; ?>
                        </figure>
                        <div class="latestInfoTxt">
                            <h3><?= !empty($post['title']) ? BaseStringHelper::truncate($post['title'], 92) : BaseStringHelper::truncate($post['name'], 92) ?></h3>
                            <p><?= $authorName ?></p>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>

            <div class="latestInfoDiv no-shadow">
                <div class="viewAllDiv">
                    <a href="<?= Url::toNewsDetail($categorySlug, Yii::$app->language) ?>" title="<?= ucwords(str_replace('-', ' ', $categorySlug)) ?> News"><i class="spriteIcon viewAllIcon"></i>
                        <?= Yii::t('app', 'VIEW ALL'); ?></a>
                </div>
            </div>
        </div>
    </div>
</section>