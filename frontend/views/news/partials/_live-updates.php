<?php

use Carbon\Carbon;

$currentDate = Carbon::now()->toDateTimeString();
if (!empty($models)):
    foreach ($models as $liveUpdateCheck):
        $redirectionDate = null;
        if (!empty($liveUpdateCheck->expired_at)):
            $redirectionDate = date('Y-m-d H:i:s', strtotime($liveUpdateCheck->expired_at));
        endif;
        if (isset($redirectionDate) && $currentDate <= $redirectionDate):
            break;
        endif;
    endforeach;
    if ((!empty($redirectionDate) &&  $currentDate <= $redirectionDate) || (empty($redirectionDate))):
        ?>
        <div class="liveUpdateSection">
            <p class="liveUpdateHeading"><span class="whiteDot"></span> <?= Yii::t('app', 'Live Updates') ?></p>
            <div class="latestNewsList">
                <?php foreach ($models as $liveUpdate):
                    $redirectionDate1 = !empty($liveUpdate->expired_at) ? date('Y-m-d H:i:s', strtotime($liveUpdate->expired_at)) : null;
                    if ((!empty($redirectionDate1) && ($currentDate <= $redirectionDate1)) || (empty($redirectionDate1))): ?>
                        <div class="lastestNewsDiv">
                            <p class="newsUpdatedTime"><?= date('F d, Y', strtotime($liveUpdate->updated_at)) ?> | <?= date('h:i A', strtotime($liveUpdate->updated_at)) ?> IST</p>
                            <h3><?= $liveUpdate->title ??  '' ?></h3>
                            <p><?= $liveUpdate->content ?></p>
                        </div>
                        <?php
                    endif;
                endforeach; ?>
            </div>
        </div>
        <?php
    endif;
endif;
?>