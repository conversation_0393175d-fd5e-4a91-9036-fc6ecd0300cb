<?php

use frontend\assets\AppAsset;
use frontend\helpers\Url;
use common\services\QnaCommonService;
use common\helpers\ContentHelper;

$currentUrl = Url::base(true) . Url::current();

// dd($qnadetails[0]->answers);
// $this->registerJsFile('https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js');
$this->registerJsFile('https://cdn.tiny.cloud/1/7u344hh97vkdoop6zeyt47i7b6vx7glcspcxglwwocxat8ga/tinymce/5/tinymce.min.js');
// $this->registerJsFile('/yas/js/version2/qna-new.js');
// $this->registerCssFile('/yas/css/version2/forum-revamp.css', ['depends' => [AppAsset::class]]);

// $entity_name = $entityName;
// $entity_display_name = $entityName;
// $url = Url::base(true);
// if ($entityName == 'board' || $entityName == 'exam') {
//     $entityName .= 's';
// }
$this->title = strip_tags($qnadetails[0]->question);

$this->params['qna'] = 'qna_details';

$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];

$this->params['breadcrumbs'][] = ['label' => 'QnA', 'url' => ['/q-n-a'], 'title' => 'QnA'];

$this->params['breadcrumbs'][] = ['label' => strip_tags($qnadetails[0]->question), 'title' => strip_tags($qnadetails[0]->question)];

$this->params['entity'] = 'qna';

$questionUpvote = 0;
foreach ($qnadetails[0]->answers as $ans) {
    $questionUpvote += (int) $qnadetails[1][$ans->id]['upvote'];
}

// QnA schema
if (!empty($qnadetails)) {
    foreach ($qnadetails[0]->answers as $ans) {
        $loadAnswer[] = [
            '@type' => 'Answer',
            'text' => ContentHelper::htmlDecode($ans->answer, true),
            'dateCreated' => date('Y-m-d\TH:i:sP', strtotime($ans->created_at)),
            'upvoteCount' => $qnadetails[1][$ans->id]['upvote'],
            'url' => $currentUrl,
            'author' => [
                '@type' => 'Person',
                'name' => $ans->student->name ?? 'GetmyuniEnterprise'
            ]
        ];
    }
    $loadQna[] = [
        '@type' => 'Question',
        'name' => ContentHelper::htmlDecode($qnadetails[0]->question, true),
        'text' => ContentHelper::htmlDecode($qnadetails[0]->question, true),
        'answerCount' => count($qnadetails[0]->answers),
        'upvoteCount' => $questionUpvote,
        'dateCreated' => date('Y-m-d\TH:i:sP', strtotime($qnadetails[0]->created_at)),
        'author' => [
            '@type' => 'Person',
            'name' => $qnadetails[0]->student->name ?? 'GetmyuniEnterprise'
        ],
        'acceptedAnswer' => $loadAnswer ?? ''
    ];
}
$this->params['schema1'] = \yii\helpers\Json::encode([
    [
        '@context' => 'http://schema.org',
        '@type' => 'QAPage',
        'mainEntity' => $loadQna
    ]
], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

$this->registerCssFile(Yii::$app->params['cssPath'] . 'forum-revamp.css', ['depends' => [AppAsset::class]]);

?>
<div class="qnaPage qnaDetail">
    <div class="row">
        <div class="col-md-8">
            <div class="mainQuestionCard forumCard">
                <h1>
                    <?= str_replace(['<p>', '</p>'], '', $qnadetails[0]->question) ?>
                </h1>
                <div class="cardUtilities">
                    <div class="leftSideUtils">
                        <div class="answered">
                            <span class="spriteIcon utilsIcon answeredIcon"></span>
                            <?= count($qnadetails[0]->answers) > 1 ? count($qnadetails[0]->answers) . ' Answers' : count($qnadetails[0]->answers) . ' Answer' ?>
                        </div>
                        <div class="share dropup">
                            <span class="spriteIcon utilsIcon shareIcon"></span>
                            Share
                            <div class="dropup-content more-answer-share">
                                <a href="https://twitter.com/share?url=<?= $currentUrl ?>" rel="noopener nofollow" target="_blank">
                                    <button id="shareTwiter">Twitter</button>
                                </a>
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $currentUrl ?>" target="_blank" rel="noopener nofollow">
                                    <button id="shareFb">Facebook</button>
                                </a>
                                <a href="https://api.whatsapp.com/send?text=<?= $currentUrl ?>" data-action="share/whatsapp/share" target="_blank" rel="nofollow">
                                    <button id="shareWp">WhatsApp</button>
                                </a>
                                <button class="linkCopy" onclick="copyLink('<?= Yii::$app->request->absoluteUrl ?>');">Copy Link</button>
                            </div>
                        </div>
                    </div>
                    <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                        <div class="rightSideUtilities">
                            <?php if (Yii::$app->user->getIsGuest()) { ?>
                                <a class="js-open-lead-form-new" data-ctalocation="qna-details" data-ctaText="Write Answer" data-leadformtitle="REGISTER NOW TO WRITE ANSWER">
                                    <div class="writeAnswer">
                                        <span class="spriteIcon utilsIcon writeAnswerIcon"></span>
                                        <span class="writeAnswerText">Write Answer</span>
                                    </div>
                                </a>
                            <?php } else { ?>
                                <div class="writeAnswer writeAnswerPopupTrigger">
                                    <span class="spriteIcon utilsIcon writeAnswerIcon"></span>
                                    <span>Write Answer</span>
                                </div>
                            <?php } ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php foreach ($qnadetails[0]->answers as $value) { ?>
                <div class="forumCard" id="<?= 'answer-' . $value->id ?>">
                    <div class="authorRow">
                        <span class="authorIcon">
                            <?php if ($value->student == null) {
                                echo 'G';
                            } else {
                                echo ucfirst(mb_substr($value->student->name, 0, 1));
                            } ?>
                        </span>
                        <div class="authorDetails">
                            <p>Answered By: <span>
                                    <?= $value->student->name ?? 'GetmyuniEnterprise' ?>
                                </span> |
                                <?= Yii::$app->formatter->asDatetime($value->updated_at) ?>
                            </p>
                            <!-- <p class="authorCollege">B.Tech from National Institute of Technology, Kurukshetra</p> -->
                        </div>
                    </div>
                    <div class="cardAnswer">
                        <div class="answerPara">
                            <?php echo htmlspecialchars_decode(stripslashes($value->answer)); ?>
                        </div>
                        <span class="readMoreExpand">Read More</span>
                    </div>
                    <div class="cardUtilities">
                        <div class="leftSideUtils">
                            <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                                <?php if (Yii::$app->user->isGuest) { ?>
                                    <a class="js-open-lead-form-new" data-ctalocation="qna-landing-page" data-ctatext="Write Answer" data-leadformtitle="REGISTER NOW TO VOTE ANSWER">
                                        <div class="upvotes">
                                            <span class="spriteIcon utilsIcon upvoteIcon"></span>
                                            <span class="currentCount">
                                                <?= $qnadetails[1][$value->id]['upvote']; ?>
                                            </span>
                                        </div>
                                    </a>
                                    <a class="js-open-lead-form-new" data-ctalocation="qna-landing-page" data-ctatext="Write Answer" data-leadformtitle="REGISTER NOW TO VOTE ANSWER">
                                        <div class="downvotes">
                                            <span class="spriteIcon utilsIcon downvoteIcon"></span>
                                            <span class="currentCount">
                                                <?= $qnadetails[1][$value->id]['downvote']; ?>
                                            </span>
                                        </div>
                                    </a>
                                <?php } else { ?>
                                    <div class="upvotes <?= !empty($qnadetails[1][$value->id]['userUpVote']) ? 'clicked' : '' ?>" data-answer-id="<?= $value->id ?>">
                                        <span class="spriteIcon utilsIcon upvoteIcon" style="<?= !empty($qnadetails[1][$value->id]['userUpVote']) ? 'background-position: -628px -1013px;' : '' ?>"></span>
                                        <span class="currentCount">
                                            <?= $qnadetails[1][$value->id]['upvote']; ?>
                                        </span>
                                    </div>
                                    <div class="downvotes <?= !empty($qnadetails[1][$value->id]['userDownVote']) ? 'clicked' : '' ?>" data-answer-id="<?= $value->id ?>">
                                        <span class="spriteIcon utilsIcon downvoteIcon" style="<?= !empty($qnadetails[1][$value->id]['userDownVote']) ? 'background-position: -658px -1013px;' : '' ?>"></span>
                                        <span class="currentCount">
                                            <?= $qnadetails[1][$value->id]['downvote']; ?>
                                        </span>
                                    </div>
                                <?php } ?>
                            <?php endif;?>
                            <div class="share dropup">
                                <span class="spriteIcon utilsIcon shareIcon"></span>
                                Share
                                <div class="dropup-content more-answer-share">
                                    <a href="https://twitter.com/share?url=<?= $currentUrl ?>" rel="noopener nofollow" target="_blank">
                                        <button id="shareTwiter">Twitter</button>
                                    </a>
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $currentUrl ?>" target="_blank" rel="noopener nofollow">
                                        <button id="shareFb">Facebook</button>
                                    </a>
                                    <a href="https://api.whatsapp.com/send?text=<?= $currentUrl ?>" data-action="share/whatsapp/share" target="_blank" rel="nofollow">
                                        <button id="shareWp">WhatsApp</button>
                                    </a>
                                    <button class="linkCopy" onclick="copyLink('<?= Yii::$app->request->absoluteUrl . '#answer-' . $value->id ?>');">Copy Link</button>
                                </div>
                            </div>
                        </div>
                        <div class="reportBubbleContainer">
                            <div class="reportIcon">
                                <svg width="16" height="5" viewBox="0 0 16 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8 3.69238C8.55228 3.69238 9 3.24467 9 2.69238C9 2.1401 8.55228 1.69238 8 1.69238C7.44772 1.69238 7 2.1401 7 2.69238C7 3.24467 7.44772 3.69238 8 3.69238Z" stroke="#787878" stroke-width="2" stroke-linecap="round" />
                                    <path d="M2 3.69238C2.55228 3.69238 3 3.24467 3 2.69238C3 2.1401 2.55228 1.69238 2 1.69238C1.44772 1.69238 1 2.1401 1 2.69238C1 3.24467 1.44772 3.69238 2 3.69238Z" stroke="#787878" stroke-width="2" stroke-linecap="round" />
                                    <path d="M14 3.69238C14.5523 3.69238 15 3.24467 15 2.69238C15 2.1401 14.5523 1.69238 14 1.69238C13.4477 1.69238 13 2.1401 13 2.69238C13 3.24467 13.4477 3.69238 14 3.69238Z" stroke="#787878" stroke-width="2" stroke-linecap="round" />
                                </svg>
                                More
                                <?php if (Yii::$app->user->getIsGuest()) {
                                    $svgPath = 'M13.4234 13.0608C12.2138 14.2704 10.5732 14.95 8.86255 14.95C7.1519 14.95 5.51132 14.2704 4.30171 13.0608C3.0921 11.8512 2.41255 10.2106 2.41255 8.5C2.41255 6.78935 3.0921 5.14877 4.30171 3.93916C5.51132 2.72955 7.1519 2.05 8.86255 2.05C10.5732 2.05 12.2138 2.72955 13.4234 3.93916C14.633 5.14877 15.3125 6.78935 15.3125 8.5C15.3125 10.2106 14.633 11.8512 13.4234 13.0608ZM8.86255 16.55C10.9975 16.55 13.0451 15.7019 14.5548 14.1922C16.0644 12.6825 16.9125 10.635 16.9125 8.5C16.9125';
                                    $svgPath .= ' 6.36501 16.0644 4.31746 14.5548 2.80779C13.0451 1.29812 10.9975 0.45 8.86255 0.45C6.72756 0.45 4.68001 1.29812 3.17034 2.80779C1.66067 4.31746 0.812549 6.36501 0.812549 8.5C0.812549 10.635 1.66067 12.6825 3.17034 14.1922C4.68001 15.7019 6.72756 16.55 8.86255 16.55ZM9.60501 12.2425C9.80192 12.0455 9.91255 11.7785 9.91255 11.5C9.91255 11.2215 9.80192 10.9545 9.60501 10.7575C9.4081 10.5606 9.14103 10.45 8.86255 10.45C8.58407 10.45 8.317 10.5606 8.12009 10.7575C7.92317 10.9545';
                                    $svgPath .= ' 7.81255 11.2215 7.81255 11.5C7.81255 11.7785 7.92317 12.0455 8.12009 12.2425C8.317 12.4394 8.58407 12.55 8.86255 12.55C9.14103 12.55 9.4081 12.4394 9.60501 12.2425ZM9.66255 5.25C9.66255 5.03783 9.57826 4.83434 9.42823 4.68431C9.2782 4.53429 9.07472 4.45 8.86255 4.45C8.65038 4.45 8.44689 4.53429 8.29686 4.68431C8.14683 4.83434 8.06255';
                                    $svgPath .= ' 5.03783 8.06255 5.25V8.75C8.06255 8.96217 8.14683 9.16566 8.29686 9.31569C8.44689 9.46571 8.65038 9.55 8.86255 9.55C9.07472 9.55 9.2782 9.46571 9.42823 9.31569C9.57826 9.16566 9.66255 8.96217 9.66255 8.75V5.25Z';  ?>
                                    <a class="reportBubble reportBubble ctaButton askQuestionButton js-open-lead-form-new" data-ctalocation="qna-details" data-ctaText="Ask Question" data-leadformtitle="REGISTER NOW TO ASK QUESTIONS">
                                        <span class="reportBubbleIcon">
                                            <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="<?= $svgPath ?>" fill="#282828" stroke="#787878" stroke-width="0.1" />
                                            </svg>
                                        </span>
                                        <span>
                                            Report
                                        </span>
                                    </a>
                                <?php } else {
                                    $svgPath = 'M13.4234 13.0608C12.2138 14.2704 10.5732 14.95 8.86255 14.95C7.1519 14.95 5.51132 14.2704 4.30171 13.0608C3.0921 11.8512 2.41255 10.2106 2.41255 8.5C2.41255 6.78935 3.0921 5.14877 4.30171 3.93916C5.51132 2.72955 7.1519 2.05 8.86255 2.05C10.5732 2.05 12.2138 2.72955 13.4234 3.93916C14.633 5.14877 15.3125 6.78935 15.3125 8.5C15.3125 10.2106 14.633 11.8512 13.4234 13.0608ZM8.86255 16.55C10.9975 16.55 13.0451 15.7019 14.5548 14.1922C16.0644 12.6825 16.9125 10.635 16.9125 8.5C16.9125';
                                    $svgPath .= ' 6.36501 16.0644 4.31746 14.5548 2.80779C13.0451 1.29812 10.9975 0.45 8.86255 0.45C6.72756 0.45 4.68001 1.29812 3.17034 2.80779C1.66067 4.31746 0.812549 6.36501 0.812549 8.5C0.812549 10.635 1.66067 12.6825 3.17034 14.1922C4.68001 15.7019 6.72756 16.55 8.86255 16.55ZM9.60501 12.2425C9.80192 12.0455 9.91255 11.7785 9.91255 11.5C9.91255 11.2215 9.80192 10.9545 9.60501 10.7575C9.4081 10.5606 9.14103 10.45 8.86255 10.45C8.58407 10.45 8.317 10.5606 8.12009 10.7575C7.92317 10.9545';
                                    $svgPath .= ' 7.81255 11.2215 7.81255 11.5C7.81255 11.7785 7.92317 12.0455 8.12009 12.2425C8.317 12.4394 8.58407 12.55 8.86255 12.55C9.14103 12.55 9.4081 12.4394 9.60501 12.2425ZM9.66255 5.25C9.66255 5.03783 9.57826 4.83434 9.42823 4.68431C9.2782 4.53429 9.07472 4.45 8.86255 4.45C8.65038 4.45 8.44689 4.53429 8.29686 4.68431C8.14683 4.83434 8.06255';
                                    $svgPath .= ' 5.03783 8.06255 5.25V8.75C8.06255 8.96217 8.14683 9.16566 8.29686 9.31569C8.44689 9.46571 8.65038 9.55 8.86255 9.55C9.07472 9.55 9.2782 9.46571 9.42823 9.31569C9.57826 9.16566 9.66255 8.96217 9.66255 8.75V5.25Z';
                                    ?>
                                    <div class="reportBubble reportAction">
                                        <span class="reportBubbleIcon">
                                            <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="<?= $svgPath ?>" fill="#282828" stroke="#787878" stroke-width="0.1" />
                                            </svg>
                                        </span>
                                        <span>
                                            Report
                                        </span>
                                        <input type="hidden" value="<?= $value->id ?>" class="report_answer_id">
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                        <div class="reportPageMask"></div>
                        <ul class="moreOptionsMobileDrawer">
                            <li class="closeMoreOptionsMobileDrawer">
                                <svg width="16" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="18.5264" height="1.54386" rx="0.771932" transform="matrix(0.697537 0.716549 -0.697537 0.716549 1.07715 0.809082)" fill="white" stroke="white" stroke-width="0.54" />
                                    <rect width="18.5264" height="1.54386" rx="0.771932" transform="matrix(-0.697537 0.716549 -0.697537 -0.716549 14 1.91553)" fill="white" stroke="white" stroke-width="0.54" />
                                </svg>
                            </li>
                            <li class="<?php if (!(Yii::$app->user->getIsGuest())) {
                                            echo 'reportItemMobile';
                                       } ?>">
                                <?php if (Yii::$app->user->getIsGuest()) { ?>
                                    <a class="reportBubbleMobile reportBubble ctaButton askQuestionButton js-open-lead-form-new" data-ctalocation="qna-details" data-ctaText="Ask Question" data-leadformtitle="REGISTER NOW TO ASK QUESTIONS">
                                <?php }
                                $svgPath = 'M13.4234 13.0608C12.2138 14.2704 10.5732 14.95 8.86255 14.95C7.1519 14.95 5.51132 14.2704 4.30171 13.0608C3.0921 11.8512 2.41255 10.2106 2.41255 8.5C2.41255 6.78935 3.0921 5.14877 4.30171 3.93916C5.51132 2.72955 7.1519 2.05 8.86255 2.05C10.5732 2.05 12.2138 2.72955 13.4234 3.93916C14.633 5.14877 15.3125 6.78935 15.3125 8.5C15.3125 10.2106 14.633 11.8512 13.4234 13.0608ZM8.86255 16.55C10.9975 16.55 13.0451 15.7019 14.5548 14.1922C16.0644 12.6825 16.9125 10.635 16.9125 8.5C16.9125';
                                $svgPath .= ' 6.36501 16.0644 4.31746 14.5548 2.80779C13.0451 1.29812 10.9975 0.45 8.86255 0.45C6.72756 0.45 4.68001 1.29812 3.17034 2.80779C1.66067 4.31746 0.812549 6.36501 0.812549 8.5C0.812549 10.635 1.66067 12.6825 3.17034 14.1922C4.68001 15.7019 6.72756 16.55 8.86255 16.55ZM9.60501 12.2425C9.80192 12.0455 9.91255 11.7785 9.91255 11.5C9.91255 11.2215 9.80192 10.9545 9.60501 10.7575C9.4081 10.5606 9.14103 10.45 8.86255 10.45C8.58407 10.45 8.317 10.5606 8.12009 10.7575C7.92317 10.9545';
                                $svgPath .= ' 7.81255 11.2215 7.81255 11.5C7.81255 11.7785 7.92317 12.0455 8.12009 12.2425C8.317 12.4394 8.58407 12.55 8.86255 12.55C9.14103 12.55 9.4081 12.4394 9.60501 12.2425ZM9.66255 5.25C9.66255 5.03783 9.57826 4.83434 9.42823 4.68431C9.2782 4.53429 9.07472 4.45 8.86255 4.45C8.65038 4.45 8.44689 4.53429 8.29686 4.68431C8.14683 4.83434 8.06255';
                                $svgPath .= ' 5.03783 8.06255 5.25V8.75C8.06255 8.96217 8.14683 9.16566 8.29686 9.31569C8.44689 9.46571 8.65038 9.55 8.86255 9.55C9.07472 9.55 9.2782 9.46571 9.42823 9.31569C9.57826 9.16566 9.66255 8.96217 9.66255 8.75V5.25Z';
                                ?>
                                    <span>
                                        <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="<?= $svgPath ?>" fill="#282828" stroke="#282828" stroke-width="0.1" />
                                        </svg>
                                    </span>
                                    <span>
                                        Report
                                    </span>
                                    <?php if (Yii::$app->user->getIsGuest()) { ?>
                                    </a>
                                    <?php } ?>
                                <input type="hidden" value="<?= $value->id ?>" class="mobile_report_answer_id">
                            </li>
                        </ul>

                    </div>
                </div>

            <?php } ?>
        </div>
        <div class="col-md-4">
            <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                <div class="queryWidget desktopOnly">
                    <div class="promptRow">
                        <span class="queryIcon spriteIcon"></span>
                        <h3>Do you have a query?</h3>
                    </div>
                    <div class="questionCTARow">
                        <?php if (Yii::$app->user->getIsGuest()) { ?>
                            <a class="ctaButton askQuestionButton js-open-lead-form-new" data-ctalocation="qna-details" data-ctaText="Ask Question" data-leadformtitle="REGISTER NOW TO ASK QUESTIONS">Ask Question</a>
                        <?php } else { ?>
                            <button class="ctaButton askQuestionButton askQuestionTrigger">Ask Question</button>
                        <?php } ?>

                        <!-- <a href="<?php /*Url::toQnaLandingPage($entityName, $qnadetails[0]->slug, $pageName) */ ?>" class="ctaButton viewAllButton">View All Questions</a> -->
                    </div>
                </div>
            <?php endif; ?>
            <div class="questionCTARow">
                <?php if (Yii::$app->user->getIsGuest()) { ?>
                    <a class="ctaButton askQuestionButton js-open-lead-form-new" data-ctalocation="qna-details" data-ctaText="Ask Question" data-leadformtitle="REGISTER NOW TO ASK QUESTIONS">Ask Question</a>
                <?php } else { ?>
                    <button class="ctaButton askQuestionButton askQuestionTrigger">Ask Question</button>
                <?php } ?>
                <!-- <a href="<?php /*Url::toQnaLandingPage($entityName, $qnadetails[0]->slug, $pageName) */ ?>" class="ctaButton viewAllButton">View All Questions</a> -->
            </div>
            <?php if (!empty($relatedQuestions)) { ?>
                <div class="relatedQuestions">
                    <h2>Related Questions</h2>
                    <ul>
                        <?php foreach ($relatedQuestions as $value) { ?>
                            <li><a href="<?= Url::toQnaDetail('', $value->slug) ?>">
                                    <?= $value->question; ?>
                                </a></li>
                            <?php
                        } ?>
                    </ul>
                </div>
            <?php } ?>
        </div>
    </div>
</div>

<dialog class="successfulSubmitAnswer">
    <div class="thankYouMsg">
        <i class="spriteIcon thankYouIcon"></i>
        <p class="thankYouText"> Your Answer Has Successfully Added! </p>
    </div>
</dialog>

<!-- <dialog class="errorWhileSubmitAnswer">
    <div class="errorMsg">
        <p class="errorText"> Your Answer Can't be Empty Nor Morethan 500 Character! </p>
    </div>
</dialog> -->

<dialog class="questionPopupContainer">
    <h2>Add Your Question</h2>
    <span class="spriteIcon closeQuestionPopupIcon closeQuestionPopupTrigger"></span>
    <div class="questionHints">
        <p>Tips on getting good answers quickly</p>
        <ul>
            <li>Make sure that your question not been asked already</li>
            <li>Keep your questions short and to the point</li>
            <li>Double check grammar and spellings</li>
        </ul>
    </div>
    <form class="questionPopupForm">
        <textarea id="questionTextBox" name='question' placeholder="Ask Your Question" maxlength="930"></textarea>
        <input type="hidden" id="question-entity" name='entity' value="<?= $qnadetails[0]['entity'] ?>">
        <input type="hidden" id="question-entity-id" name='entity_id' value="<?= $qnadetails[0]['entity_id'] ?>">
        <input type="hidden" id="question-entity-sub-type" name='entity_sub_type' value="<?= $qnadetails[0]['entity_sub_type'] ?>">
        <p class="charLimit">Character limit 500</p>
        <p class="tinyErrorMsz"></p>
        <div class="popupButtonRow">
            <button type="button" class="closeQuestionPopupTrigger backBtn">Back</button>
            <button class="submitQuestion">Submit</button>
        </div>
    </form>
</dialog>
<dialog class="answerPopupContainer">
    <h2>Answer Now</h2>
    <span class="spriteIcon closeAnswerPopupIcon closeAnswerPopupTrigger"></span>
    <form class="answerPopupForm">
        <textarea id="answerTextBox" placeholder="Enter your answer"></textarea>
        <input id="qnaQuestionId" name='question_id' value="<?= $qnadetails[0]->id ?>" type="hidden">
        <p class="charLimit">Character limit 500</p>
        <p class="tinyErrorMsz"></p>
        <div class="popupButtonRow">
            <button type="button" class="closeAnswerPopupTrigger backBtn">Back</button>
            <button class="submitAnswer">Submit</button>
        </div>
    </form>
</dialog>
<dialog class="successfulSubmitQuestion">
    <div class="thankYouMsg">
        <i class="spriteIcon thankYouIcon"></i>
        <p class="thankYouText"> Your Question Has Successfully Added! </p>
    </div>
</dialog>

<div class="reportPopupContainer">
    <form class="reportPopup">
        <div class="reportHeading">
            <h2>Report Answer</h2>
            <span class="closeReportPopup">
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="21.1734" height="1.76445" rx="0.882225" transform="matrix(0.697537 0.716549 -0.697537 0.716549 1.23047 0.194824)" fill="#282828" />
                    <rect width="21.1734" height="1.76445" rx="0.882225" transform="matrix(-0.697537 0.716549 -0.697537 -0.716549 16 1.45947)" fill="#282828" />
                </svg>
            </span>
        </div>
        <div class="reportBody">
            <ul>
                <li class="reportItem">
                    <input name="reportOptions" type="radio" value="Spam">
                    <div class="reportOption">
                        <label>Spam</label>
                        <span>Serious attack on a group <a href="<?= Url::toDomain() ?>terms-and-conditions">(Learn More)</a></span>
                    </div>
                </li>
                <li class="reportItem">
                    <input name="reportOptions" type="radio" value="Harassment and Bullying">
                    <div class="reportOption">
                        <label>Harassment and Bullying</label>
                        <span>Harassing or threatening an individual <a href="<?= Url::toDomain() ?>terms-and-conditions">(Learn More)</a></span>
                    </div>
                </li>
                <li class="reportItem">
                    <input name="reportOptions" type="radio" value="Harmful Activities">
                    <div class="reportOption">
                        <label>Harmful Activities</label>
                        <span>Glorifying violence including self-harm or intent to seriously harm others <a href="<?= Url::toDomain() ?>terms-and-conditions">(Learn More)</a></span>
                    </div>
                </li>
                <li class="reportItem">
                    <input name="reportOptions" type="radio" value="Adult Content">
                    <div class="reportOption">
                        <label>Adult Content</label>
                        <span>Nudity/Sexual content <a href="<?= Url::toDomain() ?>terms-and-conditions">(Learn More)</a></span>
                    </div>
                </li>
                <li class="reportItem">
                    <input name="reportOptions" type="radio" value="Poorly Written">
                    <div class="reportOption">
                        <label>Poorly Written</label>
                        <span>Not in English or has very bad formatting, grammar, and spelling <a href="<?= Url::toDomain() ?>terms-and-conditions">(Learn More)</a></span>
                    </div>
                </li>
                <li class="reportItem">
                    <input name="reportOptions" type="radio" value="Other">
                    <div class="reportOption">
                        <label>Other</label>
                        <span>Ex. Illegal content (Upon clicking you will be directed to a form where you can provide details)</span>
                    </div>
                </li>
            </ul>
            <textarea class="reportDetail" placeholder="Optional: Explain this report here" maxlength="500"></textarea>
            <div class="reportTextLimit">Character limit: <span>500</span></div>
        </div>
        <div class="reportFooter">
            <input type="hidden" class="answer_id">
            <button type="button" class="reportBackButton" disabled>Back</button>
            <button type="submit" class="reportSubmitButtom" disabled>Submit</button>
        </div>
    </form>
    <div class="reportSubmitScreen">
        <span class="closeReportPopup">
            <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="21.1734" height="1.76445" rx="0.882225" transform="matrix(0.697537 0.716549 -0.697537 0.716549 1.23047 0.194824)" fill="#282828" />
                <rect width="21.1734" height="1.76445" rx="0.882225" transform="matrix(-0.697537 0.716549 -0.697537 -0.716549 16 1.45947)" fill="#282828" />
            </svg>
        </span>
        <?php
        $svgPath = 'M26 51.5C40.0836 51.5 51.5 40.0836 51.5 26C51.5 11.9164 40.0836 0.5 26 0.5C11.9164 0.5 0.5 11.9164 0.5 26C0.5 40.0836 11.9164 51.5 26 51.5ZM39.696 17.9293C39.8153 17.8064 39.9087 17.6607 39.9704 17.5009C40.0322 17.3412 40.0611 17.1706 40.0555 16.9995C40.0499 16.8283 40.0099 16.66 39.9378 16.5046C39.8657 16.3492 39.7631 16.21 39.636 16.0952C39.5089 15.9803 39.36 15.8923 39.1981 15.8363C39.0363 15.7803 38.8648 15.7575';
        $svgPath .= ' 38.6939 15.7692C38.5231 15.7809 38.3563 15.8269 38.2036 15.9045C38.0509 15.9821 37.9154 16.0896 37.8052 16.2207L22.532 33.0979L14.1297 25.0769C13.8853 24.8432 13.558 24.7163 13.2199 24.7239C12.8818 24.7316 12.5606 24.8732 12.3269 25.1177C12.0932 25.3622 11.9663 25.6895 11.9739 26.0276C11.9816 26.3657 12.1232 26.6869 12.3677 26.9205L21.7185 35.8456L22.6659 36.7508L23.5443 35.7793L39.696 17.9293Z'; ?>
        <span class="reportThankyouIcon">
            <svg width="52" height="52" viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="<?= $svgPath ?>" fill="#0966C2" />
            </svg>

        </span>
        <p>Thank You!</p>
        <p>Your report has been successfully submitted.</p>
    </div>
</div>

<div class="linkCopyPopup">
    <p>Link Copied</p>
</div>



<!-- <dialog class="errorWhileSubmitQuestion">
    <div class="errorMsg">
        <p class="errorText"> Your Question Can't Be Empty Nor Morethan 500 Character! </p>
    </div>
</dialog> -->

<?php
$this->registerJsFile(Yii::$app->params['jsPath'] . 'qna-new.js', ['defer' => true, 'depends' => [AppAsset::class]]);
?>