<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\assets\AppAsset;
use yii\helpers\BaseStringHelper;
use common\models\Article;
use yii\widgets\ListView;

//utils
$this->title = 'Find Latest Articles on Colleges, Exams, Courses, Boards & more - Getmyuni';
$this->context->description = 'Find articles by Getmyuni - Renowned source of free, accurate, & insightful college admissions, exam updates and student mentorship information.';
$isMobile = \Yii::$app->devicedetect->isMobile();

$url = '/getgis-article';

$this->registerLinkTag(['href' =>  Url::base(true) . $url, 'rel' => 'alternate']);

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = Yii::t('app', 'Getgis Articles');
$this->params['previous_url'] = Yii::$app->request->referrer;
$this->params['page_url'] = Yii::$app->request->url;
$this->params['entity_name'] = 'Articles-Listing-Page';
$this->params['entity'] = Article::ENTITY_ARTICLE;
$this->params['entity'] = Article::ENTITY_ARTICLE;
$this->params['canonicalUrl'] = Url::base(true) . $url;

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'getgis-article.css', ['depends' => [AppAsset::class]]);
?>

<main>
    <div class="bannerSection">
        <?php
        if (!empty($trendings)): ?>
            <section class="latestInfoSection">
                <h2 class="row"><?= Yii::t('app', 'Trending Articles'); ?></h2>
                <div class="latestInfoList row">
                    <?php foreach ($trendings as $trending):
                        $author_name =  isset($trending->author) ? $trending->author->name : '';
                        $author_slug =  isset($trending->author) ? $trending->author->slug : '';
                        ?>
                        <?php if (empty($trending)) {
                            return '';
                        } ?>
                        <article class="latestInfoDiv">
                            <a href="<?= Url::toGetgisArticleDetail($trending->slug) ?>" title="<?= $trending->h1 ?>">
                                <figure>
                                    <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toGetgisArticleDetail($trending->slug) ?>')" data-src=" <?= ArticleDataHelper::getImage($trending->cover_image) ?>" src="<?= ArticleDataHelper::getImage($trending->cover_image) ?>" alt="<?= $trending->title ?>" />
                                </figure>
                                <div class="latestInfoTxt">
                                    <h3>
                                        <?= BaseStringHelper::truncate($trending->h1, 90) ?? '' ?>
                                    </h3>
                                    <a class="authorName" href="<?= Url::toAllAuthorPost($author_slug) ?>" title="<?= $author_name ?>"><?= $author_name ?></a>
                                </div>
                            </a>
                        </article>
                    <?php endforeach; ?>
                </div>
            </section>
        <?php endif; ?>
        <div class="row categoryArticlesList">
            <div class="col-md-8">
                <section class="latestInfoSection">
                    <h2 class="row"><?= Yii::t('app', 'Latest Articles'); ?></h2>
                    <div class="categoryArticleList">

                        <?php
                        echo ListView::widget([
                            'dataProvider' => $latestArticles,
                            'itemView' => 'partials/_getgis-article-list',
                            'pager' => [
                                'class' => 'LinkPager',
                            ],
                            'viewParams' => [
                                'fullView' => true,
                                'context' => 'main-page',
                                'isMobile' => $isMobile,
                            ],
                            'layout' => "{items}\n{pager}",
                            'pager' => [
                                'class' => '\justinvoelker\separatedpager\LinkPager',
                                'maxButtonCount' => 4,
                                'prevPageLabel' => 'Previous',
                                'nextPageLabel' => 'Next',
                                'prevPageCssClass' => 'prev hidden-xs',
                                'nextPageCssClass' => 'next hidden-xs',
                                'activePageAsLink' => false,
                            ]
                        ]);
                        ?>

                    </div>
                </section>
            </div>
            <div class="col-md-4">
                <aside>
                    <div class="articleSidebarSection">
                        <ul>
                            <?php if (!empty($trendings)): ?>
                                <li data-tab="trendingArtilceList activeLink"><?= Yii::t('app', 'Popular Articles'); ?></li>
                            <?php endif; ?>
                        </ul>
                        <?php if (!empty($trendings)): ?>
                            <div id="trendingArtilceList" class="trendingArtilce activeLink trendingArtilerList">
                                <?php foreach ($trendings as $trending): ?>
                                    <?php if (isset($article) && $article->slug == $trending['slug']): ?>
                                        <?php continue; ?>
                                    <?php endif; ?>
                                    <a class="listCard" href="<?= Url::toGetgisArticleDetail($trending['slug']) ?>" title="<?= $trending['title'] ?>">
                                        <div class="trendingArtilerDiv row">
                                            <div class="sidebarImgDiv">
                                                <img class="lazyload" loading="lazy" src="<?= ArticleDataHelper::getImage($trending['cover_image']) ?>" width="60px" height="60px" alt="<?= $trending['title'] ?>">
                                            </div>
                                            <div class="trendingArtileText">
                                                <p class="sidebarTextLink"><?= $trending['title'] ?? '' ?></p>
                                            </div>
                                        </div>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </aside>
            </div>
        </div>
    </div>

    <section class="articleSection">
        <h2><?= Yii::t('app', 'Explore Articles By Category'); ?></h2>
        <div class="articleRelataedLinks">
            <p class="btn_left over">
                <i class="spriteIcon left_angle"></i>
            </p>
            <p class="btn_right">
                <i class="spriteIcon right_angle"></i>
            </p>
            <ul>
                <?php $categoryCount = 0; ?>
                <?php foreach ($byCategories as $category):
                    if (!empty($category['articles'])) { ?>
                        <li>
                            <a class="<?= $categoryCount == 0 ? 'activeLink' : '' ?>" href="javascript:;" data-tab="<?= $category['slug'] ?>" title="<?= $category['name'] ?>"><?= $category['name'] ?></a>
                        </li>
                        <?php $categoryCount++ ?>
                    <?php } ?>
                <?php endforeach; ?>
            </ul>

        </div>
        <div class="generalArticleSection">
            <?php $articleCategoryCount = 0 ?>
            <?php foreach ($byCategories as $articleCategory):
                if (!empty($articleCategory['articles'])) { ?>
                    <div id="<?= $articleCategory['slug'] ?>" class="tab-content <?= $articleCategoryCount == 0 ? 'activeLink' : '' ?>">
                        <div class="articlesByCategory row">
                            <?php $totalArticleDisplayCount = 0;
                            $s3Path = 'https://media.getmyuni.com/yas/images/defaultcardbanner.png'; ?>
                            <?php foreach ($articleCategory['articles'] as $article):
                                $articleDetailUrl = Url::toGetgisArticleDetail($article['slug']);
                                $article_source_img = $article['cover_image'] ? ArticleDataHelper::getImage($article['cover_image']) : ArticleDataHelper::getImage();
                                $country_article_source_img = $article['cover_image'] ? Url::getStudyAbroadImage($article['cover_image']) : Url::getStudyAbroadImage();
                                ?>
                                <?php if ($totalArticleDisplayCount > 11): ?>
                                    <?php break ?>
                                <?php endif; ?>
                                <div class="browsedArticleDiv">
                                    <a href="<?= $articleDetailUrl; ?>" title="<?= $article['title'] ?>">
                                        <figure>
                                            <img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toGetgisArticleDetail($article['slug']) ?>')" data-src="<?= $article_source_img ?>" src="<?= $article_source_img ?>" alt="<?= $article['title'] ?>" width="237" height="207" />
                                        </figure>
                                        <div class="browsedArticleText">
                                            <h3>
                                                <?= BaseStringHelper::truncate($article['title'], 90) ?? '' ?>
                                            </h3>
                                            <?php if (isset($article['userStatus']) && $article['userStatus'] == '10'): //10 status active
                                                ?>
                                                <p><?= isset($article['user_name']) && $article['lang_code'] != 1 ? $article['user_name'] : $article['name'] ?></p>
                                            <?php else:
                                                $art_default_user = !empty($articleCategory['defaultUser']->name) ? $articleCategory['defaultUser']->name : '';
                                                $art_default_user_slug = !empty($articleCategory['defaultUser']->slug) ? $articleCategory['defaultUser']->slug : '';
                                                ?>
                                                <a class="authorName" href="<?= Url::toAllAuthorPost($art_default_user_slug) ?>" title="<?= $art_default_user ?>"><?= $art_default_user ?></a>
                                            <?php endif; ?>
                                        </div>
                                    </a>
                                </div>
                                <?php $totalArticleDisplayCount++ ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php $articleCategoryCount++ ?>
                <?php } ?>

            <?php endforeach; ?>
        </div>
    </section>
    <aside>
        <div class="horizontalRectangle">
            <div class="appendAdDiv" style="background: #eaeaea;">
                <?php if ($isMobile): ?>
                    <?php echo Ad::unit('GMU_ARTICLES_LANDING_WAP_300x250_BTF', '[300,250]') ?>
                <?php else: ?>
                    <?php echo Ad::unit('GMU_ARTICLES_LANDING_WEB_728x90_BTF', '[728,90]') ?>
                <?php endif; ?>
            </div>
        </div>
    </aside>
</main>