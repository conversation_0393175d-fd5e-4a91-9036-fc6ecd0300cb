<?php

use common\helpers\ArticleDataHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use common\models\GetgisArticle;

// utils
if (empty($article->author)) {
    $author = $article->defaultuser;
} else {
    $author = $article->author;
}
$currentUrl = Url::base(true) . Url::current();
$canonicalUrl = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();
$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = $author ? ContentHelper::getUserProfilePic($author->slug) : '';
$image = Url::defaultCollegeLogo();

// meta key
$this->title = $article->title . ' - Getmyuni' ?? '';
$this->context->description = $article->meta_description ?? '';
$this->context->ogType = 'article';
$assetUrl = Yii::getAlias('@getmyuniExamAsset/');

if (!empty($article->cover_image)) {
    $this->registerMetaTag(['property' => 'og:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'og:image:height', 'content' => '667']);
    $this->registerMetaTag(['property' => 'og:image:alt', 'content' => $article->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:alt', 'content' => $article->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:type', 'content' => 'image/jpeg']);
    $this->registerMetaTag(['property' => 'twitter:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'twitter:image:height', 'content' => '667']);
    $this->registerLinkTag(['href' => ArticleDataHelper::getImage($article->cover_image),  'fetchpriority' => 'high', 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => ArticleDataHelper::getImage($article->cover_image) . ' 300w', 'imagesizes' => '50vw']);
    $this->context->ogImage = ArticleDataHelper::getImage($article->cover_image);
}
if (!empty($authorImage)) {
    $this->registerLinkTag(['href' => $authorImage, 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => $authorImage . ' 300w', 'imagesizes' => '50vw']);
}
$this->registerMetaTag(['name' => 'robots', 'content' => 'max-image-preview:large']);
$this->registerMetaTag(['property' => 'article:published_time', 'content' => !empty($article->published_at) ? date(DATE_ATOM, strtotime($article->published_at)) : date(DATE_ATOM, strtotime($article->created_at))]);

$this->registerMetaTag(['property' => 'article:modified_time', 'content' => date(DATE_ATOM, strtotime($article->updated_at))]);
// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Getgis Articles'), 'url' => Url::toGetgisArticles(), 'title' => 'Articles'];
$this->params['breadcrumbs'][] = ContentHelper::htmlDecode(stripslashes($article->h1), false);
$this->params['entity_name'] = addslashes($article->title) ?? '';
$this->params['entity_id'] = $article->id ?? 0;
$this->params['entity'] = GetgisArticle::ENTITY_GETGIS_ARTICLE;
$this->params['entitySlug'] = $entitySlug ?? $article->slug;
$this->params['entityDisplayName'] = empty($entityDisplayName) ? $article->title : $entityDisplayName;
$this->params['canonicalUrl'] = $canonicalUrl;

$url = '#';

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'article-detail.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'bottom-widget.css', ['depends' => [AppAsset::class]]);

$courseState = json_encode([]);
if (!empty($stateList)) {
    $this->params['stateList'] = $stateList;
    if (isset($stateList['data']) && !empty($stateList['data'])) {
        $courseState = json_encode(array_keys($stateList['data']));
    }
}
// schema
if (!empty($faqs)) {
    foreach ($faqs as $faq) {
        $loadFaq[] = [
            '@type' => 'Question',
            'name' => ContentHelper::htmlDecode($faq->question, true),
            'acceptedAnswer' => [
                '@type' => 'Answer',
                'text' => ContentHelper::htmlDecode($faq->answer, false)
            ]
        ];
    }
    $this->params['schema1'] = \yii\helpers\Json::encode([[
        '@context' => 'http://schema.org',
        '@type' => 'FAQPage',
        'mainEntity' => $loadFaq
    ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
}

$this->params['schema'] = \yii\helpers\Json::encode([[
    '@context' => 'http://schema.org',
    '@type' => 'Article',
    'mainEntityOfPage' => [
        '@type' => 'WebPage',
        '@id' => $canonicalUrl,
    ],
    'headline' => $this->title,
    'image' => [ArticleDataHelper::getImage($article->cover_image)],
    'datePublished' => !empty($article->published_at) ? date(DATE_ATOM, strtotime($article->published_at)) : date(DATE_ATOM, strtotime($article->created_at)),
    'dateModified' => date(DATE_ATOM, strtotime($article->updated_at)),
    'author' => [
        '@type' => 'Person',
        'name' => $article->author ? $article->author->name : ''
    ],
    'publisher' => [
        '@type' => 'Organization',
        'name' => 'Getmyuni',
        'logo' => [
            '@type' => 'ImageObject',
            'url' => Yii::$app->params['gmuLogo']
        ],
    ],
    'description' => $article->meta_description
]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

$h2Text = ContentHelper::getGenerateHtml($article->description, '');
$article->description = $h2Text['content'];
?>

<div class="containerMargin">
    <div class="row">
        <div class="col-md-12">
            <div class="articleHeader">
                <section class="pageDescription">
                    <div class="row">
                        <div class="col-md-12">
                            <h1><?= ContentHelper::htmlDecode(stripslashes($article->h1), false) ?></h1>
                            <div class="authorInfoAndTranslateBtn">
                                <div class="updated-info row">
                                    <img class="lazyload" loading="lazy" width="60" height="60" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author ? $author->name . ' Image' : '' ?>" />
                                    <span class="updatedDetails">
                                        <?php if ($author): ?>
                                            <div class="updatedBy">
                                                <p><a href="<?= $author ? '/author/' . $author->slug : '#' ?>"><?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?></a>, </p>
                                            </div>
                                            <p><span><?= Yii::$app->formatter->asDate($article->updated_at ?? 'today') ?> </span>
                                        <?php endif; ?>
                                            </p>
                                            <ul>
                                                <p><?= Yii::t('app', 'Share it on') ?>:</p>
                                                <li>
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $canonicalUrl ?>" target="_blank" rel="noopener nofollow" class="spriteIcon greyFbIcon"></a>
                                                </li>
                                                <li>
                                                    <a href="https://twitter.com/share?url=<?= $canonicalUrl ?>" class="spriteIcon greyTwitterIcon" rel="noopener nofollow" target="_blank"></a>
                                                </li>
                                            </ul>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <div class="col-md-8">
            <main>
                <article>
                    <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                        <div class="horizontalRectangle">
                            <div class="appendAdDiv" style="<?= $isMobile ? 'height: 50px;' : '' ?>background:#EAEAEA;">
                                <?php /* if ($isMobile) : ?>
                                    <?php echo Ad::unit('GMU_ARTICLES_LANDING_WAP_300x50_ATF', '[300,50]') ?>
                                <?php else : */ ?>
                                <?php echo Ad::unit('GMU_ARTICLES_LANDING_WEB_728x90_ATF', '[728,90]') ?>
                                <?php //endif;
                                ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    <div class="articelNote">
                        <p><?= $article->meta_description ?></p>
                    </div>
                    <?php if (!empty($article->cover_image)): ?>
                        <div class="bannerImg">
                            <picture>
                                <source media="(max-width: 500px)" srcset="<?= ArticleDataHelper::getImage($article->cover_image) ?>">
                                <img width="1200" height="675" src="<?= ArticleDataHelper::getImage($article->cover_image) ?>" alt="<?= $article->h1 ?>" />
                            </picture>
                        </div><br />
                    <?php endif; ?>
                    <?php if (!empty($article->audio)): ?>
                        <div class="audio-container">
                            <span class="audio-text"><?= Yii::t('app', 'Listen to this article') ?></span>
                            <audio controls id="myAudio" preload="none">
                                <source src="<?php echo $article->audio ?>" type="audio/mpeg">
                                Your browser does not support the audio element.
                            </audio>
                        </div>
                    <?php endif; ?>
                    <div class="articleInfo">
                        <?= ContentHelper::removeStyleTag(stripslashes(
                            html_entity_decode(DataHelper::parseDomainUrlInContent($article->description))
                        )) ?>
                        <?php if (!empty($tags)): ?>
                            <div class="tagsDiv">
                                <ul>
                                    <li><?= Yii::t('app', 'TAGS') ?></li>
                                    <?php foreach ($tags as $tag): ?>
                                        <li><a href="<?= Url::toTag($tag->slug) ?>" title="<?= $tag->name ?>"><?= strtoupper($tag->name) ?></a></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </article>

                <?php if (!empty($faqs)): ?>
                    <section class="faq_section">
                        <?php if (empty($article->display_name)) { ?>
                            <h2><?= Yii::t('app', 'FAQs') ?></h2>
                        <?php } else { ?>
                            <h2><?= Yii::t('app', ' FAQs on ' . $article->display_name) ?></h2>
                        <?php } ?>
                        <div class="faqDiv">
                            <?php foreach ($faqs as $faq): ?>
                                <div>
                                    <p class="faq_question"><?= 'Q: ' . ContentHelper::htmlDecode($faq->question, true) ?></p>
                                    <div class="faq_answer" style="display: none;">
                                        <?= 'A: ' . ContentHelper::htmlDecode($faq->answer, false) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>
            </main>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Ad::unit('GMU_ARTICLES_DETAIL_WAP_300x250_MTF_2', '[300,250]') ?>
                        <?php else: ?>
                            <?php echo Ad::unit('GMU_ARTICLES_DETAIL_WEB_720x90_MTF_2', '[728,90]') ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <div class="col-md-4">
            <aside class="article-aside">
                <?php if (!empty($recentArticles)): ?>
                    <?= $this->render('partials/_sidebar-articles', [
                        // 'trendings' => $trendings,
                        'recentArticles' => $recentArticles,
                        'article' => $article
                    ]); ?>
                <?php endif; ?>

                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="squareDiv">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Ad::unit('GMU_ARTICLES_DETAIL_WAP_300x250_MTF_1', '[300,250]') ?>
                            <?php else: ?>
                                <?php echo Ad::unit('GMU_ARTICLES_DETAIL_WEB_300x250_MTF_1', '[300,250]') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif;
                if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="verticleRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php echo Ad::unit('GMU_ARTICLES_DETAIL_WEB_300x600_MTF_3', '[300,600]') ?>
                        </div>
                    </div>
                <?php endif; ?>
            </aside>
        </div>
    </div>
</div>