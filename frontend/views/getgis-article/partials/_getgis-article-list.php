<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;

if (empty($model->author)) {
    $author = $model->defaultuser;
} else {
    $author = $model->author;
}

?>
<article class="catgegoryArticle">
    <div class="row">
        <div class="articleBanner">
            <figure>
                <?php $cardImage = $model->cover_image ? ArticleDataHelper::getImage($model->cover_image) : ArticleDataHelper::getImage() ?>
                <img onclick="gmu.url.goto('<?= Url::toGetgisArticleDetail($model->slug) ?>')" data-src="<?= $cardImage ?>" src="<?= $cardImage ?>" alt="<?= $model->h1 ?>" />
            </figure>
        </div>
        <div class="articleText">
            <h2>
                <a href="<?= Url::toGetgisArticleDetail($model->slug) ?>" title="<?= $model->h1 ?>"> <?= BaseStringHelper::truncateWords($model->h1, 9) ?></a>
            </h2>

            <p><?= $model->meta_description ?></p>
            <div class="updated-info row">

                <?php if ($author):
                    $author_name =  isset($author) ? $author->name : '';
                    ?>
                    <div class="updatedBy">
                        <?php $authorImage = isset($author->profile->image) ? Yii::getAlias('@profileDPFrontend') . '/' . $author->profile->image : '/yas/images/usericon.png' ?>
                        <img height="60" width="60" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author->name . ' Image' ?>">
                        <p class="authorName"><?= isset($author_name) ? $author_name : '' ?></p>
                    </div>
                <?php endif; ?>
                <p><?= Yii::$app->formatter->asDate($model->updated_at ?? 'today') ?></p>

            </div>
        </div>
    </div>
</article>