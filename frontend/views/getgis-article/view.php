<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\GetgisArticle */

$this->title = $model->title;
$this->params['breadcrumbs'][] = ['label' => 'Getgis Articles', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="getgis-article-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('Delete', ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger btn-flat',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                'author_id',
                'category_id',
                'title',
                'display_name',
                'slug',
                'cover_image',
                'thumbnail_image',
                'description:ntext',
                'h1',
                'meta_title',
                'meta_description',
                'view_count',
                'is_popular',
                'status',
                'reading_time',
                'created_by',
                'updated_by',
                'published_by',
                'published_at',
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>
