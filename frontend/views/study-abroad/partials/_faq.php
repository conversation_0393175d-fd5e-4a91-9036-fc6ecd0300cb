<?php

use common\helpers\ContentHelper;
?>
<style>
    .faq-section {
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .faq-section a>p {
        font-size: 18px;
    }

    .faq-section {
        padding-bottom: 29px;
        border-bottom: 1px solid #eee;
        padding-top: 10px;
        font-size: 16px;
    }

    .faq_naming p {
        display: inline-block;
        font-size: 16px !important;
        max-width: 100%;
        margin-left: 25px;
        overflow-wrap: break-word;
    }

    .answer-data {
        padding-top: 10px !important;
    }

    .answer-data p {
        overflow-wrap: break-word;
    }

    .answer-data ul li {
        font-size: 14px;
        overflow-wrap: break-word;
    }

    .faq_naming span {
        /*display: inline-block!important;*/
        padding-right: 10px;
    }

    .details-icons {
        width: 20px;
        height: 13px;
        float: right;
    }

    .up_arrow,
    .down_arrow {
        display: inline-block;
        background: url('../../images/master_sprite.png') no-repeat;
        overflow: hidden;
        text-indent: -9999px;
        text-align: left;
    }

    .down_arrow {
        background-position: -6px -165px;
        width: 35px;
        height: 20px
    }

    .up_arrow {
        background-position: -37px -165px;
        width: 35px;
        height: 20px
    }

    @media (max-width: 991px) {
        .faq_naming p {
            width: 90% !important;
        }

        .collapse .card.card-body {
            color: #333333 !important;
            font-weight: 600 !important;
        }

        .answer-data {
            font-weight: 400 !important;
        }
    }

    .faqDiv {
        /* padding-bottom: 20px; */
        border-bottom: 1px solid #eee;
        padding-top: 10px;
    }

    .faqQuestion {
        font-weight: 600;
        margin: 0;
        position: relative;
        font-size: 16px;
        line-height: 29px;
        color: #4374b9;
        cursor: pointer;
        padding-right: 35px;
    }

    .faqQuestion p {
        margin-left: 23px;
    }

    .faqAnswer {
        font-size: 14px;
        line-height: 25px;
        color: #333333;
        padding: 1px 27px;
        margin: 0;
        padding-bottom: 0;

    }

    .faqQuestion:after {
        content: "";
        position: absolute;
        right: 0px;
        top: 0px;
        background: url('https://www.getmyuni.com//assets/images/master_sprite.png') no-repeat;
        background-position: -6px -165px;
        width: 25px;
        height: 20px;
        transition: 0.2s ease;
    }

    .faqQuestion.changeAngle:after {
        transform: rotate(-180deg);
    }

    .faqTag {
        display: inline-block;
        position: absolute;
    }

    .faqAnswer div {
        margin: 0;
        word-break: break-all;
    }
</style>
<section class="info-card content-data">
    <h2><?= Yii::t('app', 'Frequently Asked Questions') ?></h2>
    <div class="">
        <?php foreach ($faqs as $key => $faq): ?>
            <div class="faqDiv">
                <div class="faqQuestion"><span class="faqTag"><?= $key ?>.</span>
                    <p><?= ContentHelper::htmlDecode($faq->question, true) ?></p>
                </div>
                <div class="faqAnswer">
                    <?= 'A: ' . ContentHelper::htmlDecode($faq->answer, false) ?> 
                </div>
            </div>
        <?php endforeach; ?>
</section>