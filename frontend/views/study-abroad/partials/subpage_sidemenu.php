<?php

use common\helpers\DataHelper;

$isMobile = \Yii::$app->devicedetect->isMobile();
?>

<?php if (!$isMobile): ?>
    <div class="col-md-2 hidden-xs hidden-sm" style="padding: 0px;">
        <aside class="scroll-to-fixed-left">
            <nav class="navbar" id="menu-center">
                <ul class="nav sa_sidemenu">
                    <li><a class="left_menu sa_sidemenu_active overview" data-sidemenu="overview" data-href="#overview">
                            <svg class="aside-svg-img">
                                <use xlink:href="#CP_x5F_21" transform="matrix(1, 0, 0, -1, 0, 20)" width="20" height="20" />
                            </svg>Overview </a>
                    </li>
                    <?php if (!empty($courses)): ?>
                        <li><a class="left_menu program" data-sidemenu="program" data-href="#program">
                                <svg class="aside-svg-img">
                                    <use xlink:href="#CP_x5F_46" transform="matrix(1, 0, 0, -1, 0, 20)" width="20" height="20" />
                                </svg> Browse by Program</a>
                        </li>
                    <?php endif; ?>
                    <?php
                    if (!empty($sideMenu)): ?>
                        <?php foreach ($sideMenu as $key => $subpageSideMenu):
                            if (!DataHelper::hasSideMenuContent($key, $sideMenu, $college, $fees, $degreeStreamArray, $contactDetails)) {
                                continue;
                            }
                            $icon = isset(DataHelper::$sideMenuIcons[$key]) ? DataHelper::$sideMenuIcons[$key] : '#CP_x5F_21'; ?>
                            <li><a class="left_menu <?= $subpageSideMenu->saCollegeSubpageSidemenu->slug ?>" data-sidemenu="<?= $subpageSideMenu->saCollegeSubpageSidemenu->slug ?>" data-href="#<?= $subpageSideMenu->saCollegeSubpageSidemenu->slug ?>">
                                    <svg class="aside-svg-img">
                                        <use xlink:href="<?= $icon ?>" transform="matrix(1, 0, 0, -1, 0, 20)" width="20" height="20" />
                                    </svg> <?= $subpageSideMenu->saCollegeSubpageSidemenu->name ?> </a>
                            </li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    <?php if (isset($faq_details) && $faq_details) { ?>
                        <li><a class="faq mob_menu_font" data-sidemenu="faq" data-href="#faq">
                                <svg class="aside-svg-img">
                                    <use xlink:href="#CP_x5F_51" width="20" height="20" transform="rotate(-180)" x="-20" y="-20" />
                                </svg> Frequently Asked Questions</a>
                        </li>
                    <?php } ?>
                </ul>
            </nav>
        </aside>
    </div>
<?php endif; ?>

<?php if ($isMobile): ?>
    <div class="no-margin margin_on_mobile" id="mobile-table-menu" style="z-index: 1000;display: none;">
        <div id="slider_container" class="clearfix">
            <div id="mobile-slick-slider-menu">
                <nav class="sa_nav sa_nav_style">
                    <div id="mobile-table-content" class="sa_menu">
                        <p class="inline-display mob_menu_font" style="margin:0px;width:96%;">Table of Content</p>
                        <p class="sa_menu" id="add-icon" style="float: right!important;font-size: 20px;color:#fff;">+</p>
                        <p class="sa_menu" id="remove-icon" style="float: right!important;font-size: 20px;color:#fff;display: none;">-</p>
                        <div class="vertical-container">
                            <ul class="nav sa_sub_menu" style="display: none;">
                                <li><a class="overview mob_menu_font" data-sidemenu="overview" data-href="#overview">

                                        <svg class="aside-svg-img">
                                            <use xlink:href="#CP_x5F_40" width="20" height="20" />
                                        </svg> Overview</a>
                                </li>
                                <?php if (!empty($courses)): ?>
                                    <li><a class="overview mob_menu_font" data-sidemenu="program" data-href="#program">
                                            <svg class="aside-svg-img">
                                                <use xlink:href="#CP_x5F_46" transform="matrix(1, 0, 0, -1, 0, 20)" width="20" height="20" />
                                            </svg> Browse by Program</a>
                                    </li>
                                <?php endif; ?>

                                <?php
                                if (!empty($sideMenu)): ?>
                                    <?php foreach ($sideMenu as $key => $subpageSideMenu):
                                        if (!DataHelper::hasSideMenuContent($key, $sideMenu, $college, $fees, $degreeStreamArray, $contactDetails)) {
                                            continue;
                                        }
                                        $icon = isset(DataHelper::$sideMenuIcons[$key]) ? DataHelper::$sideMenuIcons[$key] : '#CP_x5F_21'; ?>
                                        <li><a class="mob_menu_font <?= $subpageSideMenu->saCollegeSubpageSidemenu->slug ?>" data-sidemenu="<?= $subpageSideMenu->saCollegeSubpageSidemenu->slug ?>" data-href="#<?= $subpageSideMenu->saCollegeSubpageSidemenu->slug ?>">
                                                <svg class="aside-svg-img">
                                                    <use xlink:href="<?= $icon ?>" width="20" height="20" transform="rotate(-180)" x="-20" y="-20" />
                                                </svg> <?= $subpageSideMenu->saCollegeSubpageSidemenu->name ?> </a>
                                        </li>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <?php
                                if (isset($faq_details) && $faq_details) { ?>
                                    <li><a class="faq mob_menu_font" data-sidemenu="faq" data-href="#faq">
                                            <svg class="aside-svg-img">
                                                <use xlink:href="#CP_x5F_51" width="20" height="20" transform="rotate(-180)" x="-20" y="-20" />
                                            </svg> Frequently Ask Questions</a>
                                    </li>
                                <?php } ?>
                            </ul>
                        </div>
                    </div>
                    <?php if (isset($latest_createdon) && !empty($latest_createdon)) { ?>
                        <p style="margin-top: 20px;color:#fff;font-size:12px;text-align:center;">

                            <?php if (isset($latest_createdon['date']) && !empty($latest_createdon['date'])) { ?>
                                Updated on - <?php echo date('M dS, Y | h:i A', strtotime($latest_createdon['date'])); ?>
                            <?php } ?>

                            <?php if (isset($latest_createdon['name']) && !empty($latest_createdon['name'])) { ?>
                                By <?= $latest_createdon['name']; ?>
                            <?php } ?>
                        </p>
                    <?php } ?>
                </nav>

            </div>
        </div>
    </div>
<?php endif; ?>