<?php

use common\helpers\CollegeHelper;
use common\models\SaCountry;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Url;

$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->title = !empty($subpageContent->title) ? $subpageContent->title : $college->name . 'Programs & Tuition';
$this->context->description = !empty($subpageContent->meta_description) ? $subpageContent->meta_description : 'Interested to Study in' . $country->name . '? Know more details on Colleges & Universities, Cost of Study, Cost of Living, Student Visa Requirements in' . $country->name . 'and more.';
$this->context->ogImage = '';

// // breadcrumbs
// $this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
// $this->params['breadcrumbs'][] = Yii::t('app', 'Study Abroad');

// schema
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}

// page specific assets
$this->params['entity'] = SaCountry::ENTITY;
?>

<head>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="//fonts.googleapis.com/css?family=Open+Sans" />
    <link rel="stylesheet" href="/yas/css/version2/abroad_detail.css">
</head>


<?= $this->render('partials/top_college_detail_card', [
    'college' => $college,
    'country' => $country,
    'page' => 'Courses Offered',
    'user' => $user
]); ?>

<?= $this->render('partials/subpage_menu', [
    'college' => $college,
    'menus' => $menus ?? [],
    'page' => $page,
    'countrySlug' => $countrySlug,
]); ?>

<?php if ($isMobile): ?>
    <?= $this->render('partials/subpage_sidemenu', [
        'sideMenu' => $sideMenu ?? [],
        'faq_details' => $faq_details ?? [],
        'courses' => $courses,
        'college' => $college,
        'fees' => $fees ?? [],
        'degreeStreamArray' => $degreeStreamArray ?? [],
        'contactDetails' => $contactDetails ?? [],
    ]); ?>
<?php endif; ?>

<div class="sa_master_svg"></div>
<div class="container-fluid no-pading margin-page">
    <div class="col-9" style="padding-left:0px;">
        <?php if (!$isMobile): ?>
            <?= $this->render('partials/subpage_sidemenu', [
                'sideMenu' => $sideMenu ?? [],
                'faq_details' => $faq_details ?? [],
                'courses' => $courses,
                'college' => $college,
                'fees' => $fees ?? [],
                'degreeStreamArray' => $degreeStreamArray ?? [],
                'contactDetails' => $contactDetails ?? [],
            ]); ?>
        <?php endif; ?>

        <div class="col-md-10 midlepart">
            <div style="text-align: center; margin-top: 10px;">
                <?php if (!$isMobile) {
                    echo Ad::unit('GMU_STUDY_ABROAD_DETAIL_PAGE_WEB_ATF_728x90', '[728,90]');
                } else {
                    echo Ad::unit('GMU_STUDY_ABROAD_DETAIL_PAGE_WAP_ATF_300x50', '[300,50]');
                } ?>
            </div>

            <section class="row card_white_background" id="overview" style="padding: 5px">
                <div class="overview_text" style="line-height: 22px; font-family: Open Sans;">
                    <?= ((isset($subpageContent->content) && !empty(isset($subpageContent->content))) ? stripslashes(html_entity_decode($subpageContent->content)) : ''); ?>
                </div>
            </section>

            <!-- FAQ START-->
            <?php if (isset($faq_details) && $faq_details) { ?>
                <div class="row faqsection" id="faq">
                    <?php if (isset($faq_title) && !empty($faq_title)) {
                        $title = $faq_title;
                    } else {
                        $title = 'Frequently Asked Questions';
                    } ?>
                    <h2 class="h4_title_hrader"><?= $title; ?></h2>
                    <?php $this->load->view('faq/faq_comments', $faq_details); ?>
                </div>
            <?php } ?>
            <!--FAQ ENDS -->

            <div style="text-align: center; margin-top: 10px;">
                <?php if (!$isMobile) {
                    echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280');
                } else {
                    echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280');
                } ?>
            </div>

            <?php if (!empty($degreeStreamArray)): ?>
                <?= $this->render('partials/_program_offered_card', [
                    'sideMenu' => $sideMenu ?? [],
                    'subpageContent' => $subpageContent ?? [],
                    'college' => $college ?? [],
                    'degreeStreamArray' => $degreeStreamArray,
                    'headCard' => $headCard,
                    'only_degree_array' => $only_degree_array,
                    'courses' => $courses,
                    'country' => $country
                ]); ?>
            <?php endif; ?>

            <div style="text-align: center; margin-top: 10px;">
                <?php if (!$isMobile) {
                    echo Freestartads::unit('getmyuni-com_bottom', '__728x90');
                } else {
                    echo Freestartads::unit('getmyuni-com_bottom', '__300x250');
                } ?>
            </div>
        </div>
    </div>

    <div class="col-3" id="desktop-top-recommand">
        <div id="right-fix" class="scroll-to-fixed-right" style="z-index: 1000;">
            <div class="right-lead-card hidden-xs">
                <svg>
                    <use xlink:href="#CP_x5F_28" transform="rotate(180)" x="-75" y="-75"></use>
                </svg>
                <div>Interested to study</div>
                <div>in <?= $country->name ?>?</div>
                <!-- <button class="loadLeadModelNew" data-click-source="desktop_right_expert">Talk To Experts</button> -->
                <?= $this->render('partials/_lead_button', [
                    'ctaText' => 'Talk to Experts',
                    'ctaPosition' => 'right_aside_' . $page . '_cta1',
                    'formTitle' => 'Thank you for your interest',
                    'subHeadingText' => 'Please leave your information to get the best suggested colleges and free counseling.',
                    'className' => 'loadLeadModelNew',
                    'country' => $country,
                    'entity' => SaCountry::ENTITY_COUNTRY_COLLEGE
                ]); ?>
            </div>

            <?= $this->render('partials/_top_college_recommendation', [
                'topCollegeRecommendations' => $topCollegeRecommendations,
                'countrySlug' => $countrySlug,
            ]); ?>
        </div>
    </div>

    <!-- Mobile view top recommand  -->
    <?php if ($isMobile): ?>
        <?= $this->render('partials/_top_college_recommendation', [
            'topCollegeRecommendations' => $topCollegeRecommendations,
            'countrySlug' => $countrySlug,
        ]); ?>
    <?php endif; ?>

</div>
<div class="bottom_nav mobile_view">
    <?= $this->render('partials/_lead_button', [
        'ctaText' => 'Talk to Experts',
        'ctaPosition' => 'bottom_mobile' . $page . '_cta1',
        'formTitle' => 'Thank you for your interest',
        'subHeadingText' => 'Please leave your information to get the best suggested colleges and free counseling.',
        'className' => 'col-xs-6 loadLeadModelNew',
        'entity' => SaCountry::ENTITY_COUNTRY_COLLEGE,
        'country' => $country,
        'college' => $college
    ]); ?>
    <?= $this->render('partials/_lead_button', [
        'ctaText' => 'Apply Now',
        'ctaPosition' => 'bottom_mobile' . $page . '_cta2',
        'formTitle' => 'Thank you for your interest',
        'subHeadingText' => 'Please leave your information to get the best suggested colleges and free counseling.',
        'className' => 'col-xs-6 loadLeadModelNew',
        'country' => $country,
        'entity' => SaCountry::ENTITY_COUNTRY_COLLEGE
    ]); ?>
</div>
<div class="svg_apnd"></div>


<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
<div id="foot">

</div>