<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\models\SaCountry;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;

$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->title = 'Study in' . $country->name . '- Find Colleges and Universities';
$this->context->description = 'Interested to Study in' . $country->name . '? Know more details on Colleges & Universities, Cost of Study, Cost of Living, Student Visa Requirements in' . $country->name . 'and more.';
$this->context->ogImage = '';

// // breadcrumbs
// $this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
// $this->params['breadcrumbs'][] = Yii::t('app', 'Study Abroad');

// schema
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}

// page specific assets
$this->params['entity'] = SaCountry::ENTITY;
$SITE_IMAGES = 'https://www.getmyuni.com/yas/images/';
$GETMYUNI_CDN_IMAGES = 'https://media.getmyuni.com/azure/assets/images/';

?>

<head>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="//fonts.googleapis.com/css?family=Open+Sans" />
    <link rel="stylesheet" href="/yas/css/version2/abroad.css">
    <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.3/themes/smoothness/jquery-ui.css" />
</head>

<div class="sa_master_svg"></div>
<main>
    <img class="background_top hidden-sm" src="<?php echo $SITE_IMAGES; ?>abroad_college/background.svg" alt="" />
    <img class="background_top hidden-md hidden-lg" src="<?php echo $SITE_IMAGES; ?>abroad_college/background_mobile.svg"
        alt="" />
    <div class="container top_section">
        <h1><?= !empty($countryDetail->h1) ? $countryDetail->h1 : 'Study in ' . $country->name; ?></h1>
        <p>Explore Top Universities and Colleges in <?= $country->name ?>.<br>Get Updates on Tuition, Courses Offered, Duration and more.</p>
        <div class="search">
            <input name="search" class="sa_college_search" placeholder="Find your dream college" data-country=<?= $country->id ?>>
            <button>Search</button>
        </div>
        <!-- <button class="btn_help loadLeadModelNew" data-click-source="Country Landing page">Help me with options</button> -->
        <?= frontend\helpers\Html::leadButton(
            'Help me with options',
            [
                'entity' => SaCountry::ENTITY_COUNTRY,
                'entityId' => $country->id ?? null,
                'ctaLocation' => 'study_abroad_country_desktop_1',
                'ctaText' => 'Help me with options',
                'leadformtitle' => 'SUBSCRIBE NOW TO GET UPDATES'
            ],
            ['class' => 'primaryBtn registerNow btn_help loadLeadModelNew'],
            'sa-js-open-lead-form'
        ) ?>
        <svg class="girl" id="desktop-girl">
            <use transform="rotate(360)" href="#SB_4" x="0" y="0"></use>
        </svg>
        <svg class="girl" id="mobile-girl" style="display:none;" width="140" height="140">
            <use href="#SB_4" transform="rotate(360)" x="0" y="0"></use>
        </svg>
    </div>

    <div class="container-fluid middle_section">
        <div>
            <img alt="Studying" src="<?php echo $SITE_IMAGES; ?>abroad_college/studying.svg">
        </div>
        <div class="country_description_text">
            <?= !empty($countryDetail->description) ? ContentHelper::htmlDecode(stripslashes($countryDetail->description)) : ''; ?>
            <br>
            <!-- <button class="btn_help loadLeadModelNew" data-click-source="Country Landing page">Help me with options</button> -->
            <?= frontend\helpers\Html::leadButton(
                'Help me with options',
                [
                    'entity' => SaCountry::ENTITY_COUNTRY,
                    'entityId' => $country->id ?? null,
                    'ctaLocation' => 'study_abroad_country_desktop_2',
                    'ctaText' => 'Help me with options',
                    'leadformtitle' => 'SUBSCRIBE NOW TO GET UPDATES'
                ],
                ['class' => 'primaryBtn registerNow btn_help loadLeadModelNew'],
                'sa-js-open-lead-form'
            ) ?>
        </div>
    </div>
    <div class="gmu-ad container">
        <?php if (!$isMobile) {
            echo Ad::unit('GMU_STUDY_ABROAD_LANDING_PAGE_WEB_MTF_1_728x90', '[728,90]');
        } else {
            echo Ad::unit('GMU_STUDY_ABROAD_LANDING_PAGE_WAP_MTF_1_300x250', '[300,250]');
        } ?>
    </div>
    <div style="margin:50px 50px;">
        <?= !empty($countryDetail->content) ? ContentHelper::htmlDecode(stripslashes($countryDetail->content)) : ''; ?>
    </div>

    <!-- <button class="ratechanceBtn loadLeadModelNew" data-click-source="sa_landing_rmc">Rate My Chances</button> -->

    <?= frontend\helpers\Html::leadButton(
        'Rate My Chances',
        [
            'entity' => SaCountry::ENTITY_COUNTRY,
            'entityId' => $country->id ?? null,
            'ctaLocation' => 'study_abroad_country_desktop_2',
            'ctaText' => 'Rate My Chances',
            'leadformtitle' => 'SUBSCRIBE NOW TO GET UPDATES'
        ],
        ['class' => 'ratechanceBtn loadLeadModelNew'],
        'sa-js-open-lead-form'
    ) ?>

    <div class="gmu-ad container">
        <?php if (!$isMobile) {
            echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280');
        } else {
            echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280');
        } ?>
    </div>

    <div class="carousel_section">
        <h2>Top Colleges and Universities in <?= $country->name; ?></h2>
        <div class="carousel_container">
            <div class="btn_carousel_left">
                <svg class="icon" viewBox="-55.2 -78.4 110.4 156.8">
                    <path id="Path_277-2_1_" style="fill:#D4D4D2;" d="M3.3,76.7l1.2,1.7h50.7L54,76.7L-4-0.6l58.1-77.8H3.3L-55.2-0.6L3.3,76.7z"></path>
                </svg>
            </div>
            <div class="slide_container">

                <?php if (!empty($colleges)): ?>
                    <?= $this->render('partials/_more_college', [
                        'colleges' => $colleges,
                        'country' => $country
                    ]) ?>
                <?php endif; ?>
                <div class="box_container more card">
                    <div class="img_container">
                        <span class="background"></span>
                        <span class="logo"></span>
                    </div>
                    <h2 class="title"></h2>
                    <h4 class="location"></h4>
                    <div class="course_container">
                    </div>
                    <button class="btn_apply"></button>
                    <button class="btn_show_more" data-country='<?= $country->id ?>' data-page="1">Show More</button>
                </div>
            </div>
            <div class="btn_carousel_right">
                <svg class="icon" viewBox="-55.25 -78.4 110.5 156.8">
                    <path id="Path_277_1_" style="fill:#D4D4D2;" d="M55.25-0.6l-58.6-77.8h-50.7l58,77.8l-58,77.3l-1.2,1.7h50.7l1.2-1.7L55.25-0.6z"></path>
                </svg>
            </div>
        </div>
    </div>

    <?php if (!empty($faqs)): ?>
        <div class="info-card content-data faqsection carousel_section">
            <?= $this->render('partials/_faq', [
                'faqs' => $faqs
            ]); ?>
        </div>
    <?php endif; ?>

    <div class="container article_section">
        <h2>Related Articles</h2>
        <p>
            <?= !empty($countryDetail->related_article_h1) ? ContentHelper::htmlDecode($countryDetail->related_article_h1) : ''; ?>
        </p>

        <div class="article_container">
            <?php if (!empty($relatedArticles)): ?>
                <?php foreach ($relatedArticles as $article): ?>
                    <div class="col-lg-3 box_container card">
                        <span class="background" style="background-image: url(' <?= $article->cover_image ? Url::getStudyAbroadImage($article->cover_image) : Url::getStudyAbroadImage() ?>');"></span>
                        <h2 class="content-haeding"><a href="<?= Url::toCountryDetail($article->country_slug, $article->slug) ?>" title="<?= $article->h1 ?>"><?= BaseStringHelper::truncateWords($article->h1, 15) ?></a></h2>
                        <div class="read-more-div">
                            <p class="content"><?= stripslashes(html_entity_decode($article->description)); ?></p>
                        </div>
                        <a class="read_more" href="<?= Url::toCountryDetail($article->country_slug, $article->slug) ?>" target="_blank">Read more....</a>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <?php if (!empty($article)) { ?>
            <div class="view_all"><a href="<?= $country->slug . '/articles'; ?>" class="btn_view_all">View All Articles</a></div>
    </div>
        <?php } ?>
<div class="container bottom_section">
    <p class="resource text-center">Resources</p>
    <div class="right_container mx-auto">
        <div class="icon_text">
            <svg class="icon">
                <use href="#SB_6"></use>
            </svg>
            <?= frontend\helpers\Html::leadButton(
                'Need a guide ?',
                [
                    'entity' => SaCountry::ENTITY_COUNTRY,
                    'entityId' => $country->id ?? null,
                    'ctaLocation' => 'study_abroad_country_desktop_2',
                    'ctaText' => 'Need a guide ?',
                    'leadformtitle' => 'SUBSCRIBE NOW TO GET UPDATES'
                ],
                ['class' => 'content needguide loadLeadModelNew'],
                'sa-js-open-lead-form'
            ) ?>
        </div>
        <div class="icon_text">
            <svg class="icon">
                <use href="#SB_5"></use>
            </svg>
            <a href=" https://www.getmyuni.com/articles/study-abroad" class="content">Travel related articles</a>
        </div>
    </div>
</div>
</main>
<br>


<div class="gmu-ad container">
    <?php if (!$isMobile) {
        echo Freestartads::unit('getmyuni-com_bottom', '__728x90');
    } else {
        echo Freestartads::unit('getmyuni-com_bottom', '__300x250');
    } ?>
</div>