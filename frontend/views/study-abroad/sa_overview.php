<?php

use common\helpers\CollegeHelper;
use common\models\SaCountry;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Url;

$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->title = !empty($subpageContent->title) ? $subpageContent->title : 'Study in' . $country->name . '- Find Colleges and Universities';
$this->context->description = !empty($subpageContent->meta_description) ? $subpageContent->meta_description : 'Interested to Study in' . $country->name . '? Know more details on Colleges & Universities, Cost of Study, Cost of Living, Student Visa Requirements in' . $country->name . 'and more.';
$this->context->ogImage = '';

// schema
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}

// page specific assets
$this->params['entity'] = SaCountry::ENTITY;
?>

<head>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="//fonts.googleapis.com/css?family=Open+Sans" />
    <link rel="stylesheet" href="/yas/css/version2/abroad_detail.css">
    <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.3/themes/smoothness/jquery-ui.css" />
</head>

<?= $this->render('partials/top_college_detail_card', [
    'college' => $college,
    'country' => $country,
    'page' => '',
    'user' => $user
]); ?>

<?= $this->render('partials/subpage_menu', [
    'college' => $college,
    'menus' => $menus ?? [],
    'page' => $page,
    'countrySlug' => $countrySlug,
]); ?>

<?php if ($isMobile): ?>
    <?= $this->render('partials/subpage_sidemenu', [
        'sideMenu' => $sideMenu ?? [],
        'faq_details' => $faqs ?? [],
        'college' => $college,
        'fees' => $fees ?? [],
        'degreeStreamArray' => $degreeStreamArray ?? [],
        'contactDetails' => $contactDetails ?? [],

    ]); ?>
<?php endif; ?>

<div class="sa_master_svg"></div>
<div class="container-fluid no-pading">
    <div class="col-9" style="padding-left:0px;">

        <?php if (!$isMobile): ?>
            <?= $this->render('partials/subpage_sidemenu', [
                'sideMenu' => $sideMenu ?? [],
                'faq_details' => $faqs ?? [],
                'degreeStreamArray' => $degreeStreamArray,
                'college' => $college,
                'fees' => $fees ?? [],
                'degreeStreamArray' => $degreeStreamArray ?? [],
                'contactDetails' => $contactDetails ?? [],
            ]); ?>
        <?php endif; ?>

        <div class="col-md-10 midlepart">
            <div style="text-align: center; margin-top: 10px;">
                <?php if (!$isMobile) {
                    echo Ad::unit('GMU_STUDY_ABROAD_DETAIL_PAGE_WEB_ATF_728x90', '[728,90]');
                } else {
                    echo Ad::unit('GMU_STUDY_ABROAD_DETAIL_PAGE_WAP_ATF_300x50', '[300,50]');
                } ?>
            </div>

            <?= $this->render('partials/subpage_sidemenu_content', [
                'sideMenu' => $sideMenu ?? [],
                'subpageContent' => $subpageContent ?? [],
                'college' => $college ?? [],
                'fees' => $fees,
                'degreeStreamArray' => $degreeStreamArray,
                'headCard' => $headCard,
                'country' => $country
            ]); ?>

            <!-- FAQ START TO DO-->

            <?php if (!empty($faqs)): ?>
                <div class="info-card content-data faqsection carousel_section">
                    <?= $this->render('partials/_faq', [
                        'faqs' => $faqs
                    ]); ?>
                </div>
            <?php endif; ?>
            <!--FAQ ENDS -->

            <div class="card_white_background">
                <h2 class="h4_title_hrader">Contact Information</h2>
                <section class="row" id="contact">
                    <div style="margin-top:20px;" class="overview_text">
                        <?= ((isset($sideMenu['contant']->content) && !empty(isset($sideMenu['contant']->content))) ? stripslashes(html_entity_decode($sideMenu['contant']->content)) : ''); ?>
                    </div>
                    <div class="contact-info-container-loc">
                        <iframe class="location_iframe"
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d243.04417310550355!2d77.60991040393081!3d12.926558581595733!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bae14526d1dedbf%3A0x292bed76a7c8888e!2sGetMyUni!5e0!3m2!1sen!2sin!4v1571122288046!5m2!1sen!2sin"
                            width="100%" height="450" frameborder="0" style="border:0;" allowfullscreen="">
                        </iframe>
                    </div>

                    <div class="contact-inform">
                        <?php foreach ($contactDetails as $detail): ?>
                            <?php if (!empty($detail['value'])): ?>
                                <div class="contact-circle">
                                    <div class="contact-info-container-l">
                                        <div class="contatc-icon-circle">
                                            <svg style="display: inline-block; margin-right: 10px; width: 50px; height: 50px;">
                                                <use xlink:href="<?= $detail['icon']; ?>" width="40" height="40" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="contact-info-container-r">
                                        <?php if (!empty($detail['isLink'])) { ?>
                                            <a href="<?= $detail['value']; ?>" rel="nofollow" class="contact-info-head-one"
                                                target="_blank">
                                                <?= strlen($detail['value']) > 18
                                                    ? substr($detail['value'], 0, 18) . '...'
                                                    : $detail['value']; ?>
                                            </a>
                                        <?php } elseif (!empty($detail['isEmail'])) { ?>
                                            <a href="https://mail.google.com/mail/?view=cm&fs=1&tf=1&to=<?= trim($detail['value']); ?>" rel="nofollow" class="contact-info-head-one"
                                                target="_blank">
                                                <?= strlen($detail['value']) > 18
                                                    ? substr($detail['value'], 0, 18) . '...'
                                                    : $detail['value']; ?>
                                            </a>
                                        <?php } else { ?>
                                            <p class="contact-info-head-one"><?= $detail['value']; ?></p>
                                        <?php } ?>
                                        <p class="contact-info"><?= $detail['label']; ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </section>
            </div>
        </div>
        <?php //echo Ads::get_slot('BTF')
        ?>
    </div>

    <div class="col-3" id="desktop-top-recommand">
        <div id="right-fix" class="scroll-to-fixed-right" style="z-index: 1000;">
            <div class="right-lead-card hidden-xs">
                <svg>
                    <use xlink:href="#CP_x5F_28" transform="rotate(180)" x="-75" y="-75"></use>
                </svg>
                <div>Interested to study</div>
                <div>in <?= $country->name ?>?</div>
                <!-- <button class="loadLeadModelNew" data-click-source="desktop_right_expert">Talk To Experts</button> -->
                <?= frontend\helpers\Html::leadButton(
                    'Talk To Experts',
                    [
                        'entity' => SaCountry::ENTITY_COUNTRY_COLLEGE,
                        'entityId' => $college->id ?? null,
                        'ctaLocation' => 'study_abroad_country_desktop_2',
                        'ctaText' => 'Talk To Experts',
                        'leadformtitle' => 'SUBSCRIBE NOW TO GET UPDATES'
                    ],
                    ['class' => 'loadLeadModelNew'],
                    'sa-js-open-lead-form'
                ) ?>
            </div>

            <?= $this->render('partials/_top_college_recommendation', [
                'topCollegeRecommendations' => $topCollegeRecommendations,
                'countrySlug' => $countrySlug,
            ]); ?>
        </div>
    </div>
</div>

<!-- Mobile view top recommand  -->
<?php if ($isMobile): ?>
    <?= $this->render('partials/_top_college_recommendation', [
        'topCollegeRecommendations' => $topCollegeRecommendations,
        'countrySlug' => $countrySlug,
    ]); ?>
<?php endif; ?>
<!-- End of recommand -->

<div class="bottom_nav mobile_view">
    <?= $this->render('partials/_lead_button', [
        'ctaText' => 'Talk to Experts',
        'ctaPosition' => 'bottom_mobile_cta1',
        'formTitle' => 'Thank you for your interest',
        'subHeadingText' => 'Please leave your information to get the best suggested colleges and free counseling.',
        'className' => 'col-xs-6 loadLeadModelNew',
        'entity' => SaCountry::ENTITY_COUNTRY_COLLEGE,
        'country' => $country,
        'college' => $college
    ]); ?>
    <?= $this->render('partials/_lead_button', [
        'ctaText' => 'Apply Now',
        'ctaPosition' => 'bottom_mobile_cta2',
        'formTitle' => 'Thank you for your interest',
        'subHeadingText' => 'Please leave your information to get the best suggested colleges and free counseling.',
        'className' => 'col-xs-6 loadLeadModelNew',
        'country' => $country,
        'entity' => SaCountry::ENTITY_COUNTRY_COLLEGE,
        'college' => $college
    ]); ?>
    <!-- <div class="col-xs-6 loadLeadModelNew" data-click-source="mobile_bottom_expert">Talk to Experts</div>
    <div class="col-xs-6 loadLeadModelNew" data-click-source="mobile_bottom_apply">Apply Now</div> -->
</div>

<div class="gmu-ad container">
    <?php if (!$isMobile) {
        echo Freestartads::unit('getmyuni-com_bottom', '__728x90');
    } else {
        echo Freestartads::unit('getmyuni-com_bottom', '__300x250');
    } ?>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
<div id="foot">

</div>