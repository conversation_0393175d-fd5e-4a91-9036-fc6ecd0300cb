<?php

use common\helpers\DataHelper;
use common\models\CustomLandingPage;
use yii\helpers\BaseStringHelper;

?>
<section class="college-usps-section section-space">
    <div class="college-usps-bgoverlay"></div>
    <div class="container">
        <h2>College USPs
            <span><?= !empty($collegeUSPsSubText['fc_subtitle']) ? $collegeUSPsSubText['fc_subtitle'] : 'Unlock your potential with ' . $collegeName . ' industry-focused programs, expert faculty, and a transformative learning experience' ?></span>
        </h2>
        <div class="college-usps-inner">

            <?php
            $imagePaths = DataHelper::$clpUspImages;
            $totalImages = count($imagePaths['color']);

            foreach ($collegeUsp as $index => $usp):
                $imageIndex = $index % $totalImages;
                $colorImage = $imagePaths['color'][$imageIndex];
                $whiteImage = $imagePaths['white'][$imageIndex];
                ?>
                <div class="usps-card">
                    <div class="usps-card-body"><?= BaseStringHelper::truncate($usp->usp_title, 90) ?> <span><?= !empty($usp->usp_sub_title) ? BaseStringHelper::truncate($usp->usp_sub_title, 130) : '' ?></span></div>
                   <div class="cirlcradius"></div>
                   <?php /*
                    <?php if ($template == CustomLandingPage::TEMPLATE_COLLEGE_BASED_THREE): ?>
                        <div class="usps-icon">
                            <div class="whiteups">
                                <img src="<?= $whiteImage ?>" class="uspwhite" alt="usps">
                            </div>
                            <div class="colorups">
                                <img src="<?= $colorImage ?>" class="uspColor" alt="usps">
                            </div>
                        </div>
                        <div class="usps-card-body"><?= BaseStringHelper::truncate($usp->usp_title, 90) ?> <span><?= !empty($usp->usp_sub_title) ? BaseStringHelper::truncate($usp->usp_sub_title, 130) : '' ?></span></div>
                        <div class="cirlcradius"></div>
                    <?php else: ?>
                        <div class="usps-card-body"><?= BaseStringHelper::truncate($usp->usp_title, 90) ?> <span><?= !empty($usp->usp_sub_title) ? BaseStringHelper::truncate($usp->usp_sub_title, 130) : '' ?></span></div>
                        <div class="usps-icon">
                            <img src="<?= $colorImage ?>" class="uspColor" width="91" height="91" alt="usps">
                            <img src="<?= $whiteImage ?>" class="uspwhite" width="91" height="91" alt="usps">
                        </div>
                    <?php endif;
                    */ ?>
                </div>
            <?php endforeach; ?>
        </div>
        <div class="know-more-btn"><button class="button-style open-modal"><?= !empty($collegeUSPsSubText['fc_cta']) ? $collegeUSPsSubText['fc_cta'] : 'Know More' ?></button></div>
    </div>
</section>