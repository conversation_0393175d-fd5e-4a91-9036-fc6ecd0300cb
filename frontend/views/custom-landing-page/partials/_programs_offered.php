<?php

use common\helpers\DataHelper;
use common\models\CustomLandingPage;

$classNameOne = $template == (CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ) ? 'our-campus-section section-image' : 'programs-offered';
$divContainer = $template == (CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ) ? '<div class="campus-overlay"></div>' : '';
$classNameTwo = $template == (CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ) ? 'our-campus-slider' : 'programs-offered-slider';
$classNameThree = $template == (CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ) ? 'campus-card' : 'programs-card';
$classNameFour = $template == (CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ) ? 'campus-img' : 'program-img';
$classNameFive = $template == (CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ) ? 'campus-card-text' : 'program-card-text';

$templatesGeneric = [
    CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_1,
    CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_3
];

?>
<section class="<?= $classNameOne ?> section-space">
    <?= $divContainer ?>
    <div class="container">
        <h2>
            Programs Offered
            <span><?= $programsOfferedSubText['fc_subtitle'] ?? 'Advance your career by choosing the course that interest you the most' ?></span>
        </h2>
        <div class="<?= $classNameTwo ?>">
            <?php
            $imagePaths = DataHelper::$clpProgramImages;
            $totalImages = count($imagePaths);

            foreach ($programs as $index => $program):
                // When we reach the end of the image array, start from the beginning
                $imageIndex = $index % $totalImages;
                $image = $imagePaths[$imageIndex];
                ?>
                <div class="<?= $classNameThree ?>">
                    <div class="fit-content-height">
                    <div class="<?= $classNameFour ?>"><img src="<?= $image ?>"></div>
                    <div class="<?= $classNameFive ?>"><?= $program['name'] ?></div>
            </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php if (in_array($template, $templatesGeneric)): ?>
            <div class="fee-detail-button"><button class="button-style open-modal"><?= $programsOfferedSubText['fc_cta'] ?? 'Know Fee Details' ?></button></div>
        <?php endif; ?>
    </div>
</section>