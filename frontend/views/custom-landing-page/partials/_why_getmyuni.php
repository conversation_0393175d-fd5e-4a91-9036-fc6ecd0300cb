<?php

use common\helpers\DataHelper;
use common\models\CustomLandingPage;

$classNameOne = $template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ? 'streams-offered' : 'why-getmyuni';
$classNameTwo = $template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ? 'streams-offered-inner' : 'why-getmyuni-inner';
?>
<section class="<?= $classNameOne ?> section-space">
    <div class="container">
        <h2 class="headingSpace">Why GetMyUni?</h2>
        <div class="<?= $classNameTwo ?>">
            <?php $ctaText = 'Explore Now'; ?>
            <?php if (isset(DataHelper::$whyGetmyuniImages[$template])): ?>
                <?php foreach (DataHelper::$whyGetmyuniImages[$template] as $key => $item):
                    $text = $whyGetmyuni[$key]['fc_subtitle'] ?? $item['text'];
                    if (!empty($whyGetmyuni[$key]['fc_cta'])) {
                        $ctaText = $whyGetmyuni[$key]['fc_cta'];
                    }
                    ?>
                    <div class="detail-block">
                        <?php if ($template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_3): ?>
                            <div class="text-block"><?= $text ?></div>
                            <div class="img-block">
                                <img src="<?= $item['image'] ?>" width=103 height=103 alt="explore">
                            </div>
                        <?php else: ?>
                            <div class="img-block">
                                <img src="<?= $item['image'] ?>" <?= in_array($template, [CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_1]) ? 'width="103" height="103"' : '' ?> alt="explore">
                            </div>
                            <div class="text-block"><?= $text ?></div>
                        <?php endif; ?>
                    </div>
                  
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <div class="explore-now-btn"><a href="" class="button-style open-modal"><?= $ctaText ?></a></div>
    </div>
</section>