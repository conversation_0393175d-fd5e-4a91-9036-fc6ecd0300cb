<?php

use common\models\CustomLandingPage;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
$wordCount = str_word_count(strip_tags($info->content));
$countLimit = $isMobile ? 90 : 150;
$showReadMore = $wordCount > $countLimit;

?>
<?php if ($template == CustomLandingPage::TEMPLATE_BRAND_SPECIFIC_LANDING_PAGE_VARIATION_2): ?>
    <section class="about-insti-section">
        <div class="container about-insti-inner">
            <div class="about-insti-left" style="<?= (isset($info->banner_image) && !empty($info->banner_image) && ($info->banner_image == 1)) ? 'width:55%;' : 'width:100%; height:200px;' ?>">
                <h2>About <?= $collegeName ?></h2>
                <div class="show-more-content" data-height="218">
                    <?= $info->content ?>
                </div>
                <?php if ($showReadMore): ?>
                    <a href="javascript:void(0);" class="toggle-btn"> Read More <i class="arrow up"></i></a>
                <?php endif; ?>
            </div>
            <?php if (isset($info->banner_image) && !empty($info->banner_image) && ($info->banner_image == 1)): ?>
                <div class="about-insti-right">
                    <img src="<?= Url::getCollegeBannerImage($collegeBanner) ?? Url::toDefaultCollegeBanner() ?>" alt="image" width="auto" height="auto">
                </div>
            <?php endif; ?>
        </div>
        <?= '<div class="know-more-btn" style="margin-top: 30px;z-index=1;position:relative;"><a href="' . Url::toCollege($collegeSlug) . '" class="button-style open-modal" target="_blank">' . (!empty($aboutCollegeSubText['fc_cta']) ? $aboutCollegeSubText['fc_cta'] : 'Apply Now') . '</a></div>'; ?>
    </section>
<?php endif; ?>

<?php if ($template == CustomLandingPage::TEMPLATE_BRAND_SPECIFIC_LANDING_PAGE_VARIATION_3): ?>
    <section class="about-insti-section section-space">
        <div class="container about-insti-inner">
            <div class="about-insti-left" style="<?= (isset($info->banner_image) && !empty($info->banner_image) && ($info->banner_image == 1)) ? 'width:55%;' : 'width:100%; height:200px;' ?>">
                <h2>About <?= $collegeName ?></h2>
                <div class="show-more-content" data-height="285">
                    <?= $info->content ?>
                </div>
                <?php if ($showReadMore): ?>
                    <a href="javascript:void(0);" class="toggle-btn">Read More <i class="arrow up"></i></a>
                <?php endif; ?>
            </div>
            <?php if (isset($info->banner_image) && !empty($info->banner_image) && ($info->banner_image == 1)): ?>
                <div class="about-insti-right">
                    <img src="<?= Url::getCollegeBannerImage($collegeBanner) ?? Url::toDefaultCollegeBanner() ?>" alt="image" width="auto" height="auto">
                </div>
            <?php endif; ?>
        </div>
        </div>
        <?= '<div class="know-more-btn" style="z-index=1;position:relative;"><a href="' . Url::toCollege($collegeSlug) . '" class="button-style open-modal" target="_blank">' . (!empty($aboutCollegeSubText['fc_cta']) ? $aboutCollegeSubText['fc_cta'] : 'Apply Now') . '</a></div>'; ?>
    </section>
<?php endif; ?>

<?php if ($template == CustomLandingPage::TEMPLATE_BRAND_SPECIFIC_LANDING_PAGE): ?>
    <section class="about-insti-section section-space">
        <div class="container about-insti-inner">

            <h2>About <?= $collegeName ?></h2>
            <div class="about-insti-left" style="<?= (isset($info->banner_image) && !empty($info->banner_image) && ($info->banner_image == 1)) ? 'width:55%;' : 'width:100%; height:200px;' ?>">
                <div class="show-more-content" data-height="380">
                    <?= $info->content ?>
                </div>
                <?php if ($showReadMore): ?>
                    <a href="javascript:void(0);" class="toggle-btn">Read More <i class="arrow up"></i> </a>
                <?php endif; ?>
            </div>
            <?php if (isset($info->banner_image) && !empty($info->banner_image) && ($info->banner_image == 1)): ?>
                <div class="about-insti-right">
                    <img src="<?= Url::getCollegeBannerImage($collegeBanner) ?? Url::toDefaultCollegeBanner() ?>" alt="image" width="auto" height="auto">
                </div>
            <?php endif; ?>
        </div>
        <?= '<div class="know-more-btn" style="z-index=1;position:relative;"><a href="' . Url::toCollege($collegeSlug) . '" class="button-style open-modal" target="_blank">' . (!empty($aboutCollegeSubText['fc_cta']) ? $aboutCollegeSubText['fc_cta'] : 'Apply Now') . '</a></div>'; ?>
    </section>
<?php endif; ?>