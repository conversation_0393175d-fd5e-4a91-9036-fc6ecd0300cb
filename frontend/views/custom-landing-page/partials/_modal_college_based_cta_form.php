<form id="modalFormClp">
    <div class="form-content">
        <div class="form-field-block">
            <input type="text" id="name1" name="name" placeholder="Name*" autocomplete="off" class="required-field field-style">
            <span class="form-error" id="nameError1"></span>
        </div>
        <div class="form-field-block">
            <input type="email" id="email1" name="email" placeholder="Email*" autocomplete="off" class="required-field field-style">
            <span class="form-error" id="email1Error"></span>
        </div>
        <div class="form-field-block">
            <input type="text" id="phone1" name="phone" placeholder="Phone number*" maxlength="10" autocomplete="off" class="required-field field-style">
            <span class="form-error" id="phoneError1"></span>
        </div>
        <?= $this->render('_college_based_select_form_fields', [
            'clp' => $clp,
            'streams' => $streams,
            'city' => $city,
            'states' => $states,
            'courses' => $courses,
            'programsList' => $programsList,
            'level' => $level,
            'college' => $college,
            'type' => 'modal',
            'enabled_fields' => $enabled_fields ?? [],
        ]) ?>
        <div class="form-field-block">
            <button type="submit" class="modal-button" id="modalScreenSubmit" disabled>Apply Now</button>
        </div>
    </div>
    <input type="hidden" id="template_id" name="template_id" value="<?= $clp->template_id ?>">
    <input type="hidden" name="domain" value="<?= $clp->domain ?>">
    <input type="hidden" name="slug" value="<?= $clp->slug ?>">
    <input type="hidden" name="form_cta_text" value="<?= $clp->form_cta_text ?? 'Apply Now' ?>">
    <input type="hidden" id="college_id" name="college_id" value="<?= $college->id ?? '' ?>">
    <input type="hidden" id="college_slug" value="<?= $college->slug ?? '' ?>">
    <input type="hidden" id="redirection_url" value="<?= $clp->redirection_url ?? '' ?>">
    <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
    <input type="hidden" name="utm_source" value="<?= !empty($_GET['utm_source']) ? $_GET['utm_source'] : '' ?>">
    <input type="hidden" name="utm_medium" value="<?= !empty($_GET['utm_medium']) ? $_GET['utm_medium'] : '' ?>">
    <input type="hidden" name="utm_campaign" value="<?= !empty($_GET['utm_campaign']) ? $_GET['utm_campaign'] : '' ?>">
    <input type="hidden" name="utm_id" value="<?= !empty($_GET['utm_id']) ? $_GET['utm_id'] : '' ?>">
    <input type="hidden" name="utm_term" value="<?= !empty($_GET['utm_term']) ? $_GET['utm_term'] : '' ?>">
</form>