 <?php
    use common\helpers\DataHelper;

    ?>
 <?php if (!empty($enabled_fields) && !empty($enabled_fields['course_id']) && ($enabled_fields['course_id'] == 1)): ?>
        <?php if (!empty($courses) && count($courses) > 1): ?>
         <div class="form-field-block">
             <select name="course_id" id="<?= DataHelper::$modalSelectFormId[$type]['course'] ?? 'user_course' ?>" class="field-style select-arrow clpCourse required-field">
                 <option></option>
                 <?php foreach ($courses as $course): ?>
                     <option value="<?= $course['id'] ?>"><?= $course['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="courseError2"></span>
             <input type="hidden" id="course_ids" value="<?= implode(',', array_column($courses, 'id')) ?>">
         </div>
        <?php elseif (!empty($courses) && count($courses) == 1): ?>
         <input type="hidden" name="course_id" value="<?= $courses[0]['id'] ?? '' ?>">
        <?php else: ?>
         <div class="form-field-block">
             <select name="course_id" id="<?= DataHelper::$modalSelectFormId[$type]['course'] ?? 'user_course' ?>" class="field-style select-arrow clpCourse required-field">
                 <option></option>
                 <?php foreach ($courses as $course): ?>
                     <option value="<?= $course['id'] ?>"><?= $course['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="courseError2"></span>
             <input type="hidden" id="course_ids" value="<?= implode(',', array_column($courses, 'id')) ?>">
         </div>
        <?php endif; ?>
 <?php endif; ?>

 <?php if (!empty($enabled_fields) && !empty($enabled_fields['program_id']) && ($enabled_fields['program_id'] == 1)): ?>
        <?php if (!empty($programsList) && count($programsList) > 1): ?>
         <div class="form-field-block">
             <select name="program_id" id="<?= DataHelper::$modalSelectFormId[$type]['program'] ?? 'user_program' ?>" class="field-style select-arrow clpProgram required-field">
                 <option></option>
                 <?php foreach ($programsList as $program): ?>
                     <option value="<?= $program['id'] ?>"><?= $program['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="programError2"></span>
             <input type="hidden" id="program_ids" value="<?= implode(',', array_column($programsList, 'id')) ?>">
         </div>
        <?php elseif (!empty($programsList) && count($programsList) == 1): ?>
         <input type="hidden" name="program_id" value="<?= $programsList[0]['id'] ?? '' ?>">
        <?php else: ?>
         <div class="form-field-block">
             <select name="program_id" id="<?= DataHelper::$modalSelectFormId[$type]['program'] ?? 'user_program' ?>" class="field-style select-arrow clpProgram required-field">
                 <option></option>
                 <?php foreach ($programsList as $program): ?>
                     <option value="<?= $program['id'] ?>"><?= $program['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="programError2"></span>
             <input type="hidden" id="program_ids" value="<?= implode(',', array_column($programsList, 'id')) ?>">
         </div>
        <?php endif; ?>
 <?php endif; ?>

 <?php if (!empty($enabled_fields) && !empty($enabled_fields['stream_id']) && ($enabled_fields['stream_id'] == 1)): ?>
        <?php if (!empty($streams) && count($streams) > 1): ?>
         <div class="form-field-block <?= DataHelper::$modalSelectFormId[$type]['clpStreamConatiner'] ?? 'clpStreamConatiner' ?>">
             <select name="stream_id" id="<?= DataHelper::$modalSelectFormId[$type]['stream'] ?? 'user_stream' ?>" class="field-style select-arrow clpStream required-field">
                 <option></option>
                 <?php foreach ($streams as $stream): ?>
                     <option value="<?= $stream['id'] ?>"><?= $stream['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="streamError2"></span>
             <input type="hidden" id="stream_ids" value="<?= implode(',', array_column($streams, 'id')) ?>">
         </div>
        <?php else: ?>
         <input type="hidden" name="stream_id" value="<?= $streams[0]['id'] ?? '' ?>">
        <?php endif; ?>
 <?php endif; ?>

 <?php if (!empty($enabled_fields) && !empty($enabled_fields['level_id']) && ($enabled_fields['level_id'] == 1)): ?>
        <?php if (!empty($level) && count($level) > 1): ?>
         <div class="form-field-block">
             <select name="degree_id" id="<?= DataHelper::$modalSelectFormId[$type]['degree'] ?? 'user_degree' ?>" class="field-style select-arrow clpDegree required-field">
                 <option></option>
                 <?php foreach ($level as $degree): ?>
                     <option value="<?= $degree['id'] ?>"><?= $degree['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="degreeError2"></span>
             <input type="hidden" id="degree_ids" value="<?= implode(',', array_column($level, 'id')) ?>">
         </div>
        <?php elseif (!empty($level) && count($level) == 1): ?>
         <input type="hidden" name="degree_id" value="<?= $level[0]['id'] ?? '' ?>">
        <?php else: ?>
         <div class="form-field-block">
             <select name="degree_id" id="<?= DataHelper::$modalSelectFormId[$type]['degree'] ?? 'user_degree' ?>" class="field-style select-arrow clpDegree required-field" disabled>
                 <option></option>
                 <?php foreach ($level as $degree): ?>
                     <option value="<?= $degree['id'] ?>"><?= $degree['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="degreeError2"></span>
             <input type="hidden" id="degree_ids" value="<?= implode(',', array_column($level, 'id')) ?>">
         </div>
        <?php endif; ?>
 <?php endif; ?>

 <?php if (!empty($enabled_fields) && !empty($enabled_fields['city_id']) && ($enabled_fields['city_id'] == 1)): ?>
        <?php if (!empty($city) && count($city) > 1): ?>
         <div class="form-field-block">
             <select name="city_id" id="<?= DataHelper::$modalSelectFormId[$type]['city'] ?? 'user_city' ?>" class="field-style select-arrow clpCity required-field">
                 <option></option>
                 <?php foreach ($city as $value): ?>
                     <option value="<?= $value['id'] ?>"><?= $value['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="cityError2"></span>
             <input type="hidden" id="city_ids" value="<?= implode(',', array_column($city, 'id')) ?>">
         </div>
        <?php elseif (!empty($city) && count($city) == 1): ?>
         <input type="hidden" name="city_id" value="<?= $city[0]['id'] ?? '' ?>">
        <?php else: ?>
         <input type="hidden" name="city_id" value="<?= $college->city_id ?? '' ?>">
        <?php endif; ?>
 <?php endif; ?>

 <?php if (!empty($enabled_fields) && !empty($enabled_fields['state_id']) && ($enabled_fields['state_id'] == 1)): ?>
        <?php if (!empty($states) && count($states) > 1): ?>
         <div class="form-field-block">
             <select name="state_id" id="<?= DataHelper::$modalSelectFormId[$type]['state'] ?? 'user_state' ?>" class="field-style select-arrow clpState required-field">
                 <option></option>
                 <?php foreach ($states as $state): ?>
                     <option value="<?= $state['id'] ?>"><?= $state['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="stateError2"></span>
             <input type="hidden" id="state_ids" value="<?= implode(',', array_column($states, 'id')) ?>">
         </div>
        <?php elseif (!empty($states) && count($states) == 1): ?>
         <input type="hidden" name="state_id" value="<?= $states[0]['id'] ?? '' ?>">
        <?php else: ?>
         <input type="hidden" name="state_id" value="<?= $college->city->state_id ?? '' ?>">
        <?php endif; ?>
 <?php endif; ?>

 <?php if (!empty($enabled_fields) && !empty($enabled_fields['campus_id']) && ($enabled_fields['campus_id'] == 1)): ?>
        <?php if (!empty($campus) && count($campus) > 1): ?>
         <div class="form-field-block">
             <select name="campus_id" id="<?= DataHelper::$modalSelectFormId[$type]['campus'] ?? 'user_campus' ?>" class="field-style select-arrow clpCampus required-field">
                 <option></option>
                 <?php foreach ($campus as $value): ?>
                     <option value="<?= $value['id'] ?>"><?= $value['name'] ?></option>
                 <?php endforeach; ?>
             </select>
             <span class="form-error" id="campusError2"></span>
             <input type="hidden" id="campus_ids" value="<?= implode(',', array_column($campus, 'id')) ?>">
         </div>
        <?php elseif (!empty($campus) && count($campus) == 1): ?>
         <input type="hidden" name="campus_id" value="<?= $campus[0]['id'] ?? '' ?>">
        <?php else: ?>
         <input type="hidden" name="campus_id" value="<?= $clp->campus_id ?? '' ?>">
        <?php endif; ?>
 <?php endif; ?>