<?php

use common\helpers\DataHelper;
use common\models\CustomLandingPage;

$isMobile = \Yii::$app->devicedetect->isMobile();

$style = [
    4 => 'width="400" height="300"',
    6 => 'width="412" height="190"',
];
$count = $isMobile ? 1 : 3;
?>
<section class="streams-offered section-space">
    <div class="container">
        <h2 class="headingSpace">
            Streams Offered
            <span><?= !empty($streamsOfferedSubText['fc_subtitle']) ? $streamsOfferedSubText['fc_subtitle'] : 'Advance your career by choosing the course that interest you the most.' ?></span>
        </h2>
      
            <div class="streams-offered-inner" id="textContent">
                <?php
                $imagePaths = DataHelper::$clpStreamImagesBrandSpecific;
                foreach ($streamsWidget as $stream):
                    $image = !empty($imagePaths[$stream['name']]) ? $imagePaths[$stream['name']] : '';
                    ?>

                    <div class="detail-block">
                        <?php if (in_array($template, [CustomLandingPage::TEMPLATE_BRAND_SPECIFIC_LANDING_PAGE_VARIATION_2, CustomLandingPage::TEMPLATE_BRAND_SPECIFIC_LANDING_PAGE])): ?>
                            <div class="img-overlay"></div>
                        <?php endif; ?>
                        <div class="img-block"><img src="<?= $image ?>" <?= isset($style[$template]) ? $style[$template] : '' ?> alt="explore"> </div>
                        <div class="text-block"><?= $stream['name'] ?></div>
                    </div>

                <?php endforeach; ?>

            </div>
            <div class="text-center">
                <?php if (count($streamsWidget) > $count): ?>
                    <!-- <button class="view-toggle button-style" id="toggleBtn">View More <i class="arrow up"></i></button> -->
                <?php endif; ?>
                <a href="" class="button-style open-modal"><?= !empty($streamsOfferedSubText['fc_cta']) ? $streamsOfferedSubText['fc_cta'] : 'Explore Now' ?></a>
            </div>
     
    </div>
</section>