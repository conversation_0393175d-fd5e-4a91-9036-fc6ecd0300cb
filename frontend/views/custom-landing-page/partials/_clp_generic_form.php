<?php

use common\models\CustomLandingPage;
use frontend\helpers\Url;

$templatesGeneric = [
    CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4,
    CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_3
];
?>
<div class="banner-right-content <?= in_array($template, $templatesGeneric) ? 'modal-body' : '' ?>">
    <div class="<?= in_array($template, $templatesGeneric) ? 'modal-head' : 'form-head ' ?>"><?= !empty($clp->form_title) ? $clp->form_title : 'Admissions Open for 2025' ?></div>
    <span class="form-subheading-generic"><?= !empty($clp->form_description) ? $clp->form_description : 'Hurry, Limited Seats Available' ?></span>
    <form id="genericfirstScreen">
        <div class="<?= in_array($template, $templatesGeneric) ? 'form-content' : 'form-body-block' ?>">
            <div class="<?= in_array($template, $templatesGeneric) ? 'form-field-block' : 'banner-form-inner' ?>">
                <input type="text" placeholder="Name*" id="name" name="name" autocomplete="off" class="required-field field-style">
                <span class="form-error" id="nameError"></span>
            </div>
            <div class="<?= in_array($template, $templatesGeneric) ? 'form-field-block' : 'banner-form-inner' ?>">
                <input type="email" placeholder="Email*" id="email" name="email" autocomplete="off" class="required-field field-style">
                <span class="form-error" id="emailError"></span>
            </div>
            <div class="banner-form-inner">
                <input type="text" placeholder="Phone number*" id="phone" maxlength="10" name="phone" autocomplete="off" class="required-field field-style">
                <span class="form-error" id="phoneError"></span>
            </div>

            <?= $this->render('_generic_select_form_fields', [
                'clp' => $clp,
                'stream' => $stream,
                'city' => $city,
                'states' => $states,
                'courses' => $courses,
                'programsList' => $programsList,
                'level' => $level,
                'type' => 'banner',
                'campus' => $campus ?? [],
                'enabled_fields' => $enabled_fields ?? [],
            ]) ?>

            <input type="hidden" name="stream_id" value="<?= count($stream) > 1 ? '' : $stream[0]['id'] ?? null ?>">
            <input type="hidden" name="domain" value="<?= $clp->domain ?>">
            <input type="hidden" name="slug" value="<?= $clp->slug ?>">
            <input type="hidden" name="form_cta_text" value="<?= $clp->form_cta_text ?? 'Apply Now' ?>">
            <input type="hidden" id="template_id" name="template_id" value="<?= $clp->template_id ?>">
            <input type="hidden" id="redirection_url" value="<?= empty($clp->redirection_url) ? Url::toGetmyuni() : $clp->redirection_url ?>">
            <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
            <input type="hidden" name="utm_source" value="<?= !empty($_GET['utm_source']) ? $_GET['utm_source'] : '' ?>">
            <input type="hidden" name="utm_medium" value="<?= !empty($_GET['utm_medium']) ? $_GET['utm_medium'] : '' ?>">
            <input type="hidden" name="utm_campaign" value="<?= !empty($_GET['utm_campaign']) ? $_GET['utm_campaign'] : '' ?>">
            <input type="hidden" name="utm_id" value="<?= !empty($_GET['utm_id']) ? $_GET['utm_id'] : '' ?>">
            <input type="hidden" name="utm_term" value="<?= !empty($_GET['utm_term']) ? $_GET['utm_term'] : '' ?>">

            <button type="submit" class="<?= $template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ? 'modal-button' : 'submit-btn'; ?>" id="genericScreenSubmit" disabled><?= !empty($clp->form_cta_text) ? $clp->form_cta_text : 'Apply Now' ?></button>
        </div>
    </form>
</div>