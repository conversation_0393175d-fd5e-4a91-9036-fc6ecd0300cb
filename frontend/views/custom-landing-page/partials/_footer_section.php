<?php

use common\models\CustomLandingPage;

$classNameOne = $template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ? 'about-insti-section' : 'submit-application-section';
$classNameTwo = $template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ? 'about-insti-inner' : 'submit-application-inner';
$classNameThree = $template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ? 'about-insti-left' : 'submit-app-left';
$classNameFour = $template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ? 'about-insti-right' : 'submit-app-btn';
$width = $template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ? 'auto' : '20';
$height = $template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4 ? 'auto' : '22';
$text = '"Unlock your design success with personalized career guidance - <br><span>Take the first step now!</span>"';
$textOne = '"Unlock your career potential with expert guidance – Take the first step today!"';
// $textOne = '"Unlock your design success with personalized career guidance - Take the first step now!"';
?>
<section class="<?= $classNameOne ?> section-space">
    <div class="container <?= $classNameTwo ?>">
        <div class="<?= $classNameThree ?>">
            <?php if ($template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_4): ?>
                <p><?= $aboutSubText['fc_subtitle'] ?? $text ?></p>
            <?php elseif ($template == CustomLandingPage::TEMPLATE_GENERIC_LANDING_PAGE_VARIATION_2): ?>
                <?= $aboutSubText['fc_subtitle'] ?? $textOne ?>
            <?php else:  ?>
                <?= $aboutSubText['fc_subtitle'] ?? $text ?>
            <?php endif; ?>
        </div>
        <div class="<?= $classNameFour ?>">
            <button class="button-style open-modal"><img src="/yas/images/clp/generic/submit-btn-icon.png" width="<?= $width ?>" height="<?= $height ?>" alt="icon"> <?= $aboutSubText['fc_cta'] ?? 'Submit Your Application' ?></button>
        </div>
    </div>
</section>
<div class="stick-button">
    <p>
        <!-- <span>Hurry Limited Seats Available !</span> -->
        <button class="button-style open-modal"><i class="spriteIcon whiteDownloadIcon redDownloadIcon"></i> Download Brochure</button>
        <button class="button-blue open-modal"><i class="spriteIcon phoneIcon"></i> Call us</button>
    </p>
</div>
<!-- <footer>&copy; Copyright 2025 GMU</footer> -->