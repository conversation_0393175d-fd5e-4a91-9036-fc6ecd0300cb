<?php

use common\helpers\DataHelper;

?>
<?php if (!empty($enabled_fields) && !empty($enabled_fields['state_id']) && ($enabled_fields['state_id'] == 1)): ?>
    <?php if (!empty($states) && count($states) > 1): ?>
        <div class="banner-form-inner">
            <select class="select2HookClass clpState field-style required-field select-arrow" name="state_id" id="<?= DataHelper::$modalSelectFormId[$type]['state'] ?? 'user_state' ?>">
                <option></option>
                <?php foreach ($states as $state): ?>
                    <option value="<?= $state['id'] ?>"><?= $state['name'] ?></option>
                <?php endforeach; ?>
            </select>
            <span class="form-error" id="stateError"></span>
            <input type="hidden" id="state_ids" value="<?= implode(',', array_column($states, 'id')) ?>">
        </div>
    <?php else: ?>
        <input type="hidden" name="state_id" value="<?= $states[0]['id'] ?? '' ?>">
    <?php endif; ?>
<?php endif; ?>

<?php if (!empty($enabled_fields) && !empty($enabled_fields['city_id']) && ($enabled_fields['city_id'] == 1)): ?>
    <?php if (!empty($city) && count($city) > 1): ?>
        <div class="banner-form-inner">
            <select class="select2HookClass clpCity field-style required-field select-arrow" name="city_id" id="<?= DataHelper::$modalSelectFormId[$type]['city'] ?? 'user_city' ?>">
                <option></option>
                <?php foreach ($city as $value): ?>
                    <option value="<?= $value['id'] ?>"><?= $value['name'] ?></option>
                <?php endforeach; ?>
            </select>
            <span class="form-error" id="cityError"></span>
            <input type="hidden" id="city_ids" value="<?= implode(',', array_column($city, 'id')) ?>">
        </div>
    <?php else: ?>
        <input type="hidden" name="city_id" value="<?= $city[0]['id'] ?? '' ?>">
    <?php endif; ?>
<?php endif; ?>

<?php if (!empty($enabled_fields) && !empty($enabled_fields['course_id']) && ($enabled_fields['course_id'] == 1)): ?>
    <?php if (!empty($courses) && count($courses) > 1): ?>
        <div class="banner-form-inner">
            <select class="select2HookClass clpCourse field-style required-field select-arrow" name="course_id" id="<?= DataHelper::$modalSelectFormId[$type]['course'] ?? 'user_course' ?>">
                <option></option>
                <?php foreach ($courses as $course): ?>
                    <option value="<?= $course['id'] ?>"><?= $course['name'] ?></option>
                <?php endforeach; ?>
            </select>
            <span class="form-error" id="courseError"></span>
            <input type="hidden" id="course_ids" value="<?= implode(',', array_column($courses, 'id')) ?>">
        </div>
    <?php else: ?>
        <input type="hidden" name="course_id" value="<?= $courses[0]['id'] ?? '' ?>">
    <?php endif; ?>
<?php endif; ?>

<?php if (!empty($enabled_fields) && !empty($enabled_fields['level_id']) && ($enabled_fields['level_id'] == 1)): ?>
    <?php if (!empty($level) && count($level) > 1): ?>
        <div class="banner-form-inner">
            <select class="select2HookClass clpDegree field-style required-field select-arrow" name="degree_id" id="<?= DataHelper::$modalSelectFormId[$type]['degree'] ?? 'user_degree' ?>">
                <option></option>
                <?php foreach ($level as $degree): ?>
                    <option value="<?= $degree['id'] ?>"><?= $degree['name'] ?></option>
                <?php endforeach; ?>
            </select>
            <span class="form-error" id="degreeError"></span>
            <input type="hidden" id="degree_ids" value="<?= implode(',', array_column($level, 'id')) ?>">
        </div>
    <?php else:  ?>
        <input type="hidden" name="degree_id" value="<?= $level[0]['id'] ?? '' ?>">
    <?php endif; ?>
<?php endif; ?>

<?php if (!empty($enabled_fields) && !empty($enabled_fields['program_id']) && ($enabled_fields['program_id'] == 1)): ?>
    <?php if (!empty($programsList) && count($programsList) > 1): ?>
        <div class="banner-form-inner">
            <select class="select2HookClass clpProgram field-style required-field select-arrow" name="program_id" id="<?= DataHelper::$modalSelectFormId[$type]['program'] ?? 'user_program' ?>">
                <option></option>
                <?php foreach ($programsList as $program): ?>
                    <option value="<?= $program['id'] ?>"><?= $program['name'] ?></option>
                <?php endforeach; ?>
            </select>
            <span class="form-error" id="programError"></span>
            <input type="hidden" id="program_ids" value="<?= implode(',', array_column($programsList, 'id')) ?>">
        </div>
    <?php else: ?>
        <input type="hidden" name="program_id" value="<?= $programsList[0]['id'] ?? '' ?>">
    <?php endif; ?>
<?php endif; ?>

<?php if (!empty($enabled_fields) && !empty($enabled_fields['campus_id']) && ($enabled_fields['campus_id'] == 1)): ?>
    <?php if (!empty($campus) && count($campus) > 1): ?>
        <div class="banner-form-inner">
            <select class="select2HookClass clpCampus field-style required-field select-arrow" name="campus_id" id="<?= DataHelper::$modalSelectFormId[$type]['campus'] ?? 'user_campus' ?>">
                <option></option>
                <?php foreach ($campus as $value): ?>
                    <option value="<?= $value['id'] ?>"><?= $value['name'] ?></option>
                <?php endforeach; ?>
            </select>
            <span class="form-error" id="campusError"></span>
            <input type="hidden" id="campus_ids" value="<?= implode(',', array_column($campus, 'id')) ?>">
        </div>
    <?php else: ?>
        <input type="hidden" name="campus_id" value="<?= $campus[0]['id'] ?? '' ?>">
    <?php endif; ?>
<?php endif; ?>