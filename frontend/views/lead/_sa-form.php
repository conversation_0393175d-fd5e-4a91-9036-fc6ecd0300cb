<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\helpers\DataHelper;
use common\models\GmuSaLeads;
use common\models\SaDegree;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $model frontend\models\LeadForm */
/* @var $form ActiveForm */

$model = new GmuSaLeads();
$student = \Yii::$app->saUser->identity;

?>
<div class="pageMask"></div>
<div class="leadFormContainer">
    <div class="closeLeadForm mobileOnly">
        <img src="/yas/images/closeLeadForm.svg">
    </div>

    <div class="leadFormDiv">
        <form class="signupModalFormStudyAbroad" name="signupModalFormStudyAbroad" id="firstScreenStudyAbroad">
            <span class="spriteIcon closeLeadForm"></span>
            <div class="userInputs" id="leadFormInputs">
                <div class="saScreeOne">
                    <div class="formHeadingDiv row">
                        <div class="formImg">
                            <img src="/yas/images/default-lead-form-icon.png" alt="" id="leadform-image">
                        </div>
                        <div class="formHeading">
                            <p class="headingText">REGISTER NOW TO APPLY</p>
                            <p class="subHeadingText">Get details and latest updates</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="formField">
                                <i class="spriteIcon userIcon"></i>
                                <input type="text" id="gmusaleads-name" class="form-control" name="name" placeholder="Enter Your Name" maxlength="50" value="<?= $student->name ?? '' ?>">
                            </div>
                            <p class="error errorMsg errorMsgSaName"></p>
                        </div>
                        <div class="col-md-6">
                            <div class="formField saEmail">
                                <i class="spriteIcon mailIcon"></i>
                                <input type="text" id="gmusaleads-email" class="form-control" name="email" placeholder="Enter Your Email" value="<?= $student->email ?? '' ?>">
                                <p class="error errorMsg errorMsgSaEmail"></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mobileNumber row">
                                <div class="countryCode otherCountryCode">
                                    <div class="dialCodeDiv">
                                        <input type="tel" id="gmusaleads-country_code" class="form-control" name="country_code" value="+<?= $student->country_code ?? '' ?>">
                                    </div>
                                </div>
                                <div class="numberInput">
                                    <input type="text" id="gmusaleads-phone" class="form-control" name="phone" maxlength="10" placeholder="Enter Your Contact Number" value="<?= $student->phone ?? '' ?>">
                                </div>
                            </div>
                            <p class="error errorMsg errorMsgSaPhone"></p>
                        </div>

                        <div class="col-md-6 form-group inputCountryContainer modalInputContainer countryClass current_country">
                            <select class="inputContainerField select2HookClass country" data-user-input="country" name="current_country" id="current_country">
                                <option></option>
                            </select>
                            <span class="spriteIcon globeIcon"></span>
                        </div>

                        <div class="col-md-6 form-group inputsaCityContainer modalInputContainer saCityClass current_sa_city">
                            <select class="inputContainerField select2HookClass saCity" data-user-input="sa_city" name="current_sa_city" id="current_sa_city" disabled>
                                <option></option>
                            </select>
                            <span class="spriteIcon locationIcon"></span>
                        </div>


                        <div class="col-md-6 form-group inputstudyDestination modalInputContainer studyDestinationClass sa_study_destination">
                            <select class="inputContainerField select2HookClass studyDestination" data-user-input="sa_study_destination" name="sa_study_destination" id="sa_study_destination">
                                <option></option>
                            </select>
                            <span class="spriteIcon globeIcon"></span>
                        </div>

                        <div class="col-md-6">
                            <div class="formField">
                                <select class="inputContainerField select2HookClass planningDuration" data-user-input="planning_duration" name="planning_duration" id="gmusaleads-planning_duration">
                                    <option value="" selected disabled>When do you plan to study</option>
                                    <?php foreach (DataHelper::plannedYear() as $year): ?>
                                        <option value="<?= $year['value'] ?>"><?= $year['name'] ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <span class="spriteIcon calenderIcon"></span>
                            </div>
                        </div>


                        <div class="col-md-6 form-group intrestredDegreeSelect">
                            <div class="formField">
                                <select class="inputContainerField select2HookClass intrestredDegree" data-user-input="intrestred_degree" name="intrestred_degree" id="gmusaleads-degree">
                                    <option></option>
                                </select>
                                <span class="spriteIcon bookIcon"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row m-0 appearedExams">
                        <div class="row m-0">
                            <p>Appeared Exam:</p>
                            <div class="checkbox-group">
                                <div class="form-group field-gmusaleads-appeared_exam">
                                    <input type="hidden" name="GmuSaLeads[appeared_exam]" value="">
                                    <div id="gmusaleads-appeared_exam">
                                        <?php foreach (DataHelper::$examOptions as $value => $label) {
                                            echo '<label>';
                                            echo '<input type="checkbox" name="GmuSaLeads[appeared_exam][]" value="' . $value . '"> ' . $label;
                                            echo '</label>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="formSumbitBtn col-md-2 p-0">
                            <?= Html::submitButton('Submit', ['class' => 'btn primaryBtn submit-sa-lead-form', 'disabled' => true]) ?>
                        </div>
                    </div>

                    <input type="hidden" name="entity" id="gmusaleads-entity">
                    <input type="hidden" name="entity_id" id="gmusaleads-entity_id">
                    <input type="hidden" name="cta_text" id="gmusaleads-cta_text">
                    <input type="hidden" name="cta_location" id="gmusaleads-cta_location">
                    <input type="hidden" name="url" id="gmusaleads-url">
                    <input type="hidden" name="hidden_number" id="hidden_number" value="<?= $student->phone ?? '' ?>">
                    <input type="hidden" id="gmusaleads-durl">
                    <input type="hidden" id="gmusaleads-dynamic_redirection">
                    <input type="hidden" name="country_code_full" id="country_code_full">
                    
                </div>

                <div class="optSectionSa" style="display: none;">
                    <p class="headingText">Verify Mobile Number</p>
                    <p>OTP has been sent to your mobile number <span id="leadUserMobile"></span></p>

                    <div class="numberInputs">
                        <div>
                            <input type="text" class="nextInput" id="digit-1" name="digit[]" data-next="digit-2" maxlength="1" inputmode="numeric" pattern="[0-9]*" autocomplete="off" />
                            <input type="text" class="nextInput" id="digit-2" name="digit[]" data-next="digit-3" data-previous="digit-1" maxlength="1" inputmode="numeric" pattern="[0-9]*" />
                            <input type="text" class="nextInput" id="digit-3" name="digit[]" data-next="digit-4" data-previous="digit-2" maxlength="1" inputmode="numeric" pattern="[0-9]*" />
                            <input type="text" class="nextInput" id="digit-4" name="digit[]" data-next="digit-5" data-previous="digit-3" maxlength="1" inputmode="numeric" pattern="[0-9]*" />
                        </div>
                        <p class="text-danger" id="otpResponseText"></p>
                    </div>
                    <div class="row">
                        <button class="primaryBtn sa-verifyOtp">VERIFY OTP</button>
                        <p class="pb-0">
                            Didn’t receive the OTP?
                            <a href="javascript:void(0)" id="sa-resendOtp"> Resend it.</a>
                            <span id="resendTimer"></span>
                        </p>
                    </div>
                </div>

                <div class="engagementPanel" style="display: none;">

                </div>
                <div class="saThankYouMsg" style="display: none;">
                    <i class="spriteIcon thankYouIcon"></i>
                    <p class="saThankYouText"></p>
                </div>
                <h2 class="thankYouResponse" style="display: none;"></h2>
            </div>
        </form>
    </div>