<?php

use common\helpers\DataHelper;
use common\models\Course;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\models\LeadForm;
use yii\helpers\ArrayHelper;

$model = new LeadForm();

$isMobile = \Yii::$app->devicedetect->isMobile();
$cities = $model->city;
$cities = ArrayHelper::map($cities, 'id', 'name', 'state_slug');
$userLocation = DataHelper::getUserLocation();
$styleCityField = $userLocation['cityId'] == '' ? 'block' : 'none';
$student = \Yii::$app->user->identity;
$emailValue = !empty($student->email) ? $student->email : '';

$this->registerCssFile(Yii::$app->params['cssPath'] . 'news_lead.css', ['depends' => [AppAsset::class]]);


?>
<div class="pageMask" style="display: none;"></div>
<div class="leadFormContainerNews">
    <div class="closeLeadFormContainer">
        <span class="webpSpriteIcon closeLeadForm"></span>
    </div>
    <div class="leadFormDiv">
        <form class="userForm signinFormNews" id="signup-form-news">
            <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
            <i class="spriteIcon closeLeadForm"></i>
            <div class="userInputs">
                <div class="formHeadingDiv row">
                    <div class="formImg">
                        <img src="<?= Url::defaultCollegeLogo() ?>" alt="" id="leadform-image">
                    </div>
                    <div class="formHeading">
                        <p class="headingText">Signup to continue</p>
                        <p class="subHeadingText">Get latest updates and access to all
                            premium content</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 newsMobileName">
                        <div class="formField">
                            <i class="spriteIcon userIcon"></i>
                            <div class="">
                                <input type="text" name="name" class="txtOnlyNews" id="formNameNews" placeholder="Enter Your Name" value="<?= $student->name ?? '' ?>">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 newsMobilePhone">
                        <div class="mobileNumber row">
                            <div class="countryCode">
                                <div class="dialCodeDiv">
                                    <i class="webpSpriteIcon flagIcon"></i>
                                    <span class="dialCode">+91</span>
                                </div>
                            </div>
                            <div class="numberInput">
                                <div class="field-leadform-mobile">
                                    <input type="number" name="phone" class="signup-phone-news" id="signup-phone-news" min="0" maxlength="10" placeholder="Mobile Number" value="<?= $student->phone ?? '' ?>">
                                </div>
                            </div>
                            <p class="error errorMsg errorMsgMobile"></p>
                        </div>
                    </div>
                    <div class="col-md-6 inputEmailContainerNewsMobile">
                        <div class="formField">
                            <i class="spriteIcon mailIcon"></i>
                            <div class="mobileNewsEmailAlignment">
                                <input type="text" class="emailTxtOnlyNews" id="formEmailNews" name="email" placeholder="Email Address" value="<?= $emailValue ?? '' ?>">
                                <p class="error errorMsg errorMsgEmailNews"></p>
                                <!-- <span class="domainExtention">@gmail.com</span> -->
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 newsMobileCity" style="display: <?= $styleCityField ?>;">
                        <div class="formField selectCity">
                            <i class="spriteIcon locationIcon"></i>
                            <select class="select2" id="selectCityNews" name="current_city" data-placeholder="Select Your Current City">
                                <option disabled="disabled" selected="true" value="">Select Your Current City</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="formField streamCategory">
                            <i class="spriteIcon bookIcon"></i>
                            <select class="select2" id="selectStreamNews" name="stream">
                                <option></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="formField levelCategory">
                            <i class="spriteIcon bookIcon"></i>
                            <select class="select2" id="selectLevelNews" name="level" disabled>
                                <option></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6" style="display: none;">
                        <div class="formField">
                            <i class="spriteIcon capIcon"></i>
                            <div class="">
                                <select class="select2" id="selectEducationNews" name="education_level" data-placeholder="Select Your Highest Qualification">
                                    <option disabled="disabled" selected="true" value="">Select Your Highest Qualification</option>
                                    <?php
                                    foreach (ArrayHelper::map(DataHelper::highestQualification(), 'value', 'displayName') as $key => $value) {
                                        echo "<option value='$key'>$value</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="row locationRow">
                    <span class="webpSpriteIcon formLocationIcon"></span>
                    <p>
                        <span class="locationCity">Bangalore</span> is your current location. <span class="locationChange">Change</span>
                    </p>
                </div> -->
                <div class="row m-0">
                    <div class="checkbox-group col-md-10 pr-0 pl-0">
                        <input type="checkbox" id="distanceEducation" name="distanceEducation" value="distanceEducation" checked="checked">

                        <label for="distanceEducation">By clicking, I agree GetMyUni Terms and Conditions.
                        </label>
                    </div>
                    <div class="formSumbitBtn col-md-2 p-0">
                        <button class="primaryBtn newsSubmit" type="submit" disabled>Submit</button>
                    </div>
                </div>
                <div class="row accountExist">
                    <p class="loginNews">Already have an account? <span>Login Here!</span></p>
                </div>
                <input type="hidden" name="entity">
                <input type="hidden" name="entity_id">
                <input type="hidden" name="url">
                <input type="hidden" name="platform" value="<?= $isMobile ? 'wap' : 'web' ?>">
                <input type="hidden" name="entity_sub_type">
                <input type="hidden" name="cta_location">
                <input type="hidden" name="cta_text">
                <input type="hidden" name="is_lead">
                <input type="hidden" name="source" value="<?= empty($_GET['source']) ? 0 : DataHelper::$leadSource[$_GET['source']] ?>">
                <input type="hidden" name="utm_source" value="<?= empty($_GET['utm_source']) ? '' : $_GET['utm_source'] ?>">
                <input type="hidden" name="utm_medium" value="<?= empty($_GET['utm_medium']) ? '' : $_GET['utm_medium'] ?>">
                <input type="hidden" name="utm_campaign" value="<?= empty($_GET['utm_campaign']) ? '' : $_GET['utm_campaign'] ?>">
                <input type="hidden" name="mobile_lead_news" value="1">
                <input type="hidden" id="selectCityNewsIp" name="current_city_ip" value="<?= !empty($userLocation) ? $userLocation['cityId'] : '' ?>">
                <input type="hidden" name="current_state_ip" id="current_state_ip_news" value="<?= !empty($userLocation) ? $userLocation['stateId'] : '' ?>">
            </div>
        </form>
    </div>
</div>
<div class="subscribeSectionNews logInPage" style="display: none;">
    <div class="container">
        <div class="pageBody">
            <div class="closeLeadFormContainer">
                <span class="webpSpriteIcon closeLeadForm"></span>
            </div>
            <div class="row m-0 align-items-center">
                <div class="col-md-6 right-col">
                    <form class="userForm" id="otp-form-news">
                        <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
                        <input type="hidden" name="entity" value="news">
                        <div class="signInDiv">
                            <div>
                                <h2 class="m-0">Welcome Back!</h2>
                                <p>Lorem ipsum dolor sit amet, consectetur
                                    adipiscing elit</p>
                                <img class="lazyload" loading="lazy" src="/yas/images/login-image.webp" alt="img" width="273" height="400">

                                <div class="formField">
                                    <div class="row m-0">
                                        <div class="dialCodeDiv">
                                            <i class="spriteIcon flagIcon"></i>
                                            <span class="dialCode">+91</span>
                                            <img src="https://www.getmyuni.com/yas/images/select-angle.png" width="14" height="9" alt="">
                                        </div>
                                        <div class="numberInput">
                                            <div class="form-group field-leadform-mobile required">
                                                <input type="number" class="otp-phone-news" id="otp-phone-news" name="phone" min="0" oninput="validity.valid||(value='');" maxlength="10" placeholder="Mobile Number" required>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="error errorMsg"></p>
                                </div>
                                <p class="disclaimer forPage">A 4 digit OTP will be sent via SMS to verify your mobile number!</p>
                                <button class="primaryBtn" type="submit">Request OTP</button>
                                </p>
                                <div class="row accountNotExist">
                                    <p class="logInOptionNews">Dont have an account yet? <span>Sign Up</span></p>
                                </div>
                            </div>
                        </div>
                    </form>
                    <form class="userForm" id="login-form-news" style="display:none;">
                        <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
                        <div class="optSection">
                            <h2>Verify Mobile Number</h2>
                            <p class="otpMessage">OTP has been sent to your mobile
                                number <br /> +91 <span id="mobileNum"></span><a class="changeNumber"> Change </a></p>
                            <input type="hidden" id="login-phone-news" name="phone">
                            <input type="hidden" name="otp">
                            <div class="otpInputsNews numberInputs">
                                <div>
                                    <input type="number" class="nextInput digit" id="digit-1" name="digit[]" data-next="digit-2" maxlength="1" data-count=1>
                                    <input type="number" class="nextInput digit" id="digit-2" name="digit[]" data-next="digit-3" data-previous="digit-1" maxlength="1" data-count=1>
                                    <input type="number" class="nextInput digit" id="digit-3" name="digit[]" data-next="digit-4" data-previous="digit-2" maxlength="1" data-count=1>
                                    <input type="number" class="nextInput digit" id="digit-4" name="digit[]" data-next="digit-5" data-previous="digit-3" maxlength="1" data-count=1>
                                </div>
                                <p class="text-danger" id="otpResponseText"></p>
                                <!-- <input id="gdGonka" name="gdGonka" type="hidden"/> -->
                            </div>
                            <p class="error errorMsg"></p>
                            <div class="row">
                                <button class="primaryBtn" type="submit">Verify OTP</button>
                                <p id="otpResend"></p>
                            </div>
                        </div>
                    </form>
                    <div class="closeLeadFormContainerThankYou" style="display:none;">
                        <span class="webpSpriteIcon closeLeadForm"></span>
                    </div>
                    <div class="thankYouMsgNews" style="display: none;">
                        <i class="webpSpriteIcon thankYouIcon"></i>
                        <div class="thankYouText">
                            <p>Thank You</p>
                            <p>Our team will contact you shortly</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>