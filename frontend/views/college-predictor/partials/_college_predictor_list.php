<?php

use common\helpers\DataHelper;
use frontend\helpers\Url;
use yii\helpers\Html;

// Default image if college image is not available
$defaultImage = 'https://images.shiksha.com/mediadata/images/articles/1693247327php9Ixvyk.jpeg';
$defaultLogo = 'https://media.collegedekho.com/media/img/institute/logo/Lovely_Professional_University_Seal.jpg';
?>
<div class="predictor-container" id="itemContainer">
    <?php if (!empty($colleges)): ?>
        <?php foreach ($colleges as $college):
            ?>
            <div class="predictor-box">
                <div class="predictor-image">
                    <img src="<?= !empty($college['cover_image']) ? Url::getCollegeBannerImage($college['cover_image']) : Url::toDefaultCollegeBanner() ?>" />

                    <?php if (!empty($college['brochure'])): ?>
                        <div class="download">
                            <!-- <span class="spriteIcon downloadIcon"></span> -->

                            <?= frontend\helpers\Html::leadButton(
                                '',
                                [
                                    'entity' => $entity,
                                    'entityId' => $college['id'],
                                    'ctaLocation' => 'college_predictor_doenload_brochure',
                                    'ctaText' => 'Brochure',
                                    'ctaTitle' => '',
                                    'leadformtitle' => 'Register To Download Brochure',
                                    'subheadingtext' => 'Register To Download Brochure',
                                    'image' => !empty($college['cover_image']) ? Url::getCollegeBannerImage($college['cover_image']) : '/yas/images/defaultcardbanner.png',
                                    'redirection' => null,
                                    'alternateMedia' => null,
                                    'alternatePageRedirectSlug' => null,
                                    'durl' => empty($college['brochure']) ? null : (DataHelper::s3Path($college['brochure']['pdf'], 'brochure', true) ?? ''),
                                ],
                                ['class' => 'spriteIcon downloadIcon leadCourseCapture js-open-lead-form-new', 'onclick' => null]
                            ) ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="predictor_text">
                    <div class="predictor_logo">
                        <img src="<?= !empty($college['logo']) ? Url::getCollegeLogo($college['logo']) : $defaultLogo ?>" />
                    </div>
                    <div class="common-height">
                        <p>
                            <?= Html::encode($college['name']) ?>
                            <?= !empty($college['state']) ? ', ' . Html::encode($college['state']) : '' ?>
                        </p>
                        <div class="predictor_text_1">
                            <p class="percentage"><span class="spriteIcon percentageIcon"></span>Highest Rank</p>
                            <p><?= Html::encode($college['highest_program'] ?? 'N/A') ?></p>
                            <p>Rank: <?= Html::encode($college['highest_cutoff'] ?? 'N/A') ?></p>
                        </div>

                        <div class="predictor_text_1">
                            <p class="percentage"><span class="spriteIcon percentageIcon"></span>Lowest Rank</p>
                            <p><?= Html::encode($college['lowest_program'] ?? 'N/A') ?></p>
                            <p>Rank: <?= Html::encode($college['lowest_cutoff'] ?? 'N/A') ?></p>
                        </div>
                    </div>
                    <a href="<?= Url::toCollege($college['slug']) ?>">
                        <button>View Details</button>
                    </a>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="no-results">
            <p>No colleges found matching your criteria.</p>
        </div>
    <?php endif; ?>
</div>

<?php if ($hasNext): ?>
    <div class="load-more">
        <button id="loadMoreCollegePredictor">View More colleges</button>
    </div>
<?php endif; ?>