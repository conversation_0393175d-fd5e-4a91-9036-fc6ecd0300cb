<?php

use yii\helpers\Html;

// Get college count and type for header text
$collegeCount = isset($totalCount) ? $totalCount : 0;
$collegeTypeText = '';
if (isset($searchModel) && !empty($searchModel->collegeType)) {
    $collegeTypeOptions = $searchModel->getCollegeTypeOptions();
    $collegeTypeText = $collegeTypeOptions[$searchModel->collegeType] ?? '';
}
?>
<?php /*
<?php if (!$showAllData): ?>
    <div class="predictor-header-text">
        <h2>Colleges based on your marks</h2>
    </div>
<?php else: ?>
    <div class="predictor-header-text">
        <h2>All Colleges</h2>
    </div>
<?php endif; ?>
*/ ?>

<div class="predictor-select-box">
    <form id="college-predictor-filter-form">
        <!-- Hidden inputs for rank and category -->
        <input type="hidden" name="rank" value="<?= $searchModel->rank ?>">
        <input type="hidden" name="category" value="<?= $searchModel->category ?>">

        <ul>
            <?php /*
            <li class="custom-select">
                <select name="course" id="course-filter" class="filter-select">
                    <option value="" selected="">Course</option>
                    <?php foreach ($programOptions as $id => $name): ?>
                        <option value="<?= $id ?>" <?= $searchModel->course == $id ? 'selected' : '' ?>><?= $name ?></option>
                    <?php endforeach; ?>
                </select>
            </li>
            */ ?>

            <?php
            // Filter collegeTypeOptions to exclude empty/null entries
            $collegeTypeOptions = array_filter($collegeTypeOptions, function ($value) {
                return !empty($value);
            });
            ?>

            <?php if (!empty($collegeTypeOptions)): ?>
                <li class="custom-select">
                    <select name="collegeType" id="college-type-filter" class="filter-select">
                        <option value="" selected="">College Type</option>
                        <?php foreach ($collegeTypeOptions as $id => $name): ?>
                            <option value="<?= $id ?>" <?= $searchModel->collegeType == $id ? 'selected' : '' ?>><?= $name ?></option>
                        <?php endforeach; ?>
                    </select>
                </li>
            <?php endif; ?>

            <li class="custom-select">
                <select name="state" id="state-filter" class="filter-select">
                    <option value="" selected="">State</option>
                    <?php foreach ($stateOptions as $id => $name): ?>
                        <option value="<?= $id ?>" <?= $searchModel->state == $id ? 'selected' : '' ?>><?= $name ?></option>
                    <?php endforeach; ?>
                </select>
            </li>
        </ul>
    </form>
</div>