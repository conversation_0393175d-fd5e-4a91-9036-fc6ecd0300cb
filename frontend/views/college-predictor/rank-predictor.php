<?php

//utlis

use frontend\assets\AppAsset;
use frontend\helpers\Url;

$currentUrl = Url::base(true);
$isMobile = \Yii::$app->devicedetect->isMobile();

//meta data
$this->title = 'Rank Predictor';
$this->context->description = 'Find all the latest college reviews by students and alumni across the country for top colleges & Universities in India.';
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
if (!empty($examSlug)) {
    $this->params['canonicalUrl'] = Url::base(true) . '/exams/' . $examSlug . '-rank-predictor';
    $this->params['breadcrumbs'][] = ['label' => 'Predictor', 'url' => ['/rank-predictor'], 'title' => 'Predictor'];
}
$this->params['breadcrumbs'][] = ['label' => 'Rank Predictor'];


$this->registerCssFile(Yii::$app->params['cssPath'] . 'college_predictor_landing.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'bottom-widget.css', ['depends' => [AppAsset::class]]);
$this->registerJsFile(Yii::$app->params['jsPath'] . 'college_predictor.js', ['position' => \yii\web\View::POS_END, 'depends' => [AppAsset::class]]);

$this->params['entity'] = 'rank-predictor';
$this->params['entity_name'] = 'rank-predictor';
$this->params['entitySlug'] = 'rank-predictor';
?>

<main>
    <!--header-->
    <div class="predictor-header">
        <h1>GMU Rank Predictor 2025</h1>
    </div>

    <!--jquery tab-->
    <div class="predict-college-tab">
        <?php if (!empty($streamExams)): ?>
            <ul class="tabs">
                <?php
                $tabCount = 1;
                foreach ($streamExams as $streamId => $data):
                    $stream = $data['stream'];
                    $isActive = ($tabCount == 1) ? 'current' : '';
                    ?>
                    <li class="tab <?= $isActive ?>" data-tab="tab-<?= $tabCount ?>"><?= $stream->display_name ?? $stream->name ?> Exams</li>
                    <?php
                    $tabCount++;
                endforeach;
                ?>
            </ul>
            <div class="tab-content-section">
                <?php
                $tabCount = 1;
                foreach ($streamExams as $streamId => $data):
                    $stream = $data['stream'];
                    $exams = $data['exams'];
                    $isActive = ($tabCount == 1) ? 'current' : '';
                    ?>
                    <div id="tab-<?= $tabCount ?>" class="tab-content <?= $isActive ?>">
                        <h2><?= $stream->display_name ?? $stream->name ?> Exams</h2>
                        <?php if (!empty($exams)): ?>
                            <ul>
                                <?php foreach ($exams as $exam): ?>
                                    <li>
                                        <a href="<?= Url::base(true) ?>/exams/<?= $exam->slug ?>-rank-predictor">
                                            <div class="logo-section">
                                                <div class="icon-section">
                                                    <img src="<?= !empty($exam->cover_image) ? Url::toExamImage($exam->cover_image) : Url::defaultCollegeLogo() ?>" alt="">
                                                </div>
                                                <span><?= $exam->display_name . ' Rank Predictor' ?? $exam->name . ' Rank Predictor' ?></span>
                                            </div>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <p>No exams available for this stream.</p>
                        <?php endif; ?>
                    </div>
                    <?php
                    $tabCount++;
                endforeach;
                ?>
            </div>
        <?php else: ?>
            <div class="no-data-message">
                <p>No exam data available. Please check back later.</p>
            </div>
        <?php endif; ?>
    </div>
    <!--jquery tab-->

    <!--share-whatsup-->
    <div class="share-whatsup">
        <div class="share-flex">
            <div class="whatsup_text">
                <h3>Help your friends by sharing this FREE tool in your friend and coaching groups</h3>
            </div>
            <div class="share-btn">
                <button class="whatsup" onclick="shareOnWhatsApp()"><span class="spriteIcon whatsupicon"></span>Share on WhatsApp</button>
                <button class="copy-link" onclick="copyLink()"><span class="spriteIcon attachIcon"></span>Copy Link</button>
            </div>
        </div>
    </div> <!--share-whatsup-->

</main>