<?php

$params = array_merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php',
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php'
);

return [
    'id' => 'app-frontend',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'controllerNamespace' => 'frontend\controllers',
    'language' => 'en',
    'components' => [
        'assetManager' => [
            'appendTimestamp' => true,
            'basePath' => '@gnAsset' . '/assets',
            'baseUrl' => '/yas/assets',
            'assetMap' => [
                'jquery.js' => '/yas/js/version2/ajax/libs/js/jquery.min.js',
            ],
        ],
        'i18n' => [
            'translations' => [
                'app' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@app/messages',
                    'sourceLanguage' => 'en',
                ],
            ]
        ],

        'request' => [
            'csrfParam' => '_csrf-frontend',
            'enableCsrfValidation' => false,
        ],
        'user' => [
            'identityClass' => 'frontend\models\Student',
            'enableAutoLogin' => true,
            'identityCookie' => ['name' => '_identity-frontend', 'httpOnly' => true],
        ],
        'saUser' => [
            'class' => 'yii\web\User',
            'identityClass' => 'frontend\models\SaStudent',
            'enableAutoLogin' => true,
            'identityCookie' => ['name' => '_identity-sa', 'httpOnly' => true],
        ],
        'session' => [
            // this is the name of the session cookie used for login on the frontend
            'name' => 'advanced-frontend',
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],

        //'urlManager' => require __DIR__ . '/_url-manager.php',

        'urlManager' => (function () {
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

            // Clp domain based routing
            if (stripos($host, 'admission.') === 0 || $host === 'admission.getmyuni.com/' || stripos($host, 'admissions.') === 0 || $host === 'admissions.mycollegeedu.com/') {
                return require __DIR__ . '/_url-manager-lp.php';
            } else {
            // Default rule set
                return require __DIR__ . '/_url-manager.php';
            }
        })(),
    ],
    'params' => $params,
];
