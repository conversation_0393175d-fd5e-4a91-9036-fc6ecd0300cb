function loadCityWiseData() { $("#gis__salaryContainer__chart").html('<p style="padding:10px;">Please wait for a while we are processing your request.</p>'); var a = $("#checkSalarySelect").val(), t = slug; $.ajax({ type: "POST", url: "/immigration/get-chart-data", data: { designation: a, page: t }, dataType: "json", success: function (a) { drawCityWiseChart(a) } }) } function drawCityWiseChart(a) { var t = $("#checkSalarySelect").val(), e = a, i = new google.visualization.DataTable; i.addColumn("string", "City"), i.addColumn("number", "Avg. Salary (USD)"), $.each(e, (function (a, t) { var e = t.city, r = parseFloat($.trim(t.salary)); i.addRows([[e, r]]) })), new google.visualization.NumberFormat({ negativeColor: "red", negativeParens: !0, pattern: "$###,###" }).format(i, 1); var r = { title: t + " Salaries", hAxis: { title: countryName + " Cities", color: "#282828", format: "$#", textStyle: { fontSize: 8 } }, vAxis: { title: "Salaries per annum", color: "#282828", format: "$#" }, height: 600, bar: { groupWidth: "50%" } }; new google.visualization.ColumnChart(document.getElementById("gis__salaryContainer__chart")).draw(i, r) } google.charts.load("current", { packages: ["corechart", "bar"] }), google.charts.setOnLoadCallback((function () { loadCityWiseData() })), $(document).ready((function () { setTimeout(loadCityWiseData, 500), $("#checkSalarySelect").change((function () { loadCityWiseData() })), $(".salaryFilterButton").click((function () { loadCityWiseData() })) }));