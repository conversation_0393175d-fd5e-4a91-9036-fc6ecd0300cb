$(window).on('load', function () {
  $('.programs-offered-slider').slick({
    slidesToShow: 4,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    speed: 5000,
    infinite: true,
    autoplaySpeed: 0,
    autoplay: true,
    cssEase: 'linear',
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 1,
        }
      }
    ]
  });

  $('.programs-offered-slider').fadeIn();

  $('.college-usps-inner').slick({
    slidesToShow: 3,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    speed: 5000,
    infinite: true,
    autoplaySpeed: 0,
    autoplay: true,
    cssEase: 'linear',
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 1,
        }
      }
    ]
  });

  $('.college-usps-inner').fadeIn();

  $('.top-recruiters-slider').slick({
    slidesToShow: 6,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    speed: 5000,
    infinite: true,
    autoplaySpeed: 0,
    autoplay: true,
    cssEase: 'linear',
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 1,
        }
      }
    ]
  });

  $('.top-recruiters-slider').fadeIn();

  $('.why-getmyuni-inner').slick({
    slidesToShow: 3,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    speed: 1500,
    infinite: false,
    autoplaySpeed: 5000,
    autoplay: false,
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 1,
        }
      }
    ]

  });
  $('.why-getmyuni-inner').fadeIn();


    $('.college-usps-inner').slick({
    slidesToShow: 3,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    speed: 1500,
    infinite: false,
    autoplaySpeed: 5000,
    autoplay: false,
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 1,
        }
      }
    ]
  });
  // $('.streams-offered-inner').fadeIn();

  $('.why-getmyuni-inner').slick({
    slidesToShow: 3,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    speed: 1500,
    infinite: false,
    autoplaySpeed: 5000,
    autoplay: false,
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 1,
        }
      }
    ]
  });
  $('.why-getmyuni-inner').fadeIn();

  $('.our-partner-slider').slick({
    slidesToShow: 6,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    speed: 1500,
    infinite: true,
    autoplaySpeed: 0,
    autoplay: true,
    cssEase: 'linear',
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 4,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      }
    ]
  });

  $('.our-partner-slider').fadeIn();

  $('.our-campus-slider').slick({
    slidesToShow: 4,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    speed: 1500,
    infinite: true,
    autoplaySpeed: 5000,
    autoplay: false,
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 1,
        }
      }
    ]
  });
  $('.our-campus-slider').fadeIn()

  $('.top-recruiters-slider').slick({
    lazyLoad: 'ondemand',
    slidesToShow: 6,
    slidesToScroll: 2,
    arrows: false,
    dots: false,
    speed: 1500,
    infinite: true,
    autoplaySpeed: 1000,
    autoplay: true,
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 4,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      }
    ]
  });

   $('.generic_five .why-getmyuni-inner').slick({
      slidesToShow: 3,
      slidesToScroll: 1,
      arrows: false,
      dots: false,
      speed: 5000,
      infinite: true,
      autoplaySpeed: 0,
      autoplay: true,
      cssEase: 'linear',
      responsive: [
        {
          breakpoint: 991,
          settings: {
            slidesToShow: 3,
          }
        },
        {
          breakpoint: 767,
          settings: {
            slidesToShow: 2,
          }
        },
        {
          breakpoint: 500,
          settings: {
            slidesToShow: 1,
          }
        }
      ]
    });
  $('.generic_five .our-partner-slider').slick({
      slidesToShow: 6,
      slidesToScroll: 1,
      arrows: false,
      dots: false,
      speed: 5000,
      infinite: true,
      autoplaySpeed: 0,
      autoplay: true,
      cssEase: 'linear',
      responsive: [
        {
          breakpoint: 991,
          settings: {
            slidesToShow: 3,
          }
        },
        {
          breakpoint: 767,
          settings: {
            slidesToShow: 2,
          }
        },
        {
          breakpoint: 500,
          settings: {
            slidesToShow: 1,
          }
        }
      ]
  });
});

$(document).ready(function () {
  // form submission
  $('#genericScreenSubmit, #modalScreenSubmit').click(function (e) {
    e.preventDefault();

    // Determine which button was clicked
    var clickedBtnId = e.target.id;

    // Map button ID to form ID
    var formIdMap = {
      'genericScreenSubmit': '#genericfirstScreen',
      'modalScreenSubmit': '#modalFormClp'
    };

    var formSelector = formIdMap[clickedBtnId];
    if (!formSelector) return;

    var form = $(formSelector);

    $.ajax({
      url: '/custom-landing-page-lead/create',
      data: form.serialize(),
      dataType: 'json',
      method: 'POST',
      beforeSend: function () {
        $('#' + clickedBtnId).prop('disabled', true);
      },
      error: function () {
        $('#' + clickedBtnId).prop('disabled', false);
        alert('Something went wrong, please try again!');
      },
      complete: function () {
        $('#' + clickedBtnId).prop('disabled', true);
      },
      success: function (data) {
        if (data.success == true) {
          // console.log(gmu.config);

          gtag('event', 'conversion', {
            'send_to': gmu.config.scriptConversion
          });

          var collegeSlug = $('#college_slug').val();
          var redirectionUrl = $('#redirection_url').val();

          if (redirectionUrl) {
            window.location.href = redirectionUrl;
          } else if (collegeSlug) {
            window.location.href = '/college/' + collegeSlug;
          } else {
            location.reload(true);
          }
        }
      }
    });

    return false;
  });

  //enable form submit button
  function isDynamicFormValid(formSelector = '') {

    let isValid = true;

    $(`${formSelector} .required-field`).each(function () {
      const field = $(this);
      const tagName = field.prop('tagName').toLowerCase();
      const value = field.val();
      const id = field.attr('id');
      // Handle email validation

      if ((id === 'email' || id === 'email1') && (!value || $('#' + id + 'Error').text().trim() !== '')) {
        isValid = false;
        return false; // break loop
      }

      // Handle phone validation
      else if ((id === 'phone' || id === 'phone1') && value.replace(/\D/g, '').length !== 10) {
        isValid = false;
        return false;
      }

      // Handle select fields (including Select2-enhanced ones)
      else if (tagName === 'select' && (!value || value === 'null' || value.length === 0)) {
        isValid = false;
        return false;
      }

      // Handle other input fields
      else if (!value || value.trim() === '') {
        isValid = false;
        return false;
      }
    });

    return isValid;
  }

  $(document).on('input change', '.required-field', function () {
    // For main form
    $('#genericScreenSubmit').prop('disabled', !isDynamicFormValid('#genericfirstScreen'));

    // For modal form
    $('#modalScreenSubmit').prop('disabled', !isDynamicFormValid('#modalFormClp'));
  });

  // Phone validation
  document.querySelectorAll("#phone, #phone1").forEach(function (phoneInput) {
    phoneInput.addEventListener("input", function (e) {
      e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '');
    });
  });

  //name validation
  $("#name, #name1").on("input", function (e) {
    var inputValue = $(this).val();
    var regex = /^[A-Za-z ]+$/;

    if (!regex.test(inputValue)) {
      var nameValue = inputValue.replace(/[^A-Za-z ]/g, '');
      $(this).val(nameValue);
    }
  });

  // Email validation
  document.querySelectorAll("#email, #email1").forEach(function (emailInput) {
    emailInput.addEventListener("input", function (e) {
      let val = e.target.value;

      // Allow only valid characters
      val = val.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');

      // Control dot usage
      const atCount = (val.match(/@/g) || []).length;
      const dotCount = (val.match(/\./g) || []).length;
      if (atCount > 0 && dotCount > 2) {
        val = val.slice(0, -1);
      } else if (atCount === 0 && dotCount > 1) {
        val = val.slice(0, -1);
      }

      e.target.value = val;

      // Get the related error field
      const errorId = e.target.id === "email" ? "#emailError" : "#email1Error";

      // Final validation
      const email = val.trim();
      let errorMessage = "";

      if (email === "") {
        errorMessage = ""; // clear error on empty
      } else {
        const domain = email.slice(email.indexOf("@") + 1);
        const isValid = email.includes("@") &&
          !email.endsWith("@") &&
          /^[a-zA-Z]+\.[a-zA-Z]+$/.test(domain);
        if (!isValid) {
          errorMessage = "Email is not a valid email address.";
        }
      }

      // Show the error only in the corresponding field
      $(errorId).html(errorMessage);
    });
  });

  //select2 widget
  if (document.querySelector(".clpState") !== null) {
    const selectedStateIds = $('#state_ids').val();
    $('.clpState').select2({
      placeholder: "Select State",
      name: 'user_state',
      ajax: {
        url: "/ajax/clp-state-list",
        dataType: "json",
        type: "POST",
        data: function (params) {
          var queryParameters = {
            q: params.term,
            ids: selectedStateIds
          }
          return queryParameters;
        },
        processResults: function (data) {
          console.log(data);
          return {
            results: $.map(data.results, function (item) {
              return {
                text: item.text,
                id: item.id,
              };
            }),
          };
        },
      },
    });
  }

  if (document.querySelector(".clpCity") !== null) {
    const selectedCityIds = $('#city_ids').val();
    $('.clpCity').select2({
      placeholder: "Select City",
      name: 'user_city',
      ajax: {
        url: "/ajax/clp-city-list",
        dataType: "json",
        type: "POST",
        data: function (params) {
          var queryParameters = {
            q: params.term,
            ids: selectedCityIds
          }
          return queryParameters;
        },
        processResults: function (data) {
          console.log(data);
          return {
            results: $.map(data.results, function (item) {
              return {
                text: item.text,
                id: item.id,
              };
            }),
          };
        },
      },
    });
  }

  if (document.querySelector(".clpCourse") !== null) {
    $('.clpCourse').select2({
      placeholder: "Select Course",
      name: 'user_course',
      ajax: {
        url: "/ajax/clp-course-list",
        dataType: "json",
        type: "POST",
        data: function (params) {
          var queryParameters = {
            q: params.term,
            ids: $('#course_ids').val(),
            college_id: $('#college_id').val(),
            template_id: $('#template_id').val(),
          }
          return queryParameters;
        },
        processResults: function (data) {
          console.log(data);
          return {
            results: $.map(data.results, function (item) {
              return {
                text: item.text,
                id: item.id,
              };
            }),
          };
        },
      },
    });
  }

  if (document.querySelector(".clpStream") !== null) {
    const selectedStreamIds = $('#stream_ids').val();
    const getCollegeId = $('#college_id').val();
    const templateId = $('#template_id').val();
    $('.clpStream').select2({
      placeholder: "Select Stream",
      name: 'user_stream',
      ajax: {
        url: "/ajax/clp-stream-list",
        dataType: "json",
        type: "POST",
        data: function (params) {
          var queryParameters = {
            q: params.term,
            ids: selectedStreamIds,
            college_id: getCollegeId,
            template_id: templateId
          }
          return queryParameters;
        },
        processResults: function (data) {
          return {
            results: $.map(data.results, function (item) {
              return {
                text: item.text,
                id: item.id,
              };
            }),
          };
        },
      },
    });
  }

  if (document.querySelector(".clpCampus") !== null) {
    const selectedStreamIds = $('#campus_ids').val();
    $('.clpCampus').select2({
      placeholder: "Select Campus",
      name: 'user_campus',
      ajax: {
        url: "/ajax/clp-campus-list",
        dataType: "json",
        type: "POST",
        data: function (params) {
          var queryParameters = {
            q: params.term,
            ids: selectedStreamIds,
          }
          return queryParameters;
        },
        processResults: function (data) {
          return {
            results: $.map(data.results, function (item) {
              return {
                text: item.text,
                id: item.id,
              };
            }),
          };
        },
      },
    });
  }

  if (document.querySelector(".clpDegree") !== null) {
    $('.clpDegree').select2({
      placeholder: "Select Degree",
      name: 'user_degree',
      ajax: {
        url: "/ajax/clp-degree-list",
        dataType: "json",
        type: "POST",
        delay: 250,
        data: function (params) {
          const streamVal = $('#user_stream').is(':visible') && $('#user_stream').val()
            ? $('#user_stream').val()
            : $('#modal_stream').val();

          return {
            q: params.term,
            ids: $('#degree_ids').val(),
            college_id: $('#college_id').val(),
            template_id: $('#template_id').val(),
            stream_id: streamVal
          };
        },
        processResults: function (data) {
          return {
            results: $.map(data.results, function (item) {
              return {
                text: item.text,
                id: item.id
              };
            })
          };
        }
      }
    });
  }

  $('.clpStreamConatiner select').on('change', function (e) {
    $('#user_degree').val(null).trigger('change');
    $('#user_degree').prop('disabled', false);
  });


  $('.clpModalStreamConatiner select').on('change', function (e) {
    $('#modal_degree').val(null).trigger('change');
    $('#modal_degree').prop('disabled', false);
  });

  if (document.querySelector(".clpProgram") !== null) {
    $('.clpProgram').select2({
      placeholder: "Select Program",
      name: 'user_program',
      ajax: {
        url: "/ajax/clp-program-list",
        dataType: "json",
        type: "POST",
        data: function (params) {
          var queryParameters = {
            q: params.term,
            ids: $('#program_ids').val(),
            college_id: $('#college_id').val(),
            template_id: $('#template_id').val()
          }
          return queryParameters;
        },
        processResults: function (data) {
          return {
            results: $.map(data.results, function (item) {
              return {
                text: item.text,
                id: item.id,
              };
            }),
          };
        },
      },
    });
  }

  // Show toggle if overflow
  $(".show-more-content").each(function () {
    var content = $(this);
    var toggleBtn = content.siblings(".toggle-btn");

    var visibleHeight = parseInt(content.data("height")) || 300;

    if (content.prop('scrollHeight') > visibleHeight) {
      toggleBtn.show();
    } else {
      toggleBtn.hide();
    }
  });

  //about section
  $(".toggle-btn").click(function () {
    var content = $(this).siblings(".show-more-content");
    if (content.length > 0) {
      content.toggleClass("expanded");

      if (content.hasClass("expanded")) {
        $(this).html('Read Less <i class="arrow down"></i>');
      } else {
        $(this).html('Read More <i class="arrow up"></i>');
        $('.show-more-content').animate({ scrollTop: 0 }, 400);

      }
    }
  });

  //streams section
  $('#toggleBtn').click(function () {
    $('#textContent').toggleClass('expanded');

    if ($('#textContent').hasClass('expanded')) {
      $(this).html('View Less <i class="arrow down"></i>');
    } else {
      $(this).html('View More <i class="arrow up"></i>');
    }
  });
});

$(document).ready(function () {

  $("body .open-modal").click(function (e) {
    e.preventDefault();
    console.log('open modal');
    $("#form-modal").addClass("show-modal");
    $("body").addClass("overflow-hide")
  })
  $("#modal-close").click(function () {
    $("#form-modal").removeClass("show-modal");
    $("body").removeClass("overflow-hide")
  })

  $(' .streams-offered-inner').slick({
    slidesToShow: 3,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    speed: 5000,
    infinite: true,
    autoplaySpeed: 0,
    autoplay: true,
    cssEase: 'linear',
    responsive: [
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        }
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 1,
        }
      }
    ]
  });
});