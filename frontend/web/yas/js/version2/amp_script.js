//get field info
const nameInput = document.querySelector('.ampName');
const emailInput = document.querySelector('.ampEmail');
const mobileInput = document.querySelector('.ampMobile');
const citySelect = document.querySelector('.ampCity');
const streamSelect = document.querySelector('.ampStream');
const levelSelect = document.querySelector('.ampLevel');
const submitButton = document.querySelector('#leadpopupsubmit');
const streamSelectNumericPart = extractIdValue(streamSelect.id);
const levelSelectNumericPart = extractIdValue(levelSelect.id);
const cityIpSelectNumericPart = extractIdValue(citySelect.id);

// Function to extract numeric part from id value
function extractIdValue(value) {
    // Extract the numeric part using a regular expression
    const numericPart = value.match(/\d+/);
    return numericPart ? parseInt(numericPart[0]) : null;
}

//Function to check if all fields are filled
function checkFields(event) {
    if (nameInput.value !== '' && emailInput.value !== '' && (mobileInput.value !== '' && mobileInput.value.length === 10) && (citySelect.value !== ' ' || cityIpSelectNumericPart !== null) && (streamSelect.value !== ' ' || streamSelectNumericPart !== null) && (levelSelect.value !== ' ' || levelSelectNumericPart !== null) && ((document.querySelector("#id").value == '1' && document.getElementById("email_error_div_auto").innerHTML == '') || (document.querySelector("#id").value == '2' && document.getElementById("email_error_div").innerHTML == ''))) {
        submitButton.removeAttribute('disabled');
    } else {
        submitButton.setAttribute('disabled', true);
    }

    if (event?.target?.tagName === 'SELECT') {
        if (event.target.value === ' ') {
            event.target.style.color = '#989898'
        } else {
            event.target.style.color = "#282828"
        }
    }

    if (event.target.className === 'ampName') {
        event.target.value = event.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z ]/g, '');
    }

    if (event.target.className === 'ampMobile') {
        event.target.value = event.target.value.replace(/^[^6-9]|\D/g, '')
    }
}

// Add event listeners to input fields and select element
nameInput.addEventListener('input', checkFields);
emailInput.addEventListener('input', checkFields);
mobileInput.addEventListener('input', checkFields);
citySelect.addEventListener('change', checkFields);
streamSelect.addEventListener('change', checkFields);
levelSelect.addEventListener('change', checkFields);

emailInput.addEventListener('input', (event) => {
    var email = event.target.value;
    if (email.length > 0) {
        var reg = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;

        if (reg.test(email) === false) {
            if (document.querySelector("#id").value == 1) {
                document.getElementById("email_error_div_auto").innerHTML = "Invalid email format";
                document.getElementById("email_error_div_auto").style.display = "inline-block";
            } else {
                document.getElementById("email_error_div").innerHTML = "Invalid email format";
                document.getElementById("email_error_div").style.display = "inline-block";
            }
        } else {
            if (document.querySelector("#id").value == 1) {
                document.getElementById("email_error_div_auto").innerHTML = "";
                document.getElementById("email_error_div_auto").style.display = "none";
            } else {
                document.getElementById("email_error_div").innerHTML = "";
                document.getElementById("email_error_div").style.display = "none";
            }
        }

        if (reg.test(email) === true) {
            var split_data = email.split("@");
            var popular_domain = ["gmail.com", "yahoo.com", "hotmail.com", "aol.com", "outlook.com", "msn.com", "live.com"];
            if (!popular_domain.includes(split_data[1])) {
                if (document.querySelector("#id").value == 1) {
                    document.getElementById("email_error_div_auto").innerHTML = "Invalid email format";
                    document.getElementById("email_error_div_auto").style.display = "inline-block";
                } else {
                    document.getElementById("email_error_div").innerHTML = "Invalid email format";
                    document.getElementById("email_error_div").style.display = "inline-block";
                }
            } else {
                if (document.querySelector("#id").value == 1) {
                    document.getElementById("email_error_div_auto").innerHTML = "";
                    document.getElementById("email_error_div_auto").style.display = "none";
                } else {
                    document.getElementById("email_error_div").innerHTML = "";
                    document.getElementById("email_error_div").style.display = "none";
                }
            }
        }
    }
})
