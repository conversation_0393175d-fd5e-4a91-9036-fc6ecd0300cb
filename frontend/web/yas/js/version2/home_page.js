$(document).ready(function () {
    $('.featuredBrandList').slick({
        speed: 4000,
        autoplay: true,
        autoplaySpeed: 1000,
        centerMode: true,
        cssEase: 'linear',
        slidesToShow: 1,
        slidesToScroll: 1,
        variableWidth: true,
        infinite: true,
        initialSlide: 1,
        arrows: false,
        buttons: false
    });

    $('.carouselSection').slick({
        autoplay: true,
        dots: true,
        arrows: false,
        fade: true,
        speed: 1000
    });
    
    $(".foucus-search").focus(function(e){
        $("body .zsiq_floatmain").addClass('overrideZindex');
        $(".advanceSearch").fadeIn();
        $('meta[name=viewport]').attr('content', 'width=device-width, minimum-scale=1.0, maximum-scale=0.5, initial-scale=0.5');
        $("body .seachInput").focus();
        $("body").css('overflow', 'hidden');
        $('#showSearchDiv').show();
        getPopularRecentList();
      });

      
    // $('.tab-nav-link').click(function () {
        $("body").on("click", ".tab-nav-link", function (e) {
        $tabs = $(this).parent().parent().next();
        $target = $($(this).data("target")); // get the target from data attribute
        $(this).siblings().removeClass('tabLink');
        $target.siblings().css("display", "none");
        $(this).addClass('tabLink');
        $target.fadeIn();
    });

    var courseCardCount = $('.courseCard').length;
    $('.courseCardList .courseCard').slice(0, 9).show();
    $('.courseCardList .viewAllStream').on('click', function (e) {
        e.preventDefault();
        $(this).remove();
        $('.courseCardList .courseCard').slice(0, courseCardCount).slideDown();
    });

    // var exploreCardCount = $('.collegesWithCategoryData .dataCard').length;
    // $('.collegesWithCategoryData .dataCard').slice(0, 18).show();
    // $('.viewMoreCards').on('click', function (e) {
    //     e.preventDefault();
    //     $(this).remove();
    //     $('.collegesWithCategoryData .dataCard').slice(0, exploreCardCount).slideDown();
    // });

    $(document).scroll(function () {
        var offsetForToTop = $(this).scrollTop();
        if (offsetForToTop > 800) {
            $('.scrollToTop').fadeIn();
        } else {
            $('.scrollToTop').fadeOut();
        }
    });
    $(document).on("click", ".scrollToTop", function () {
        $(window).scrollTop(0);
    });

    $('.trendingTopicsList .scrollLeft').click(function () {
        $(this).next().next().animate({
            scrollLeft: '-=150px'
        }, 750);

    });
    $('.trendingTopicsList .scrollRight').click(function () {
        $(this).next().animate({
            scrollLeft: '+=150px'
        }, 750);
        $(this).prev().removeClass("over");
    });

    $('.student-testimonial .scrollLeft').click(function () {
        $(this).next().next().animate({
            scrollLeft: '-=365px'
        }, 750);

    });
    $('.student-testimonial .scrollRight').click(function () {
        $(this).next().animate({
            scrollLeft: '+=365px'
        }, 750);

        $(this).prev().removeClass("over");
    });

    $('.four-cardDisplay .scrollLeft').click(function () {
        $(this).next().next().animate({
            scrollLeft: '-=297px'
        }, 750);

    });
    $('.four-cardDisplay .scrollRight').click(function () {
        $(this).next().animate({
            scrollLeft: '+=297px'
        }, 750);

        $(this).prev().removeClass("over");
    });

    $('.trendingTopicsList ul, .customSliderCards, .customSliderList, .latestInfoList.row').scroll(function () {
        var $examInfoSliderWidth = $(this).outerWidth()
        var $examInfoSliderScrollWidth = $(this)[0].scrollWidth;
        var $examInfoSliderScrollLeft = $(this).scrollLeft();
        if (parseInt($examInfoSliderScrollWidth - $examInfoSliderWidth) === parseInt($examInfoSliderScrollLeft)) {
            $(this).prev().addClass("over");
        } else {
            $(this).prev().removeClass("over");
        }

        if ($examInfoSliderScrollLeft === 0) {
            $(this).prev().prev().addClass("over");
        } else {
            $(this).prev().prev().removeClass("over");
        }
    });

    // $(".pageData").each(function () {
    //     pageInfoHeight += $(this).height();
    // });
    $('.viewMoreCards').click(function () {
        $(this).parent().prev().removeClass("limitCards");
        $(this).remove();
    });
    $(".limitCards").each(function () {
        var asd = $(this).height();
        if (asd < 525) {
            // $(this).next().hide();
        } else {
            $(this).next().show();
        }
    });

});

$('.college-name-text-box').keyup(function (e) {
    clearTimeout($.data(this, 'timer'));
    if (e.keyCode == 13)
        search(true);
    else
        $(this).data('timer', setTimeout(search, 500));
});

//truncate the name
function truncateName(name, maxLength) {
    if (name.length <= maxLength) {
        return name;
    } else {
        return name.slice(0, maxLength) + '...';
    }
}

function logout() {
    $('#logoutForm').submit();
    webengage.track("logout", {
        'logout':'Yes'
    });
    setTimeout(() => {
        window.location.reload();
    }, 2000)
}

// to clear search panel
$('.tab-nav-link').click(function () {
    $('.heading-design').html('');
    $('.trending').html('').css({
        'display': 'none'
    });
});

if (document.getElementById("worldmap") !== null) {
    jQuery('#worldmap').vectorMap(
        {
            map: 'world_en',
            backgroundColor: '#fff',
            borderColor: '#818181',
            borderOpacity: 0.25,
            borderWidth: 1,
            color: '#d9d9d9',
            enableZoom: false,
            hoverColor: '#373bc4',
            hoverOpacity: 0.8,
            normalizeFunction: 'linear',
            scaleColors: ['#b6d6ff', '#005ace'],
            selectedColor: '#6dabf5',
            selectedRegions: ['CA', 'US', 'AU', 'DE', 'GB'],
            showTooltip: true,
            onRegionClick: function (element, code, region) {
                regionSlugs = {
                    'ca': 'canada',
                    'us': 'usa',
                    'au': 'australia',
                    'de': 'germany',
                    'gb': 'uk',
                }

                if (regionSlugs[code]) {
                    window.location.href = 'https://getmyuni.com/' + regionSlugs[code];
                }
            }
        });
}

$('.marquee').marquee({
    duration: 25000,
    gap: 50,
    delayBeforeStart: 0,
    direction: 'left',
    duplicated: true,
    pauseOnHover: true,
    startVisible: true
});

$('.homeFeaturedCollege').slick({
    speed: 2000,
    autoplay: true,
    autoplaySpeed: 1500,
    cssEase: 'linear',
    slidesToShow: 4,
    slidesToScroll: 1,
    float: screenLeft,
    infinite: true,
    initialSlide: 1,
    arrows: false,
    buttons: false,
    responsive: [
        {
            breakpoint: 1024,
            settings: {
                slidesToShow: 3,
                slidesToScroll: 1,
                infinite: true
            }
        },
        {
            breakpoint: 600,
            settings: {
                slidesToShow: 2,
                slidesToScroll: 1,
                infinite: true
            }
        },
        {
            breakpoint: 480,
            settings: {
                slidesToShow: 1,
                slidesToScroll: 1,
                infinite: true
            }
        }
    ]
});

$('.featuredScrollLeft').click(function () {
    $('.homeFeaturedCollege').slick('slickPrev');
});

$('.featuredScrollRight').click(function () {
    $('.homeFeaturedCollege').slick('slickNext');
});

$(document).ready(function () {
    function fetchTrendingData(entity, entityId, containerClass, subPage) {
        $.ajax({
            type: "POST",
            url: "/site/get-trending-page-data",
            data: {
                entity: entity,
                entityid: entityId,
                subPage: subPage
            },
            dataType: "json",
            success: function (response) {
                var container = $(containerClass);
                container.empty();

                if (response.length === 0) {
                    container.append('<p>No result found</p>');
                } else {
                    response.forEach(function (item) {
                        // Check if the entity is 'course' to apply the custom border style
                        var style = (entity === 'course') ? 'border: 1px solid #d8d8d8;' : '';
                        var itemHtml = '<div class="dataCard" style="display:flex;justify-content:center;align-items:center;height:150px;flex-shrink:0;' + style + '">' +
                            '<a href="' + item.url + '" class="dataCardText">' +
                            '<p>' + item.display_name + '</p>' +
                            '</a>' +
                            '</div>';
                        container.append(itemHtml);
                    });
                }
            },
            error: function () {
                console.log('Error fetching data.');
            }
        });
    }

    $('.exam-tab').on('click', function () {
        let entityId = $(this).data('entityid');
        let entity = $(this).data('entity');
        let containerMap = {
            'exam': '.trendingExamCardList',
            'course': '.trendingCourseCardList',
            'board': '.trendingBoardCardList'
        };
        let containerName = containerMap[entity];
        $('.exam-tab').removeClass('selected');
        $(this).addClass('selected');
        fetchTrendingData(entity, entityId, containerName, true);
    });
});