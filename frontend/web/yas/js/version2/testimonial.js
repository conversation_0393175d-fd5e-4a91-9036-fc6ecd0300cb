$('#loadmore').click(function () {
    var offset = $(this).attr('data-offset');
    var totalCount = $(this).attr('data-total');
    var newOffset = parseInt(offset) + 3;
    var applicationCategory = $('.gis__testimonialTabBtn.selected').attr('data-tab') ?? '';
    $.ajax({
        type: "POST",
        url: "/immigration/get-testimonial",
        cache: true,
        data: { showHome: true, applicationCategory: applicationCategory, limit: 3, offset: $(this).attr('data-offset') },
        success: function (response) {
            $('#testimonialContentBoxAjaxLoadMore').append(response).show();
            if (newOffset < totalCount) {
                $('#loadmore').attr('data-offset', newOffset);
            }
            else {
                $('#loadmore').hide();
            }
        }
    });
});

// Tab switching logic
$('.gis__testimonialTabBtn').click(function () {
    $('.gis__testimonialTabBtn').removeClass('selected');
    $(this).addClass('selected');
    $.ajax({
        type: "POST",
        url: "/immigration/get-testimonial",
        cache: true,
        data: { showHome: true, applicationCategory: $(this).attr('data-tab'), limit: 3, offset: 0 },
        success: function (response) {
            $('#testimonialContentBoxAjax').html(response);
            $('#testimonialContentBoxAjax').show();
            $('#testimonialContentBox,#testimonialContentBoxAjaxLoadMore').hide();
            $('#testimonialContentBoxAjaxLoadMore').empty();

            // don't move it above this input feild is hidden and comes from ajax thus first need to populate the div with response data then will get this value.

            $('#loadmore').show();
            var testimoialCount = $('#testimonailCount').val();
            if (testimoialCount <= 3) {
                $('#loadmore').hide();
            }
            $('#loadmore').attr('data-offset', 3);
            $('#loadmore').attr('data-total', testimoialCount);
        }
    });
});

// Close button logic
$('.closeIcon').click(function (event) {
    event.stopPropagation();
    var originalCount = $('#oiginalTotal').val();
    $(this).closest('.gis__testimonialTabBtn').removeClass('selected');
    $('#testimonialContentBox,#loadmore').show();
    $('#testimonialContentBoxAjax,#testimonialContentBoxAjaxLoadMore').hide();
    $('#testimonialContentBoxAjaxLoadMore,#testimonialContentBoxAjax').empty();
    $('#loadmore').attr('data-offset', 3);
    $('#loadmore').attr('data-total', originalCount);
});