$("table").wrap("<div class='table-responsive'></div>");
// $('table').removeAttr("border");

$(document).ready(function () {
  // article page faq

  $(".faq_answer").hide();
  $(".faq_question").click(function () {
    $(this).toggleClass("downAngle");
    $(this)
      .next(".faq_answer")
      .slideToggle("slow")
      .siblings(".faq_answer:visible")
      .slideUp("slow");
  });

  //scroll to top
  $(document).scroll(function () {
    var offsetForToTop = $(this).scrollTop();
    if (offsetForToTop > 800) {
      $(".scrollToTop").fadeIn();
      // $(".getSupport").fadeIn();
    } else {
      $(".scrollToTop").fadeOut();
      // $(".getSupport").fadeOut();
    }
  });

  $(document).scroll(function () {

    var offsetForToTop = $(this).scrollTop();
    if (offsetForToTop > 250) {
      $(".getSupport, .shortlist__college.shortlist__college_scroll, .mobile-bottom-cta").fadeIn().css('display', 'flex');
    } else {
      //if (gmu.config.isMobile == 1) {
      $(".getSupport, .shortlist__college.shortlist__college_scroll, .mobile-bottom-cta").fadeOut();
      // }

    }
  });

  $(document).on("click", ".scrollToTop", function () {
    $(window).scrollTop(0);
  });
});

// header scroll js animation
/*const scrollHeader = document.querySelector(".page-header");
const scrollUp = "scroll-up";
const scrollDown = "scroll-down";
let lastScroll = 0;

window.addEventListener("scroll", () => {
  const currentScroll = window.pageYOffset - 60;
  if (currentScroll <= 0) {
    scrollHeader.classList.remove(scrollUp);
    return;
  }

  if (
    currentScroll > lastScroll &&
    !scrollHeader.classList.contains(scrollDown)
  ) {
    // down
    scrollHeader.classList.remove(scrollUp);
    scrollHeader.classList.add(scrollDown);
  } else if (
    currentScroll < lastScroll - 10 &&
    scrollHeader.classList.contains(scrollDown)
  ) {
    // up
    scrollHeader.classList.remove(scrollDown);
    scrollHeader.classList.add(scrollUp);
  }
  lastScroll = currentScroll;
});
*/
//header mega menu strats

// $("body #Pharmacy-blog").css("background", "block");

var mobile_menu = document.querySelector(".slider-menu");
var hamberger_menu = document.querySelector(".hambergerIcon");
// var slide_clearfix = document.querySelector('.slide-top-clearfix');
let mobile_menu_slider = document.querySelector(".mobileMenu");

$(document).ready(function () {
  $("body").on("click", ".hambergerIcon", function () {
    // $("body").addClass("no-scroll");

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    if (gmu.config.prodEvn == 'prod') {
      link.href = 'https://www.getmyuni.com/yas/css/version2/min/mobile-sidemenu.css';
    } else {
      link.href = 'https://www.getmyuni.com/yas/css/version2/mobile-sidemenu.css';
    }
    document.head.appendChild(link);

    var a = $(".slider-menu").css("width", "85%");
    var b = $(".slider-menu").css("display", "block");
    if (a || b) {
      $(".slider-menu").css("width", "85%");
      $(".mobileMenu").css("width", "100%");
      $('body').css('overflow-y', "hidden");
    } else {
      $(".slider-menu").css("width", "0%");
      $(".mobileMenu").css("width", "0%");
      $('body').css('overflow-y', "auto");
    }
  });

  $("body").on("click", ".hambergerIcon", function () {
    $("body").addClass("no-scroll");
    isModalOpenScript = true;
    document.addEventListener('touchmove', preventDefaultScrollScript, { passive: false });
    var a = $(".slider-menu").css("width", "85%");
    var b = $(".slider-menu").css("display", "block")
    if (a || b) {

      $(".slider-menu").css("width", "85%");
      $(".mobileMenu").css("width", "100%");
      $('body').css('overflow-y', "hidden");
    } else {
      $(".slider-menu").css("width", "0%");
      $(".mobileMenu").css("width", "0%");
      $('body').css('overflow-y', "auto");
    }
  });

  $("body").on("click", ".closeMenu", function () {
    isModalOpenScript = false;
    $('.slider-menu', '.mobileMenu').css('width', '0%');
    $('.mobileMenu').css('width', '0%');
    $('body').removeClass("no-scroll");
    $('body').css('overflow-y', "auto");
  });


  $(document.body).on('click', '.sliderCategory', function () {
    $(this).next().css("width", "100%")
  });
  $(document.body).on('click', '.sub-menu-div', function () {
    $(this).parent().css("width", "0%")
  });

  $('.shape-class').click(function () {
    $('.view-course').toggleClass('view-all');
    $('.vactional').toggleClass('view-all');
  });
});

// mobile menu ends here

// advance search starts here

// $(document).ready(function () {
//   $("#navbar-searchboxNNH").click(function () {
//     $(".advanceSearch").css("display", "block");
//   });
//   $(".cancelIcon").click(function () {
//     $(".advanceSearch").css("display", "none");
//   });
// });

// $(document).ready(function () {
//   $("body").on("click", ".tabButtons li", function () {
//     var tabButtons_id = $(this).attr("data-tab");

//     $(".tabButtons li").removeClass("current");
//     $(".tab-content").removeClass("current");

//     $(this).addClass("current");
//     $("#" + tabButtons_id).addClass("current");
//   });
// });

// $(document).ready(function () {
//     $("body").on("click", ".searchIcon", function () {
//         $(".advanceSearch").fadeIn();
//     });
//     $("body").on("click", ".cancelIcon, .close-layer", function () {
//         $(".advanceSearch").fadeOut();
//     });
// });

// exam filter page

$(document).ready(function () {

  // $(".examFilterSection form, .filterSearch").addClass("tglClass");
  // $(".examFilterSection p").click(function () {
  //   $(this)
  //     .next(".examFilterSection .filterByTypeDiv, .filterSearch, .filterToggle")
  //     .slideToggle("slow")
  //     .toggleClass("tglClass");
  //   $(this).toggleClass("down_angle");
  // });

  $(".dot_icon").click(function () {
    $(".moreCategoryOptions").slideToggle();
  });
});

$("body").on('click', ".mobileFilter, .cafFilterOption", function () {
  $(".mobileFilterSection").fadeIn();
});

$("body").on('click', ".mobileSort", function () {
  $(".mobileSortSection").fadeIn();
});

$("body .close_sortPopup").click(function () {
  $(".mobileSortSection").fadeOut();
});

$("body").on('click', ".closeFilter, .applyFilter", function (e) {
  e.preventDefault();
  $(".mobileFilterSection").fadeOut();
});

// e-course tabber script starts here
$(document).ready(function () {
  $('body').on('click', 'ul.tabs li', function () {
    var tab_id = $(this).attr("data-tab");

    $("ul.tabs li").removeClass("current");
    $(".tab-content").removeClass("current");

    $(this).addClass("current");
    $("#" + tab_id).addClass("current");
  });
});


// get scholarship css here
// if ($(".getFreeScholorship").offset()) {
//   $(document).ready(function () {
//     var stickyschlrshipBtn = $(".getFreeScholorship").offset().top - 56;
//     $(window).scroll(function () {
//       var sticky = $(".getFreeScholorship");
//       var scroll = $(window).scrollTop();

//       if (scroll >= stickyschlrshipBtn) {
//         sticky.addClass("fixedScholarshipBtn");
//       } else {
//         sticky.removeClass("fixedScholarshipBtn");
//       }
//     });
//   });
// }

// quick links sticky
/*if (window.screen.width > 1023) {
   if ($('.quickLinks').offset() || $(".sidebarAds").offset()) {
       $(document).ready(function () {
           var stickyQuickLinks = $('.quickLinks, .sidebarAds').offset().top - 130;
           $(window).scroll(function () {
               var examInfoHeight = $(".examInfo").height();
               var quickLinks = $('.quickLinks');
               var sideBar = $(".sidebarAds")
               var scroll = $(window).scrollTop();

               if (scroll >= stickyQuickLinks && examInfoHeight > 1520) {
                   quickLinks.addClass('fixedQuickLinksDiv');
                   sideBar.addClass('fixedQuickLinksDiv');

               }
               else {
                   quickLinks.removeClass('fixedQuickLinksDiv');
                   sideBar.removeClass('fixedQuickLinksDiv');

               }
           });

       });
   }

}
if (window.screen.width > 1023) {
  if ($(".quickLinks").offset() || $(".sidebarAds").offset()) {
    $(document).ready(function () {
      var pageInfoHeight = 0;
      $(".pageData").each(function () {
        pageInfoHeight += $(this).height();
      });

      var stickyQuickLinks = $(".quickLinks, .sidebarAds").offset().top - 60;
      $(window).scroll(function () {
        var examInfoHeight = $(".examInfo").height();
        // var pageInfoHeight = $(".pageInfo").height();

        var quickLinks = $(".quickLinks");
        var sideBar = $(".sidebarAds");
        var scroll = $(window).scrollTop();

        if (
          (scroll >= stickyQuickLinks && examInfoHeight > 1520) ||
          (scroll >= stickyQuickLinks && pageInfoHeight > 1200)
        ) {
          quickLinks.addClass("fixedQuickLinksDiv");
          sideBar.addClass("fixedQuickLinksDiv");
        } else {
          quickLinks.removeClass("fixedQuickLinksDiv");
          sideBar.removeClass("fixedQuickLinksDiv");
          $(".sidebarAds").css("margin-top", "0px");
        }
      });
    });
  }
} */
$(document).ready(function () {
  var scrollToContent = $(".examInfo ul li a");
  scrollToContent.click(function (e) {
    if ($(this.hash).offset().top == undefined) {
      e.preventDefault();
      $("body,html").animate(
        {
          scrollTop: $(this.hash).offset().top - 60,
        },
        100
      );
    }
  });
});

/* if (window.screen.width > 1023) {
  if ($(".quickLinks").offset() || $(".sidebarAds").offset()) {
    function stickyeQuicklinks() {
      var removeSticky = $(".removeFixedQuickLink").position() !== undefined ? $(".removeFixedQuickLink").position().top : '';
      var scrollingtop = $(document).scrollTop() + 330;
      var diff = scrollingtop - removeSticky;
      if (scrollingtop > removeSticky) {
        $(".fixedQuickLinksDiv").css("margin-top", 20 - diff);
      } else {
        // $('.fixedQuickLinksDiv').removeClass('fixedQuickLinksDiv');
        $(".fixedQuickLinksDiv").css("margin-top", "0px");
      }
    }
    window.onscroll = function () {
      stickyeQuicklinks();
    };
  }
} */

// scroll nav bar
$(document).ready(function () {
  $(".btn_left").click(function () {
    $(
      ".examRelataedLinks ul, .articleRelataedLinks ul, .collegeRelataedLinks ul, .pageRedirectionLinks ul"
    ).animate(
      {
        scrollLeft: "-=150px",
      },
      500
    );
  });
  $(".btn_right").click(function () {
    $(
      ".examRelataedLinks ul, .articleRelataedLinks ul, .collegeRelataedLinks ul, .pageRedirectionLinks ul"
    ).animate(
      {
        scrollLeft: "+=150px",
      },
      500
    );
  });

  // $("body .navigation_header_dropdown").prepend("<p class='greyBg'></p>");

  // $("body .navigation_header_dropdown a").mouseenter(function () {
  //     $("p.greyBg").css('display', 'block');
  //     $("body").addClass("no-scroll");
  // });

  // $("body p.greyBg, .topHeader, .headerMegaMenu ul li:first-child a").mouseenter(function () {
  //     $("p.greyBg").css('display', 'none');
  //     $("body").removeClass("no-scroll");

  // });

  // $("#Paramedical a, #resource-dropdown ul li:first-child a").mouseenter(
  //   function () {
  //     $("p.greyBg").css("display", "block");
  //   }
  // );
});

// scroll bar sticky

// if ($(".examRelataedLinks").offset()) {
//   $(document).ready(function () {
//     var stickyExamRelatedDiv = $(".examRelataedLinks").offset().top - 0;
//     var examLinkksheight = $(".examRelataedLinks").outerHeight();

//     $(window).scroll(function () {
//       var examRelataedLinks = $(".examRelataedLinks");
//       var scroll = $(window).scrollTop();

//       if (scroll >= stickyExamRelatedDiv) {
//         examRelataedLinks.addClass("fixedExamRelatedDiv");
//         $("body").css("margin-top", examLinkksheight + 18);
//         if (window.screen.width <= 1023) {
//           $("body").css("margin-top", examLinkksheight + 10);
//         }
//       } else {
//         $("body").css("margin-top", 0);
//         examRelataedLinks.removeClass("fixedExamRelatedDiv");
//       }
//     });
//   });
// }

// scroll upcoming exam
if (window.screen.width > 1023) {
  $(document).ready(function () {
    $(".upcomigExamDiv .scrollLeft").click(function () {
      $(".upcomigExamList").animate(
        {
          scrollLeft: "-=341px",
        },
        750
      );
    });
    $(".upcomigExamDiv .scrollRight").click(function () {
      $(".upcomigExamList").animate(
        {
          scrollLeft: "+=341px",
        },
        750
      );
      $(".scrollLeft").removeClass("over");
    });
  });
}

if (window.screen.width > 1023) {
  $(document).on('click', '.examInfoSlider .scrollLeft', function () {
    console.log('Scroll Left Clicked');
    $(this).next().next().animate({ scrollLeft: "-=411px" }, 750);
  });

  $(document).on('click', '.examInfoSlider .scrollRight', function () {
    $(this).next().animate({ scrollLeft: "+=411px" }, 750);
    $(this).prev().removeClass("over");
  });

}

// $(".writeAnswer").click(function () {
//     $(".writeAnswerForm").fadeIn();
// });

$(document).on("keydown", function (event) {
  if (event.key == "Escape") {
    document.querySelector('#lead-form-js-new').style.display = "none";
    $(".writeAnswerForm").fadeOut();
    $("body").removeClass("no-scroll");
  }
});

// key up function
$(document.body).ready(function () {
  $("body").on(
    "keyup",
    ".filterByDegree input, .filterSearch input, .filterSearchValue input",
    function () {
      var filterOption = $(this).val().toLowerCase();
      // $(".filterByDegree ul li, .filterSearch ul li").filter(function () {
      $(this)
        .parent()
        .parent()
        .find("ul li")
        .filter(function () {
          $(this).toggle(
            $(this).text().toLowerCase().indexOf(filterOption) > -1
          );
        });
    }
  );
});

// checked and unchecked

$('input[type="checkbox"]').click(function () {
  if ($(this).is(":checked")) {
    $(this).attr("checked", true);
  } else {
    $(this).attr("checked", false);
  }
});

// custom select dropdown

$(".sortByOption ul").on("click", ".init", function () {
  $(this).closest("ul").children("li:not(.init)").toggle();
});
$(".sortByOption ul").click(function () {
  $(this).toggleClass("rotateAngle");
});

var allOptions = $(".sortByOption ul").children("li:not(.init)");
$(".sortByOption ul").on("click", "li:not(.init)", function () {
  allOptions.removeClass("sortBy");
  $(this).addClass("sortBy");
  $(".sortByOption ul").children(".init").html($(this).html());
  allOptions.toggle();
});

// tabs for the artcle landing
$(document).ready(function () {
  $(".articleRelataedLinks ul li a, .articleTypes ul li a").click(function () {
    var tab_id = $(this).attr("data-tab");

    $(".articleRelataedLinks ul li a, .articleTypes ul li a").removeClass(
      "activeLink"
    );
    $(".tab-content").removeClass("activeLink");

    $(this).addClass("activeLink");
    $("#" + tab_id).addClass("activeLink");
  });
});

$(".register").click(function (e) {
  e.preventDefault();
  $(".cafLoginFormContainer").addClass("opened");
});
$(".formCloseBtn").click(function (e) {
  e.preventDefault();
  $(".cafLoginFormContainer").removeClass("opened");
});

// caf numerinputs div

$(document).ready(function () {
  $(".numberInputs")
    .find("input")
    .each(function () {
      $(this).attr("maxlength", 1);
      $(this).on("keyup", function (e) {
        var parent = $($(this).parent());

        if (e.keyCode === 8 || e.keyCode === 37) {
          var prev = parent.find("input#" + $(this).data("previous"));

          if (prev.length) {
            $(prev).select();
          }
        } else if (
          (e.keyCode >= 48 && e.keyCode <= 57) ||
          (e.keyCode >= 65 && e.keyCode <= 90) ||
          (e.keyCode >= 96 && e.keyCode <= 105) ||
          e.keyCode === 39
        ) {
          var next = parent.find("input#" + $(this).data("next"));

          if (next.length) {
            $(next).select();
          } else {
            if (parent.data("autosubmit")) {
              parent.submit();
            }
          }
        }
      });
    });
});

// $('body').on('click', '.closeLeadForm', function () {
//   $('#lead-form').trigger("reset");
//   $('.help-block').html('');
//   $('.headingText').empty();
//   $(".leadFormContainer, .writeAnswerForm").fadeOut();
//   $("body").removeClass("no-scroll");

// });

// $(".getLeadForm").click(function () {
//   $(".leadFormContainer").fadeIn();
//   $("body").addClass("no-scroll");
// });

$("#selectCategory").select2({
  placeholder: "Select course category",
  allowClear: true,
});

/** lead-form js ends -janavi */

$(document).ready(function () {
  $(
    ".upcomigExamList, .examRelataedLinks ul, .articleRelataedLinks ul, .collegeRelataedLinks ul, .pageRedirectionLinks ul").each(function () {

      var $this = $(this);

      function updateScrollButtons() {
        var $width = $this.outerWidth();
        var $scrollWidth = $this[0].scrollWidth;
        var $scrollLeft = $this.scrollLeft();

        // Hide scroll buttons if no scrolling is required
        if ($scrollWidth <= $width) {
          $(".scrollLeft, .btn_left, .scrollRight, .btn_right").addClass("over");
          return;
        }

        // Show/hide right scroll button
        if (Math.ceil($scrollWidth - $width) === Math.ceil($scrollLeft)) {
          $(".scrollRight, .btn_right").addClass("over");
        } else {
          $(".scrollRight, .btn_right").removeClass("over");
        }

        // Show/hide left scroll button
        if ($scrollLeft === 0) {
          $(".scrollLeft, .btn_left").addClass("over");
        } else {
          $(".scrollLeft, .btn_left").removeClass("over");
        }
      }

      // Initial check on page load
      updateScrollButtons();

      // Attach scroll event
      $this.scroll(updateScrollButtons);

      // Attach resize event in case window resizes
      $(window).resize(updateScrollButtons);
    });

  $(
    ".examInfoSlider .row, .liveApllicationFormsInner .row, .customSliderCards, .collegeRelataedLinks, .pageRedirectionLinks ul, .latestInfoList, .topSearchDiv ul, .otherCategorySection .row, .gis__infoList"
  ).scroll(function () {
    var $examInfoSliderWidth = $(this).outerWidth();
    var $examInfoSliderScrollWidth = $(this)[0].scrollWidth;
    var $examInfoSliderScrollLeft = $(this).scrollLeft();
    if (
      parseInt($examInfoSliderScrollWidth - $examInfoSliderWidth) ===
      parseInt($examInfoSliderScrollLeft)
    ) {
      $(this).prev().addClass("over");
    } else {
      $(this).prev().removeClass("over");
    }

    if ($examInfoSliderScrollLeft === 0) {
      $(this).prev().prev().addClass("over");
    } else {
      $(this).prev().prev().removeClass("over");
    }
  });

  //automatically move otp to next input field
  $("body").on("keyup", ".nextInput", function () {
    if (this.value.length == this.maxLength) {
      $(this).next(".nextInput").select();
    }
  });
});
// $(".examRelataedLinks ul li, .pageRedirectionLinks ul li").each(function (
//   i,
//   v
// ) {
//   if ($(this).find("span").text() == "Overview") {
//     return false;
//   }
//   if (i === 0) {
//     $(this).after($(this).parent().find("span").parent());
//   }
// });

//college page
$(".collegeRelataedLinks ul li").each(function (
  i,
  v
) {
  if ($(this).find("span").text() == "Info") {
    return false;
  }
  if (i === 0) {
    $(this).after($(this).parent().find("span").parent());
  }
});

// Article pages js
$(document).ready(function () {
  function handleTabClick(section) {
    $("." + section + " ul li").click(function () {
      var tabId = $(this).attr("data-tab");

      $("." + section + " ul li").removeClass("activeLink");
      $("." + section + " .tab-content").removeClass("activeLink");

      $(this).addClass("activeLink");
      $("#" + tabId).addClass("activeLink");
    });
  }

  handleTabClick("articleSidebarSection.bottomWidget");
  handleTabClick("articleSidebarSection.sideBarWidget");
});


$(document).ready(function () {
  $(".newsSidebarSection ul li").click(function () {
    var tabId = $(this).attr("data-tab");

    $(".newsSidebarSection ul li").removeClass("activeLink");
    $(".newsSidebarSection .tab-content").removeClass("activeLink");

    $(this).addClass("activeLink");
    $("#" + tabId).addClass("activeLink");
  });
});

var articlesArr = ["article1", "article2", "article3", "article4", "article5"];
$(document.body).on("mouseover", ".articleList ul li", function () {
  $.each(articlesArr, function (i, val) {
    $("." + val + "-view").addClass("display_none");
    $("." + val + "-view").removeAttr("style");
    $("." + val + " a").removeClass("fontBold");
    // $("." + val).removeClass("hoverbg");
  });
  var className = $(this).attr("class");
  $("." + className + "-view").removeClass("display_none");
  // $("." + className).addClass("hoverbg");
});

$(".articleInfo button").parent("a").css("text-decoration", "none");
$(".examCardBtn").each(function () {
  if ($(this).children().length == 0) {

    $(this).parent().css({ "padding-top": "0" });
    $(this).parent().prev().css({ "border-bottom": "0px" });
  }
});

//added adevertise title
// $(".appendAdDiv").parent().prepend($("<div class='advertise'>-------- Advertisement --------</div>"));
$(document).ready(function () {
  $("body").on("click", ".topSearchDiv .scrollLeft", function () {
    $(this).next().next().animate(
      {
        scrollLeft: "-=140px",
      },
      850
    );
  });
  $("body").on("click", ".topSearchDiv .scrollRight", function () {
    $(this).next().animate(
      {
        scrollLeft: "+=140px",
      },
      850
    );
    $(this).next().next().removeClass("over");
  });
});

// HelloBar Js
// $(document).ready(function () {
//   setTimeout(function () {
//     $(".siq_bR").attr("id", "siq_bR");
//   }, 1000);
// });

///College Script start here
$(document).ready(function () {
  $(".moreFilterText").next().hide();
  $(".moreFilterText").click(function () {
    $(this).hide();
    $(this).next().show();
  });

  $(".courseFeeFilderDiv ul li").click(function () {
    $(this).toggleClass("selected");
  });

  // $(".foundesults").hide();
  // $(".filterCategory.selectedFilters").hide();
  //$(".redirectToLink").after('<a class="redirectionUrl" href="javscript:;">Read More <span class="spriteIcon urlIcon"></span></a>');
  //$(".redirectionUrl").wrap("<div class='redirectPage'></div>");
  $(".redirectToLink").css("margin-bottom", "0px");

  if (window.screen.width > 1023) {
    $(document).ready(function () {
      $(".two-cardDisplay .scrollLeft").click(function () {
        $(this).next().next().animate(
          {
            scrollLeft: "-=390px",
          },
          750
        );
      });
      $(".two-cardDisplay .scrollRight").click(function () {
        $(this).next().animate(
          {
            scrollLeft: "+=390px",
          },
          750
        );

        $(this).prev().removeClass("over");
      });

      $(".four-cardDisplay .scrollLeft").click(function () {
        $(this).next().next().animate(
          {
            scrollLeft: "-=297px",
          },
          750
        );
      });
      $(".four-cardDisplay .scrollRight").click(function () {
        $(this).next().animate(
          {
            scrollLeft: "+=297px",
          },
          750
        );

        $(this).prev().removeClass("over");
      });

      $('.three-cardDisplay .scrollLeft').click(function () {
        $(this).next().next().animate({
          scrollLeft: '-=285px'
        }, 750);

      });
      $('.three-cardDisplay .scrollRight').click(function () {
        $(this).next().animate({
          scrollLeft: '+=285px'
        }, 750);

        $(this).prev().removeClass("over");
      });

      $(".custom-cardDisplay .scrollLeft").click(function () {
        $(this).next().next().animate(
          {
            scrollLeft: "-=285px",
          },
          750
        );
      });
      $(".custom-cardDisplay .scrollRight").click(function () {
        $(this).next().animate(
          {
            scrollLeft: "+=285px",
          },
          750
        );

        $(this).prev().removeClass("over");
      });

      $(".otherCategorySection .scrollLeft").click(function () {
        $(this).next().next().animate(
          {
            scrollLeft: "-=165px",
          },
          750
        );
      });
      $(".otherCategorySection .scrollRight").click(function () {
        $(this).next().animate(
          {
            scrollLeft: "+=165px",
          },
          750
        );

        $(this).prev().removeClass("over");
      });
    });
  }

  // if (
  //   $(".collegeRelataedLinks").offset() ||
  //   $(".mobileOnly.brochureBtn").offset() ||
  //   $(".pageRedirectionLinks").offset() ||
  //   $(".examRelataedLinks").offset()
  // ) {
  // $(document).ready(function () {
  //   var stickyTopBar =
  //     $(
  //       ".collegeRelataedLinks, .pageRedirectionLinks, .examRelataedLinks, .reviewContainer, .review"
  //     ).offset().top - 0;

  //   $(window).scroll(function () {
  //     var collegeRelataedLinks = $(".collegeRelataedLinks");
  //     var pageRedirectionLinks = $(".pageRedirectionLinks");
  //     var scroll = $(window).scrollTop();
  //     var fixedBarHeight = $(
  //       ".collegeRelataedLinks, .pageRedirectionLinks"
  //     ).outerHeight();

  //     if (scroll >= stickyTopBar) {
  //       collegeRelataedLinks.addClass("fixedExamRelatedDiv");
  //       $("body").css("margin-top", fixedBarHeight + 20);
  //       if (window.screen.width <= 1023) {
  //         $("body").css("margin-top", fixedBarHeight + 10);
  //       }
  //       pageRedirectionLinks.addClass("fixedRedirectionLinks");
  //     } else {
  //       $("body").css("margin-top", "0px");
  //       collegeRelataedLinks.removeClass("fixedExamRelatedDiv");
  //       pageRedirectionLinks.removeClass("fixedRedirectionLinks");
  //     }
  //   });
  // });
  // }

  // $(".foundesults").hide();
  // $(".filterCategory.selectedFilters").hide();
  //$(".redirectToLink").after('<a class="redirectionUrl" href="javscript:;">Read More <span class="spriteIcon urlIcon"></span></a>');
  //$(".redirectionUrl").wrap("<div class='redirectPage'></div>");
  $(".redirectToLink").css("margin-bottom", "0px");
  $(".redirectToLink.facilitySection").css("margin-bottom", "20px");

  // if ($(".mobileOnly.brochureBtn").offset()) {
  //   $(document).ready(function () {
  //     var stickybrochureBtn = $(".mobileOnly.brochureBtn").offset().top - 48;
  //     $(window).scroll(function () {
  //       var sticky = $(".brochureBtn");
  //       var scroll = $(window).scrollTop();
  //       var fixedBarHeight = $(
  //         ".collegeRelataedLinks, .pageRedirectionLinks, .examRelataedLinks"
  //       ).outerHeight();
  //       var brochureBtnHeight = $(".mobileOnly.brochureBtn").outerHeight();

  //       if (window.screen.width <= 1023) {
  //         if (scroll >= stickybrochureBtn) {
  //           sticky.addClass("fixedbrochureBtn");
  //           $("body").css(
  //             "margin-top",
  //             fixedBarHeight + brochureBtnHeight + 20
  //           );
  //         } else {
  //           sticky.removeClass("fixedbrochureBtn");
  //         }
  //       }
  //     });
  //   });
  // }

  // $(".filterCategoryName").click(function () {
  //     $(this).next("").slideToggle("").toggleClass("tglClass");
  //     $(this).toggleClass("down_angle");
  // });

  // if ($(".gallerypage")) {
  if (jQuery().fancybox) {
    $('[data-fancybox="gallery"]').fancybox({
      buttons: ["slideShow", "thumbs", "zoom", "fullScreen", "share", "close"],
      loop: false,
      protect: true,
    });
  }

  $(".answerText").each(function () {
    var answerHeight = $(this).height();
    if (window.screen.width > 1023) {
      if (answerHeight > 96) {
        $(this).addClass("answerHeight");
        $(this).after(
          '<a href="javascript:void(0);" class="readMore">Read More</a>'
        );
      }
    } else {
      if (answerHeight > 144) {
        $(this).addClass("answerHeight");
        $(this).after(
          '<a href="javascript:void(0);" class="readMore">Read More</a>'
        );
      }
    }
  });
  $(".readMore").click(function () {
    $(this).prev(".answerText").removeClass("answerHeight");
    $(this).remove();
  });

  // $(".getPopup").click(function () {
  //     $(".popupContainer").fadeIn();
  // });

  // $(".popupHeading .closeForm").click(function () {
  //     $(".popupContainer").fadeOut();
  // });
});

//tab section data course category page
$(document).ready(function () {
  $(".viewAllStream").click(function () {
    $(".hideStreamCard").show();
    $(".col-12.p-0").hide();
  });

  $(".courseCategoryCard.hide").hide();
  $("#viewCourses").click(function () {
    $(".courseCategoryCard.hide").show();
    $(".col-12.p-0").hide();
  });

  $("body").on("click", ".tabList li", function () {
    var tabButtons_id = $(this).attr("data-tab");
    $(".tabList li").removeClass("activeLink");
    $(".courseCategoryCard").css("display", "none");

    $(this).addClass("activeLink");
    if (tabButtons_id == "all") {
      $(".courseCategoryCard").css("display", "block");
      $(".courseCategoryCard.hide").show();
      $(".col-12.p-0").hide();
    } else {
      $(".courseCategoryCard." + tabButtons_id + "_active").css(
        "display",
        "block"
      );
      $(".courseCategoryCard." + tabButtons_id + "_active.hide").css(
        "display",
        "block"
      );
      $(".col-12.p-0").hide();
    }
    $(".removeCta").remove();
    loadCta('course-category');
  });
});

//write a review starts
// $(document).ready(function () {
//   //validations and  form functionalities starts
//   $('.submit-review-form, .submit-review-step-next, .previousBtn, .submitReviewForm, .verifyOtp').click(function () {
//     $('html, body').animate({ scrollTop: 0 }, 200);
//   });

//   if (window.location.href.includes("/review/create")) {
//     $("#lead-form-js").remove();
//     $("#login-form-js").remove();
//     $("#secondaryHeader-web").remove();
//     window.onbeforeunload = function () {
//       return "Leave this page ?";
//     }
//   }

//   //find if the url containd fbclid id and replace with the original url
//   //This is used only if the user navigate from the fb app
//   var baseUrl = window.location.href;
//   var url = baseUrl.includes("fbclid");

//   if (url === true) {
//     var stripfbclis = baseUrl.indexOf("?");
//     var value = baseUrl.substring(stripfbclis);
//     var replaceString = baseUrl.replace(value, "");
//     window.history.pushState(baseUrl, "", replaceString);
//   }

//   //count textarea words
//   function countWords(s) {
//     s = s.replace(/(^\s*)|(\s*$)/gi, "");
//     s = s.replace(/\n /, "\n");
//     return s.split(" ").length;
//   }

//   //diable/enable radio buttons
//   $("input[type=radio]").click(function (e) {
//     var getClass = $(this).attr("id");
//     if ($(this).val() == "yes") {
//       $("." + getClass).prop("readonly", false);
//       $("." + getClass).css("pointer-events", "");
//       $("." + getClass).prop('required', true);
//       $("." + getClass).val("");
//       if (getClass == "yes3") {
//         $(".yes3").val("Yes");
//         $(".yes3" + "+div").html("");
//       }
//       if (getClass == "yes1") {
//         $('.field-exam_id' + ' .help-block').html("");
//         $(".field-exam_id .selection").css("pointer-events", "auto");
//         $("#select2-exam_id-container .select2-selection__placeholder").attr('style', 'color: #282828!important');
//         $(".field-exam_id .select2, .field-exam_id .select2-selection").attr('style', 'background: ""');
//       }
//       if (getClass == "yes2") {
//         $(".yes2" + "+div").html("");
//         $(".yes2").attr('style', 'color: #282828!important');
//         $(".yes2").attr('style', 'background: ""');
//       }
//       if (getClass == 'yes5' || getClass == 'yes6' || getClass == 'yes7') {
//         $("." + getClass + "+div").html("");
//         $("." + getClass).attr('style', 'background: ""');
//       }
//     } else {
//       var text = getClass;
//       var replaceId = text.replace("no", "yes");
//       $("." + replaceId).prop("readonly", true);
//       $("." + replaceId).prop('required', false);
//       if (replaceId == 'yes1') {
//         $('.field-exam_id' + ' .help-block').html("");
//         $("#exam_id").val('').trigger('change');
//         $("#select2-exam_id-container .select2-selection__placeholder").attr('style', 'color: #989898!important');
//         $(".field-exam_id .selection").css("pointer-events", "none");
//         $(".field-exam_id .select2, .field-exam_id .select2-selection").attr('style', 'background : #d8d8d8!important');
//       }
//       if (replaceId == 'yes2') {
//         $(".yes2" + "+div").html("");
//         $(".yes2").val("");
//         $(".yes2").css({ "pointer-events": "none", "color": "#989898!important", "font-size": "14px", "background": "#d8d8d8" });
//       }
//       if (replaceId == 'yes5' || replaceId == 'yes6' || replaceId == 'yes7') {
//         $("." + replaceId + "+div").html("");
//         $("." + replaceId).val("");
//         $("." + replaceId).css({ "pointer-events": "none", "background": "#d8d8d8" });
//       }
//       if (replaceId == "yes3") {
//         $(".yes3" + "+div").html("");
//         $(".yes3").val("No");
//       }
//     }
//   });

//   //textarea, input field validations
//   var maxchars = 500;
//   $("textarea").keyup(function () {
//     var tlength = $(this).val().length;
//     $(this).val($(this).val().substring(0, maxchars));
//     var tlength = $(this).val().length;
//     remain = maxchars - parseInt(tlength);
//     $(".remain").text(remain);
//     $("body").find('.category-review-content').html('');
//   });

//   $("body").on("click", ".ratingSectionList", function () {
//     $("body").find('.rating-review-content').html('');
//   });

//   $('.yes4').keyup(function (e) {
//     $('.yes4 + div').html("");
//     var input = parseInt(this.value);
//     if (input < 0 || input > 200) {
//       $('.yes4 + div').html("Class Size cannot be greater than 200");
//       $(".yes4").val("");
//     }
//   });

//   $('.ReviewOtpInputs, .reviewFormDiv').keypress(function (e) {
//     if (e.which == 13 && e.target.nodeName != "TEXTAREA") return false;
//   });

//   $('.yes4, #review-college_fees, #student-phone, .yes5, .yes6, .yes7').keypress(function (e) {
//     var charCode = (e.which) ? e.which : e.keyCode
//     if (String.fromCharCode(charCode).match(/[^0-9]/g)) {
//       return false;
//     }
//   });

//   $("#student-name").keypress(function (e) {
//     var key = e.keyCode;
//     if (key >= 48 && key <= 57) {
//       e.preventDefault();
//     }
//   });

//   $("textarea").on("cut copy paste", function (e) {
//     e.preventDefault();
//   });

//   $("body").on("change", "#review-admission_year", function (event) {
//     if ($(this).val() == '') {
//       $(this).attr('style', 'color: #989898!important');
//     } else {
//       $(this).attr('style', 'color: black!important');
//     }
//   });
//   //textarea, input field validations ends

//   //validations and  form functionalities starts
//   //user reviews saving functionality
//   var nextBtn = $(".submit-review-step-next");
//   prevBtn = $(".previousBtn");
//   function getReviewSteps(reviewId, studentId) {
//     var index = $(".step.active").attr("data-index");
//     var oldLocation = window.location.href;
//     var url = window.location.origin;
//     if (oldLocation.includes(reviewId) == false) {
//       window.history.pushState(oldLocation, "", url + "/review/create/" + reviewId);
//     }

//     var success = 0;
//     var questionId = {
//       1: {
//         "errorMessage": "Please select an option",
//         "yesErrorMessage": "Select Exam cannot be blank"
//       },
//       2: {
//         "errorMessage": "Please select an option",
//         "yesErrorMessage": "Select option cannot be blank"
//       },
//       5: {
//         "errorMessage": "Please select an option",
//         "yesErrorMessage": "  This field cannot be blank"
//       },
//       6: {
//         "errorMessage": "Please select an option",
//         "yesErrorMessage": "  This field cannot be blank"
//       },
//       7: {
//         "errorMessage": "Please select an option",
//         "yesErrorMessage": "  This field cannot be blank"
//       },
//     };

//     $.each(questionId, function (i, value) {
//       if ($("input[name=question" + i + "]").is(":checked") == false) {
//         if ($(".yes" + i).val() == '') {
//           success++;
//           if (i == 1) {
//             $('.field-exam_id' + ' .help-block').html(value['errorMessage']);
//           } else {
//             $(".yes" + i + "+div").html(value['errorMessage']);
//           }
//         }
//       } else if ($("input[name=question" + i + "]").is(":checked") == true && $("input[name=question" + i + "]:checked").val() == 'yes') {
//         if ($(".yes" + i).val() == '') {
//           success = 2;
//           if (i == 1) {
//             $('.field-exam_id' + ' .help-block').html(value['yesErrorMessage']);
//           } else {
//             $(".yes" + i + "+div").html(value['yesErrorMessage']);
//           }
//         }
//       }
//     });
//     if ($(".yes4").val() == '' || $(".yes4").val() == null) {
//       success = 1;
//       $('.yes4 + div').html("Class Size cannot be blank");
//     }
//     if (success == 0) {
//       if (index < stepsCount) {
//         $(".step").removeClass("active").eq(index).addClass("active");
//         index++;
//         stepCount.textContent = index;
//         stepPercent.textContent = `${(index - 1) * Math.ceil(100 / stepsCount)}%`;
//         progressBarLine.style.width = `${(index - 1) * Math.ceil(100 / stepsCount)}%`;
//       }
//       if (index === stepsCount) {
//         $(this).prop("disabled", true);
//       }
//     }
//   }
//   $(prevBtn).on("click", function () {
//     var index = $(".step.active").attr("data-index");
//     if (index > 0) {
//       $(".step").removeClass("active");
//       index--;
//       $('div[data-index = ' + index + ']').addClass("active");
//       stepCount.textContent = index;
//       stepPercent.textContent = `${(index - 1) * Math.ceil(100 / stepsCount)}%`;
//       progressBarLine.style.width = `${(index - 1) * Math.ceil(100 / stepsCount)}%`;
//     }
//     if (index === 0) {
//       $(this).css("disabled", true);
//     }
//   });

//   $('.titleValue').keypress(function (e) {
//     $('.title' + ' .help-block').html("");
//   });
//   $(nextBtn).on("click", function (event) {
//     event.preventDefault();
//     var dataIndex = $(".step.active").attr("data-index");
//     var rating = $(".ratingSection" + dataIndex).attr("data-current");
//     var categoryAnswer = $(".step.active").find('textarea[name="step1"]').val();
//     var reviewContent = categoryAnswer !== undefined ? categoryAnswer.replace(/(<([^>]+)>)/gi, "") : "";
//     var arrayOfFake = reviewContent.match(/(.)\1{15,}/);
//     $reviewId = window.location.href.split("/");
//     $reviewCategoryId = $(".step.active").find("input:hidden[name=review-category-id]").val();
//     title = $(".step.active").find("input:text[name=title]").val();

//     if (rating == undefined && reviewContent.length == 0 && (title == '' || title == null)) {
//       $('.title' + ' .help-block').html("Title of your review cannot be blank");
//       $('.category-review-content').html("A review cannot be left blank. Please answer the questions above and make it useful for the reader.");
//       $('.rating-review-content').html("Please add a rating to support this review.");
//       return false;
//     }

//     // if ($('.mobileOnly.brochureBtn').offset()) {
//     //     $(document).ready(function () {
//     //         var stickybrochureBtn = $('.mobileOnly.brochureBtn').offset().top - (48);
//     //         $(window).scroll(function () {
//     //             var sticky = $('.brochureBtn');
//     //             var scroll = $(window).scrollTop();
//     //             var fixedBarHeight = $(".collegeRelataedLinks, .pageRedirectionLinks, .examRelataedLinks").outerHeight();
//     //             var brochureBtnHeight = $(".mobileOnly.brochureBtn").outerHeight();

//     //             if (window.screen.width <= 1023) {
//     //                 if (scroll >= stickybrochureBtn) {
//     //                     sticky.addClass('fixedbrochureBtn');
//     //                     $('body').css("margin-top", fixedBarHeight + brochureBtnHeight + 20);
//     //                 }
//     //                 else {
//     //                     sticky.removeClass('fixedbrochureBtn');
//     //                 }
//     //             }
//     //         });
//     //     });
//     // }
//     if (rating == undefined && reviewContent.length == 0) {
//       $('.category-review-content').html("A review cannot be left blank. Please answer the questions above and make it useful for the reader.");
//       $('.rating-review-content').html("Please add a rating to support this review.");
//       return false;
//     }

//     if (reviewContent.length > 0 && reviewContent.length < 100 && rating == undefined) {
//       $('.category-review-content').html("This review is too short to be helpful (" + (100 - parseInt(reviewContent.length)).toString() + " characters remaining). Please see the questions above for more ideas on what to write.");
//       $('.rating-review-content').html("Please add a rating to support this review.");
//       return false;
//     }

//     if (categoryAnswer !== undefined && reviewContent.length == 0) {
//       $('.category-review-content').html("A review cannot be left blank. Please answer the questions above and make it useful for the reader.");
//       return false;
//     }
//     if (arrayOfFake != null && arrayOfFake["length"] > 0) {
//       $('.category-review-content').html("The text below doesn't make much sense. Can you correct this ?");
//       return false;
//     }
//     if (reviewContent.length > 0 && reviewContent.length < 100) {
//       $('.category-review-content').html("This review is too short to be helpful (" + (100 - parseInt(reviewContent.length)).toString() + " characters remaining). Please see the questions above for more ideas on what to write.");
//       return false;
//     }
//     if (reviewContent.length > 0 && rating == undefined) {
//       $('.rating-review-content').html("Please add a rating to support this review.");
//       return false;
//     }
//     if (reviewContent.length > 0 && countWords(reviewContent) < 6) {
//       $('.category-review-content').html("This review is too short to be helpful. Please see the questions above for more ideas on what to write.");
//       return false;
//     }
//     if ($(".step.active").find("input:text[name=title]").length == 1) {
//       if (title == '' || title == null) {
//         $('.title' + ' .help-block').html("Title of your review cannot be blank");
//         return false;
//       }
//     }

//     $.ajax({
//       type: "POST",
//       url: "/writereview/store-content",
//       data: {
//         steps: dataIndex,
//         content: categoryAnswer,
//         reviewId: $reviewId['5'] ?? '',
//         reviewCatId: $reviewCategoryId,
//         reviewTitle: title,
//         ratingValue: rating,
//         // referralcode: $referralcode ?? '',
//       },
//       dataType: "json",
//       success: function (response) {
//         if (response.success) {
//           $('.remain').text(500);
//           if (dataIndex < stepsCount) {
//             $(".step").removeClass("active").eq(dataIndex).addClass("active");
//             dataIndex++;
//             stepCount.textContent = dataIndex;
//             stepPercent.textContent = `${(dataIndex - 1) * Math.ceil(100 / stepsCount)}%`;
//             progressBarLine.style.width = `${(dataIndex - 1) * Math.ceil(100 / stepsCount)}%`;
//           }
//           if (dataIndex === stepsCount) {
//             $(this).prop("disabled", true);
//           }
//         } else {
//           return false;
//         }
//       },
//     });
//   });

//   //user reviews saving functionality ends

//   //submit review form step 1
//   $("body").on("click", ".submit-review-form", function (event) {
//     event.preventDefault();
//     var $reviewForm = $("#review-form");
//     $.post($reviewForm.attr("action"), $reviewForm.serialize(), function (response) {
//       var requiredFields = {
//         "review-college_fees": 'College Fees cannot be blank',
//         "student-name": 'Name cannot be blank',
//         "student-email": 'Email cannot be blank',
//         "student-phone": 'Phone cannot be blank',
//         "review-college_id": 'College cannot be blank',
//         "review-course_id": 'Course cannot be blank',
//         "review-admission_year": 'Admission Year cannot be blank',
//       };
//       var questionId = {
//         1: {
//           "errorMessage": "Please select an option",
//           "yesErrorMessage": "Select Exam cannot be blank"
//         },
//         2: {
//           "errorMessage": "Please select an option",
//           "yesErrorMessage": "Select option cannot be blank"
//         },
//         5: {
//           "errorMessage": "Please select an option",
//           "yesErrorMessage": "  This field cannot be blank"
//         },
//         6: {
//           "errorMessage": "Please select an option",
//           "yesErrorMessage": "  This field cannot be blank"
//         },
//         7: {
//           "errorMessage": "Please select an option",
//           "yesErrorMessage": "  This field cannot be blank"
//         },
//       };
//       if (response.success && response.success == true) {
//         if (response.otp) {
//           var field = 0;
//           $.each(questionId, function (i, value) {
//             if ($("input[name=question" + i + "]").is(":checked") == false) {
//               if ($(".yes" + i).val() == '') {
//                 field++;
//                 if (i == 1) {
//                   $('.field-exam_id' + ' .help-block').html(value['errorMessage']);
//                 } else {
//                   $(".yes" + i + "+div").html(value['errorMessage']);
//                 }
//               }
//             } else if ($("input[name=question" + i + "]").is(":checked") == true && $("input[name=question" + i + "]:checked").val() == 'yes') {
//               if ($(".yes" + i).val() == '') {
//                 field = 2;
//                 if (i == 1) {
//                   $('.field-exam_id' + ' .help-block').html(value['yesErrorMessage']);
//                 } else {
//                   $(".yes" + i + "+div").html(value['yesErrorMessage']);
//                 }
//               }
//             }
//           });
//           if ($(".yes4").val() == '' || $(".yes4").val() == null) {
//             field = 1;
//             $('.yes4 + div').html("Class size cannot be blank");
//           }

//           if (field == 0) {
//             $("body").css("overflowY", "hidden");
//             $("#reviewFormInputs").css("filter", "blur(1px)");
//             $("#reviewUserMobile").html($("#student-phone").val());
//             $(".popupOtpSection .headingText").html("VERIFY MOBILE NUMBER");
//             $(".popupOtpSection").show();
//           }
//         } else {
//           getReviewSteps(response.review_id, response.student_id);
//         }
//       } else {
//         $.each(requiredFields, function (index, value) {
//           if ($("#" + index).val() == null || $("#" + index).val() == '') {
//             $('.field-' + index + ' .help-block').html(value);
//           }

//           if ($(".yes4").val() == '' || $(".yes4").val() == null) {
//             $('.yes4 + div').html("Class Size cannot be blank");
//           }
//         });
//         $.each(questionId, function (i, value) {
//           if ($("input[name=question" + i + "]").is(":checked") == false) {
//             if ($(".yes" + i).val() == '') {
//               field++;
//               if (i == 1) {
//                 $('.field-exam_id' + ' .help-block').html(value['errorMessage']);
//               } else {
//                 $(".yes" + i + "+div").html(value['errorMessage']);
//               }
//             }
//           } else if ($("input[name=question" + i + "]").is(":checked") == true && $("input[name=question" + i + "]:checked").val() == 'yes') {
//             if ($(".yes" + i).val() == '') {
//               field = 2;
//               if (i == 1) {
//                 $('.field-exam_id' + ' .help-block').html(value['yesErrorMessage']);
//               } else {
//                 $(".yes" + i + "+div").html(value['yesErrorMessage']);
//               }
//             }
//           }
//         });
//       }
//     }, 'json');
//   });

//   //review-verify-otp
//   $(".ReviewOtpInputs").find("input").each(function () {
//     $(this).on("keyup", function (e) {
//       if ($("#digit-1").val() !== '' && $("#digit-2").val() !== '' && $("#digit-3").val() !== '' && $("#digit-4").val() !== '') {
//         $(".js-otp-confirm").prop("disabled", false);
//         $(".js-otp-confirm").css({ "border": "1px solid #ff4e53", "background": "#ff4e53" });
//       } else {
//         $(".js-otp-confirm").prop("disabled", true);
//         $(".js-otp-confirm").css({ "border": "1px solid #e9acac", "background": "#e9acac" });
//       }

//     });
//   });
//   $('.nextInput').keypress(function (e) {
//     if (e.keyCode >= 97 && e.keyCode <= 122) {
//       return false;
//     }
//   });
//   $(".ReviewOtpInputs").find("input").each(function () {
//     $(this).attr("maxlength", 1);
//     $(this).on("keyup", function (e) {
//       var parent = $($(this).parent());
//       if (e.keyCode === 8 || e.keyCode === 37) {

//         var prev = parent.find("input#" + $(this).data("previous"));
//         if (prev.length) {
//           $(prev).select();
//         }
//       } else if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 96 && e.keyCode <= 105) || e.keyCode === 39) {
//         var next = parent.find("input#" + $(this).data("next"));
//         if (next.length) {
//           $(next).select();
//         } else {
//           if (parent.data("autosubmit")) {
//             parent.submit();
//           }
//         }
//       }
//     });
//   });
//   $("body").on("click", ".writeReviewVerifyOtp", function (event) {
//     $("body").css("overflowY", "unset");
//     event.preventDefault();
//     var validForm = true;
//     var otpResponseText = $("#otpResponseText").html("");

//     // validate
//     $.each($(".ReviewOtpInputs input[name='digit[]']"), function (index, value) {
//       if ($(this).val() == "") {
//         validForm = false;
//       }
//     });
//     if (validForm == false) {
//       otpResponseText.html("Invalid otp entered");
//       return false;
//     }
//     $.post("/ajax/write-review-verify-otp", $("#review-form").serialize(), function (response) {
//       if (response.success && response.success == true) {
//         $(".popupOtpSection").hide();
//         $("#reviewFormInputs").css("filter", "");
//         getReviewSteps(response.review_id, response.student_id);
//       } else {
//         otpResponseText.html(response.message);
//       }
//     });
//   });

//   //resend otp
//   var resend = true;
//   $("body").on("click", "#resendReviewOtp", function (e) {
//     e.preventDefault();
//     if (!resend) {
//       return false;
//     }
//     $.post("/ajax/write-review-resend-otp", $("#review-form").serialize(), function (response) {
//       if (response.success) {
//         resend = false;
//         setResendInterval();
//       }
//     });
//   });

//   function setResendInterval() {
//     var timer = 30;
//     $("#resendReviewOtp").hide();
//     var handle = setInterval(function () {
//       $("#resendTimer").html("Resend in " + timer + " Sec");
//       timer--;

//       if (timer == -1) {
//         resend = true;
//         $("#resendTimer").html("");
//         $("#resendReviewOtp").show();
//         clearInterval(handle);
//       }
//     }, 1000);
//   }

//   //close otp box
//   $("body").on("click", ".reviewClosePopupForm", function () {
//     $("body").css("overflowY", "unset");
//     $(".popupOtpSection").fadeOut();
//     $("#reviewFormInputs").css("filter", "");
//   });

//   //review image profile upload
//   $(".file-upload").on("click", function (e) {
//     e.preventDefault();
//     $("#file-profile").trigger("click");
//   });

//   $("#file-profile").change(function () {
//     var fd = new FormData();
//     var files = $('#file-profile')[0].files;

//     // Check file selected or not
//     if (files.length > 0) {
//       fd.append('file', files[0]);

//       $.ajax({
//         type: "POST",
//         url: "/writereview/store-profile-image",
//         data: fd,
//         dataType: 'json',
//         contentType: false,
//         processData: false,
//         success: function (response) {
//           if (response && response.success == 1) {
//             var src = "https://getmyuni-assets.s3.ap-south-1.amazonaws.com/students/" + response.file_name;
//             // Add img element in <div id='preview'>
//             $('#preview').append('<img src="' + src + '" width="60px;" height="60px">');
//             $('.uploadImgBtn').css("display", "none");
//           } else if (response.success == 2) {
//             alert(response.message);
//           } else {
//             alert('Image Not Uploaded Successfully');
//           }
//         },
//       });
//     }
//   });
//   //review image profile upload ends

//   // review College Images upload dropzone
//   var maxfile = 100;//in KB
//   var maxfileresult = maxfile / 1000;
//   if ($("#design-image").length !== 0) {
//     var myDropzone = Dropzone.forElement("#design-image");
//     myDropzone.options.acceptedFiles = '.jpg, .png, .webp, .jpeg';
//     myDropzone.options.maxFilesize = maxfileresult;
//     myDropzone.options.maxFiles = 15;
//     myDropzone.on("error", function (file, response) {
//       if (file.size > 100000)
//         alert("Image Size Should not exceed 100kb");
//       myDropzone.removeFile(file);
//     });
//   }
//   // review College Images upload dropzone ends

//   //copy referral code
//   function copyToClipboard(text) {
//     var sampleTextarea = document.createElement("textarea");
//     document.body.appendChild(sampleTextarea);
//     sampleTextarea.value = text; //save main text in it
//     sampleTextarea.select(); //select textarea contenrs
//     document.execCommand("copy");
//     document.body.removeChild(sampleTextarea);
//     alert("Referral Code Copied");
//   }

//   $(".copyReferalUrl").click(function () {
//     var copyText = document.getElementById("referalUrl");
//     copyToClipboard(copyText !== null ? copyText.value : '');
//   });
//   //copy referral code ends

//   //submit final write-a-review form
//   $("body").on("click", ".submitReviewForm", function () {
//     window.onbeforeunload = null;
//     var dataIndex = $(".thankyouSection").attr("data-index");
//     $(".progressBarLine").css("width", "100%");
//     $(".stepPercent").html("100%");
//     $(".stepCount").html(dataIndex);
//     var location = window.location.href;
//     var boxes = $('#agreeterms').attr('checked');
//     if (boxes == undefined) {
//       $agreeTerms = 0;
//     } else if (boxes === 'checked') {
//       $agreeTerms = 1;
//     }
//     $.ajax({
//       type: "POST",
//       url: "/writereview/submit-review-form",
//       data: {
//         terms_agreed: $agreeTerms,
//         location: location,
//       },
//       dataType: 'json',
//       success: function (response) {
//         $(".step.active").css("display", "none");
//         $(".thankyouSection").css("display", "block");
//       }
//     });
//   });

//   //rating and progress bar functionality
//   function ratingFunctionality(section) {
//     let ratingList = section.querySelectorAll(".ratingSectionList > li");
//     let totalRating = 0;
//     ratingList.forEach((list, index) => {
//       list.dataset.rating = index + 1;
//       list.addEventListener('mouseenter', onMouseOver);
//       list.addEventListener('click', onClick);
//       list.addEventListener("mouseleave", onMouseLeave);
//     });

//     function onMouseOver(e) {
//       const ratingVal = Number(e.target.dataset.rating);
//       if (!ratingVal) {
//         return;
//       } else if (e.target.parentElement.parentElement.dataset.current) {
//         return;
//       } else {
//         if (ratingVal < e.target.parentElement.parentElement.dataset.current) {
//           return;
//         } else {
//           fill(ratingVal);
//         }
//       }
//     }

//     function onClick(e) {
//       const ratingVal = Number(e.target.parentElement.dataset.rating);
//       totalRating = ratingVal;
//       fill(totalRating);
//       const activeClassName = document.getElementsByClassName('step active')[0];
//       const dataIndex = activeClassName.getAttribute('data-index');
//       const ratingClass = document.querySelector(".ratingSection" + dataIndex);
//       ratingClass.dataset.current = totalRating;
//     }

//     function onMouseLeave(e) {
//       if (typeof e.target.parentElement.parentElement.dataset.current !== undefined) {
//         fill(e.target.parentElement.parentElement.dataset.current);
//       }
//     }

//     function fill(ratingVal) {
//       for (let i = 0; i < 5; i++) {
//         ratingList[i].children[0].src = '/yas/images/empty-star.svg';
//         if (i < ratingVal) {
//           ratingList[i].children[0].src = '/yas/images/full-star.svg'
//         }
//       }
//     }
//   }

//   var progressButtons = document.querySelectorAll("button[data-index]");
//   var stepSections = document.querySelectorAll("div[data-index]");
//   var stepCount = document.querySelector(".stepCount");
//   var stepPercent = document.querySelector(".stepPercent");
//   var progressBarLine = document.querySelector(".progressBarLine");
//   var stepsCount = $(".step").length;

//   progressButtons.forEach((progressButton) => {
//     progressButton.addEventListener("click", (e) => {
//       stepSections.forEach((stepSection) => {
//         ratingFunctionality(stepSection);
//       });
//     });
//   });
//   //rating and progress bar functionality ends

//   $("#review-college_id").select2({
//     placeholder: "Select College/University Name",
//     ajax: {
//       url: "/ajax/review-colleges",
//       processResults: function (data) {
//         return {
//           results: $.map(data, function (item) {
//             return {
//               text: item.text,
//               id: item.id,
//             };
//           }),
//         };
//       },
//     },
//   });

//   $("#review-course_id").select2({
//     placeholder: "Select Course (E.g. B.com, B.tech Civil Engineering, B.arch)",
//     ajax: {
//       url: "/ajax/review-courses",
//       processResults: function (data) {
//         return {
//           results: $.map(data, function (item) {
//             return {
//               text: item.text,
//               id: item.id,
//             };
//           }),
//         };
//       },
//     },
//   });

//   $("#exam_id").select2({
//     placeholder: "Select Exam",
//     ajax: {
//       url: "/ajax/review-exams",
//       processResults: function (data) {
//         return {
//           results: $.map(data, function (item) {
//             return {
//               text: item.text,
//               id: item.id,
//             };
//           }),
//         };
//       },
//     },
//   });

//   if (jQuery().fancybox) {
//     $('[data-fancybox="reviews"]').fancybox({
//       buttons: ["slideShow", "thumbs", "zoom", "fullScreen", "share", "close"],
//       loop: false,
//       protect: true,
//     });
//   }
// });

window.addEventListener("click", function (event) {
  if (event.target === document.getElementById(`${event.target.id}`)) {
    $('.mobileSubNavDropDown .caret').removeClass('current');
    $('.mobileSubNavDropDownMenu').removeClass('current');
    $('.mobileSubNavDropDown .caret').css("background-position", "-382px -1057px");
    if (isModalOpenScript == true) {
      document.querySelector('body').style.overflowY = 'auto';
    }
    isModalOpenScript = false;
  }
});

//iphone 
if (navigator.userAgent.match(/(iPod|iPhone|iPad)/)) {
  var hitEventType = "click tap touchstart";
} else {
  var hitEventType = "click";
}

$('.caret').css('cursor', 'pointer');

if (window.screen.width < 1023) {

  $(document).on(hitEventType, ".caret", function () {
    var list_id = $(this).attr('data-list');

    $('.mobileSubNavDropDown .caret').removeClass('current');
    $('.mobileSubNavDropDownMenu').removeClass('current');

    $(this).addClass('current');
    $("#" + list_id).addClass('current');

    $(this).css("background-position", "-438px -1089px");
    document.querySelector('body').style.overflowY = 'hidden';
    isModalOpenScript = true;
    document.addEventListener('touchmove', preventDefaultScrollScript, { passive: false });
  });
}

let isModalOpenScript = false;
function preventDefaultScrollScript(event) {
  if (isModalOpenScript) {
    event.preventDefault();
  }
}

// $('.mobileSubNavDropDown .caret').click(function () {
//   alert("here!!");
//   var list_id = $(this).attr('data-list');

//   $('.mobileSubNavDropDown .caret').removeClass('current');
//   $('.mobileSubNavDropDownMenu').removeClass('current');

//   $(this).addClass('current');
//   $("#" + list_id).addClass('current');

//   $(this).css("background-position", "651px -154px");
//   $(this).css("transform", "scale(0.7)");
// });


$('.subNavDropDown').hover(function () {
  $('.subNavDropDownMenu').css('left', $(this).position().left + 'px')
})


// let cssLoaded = false;

// window.onscroll = function () {
//   const scrollTriggerHeight = window.innerHeight;

//   if (!cssLoaded && window.scrollY > scrollTriggerHeight) {
//     let link = document.createElement('link');
//     link.rel = 'stylesheet';
//     if (gmu.config.prodEvn == 'prod') {
//       link.href = window.location.origin + '/yas/css/version2/min/bottom-widget.css';
//     } else {
//       link.href = window.location.origin + '/yas/css/version2/bottom-widget.css';
//     }
//     // link.href = 'bottom-widget.css';
//     document.head.appendChild(link);
//     cssLoaded = true;
//   }
// };

if (gmu.config.entity !== 'college-listing') {
  document.addEventListener("DOMContentLoaded", function () {
    const heading = document.querySelector(".table-content-heading-article");
    const content = document.querySelector(".table-content-article");
    if(heading){
      const arrowIcon = heading.querySelector(".downArrowIcon");
      heading.addEventListener("click", function () {
        if (content.classList.contains("open")) {
          content.classList.remove("open");
          arrowIcon.classList.remove("rotate");
        } else {
          content.classList.add("open");
          arrowIcon.classList.add("rotate");
        }
      });

    }

   
  });
}

jQuery(function ($) { });

// Tabber and Accordion
var accordianElement = document.querySelectorAll(".pathAccordion");
var j;
if (accordianElement.length > 0) {
  accordianElement[0].classList.add('active')
  accordianElement[0].nextElementSibling.classList.add('show');
  for (j = 0; j < accordianElement.length; j++) {
    accordianElement[j].addEventListener("click", function () {
      this.classList.toggle("active");
      var panel = this.nextElementSibling;
      if (panel.classList.contains("show")) {
        $(".pathAccordion").removeClass("active");
        $(".pathPanel").removeClass("show").removeAttr("style");
      }
      else {
        $(".pathAccordion").removeClass("active");
        $(".pathPanel").removeClass("show").removeAttr("style");

        panel.classList.add("show")
        this.classList.add("active")
      }
    });
  }
}

$(".gis__tabber__immigration ul li").click(function () {
  var tab_id = $(this).attr("data-tab");

  Array.from(this.parentElement.children).forEach((li) => {
    li.classList.remove("current");
  });
  Array.from(
    document.querySelector(`#${tab_id}`).parentElement.children
  ).forEach((li) => {
    li.classList.remove("current");
  });

  $(this).addClass("current");
  $("#" + tab_id).addClass("current");
  var tabCicked = $(this).attr('data-tab');
  $('#' + tabCicked + '> .pathRow > .pathPanel:eq(0)').addClass('show');
  $('#' + tabCicked + '> .pathRow > .pathAccordion:eq(0)').addClass('active');
});

// Our Offices
document.querySelectorAll("input[type=radio]").forEach((radio, index) => {
  radio.addEventListener("click", (e) => {
    document.querySelectorAll(".mapTab .selectedCityMap").forEach((city) => {
      city.classList.remove("selectedCity");
    });
    document
      .querySelectorAll(".selectedCityArea .cityLocationAddress")
      .forEach((city) => {
        city.classList.remove("selectedCity");
      });
    document
      .querySelector(
        `.selectedCityArea div[data-city="${e.target.dataset.city}"]`
      )
      .classList.add("selectedCity");
    document
      .querySelector(`.mapTab div[data-city="${e.target.dataset.city}"]`)
      .classList.add("selectedCity");
  });
  if (index === 0) {
    document
      .querySelector(`.selectedCityArea div[data-city="${radio.dataset.city}"]`)
      .classList.add("selectedCity");
    document
      .querySelector(`.mapTab div[data-city="${radio.dataset.city}"]`)
      .classList.add("selectedCity");
  }
});

// Accordion
document.addEventListener("DOMContentLoaded", function () {
  const accordionHeadings = document.querySelectorAll(
    ".gis__accordion__heading"
  );

  accordionHeadings.forEach((heading) => {
    heading.addEventListener("click", function () {
      this.classList.toggle("active");
      const content = this.nextElementSibling;

      if (content.style.display == "none" || content.style.display == "") {
        content.style.display = "block";
      } else {
        content.style.display = "none";
      }
    });
  });
});
// Tabber
document.addEventListener("DOMContentLoaded", function () {
  const tabLinks = document.querySelectorAll(".tab-link");
  const tabContents = document.querySelectorAll(".tab-content");

  tabLinks.forEach((tab) => {
    tab.addEventListener("click", function () {
      tabLinks.forEach((link) => link.classList.remove("current"));
      this.classList.add("current");

      tabContents.forEach((content) => content.classList.remove("current"));

      const activeTab = this.getAttribute("data-tab");
      document.getElementById(activeTab).classList.add("current");
    });
  });
});

$(document).ready(function () {
  const messages = [
    "🏆 Over 10,000 successful applications",
    "🏆 Expert Consultant",
    "🏆 Customized Solutions",
    "🏆 95% success rate",
    "🏆 Exceptional results",
    "🏆 Affordable Visa Services",
    "🏆 Economical visa solutions",
    "🏆 Real-time Application",
  ];

  let index = 0;

  function animateText() {
    $(".gis__immigrationBanner__subTitleEnd")
      .html(`<b>${messages[index]}</b>`)
      .removeClass("slide-out")
      .addClass("slide-in");

    setTimeout(function () {
      $(".gis__immigrationBanner__subTitleEnd")
        .removeClass("slide-in")
        .addClass("slide-out");

      index = (index + 1) % messages.length;
      setTimeout(animateText, 1000);
    }, 2000);
  }

  animateText();

  $(".expertVideo").on("click", function (e) {
    e.preventDefault(); // Prevent any default behavior of the button

    // Open Fancybox manually
    $.fancybox.open({
      src: $(this).attr("href"), // Get the video URL from the href attribute
      type: "iframe",
      iframe: {
        css: {
          width: "1040px",
          height: "660px",
        },
      },
      toolbar: false,
      smallBtn: true,
    });
  });

  $('.gis__readMoreBtn').click(function () {
    const content = $('.gis__section__bannerContent');
    const banner = $('.gis__section__banner');

    if (content.hasClass('expanded')) {
      content.removeClass('expanded'); // Collapse content
      $(this).text('Read More'); // Change button text

      // Scroll to the top of the container
      $('html, body').animate({
        scrollTop: banner.offset().top
      }, 300); // Adjust the speed as needed (300ms in this case)

    } else {
      content.addClass('expanded'); // Expand content
      $(this).text('Read Less'); // Change button text
    }
  });
});
