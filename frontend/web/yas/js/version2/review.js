$(document).ready(function () {
  //validations and  form functionalities starts
  $('.submit-review-form, .submit-review-step-next, .previousBtn, .submitReviewForm, .verifyOtp').click(function () {
    $('html, body').animate({ scrollTop: 0 }, 200);
  });

  if (window.location.href.includes("/review/create")) {
    $("#lead-form-js").remove();
    $("#login-form-js").remove();
    $("#secondaryHeader-web").remove();
    window.onbeforeunload = function () {
      return "Leave this page ?";
    }
  }

  //find if the url containd fbclid id and replace with the original url
  //This is used only if the user navigate from the fb app
  var baseUrl = window.location.href;
  var url = baseUrl.includes("fbclid");

  if (url === true) {
    var stripfbclis = baseUrl.indexOf("?");
    var value = baseUrl.substring(stripfbclis);
    var replaceString = baseUrl.replace(value, "");
    window.history.pushState(baseUrl, "", replaceString);
  }

  //count textarea words
  function countWords(s) {
    s = s.replace(/(^\s*)|(\s*$)/gi, "");
    s = s.replace(/\n /, "\n");
    return s.split(" ").length;
  }

  //diable/enable radio buttons
  $("input[type=radio]").click(function (e) {
    var getClass = $(this).attr("id");
    if ($(this).val() == "yes") {
      $("." + getClass).prop("readonly", false);
      $("." + getClass).css("pointer-events", "");
      $("." + getClass).prop('required', true);
      $("." + getClass).val("");
      if (getClass == "yes3") {
        $(".yes3").val("Yes");
        $(".yes3" + "+div").html("");
      }
      if (getClass == "yes1") {
        $('.field-exam_id' + ' .help-block').html("");
        $(".field-exam_id .selection").css("pointer-events", "auto");
        $("#select2-exam_id-container .select2-selection__placeholder").attr('style', 'color: #282828!important');
        $(".field-exam_id .select2, .field-exam_id .select2-selection").attr('style', 'background: ""');
      }
      if (getClass == "yes2") {
        $(".yes2" + "+div").html("");
        $(".yes2").attr('style', 'color: #282828!important');
        $(".yes2").attr('style', 'background: ""');
      }
      if (getClass == 'yes5' || getClass == 'yes6' || getClass == 'yes7') {
        $("." + getClass + "+div").html("");
        $("." + getClass).attr('style', 'background: ""');
      }
    } else {
      var text = getClass;
      var replaceId = text.replace("no", "yes");
      $("." + replaceId).prop("readonly", true);
      $("." + replaceId).prop('required', false);
      if (replaceId == 'yes1') {
        $('.field-exam_id' + ' .help-block').html("");
        $("#exam_id").val('').trigger('change');
        $("#select2-exam_id-container .select2-selection__placeholder").attr('style', 'color: #989898!important');
        $(".field-exam_id .selection").css("pointer-events", "none");
        $(".field-exam_id .select2, .field-exam_id .select2-selection").attr('style', 'background : #d8d8d8!important');
      }
      if (replaceId == 'yes2') {
        $(".yes2" + "+div").html("");
        $(".yes2").val("");
        $(".yes2").css({ "pointer-events": "none", "color": "#989898!important", "font-size": "14px", "background": "#d8d8d8" });
      }
      if (replaceId == 'yes5' || replaceId == 'yes6' || replaceId == 'yes7') {
        $("." + replaceId + "+div").html("");
        $("." + replaceId).val("");
        $("." + replaceId).css({ "pointer-events": "none", "background": "#d8d8d8" });
      }
      if (replaceId == "yes3") {
        $(".yes3" + "+div").html("");
        $(".yes3").val("No");
      }
    }
  });

  //textarea, input field validations
  var maxchars = 500;
  $("textarea").keyup(function () {
    var tlength = $(this).val().length;
    $(this).val($(this).val().substring(0, maxchars));
    var tlength = $(this).val().length;
    remain = maxchars - parseInt(tlength);
    $(".remain").text(remain);
    $("body").find('.category-review-content').html('');
  });

  $("body").on("click", ".ratingSectionList", function () {
    $("body").find('.rating-review-content').html('');
  });

  $('.yes4').keyup(function (e) {
    $('.yes4 + div').html("");
    var input = parseInt(this.value);
    if (input < 0 || input > 200) {
      $('.yes4 + div').html("Class Size cannot be greater than 200");
      $(".yes4").val("");
    }
  });

  $('.ReviewOtpInputs, .reviewFormDiv').keypress(function (e) {
    if (e.which == 13 && e.target.nodeName != "TEXTAREA") return false;
  });

  $('.yes4, #review-college_fees, #student-phone, .yes5, .yes6, .yes7').keypress(function (e) {
    var charCode = (e.which) ? e.which : e.keyCode
    if (String.fromCharCode(charCode).match(/[^0-9]/g)) {
      return false;
    }
  });

  $("#student-name").keypress(function (e) {
    var key = e.keyCode;
    if (key >= 48 && key <= 57) {
      e.preventDefault();
    }
  });

  $("textarea").on("cut copy paste", function (e) {
    e.preventDefault();
  });

  $("body").on("change", "#review-admission_year", function (event) {
    if ($(this).val() == '') {
      $(this).attr('style', 'color: #989898!important');
    } else {
      $(this).attr('style', 'color: black!important');
    }
  });
  //textarea, input field validations ends

  //validations and  form functionalities starts
  //user reviews saving functionality
  var nextBtn = $(".submit-review-step-next");
  prevBtn = $(".previousBtn");
  function getReviewSteps(reviewId, studentId) {
    var index = $(".step.active").attr("data-index");
    var oldLocation = window.location.href;
    var url = window.location.origin;
    if (oldLocation.includes(reviewId) == false) {
      window.history.pushState(oldLocation, "", url + "/review/create/" + reviewId);
    }

    var success = 0;
    var questionId = {
      1: {
        "errorMessage": "Please select an option",
        "yesErrorMessage": "Select Exam cannot be blank"
      },
      2: {
        "errorMessage": "Please select an option",
        "yesErrorMessage": "Select option cannot be blank"
      },
      5: {
        "errorMessage": "Please select an option",
        "yesErrorMessage": "  This field cannot be blank"
      },
      6: {
        "errorMessage": "Please select an option",
        "yesErrorMessage": "  This field cannot be blank"
      },
      7: {
        "errorMessage": "Please select an option",
        "yesErrorMessage": "  This field cannot be blank"
      },
    };

    $.each(questionId, function (i, value) {
      if ($("input[name=question" + i + "]").is(":checked") == false) {
        if ($(".yes" + i).val() == '') {
          success++;
          if (i == 1) {
            $('.field-exam_id' + ' .help-block').html(value['errorMessage']);
          } else {
            $(".yes" + i + "+div").html(value['errorMessage']);
          }
        }
      } else if ($("input[name=question" + i + "]").is(":checked") == true && $("input[name=question" + i + "]:checked").val() == 'yes') {
        if ($(".yes" + i).val() == '') {
          success = 2;
          if (i == 1) {
            $('.field-exam_id' + ' .help-block').html(value['yesErrorMessage']);
          } else {
            $(".yes" + i + "+div").html(value['yesErrorMessage']);
          }
        }
      }
    });
    if ($(".yes4").val() == '' || $(".yes4").val() == null) {
      success = 1;
      $('.yes4 + div').html("Class Size cannot be blank");
    }
    if (success == 0) {
      if (index < stepsCount) {
        $(".step").removeClass("active").eq(index).addClass("active");
        index++;
        stepCount.textContent = index;
        stepPercent.textContent = `${(index - 1) * Math.ceil(100 / stepsCount)}%`;
        progressBarLine.style.width = `${(index - 1) * Math.ceil(100 / stepsCount)}%`;
      }
      if (index === stepsCount) {
        $(this).prop("disabled", true);
      }
    }
  }
  $(prevBtn).on("click", function () {
    var index = $(".step.active").attr("data-index");
    if (index > 0) {
      $(".step").removeClass("active");
      index--;
      $('div[data-index = ' + index + ']').addClass("active");
      stepCount.textContent = index;
      stepPercent.textContent = `${(index - 1) * Math.ceil(100 / stepsCount)}%`;
      progressBarLine.style.width = `${(index - 1) * Math.ceil(100 / stepsCount)}%`;
    }
    if (index === 0) {
      $(this).css("disabled", true);
    }
  });

  $('.titleValue').keypress(function (e) {
    $('.title' + ' .help-block').html("");
  });
  $(nextBtn).on("click", function (event) {
    event.preventDefault();
    var dataIndex = $(".step.active").attr("data-index");
    var rating = $(".ratingSection" + dataIndex).attr("data-current");
    var categoryAnswer = $(".step.active").find('textarea[name="step1"]').val();
    var reviewContent = categoryAnswer !== undefined ? categoryAnswer.replace(/(<([^>]+)>)/gi, "") : "";
    var arrayOfFake = reviewContent.match(/(.)\1{15,}/);
    $reviewId = window.location.href.split("/");
    $reviewCategoryId = $(".step.active").find("input:hidden[name=review-category-id]").val();
    title = $(".step.active").find("input:text[name=title]").val();

    if (rating == undefined && reviewContent.length == 0 && (title == '' || title == null)) {
      $('.title' + ' .help-block').html("Title of your review cannot be blank");
      $('.category-review-content').html("A review cannot be left blank. Please answer the questions above and make it useful for the reader.");
      $('.rating-review-content').html("Please add a rating to support this review.");
      return false;
    }

    // if ($('.mobileOnly.brochureBtn').offset()) {
    //     $(document).ready(function () {
    //         var stickybrochureBtn = $('.mobileOnly.brochureBtn').offset().top - (48);
    //         $(window).scroll(function () {
    //             var sticky = $('.brochureBtn');
    //             var scroll = $(window).scrollTop();
    //             var fixedBarHeight = $(".collegeRelataedLinks, .pageRedirectionLinks, .examRelataedLinks").outerHeight();
    //             var brochureBtnHeight = $(".mobileOnly.brochureBtn").outerHeight();

    //             if (window.screen.width <= 1023) {
    //                 if (scroll >= stickybrochureBtn) {
    //                     sticky.addClass('fixedbrochureBtn');
    //                     $('body').css("margin-top", fixedBarHeight + brochureBtnHeight + 20);
    //                 }
    //                 else {
    //                     sticky.removeClass('fixedbrochureBtn');
    //                 }
    //             }
    //         });
    //     });
    // }
    if (rating == undefined && reviewContent.length == 0) {
      $('.category-review-content').html("A review cannot be left blank. Please answer the questions above and make it useful for the reader.");
      $('.rating-review-content').html("Please add a rating to support this review.");
      return false;
    }

    if (reviewContent.length > 0 && reviewContent.length < 100 && rating == undefined) {
      $('.category-review-content').html("This review is too short to be helpful (" + (100 - parseInt(reviewContent.length)).toString() + " characters remaining). Please see the questions above for more ideas on what to write.");
      $('.rating-review-content').html("Please add a rating to support this review.");
      return false;
    }

    if (categoryAnswer !== undefined && reviewContent.length == 0) {
      $('.category-review-content').html("A review cannot be left blank. Please answer the questions above and make it useful for the reader.");
      return false;
    }
    if (arrayOfFake != null && arrayOfFake["length"] > 0) {
      $('.category-review-content').html("The text below doesn't make much sense. Can you correct this ?");
      return false;
    }
    if (reviewContent.length > 0 && reviewContent.length < 100) {
      $('.category-review-content').html("This review is too short to be helpful (" + (100 - parseInt(reviewContent.length)).toString() + " characters remaining). Please see the questions above for more ideas on what to write.");
      return false;
    }
    if (reviewContent.length > 0 && rating == undefined) {
      $('.rating-review-content').html("Please add a rating to support this review.");
      return false;
    }
    if (reviewContent.length > 0 && countWords(reviewContent) < 6) {
      $('.category-review-content').html("This review is too short to be helpful. Please see the questions above for more ideas on what to write.");
      return false;
    }
    if ($(".step.active").find("input:text[name=title]").length == 1) {
      if (title == '' || title == null) {
        $('.title' + ' .help-block').html("Title of your review cannot be blank");
        return false;
      }
    }

    $.ajax({
      type: "POST",
      url: "/writereview/store-content",
      data: {
        steps: dataIndex,
        content: categoryAnswer,
        reviewId: $reviewId['5'] ?? '',
        reviewCatId: $reviewCategoryId,
        reviewTitle: title,
        ratingValue: rating,
        // referralcode: $referralcode ?? '',
      },
      dataType: "json",
      success: function (response) {
        if (response.success) {
          $('.remain').text(500);
          if (dataIndex < stepsCount) {
            $(".step").removeClass("active").eq(dataIndex).addClass("active");
            dataIndex++;
            stepCount.textContent = dataIndex;
            stepPercent.textContent = `${(dataIndex - 1) * Math.ceil(100 / stepsCount)}%`;
            progressBarLine.style.width = `${(dataIndex - 1) * Math.ceil(100 / stepsCount)}%`;
          }
          if (dataIndex === stepsCount) {
            $(this).prop("disabled", true);
          }
        } else {
          return false;
        }
      },
    });
  });

  //user reviews saving functionality ends

  //submit review form step 1
  $("body").on("click", ".submit-review-form", function (event) {
    event.preventDefault();
    var $reviewForm = $("#review-form");
    $.post($reviewForm.attr("action"), $reviewForm.serialize(), function (response) {
      var requiredFields = {
        "review-college_fees": 'College Fees cannot be blank',
        "student-name": 'Name cannot be blank',
        "student-email": 'Email cannot be blank',
        "student-phone": 'Phone cannot be blank',
        "review-college_id": 'College cannot be blank',
        "review-course_id": 'Course cannot be blank',
        "review-admission_year": 'Admission Year cannot be blank',
      };
      var questionId = {
        1: {
          "errorMessage": "Please select an option",
          "yesErrorMessage": "Select Exam cannot be blank"
        },
        2: {
          "errorMessage": "Please select an option",
          "yesErrorMessage": "Select option cannot be blank"
        },
        5: {
          "errorMessage": "Please select an option",
          "yesErrorMessage": "  This field cannot be blank"
        },
        6: {
          "errorMessage": "Please select an option",
          "yesErrorMessage": "  This field cannot be blank"
        },
        7: {
          "errorMessage": "Please select an option",
          "yesErrorMessage": "  This field cannot be blank"
        },
      };
      if (response.success && response.success == true) {
        if (response.otp) {
          var field = 0;
          $.each(questionId, function (i, value) {
            if ($("input[name=question" + i + "]").is(":checked") == false) {
              if ($(".yes" + i).val() == '') {
                field++;
                if (i == 1) {
                  $('.field-exam_id' + ' .help-block').html(value['errorMessage']);
                } else {
                  $(".yes" + i + "+div").html(value['errorMessage']);
                }
              }
            } else if ($("input[name=question" + i + "]").is(":checked") == true && $("input[name=question" + i + "]:checked").val() == 'yes') {
              if ($(".yes" + i).val() == '') {
                field = 2;
                if (i == 1) {
                  $('.field-exam_id' + ' .help-block').html(value['yesErrorMessage']);
                } else {
                  $(".yes" + i + "+div").html(value['yesErrorMessage']);
                }
              }
            }
          });
          if ($(".yes4").val() == '' || $(".yes4").val() == null) {
            field = 1;
            $('.yes4 + div').html("Class size cannot be blank");
          }

          if (field == 0) {
            $("body").css("overflowY", "hidden");
            $("#reviewFormInputs").css("filter", "blur(1px)");
            $("#reviewUserMobile").html($("#student-phone").val());
            $(".popupOtpSection .headingText").html("VERIFY MOBILE NUMBER");
            $(".popupOtpSection").show();
          }
        } else {
          getReviewSteps(response.review_id, response.student_id);
        }
      } else {
        $.each(requiredFields, function (index, value) {
          if ($("#" + index).val() == null || $("#" + index).val() == '') {
            $('.field-' + index + ' .help-block').html(value);
          }

          if ($(".yes4").val() == '' || $(".yes4").val() == null) {
            $('.yes4 + div').html("Class Size cannot be blank");
          }
        });
        $.each(questionId, function (i, value) {
          if ($("input[name=question" + i + "]").is(":checked") == false) {
            if ($(".yes" + i).val() == '') {
              field++;
              if (i == 1) {
                $('.field-exam_id' + ' .help-block').html(value['errorMessage']);
              } else {
                $(".yes" + i + "+div").html(value['errorMessage']);
              }
            }
          } else if ($("input[name=question" + i + "]").is(":checked") == true && $("input[name=question" + i + "]:checked").val() == 'yes') {
            if ($(".yes" + i).val() == '') {
              field = 2;
              if (i == 1) {
                $('.field-exam_id' + ' .help-block').html(value['yesErrorMessage']);
              } else {
                $(".yes" + i + "+div").html(value['yesErrorMessage']);
              }
            }
          }
        });
        if (response.error_limit.status == true) {
          $("body").css("overflow", "hidden");
          $("body").css("height", "100vh");
          $("body").css("position", "fixed");
          $('.review__Error__Popup').show();
          $(".error__text").html(response.error_limit.message)
        }
      }
    }, 'json');
  });

  //review-verify-otp
  $(".ReviewOtpInputs").find("input").each(function () {
    $(this).on("keyup", function (e) {
      if ($("#digit-1").val() !== '' && $("#digit-2").val() !== '' && $("#digit-3").val() !== '' && $("#digit-4").val() !== '') {
        $(".js-otp-confirm").prop("disabled", false);
        $(".js-otp-confirm").css({ "border": "1px solid #ff4e53", "background": "#ff4e53" });
      } else {
        $(".js-otp-confirm").prop("disabled", true);
        $(".js-otp-confirm").css({ "border": "1px solid #e9acac", "background": "#e9acac" });
      }

    });
  });
  $('.nextInput').keypress(function (e) {
    if (e.keyCode >= 97 && e.keyCode <= 122) {
      return false;
    }
  });
  $(".ReviewOtpInputs").find("input").each(function () {
    $(this).attr("maxlength", 1);
    $(this).on("keyup", function (e) {
      var parent = $($(this).parent());
      if (e.keyCode === 8 || e.keyCode === 37) {

        var prev = parent.find("input#" + $(this).data("previous"));
        if (prev.length) {
          $(prev).select();
        }
      } else if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 96 && e.keyCode <= 105) || e.keyCode === 39) {
        var next = parent.find("input#" + $(this).data("next"));
        if (next.length) {
          $(next).select();
        } else {
          if (parent.data("autosubmit")) {
            parent.submit();
          }
        }
      }
    });
  });
  $("body").on("click", ".writeReviewVerifyOtp", function (event) {
    $("body").css("overflowY", "unset");
    event.preventDefault();
    var validForm = true;
    var otpResponseText = $("#otpResponseText").html("");

    // validate
    $.each($(".ReviewOtpInputs input[name='digit[]']"), function (index, value) {
      if ($(this).val() == "") {
        validForm = false;
      }
    });
    if (validForm == false) {
      otpResponseText.html("Invalid otp entered");
      return false;
    }
    $.post("/ajax/write-review-verify-otp", $("#review-form").serialize(), function (response) {
      if (response.success && response.success == true) {
        $(".popupOtpSection").hide();
        $("#reviewFormInputs").css("filter", "");
        getReviewSteps(response.review_id, response.student_id);
      } else {
        otpResponseText.html(response.message);
      }
    });
  });

  //resend otp
  var resend = true;
  $("body").on("click", "#resendReviewOtp", function (e) {
    e.preventDefault();
    if (!resend) {
      return false;
    }
    $.post("/ajax/write-review-resend-otp", $("#review-form").serialize(), function (response) {
      if (response.success) {
        resend = false;
        setResendInterval();
      }
    });
  });

  function setResendInterval() {
    var timer = 30;
    $("#resendReviewOtp").hide();
    var handle = setInterval(function () {
      $("#resendTimer").html("Resend in " + timer + " Sec");
      timer--;

      if (timer == -1) {
        resend = true;
        $("#resendTimer").html("");
        $("#resendReviewOtp").show();
        clearInterval(handle);
      }
    }, 1000);
  }

  //close otp box
  $("body").on("click", ".reviewClosePopupForm", function () {
    $("body").css("overflowY", "unset");
    $(".popupOtpSection").fadeOut();
    $("#reviewFormInputs").css("filter", "");
  });

  //review image profile upload
  $(".file-upload").on("click", function (e) {
    e.preventDefault();
    $("#file-profile").trigger("click");
  });

  $("#file-profile").change(function () {
    var fd = new FormData();
    var files = $('#file-profile')[0].files;

    // Check file selected or not
    if (files.length > 0) {
      fd.append('file', files[0]);

      $.ajax({
        type: "POST",
        url: "/writereview/store-profile-image",
        data: fd,
        dataType: 'json',
        contentType: false,
        processData: false,
        success: function (response) {
          if (response && response.success == 1) {
            var src = "https://getmyuni-assets.s3.ap-south-1.amazonaws.com/students/" + response.file_name;
            // Add img element in <div id='preview'>
            $('#preview').append('<img src="' + src + '" width="60px;" height="60px">');
            $('.uploadImgBtn').css("display", "none");
          } else if (response.success == 2) {
            alert(response.message);
          } else {
            alert('Image Not Uploaded Successfully');
          }
        },
      });
    }
  });
  //review image profile upload ends

  // review College Images upload dropzone
  var maxfile = 100;//in KB
  var maxfileresult = maxfile / 1000;
  if ($("#design-image").length !== 0) {
    var myDropzone = Dropzone.forElement("#design-image");
    myDropzone.options.acceptedFiles = '.jpg, .png, .webp, .jpeg';
    myDropzone.options.maxFilesize = maxfileresult;
    myDropzone.options.maxFiles = 15;
    myDropzone.on("error", function (file, response) {
      if (file.size > 100000)
        alert("Image Size Should not exceed 100kb");
      myDropzone.removeFile(file);
    });
  }
  // review College Images upload dropzone ends

  //copy referral code
  function copyToClipboard(text) {
    var sampleTextarea = document.createElement("textarea");
    document.body.appendChild(sampleTextarea);
    sampleTextarea.value = text; //save main text in it
    sampleTextarea.select(); //select textarea contenrs
    document.execCommand("copy");
    document.body.removeChild(sampleTextarea);
    alert("Referral Code Copied");
  }

  $(".copyReferalUrl").click(function () {
    var copyText = document.getElementById("referalUrl");
    copyToClipboard(copyText !== null ? copyText.value : '');
  });
  //copy referral code ends

  //submit final write-a-review form
  $("body").on("click", ".submitReviewForm", function () {
    window.onbeforeunload = null;
    var dataIndex = $(".thankyouSection").attr("data-index");
    $(".progressBarLine").css("width", "100%");
    $(".stepPercent").html("100%");
    $(".stepCount").html(dataIndex);
    var location = window.location.href;
    var boxes = $('#agreeterms').attr('checked');
    if (boxes == undefined) {
      $agreeTerms = 0;
    } else if (boxes === 'checked') {
      $agreeTerms = 1;
    }
    $.ajax({
      type: "POST",
      url: "/writereview/submit-review-form",
      data: {
        terms_agreed: $agreeTerms,
        location: location,
      },
      dataType: 'json',
      success: function (response) {
        $(".step.active").css("display", "none");
        $(".thankyouSection").css("display", "block");
      }
    });
  });

  //rating and progress bar functionality
  function ratingFunctionality(section) {
    let ratingList = section.querySelectorAll(".ratingSectionList > li");
    let totalRating = 0;
    ratingList.forEach((list, index) => {
      list.dataset.rating = index + 1;
      list.addEventListener('mouseenter', onMouseOver);
      list.addEventListener('click', onClick);
      list.addEventListener("mouseleave", onMouseLeave);
    });

    function onMouseOver(e) {
      const ratingVal = Number(e.target.dataset.rating);
      if (!ratingVal) {
        return;
      } else if (e.target.parentElement.parentElement.dataset.current) {
        return;
      } else {
        if (ratingVal < e.target.parentElement.parentElement.dataset.current) {
          return;
        } else {
          fill(ratingVal);
        }
      }
    }

    function onClick(e) {
      const ratingVal = Number(e.target.parentElement.dataset.rating);
      totalRating = ratingVal;
      fill(totalRating);
      const activeClassName = document.getElementsByClassName('step active')[0];
      const dataIndex = activeClassName.getAttribute('data-index');
      const ratingClass = document.querySelector(".ratingSection" + dataIndex);
      ratingClass.dataset.current = totalRating;
    }

    function onMouseLeave(e) {
      if (typeof e.target.parentElement.parentElement.dataset.current !== undefined) {
        fill(e.target.parentElement.parentElement.dataset.current);
      }
    }

    function fill(ratingVal) {
      for (let i = 0; i < 5; i++) {
        ratingList[i].children[0].src = '/yas/images/empty-star.svg';
        if (i < ratingVal) {
          ratingList[i].children[0].src = '/yas/images/full-star.svg'
        }
      }
    }
  }

  var progressButtons = document.querySelectorAll("button[data-index]");
  var stepSections = document.querySelectorAll("div[data-index]");
  var stepCount = document.querySelector(".stepCount");
  var stepPercent = document.querySelector(".stepPercent");
  var progressBarLine = document.querySelector(".progressBarLine");
  var stepsCount = $(".step").length;

  progressButtons.forEach((progressButton) => {
    progressButton.addEventListener("click", (e) => {
      stepSections.forEach((stepSection) => {
        ratingFunctionality(stepSection);
      });
    });
  });
  //rating and progress bar functionality ends

  $("#review-college_id").select2({
    placeholder: "Select College/University Name",
    ajax: {
      url: "/ajax/review-colleges",
      processResults: function (data) {
        return {
          results: $.map(data, function (item) {
            return {
              text: item.text,
              id: item.id,
            };
          }),
        };
      },
    },
  });

  $("#review-course_id").select2({
    placeholder: "Select Course (E.g. B.com, B.tech Civil Engineering, B.arch)",
    ajax: {
      url: "/ajax/review-courses",
      processResults: function (data) {
        return {
          results: $.map(data, function (item) {
            return {
              text: item.text,
              id: item.id,
            };
          }),
        };
      },
    },
  });

  $("#exam_id").select2({
    placeholder: "Select Exam",
    ajax: {
      url: "/ajax/review-exams",
      processResults: function (data) {
        return {
          results: $.map(data, function (item) {
            return {
              text: item.text,
              id: item.id,
            };
          }),
        };
      },
    },
  });

  if (jQuery().fancybox) {
    $('[data-fancybox="reviews"]').fancybox({
      buttons: ["slideShow", "thumbs", "zoom", "fullScreen", "share", "close"],
      loop: false,
      protect: true,
    });
  }
  $(".close__review__error__popup").on("click", function () {
    $('.review__Error__Popup').hide();
    $("body").css("overflow", "auto")
    $("body").css("height", "unset")
    $("body").css("position", "static")
  });
});
