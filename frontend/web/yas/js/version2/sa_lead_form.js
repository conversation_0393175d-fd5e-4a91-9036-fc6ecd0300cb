
$.get('/sa-lead-form', {}, function (leadFormContainerV2Lead) {
    $("#sa-lead-form-js").html(leadFormContainerV2Lead);
    //get field info
    const nameInputLead = document.querySelector('#gmusaleads-name');
    const emailInputLead = document.querySelector('#gmusaleads-email');
    const mobileInputLead = document.querySelector('#gmusaleads-phone');
    const curentCountry = document.querySelector('#current_country');
    const curentCountryCode = document.querySelector('.iti__selected-dial-code') ? document.querySelector('.iti__selected-dial-code').textContent : '';
    const currentCity = document.querySelector('#current_sa_city');
    const saStudyDestination = document.querySelector('#sa_study_destination');
    const planToStudy = document.querySelector('#gmusaleads-planning_duration');
    const degree = document.querySelector('#gmusaleads-degree');
    const submitButtonLead = document.querySelector('.submit-sa-lead-form');

    //submit button
    function submitButtonSaFunctionality() {
        if (nameInputLead.value.length !== 0
            && (emailInputLead.value.length !== 0
                && $(".errorMsgSaEmail").html() == '')
            && (mobileInputLead.value.length == 10)
            && curentCountry.value.length !== 0
            && currentCity.value.length !== 0
            && saStudyDestination.value.length !== 0
            && planToStudy.value.length !== 0
            && degree.value.length !== 0
            // && curentCountryCode !== ''
            && $("#gmusaleads-appeared_exam input[type='checkbox']:checked").length > 0
        ) {
            submitButtonLead.removeAttribute('disabled');
        } else {
            submitButtonLead.setAttribute('disabled', true);
        }
    }

    function initCountrySelect2(selector, placeholder, entity) {
        $(selector).select2({
            placeholder: placeholder,
            ajax: {
                url: "/ajax/lead-sa-countries",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    return {
                        term: params.term,
                        entity: entity
                    };
                },
                processResults: function (data) {
                    return {
                        results: data.map(item => ({
                            text: item.text,
                            id: item.id,
                        })),
                    };
                },
            },
        });
    }

    $('.intrestredDegreeSelect select').select2({
        placeholder: "What do you plan to study",
        ajax: {
            url: "/ajax/lead-sa-degree",
            dataType: "json",
            type: "GET",
            data: function (params) {
                return {
                    term: params.term,
                };
            },
            processResults: function (data) {
                return {
                    results: data.map(item => ({
                        text: item.text,
                        id: item.id,
                    })),
                };
            },
        },
    });

    //Function to check if all fields are filled for submit button functionality || screen one
    $(".countryClass, .saCityClass, #gmusaleads-name, #gmusaleads-email, .studyDestinationClass, #gmusaleads-phone, #gmusaleads-planning_duration, #gmusaleads-degree, #gmusaleads-appeared_exam input[type='checkbox']").bind("change keyup", function (event) {
        submitButtonSaFunctionality()
    });

    $('#gmusaleads-planning_duration, #gmusaleads-degree').on('change', function () {
        $(this).css('color', this.value ? '#000' : '#989898');
    });

    initCountrySelect2('.inputCountryContainer select', 'Country You Live In', 1);
    initCountrySelect2('.inputstudyDestination select', 'Study Destination', 2);

    const $countrySelect = $('.inputCountryContainer select');
    const $citySelect = $('.inputsaCityContainer select');

    $countrySelect.on('change', function () {
        $('#current_sa_city').val('').trigger('change');
        let countryId = $("#current_country").val();
        if (!countryId) {
            $citySelect.prop('disabled', true).empty().trigger('change');
            return;
        }

        $.ajax({
            url: "/ajax/lead-sa-cities",
            dataType: "json",
            type: "GET",
            data: { country_id: countryId },
            success: function (data) {
                if (data && data.length) {
                    let cityOptions = data.map(city =>
                        $("<option></option>").val(city.id).text(city.text)
                    );
                    $citySelect.append(cityOptions).prop('disabled', false).trigger('change');
                } else {
                    $citySelect.prop('disabled', true).trigger('change');
                }
            }
        });
    });

    $citySelect.select2({
        placeholder: "City You Live In",
        allowClear: true,
        ajax: {
            url: "/ajax/lead-sa-cities",
            dataType: "json",
            type: "GET",
            data: function (params) {
                return {
                    term: params.term || '',
                    country_id: $("#current_country").val() || null
                };
            },
            processResults: function (data) {
                return {
                    results: data.map(item => ({
                        id: item.id,
                        text: item.text
                    }))
                };
            }
        }
    });

    $('body').on('click', '.sa-js-open-lead-form', function (e) {
        // $(".pageMask").css("display", "block");
        $("body").css("overflowY", "hidden");
        document.querySelector('body').style.position = 'fixed';
        document.querySelector('body').style.top = '0';
        document.querySelector('body').style.bottom = '0';
        document.querySelector('body').style.right = '0';
        document.querySelector('body').style.left = '0';
        // var leadFormTitle = this.dataset.leadformtitle;
        var url = document.querySelectorAll('meta[property="og:url"]')[0].content;
        document.querySelector('#gmusaleads-entity').value = this.dataset.entity ?? 'study-abroad-article';
        document.querySelector('#gmusaleads-entity_id').value = this.dataset.entityid ?? null;
        document.querySelector('#gmusaleads-cta_location').value = this.dataset.ctalocation;
        document.querySelector('#gmusaleads-cta_text').value = this.dataset.ctatext;
        document.querySelector('#gmusaleads-url').value = url;
        document.querySelector("#gmusaleads-durl").value = this.dataset.durl ?? ''
        document.querySelector("#gmusaleads-dynamic_redirection").value = this.dataset.dynamic_redirection ?? ''

        // if (this.dataset.subheadingtext) {
        //     $('.subHeadingText').html(this.dataset.subheadingtext);
        // } else {
        //     $('.subHeadingText').html('Get details and latest updates');
        // }

        // if (leadFormTitle) {
        //     $('.headingText').append(this.dataset.leadformtitle);
        // } else {
        //     $('.headingText').append('REGISTER NOW TO APPLY');
        // }

        $("#leadFormInputs").show();
        $(".optSection").hide();
        $(".engagementPanel").hide();
        $(".thankYouMsg").hide();
        $(".leadFormContainer").show();
    });

    //lead validations starts
    //email validation
    if (document.querySelector("#gmusaleads-email") !== null) {
        document.querySelector("#gmusaleads-email").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
            if (e.target.value.match(/@/g)) {
                if ((e.target.value.match(/\./g) || []).length > 2) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            } else {
                if ((e.target.value.match(/\./g) || []).length > 1) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            }

            var email = $('#gmusaleads-email').val();
            let strLst = email.slice(email.indexOf("@") + 1, email.length)

            if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
                $(".errorMsgSaEmail").html("Email is not a valid email address.");
                $('#gmusaleads-phone').prop("disabled", true);
                // $('.OTPField').prop("disabled", true);
                // $('.sendOtpButton').css("pointer-events", "none");
                // $('.validEmailIcon').css("display", "none");
            } else {
                $(".errorMsgSaEmail").html("");
                $('#gmusaleads-phone').prop("disabled", false);
            }
        })
    }

    //name validation
    $("#gmusaleads-name").on("input", function (e) {
        var inputValue = $(this).val();
        var regex = /^[A-Za-z ]+$/;

        if (!regex.test(inputValue)) {
            var nameValue = inputValue.replace(/[^A-Za-z ]/g, '');
            $(this).val(nameValue);
        }
    });

    //lead form mobile validation
    // if (document.querySelector("#gmusaleads-phone") !== null) {
    //     document.querySelector("#gmusaleads-phone").addEventListener('input', (e) => {
    //         e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '')
    //     });
    // }

    document.querySelectorAll(".nextInput").forEach(input => {
        input.addEventListener("input", () => input.value = input.value.replace(/\D/g, ""));
    });
    //lead validations ends


    $('body').on('click', '.submit-sa-lead-form', function (event) {
        console.log($("#gmusaleads-phone").val(), $("#hidden_number").val())
        event.preventDefault();
        var form = $('#firstScreenStudyAbroad');
        var countryCode = document.querySelector('.iti__selected-dial-code')?.textContent || '';

        // Set hidden field value
        form.find('#country_code_full').val(countryCode);

        $.ajax({
            url: '/studyabroadlead/create',
            data: form.serialize(),
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                $('.primaryBtn').prop('disabled', false)
                displayErrorsLead('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (data) {
                if (data.success == true) {
                    $(".saScreeOne").hide();
                    if (window.innerWidth <= 768) {
                        $(".leadFormContainer").addClass("sa-mobile-lead-form");
                    }
                    console.log(gmu.config.isLoggedIn, $("#gmusaleads-phone").val(), $("#hidden_number").val(), curentCountryCode)
                    if (gmu.config.isLoggedIn == true && ($("#gmusaleads-phone").val() == $("#hidden_number").val())) {
                        $(".saThankYouMsg").show();
                        $(".saThankYouText").html("Thank you for your response.")
                    } else {
                        sendOtp(data.student_id, 'send-otp')
                    }
                    localStorage.setItem('sa_student_id', data.student_id)
                } else {
                    displayErrorsLead(data.message)
                }
            }
        });
    });

    $('body').on('click', '#sa-resendOtp', function (event) {
        event.preventDefault();
        var student_id = localStorage.getItem('sa_student_id');
        sendOtp(student_id, 'resend_otp');
    });

    $('body').on('click', '.sa-verifyOtp', function (event) {
        event.preventDefault();
        var sa_student_id = localStorage.getItem('sa_student_id');

        let otp = "";

        // Collect all OTP digits
        $(".nextInput").each(function () {
            otp += $(this).val();
        });

        // Ensure OTP has 4 digits before sending
        if (otp.length !== 4) {
            $("#otpResponseText").text("Please enter a valid 4-digit OTP.");
            return;
        }

        // Clear any previous error
        $("#otpResponseText").text("");


        $.ajax({
            url: '/studyabroadlead/sa-verify-otp',
            data: { student_id: sa_student_id, otp: otp },
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                $('.primaryBtn').prop('disabled', false)
                $("#otpResponseText").text('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (response) {
                if (response.success == true) {
                    $("#otpResponseText").text("OTP Verified Successfully!");
                    $(".optSectionSa").hide();
                    $(".saThankYouText").html("Thank you for your response.")
                    $(".saThankYouMsg").show();
                } else {
                    $("#otpResponseText").text(response.message);
                }
            },
            error: function () {
                $("#otpResponseText").text("Error verifying OTP. Please try again");
            }
        });
    });


    $('body').on('click', '.closeLeadForm', function (event) {
        document.querySelector('#sa-lead-form-js').style.display = "none";
        $(".headingText").html("");
        $(".errorMsgSaEmail").html("");
        $('.inputCountryContainer select option').remove();
        $('.inputsaCityContainer select option').remove();
        $('.inputstudyDestination select option').remove();
        $('.planningDuration select option').remove();
        $('.intrestredDegreeSelect select option').remove();
        $("#current_sa_city").prop("disabled", true);
        $(".pageMask").css("display", "none");
        $(".optSectionSa").css("display", "none");
        $("body").css("overflowY", "unset");
        document.querySelector('body').style.position = 'unset';
        document.querySelector('body').style.height = 'unset';
        getScrollPosition();
        document.querySelector('body').style.bottom = 'unset';
        document.querySelector('body').style.right = 'unset';
        document.querySelector('body').style.left = 'unset';
        $('.signupModalFormStudyAbroad').trigger("reset");

        if ($(".saScreeOne").is(":visible")) {
            document.querySelector('#sa-lead-form-js').style.display = "none";
            return false;
        }

        activateSaStudentSession();
    })


    function activateSaStudentSession() {
        var student_id = localStorage.getItem('sa_student_id') ?? '';
        var csrf = $("input[name='_csrf-frontend']").val();
        $.ajax({
            url: '/studyabroadlead/sa-student-session-activate',
            data: { student_id: student_id },
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                $('.primaryBtn').prop('disabled', false)
                displayErrorsLead('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (data) {
                if (data.success == true) {
                    setCookie('gmu_leadform', 1, 10);
                    localStorage.removeItem('student_id');
                    location.reload(true);
                } else {
                    document.querySelector('#sa-lead-form-js').style.display = "none";
                }
            }
        });
    }

    function sendOtp(sa_student_id, param) {
        console.log(document.querySelector('.iti__selected-dial-code').textContent, "hi")
        if (document.querySelector('.iti__selected-dial-code').textContent !== '+91') {
            $(".saThankYouMsg").show();
            $(".saThankYouText").html("Thank you for your response.")
            return;
        }

        console.log(document.querySelector('.iti__selected-dial-code').textContent, "hi`")

        $(".optSectionSa").show();

        $.ajax({
            url: '/studyabroadlead/sa-send-otp',
            data: { student_id: sa_student_id, param: param },
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                $('.primaryBtn').prop('disabled', false)
                displayErrorsLead('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
        });
    }

});