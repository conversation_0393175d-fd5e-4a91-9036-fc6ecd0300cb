var mobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

//get field info
if (window.location.href.indexOf("/news") > -1 && mobileDevice && gmu.config.page_category == 'news-cta') {
    //news leadform using js
    $.get('/news-lead-form', {}, function (leadFormContainerNews) {
        $("#lead-form-js-new").html(leadFormContainerNews);
        inputErrorClearonFocusLeadForm()

        $('.streamCategory select').select2({
            placeholder: "Stream Interested",
            name: 'stream',
            ajax: {
                url: "/ajax/lead-stream",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                        entity: gmu.config.entity,
                        entity_id: gmu.config.entity_id,
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        $('.selectCity select').select2({
            placeholder: "City you live in",
            name: 'current_city',
            ajax: {
                url: "/ajax/lead-cities",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    var states = data.states || [];
                    var results = states.map(function (state) {
                        return {
                            text: state.state_name,
                            children: state.cities.map(function (city) {
                                return {
                                    id: city.id,
                                    text: city.text
                                };
                            })
                        };
                    });

                    return { results: results };
                },
            },
        });

        // Function to enable/disable the select dropdown
        function toggleLevelSelectNews(enabled) {
            var $levelSelect = $('#selectLevelNews'); // select dropdown
            if (enabled) {
                $levelSelect.prop('disabled', false);
            } else {
                $levelSelect.prop('disabled', true);
            }
        }

        $('.streamCategory select').on('change', function (e) {
            toggleLevelSelectNews(true);
        });

        $('.levelCategory select').select2({
            placeholder: "Level Interested",
            name: 'inputLevel',
            ajax: {
                url: "/ajax/lead-level",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                        entity: gmu.config.entity,
                        stream_id: $("#selectStreamNews").val(), // selected stream ID
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        //article page lead auto capture
        $("body").on('click', '.newsLeadValue', function () {
            var articleId = $(this).attr("data-entityid");
            var entity = gmu.config.entity;

            fetchArticleStreamLevelNews(articleId, entity);
        });

        function fetchArticleStreamLevelNews(articleId, entity) {
            if (articleId == '') {
                return '';
            }
            $.ajax({
                type: 'POST',
                url: '/ajax/article-stream-level',
                data: { id: articleId, entity: entity },
                dataType: "json",
                success: function (response) {
                    if (response.success === true && response.stream_id !== "" || response.level !== '') {
                        toggleStreamFieldArticleNews(response.stream_id, response.level);
                    }

                }
            });
        }

        function toggleStreamFieldArticleNews(stream_id = '', level = '') {
            let highest_qualification_degree_id = ['9', '10', '11'];
            if (stream_id == '' || stream_id == '22' || $.inArray(level, highest_qualification_degree_id) !== -1) {
                $(".inputStreamContainer").show();
            } else {
                $(".streamCategory").hide();
                $('.streamCategory select').append($('<option>', {
                    value: stream_id,
                    selected: true
                }));
            }
            toggleLevelNews(level);
            EnableSubmitButtonForloggedInUsersNews();
        }

        function toggleLevelNews(level) {
            if (level !== '' && level !== '12') {
                $(".levelCategory").hide();
                $('.levelCategory select').append($('<option>', {
                    value: level,
                    selected: true,
                })).prop('disabled', false);
            } else {
                $(".levelCategory").show();
                $("#selectLevelNews").prop("disabled", false);
            }
        }

        function loadNewsLeadData(lead) {
            if (lead == 1) {
                $('.headingText').html("");
                document.querySelector('input[name="utm_source"]').value = gmu.config.utm_source ?? '';
                document.querySelector('input[name="utm_medium"]').value = gmu.config.utm_medium ?? '';
                document.querySelector('input[name="utm_campaign"]').value = gmu.config.utm_campaign ?? '';
            }
            var url = document.querySelectorAll('meta[property="og:url"]')[0].content;
            var ctaLoaction = lead !== 1 ? lead.dataset.ctalocation : 'auto-pop-up';
            var ctaText = lead !== 1 ? lead.dataset.ctatext : 'Auto Pop Up';
            var entity = lead !== 1 ? gmu.config.entity : 'news';
            var entity_id = lead !== 1 ? gmu.config.entity_id : 0;
            var url = lead !== 1 ? window.location.href : url;
            var sub_type = lead !== 1 ? gmu.config.entity_subtype : 'detail-page';

            $(".pageMask").css("display", "block");
            $("body").css("overflowY", "hidden");
            $(".leadFormContainerNews, #signup-form-news").css("display", "block");
            $('input[name="entity"]').val(entity);
            $('input[name="entity_id"]').val(entity_id);
            $('input[name="url"]').val(url);
            $('input[name="entity_sub_type"]').val(sub_type);
            $('input[name="cta_location"]').val(ctaLoaction);
            $('input[name="cta_text"]').val(ctaText);
            $('input[name="is_lead"]').val("yes");

            if (lead !== undefined && lead !== 1 && lead.dataset.subheadingtext) {
                $('.subHeadingText').html(lead.dataset.subheadingtext);
            } else {
                $('.subHeadingText').html('Get details and latest updates');
            }

            if (lead !== undefined && lead !== 1 && lead.dataset.leadformtitle) {
                $('.headingText').append(lead.dataset.leadformtitle);
            } else {
                $('.headingText').append('REGISTER NOW TO APPLY');
            }
        }

        //auto pop up
        if (getCookie('gmu_leadform') != '1') {
            var leadTimeOut = setTimeout(() => {
                isModalOpenNews = true;
                document.addEventListener('touchmove', preventDefaultScrollNews, { passive: false });
                $(".leadFormContainerNews").css("display", "block");
                $(".pageMask").css("display", "block");
                $("body").css("overflow-y", "hidden");
                $(".headerCTAPair").css("z-index", 0);
                document.querySelector('#lead-form-js-new').style.display = "block";
                var articleId = gmu.config.entity_id;
                var entity = gmu.config.entity;
                loadNewsLeadData(1);
                fetchArticleStreamLevelNews(articleId, entity);
            }, 10000);
        }

        $("body").on('click', '.loginNews', function () {
            $(".leadFormContainerNews").css("display", "none");
            $(".subscribeSectionNews").css("display", "block");
        });

        $("body").on('click', '.logInOptionNews', function () {
            $(".leadFormContainerNews, #signup-form-news").css("display", "block");
            $(".subscribeSectionNews").css("display", "none");
        });

        $("body").on('click', '.changeNumber', function () {
            $(".leadFormContainerNews, #login-form-news").css("display", "none");
            $(".subscribeSectionNews, #otp-form-news").css("display", "block");
        });

        //open news lead form
        $('body').on('click', '.js-open-lead-form-news', function (e) {
            isModalOpenNews = true;
            document.addEventListener('touchmove', preventDefaultScrollNews, { passive: false });
            $(".headerCTAPair").css("z-index", 0);
            $('#signup-form-news').trigger("reset");
            clearTimeout(leadTimeOut);
            $(".headingText").html("");
            $(".leadFormContainerNews .leadFormDiv .userInputs .row .col-md-6 select").css("color", "#989898");
            document.querySelector('#lead-form-js-new').style.display = "block";
            loadNewsLeadData(this);
        });

        //name validation
        $(".txtOnlyNews").keypress(function (e) {
            var key = e.keyCode;
            var regex = /^[A-Za-z ]+$/;
            var isValid = regex.test(String.fromCharCode(key));
            if (!isValid) {
                e.preventDefault();
            }
        });

        $('.otpInputsNews').find('input').each(function () {
            $(this).attr('maxlength', 1);
            $(this).on('keyup', function (e) {
                var id = $(this).attr('id')
                var parent = $($(this).parent());

                if (e.keyCode === 8 || e.keyCode === 37) {
                    var prev = parent.find('input#' + $(this).data('previous'));
                    $("#" + id).attr('data-count', 1);

                    if (prev.length) {
                        $(prev).select();
                    }
                } else if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 96 && e.keyCode <= 105) || e.keyCode === 39) {
                    var next = parent.find('input#' + $(this).data('next'));

                    if (next.length) {
                        $(next).select();
                    } else {
                        if (parent.data('autosubmit')) {
                            parent.submit();
                        }
                    }
                } else {
                    $(this).val('');
                }
            });
        });

        $('.digit').on('keypress', function (e) {
            var id = $(this).attr('id')
            var dataCount = $("#" + id).attr('data-count');
            var count = parseInt(dataCount) + 1;
            $("#" + id).attr('data-count', count);
            if (count > 2) {
                e.preventDefault();
            }
        });

        //resend otp
        var resend = true;
        $("body").on('click', '#resendOtp', function (e) {
            e.preventDefault();
            if (!resend) {
                return false;
            }
            $.post('/ajax/resend-otp', $("#lead-form").serialize(), function (response) {
                if (response.success) {
                    resend = false;
                    setResendInterval();
                }
            })
        });

        var $htmlOrBody = $('html, body'), // scrollTop works on <body> for some browsers, <html> for others
            scrollTopPadding = 8;

        $('.otp-phone-news').focus(function () {
            // get textarea's offset top position
            var textareaTop = $(this).offset().top;
            // scroll to the textarea
            $htmlOrBody.scrollTop(textareaTop - scrollTopPadding);
        });

        function EnableSubmitButtonForloggedInUsersNews() {
            if (gmu.config.isLoggedIn == true) {
                if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainerNewsMobile .errorMsg").html() == '') !== 0 && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0) {
                    submitButtonLead.removeAttribute('disabled');
                } else {
                    submitButtonLead.setAttribute('disabled', true);
                }
            }
        }

        //validtion for mobile
        $(".otp-phone-news, .signup-phone-news").keypress(function (e) {
            var mobNum = $(this).val();
            var key = e.keyCode;
            var regex = /^[0-9]+$/;
            var isValid = regex.test(String.fromCharCode(key));
            if (!isValid || (
                jQuery.inArray(String.fromCharCode(key), ['9', '8', '7', '6']) == -1 &&
                mobNum.length == 0)) {
                e.preventDefault();
            } else {
                if (mobNum.length >= 10) {
                    e.preventDefault();
                }
            }
        });

        //validtion for mobile
        if (document.querySelector(".signup-phone-news") !== null) {
            document.querySelector(".signup-phone-news").addEventListener('input', (e) => {
                e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '');
                if (e.target.value.length < 10) {
                    $(".errorMsgMobile").html("phone number should contain 10 digits");
                } else {
                    $(".errorMsgMobile").html("");
                }
            });
        }

        //email validation
        document.querySelector(".emailTxtOnlyNews").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
            if (e.target.value.match(/@/g)) {
                if ((e.target.value.match(/\./g) || []).length > 2) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            } else {
                if ((e.target.value.match(/\./g) || []).length > 1) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            }
            var email = $('.emailTxtOnlyNews').val();
            let strLst = email.slice(email.indexOf("@") + 1, email.length)

            // String contains "@" after the first character and "@" is not the last character
            if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
                $(".errorMsgEmailNews").html("Email is not a valid email address.");

            } else {
                $('.errorMsgEmailNews').html('');
            }
        })

        //Function to check if all fields are filled
        $(".streamCategory, .levelCategory, .newsMobileName, .newsMobilePhone, .newsMobileCity, .inputEmailContainerNewsMobile").bind("change keyup", function (event) {
            if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainerNewsMobile .errorMsgEmailNews").html() == '') && mobileInputLead.value.length == 10 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0) {
                submitButtonLead.removeAttribute('disabled');
            } else {
                submitButtonLead.setAttribute('disabled', true);
            }
        });

        $("#otp-form-news").submit(function (e) {
            e.preventDefault()
            displayErrorsLead()
            var csrf = $("input[name='_csrf-frontend']").val();
            var phone = $("#otp-phone-news").val();
            $.ajax({
                url: '/site/send-otp',
                data: { phone: phone, '_csrf-frontend': csrf },
                dataType: 'json',
                method: 'POST',
                beforeSend: function () {
                    $('.primaryBtn').prop('disabled', true);
                },
                error: function (xhr, err) {
                    $('.primaryBtn').prop('disabled', false)
                    displayErrors('Something went wrong, please try again!')
                },
                complete: function () {
                    $('.primaryBtn').prop('disabled', false);
                },
                success: function (data) {
                    if (data.success == true) {
                        $('.subscribeSectionNews, #login-form-news').css("display", "block");
                        $('#mobileNum').html($('#otp-phone-news').val())
                        $('#otp-form-news').hide()
                        $('#login-form-news').show()
                        otpTimer(data.data.expiresIn);
                    }
                    displayErrors(data.message)

                }
            });
        });

        $("#signup-form-news").submit(function (e) {
            e.preventDefault()
            displayErrorsLead()
            var form = $(this)
            $.ajax({
                url: '/site/signup',
                data: form.serialize(),
                dataType: 'json',
                method: 'POST',
                beforeSend: function () {
                    $('.primaryBtn').prop('disabled', true);
                },
                error: function (xhr, err) {
                    $('.primaryBtn').prop('disabled', false)
                    displayErrors('Something went wrong, please try again!')
                },
                complete: function () {
                    $('.primaryBtn').prop('disabled', false);
                },
                success: function (data) {
                    if (data.success == true) {
                        if (data.isLead == 1 && $("input[name='is_lead']").val() == "yes" && data.loggedIn == 1 && data.numberChange == 0) {
                            $('#otp-form-news, #login-form-news').css("display", "none");
                            $('.thankYouMsgNews, .subscribeSectionNews').css("display", "block");
                        }
                        if (data.isLead !== 1 || data.numberChange == 1) {
                            $('.subscribeSectionNews, #login-form-news').css("display", "block");
                            $('#otp-form-news').css("display", "none");
                            $('#otp-phone-news').val($('#signup-phone-news').val())
                            $("#otp-form-news").submit();
                        }

                        if (data.nameChange == 1 && data.isLead == 1 && data.numberChange == 1 && data.sessionStart == 0) {
                            location.reload(true);
                        }
                        $('#signup-form-news').hide()
                    } else {
                        displayErrors(data.message)
                    }

                }
            });

        });

        $("#login-form-news").submit(function (e) {
            e.preventDefault()
            displayErrorsLead()
            var otp = $("input[name='digit[]']").map(function () { return $(this).val(); }).get().join('')
            $('input[name="otp"]').val(otp)
            $('#login-phone-news').val($('#otp-phone-news').val())
            var form = $(this)
            $.ajax({
                url: '/site/verify-otp-news',
                data: form.serialize(),
                dataType: 'json',
                method: 'POST',
                beforeSend: function () {
                    $('.primaryBtn').prop('disabled', true);
                },
                error: function (xhr, err) {
                    $('.primaryBtn').prop('disabled', false)
                    displayErrors('Something went wrong, please try again!')
                },
                complete: function () {
                    $('.primaryBtn').prop('disabled', false);
                },
                success: function (data) {
                    if (data.success == true) {
                        $('.thankYouMsgNews, .closeLeadFormContainerThankYou').css("display", "block");
                        $('.leadFormContainerNews, #login-form-news, .pageMask, .closeLeadFormContainer').css("display", "none");
                        setCookie('gmu_leadform', 1, 10);
                        displayErrors(data.message)
                    } else {
                        displayErrors(data.message)
                    }

                }
            });
        });

    });

    // $("body").on('click', '.closeLeadFormContainerThankYou , .closeLeadForm', function () {
    //     document.body.style.height = 'auto';
    //     document.body.style.overflowY = 'auto';
    //     location.reload(true);
    // })

    //news lead form changes
    $('body').on("change", '[name="stream"], [name="level"]', function () {
        var idName = this.id;
        $("#" + idName).css("color", "#333333")
    });
} else {
    if (window.location.href.includes('/user-profile') == false) {
        $.get('/lead-form-new', {}, function (leadFormContainerV2Lead) {
            $("#lead-form-js-new").html(leadFormContainerV2Lead);
            inputErrorClearonFocusLeadForm();

            $('.inputMobileContainer select').select2({
                dropdownParent: $('.mobileContainerCodeDiv'),
                placeholder: "+91",
                minimumResultsForSearch: -1
            });

            $('.inputCityContainer select').select2({
                placeholder: 'City You Live In',
                ajax: {
                    url: "/ajax/lead-cities",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        var queryParameters = {
                            term: params.term
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        var states = data.states || [];
                        var results = states.map(function (state) {
                            return {
                                text: state.state_name,
                                children: state.cities.map(function (city) {
                                    return {
                                        id: city.id,
                                        text: city.text
                                    };
                                })
                            };
                        });

                        return { results: results };
                    },
                },
            });

            $('.destinationContainer select').select2({
                dropdownParent: $('.destinationContainer'),
                placeholder: 'Select upto 3 Destinations',
                maximumSelectionLength: 3,
                ajax: {
                    url: "/ajax/lead-cities",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        var queryParameters = {
                            term: params.term
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        var states = data.states || [];
                        var results = states.map(function (state) {
                            return {
                                text: state.state_name,
                                children: state.cities.map(function (city) {
                                    return {
                                        id: city.id,
                                        text: city.text
                                    };
                                })
                            };
                        });

                        return { results: results };
                    },
                },
            });

            $('.specializationContainer select').select2({
                dropdownParent: $('.specializationContainer'),
                placeholder: "Select upto 5 Specializations",
                name: 'inputStream',
                maximumSelectionLength: 5,
                ajax: {
                    url: "/ajax/lead-specialization",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        if (gmu.config.entity == 'college-listing') {
                            var entity_id = $("#leadform-entity_id").val();
                            var entity = gmu.config.entity;
                        } else if (gmu.config.entity == 'college-predictor') {
                            var entity_id = $("#leadform-entity_id").val();
                            var entity = $("#leadform-entity").val();
                        } else {
                            var entity_id = gmu.config.entity_id;
                            var entity = gmu.config.entity;
                        }

                        var queryParameters = {
                            term: params.term,
                            entity: entity,
                            entity_id: entity_id,
                            stream_id: document.querySelector('#interested_stream_lead').value,
                            level: document.querySelector('#interested_level_lead').value
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('.inputStreamContainer select').select2({
                placeholder: "Stream Interested",
                name: 'inputStream',
                ajax: {
                    url: "/ajax/lead-stream",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        if (gmu.config.entity == 'college-listing') {
                            var entity_id = $("#leadform-entity_id").val();
                            var entity = gmu.config.entity;
                        } else if (gmu.config.entity == 'college-predictor') {
                            var entity_id = $("#leadform-entity_id").val();
                            var entity = $("#leadform-entity").val();
                        } else {
                            var entity_id = gmu.config.entity_id;
                            var entity = gmu.config.entity;
                        }

                        var queryParameters = {
                            term: params.term,
                            entity: entity,
                            entity_id: entity_id,
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('.inputLevelContainer select').on('change', function (e) {
                toggleCollegeSpecialization();
            });

            $('.inputStreamContainer select').on('change', function (e) {
                if (gmu.config.entity == 'college' || gmu.config.entity == 'college-listing') {
                    $('#interested_level_lead').val('').trigger('change');
                    if (gmu.config.entity == 'college-listing') {
                        var id = localStorage.getItem('collegeId');
                        var entity = gmu.config.entity;
                    } else if (gmu.config.entity == 'college-predictor') {
                        var id = $("#leadform-entity_id").val();
                        var entity = $("#leadform-entity").val();
                    }
                    else {
                        var id = gmu.config.entity_id;
                        var entity = gmu.config.entity;
                    }


                    $.ajax({
                        url: '/ajax/lead-level',
                        data: {
                            entity: entity,
                            stream_id: $("#interested_stream_lead").val(), // selected stream ID
                            entity_id: id
                        },
                        dataType: "json",
                        type: "GET",
                        success: function (data) {
                            if (data && data.length === 1) {
                                var $option = $("<option selected></option>").val(data[0].id).text(data[0].text);
                                $('#interested_level_lead').append($option).trigger('change');
                            }
                        }
                    });
                }
                toggleLevelSelect(true);
            });

            $('.inputLevelContainer select').select2({
                placeholder: "Level Interested",
                name: 'inputLevel',
                ajax: {
                    url: "/ajax/lead-level",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        if (gmu.config.entity == 'college-listing') {
                            var id = localStorage.getItem('collegeId');
                            var entity = $("#leadform-entity").val();
                        } else if (gmu.config.entity == 'college-predictor') {
                            var id = $("#leadform-entity_id").val();
                            var entity = $("#leadform-entity").val();
                        } else {
                            var id = gmu.config.entity_id;
                            var entity = $("#leadform-entity").val();
                        }

                        var queryParameters = {
                            term: params.term,
                            entity: entity,
                            stream_id: $("#interested_stream_lead").val(), // selected stream ID
                            entity_id: id,
                            page_name: gmu.config.pageName ?? ''
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('#interested_specialization_lead, #interested_location').each(function () {
                $(this).on('select2:select', function () {
                    let ul = $(this).next().find('ul');
                    ul.scrollLeft(5000);
                })
            })

            let downloadFiles = []; // Store files for downloading after lead form submission
            let downloadCategory = '';

            // Store download files when clicking on the download button
            $('body').on('click', '.download-btn', function (e) {
                downloadFiles = $(this).attr("data-files")?.split(",") || [];
                downloadCategory = $(this).attr("data-category") || "unknown-category";
            });

            //CTA on click open the lead form
            $('body').on('click', '.js-open-lead-form-new', function (e) {
                if (this.dataset.clickbutton != undefined) {
                    localStorage.setItem('latestvalue', this.dataset.latestvalue ?? '');
                    localStorage.setItem('programid', this.dataset.programid ?? '');
                    localStorage.setItem('round', this.dataset.round ?? '');
                    localStorage.setItem('type', this.dataset.type ?? '');
                    localStorage.setItem('collegeidCutOff', this.dataset.entityid ?? '');
                    localStorage.setItem('programname', this.dataset.programname ?? '');
                    localStorage.setItem('coursename', this.dataset.coursename ?? '');
                    localStorage.setItem('clickbutton', this.dataset.clickbutton ?? '');
                }

                isModalOpenLead = true;
                let ctaLocation = $(this).data('ctalocation');
                let ctaText = $(this).data('ctatext');
                let ctaidentity = $(this).data('cta-identity');
                let ctaIndex = $(this).data('cta-index');
                let ctaTabIndex = $(this).data('tab');
                let subtopicId = $(this).data('subtopic-id');
                let indexcorrectanswer = $(this).data('index-correct-answer');
                localStorage.setItem('ctaData', JSON.stringify({
                    'ctaLocation': ctaLocation,
                    'ctaText': ctaText
                }));
                document.addEventListener('touchmove', preventDefaultScrollLead(event), { passive: false });
                e.preventDefault();
                setScrollPosition();

                if (typeof autoPopUpTimeOut !== 'undefined' && autoPopUpTimeOut !== null) {
                    clearTimeout(autoPopUpTimeOut);
                }

                $('.textDivHeading').html("");
                $('.textDivSubHeading').html("");

                document.querySelector('#lead-form-js-new').style.display = "block";
                document.querySelector('.pageMask').style.display = "block";

                var leadFormTitle = this.dataset.leadformtitle ?? '';
                document.querySelector('#cta_location').value = this.dataset.ctalocation ?? '';
                document.querySelector('#cta_text').value = this.dataset.ctatext ?? '';
                document.querySelector('#cta_title').value = this.dataset.ctatitle ?? '';
                document.querySelector('#alternate-media').value = this.dataset.alternatemedia ?? '';
                document.querySelector('#alternate-pageSlug').value = this.dataset.alternatepageredirectslug ?? '';
                document.querySelector('#entity_subtype').value = gmu.config.entity_subtype;
                document.querySelector('#entity_type').value = gmu.config.entity_type;
                document.querySelector('#leadform-entity').value = gmu.config.entity == 'college-predictor' ? this.dataset.entity : gmu.config.entity;
                document.querySelector('#leadform-entity_id').value = gmu.config.entity == 'college-predictor' ? this.dataset.entityid : gmu.config.entity_id;
                document.querySelector("#durl").value = this.dataset.durl ?? '';
                document.querySelector("#dynamic_redirection").value = this.dataset.dynamic_redirection ?? '';
                document.querySelector("#activity_id").value = sessionStorage.getItem('activity_id') ?? '';
                document.querySelector("#ctaIdentity").value = ctaidentity ?? '';
                document.querySelector("#ctaIndex").value = ctaIndex ?? '';
                document.querySelector("#ctaTabIndex").value = ctaTabIndex ?? '';
                document.querySelector("#ctaSubTopic").value = subtopicId ?? '';
                document.querySelector("#indexcorrectanswer").value = indexcorrectanswer ?? '';

                if (ctaidentity == 'attempt_more_question') {
                    $('#secondScreenSubmit').text('Attempt More Question');
                } else if (ctaidentity == 'check_explanation') {
                    $('#secondScreenSubmit').text('Check Explanation');
                } else if (ctaidentity == 'view_correct_answer') {
                    $('#secondScreenSubmit').text('View Correct Answer');
                } else if (ctaidentity == 'list_practice_tab') {
                    $('#secondScreenSubmit').text('Practice Set');
                } else if (ctaidentity == 'download_pdf') {
                    $('#secondScreenSubmit').text('Download Pdf');
                } else {
                    $('#secondScreenSubmit').text('Next');
                }

                //exam widget functionality
                if (gmu.config.isLoggedIn == true) {
                    var cta_text = $("#cta_text").val();
                    var tempDiv = document.createElement('div');
                    tempDiv.innerHTML = cta_text;

                    var spanElement = tempDiv.querySelector('span');
                    if (spanElement) {
                        var spanClasses = spanElement.className;
                    }
                    var cta_title = $("#cta_title").val();
                    if (spanElement && (spanClasses.includes('whiteDownloadIcon') || spanClasses.includes('redDownloadIcon'))) {
                        mediaDriveDownload(cta_title);
                    }
                    if (this.dataset.ctalocation && this.dataset.ctalocation.includes("exam_widget_cta")) {
                        lastScreenDisplay("Our Expert will call you shortly and we will share important exam updates with you.");
                    } else {
                        mediaDriveDownload(cta_title);
                        lastScreenDisplay();

                    }
                }


                if (leadFormTitle && typeof leadFormTitle == 'string') {
                    $('.textDivHeading').append(leadFormTitle);
                } else {
                    $('.textDivHeading').append('REGISTER NOW TO APPLY');
                }
                webengage.track("lead_cta_clicked", {
                    "entity": gmu.config.entity,
                    "entity_name": gmu.config.entity_name,
                    "page_url": gmu.config.page_url,
                    "button_name": this.dataset.ctatext ?? '',
                    "button_id": this.dataset.ctatitle ?? ''
                });

            });

            //auto pop up
            function autoPoupShow() {
                const ctaData = JSON.parse(localStorage.getItem('ctaData')) ?? [];

                if (!($('#login-form-js').is(':visible'))) {
                    $('.textDivHeading').html("");
                    $('.textDivSubHeading').html("");
                    isModalOpenLead = true;
                    document.addEventListener('touchmove', preventDefaultScrollLead(), { passive: false });
                    if (($(".mobileMenu").width() / $('.mobileMenu').parent().width() * 100) != 100) {
                        var leadFormTitle = gmu.config.auto_popup_form_title ? gmu.config.auto_popup_form_title : (this.dataset !== undefined ? (this.dataset.leadformtitle ?? '') : '');
                        if (leadFormTitle && typeof leadFormTitle == 'string') {
                            $('.textDivHeading').append(leadFormTitle);
                        } else {
                            $('.textDivHeading').append('REGISTER NOW TO APPLY');
                        }

                        document.querySelector('#lead-form-js-new').style.display = "block";
                        document.querySelector('.pageMask').style.display = "block";
                        document.querySelector('#cta_location').value = document.querySelector('#cta_location').value !== '' ? ctaData['ctaLocation'] : gmu.config.cta_location ?? '';
                        // document.querySelector('#cta_text').value = 'Auto Pop Up';
                        document.querySelector('#entity_subtype').value = gmu.config.entity_subtype;
                        document.querySelector('#entity_type').value = gmu.config.entity_type;
                        document.querySelector("#activity_id").value = sessionStorage.getItem('activity_id') ?? '';
                        document.querySelector('#leadform-entity').value = gmu.config.entity;
                        document.querySelector('#leadform-entity_id').value = gmu.config.entity_id;
                        document.querySelector('#leadform-utm_source').value = gmu.config.utm_source ?? '';
                        document.querySelector('#leadform-utm_medium').value = gmu.config.utm_medium ?? '';
                        document.querySelector('#leadform-utm_campaign').value = gmu.config.utm_campaign ?? '';
                        document.querySelector('#course_id').value = gmu.config.course_id ?? '';
                        document.querySelector('#program_id').value = gmu.config.program_id ?? '';
                        $("body").css("overflowY", "hidden");
                        setScrollPosition()

                        if (gmu.config.entity == 'college-listing') {
                            var streamSlug = gmu.config.sponsor_params.stream;
                            var courseSlug = gmu.config.sponsor_params.course;
                        }

                        var auto_pop_entity_id = gmu.config.entity_id;
                        var entity = gmu.config.entity == 'board-sample-paper' ? 'board' : gmu.config.entity;

                        if (entity == '') {
                            return '';
                        }

                        var url = window.location.href;
                        var examSlug = '';
                        var matchingItems = '';

                        //get the first course type starts
                        var params = new URL(url).searchParams;
                        var courseType = params.get('course_type');

                        // If courseType is not null, get the first value
                        var matchingItems = collegeFilterExamCourseType(courseType);
                        if (matchingItems !== undefined) {
                            toggleLevel(matchingItems[0].value);
                        }
                        //get the first course type ends

                        //get the first exam selected starts
                        var pathname = new URL(url).pathname;
                        // Extract the string between "colleges-accepting-" and "-score-in-india"
                        var startIndex = pathname.indexOf("colleges-accepting-") + "colleges-accepting-".length;
                        var endIndex = pathname.indexOf("-score-in-india");
                        if (startIndex !== -1 && endIndex !== -1) {
                            var examSlug = pathname.substring(startIndex, endIndex);
                        }
                        //get the first exam selected ends

                        $.ajax({
                            type: 'POST',
                            url: '/ajax/lead-auto-pop-up',
                            data: { auto_pop_entity_id: auto_pop_entity_id, entity: entity, course: (courseSlug !== null ? courseSlug : ''), stream: (streamSlug !== undefined ? streamSlug : ''), examSlug: examSlug, course_type: matchingItems == undefined ? '' : matchingItems[0].value },
                            dataType: "json",
                            success: function (response) {
                                if (response.success == true) {
                                    if ((gmu.config.entity == 'exam' && gmu.config.pageName !== "exam-category") || gmu.config.entity == 'course' || gmu.config.entity == 'course_stream' || gmu.config.pageName == 'ci' || gmu.config.pageName == 'program') {
                                        if (response.success == true && response.stream_id !== "") {
                                            toggleStreamField(response.stream_id, response.level, response.specialization_id)
                                        }
                                    } else if (gmu.config.entity == 'exam' && gmu.config.pageName == "exam-category") {
                                        toggleStreamField(gmu.config.entity_id, '', '')
                                    } else if (gmu.config.entity == 'college-listing') {
                                        if (response.success == true && response.data.length !== 0 && response.data.stream_id !== "" && response.data.level !== "") {
                                            toggleStreamField(response.data.stream_id, response.data.level, response.specialization_id)
                                        } else if (response.success == true && response.data.length !== 0 && response.data.stream_id !== "" && response.data.level == "") {
                                            toggleStreamField(response.data.stream_id, response.data.level, response.specialization_id)
                                            toggleLevelSelect(true);
                                            EnableSubmitButtonForloggedInUsers()
                                        }
                                        if (response.data.exam_id !== "") {
                                            $("#college_filter_exam").val(response.data.exam_id)
                                        }
                                    } else if (gmu.config.entity == 'articles' || gmu.config.entity == 'news') {
                                        if (response.success == true && response.stream_id !== "") {
                                            toggleStreamField(response.stream_id, response.level)
                                        }
                                    } else if (gmu.config.entity == 'board') {
                                        toggleBoardLevel()
                                    }

                                    //auto fetch course, program and degree id
                                    if (gmu.config.pageName == 'ci' || gmu.config.pageName == 'program') {
                                        $("#course_id").val(gmu.config.course_id);
                                        $("#program_id").val(gmu.config.program_id);
                                        $("#page_name").val(gmu.config.pageName);
                                    } else if (gmu.config.entity == 'exam') {
                                        if (response.course_id !== "0") {
                                            $("#course_id").val(response.course_id);
                                        }
                                        $("#degree_id").val(response.level);
                                    }
                                }

                            }
                        });
                    }
                }
            }

            //lead auto fetch starts
            // college and course page lead auto capture
            $("body").on('click', '.leadCourseCapture', function () {
                var id = $(this).closest('.lead-cta').attr('id');
                var program = $(this).closest('.lead-cta').attr('data-program') ?? $(this).attr('data-program');
                var leadCta = $(this).closest('.lead-cta').attr('data-lead_cta');
                var location = $(this).closest('.lead-cta').attr('data-location');
                var description = $(this).closest('.lead-cta').attr('data-description');
                var image = $(this).closest('.lead-cta').attr('data-image');
                var entity = $("#leadform-entity").val();
                var product_mapping_entity = gmu.config.product_mapping_entity == "false" ? null : gmu.config.product_mapping_entity;
                localStorage.setItem('collegeProgramId', $(this).closest('.lead-cta').data('college_program_id'));
                localStorage.setItem('programName', $(this).closest('.lead-cta').attr('data-program'));
                if ($(this).closest('.lead-cta').attr('data-course') !== undefined) {
                    var course = $(this).closest('.lead-cta').attr('data-course');
                } else {
                    var course = gmu.config.entity == 'course' ? $(this).attr('data-entityid') : id;
                }

                if (gmu.config.pageName == "courses-fees") {
                    $("#course_id").val(id);
                    if (leadCta == 7 || leadCta == 8 || leadCta == 9 || leadCta == 24) {
                        var course = $(this).closest('.lead-cta').attr('data-courseId');
                        var program_slug = id;
                        $("#course_id").val(course);
                        $("#program_id").val($(this).closest('.lead-cta').attr('data-programId'));
                        $("#page_name").val(gmu.config.pageName);
                    }
                }

                if (gmu.config.pageName == 'ci' || gmu.config.pageName == 'program') {
                    var course = gmu.config.course_id;
                    var program_slug = gmu.config.pageName == 'ci' ? id : program;
                    if (leadCta == 14 || leadCta == 16) {
                        mobileDevice ? $("#leadform-cta_location").val('colleges_course_information_wap_lead_' + id + '_card_left_cta8') : $("#leadform-cta_location").val('colleges_course_information_web_lead_' + id + '_card_center_cta8');
                    }
                    if (leadCta == 15) {
                        mobileDevice ? $("#leadform-cta_location").val('colleges_course_information_web_lead_' + id + '_card_right_cta7') : $("#leadform-cta_location").val('colleges_course_information_web_lead_' + id + '_card_right_cta7');
                    }
                    if (leadCta == 17) {
                        mobileDevice ? $("#leadform-cta_location").val('colleges_program_information_' + program + '_wap_lead_mtf_cta1') : $("#leadform-cta_location").val('colleges_program_information_' + program + '_web_lead_mtf_cta1');
                    }
                    $("#course_id").val(gmu.config.course_id);
                    $("#program_id").val(gmu.config.program_id);
                    $("#page_name").val(gmu.config.pageName);
                } else if (gmu.config.entity == 'college' && gmu.config.college_course_count !== "") {
                    var course = gmu.config.college_course_count;
                    $("#course_id").val(gmu.config.college_course_count);
                } else if (gmu.config.pageName == 'info') {
                    $("#course_id").val(id);
                }

                //data pushed to lead attributes
                if (gmu.config.entity == 'course_stream' && gmu.config.pageName !== 'course-category') {
                    $("#leadform-cta_location").val(location);
                    $("#leadform-entity_id").val(id);
                    $(".subHeadingText").text(description);
                    $("#leadform-image").attr("src", image);
                } if (gmu.config.pageName == 'course-category') {
                    var stream = $(this).closest('.lead-cta').attr('data-streamid');
                    $(".subHeadingText").text(description);
                    $("#leadform-entity_id").val(id);
                }

                $.ajax({
                    type: 'POST',
                    url: '/ajax/lead-course-capture',
                    data: { id: course, program: program, entity: entity, product_mapping_entity: product_mapping_entity, program_slug: program_slug },
                    dataType: "json",
                    success: function (response) {
                        if (response.success == true && response.stream_id !== "") {
                            toggleStreamField(response.stream_id, response.level, response.specialization_id)
                        } else if (gmu.config.entity == 'course_stream' && response.success == false && stream !== '') {
                            toggleStreamField(stream, '')
                        } else {
                            $(".inputStreamContainer").show();
                            $(".inputLevelContainer").show();
                        }

                    }
                });
            });

            //exam page lead auto capture
            $("body").on('click', '.examLeadValue', function () {
                var examId = gmu.config.pageName == 'exam-category' ? $(this).closest('.lead-cta').attr('data-entityid') : $(this).attr("data-entityid");
                var location = $(this).closest('.lead-cta').attr('data-location');
                var description = $(this).closest('.lead-cta').attr('data-description');
                var image = $(this).closest('.lead-cta').attr('data-image');

                //data pushed to lead attributes filter page
                if (gmu.config.pageName == 'exam-category') {
                    $("#cta_location").val(location);
                    $("#leadform-entity_id").val(examId);
                    $(".subHeadingText").text(description);
                    $("#leadform-image").attr("src", image);
                }
                if (examId == '') {
                    return '';
                }
                $.ajax({
                    type: 'POST',
                    url: '/ajax/exam-stream-level',
                    data: { exam_id: examId },
                    dataType: "json",
                    success: function (response) {
                        if (response.success == true) {
                            toggleStreamField(response.stream_id, response.level, response.specialization_id);
                        }
                        $("#degree_id").val(response.level);
                        if (response.course_id !== "0") {
                            $("#course_id").val(response.course_id);
                        }
                    }
                });
            });

            //article page lead auto capture
            $("body").on('click', '.articleLeadValue, .newsLeadValue', function () {
                var articleId = $(this).attr("data-entityid");
                var entity = gmu.config.entity;

                if (articleId == '') {
                    return '';
                }
                $.ajax({
                    type: 'POST',
                    url: '/ajax/article-stream-level',
                    data: { id: articleId, entity: entity },
                    dataType: "json",
                    success: function (response) {
                        if (response.success === true && response.stream_id !== "" || response.level !== '') {
                            toggleStreamField(response.stream_id, response.level);
                        }

                    }
                });
            });

            //board page lead hide stream and level for 10th and open board
            $("body").on('click', '.leadBoardAutoCapture', function () {
                toggleBoardLevel();
            });

            //college filter page lead auto fetch
            $("body").on('click', '.collegeFilterLead', function () {
                var courseSlug = $(this).attr("data-course") ?? null;
                var id = $(this).closest('.leadFilterData').attr('data-slug');
                localStorage.setItem('collegeId', id);
                if ($(this).attr("data-entity") == 'all-colleges' && $(this).attr("data-entityid") == '0') {
                    var stream = $(this).attr("data-stream") ?? null;
                    var courseId = $(this).attr("data-courseSlug") ?? null;
                } else {
                    var stream = $(this).closest('.leadFilterData').attr('data-stream');
                    var courseId = $(this).closest('.leadFilterData').attr('data-course');
                }
                var location = $(this).closest('.leadFilterData').attr('data-ctalocation');
                var state = $(this).closest('.leadFilterData').attr('data-stateid');
                var city = $(this).closest('.leadFilterData').attr('data-cityid');
                var entity = $(this).closest('.leadFilterData').attr('data-filter') ?? '';
                var title = $(this).closest('.leadFilterData').attr('data-title');
                var description = $(this).closest('.leadFilterData').attr('data-description');
                var image = $(this).closest('.leadFilterData').attr('data-image');

                var url = window.location.href;
                var examSlug = '';
                var matchingItems = '';

                //get the first course type starts
                var params = new URL(url).searchParams;
                var courseType = params.get('course_type');
                var matchingItems = collegeFilterExamCourseType(courseType);
                if (matchingItems !== undefined) {
                    toggleLevel(matchingItems[0].value);
                }
                //get the first course type ends

                //get the first exam selected starts
                var pathname = new URL(url).pathname;

                // Extract the string between "colleges-accepting-" and "-score-in-india"
                var startIndex = pathname.indexOf("colleges-accepting-") + "colleges-accepting-".length;
                var endIndex = pathname.indexOf("-score-in-india");
                if (startIndex !== -1 && endIndex !== -1) {
                    var examSlug = pathname.substring(startIndex, endIndex);
                }
                //get the first exam selected ends

                $.ajax({
                    type: 'POST',
                    url: '/ajax/all-college-lead-values',
                    data: { college_id: id ?? '', course: courseId ?? '', courseSlug: courseSlug ?? '', stream: stream, examSlug: examSlug, course_type: matchingItems == undefined ? '' : matchingItems[0].value },
                    dataType: "json",
                    success: function (response) {
                        //data pushed to lead attributes
                        $("#interested_location").val(city);
                        $("#leadform-state_id").val(state);
                        $("#cta_location").val(location);
                        $("#leadform-entity_id").val(id);
                        $("#leadform-entity").val(entity);
                        $(".headingText").text(title);
                        $(".subHeadingText").text(description);
                        $("#leadform-image").attr("src", image);
                        $("#course_id").val(courseId);

                        if (response.success == true && response.data.length !== 0 && response.data.stream_id !== "" && response.data.level !== "") {
                            toggleStreamField(response.data.stream_id, response.data.level, response.specialization_id)
                        } else if (response.success == true && response.data.length !== 0 && response.data.stream_id !== "" && response.data.level == "") {
                            toggleStreamField(response.data.stream_id, response.specialization_id);
                            toggleLevelSelect(true);
                            EnableSubmitButtonForloggedInUsers()
                        } else if (response.success == true && response.data.length !== 0 && response.data.level !== "") {
                            $(".inputStreamContainer").show();
                            $(".inputLevelContainer").hide();
                            $('.inputLevelContainer select').append($('<option>', {
                                value: response.data.level,
                                selected: true,
                            })).prop('disabled', false);
                        } else if (response.data.length == 0) {
                            $(".inputStreamContainer").show();
                            $(".inputLevelContainer").show();
                        }
                        if (response.data.exam_id !== "") {
                            $("#college_filter_exam").val(response.data.exam_id)
                        }
                    }
                });
            });

            //lead auto fetch end

            //lead validations starts
            //email validation
            if (document.querySelector("#formEmail") !== null) {
                document.querySelector("#formEmail").addEventListener('input', (e) => {
                    e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
                    if (e.target.value.match(/@/g)) {
                        if ((e.target.value.match(/\./g) || []).length > 2) {
                            e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                        }
                    } else {
                        if ((e.target.value.match(/\./g) || []).length > 1) {
                            e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                        }
                    }

                    var email = $('#formEmail').val();
                    let strLst = email.slice(email.indexOf("@") + 1, email.length)

                    if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
                        $(".errorMsgEmail").html("Email is not a valid email address.");
                        $('.mobileNumberField').prop("disabled", true);
                        $('.OTPField').prop("disabled", true);
                        $('.sendOtpButton').css("pointer-events", "none");
                        $('.validEmailIcon').css("display", "none");
                    } else {
                        if (nameInputLead.value.length > 0 && streamSelectLead.value.length > 0 && levelSelectLead.value.length > 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0)) {
                            $('.mobileNumberField').prop("disabled", false);
                            $('.OTPField').prop("disabled", false);
                            $('.sendOtpButton').css("pointer-events", "unset");
                        } else {
                            $('.sendOtpButton').css("pointer-events", "unset");
                        }

                        $('.validEmailIcon').attr('style', 'display: block !important');
                        $('.errorMsgEmail').html('');
                    }
                })
            }

            //name validation
            $(".formName").on("input", function (e) {
                var inputValue = $(this).val();
                var regex = /^[A-Za-z ]+$/;

                if (!regex.test(inputValue)) {
                    var nameValue = inputValue.replace(/[^A-Za-z ]/g, '');
                    $(this).val(nameValue);
                }
            });

            //lead form mobile validation
            if (document.querySelector("#formMobile") !== null) {
                document.querySelector("#formMobile").addEventListener('input', (e) => {
                    e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '')
                });
            }
            //lead validations ends

            //get field info
            const nameInputLead = document.querySelector('#formName');
            const emailInputLead = document.querySelector('#formEmail');
            const mobileInputLead = document.querySelector('#formMobile');
            const streamSelectLead = document.querySelector('#interested_stream_lead');
            const levelSelectLead = document.querySelector('#interested_level_lead');
            const citySelectLead = document.querySelector('#leadform-interested_location');
            const cityInputIp = document.querySelector('#current_city_ip');
            const otpInputLead = document.querySelector('#OTPField');
            const specializationInputLead = document.querySelector('#interested_specialization_lead');
            const submitButtonLead = document.querySelector('#firstScreenSubmit');
            const submitButtonLeadSecondScreen = document.querySelector('#secondScreenSubmit');

            //Function to check if all fields are filled for submit button functionality || screen one
            $(".streamClass, .levelClass, .nameClass, .emailClass, .cityClass, .mobileNumberField, .OTPField").bind("change keyup", function (event) {
                if (gmu.config.isLoggedIn == false) {
                    if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0 && (otpInputLead.value.length == 4 && $(".inputOTPContainer .validationError").html() == '')) {
                        submitButtonLead.removeAttribute('disabled');
                    } else {
                        submitButtonLead.setAttribute('disabled', true);
                    }
                } else {
                    submitButtonFunctionality()
                }
            });

            //Function to check if all fields are filled for submit button functionality || screen two
            $(".admissionRadio, .specialization, .college_location, .budgetUserSelecion").bind("change click", function (event) {
                submitButtonFunctionality();
            });

            //Function to check if all fields are filled for mobile button functionality
            $(".streamClass, .levelClass, .nameClass, .emailClass, .cityClass").bind("change keyup", function (event) {
                mobileFieldFunctionality();
            });

            //remove the sponsor on change and add based on nely selected data
            $("body").on("change", "#educationBudget, #interested_stream_lead, #leadform-interested_location", function () {
                $('.sponsorCollegeLead').empty();
            });

            if (gmu.config.isLoggedIn == true) {
                localStorage.setItem('phone', $("input[name='phone']").val());
                $('.validEmailIcon').attr('style', 'display: block !important');
            }

            // SendOTPButton toggle
            var sendOtpButton = document.querySelector('.sendOtpButton');
            var OtpField = document.querySelector('.inputOTPContainer');
            if (document.querySelector('.mobileNumberField') !== null) {
                document.querySelector('.mobileNumberField').addEventListener('input', (e) => {
                    $("input[name='hiddenNumber']").val("");
                    if (e.target.value.length === 10) {
                        $(".otpclass .validationError").remove();
                        $('.otpclass input').removeClass('errorInputField');
                        $(".OTPField").attr('disabled', false);
                        if ($(".spriteIcon").hasClass("tickIcon") == true) {
                            $(".otpclass").append("<span class='otpTimer'></span>");
                            $(".otpclass").find(".tickIcon").remove();
                        }
                        document.querySelector('.inputOTPContainer').style.display = 'block';
                        if ($("input[name='hiddenNumber']").val() !== $("input[name='phone']").val()) {
                            $('.stepOne').click();
                            var ctaCheck = localStorage.getItem('isTopicCta');
                            setTimeout(function () {
                                $.ajax({
                                    url: '/lead-v4/send-otp-lead',
                                    data: { phone: e.target.value, is_lead: 1, ctaCheck: ctaCheck },
                                    dataType: 'json',
                                    method: 'POST',
                                    beforeSend: function () {
                                        $('.primaryBtn').prop('disabled', true);
                                    },
                                    error: function (xhr, err) {
                                        $('.primaryBtn').prop('disabled', false)
                                        displayErrors('Something went wrong, please try again!')
                                    },
                                    complete: function () {
                                        $('.primaryBtn').prop('disabled', false);
                                    },
                                    success: function (data) {
                                        if (data) {
                                            if (data.is_registered_user == true) {
                                                disableOtpField()
                                            } else {
                                                otpTimerLead(30);
                                                $(".otpTimer").css("display", "block");
                                            }
                                        }
                                    }
                                });
                            }, 1000);


                        }
                    } else {
                        disableOtpField();
                        $(".OTPField").val("");
                    }
                })
            }

            window.onbeforeunload = function () {
                localStorage.removeItem('leadId');
                localStorage.removeItem('sponsorCollege');
                localStorage.removeItem('student_college_shortlist_id');
                localStorage.removeItem('activityId');
                localStorage.removeItem('verified');
                localStorage.removeItem('onClickUrl');
                localStorage.removeItem('collegeid');
                localStorage.removeItem('latestvalue');
                localStorage.removeItem('programid');
                localStorage.removeItem('round');
                localStorage.removeItem('type');
                localStorage.removeItem('collegeidCutOff');
                localStorage.removeItem('programname');
                localStorage.removeItem('coursename');
                localStorage.removeItem('clickbutton');
            };

            // Close Dialog
            if (document.querySelector('.closeDialog') !== null) {
                localStorage.removeItem('collegeId');
                document.querySelector('.closeDialog').addEventListener('click', () => {
                    document.querySelector('#lead-form-js-new').style.display = "none";
                    var onClickUrl = localStorage.getItem('onClickUrl') == null ? '' : localStorage.getItem('onClickUrl');
                    if (onClickUrl) {
                        var leadForm = $('.js-open-lead-form-new');
                        leadForm.each(function () {
                            $(this).attr('onclick', onClickUrl);
                        });
                    }
                    isModalOpenLead = false;
                    $(".textDivHeading").html("");
                    $(".errorMsgEmail").html("");
                    $('.inputStreamContainer select option').remove();
                    $('.inputLevelContainer select option').remove();
                    $("#leadform-interested_location").val('').trigger('change');
                    $("#interested_level_lead").prop("disabled", true);
                    $(".pageMask").css("display", "none");
                    $(".otpclass").css("display", "none");
                    $("body").css("overflowY", "unset");
                    $(".validEmailIcon.spriteIconTwo").attr('style', 'display: none !important');
                    // $('.closeLeadFormContainer').attr('style', 'display: none !important');
                    $('.validEmailIcon').attr('style', 'display: none !important');
                    document.querySelector('body').style.position = 'unset';
                    document.querySelector('body').style.height = 'unset';
                    getScrollPosition();
                    document.querySelector('body').style.bottom = 'unset';
                    document.querySelector('body').style.right = 'unset';
                    document.querySelector('body').style.left = 'unset';

                    //check to loggedin Through review readMore
                    if ((localStorage.getItem('loggedIn') === 'Yes')) {
                        localStorage.setItem('singupClicked', "Yes");
                        scrollToClickedReview();
                    }
                    $('.signupModalForm').trigger("reset");
                    inputErrorClearonFocusLeadForm()
                    localStorage.removeItem('onClickUrl');
                    var checkStepId = $('.signupModalForm section').not(".inactiveStep");
                    var stepId = checkStepId[0].id;
                    var ctaLocationValue = document.querySelector('#cta_location').value;

                    if (ctaLocationValue.includes("exam_widget_cta")) {
                        location.reload(true);
                    }

                    if (stepId === 'step-1') {
                        document.querySelector('#lead-form-js-new').style.display = "none";
                        return false;
                    }
                    detailedFeePopup();
                })
            }

            //unset all the style when esc key is pressed, after opening lead form
            $(document).keydown(function (e) {
                if (e.keyCode == 27) {
                    $("body").css("overflowY", "unset");
                    $(".headingText").html("");
                    $(".pageMask").css("display", "none");
                    location.reload(true);
                };
            });

            //Lead Form utm trigger changes
            //$(document).ready(function () {
            setTimeout(function () {
                if (gmu.config.show_lead_form) {
                    var url = document.querySelectorAll('meta[property="og:url"]')[0].content ?? window.location.href;
                    $('.closeLeadFormContainer').attr('style', 'display: none !important');
                    $('.headingText').append('REGISTER NOW TO APPLY');
                    $('.thankYouMsg').append('<i class="spriteIcon closeLeadFormUtm " style="display: block !important"></i>');
                    $("body #leadform-url").val(url);
                    $('body #leadform-entity').val(gmu.config.entity);
                    $('body #leadform-entity_id').val(gmu.config.entity_id);
                    $('body ##cta_location').val(gmu.config.cta_location);
                    $('body #leadform-utm_source').val(gmu.config.utm_source);
                    $('body #leadform-utm_medium').val(gmu.config.utm_medium);
                    $('body #leadform-utm_campaign').val(gmu.config.utm_campaign);
                    document.querySelector('#lead-form-js-new').style.display = "block";
                }
            }, 1000);

            //utm source leadform fadeout
            $("body").on("click", ".closeLeadFormUtm", function () {
                // $(".leadFormContainer").fadeOut();
                document.querySelector('#lead-form-js-new').style.display = "none";
            });

            //submit forms starts
            $("#firstScreenSubmit").click(function (e) {
                e.preventDefault();
                $(".errorEducationMsg").html("");
                document.querySelector("#ajaxLeadBudget").innerHTML = '';
                var student_activity_parent_id = sessionStorage.getItem('activity_id') ?? '';
                var form = $('#firstScreen');
                var lead_id = localStorage.getItem('leadId') ?? '';
                var student_id = localStorage.getItem('studentId') ?? '';
                var preference_id = localStorage.getItem('preferenceId') ?? '';
                var activity_id = localStorage.getItem('activityId') ?? '';
                var student_college_shortlist_id = localStorage.getItem('student_college_shortlist_id') ?? '';
                var product_mapping_entity = gmu.config.product_mapping_entity == "false" ? null : gmu.config.product_mapping_entity;
                var product_mapping_entity_id = gmu.config.product_mapping_entity_id ?? '';
                var verified = localStorage.getItem('verified') ?? '';
                $.ajax({
                    url: '/lead-v4/screen-one',
                    data: form.serialize() + "&lead_id=" + lead_id + "&verified=" + verified + "&product_mapping_entity=" + product_mapping_entity + "&product_mapping_entity_id=" + product_mapping_entity_id + "&student_id=" + student_id + "&preference_id=" + preference_id + "&activity_id=" + activity_id + "&student_college_shortlist_id=" + student_college_shortlist_id + "&student_activity_parent_id=" + student_activity_parent_id,
                    dataType: 'json',
                    method: 'POST',
                    beforeSend: function () {
                        $('.primaryBtn').prop('disabled', true);
                    },
                    error: function (xhr, err) {
                        $('.primaryBtn').prop('disabled', false)
                        displayErrorsLead('Something went wrong, please try again!')
                    },
                    complete: function () {
                        $('.primaryBtn').prop('disabled', false);
                    },
                    success: function (data) {
                        if (data.success == true) {
                            if ($(".otpclass .validationError").html() !== undefined) {
                                $(".otpclass .validationError").html("Please enter the valid otp")
                                return false;
                            }
                            displayErrorsLead();
                            var response = {
                                'mobile': $("input[name='phone']").val(),
                                'name': $("input[name='name']").val(),
                                'email': $("input[name='email']").val(),
                                'stream': $("input[name='inputStream']").val(),
                                'level': $("input[name='inputLevel']").val(),
                                'qualification': data.highest_qualification,
                                'specialization': data.specialization,
                                'current_location': $("input[name='current_city']").val(),
                                'state_id': $("input[name='state_id']").val(),
                                'state_name': $("input[name='state_name']").val(),
                                'interested_course': data.courseId,
                                'cta_location': data.cta_location,
                                'source_url': data.source_url,
                                'user_status': data.user_status,
                                'user_type': data.user_type,
                            };
                            var responseGtm = {
                                'mobile': $("input[name='phone']").val(),
                                'name': $("input[name='name']").val(),
                                'email': $("input[name='email']").val(),
                                'stream': $("input[name='inputStream']").val(),
                                'level': $("input[name='inputLevel']").val(),
                                'qualification': data.highest_qualification,
                                'specialization': data.specialization,
                                'current-location': $("input[name='current_city']").val(),
                                'state-id': $("input[name='state_id']").val(),
                                'state-name': $("input[name='state_name']").val(),
                                'interested-course': data.courseId,
                                'cta-location': data.cta_location,
                                'source-url': data.source_url,
                                'user-status': data.user_status,
                                'user-type': data.user_type,
                            };
                            responseGtm['cta-text'] = $('#cta_text').val();

                            sendGtmData('CTA_lead_submit-step-one-' + gmu.config.entity, responseGtm);
                            $("body").css("overflowY", "hidden");
                            localStorage.setItem('activityId', data.student_activity_id);
                            localStorage.setItem('session_activity_id', data.student_activity_id);
                            localStorage.setItem('preferenceId', data.student_prefrence_id);
                            localStorage.setItem('leadId', data.lead_id);
                            localStorage.setItem('studentId', data.student_id);

                            // setTimeout(function() {  webEngageEvent(gmu.config.entity + '_lead_submit', response); }, 5000);
                            var ctaTextShortList = $("#cta_text").val();
                            if (ctaTextShortList.trim()) {
                                ctaTextShortList = ctaTextShortList.replace(/(<([^>]+)>)/ig, '');
                                ctaTextShortList = $.trim(ctaTextShortList);
                            }
                            if (ctaTextShortList == 'Shortlist' && (gmu.config.entity == 'college')) {
                                var eventName = 'college_shortlist';
                            } else {
                                var eventName = 'lead_resubmission';
                            }
                            setTimeout(function () { webEngageEvent(eventName, response); }, 5000);



                            localStorage.setItem('student_college_shortlist_id', data.student_college_shortlist_id);
                            if (data.is_mobile_verified == 1 && gmu.config.isLoggedIn == true && data.numberChange == 0) {
                                $("input[name='inputOTP']").val(data.otp);
                                $(".otpTimer").remove();
                                $(".otpclass").append("<span class='spriteIcon tickIcon'></span>");
                            }
                            // fetchScreenTwoValues(data);

                            if (data.budget.length !== 0) {
                                $('#ajaxLeadBudget').append(data.budget);
                            } else {
                                $(".budgetText").css("display", "none");
                            }

                            if (gmu.config.isLoggedIn == true) {
                                if (localStorage.getItem('phone') == $("input[name='phone']").val()) {
                                    navigateToFormStep(2);
                                } else if (nameInputLead.value.length !== 0 && emailInputLead.value.length !== 0 && mobileInputLead.value.length !== 0 && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0 && $("input[name='hiddenNumber']").val() == $("input[name='phone']").val()) {
                                    navigateToFormStep(2);
                                }
                            } else if (nameInputLead.value.length !== 0 && emailInputLead.value.length !== 0 && mobileInputLead.value.length !== 0 && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0 && (otpInputLead.value.length == 4 && $(".inputOTPContainer .validationError").html() == '')) {
                                navigateToFormStep(2);
                            }

                            //generate pdf basd on programis, round and score
                            if (localStorage.getItem('clickbutton') == 'getComparisionCutoff') {
                                cutOffDownload();
                            }
                        } else {
                            displayErrorsLead(data.message)
                        }
                    }
                });
            });

            $("#secondScreenSubmit").click(function (e) {
                e.preventDefault();
                var form = $("#secondScreen");
                var lead_id = localStorage.getItem('leadId') ?? '';
                var student_id = localStorage.getItem('studentId') ?? '';
                var preference_id = localStorage.getItem('preferenceId') ?? '';
                var activity_id = localStorage.getItem('activityId') ?? '';
                var student_college_shortlist_id = localStorage.getItem('student_college_shortlist_id') ?? '';
                var stream = $('select[name="inputStream"] option:selected').val();
                var currentCity = $('select[name="current_city"] option:selected').val();
                var currentCityIp = $('input[name="current_city_ip"]').val();
                var distanceEducation = $("input[name='distanceEducation']").is(':checked') ? 1 : 0;
                var studyAbroad = $("input[name='studyAbroadCheck']").is(':checked') ? 1 : 0;
                var selectedRadio = $('input[name="budget"]:checked');
                var labelForSelectedRadio = $('label[for="' + selectedRadio.attr('id') + '"]');
                var budgetValueText = labelForSelectedRadio.text();

                if (gmu.config.entity_type == "college-listing") {
                    var entity = document.querySelector('#leadform-entity').value
                    var entity_id = document.querySelector('#leadform-entity_id').value
                } else {
                    var entity = gmu.config.entity;
                    var entity_id = gmu.config.entity_id;
                }

                $.ajax({
                    url: '/lead-v4/screen-two',
                    data: form.serialize() + "&entity=" + entity + "&entity_id=" + entity_id + "&stream=" + stream + "&currentCity=" + currentCity + "&currentCityIp=" + currentCityIp + "&lead_id=" + lead_id + "&student_id=" + student_id + "&preference_id=" + preference_id + "&activity_id=" + activity_id + "&student_college_shortlist_id=" + student_college_shortlist_id + "&distanceEducation=" + distanceEducation + "&studyAbroad=" + studyAbroad + "&budgetValueText=" + budgetValueText,
                    dataType: 'json',
                    method: 'POST',
                    beforeSend: function () {
                        $('.primaryBtn').prop('disabled', true);
                    },
                    error: function (xhr, err) {
                        $('.primaryBtn').prop('disabled', false)
                        displayErrorsLead('Something went wrong, please try again!')
                    },
                    complete: function () {
                        $('.primaryBtn').prop('disabled', false);
                    },
                    success: function (data) {
                        var sendGTMFormData = {
                            'entity': entity,
                            'entity-id': entity_id,
                            'stream': stream,
                            'currentCity': currentCity,
                            'currentCityIp': currentCityIp,
                            'lead-id': lead_id,
                            'student-id': student_id,
                            'preference-id': preference_id,
                            'activity-id': activity_id,
                            'student-college-shortlist-id': student_college_shortlist_id,
                            'distanceEducation': distanceEducation,
                            'budgetValueText': budgetValueText,
                        };
                        var response = {
                            'preference-id': preference_id,
                            'activity-id': activity_id,
                            'student-college-shortlist-id': student_college_shortlist_id,
                            'distanceEducation': distanceEducation,
                            'budgetValueText': budgetValueText,
                        };

                        // downLoadPdf();
                        sendGTMFormData['cta-location'] = $('#cta_location').val();
                        sendGTMFormData['cta-text'] = $('#cta_text').val();
                        var eventDataJson = Object.assign({}, response);


                        // webengage.track(gmu.config.entity + '_second_setp_lead_submit', eventDataJson);

                        $("body").css("overflowY", "hidden");
                        if (data.success == true) {
                            var sponsorCollegeLead = document.getElementsByClassName('sponsorCollegeLead');
                            sendGtmData('CTA_lead_submit-step-two-' + gmu.config.entity, sendGTMFormData);
                            const value = localStorage.getItem('userData');
                            const exists = value !== null;
                            if (exists) {
                                saveLocalStorageAnswer();
                            }
                            if ($('#ctaIdentity').val() == 'download_pdf' || $('#ctaIdentity').val() == 'attempt_more_question' || $('#ctaIdentity').val() == 'check_explanation' || $('#ctaIdentity').val() == 'view_correct_answer' || $('#ctaIdentity').val() == 'list_practice_tab') {


                                $('.gmu-mock-actionExplain').addClass('showExplanation');
                                $('.gmu-mock-actionExplain').addClass('afterLogin');
                                $('.gmu-mock-listItem').addClass('gmu-mock-listItemShow');
                                $('.gmu-mock-actionCorrect').addClass('afterLogin');
                                $('.gmu-mock-actionCorrect').addClass('showAnswer');
                                $('.gmu-mock-optionListItem').removeClass('notLogin');
                                $('.gmu-mock-downloadBtn').attr('id', 'create_pdf');
                                if ($('#ctaIdentity').val() == 'check_explanation') {
                                    $('#' + $('#ctaIndex').val()).fadeIn();
                                    checkAnswer('#showAnswer' + $('#indexcorrectanswer').val());
                                }
                                if ($('#ctaIdentity').val() == 'list_practice_tab') {
                                    $('#tab-' + $('#ctaTabIndex').val()).trigger('click');
                                }
                                if ($('#ctaIdentity').val() == 'view_correct_answer') {
                                    checkAnswer('#showAnswer' + $('#indexcorrectanswer').val());

                                }
                                if ($('#ctaIdentity').val() == 'attempt_more_question') {
                                    $(".gmu-mock-listItem").each(function (index) {
                                        if ($(this).hasClass("active") == true) {
                                            var idIndex = $(this).next().attr('data-tab');
                                            $("#tab-" + idIndex).trigger('click');
                                            $('html, body').animate({
                                                scrollTop: $(this).offset().top
                                            }, 1000);
                                            return false;
                                        }
                                    });
                                }
                                if ($('#ctaIdentity').val() == 'download_pdf') {
                                    downLoadPdf($('#ctaSubTopic').val());
                                }

                                var studentId = localStorage.getItem('studentId');
                                if (studentId) {
                                    $('.checkAuth').removeClass('js-open-lead-form-new');
                                    $("#create_pdf").removeClass('js-open-lead-form-new');
                                }
                                $('#lead-form-js-new').css('display', "none");
                                $('body').css('position', "unset");
                                $('body').css('height', "unset");
                                $('body').css('bottom', "unset");
                                $('body').css('left', "unset");
                                $('body').css('right', "unset");
                                $("body").css("overflowY", "unset");
                                $(".headingText").html("");
                                $(".pageMask").css("display", "none");
                                $(".otpclass").css("display", "none");
                                localStorage.setItem('isTopicCta', 1);
                                $(':input', '#firstScreen').val('')
                                    .prop('checked', false)
                                    .prop('selected', false);
                                $(':input', '#secondScreen').val('').prop('checked', false).prop('selected', false);
                                $("#interested_location").val('').trigger('change');
                                $("#interested_specialization_lead").val('').trigger('change');
                                $("#leadform-interested_location").val('').trigger('change');
                                $('.validEmailIcon').css("display", "none");
                                $('#firstScreenSubmit').attr('disabled', true);
                                $('#secondScreenSubmit').attr('disabled', true);
                                activateStudentSession(false);
                                isAuthentiCate();
                                navigateToFormStep(1);
                                return false;
                            }
                            localStorage.setItem('isTopicCta', 0);
                            if (data.sponsorCollege.length !== 0) {
                                localStorage.setItem('sponsorCollege', 1);
                                $(".sponsorCollegeLead").html(data.sponsorCollege);
                                navigateToFormStep(3);
                            } else {
                                lastScreenDisplay();
                            }
                            if (data.sponsorCollege.length !== 0) {
                                localStorage.setItem('sponsorCollege', 1);
                                $(".sponsorCollegeLead").html(data.sponsorCollege);
                                navigateToFormStep(3);
                            } else {
                                lastScreenDisplay();
                            }
                        } else {
                            return false;
                        }
                    }
                });
            });

            $("#thirdScreenSubmit").click(function (e) {
                $("body").css("overflowY", "hidden");
                // navigateToFormStep(4);
                e.preventDefault()
                displayErrorsLead()
                $(".errorExamMsg").html("")
                var form = $("#thirdScreen");
                var student_id = localStorage.getItem('studentId') ?? '';
                var activity_id = localStorage.getItem('activityId') ?? '';
                var csrf = $("input[name='_csrf-frontend']").val();

                var college_id = $.map($("input[name='checked[]']"), function (e) {
                    if ($(e).is(':checked')) {
                        return ($(e).attr('data-id'));
                    }
                });
                $.ajax({
                    url: '/lead-v4/screen-three',
                    data: { college_id: college_id, '_csrf-frontend': csrf, student_id: student_id, activity_id: activity_id },
                    dataType: 'json',
                    method: 'POST',
                    beforeSend: function () {
                        $('.primaryBtn').prop('disabled', true);
                    },
                    error: function (xhr, err) {
                        $('.primaryBtn').prop('disabled', false)
                        displayErrorsLead('Something went wrong, please try again!')
                    },
                    complete: function () {
                        $('.primaryBtn').prop('disabled', false);
                    },
                    success: function (data) {
                        lastScreenDisplay();
                    }
                });
            });

            //verify-otp
            if (document.querySelector('.OTPField') !== null) {
                document.querySelector('.OTPField').addEventListener('input', (e) => {
                    e.preventDefault();
                    if (e.target.value.length === 4) {
                        var otp = $("input[name='inputOTP']").val();
                        var mobile = $("input[name='phone']").val();
                        var stream = $('select[name="inputStream"] option:selected').val();
                        var level = $('select[name="inputLevel"] option:selected').val()
                        var student_id = localStorage.getItem('studentId') ?? '';
                        var preference_id = localStorage.getItem('preferenceId') ?? '';
                        var entity = gmu.config.entity

                        $.ajax({
                            url: '/lead-v4/verify-otp',
                            data: { otp: otp, entity: entity, mobile: mobile, inputStream: stream, inputLevel: level, student_id: student_id, preference_id: preference_id },
                            dataType: 'json',
                            method: 'POST',
                            success: function (data) {
                                if (data.success == true) {
                                    localStorage.setItem('verified', 1);
                                    setCookie('gmu_leadform', 1, 10);
                                    $('.errorMsg').html('');
                                    $('.validationError').html('');
                                    $(".otpTimer").remove();
                                    $(".otpclass").append("<span class='spriteIcon tickIcon'></span>");
                                    webengage.track("opt_status", {
                                        "opt_field": $("input[name='inputOTP']").val(),
                                        "otp_status": 'Yes',
                                        "page_url": gmu.config.page_url,
                                    });
                                    if ($("input[name='hiddenNumber']").val() !== $("input[name='phone']").val()) {
                                        navigateToFormStep(2);
                                    }
                                    $("input[name='hiddenNumber']").val($("input[name='phone']").val())
                                    sendOtpButton.style.display = 'none';
                                } else {
                                    $(".stepOne").attr('disabled', 'disabled')
                                    displayErrorsLead(data.message)
                                }
                            }
                        });
                    }
                });
            }

            //resend-otp
            if (sendOtpButton !== null) {
                sendOtpButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    var mobile = $("input[name='phone']").val();
                    var student_id = localStorage.getItem('studentId') ?? '';

                    $.ajax({
                        url: '/lead-v4/resend-otp-lead',
                        data: { phone: mobile, is_lead: 1, student_id: student_id },
                        dataType: 'json',
                        method: 'POST',
                        success: function (data) {
                            if (data.success == true) {
                                document.querySelector('.inputOTPContainer').style.display = 'block';
                                $(".otpTimer").css("display", "block");
                                otpTimerLead(30);
                                sendOtpButton.textContent = 'Resend';
                                sendOtpButton.style.display = 'none';
                            } else {
                                displayErrorsLead(data.message)
                            }
                        }
                    });
                })
            }
            // submit form end

            //lead form functions starts
            function collegeFilterExamCourseType(courseType) {
                // If courseType is not null, get the first value
                if (courseType !== null) {
                    var firstValue = courseType.split(',')[0]; // Split by comma and get the first value

                    var data = [
                        { key: 'bachelors', value: 1 },
                        { key: 'masters', value: 2 },
                        { key: 'doctorate', value: 3 },
                        { key: 'diploma', value: 4 },
                        { key: 'postgraduate-diploma', value: 5 },
                        { key: 'certificate', value: 6 },
                        { key: 'postgraduate-certificate', value: 7 },
                    ];

                    // Filter the array based on key and value
                    var matchingItems = data.filter(function (item) {
                        return item.key === firstValue;
                    });
                    return matchingItems;
                }
            }

            function submitButtonFunctionality() {
                if (localStorage.getItem("phone") !== $("input[name='hiddenNumber']").val()) {
                    if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0 && (otpInputLead.value.length == 4 && $(".inputOTPContainer .validationError").html() == '')) {
                        submitButtonLead.removeAttribute('disabled');
                    } else {
                        submitButtonLead.setAttribute('disabled', true);
                    }
                } else {
                    if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0) {
                        submitButtonLead.removeAttribute('disabled');
                    } else {
                        submitButtonLead.setAttribute('disabled', true);
                    }
                }

                if ($('.admissionRadio').is(':checked') && specializationInputLead.value.length !== 0) {
                    submitButtonLeadSecondScreen.removeAttribute('disabled');
                } else {
                    submitButtonLeadSecondScreen.setAttribute('disabled', true);
                }
            }

            function fetchScreenTwoValues(data) {
                if (data.screenTwo.studentPreferenceScreenTwo.length !== 0) {
                    var addedCityIds = {};
                    var addedSpecializationIds = {};
                    $.each(data.screenTwo.studentPreferenceScreenTwo, function (index, autoFetchDetails) {
                        console.log(autoFetchDetails);
                        if (!addedSpecializationIds[autoFetchDetails.id]) {
                            var option = new Option(autoFetchDetails.name, autoFetchDetails.id, true, true);
                            $('#interested_specialization_lead').append(option).trigger('change');
                            addedSpecializationIds[autoFetchDetails.id] = true;
                        }
                        if (!addedCityIds[autoFetchDetails.preferred_city_id]) {
                            var option = new Option(autoFetchDetails.cityName, autoFetchDetails.preferred_city_id, true, true);
                            $('#interested_location').append(option).trigger('change');
                            addedCityIds[autoFetchDetails.preferred_city_id] = true;
                        }
                    });
                }

                if (data.screenTwo.student_preference.admission !== '') {
                    $('input[name="admissionYearSelection"][value="' + data.screenTwo.student_preference.admission + '"]').prop('checked', true);
                }
            }

            function toggleCollegeSpecialization() {

                var $interested_specialization_lead = $('#interested_specialization_lead');
                var $interested_stream_lead = $('#interested_stream_lead');
                var $interested_level_lead = $('#interested_level_lead');
                var streamValue = $interested_stream_lead.val();
                var levelValue = $interested_level_lead.val();

                if (!streamValue || !levelValue) {
                    return false;
                }

                if (gmu.config.entity == 'college' || gmu.config.entity == 'college-listing' || gmu.config.entity == 'college-predictor') {
                    $('#interested_specialization_lead').val('').trigger('change');
                    if (gmu.config.entity == 'college-listing') {
                        var id = localStorage.getItem('collegeId');
                        var entity = gmu.config.entity;
                    } else if (gmu.config.entity == 'college-predictor') {
                        var id = $("#leadform-entity_id").val();
                        var entity = $("#leadform-entity").val();
                    } else {
                        var id = gmu.config.entity_id;
                        var entity = gmu.config.entity;
                    }

                    $.ajax({
                        url: '/ajax/lead-specialization',
                        data: {
                            entity: entity,
                            stream_id: streamValue,
                            level: levelValue,
                            entity_id: id
                        },
                        dataType: "json",
                        type: "GET",
                        success: function (data) {
                            if (data && data.length === 1 && data[0].id && data[0].text) {
                                var option = new Option(data[0].text, data[0].id, true, true);
                                $interested_specialization_lead.append(option).trigger('change');
                                $(".specializationText").css("display", "none");
                                $(".specializationContainer").hide();
                            }
                        }
                    });
                }
            }

            function lastScreenDisplay(message = "") {
                var cta_text = $("#cta_text").val();
                console.log(cta_text, "cta_text")
                var tempDiv = document.createElement('div');
                tempDiv.innerHTML = cta_text;
                var spanElement = tempDiv.querySelector('span');
                if (spanElement) {
                    var spanClasses = spanElement.className;
                }

                if (spanElement && (spanClasses.includes('whiteDownloadIcon') || spanClasses.includes('redDownloadIcon'))) {
                    document.querySelector('.messageTwoLeadFormSubmit').style.display = 'none';
                }

                $("body").css("overflowY", "hidden");
                if (localStorage.getItem('reviewReadMoreId')) {
                    localStorage.setItem('loggedIn', "Yes");
                }

                if (gmu.config.isLoggedIn == true && message) {
                    $(".messageOneLeadFormSubmit").html(message);
                    $(".messageOneLeadFormSubmit").css({
                        "font-size": "21px",
                        "text-align": "center"
                    });
                    $(".messageTwoLeadFormSubmit").css("display", "none");
                }

                var ctaText = '';
                if ($("#cta_location").val() == "auto-popup") {
                    var ctaText = gmu.config.auto_popup_form_text;
                } else {
                    var ctaText = $("#cta_text").val();
                }
                var csrf = $("input[name='_csrf-frontend']").val();

                // Show final screen immediately to avoid blank state
                document.querySelector('.headerAndForm').style.display = 'none';
                document.querySelector('.form__side__content').style.display = 'none';
                document.querySelector('.finalScreen').style.display = 'flex';
                document.querySelector('.form__main__content').style.maxWidth = 'unset';

                if ($("#alternate-media").val() == 'page_redirect') {
                    dynamicPdfUrlRedirect($("#alternate-pageSlug").val());
                    document.location.reload(true);
                }

                if (!message) {
                    // Show a loading state or default message immediately
                    $(".messageOneLeadFormSubmit").html("Thank you for your interest.");
                    $(".messageOneLeadFormSubmit").css({
                        "font-size": "21px",
                        "text-align": "center"
                    });

                    console.log(message, "hi");
                    $.ajax({
                        url: '/lead-v4/thank-you-message',
                        data: { cta_text: ctaText, '_csrf-frontend': csrf, entity: gmu.config.entity },
                        dataType: 'json',
                        method: 'POST',
                        success: function (response) {
                            if (response.success == true) {
                                $(".messageOneLeadFormSubmit").html(response.thankYouMessage);
                                $(".messageOneLeadFormSubmit").css({
                                    "font-size": "21px",
                                    "text-align": "center"
                                });
                            }
                        },
                        error: function() {
                            // Keep the default message if AJAX fails
                            console.log('Failed to load thank you message');
                        }
                    });
                }
                
                if (($('#durl').length) != 0 || ($('#dynamic_redirection').length) != 0) {
                    if ($("#durl").val() != '') {
                        dynamicPdfUrlRedirect($("#durl").val());
                        getScrollPosition()
                    }

                    if ($("#dynamic_redirection").val() != '') {
                        dynamicPdfUrlRedirect($("#dynamic_redirection").val());
                        getScrollPosition()
                    }
                }

                // setTimeout(function () {
                //     document.location.reload(true);
                // }, 3000)
            }

            function mobileFieldFunctionality() {
                if (nameInputLead.value.length > 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && streamSelectLead.value.length > 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && levelSelectLead.value.length > 0) {
                    $('.mobileNumberField').prop("disabled", false);
                } else {
                    $('.mobileNumberField').prop("disabled", true);
                }
            }

            function EnableSubmitButtonForloggedInUsers() {
                if (gmu.config.isLoggedIn == true) {
                    submitButtonFunctionality();
                    mobileFieldFunctionality();
                }
            }

            // Function to enable/disable the select dropdown
            function toggleLevelSelect(enabled) {
                var $levelSelect = $('#interested_level_lead'); // select dropdown
                if (enabled) {
                    $levelSelect.prop('disabled', false);
                } else {
                    $levelSelect.prop('disabled', true);
                }
            }

            function toggleStreamField(stream_id = '', level = '', specialization_id = '') {
                //22 -> others
                let highest_qualification_degree_id = ['9', '10', '11'];

                if (stream_id == '' || stream_id == '22' || $.inArray(level, highest_qualification_degree_id) !== -1) {
                    $(".inputStreamContainer").show();
                } else {
                    $(".inputStreamContainer").hide();
                    $('.inputStreamContainer select').append($('<option>', {
                        value: stream_id,
                        selected: true
                    }));
                }
                toggleLevel(level);
                toggleSpecializationField(specialization_id);
                EnableSubmitButtonForloggedInUsers();
            }

            function toggleSpecializationField(specialization_id = '') {
                if (specialization_id !== '' && specialization_id !== null) {
                    $(".specializationText").css("display", "none");
                    $(".specializationContainer").hide();
                    $('.specializationContainer select').append($('<option>', {
                        value: specialization_id,
                        selected: true,
                    })).prop('disabled', false);
                } else {
                    $(".specializationContainer").show();
                }
            }

            function toggleLevel(level = '') {
                //12 -> others
                if (level !== '' && level !== '12') {
                    $(".inputLevelContainer").hide();
                    $('.inputLevelContainer select').append($('<option>', {
                        value: level,
                        selected: true,
                    })).prop('disabled', false);
                } else {
                    $(".inputLevelContainer").show();
                    $("#interested_level_lead").prop("disabled", false);
                }
                toggleCollegeSpecialization();
            }

            function toggleBoardLevel() {
                if (gmu.config.board_level == "10") {
                    $(".inputLevelContainer").hide();
                    $('.inputLevelContainer select').append($('<option>', {
                        value: 4, //10th id from degree table
                        selected: true,
                    })).prop('disabled', false);
                    EnableSubmitButtonForloggedInUsers()
                } else if (gmu.config.board_level == "12") {
                    $(".inputLevelContainer").hide();
                    $('.inputLevelContainer select').append($('<option>', {
                        value: 1, //12th id from degree table
                        selected: true,
                    })).prop('disabled', false);
                    EnableSubmitButtonForloggedInUsers()
                }
            }

            //change the images based on screens
            let sideImg = document.querySelector('.lead-form-side-img');
            let headingText = document.querySelector('.textDivHeading');
            let sideHeadinText = document.querySelector('.headingOne');
            let sideSubHeadingTextOne = document.querySelector('.subHeadingOne span:nth-child(2)');
            let sideSubHeadingTextTwo = document.querySelector('.subHeadingTwo span:nth-child(2)');
            let sideSubHeadingTextThree = document.querySelector('.subHeadingThree span:nth-child(2)');

            const dynamicContent = (stepNumber) => {
                switch (stepNumber) {
                    case 1:
                        break;
                    case 2:
                        sideImg.src = '/yas/images/lead-form-side-img-2.png';
                        headingText.textContent = 'Explore, uncover and find the perfect college that fits you';
                        sideHeadinText.textContent = 'Unlock Your Dream College with Our Tailored Guidance Today!';
                        sideSubHeadingTextOne.textContent = 'Get Access to Our Comprehensive College Application Assistance!';
                        sideSubHeadingTextTwo.textContent = 'Navigate the application journey with our streamlined guidance';
                        sideSubHeadingTextThree.textContent = 'Connect with Our Experts and Get a Head Start on Your Goals!';
                        break;
                    case 3:
                        sideImg.src = '/yas/images/lead-form-side-img-3.png';
                        headingText.textContent = 'Multiply Your Opportunities: Apply to Multiple Colleges in One Go!';
                        sideHeadinText.textContent = 'Your Dream Awaits: Access Our List of Participating Colleges';
                        sideSubHeadingTextOne.textContent = 'Ace the College Application Process with Our Expert Guidance!';
                        sideSubHeadingTextTwo.textContent = 'Get Ahead of the Competition with Our Comprehensive Expert Network!';
                        sideSubHeadingTextThree.textContent = 'Secure Financial Support with Our Scholarship Programs and Guidance!';
                        break;
                }
            }

            // Function to navigate between form steps
            const navigateToFormStep = (stepNumber) => {
                //To check if download icon present for media drive download file
                var cta_text = $("#cta_text").val();
                var tempDiv = document.createElement('div');
                tempDiv.innerHTML = cta_text;

                var spanElement = tempDiv.querySelector('span');
                if (spanElement) {
                    var spanClasses = spanElement.className;
                }
                var cta_title = $("#cta_title").val();

                dynamicContent(stepNumber);
                // Hide all form steps
                document.querySelectorAll(".form-step").forEach((formStepElement) => {
                    formStepElement.classList.add("inactiveStep");
                });
                // Show the current form step (as passed to the function).
                const leadSteps = document.querySelector("#step-" + stepNumber);

                if (leadSteps !== null) {
                    document.querySelector("#step-" + stepNumber).classList.remove("inactiveStep");
                }
                $(".errorExamMsg").html("");

                if (gmu.config.entity == 'exam' || gmu.config.entity == 'articles') {
                    if (downloadFiles.length === 1) {
                        let a = document.createElement("a");
                        a.href = `https://media.getmyuni.com/assets/downloadables/${downloadFiles[0]}`;
                        a.target = "_blank";
                        a.download = downloadFiles[0];
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);

                        fetch('/ajax/delete-bulk-temp-file', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ files: [downloadFiles[0]] })  // batch format
                        }).then(res => res.json())
                            .then(data => console.log('Cleanup result:', data));

                    } else if (downloadFiles.length > 1) {
                        let zip = new JSZip();
                        let successfullyFetched = [];

                        Promise.allSettled(
                            downloadFiles.map(file =>
                                fetch(`https://media.getmyuni.com/assets/downloadables/${file}`)
                                    .then(res => {
                                        if (!res.ok) throw new Error(`Failed to fetch ${file}`);
                                        return res.blob();
                                    })
                                    .then(blob => {
                                        zip.file(file, blob);
                                        successfullyFetched.push(file); // track only success
                                    })
                                    .catch(err => {
                                        console.warn(`Skipping ${file} due to fetch error:`, err);
                                    })
                            )
                        ).then(() => {
                            zip.generateAsync({ type: "blob" }).then(zipBlob => {
                                let a = document.createElement("a");
                                a.href = URL.createObjectURL(zipBlob);
                                a.download = downloadCategory + ".zip";
                                document.body.appendChild(a);
                                a.click();
                                document.body.removeChild(a);

                                if (successfullyFetched.length > 0) {
                                    fetch('/ajax/delete-bulk-temp-file', {
                                        method: 'POST',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({ files: successfullyFetched })
                                    }).then(res => res.json())
                                        .then(data => console.log('Cleanup result:', data));
                                }
                            });
                        });
                    }
                }

                if ((stepNumber == 2) && spanElement && (spanClasses.includes('whiteDownloadIcon') || spanClasses.includes('redDownloadIcon'))) {
                    //download function here
                    mediaDriveDownload(cta_title);
                }
            };

            function disableOtpField() {
                sendOtpButton.style.display = 'none';
                OtpField.style.display = 'none';
            }

            let isModalOpenLead = false;
            function preventDefaultScrollLead(event) {
                if (isModalOpenLead) {
                    document.body.style.height = '100vh';
                    document.body.style.overflowY = 'hidden';
                    if (/iPhone|iPod|iPad/.test(navigator.userAgent)) {
                        document.body.style.position = 'fixed';
                    }
                    if (event) {
                        event.preventDefault();
                    }
                }

            }
            //set leadform pop up position
            function setScrollPosition() {
                localStorage.setItem('scrollPosition', window.pageYOffset);
            }

            //remove popup position
            function removeScrollPosition() {
                if (localStorage.getItem('scrollPosition')) {
                    localStorage.removeItem('scrollPosition');
                }
            }

            //local storage of pageOffset
            function getScrollPosition() {
                if (localStorage.getItem('scrollPosition')) {
                    window.scrollTo(0, localStorage.getItem('scrollPosition'));
                }
                removeScrollPosition();
            }

            //otp timer
            function otpTimerLead(remaining) {
                var m = Math.floor(remaining / 60);
                var s = remaining % 60;

                m = m < 10 ? '0' + m : m;
                s = s < 10 ? '0' + s : s;

                if (document.querySelector('.otpTimer') !== null) {
                    document.querySelector('.otpTimer').innerHTML = m + ':' + s;
                }
                remaining -= 1;

                if (remaining >= 0) {
                    var timeOutId = setTimeout(function () {
                        otpTimerLead(remaining);
                    }, 1000);
                    if (document.querySelector(".mobileNumberField").value.length < 10) {
                        clearTimeout(timeOutId);
                    }
                    return;
                } else {
                    // sendOtpButton.classList.remove('disabledSendOtpButton');
                    if ($(".spriteIcon").hasClass("tickIcon") == false) {
                        document.querySelector('.sendOtpButton').textContent = 'Resend';
                        $(".otpTimer").css("display", "none");
                        $(".sendOtpButton").css("display", "block");
                    }
                }
            }


            function inputErrorClearonFocusLeadForm() {
                $(".form-group").focusin(function () {
                    $(this).find('.help-block').html('')
                    $(this).find('.validationError').html('')
                });
            }

            function webEngageEvent(eventName, response) {
                var user_id;
                user_id = localStorage.getItem("studentId");
                if (user_id && webengage.state.getForever() && webengage.state.getForever().cuid != user_id) {
                    webengage.user.logout()
                    webengage.user.login(user_id)
                }
                // webengage.user.login(response.mobile);
                webengage.user.setAttribute('we_email', response.email);
                webengage.user.setAttribute('we_phone', response.mobile);
                webengage.user.setAttribute('we_first_name', response.name);
                webengage.user.setAttribute('we_whatsapp_opt_in', true); //WhatsApp
                webengage.user.setAttribute('Source_Url', response.source_url ? response.source_url : '');
                webengage.user.setAttribute('Source', response.source ? response.source : '');
                webengage.user.setAttribute('User_Status', response.user_status ? response.user_status : '');
                webengage.user.setAttribute('User_Type', response.user_type ? response.user_type : '');
                var eventData = [];
                eventData['name'] = response.name;
                if (gmu.config.entity == 'college') {
                    eventData['is_sponsor_college'] = 'No';
                    if (response.is_sponsorCollege) {
                        eventData['is_sponsor_college'] = 'Yes';
                    }
                    eventData['state_name'] = response.state_name;
                    eventData['state_id'] = response.state_id;
                }
                eventData['entity'] = gmu.config.entity;
                eventData['entity_name'] = gmu.config.entity_name;
                eventData['is_opt_verified'] = 'Yes';
                eventData['device'] = 'web/wap';
                eventData['email'] = response.email ? response.email : '';
                eventData['stream'] = response.stream ? response.stream : '';
                eventData['level'] = response.level ? response.level : '';
                eventData['phone'] = response.mobile;
                eventData['highest_qualification'] = response.qualification ? parseInt(response.qualification) : '';
                eventData['specialization'] = response.specialization ? parseInt(response.specialization) : '';
                eventData['page_url'] = gmu.config.page_url;
                eventData['previous_url'] = gmu.config.previous_url;
                eventData['city_current_location'] = response.current_location ? parseInt(response.current_location) : '';
                eventData['interested_course'] = response.interested_course ? parseInt(response.interested_course) : '';
                eventData['cta_location'] = response.cta_location ? response.cta_location : '';
                eventData['utm_source'] = gmu.config.utm_source;
                eventData['utm_campaign'] = gmu.config.utm_campaign;
                eventData['utm_medium'] = gmu.config.utm_medium;
                var eventDataJson = Object.assign({}, eventData);

                webengage.track(eventName, eventDataJson);
            }

            webengage.track("user_land_on_page", {
                "entity": gmu.config.entity,
                "entity_name": gmu.config.entity_name,
                "page_url": gmu.config.page_url,
                "entity_id": gmu.config.entity_id
            });

            function getCookie(cname) {
                var name = cname + "=";
                var ca = document.cookie.split(';');

                for (var i = 0; i < ca.length; i++) {
                    var c = ca[i];
                    while (c.charAt(0) == ' ') c = c.substring(1);

                    if (c.indexOf(name) == 0) {
                        return c.substring(name.length, c.length);
                    }
                }
                return "";
            }

            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
                var expires = "expires=" + d.toUTCString();
                document.cookie = cname + "=" + cvalue + "; " + expires;
            }

            function mediaDriveDownload(cta_title) {
                let entityArr = ['exam', 'college', 'board'];
                let product_mapping_entity = null;
                let product_mapping_entity_id = null;

                if (((gmu.config.entity == 'articles' && gmu.config.product_mapping_entity !== 'articles') || (gmu.config.entity == 'news' && gmu.config.product_mapping_entity !== 'news')) && gmu.config.product_mapping_entity_id !== false) {
                    product_mapping_entity = (gmu.config.product_mapping_entity).slice(0, -1);
                    if (entityArr.includes(product_mapping_entity)) {
                        product_mapping_entity_id = (gmu.config.product_mapping_entity_id == "false" || gmu.config.product_mapping_entity_id == 0) ? null : gmu.config.product_mapping_entity_id;
                    }
                } else if (entityArr.includes(gmu.config.entity) || (gmu.config.entity == 'articles' && gmu.config.product_mapping_entity == 'articles') || (gmu.config.entity == 'news' && gmu.config.product_mapping_entity == 'news')) {
                    product_mapping_entity = gmu.config.entity;
                    product_mapping_entity_id = gmu.config.entity_id;
                }
                if (product_mapping_entity !== null && product_mapping_entity_id !== null) {
                    $.ajax({
                        url: '/ajax/get-media-drive-file',
                        data: {
                            entity: product_mapping_entity,
                            entity_id: product_mapping_entity_id,
                            primary_entity: gmu.config.entity,
                            primary_entity_id: gmu.config.entity_id,
                            cta_title: cta_title,
                            cta_text: $("#cta_text").val(),
                            media: $("#alternate-media").val(),
                            entity_subpage: gmu.config.entity_subpage_name ?? '',
                        },
                        dataType: 'json',
                        method: 'POST',
                        error: function (xhr, err) {
                            console.log('Error');
                        },
                        success: function (data) {
                            if (data.filePath !== undefined) {
                                dynamicPdfPageUrlRedirect(data.filePath, data.isDelete);
                            }
                        }
                    });
                }
            }

            function dynamicPdfUrlRedirect(uri) {
                activateStudentSession(true)
                var a = document.createElement('a');
                a.setAttribute('href', uri);
                a.setAttribute('target', '_blank');
                a.click();
                a.remove();
            }

            // download assets and redirect dynamic url after lead form submit
            function dynamicPdfPageUrlRedirect(uri, isDelete) {
                var a = document.createElement('a');
                let fileName = uri.substring(uri.lastIndexOf('/') + 1);
                a.setAttribute('href', uri);
                a.setAttribute('target', '_blank');
                a.setAttribute('download', fileName);
                a.click();
                // setTimeout(function() {
                //     document.body.removeChild(a); 
                // }, 100);
                if (isDelete === 'Yes') {
                    $.ajax({
                        url: '/ajax/delete-temp-file', // Adjust to your route
                        method: 'POST',
                        data: { file: fileName },
                        dataType: 'json',
                        success: function (deleteResponse) {
                            if (deleteResponse.status === 'Success') {
                                console.log('File deleted successfully.');
                            } else {
                                console.log('Error deleting file: ' + deleteResponse.message);
                            }
                        },
                        error: function () {
                            console.log('Failed to delete file.');
                        }
                    });
                }
            }
            //lead form functions ends

            function cutOffDownload() {
                const selectedCategory = document.querySelector('input[name="CutOffSearch[category]"]:checked');

                if (selectedCategory) {
                    var categorySelected = document.querySelector(`label[for="${selectedCategory.id}"]`).innerText;
                }


                var latestValue = localStorage.getItem('latestvalue') ?? '';
                var programId = localStorage.getItem('programid') ?? '';
                var collegeId = localStorage.getItem('collegeidCutOff') ?? '';
                var type = localStorage.getItem('type') ?? '';
                var programName = localStorage.getItem('programname') ?? '';
                var courseName = localStorage.getItem('coursename') ?? '';
                var categorySelectedValue = categorySelected ?? '';

                $.ajax({
                    type: "POST",
                    url: "/ajax/generate-pdf-cut-off",
                    data: { categorySelected: categorySelectedValue, latestValue: latestValue, programId: programId, collegeId: collegeId, type: type, programName: programName, courseName: courseName },
                    dataType: "json",
                    success: function (response) {
                        console.log(response);
                        if (response.status == 'Success') {
                            console.log(response);

                            dynamicPdfPageUrlRedirect(response.filePath, response.isDelete);
                        }
                    },
                    failure: function (error) {
                    }
                });

            }

            const ctaData = JSON.parse(localStorage.getItem('ctaData'));
            var url = window.location.href;
            var checkURL = url.includes("immigration", "uk", "canada", "usa", "germany", "australia");
            if (gmu.config.isLoggedIn === false && (!ctaData || Object.keys(ctaData).length === 0) &&
                getCookie('gmu_leadform') != '1'
                && !checkURL
                && gmu.config.showLeadForm
                && gmu.config.show_lead_form != 1
                && gmu.config.isLoggedIn == false
                && (window.location.href.includes('/review/create') == false)
                && (window.location.href.includes('/user-profile') == false)
                && (window.location.href.includes('/getgis-articles') == false)
                && (window.location.href.includes('/scholarship-program') == false)
                && gmu.config.statusCode != '404' && /[^/]*$/.exec($(location).attr('pathname'))[0].indexOf('predictor') < 1
            ) {
                setTimeout(function () {
                    autoPoupShow();
                }, 10000);
            }
        });
    }
}

//using it in user profile too, so not adding it inside the lead call
function displayErrorsLead(errors = undefined) {
    $('.validationError').remove();
    $('.errorMsg').html('');
    $('select, input').removeClass('errorInputField');
    if (typeof errors === 'object') {
        for (const [key, value] of Object.entries(errors)) {
            $('select[name="' + key + '"], input[name="' + key + '"]').parent().append('<p class="error validationError">' + value + '</p>');
            $('select[name="' + key + '"], input[name="' + key + '"]').addClass('errorInputField');
        }
    }

    if (typeof errors === 'string') {
        $('.errorMsg').html(errors);
    }
}

let isModalOpenNews = false;
function preventDefaultScrollNews(event) {
    if (isModalOpenNews) {
        document.body.style.height = '100vh';
        document.body.style.overflowY = 'hidden';
        document.body.style.position = 'fixed';
    }
}

//otp validation || calling this on the form input so not moving inside the lead call
function validateOtpInput(input) {
    // Remove any non-numeric characters from the input value
    input.value = input.value.replace(/[^0-9]/g, '');

    if (input.value.length > 4) {
        input.value = input.value.slice(0, 4);
    }
}

$(document).on('click', '#closeFeeDetails', function () {
    document.getElementById('dialogModal').close();
    $('#fees-breakup').html('').css('display', 'none');
    activateStudentSession(true);
});

function activateStudentSession(isReferesh = null) {
    var student_id = localStorage.getItem('studentId') ?? '';
    var activity_id = localStorage.getItem('activityId') ?? '';
    var csrf = $("input[name='_csrf-frontend']").val();
    $.ajax({
        url: '/lead-v4/student-session-activate',
        data: { student_id: student_id, activity_id: activity_id },
        dataType: 'json',
        method: 'POST',
        beforeSend: function () {
            $('.primaryBtn').prop('disabled', true);
        },
        error: function (xhr, err) {
            $('.primaryBtn').prop('disabled', false)
            displayErrorsLead('Something went wrong, please try again!')
        },
        complete: function () {
            $('.primaryBtn').prop('disabled', false);
        },
        success: function (data) {
            if (data.success == true) {
                setCookie('gmu_leadform', 1, 10);
                if (isReferesh) {
                    location.reload(true);
                }
            } else {
                document.querySelector('#lead-form-js-new').style.display = "none";
            }
        }
    });
}

function detailedFeePopup() {
    var programName = localStorage.getItem('programName');
    var collegeProgramId = localStorage.getItem('collegeProgramId');

    if (collegeProgramId !== 'undefined' && collegeProgramId !== null) {
        $.get('/program-fees-structure?program=' + programName + '&collegeProgramId=' + collegeProgramId, function (detailedFee) {
            if (detailedFee !== null) {
                $('#fees-breakup').html(detailedFee).css('display', 'block');
                document.getElementById('dialogModal').showModal();
                $('table').wrap("<div class='table-responsive'></div>");
            } else {
                activateStudentSession(true);
            }
            localStorage.removeItem('program');
            localStorage.removeItem('collegeProgramId');
        });
    } else {
        activateStudentSession(true);
    }
}

function downLoadPdf(subTopicID) {
    var pageIndex = 0;
    var isActiveIndex = $(".gmu-mock-listItem").hasClass("active");
    $(".gmu-mock-listItem").each(function (index) {
        if ($(this).hasClass("active")) {
            pageIndex = $(this).attr('data-tab');
        }
    });
    $.ajax({
        url: '/ajax/qns-pdf',
        data: { subTopicID: subTopicID, pageIndex: pageIndex },
        dataType: 'json',
        method: 'POST',
        success: function (data) {

            var a = document.createElement('a');
            a.setAttribute('href', data.filePath);
            a.setAttribute('download', data.fileName);
            a.click();
            if (data.isDelete === 'Yes') {
                $.ajax({
                    url: '/ajax/delete-temp-file', // Adjust to your route
                    method: 'POST',
                    data: { file: data.fileName },
                    dataType: 'json',
                    success: function (deleteResponse) {
                        if (deleteResponse.status === 'Success') {
                            console.log('File deleted successfully.');
                        } else {
                            console.log('Error deleting file: ' + deleteResponse.message);
                        }
                    },
                    error: function () {
                        console.log('Failed to delete file.');
                    }
                });
            }
            location.reload();
        }
    });
}


$(document).on('click', '#create_pdf', function () {
    var pageIndex = 0;
    var subTopicID = $(this).attr('data-subtopic-id');
    $(".gmu-mock-listItem").each(function (index) {
        if ($(this).hasClass("active")) {
            pageIndex = $(this).attr('data-tab');
        }
    });
    $.ajax({
        url: '/ajax/qns-pdf',
        data: { subTopicID: subTopicID, pageIndex: pageIndex },
        dataType: 'json',
        method: 'POST',
        success: function (data) {

            var a = document.createElement('a');
            a.setAttribute('href', data.filePath);
            a.setAttribute('download', data.fileName);
            a.click();
            if (data.isDelete === 'Yes') {
                $.ajax({
                    url: '/ajax/delete-temp-file', // Adjust to your route
                    method: 'POST',
                    data: { file: data.fileName },
                    dataType: 'json',
                    success: function (deleteResponse) {
                        if (deleteResponse.status === 'Success') {
                            console.log('File deleted successfully.');
                        } else {
                            console.log('Error deleting file: ' + deleteResponse.message);
                        }
                    },
                    error: function () {
                        console.log('Failed to delete file.');
                    }
                });
            }
            location.reload();
        }
    });
});
$(".attempt-more").click(function () {
    $(".gmu-mock-listItem").each(function (index) {
        if ($(this).hasClass("active") == true) {
            var idIndex = $(this).next().attr('data-tab');
            //alert("#tab-"+idIndex);
            $("#tab-" + idIndex).trigger('click');
            $('html, body').animate({
                scrollTop: $(this).offset().top
            }, 1000);
            return false;
        }
    });
});
function checkAnswer(id) {
    if ($(id).hasClass('notLogin')) {
        $(id).siblings().removeClass('selected');
        $(id).addClass('selected');
        return false;
    }
    var $questionContent = $(id).closest('.gmu-mock-questionContent')
    var correctAnswer = $questionContent.find('.gmu-mock-optionList').data('answer');
    if ($(id).hasClass('isTrue')) {
        return false;
    }
    $(id).addClass('selected');
    var $selectedOption = $questionContent.find('.gmu-mock-optionListItem.selected');

    $questionContent.find('.gmu-mock-optionListItem').removeClass('selected');
    $questionContent.find('.gmu-mock-optionListItem').removeClass('isTrue isFalse');
    if ($selectedOption.length > 0) {
        var selectedValue = $selectedOption.data('value');
        if (selectedValue === correctAnswer) {
            $selectedOption.addClass('isTrue');
        } else {
            $selectedOption.addClass('isFalse');
            $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').addClass('isTrue');
        }
    } else {
        $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').addClass('isTrue');
    }
    $questionContent.find('.showAnswer').hide();
    var student_id = localStorage.getItem('studentId');
    var questionID = $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').attr('question-id');
    var answer = correctAnswer;
    var dataValue = $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').data('value');
    $.ajax({
        type: "POST",
        url: "/ajax/save-ques-ans",
        data: { questionID: questionID, answer: answer, dataValue: dataValue, student_id: student_id },
        dataType: "json",
        success: function (response) {
        },
        failure: function (error) {
        }
    });
};

function saveLocalStorageAnswer() {

    const data = localStorage.getItem("userData");
    var jasonData = JSON.parse(data);
    var student_id = localStorage.getItem('studentId');
    var questionID = jasonData['questionData'][0]['questionID'];
    var answer = jasonData['questionData'][0]['answer'];
    var dataValue = jasonData['questionData'][0]['dataValue'];
    localStorage.removeItem("userData");
    $.ajax({
        type: "POST",
        url: "/ajax/save-ques-ans",
        data: { questionID: questionID, answer: answer, dataValue: dataValue, student_id: student_id },
        dataType: "json",
        success: function (response) {
        },
        failure: function (error) {
        }
    });
}