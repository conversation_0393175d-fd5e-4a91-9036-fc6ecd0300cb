/************* Own Crousel Logic ************/

$(document).ready(function () {
    const maxWidth = window.matchMedia("(max-width: 1023px)");
    if (maxWidth.matches) {
        $(".crsSubmit").val("Show Report").css({ width: "100%" });
    }
    /*********************  Logic for Select 2 options **************/
    $(".gis-pr-form-select-2").select2({
        placeholder: "Select",
        dropdownAutoWidth: true,
        dropdownCssClass: "gis-pr-form-select-2",
    });

    // not required now
    // if (!gmu.config.isLoggedIn) {
    //     // localStorage.clear()
    //     // sessionStorage.clear()
    // }

    let currentStep = 0;

    /******* make the select feilds border color (if error occurs)*********/
    $(document).on("change", "#calculator", function () {
        if (JSON.parse(localStorage.getItem('cta-data')) !== null) {
            var divElem = document.getElementById('calculator');
            var inputElements = divElem.querySelectorAll("input, select");
            for (const input of inputElements) {
                if (input.localName == 'select' && ($(".field-" + input.id + " .select2-selection--single").css("border") == '1px solid rgb(229, 88, 93)') && input.value != '') {
                    $(".field-" + input.id + " .select2-selection--single").css("border", "1px solid #d8d8d8");
                    $(".field-" + input.id + " .help-block").html("");
                }
            }
        }
    });
    // /************ Score Unlock (not required right now)*******************/
    // $(".gis-pr-form-unlock,.submitForm").on("click", function () {
    //     var form = document.getElementById('calculator');
    //     var data = new FormData(form);
    //     for (var [key, value] of data) {
    //         sessionStorage.setItem(key, value);
    //     }
    //     if (sessionStorage.getItem('calculatorType') == 'crsCalculator') {
    //         localStorage.setItem('cta-data', JSON.stringify({
    //             ctaStep: currentStep,
    //             formType: "crs",
    //             formsubmit: "firstLogin"
    //         }));
    //     }
    //     if (!gmu.config.isLoggedIn) {
    //         var buttonVal = $(this).attr("data-ctatext");
    //     }
    //     else {
    //         var buttonVal = $(this).val();
    //     }
    //     captureLeadActivity(buttonVal, buttonVal + ' Clicked');
    // });
    // /******** populate error ************/
    $(".crsSubmit").on("click", function () {
        localStorage.setItem('cta-data', JSON.stringify({
            ctaStep: currentStep,
            formType: "crs",
            formsubmit: "try"
        }));
        var divElem = document.getElementById('calculator');
        var inputElements = divElem.querySelectorAll("input, select");
        for (const input of inputElements) {
            if (input.localName == 'select') {
                $(".field-" + input.id + " .select2-selection--single").css("border", "1px solid #d8d8d8");
                $("." + input.id + " .help-block").html("");
                if (input.value == '' && $("#" + input.id).parent().closest('.gis-pr-form-questions').css("display") != 'none') {
                    $(".field-" + input.id + " .select2-selection--single").css("border", "1px solid #E5585D");
                    $("." + input.id + " .help-block").html("Please select any option");
                }
            }
            else if (input.attributes.type.value == 'radio') {
                var requiredClass = $(document.querySelector('input[name="' + input.attributes.name.value + '"]')).parent();
                var classname = $(document.querySelector('input[name="' + input.attributes.name.value + '"]')).parent().parent().prop('className');
                $("." + classname.replace(/[ ]+/g, ".") + " .help-block").html('');
                let checkBox = document.querySelector('input[name="' + input.attributes.name.value + '"]:checked');
                if ((checkBox == '' || checkBox == null) && $(requiredClass).hasClass("required") && $("." + classname.replace(/[ ]+/g, ".")).parent().closest('.gis-pr-form-questions').css("display") != 'none') {
                    $("." + classname.replace(/[ ]+/g, ".") + " .help-block").html('Please select any option');
                }

            }
        }
        var scrollDiv = $('.help-block:not(:empty):first');
        if (scrollDiv.length > 0) {
            $('html, body').animate({
                scrollTop: $(scrollDiv).offset().top - 180
            }, 100);
        }
        else {
            var form = document.getElementById('calculator');
            var data = new FormData(form);
            for (var [key, value] of data) {
                sessionStorage.setItem(key, value);
            }
            localStorage.setItem("cta-data", JSON.stringify({
                ctaStep: 0,
                formsubmit: "no",
                formType: "crs"
            }));
            $("#calculator").submit();
        }
    });
    /*****************Reset Button ********************/
    $('.gis-pr-form-score-reset').on("click", function () {
        const formToReset = document.getElementById('calculator');
        formToReset.reset();
        localStorage.clear();
        sessionStorage.clear();
    });

    /*********** Populate the form ******/
    // if (gmu.config.isLoggedIn) {
    console.log(sessionStorage.length)
    if (JSON.parse(localStorage.getItem('cta-data')) !== null) {
        Object.keys(sessionStorage).forEach((key) => {
            var changeCase = key.toLowerCase()
            if (changeCase.includes("calculator")) {
                // for radio buttons
                let elements = document.getElementsByName(key);
                let elementsId = elements[0].id;
                for (i = 0; i < elements.length; i++) {
                    if (elements[i].type == "radio") {
                        if (elements[i].value == sessionStorage.getItem(key)) {
                            elements[i].checked = true;
                        }
                        else {
                            elements[i].checked = false;
                        }
                    }
                }
                elements[0].value = sessionStorage.getItem(key);
                if ($(elements[0]).hasClass('select2-hidden-accessible')) {
                    $("#" + elementsId).select2(({ placeholder: "Select", dropdownAutoWidth: true }));
                }
            }
        });
        calculatorFieldValidation();
        if (typeof $('#CrsMaritalStatus').val() != undefined && ($('#CrsMaritalStatus').val() == 'Married' || $('#CrsMaritalStatus').val() == 'Common Law')) {
            $('.spouseData').show();
            if ($('input:radio[name="CrsCalculator[citizenship]"]').is(':checked') && $('input:radio[name="CrsCalculator[citizenship]"]:checked').val() == 1) {
                $('.accompanyYou').hide();
            }
            else {
                $('.accompanyYou').show();
            }
        } else {
            $('.spouseData').hide();
        }
        if ($('input:radio[name="CrsCalculator[canadianDegree]"]').is(':checked') && $('input:radio[name="CrsCalculator[canadianDegree]"]:checked').val() == 1) {
            $('.canadianEducation').show();
        } else {
            $('.canadianEducation').hide();
        }
        if ($('input:radio[name="CrsCalculator[validJob]"]').is(':checked') && $('input:radio[name="CrsCalculator[validJob]"]:checked').val() == 1) {
            $('.noc').show();
        } else {
            $('.noc').hide();
        }
        if (typeof $('#CrsSpouseLatestResult').val() != undefined && ($('#CrsSpouseLatestResult').val() == 1)) {
            $('.spouseLanguageTest').show();
            var spousePrimaryExam = $('#crsSpousePrimaryExam').val();
            populateSpouseSecondaryExam(spousePrimaryExam)

        } else {
            $('.spouseLanguageTest').hide();
        }
        if ($('input:radio[name="CrsCalculator[latestResult]"]').is(':checked') && $('input:radio[name="CrsCalculator[latestResult]"]:checked').val() == 1) {
            $('.primaryTestResult').show();
            $('.notEligible').hide();
            $('.submitForm').attr('disabled', false);
            $('.skipForm').show();
            var primaryExam = $('#crsPrimaryExam').val();
            populateSecondaryExam(primaryExam);

            //to populate dropdown of secondary 
            if (JSON.parse(localStorage.getItem('cta-data')) !== null) {
                Object.keys(sessionStorage).forEach((key) => {
                    var changeCase = key.toLowerCase()
                    if (changeCase.includes("calculator")) {
                        let elements = document.getElementsByName(key);
                        elements[0].value = sessionStorage.getItem(key);

                        if (elements[0].localName == 'select') {
                            $(".field-" + elements[0].id + " .select2-selection--single").css("border", "1px solid #d8d8d8");
                            $(".field-" + elements[0].id).removeClass('has-error');
                            $("." + elements[0].id + " .help-block").html("");
                            // if add them in a single if then element[0] returns error for one input feild 

                            if (elements[0].value == '' && $("#" + elements[0].id).parent().closest('.gis-pr-form-questions').css("display") != 'none') {
                                $(".field-" + elements[0].id + " .select2-selection--single").css("border", "1px solid #E5585D");
                                $(".field-" + elements[0].id).addClass('has-error');
                                $("." + elements[0].id + " .help-block").html("Please select any option");
                            }
                        }
                    }
                });
            }
            if (typeof $('#crsSecondaryLanguage').val() != undefined && ($('#crsSecondaryLanguage').val() != 'NA')) {
                var secondaryLanguage = $('#crsSecondaryLanguage').val();
                showSecondaryExamScores(secondaryLanguage);

                // to populate secondarylanguage score
                if (JSON.parse(localStorage.getItem('cta-data')) !== null) {
                    Object.keys(sessionStorage).forEach((key) => {
                        var changeCase = key.toLowerCase()
                        if (changeCase.includes("calculator")) {
                            let elements = document.getElementsByName(key);
                            elements[0].value = sessionStorage.getItem(key);
                            if (elements[0].localName == 'select') {
                                $(".field-" + elements[0].id + " .select2-selection--single").css("border", "1px solid #d8d8d8");
                                $(".field-" + elements[0].id).removeClass('has-error');
                                $("." + elements[0].id + " .help-block").html("");
                                // if add them in a single if then element[0] returns error for one input feild 
                                if (elements[0].value == '' && $("#" + elements[0].id).parent().closest('.gis-pr-form-questions').css("display") != 'none') {
                                    $(".field-" + elements[0].id + " .select2-selection--single").css("border", "1px solid #E5585D");
                                    $(".field-" + elements[0].id).addClass('has-error');
                                    $("." + elements[0].id + " .help-block").html("Please select any option");
                                }
                            }
                        }
                    });
                }
            }
            crsTimeline();
        }
        else if ($('input:radio[name="CrsCalculator[latestResult]"]').is(':checked') && $('input:radio[name="CrsCalculator[latestResult]"]:checked').val() == 0) {
            $('.primaryTestResult').hide();
            $('.skipForm').hide();
            $('.notEligible').show();
            $('.submitForm').attr('disabled', true);
            var elem = document.getElementById("latestResult");
            elem.scrollIntoView();
            crsTimeline();
        }
    }
    crsTimeline();

    /****** scroll in mobile view *****/
    if (window.matchMedia("(max-width: 1023px)").matches) {
        const conent = document.querySelector('#gis-pr-timeline');
        conent.scrollLeft += 130
    }

    var scrollDiv = $('.help-block:not(:empty):first');
    if (scrollDiv.length > 0) {
        $('html, body').animate({
            scrollTop: $(scrollDiv).offset().top - 120
        }, 100);
    }

    /******* if the form is filled then show the result User is already logged in before filling the form *****/
    if (JSON.parse(localStorage.getItem('cta-data')) !== null && JSON.parse(localStorage.getItem('cta-data'))['formType'] == "crs" && JSON.parse(localStorage.getItem('cta-data'))['formsubmit'] == "no") {
        var calculatorData = {};
        for (var len = sessionStorage.length, i = 0; i < len; i++) {
            var key = sessionStorage.key(i);
            var changeCase = key.toLowerCase()
            if (changeCase.includes("calculator") || changeCase.includes("converter")) {
                calculatorData[key] = sessionStorage.getItem(key);
            }
        }
        updateScore(calculatorData);
        $("#showScore").show();
        $('html, body').animate({
            scrollTop: $("#showScore").offset().top - 120
        }, 100);
        sessionStorage.clear();
        localStorage.clear();
    }

    /******* If the submit button clicked and then hard refesh the page (remove te sesion and local before populate the form********/
    if (JSON.parse(localStorage.getItem('cta-data')) !== null && JSON.parse(localStorage.getItem('cta-data'))['formType'] == "crs" && JSON.parse(localStorage.getItem('cta-data'))['formsubmit'] == "try") {
        sessionStorage.clear();
        localStorage.clear();
    }
    /***** if user is logged in from this page (not required right now)*********/
    // if (JSON.parse(localStorage.getItem('cta-data')) !== null && JSON.parse(localStorage.getItem('cta-data'))['formType'] == "crs") //&& JSON.parse(localStorage.getItem('cta-data'))['formsubmit'] == "firstLogin"
    // {
    //     var calculatorData = {};
    //     for (var len = sessionStorage.length, i = 0; i < len; i++) {
    //         var key = sessionStorage.key(i);
    //         var changeCase = key.toLowerCase()
    //         if (changeCase.includes("calculator") || changeCase.includes("converter")) {
    //             calculatorData[key] = sessionStorage.getItem(key);
    //         }
    //     }
    //     updateScore(calculatorData);
    //     var scrollDiv = $('.help-block:not(:empty):first');
    //     if (scrollDiv.length == 0) {
    //         $("#showScore").show();
    //         $('html, body').animate({
    //             scrollTop: $("#showScore").offset().top - 120
    //         }, 100);
    //     }
    //     // sessionStorage.clear();
    //     // localStorage.clear();
    // }
    // }
});
function calculatorFieldValidation() {
    var divElem = document.getElementById('calculator');
    var inputElements = divElem.querySelectorAll("input, select");
    for (const input of inputElements) {
        if (input.localName == 'select') {
            $(".field-" + input.id + " .select2-selection--single").css("border", "1px solid #d8d8d8");
            $("." + input.id + " .help-block").html("");
            if (input.value == '' && $("#" + input.id).parent().closest('.gis-pr-form-questions').css("display") != 'none') {
                $(".field-" + input.id + " .select2-selection--single").css("border", "1px solid #E5585D");
                $(".field-" + input.id).addClass('has-error');
                $("." + input.id + " .help-block").html("Please select any option");
            }
        }
        else if (input.attributes.type.value == 'radio') {
            var requiredClass = $(document.querySelector('input[name="' + input.attributes.name.value + '"]')).parent();
            var classname = $(document.querySelector('input[name="' + input.attributes.name.value + '"]')).parent().parent().prop('className');
            $("." + classname.replace(/[ ]+/g, ".") + " .help-block").html('');

            let checkBox = document.querySelector('input[name="' + input.attributes.name.value + '"]:checked');
            if ((checkBox == '' || checkBox == null) && $(requiredClass).hasClass("required") && $("." + classname.replace(/[ ]+/g, ".")).parent().closest('.gis-pr-form-questions').css("display") != 'none') {
                $("." + classname.replace(/[ ]+/g, ".") + " .help-block").html('Please select any option');
            }
        }
    }
}
//Function for get my score data
function updateScore(calculatorData) {
    $.ajax({
        type: "POST",
        url: "/calculator/all-calculate",
        data: { calculatorData: calculatorData },
        dataType: "text",
        success: function (responseData) {
            if (calculatorData['calculatorType'] == 'crsCalculator') {
                var data = JSON.parse(responseData);
                $($(".gis-pr-form-score")).html("My Score&nbsp;:&nbsp;" + data['score']);
                $($("#score189")).html("Your Score&nbsp;:&nbsp;" + data['score'] + "/1200");
            }
        }
    });
}

/************ Blog unlock function **************/
$(".gis-pr-continue-btn").on("click", function () {
    $(".gis-pr-blog-unlock").css("height", "auto");
    $(".gis-pr-continue-btn").css("display", "none");
});
/************CRS Calculator input show and hide*************/

function crsTimeline() {
    var numItems = $('.gis-pr-step');
    var timeline = $('.gis-pr-timeline-line');
    var lastTimeline = $('.gis-pr-step-number');
    var lastTimelineCheck = $('.gis-pr-step-check');

    if (typeof ($('#CrsMaritalStatus').val()) != undefined && $('#CrsMaritalStatus').val() != '') {
        var spouseData = $(".spouseData").is(":visible");
        if (!spouseData) {
            $(lastTimeline['0']).addClass('active');
            $(lastTimelineCheck['0']).addClass('checked');
            $(timeline['0']).addClass('active');
        }
        else {
            if ($('input:radio[name="CrsCalculator[citizenship]"]:checked').val() == 1) {
                $(lastTimeline['0']).addClass('active');
                $(lastTimelineCheck['0']).addClass('checked');
                $(timeline['0']).addClass('active');
            }
            else if ($('input:radio[name="CrsCalculator[citizenship]"]:checked').val() == 0 && $('input:radio[name="CrsCalculator[accompaniedBySpouse]"]').is(':checked')) {
                $(lastTimeline['0']).addClass('active');
                $(lastTimelineCheck['0']).addClass('checked');
                $(timeline['0']).addClass('active');
            }
            else {
                $(lastTimeline['0']).removeClass('active');
                $(lastTimelineCheck['0']).removeClass('checked');
                $(timeline['0']).removeClass('active');
            }
        }

    }
    if ((typeof $('#CrsAge').val() != undefined && ($('#CrsAge').val() != '')) && (typeof $('#CrsEducation').val() != undefined && ($('#CrsEducation').val() != '')) && ($('input:radio[name="CrsCalculator[canadianDegree]"]:checked').val())) {
        var canadianEducation = $(".canadianEducation").is(":visible");
        if (!canadianEducation) {
            $(lastTimeline['1']).addClass('active');
            $(lastTimelineCheck['1']).addClass('checked');
            $(timeline['1']).addClass('active');
        }
        else if ($("#CrsCanadaEducation").val() != '') {
            $(lastTimeline['1']).addClass('active');
            $(lastTimelineCheck['1']).addClass('checked');
            $(timeline['1']).addClass('active');
        }
        else {
            $(lastTimeline['1']).removeClass('active');
            $(lastTimelineCheck['1']).removeClass('checked');
            $(timeline['1']).removeClass('active');
        }
    }
    if ($('input:radio[name="CrsCalculator[latestResult]"]:checked').val() == 1) {
        for (hideStep = 3; hideStep < numItems.length; hideStep++) {
            $(numItems[hideStep]).show();
        }
        $(timeline['2']).removeClass('active');
        $(timeline['2']).show();
        $(lastTimeline['2']).removeClass('active');
        $(lastTimelineCheck['2']).removeClass('checked');

        if ((typeof ($('#crsPrimaryReadingScore').val()) != undefined && $('#crsPrimaryReadingScore').val() != '') && (typeof ($('#crsPrimaryWritingScore').val()) != undefined && $('#crsPrimaryWritingScore').val() != '') && (typeof ($('#crsPrimaryListeningScore').val()) != undefined && $('#crsPrimaryListeningScore').val() != '') && (typeof ($('#crsPrimarySpeakingScore').val()) != undefined && $('#crsPrimarySpeakingScore').val() != '')) {
            if ($('#crsSecondaryLanguage').val() != 'NA') {
                if ((typeof ($('#crsSecondaryReadingScore').val()) != undefined && $('#crsSecondaryReadingScore').val() != '') && (typeof ($('#crsSecondaryWritingScore').val()) != undefined && $('#crsSecondaryWritingScore').val() != '') && (typeof ($('#crsSecondaryListeningScore').val()) != undefined && $('#crsSecondaryListeningScore').val() != '') && (typeof ($('#crsSecondarySpeakingScore').val()) != undefined && $('#crsSecondarySpeakingScore').val() != '')) {
                    $(lastTimeline['2']).addClass('active');
                    $(lastTimelineCheck['2']).addClass('checked');
                    $(timeline['2']).addClass('active');
                }
            }
            else {
                $(lastTimeline['2']).addClass('active');
                $(lastTimelineCheck['2']).addClass('checked');
                $(timeline['2']).addClass('active');
            }
        }
    }
    if ($('input:radio[name="CrsCalculator[latestResult]"]:checked').val() == 0) {
        $(lastTimeline['2']).addClass('active');
        $(lastTimelineCheck['2']).addClass('checked');
        $(timeline['2']).addClass('active');
        for (hideStep = 3; hideStep < numItems.length; hideStep++) {
            $(numItems[hideStep]).hide();
        }
        $(timeline['2']).hide();
    }

    if ((typeof $('#CrsSkilled').val() != undefined && ($('#CrsSkilled').val() != '')) && (typeof $('#CrsForeignSkilled').val() != undefined && ($('#CrsForeignSkilled').val() != '')) && ($('input:radio[name="CrsCalculator[certificate]"]:checked').val())) {
        $(lastTimeline['3']).addClass('active');
        $(lastTimelineCheck['3']).addClass('checked');
        $(timeline['3']).addClass('active');
    }
    if (($('input:radio[name="CrsCalculator[validJob]"]:checked').val()) && ($('input:radio[name="CrsCalculator[nomination]"]:checked').val()) && ($('input:radio[name="CrsCalculator[relativeInCanada]"]:checked').val())) {
        var noc = $(".noc").is(":visible");
        if (!noc) {
            $(lastTimeline['4']).addClass('active');
            $(lastTimelineCheck['4']).addClass('checked');
        }
        else if ($("#CrsNoc").val() != "") {
            $(lastTimeline['4']).addClass('active');
            $(lastTimelineCheck['4']).addClass('checked');
        }
        else {
            $(lastTimeline['4']).removeClass('active');
            $(lastTimelineCheck['4']).removeClass('checked');
        }
    }
}
// martital status div
$(document).on('change', '#CrsMaritalStatus', function () {
    var married = $(this).val();
    if (typeof married != undefined && (married == 'Married' || married == 'Common Law')) {
        $('.spouseData').show();
    } else {
        $('.spouseData').hide();
    }
    crsTimeline();
});

/// Age and education div
$(document).on('change', '#CrsAge', function () {
    crsTimeline();
});
$(document).on('change', '#CrsEducation', function () {
    crsTimeline();
});
$(document).on('change', 'input:radio[name="CrsCalculator[canadianDegree]"]', function () {
    if (this.checked && this.value == 1) {
        $('.canadianEducation').show();
    } else {
        $('.canadianEducation').hide();
    }
    crsTimeline();
});
$(document).on('change', '#CrsCanadaEducation', function () {
    crsTimeline();
});
//official Language
$(document).on('change', 'input:radio[name="CrsCalculator[latestResult]"]', function () {
    if (this.checked && this.value == 1) {
        $('.primaryTestResult').show();
        $('.notEligible').hide();
        $('.submitForm').attr('disabled', false);
        $('.skipForm').show();
        $("#seperateDiv1").addClass("seperateDiv");
        $('.next-form-btn').show();
        crsTimeline();

    } else {
        $('.primaryTestResult').hide();
        $('.skipForm').hide();
        $('.notEligible').show();
        $('.submitForm').attr('disabled', true);
        $("#seperateDiv1").removeClass("seperateDiv");
        $('.next-form-btn').hide();
        var elem = document.getElementById("latestResult");
        elem.scrollIntoView();
        crsTimeline();
        $("#showScore").hide();
    }
});
$(document).on('change', '#crsPrimaryExam', function () {
    var primaryExam = $(this).val();
    populateSecondaryExam(primaryExam);
});
$(document).on('change', '#crsPrimaryReadingScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsPrimaryWritingScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsPrimaryListeningScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsPrimarySpeakingScore', function () {
    crsTimeline();
});

$(document).on('change', '#crsSecondaryLanguage', function () {
    var secondaryLanguage = $(this).val();
    if ($("#seperateDiv .select2-selection--single").css("border") == '1px solid rgb(229, 88, 93)') {
        $("#seperateDiv .help-block").html("");
        $("#seperateDiv .select2-selection--single").css("border", "1px solid #d8d8d8");
    }
    showSecondaryExamScores(secondaryLanguage);
    crsTimeline();
});
$(document).on('change', '#crsSecondaryReadingScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsSecondaryWritingScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsSecondaryListeningScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsSecondarySpeakingScore', function () {
    crsTimeline();
});
//work Experience
$(document).on('change', '#CrsSkilled', function () {
    crsTimeline();
});
$(document).on('change', '#CrsForeignSkilled', function () {
    crsTimeline();
});
$(document).on('change', 'input:radio[name="CrsCalculator[certificate]"]', function () {
    crsTimeline();
});
//Additional Info
$(document).on('change', 'input:radio[name="CrsCalculator[validJob]"]', function () {
    if (this.checked && this.value == 1) {
        $('.noc').show();
    } else {
        $('.noc').hide();
    }
    crsTimeline();
});
$(document).on('change', 'input:radio[name="CrsCalculator[nomination]"]', function () {
    crsTimeline();
});
$(document).on('change', 'input:radio[name="CrsCalculator[relativeInCanada]"]', function () {
    crsTimeline();
});

$(document).on('change', 'input:radio[name="CrsCalculator[citizenship]"]', function () {
    crsTimeline();
    if (this.checked && this.value == 1) {
        $('.accompanyYou').hide();
    } else {
        $('.accompanyYou').show();
    }
});
$(document).on('change', 'input:radio[name="CrsCalculator[accompaniedBySpouse]"]', function () {
    crsTimeline();

});
$(document).on('change', '#CrsSpouseLatestResult', function () {
    crsTimeline();
    if ($(this).val() == 1) {
        $('.spouseLanguageTest').show();
    } else {
        $('.spouseLanguageTest').hide();
    }
});
$(document).on('change', '#crsSpousePrimaryExam', function () {
    var spousePrimaryExam = $(this).val();
    populateSpouseSecondaryExam(spousePrimaryExam)
});
$(document).on('change', '#CrsNoc', function () {
    crsTimeline();

});

function populateSecondaryExam(primaryExam) {
    if (typeof primaryExam != undefined) {
        let primaryExamSection = examSection[primaryExam];
        $.each(primaryExamSection, function (section, value) {
            let dropdownValues = '<option value="">Select</option>';
            $.each(value, function (key, bandScore) {
                dropdownValues += '<option value="' + key + '">' + bandScore + '</option>';
            });
            $('#crsPrimary' + section + 'Score').html(dropdownValues);
        });
        let updatedExamTypes = {};
        if (primaryExam == 'IELTS' || primaryExam == 'CELPIP') {
            updatedExamTypes['TEF'] = 'TEF Canada';
            updatedExamTypes['TCF'] = 'TCF Canada';
        } else {
            updatedExamTypes['IELTS'] = 'IELTS';
            updatedExamTypes['CELPIP'] = 'CELPIP - G';
        }
        updatedExamTypes['NA'] = 'Not Applicable';

        let secondaryExamDropdownValues = '<option value="">Select</option>';
        $.each(updatedExamTypes, function (examKey, examName) {
            secondaryExamDropdownValues += '<option value="' + examKey + '">' + examName + '</option>';
        });
        $('#crsSecondaryLanguage').html(secondaryExamDropdownValues);
    }
}
function showSecondaryExamScores(secondaryLanguage) {
    if (typeof secondaryLanguage != undefined && secondaryLanguage != 'NA' && secondaryLanguage != '') {
        let secondaryLanguageSection = examSection[secondaryLanguage];
        $.each(secondaryLanguageSection, function (section, value) {
            let dropdownValues = '<option value="">Select</option>';
            $.each(value, function (key, bandScore) {
                dropdownValues += '<option value="' + key + '">' + bandScore + '</option>';
            });
            $('#crsSecondary' + section + 'Score').html(dropdownValues);
        });
        $('.secondaryScore').show();
        $("#seperateDiv").addClass("seperateDiv");
    } else {
        $('.secondaryScore').hide();
        $("#seperateDiv").removeClass("seperateDiv");
    }
}
function populateSpouseSecondaryExam(spousePrimaryExam) {
    if (typeof spousePrimaryExam != undefined && spousePrimaryExam != 'NA') {
        let spousePrimaryExamSection = examSection[spousePrimaryExam];
        $.each(spousePrimaryExamSection, function (section, value) {
            let dropdownValues = '<option value="">Select</option>';
            $.each(value, function (key, bandScore) {
                dropdownValues += '<option value="' + key + '">' + bandScore + '</option>';
            });
            $('#crsSpousePrimary' + section + 'Score').html(dropdownValues);
        });
    }
}
document.querySelectorAll('select.form-control, .selectCourse').forEach((select) => {
    select.addEventListener('change', (e) => {
        e.target.style.border = '1px solid #d8d8d8'
    })
})


function captureLeadActivity(buttonText = NULL, response = NULL) {

    let flag = false;
    var action = $("#calculator").attr('action');
    if (buttonText == 'Unlock Score') {
        if (getCookie('Activity[unlock-score]') != action) {
            flag = true;
            setCookie('Activity[unlock-score]', action, 1);
        }
    }
    else if (buttonText == 'Next') {
        if (getCookie('Activity[next]') != action) {
            flag = true;
            setCookie('Activity[next]', action, 1);
        }
    }

    if (flag == true) {

        var postData = $("#calculator").serializeArray();
        postData.push({ name: 'captureAction', value: 1 });
        postData.push({ name: 'buttonText', value: buttonText });
        postData.push({ name: 'response', value: response });
        $.ajax({
            type: "POST",
            url: action,
            data: postData,
            success: function (response) {

            }

        });
    }
}
