$(document).ready(function () {
    /*********************  Logic for Select 2 options **************/
    $(".gis-pr-form-select-2").select2({
        placeholder: "Select",
        dropdownAutoWidth: true,
        dropdownCssClass: "gis-pr-form-select-2",
    });

    if (!gmu.config.isLoggedIn) {
        localStorage.clear()
        sessionStorage.clear()
    }

    /*  fill the form data*/
    if (sessionStorage.length > 1) {
        Object.keys(sessionStorage).forEach((key) => {
            var changeCase = key.toLowerCase()
            if (changeCase.includes("calculator") || changeCase.includes("converter")) {
                // for radio buttons
                let elements = document.getElementsByName(key);
                let elementsId = elements[0].id;
                for (i = 0; i < elements.length; i++) {
                    if (elements[i].type == "radio") {
                        if (elements[i].value == sessionStorage.getItem(key)) {
                            elements[i].checked = true;
                        }
                        else {
                            elements[i].checked = false;
                        }
                    }
                }
                elements[0].value = sessionStorage.getItem(key);
                if ($(elements[0]).hasClass('select2-hidden-accessible')) {
                    $("#" + elementsId).select2(({ placeholder: "Select", dropdownAutoWidth: true }));
                }

            }
        });
    }

    // Initialize the current step
    let currentStep = 0;
    const $steps = $(".gis-pr-step");
    const $stepsNumber = $(".gis-pr-step-number");
    const $checkIcon = $(".gis-pr-step-check");
    const $forms = $(".gis-pr-form");
    const $lines = $(".gis-pr-timeline-line");
    const $nextbtn = $(".next-form-btn");
    const $myscore = $(".gis-pr-form-score");
    const maxWidth = window.matchMedia("(max-width: 1023px)");

    // show the current div after login  either from unlock or show detail report section
    if (gmu.config.isLoggedIn && (JSON.parse(localStorage.getItem('cta-data')) !== null)) {
        currentStep = JSON.parse(localStorage.getItem('cta-data'))['ctaStep'];
        updateStep(currentStep);
        if (currentStep > 0) {
            $("#prev-form-btn").show();
        }
        if (currentStep == ($steps.length - 1)) {
            $($nextbtn).hide();
            $("#getmyscore").show();
            if (maxWidth.matches) {
                $("#getmyscore").val("Show Report").css({ width: "100%" });
                $("#gis-pr-form-score-reset").css("display", "block");
            }
        }

        // for PR calculator as martaila can be set after form submission
        if ((JSON.parse(localStorage.getItem('cta-data'))['showDetial'] == 'yes')) {
            $("#getmyscore").show();
            if (maxWidth.matches) {
                $("#getmyscore").val("Show Report").css({ width: "100%" });
                if (currentStep == ($steps.length - 1)) {
                    $("#gis-pr-form-score-reset").css("display", "block");
                }
            }
            $('#hideReport').hide();
        }
        else {
            var formId = $($forms[currentStep]).attr("id");
            var setscore = true;
            if (sessionStorage.getItem('calculatorType') == 'clbConverter') {
                setscore = false
            }
            var checkValidation = calculatorFieldValidation(formId, setscore);
            if (checkValidation && (currentStep == ($steps.length - 1))) {
                $($stepsNumber[currentStep]).addClass("active").removeClass("complete");
                $($checkIcon[currentStep]).addClass("checked");
                $($nextbtn).hide();
                $("#getmyscore").hide();
                $('#hideReport').show();
                if (maxWidth.matches) { $("#gis-pr-form-score-reset").css("display", "block"); }
                $(".gis-pr-eligibility-score").show();
                const conent = document.querySelector('#gis-pr-timeline');
                conent.scrollLeft += 500
                var elem = document.getElementById("hideReport");
                elem.scrollIntoView();
            }

            /// to make the upper timleines checked of CRS calculator when all the required data is filled  in CRS calculator if user already logged in
            if (checkValidation && JSON.parse(localStorage.getItem('cta-data'))['formType'] == "crs") {
                // var elem = document.getElementById("showScore");
                // elem.scrollIntoView();
                $(".gis-pr-eligibility-score").show();
                $('html, body').animate({
                    scrollTop: $(".express-cal").offset().top + 120
                }, 100);
            }
        }
    }
    if (($("#hideReport").is(":visible")) && (JSON.parse(localStorage.getItem('cta-data')) !== null) && ((JSON.parse(localStorage.getItem('cta-data'))['formsubmit'] !== 'no'))) {
        $("#calculator").submit();
    }

    var prFormType = document.getElementsByName('calculatorType');
    /************PR Calculator show divs as per selected values when page reload*************/
    if (prFormType[0].value == 'prCalculator') {
        if ($('input:radio[name="PrCalculator[maritalStatus]"]').is(':checked') && $('input:radio[name="PrCalculator[maritalStatus]"]:checked').val() == 1) {
            addRequire();
            $('.spouseData').show();
        } else {
            removeRequire();
            $('.spouseData').hide();
        }
    }
    /************PR Calculator show divs as per selected values when page reload End*************/

    /************CRS Calculator show divs as per selected values when page reload*************/
    if (prFormType[0].value == 'crsCalculator') {

        ///if user is about to login 
        if ($('.submitForm').attr('type') == 'submit' && (JSON.parse(localStorage.getItem('cta-data')) !== null) && (JSON.parse(localStorage.getItem('cta-data'))['formsubmit'] !== 'no')) {
            $("#calculator").submit();
        }

        /// set the timeline for crs calculator
        crsTimeline();
        if (maxWidth.matches) {
            $(".cta_button").val("Show Report").css({ width: "100%" });
        }
        //set the timeline for crs calculator end

        if (typeof $('#CrsMaritalStatus').val() != undefined && ($('#CrsMaritalStatus').val() == 'Married' || $('#CrsMaritalStatus').val() == 'Common Law')) {
            $('.spouseData').show();
            if ($('input:radio[name="CrsCalculator[citizenship]"]').is(':checked') && $('input:radio[name="CrsCalculator[citizenship]"]:checked').val() == 1) {
                $('.accompanyYou').hide();
            }
            else {
                $('.accompanyYou').show();
            }
        } else {
            $('.spouseData').hide();
        }
        if ($('input:radio[name="CrsCalculator[canadianDegree]"]').is(':checked') && $('input:radio[name="CrsCalculator[canadianDegree]"]:checked').val() == 1) {
            $('.canadianEducation').show();
        } else {
            $('.canadianEducation').hide();
        }
        if ($('input:radio[name="CrsCalculator[validJob]"]').is(':checked') && $('input:radio[name="CrsCalculator[validJob]"]:checked').val() == 1) {
            $('.noc').show();
        } else {
            $('.noc').hide();
        }
        if (typeof $('#CrsSpouseLatestResult').val() != undefined && ($('#CrsSpouseLatestResult').val() == 1)) {
            $('.spouseLanguageTest').show();
            var spousePrimaryExam = $('#crsSpousePrimaryExam').val();
            populateSpouseSecondaryExam(spousePrimaryExam)

        } else {
            $('.spouseLanguageTest').hide();
        }
        if ($('input:radio[name="CrsCalculator[latestResult]"]').is(':checked') && $('input:radio[name="CrsCalculator[latestResult]"]:checked').val() == 1) {
            $('.primaryTestResult').show();
            $('.notEligible').hide();
            $('.submitForm').attr('disabled', false);
            $('.skipForm').show();
            var primaryExam = $('#crsPrimaryExam').val();
            populateSecondaryExam(primaryExam);

            //to populate dropdowns
            if (sessionStorage.length > 1) {
                Object.keys(sessionStorage).forEach((key) => {
                    var changeCase = key.toLowerCase()
                    if (changeCase.includes("calculator") || changeCase.includes("converter")) {
                        let elements = document.getElementsByName(key);
                        elements[0].value = sessionStorage.getItem(key);
                    }
                });
            }

            if (typeof $('#crsSecondaryLanguage').val() != undefined && ($('#crsSecondaryLanguage').val() != 'NA')) {
                var secondaryLanguage = $('#crsSecondaryLanguage').val();
                showSecondaryExamScores(secondaryLanguage);

                //to populate secondarylanguage score
                if (sessionStorage.length > 1) {
                    Object.keys(sessionStorage).forEach((key) => {
                        var changeCase = key.toLowerCase()
                        if (changeCase.includes("calculator")) {
                            let elements = document.getElementsByName(key);
                            elements[0].value = sessionStorage.getItem(key);
                        }
                    });
                }
            }
            crsTimeline();
        }
        else if ($('input:radio[name="CrsCalculator[latestResult]"]').is(':checked') && $('input:radio[name="CrsCalculator[latestResult]"]:checked').val() == 0) {
            $('.primaryTestResult').hide();
            $('.skipForm').hide();
            $('.notEligible').show();
            $('.submitForm').attr('disabled', true);
            // var elem = document.getElementById("notEligible");
            // elem.scrollIntoView();
            $('html, body').animate({
                scrollTop: $("#notEligible").offset().top
            }, 100);
            crsTimeline();
        }
    }
    /************CRS Calculator show divs as per selected values when page reload End*************/
    localStorage.clear();
    sessionStorage.clear();


    // Show the first form and mark the first step as active
    $($steps[currentStep]).addClass("active");
    $($stepsNumber[currentStep]).addClass("complete");

    ///*** remove when login enable***/
    var checkScoreToShow = $("#isshowScore").val();
    if (checkScoreToShow == 1) {
        currentStep = $steps.length - 1
        updateStep(currentStep);
        $($stepsNumber[currentStep]).addClass("active").removeClass("complete");
        $($checkIcon[currentStep]).addClass("checked");
        $($nextbtn).hide();
        $("#getmyscore").hide();
        $('#hideReport').show();
        if (maxWidth.matches) { $("#gis-pr-form-score-reset").css("display", "block"); }
        $(".gis-pr-eligibility-score").show();
        const conent = document.querySelector('#gis-pr-timeline');
        conent.scrollLeft += 500
        var elem = document.getElementById("hideReport");
        elem.scrollIntoView();

    }

    // Function to update the form and timeline
    function updateStep(step) {
        $steps.removeClass("active");
        $($steps[step]).addClass("active");

        // Update styles for previous step as well
        if (step > 0) {
            for (i = 0; i < step; i++) {
                $($steps[i]).addClass("active");
                $($stepsNumber[i]).addClass("active");
                $($stepsNumber[step]).addClass("complete");
                $($checkIcon[i]).addClass("checked");
                $($lines[i]).addClass("active");
            }
        }

        $forms.removeClass("active");
        $($forms[step]).addClass("active");
        if (step > 0) {
            $("#prev-form-btn").css("display", "block");
        }
    }
    //Function for get my score data
    function updateScore(calculatorData) {
        $.ajax({
            type: "POST",
            url: "/calculator/all-calculate",
            data: { calculatorData: calculatorData },
            dataType: "text",
            success: function (responseData) {
                if (calculatorData['calculatorType'] == 'crsCalculator') {
                    var data = JSON.parse(responseData);
                    $($myscore).html("My Score&nbsp;:&nbsp;" + data['score']);
                }
                else if (calculatorData['calculatorType'] == 'australiaPrPointsCalculator') {
                    var data = JSON.parse(responseData);
                    $($myscore).html("My Score&nbsp;:&nbsp;" + data['totalScore']);
                    if ($(".gis-pr-form-score.last-step").hasClass('auspr')) {
                        $(".gis-pr-form-score.last-step.auspr").html("My Score&nbsp;&nbsp;" + data['score189'] + " - " + data['score491'] + " - " + data['score190'])
                    }
                }
                else if (calculatorData['calculatorType'] == 'saskatchewanCalculator') {
                    var data = JSON.parse(responseData);
                    $($myscore).html("My Score&nbsp;:&nbsp;" + data['score']);
                }
                else if (calculatorData['calculatorType'] == 'clbConverter') {
                    if (responseData.includes('"')) { responseData = responseData.replaceAll('"', ''); }
                    $('#showScore').html('My Score&nbsp;:&nbsp;' + responseData);
                }
                else if (calculatorData['calculatorType'] == 'prCalculator') {
                    var data = JSON.parse(responseData);
                    $($myscore).html("My Score&nbsp;:&nbsp;" + data['score']);
                }
                else if (calculatorData['calculatorType'] == 'germanyoppCalculator') {
                    var data = JSON.parse(responseData);
                    $($myscore).html("My Score&nbsp;:&nbsp;" + data['score']);
                }
            }
        });
    }
    /*************** Hide Previous Step and its timeline  *************/
    function removePreviousStepClasses(step) {
        $($stepsNumber[step]).removeClass("active");
        $($checkIcon[step]).removeClass("checked");
        $($stepsNumber[step]).removeClass("complete");
    }
    /*************Function to check input fields data and calculate score **********/
    function calculatorFieldValidation(divId, setscore = false) {
        var Return = true;
        var calculatorData = {};

        var divElem = document.getElementById(divId);
        var inputElements = divElem.querySelectorAll("input, select");
        for (const input of inputElements) {
            if (input.localName == 'select') {
                $(".field-" + input.id + " .select2-selection--single").css("border", "1px solid #d8d8d8");
                $("." + input.id + " .help-block").html("");
                if (input.value == '' && input.ariaRequired) {
                    $(".field-" + input.id + " .select2-selection--single").css("border", "1px solid #E5585D");
                    $("." + input.id + " .help-block").html("Please select any option");
                    Return = false;
                }
            }
            else if (input.attributes.type.value == 'radio') {
                var requiredClass = $(document.querySelector('input[name="' + input.attributes.name.value + '"]')).parent();
                var classname = $(document.querySelector('input[name="' + input.attributes.name.value + '"]')).parent().parent().prop('className');
                $("." + classname.replace(/[ ]+/g, ".") + " .help-block").html('');

                let checkBox = document.querySelector('input[name="' + input.attributes.name.value + '"]:checked');
                if ((checkBox == '' || checkBox == null) && $(requiredClass).hasClass("required")) {
                    $("." + classname.replace(/[ ]+/g, ".") + " .help-block").html('Please select any option');
                    Return = false;
                }

            }
        }
        //// session m store karna h and check the login
        if (Return || setscore) {
            // update the session storage in every step of the form
            var form = document.getElementById('calculator');
            var data = new FormData(form);
            for (var [key, value] of data) {
                sessionStorage.setItem(key, value);
            }
            //update the calculator data if user is logged in to calculate score
            // if (gmu.config.isLoggedIn) {
            for (var len = sessionStorage.length, i = 0; i < len; i++) {
                var key = sessionStorage.key(i);
                var changeCase = key.toLowerCase()
                if (changeCase.includes("calculator") || changeCase.includes("converter")) {
                    calculatorData[key] = sessionStorage.getItem(key);
                }
            }
            // }
        }

        if ((Return || setscore) && Object.keys(calculatorData).length > 0) {
            calculatorData.calculatorType = sessionStorage.getItem('calculatorType')
            updateScore(calculatorData); // update the score
        }
        return Return;

    }
    /*********** Handle previous button click Aus PR calculator************************/
    $("#prev-form-btn").click(function () {
        $($steps).show();
        $($lines).show();
        $('#hideReport').css('display', 'none');
        $('#getmyscore').css('display', 'none');
        if (currentStep > 0) {
            removePreviousStepClasses(currentStep); // Remove classes for the current step
            currentStep--;
            $($lines[currentStep]).removeClass("active");
            $($steps[currentStep]).removeClass("completed");
            removePreviousStepClasses(currentStep); // Remove classes for the new current step
            updateStep(currentStep);
        }
        if (currentStep === 0) {
            $("#prev-form-btn").css("display", "none");
        }
        $($nextbtn).show();
        if (currentStep < $steps.length - 1) {
            $(".gis-pr-eligibility-score").hide();
        }
        const conent = document.querySelector('#gis-pr-timeline');
        conent.scrollLeft -= 130
        $(window).scrollTop(100);
        $("#gis-pr-form-score-reset").hide();
        var prForm = document.getElementsByName('calculatorType');
        if ((prForm[0].value == 'australiaPrPointsCalculator')) {
            $(".gis-pr-form-score.last-step").removeClass("auspr");
        }
    });

    /*****************Reset Button ********** ???????**********/
    $('.gis-pr-form-score-reset').on("click", function () {
        const formToReset = document.getElementById('calculator');
        formToReset.reset();
        localStorage.clear();
        sessionStorage.clear();
    });
    /************ Score Unlock *******************/
    $(".gis-pr-form-unlock").on("click", function () {
        var form = document.getElementById('calculator');
        var data = new FormData(form);
        for (var [key, value] of data) {
            sessionStorage.setItem(key, value);
        }
        localStorage.setItem('cta-data', JSON.stringify({
            ctaStep: currentStep,
        }));
        if (sessionStorage.getItem('calculatorType') == 'crsCalculator') {
            localStorage.setItem('cta-data', JSON.stringify({
                ctaStep: currentStep,
                formType: "crs"
            }));
        }
    });
    /***********Hide Score**************/
    $(".hideReport").on("click", function () {
        $(".gis-pr-eligibility-score").hide();
        $(".hideReport").hide();
        $("#getmyscore").show();
        if (maxWidth.matches) {
            $("#getmyscore").val("Show Report").css({ width: "100%" });
            $("#gis-pr-form-score-reset").css("display", "block");
        }
    });


    /************** Handle next button click of Aus PR calculator ***********************/
    $("#next-form-btn").click(function () {
        var formId = $($forms[currentStep]).attr("id");

        /// in crsCalculator if user is not elligible  
        var prForm = document.getElementsByName('calculatorType');
        if ((prForm[0].value == 'crsCalculator') && $('input:radio[name="CrsCalculator[latestResult]"]').is(':checked') && $('input:radio[name="CrsCalculator[latestResult]"]:checked').val() == 0 && formId == 'form2') {
            $(this).hide();
            $($lines[currentStep + 1]).hide();
            $($stepsNumber[currentStep + 1]).addClass('active');
            $($checkIcon[currentStep + 1]).addClass('checked');
            for (i = currentStep + 2; i <= $steps.length; i++) {
                $($steps[i]).hide();
            }
        }
        /// in crsCalculator if user is not elligible  end

        var checkValidation = (calculatorFieldValidation(formId, currentStep)); // "formId" is the div id in which the form visible
        if (checkValidation) {
            if (currentStep < $steps.length - 1) {
                currentStep++;
                updateStep(currentStep);
            }
            if (currentStep == $steps.length - 1) {
                $(this).hide();
                $("#getmyscore").show();
                if (maxWidth.matches) {
                    $("#getmyscore").val("Show Report").css({ width: "100%" });
                    $("#gis-pr-form-score-reset").css("display", "block");
                }
            }
            const conent = document.querySelector('#gis-pr-timeline');
            conent.scrollLeft += 130
            $(window).scrollTop(100);
        }
    });
    $(".submitForm").click(function () {
        var formId = $($forms[currentStep]).attr("id");
        var checkValidation = (calculatorFieldValidation(formId, currentStep));
        if (checkValidation) {
            $('#calculator').attr('onsubmit', 'return true;');
            if (!gmu.config.isLoggedIn) {
                localStorage.setItem('cta-data', JSON.stringify({
                    ctaStep: currentStep,
                }));
                if (sessionStorage.getItem('calculatorType') == 'crsCalculator') {
                    localStorage.setItem('cta-data', JSON.stringify({
                        ctaStep: currentStep,
                        formType: "crs"
                    }));
                }
                $("#getmyscore").addClass("getLeadForm");
            }
        }
        else {
            $('#calculator').attr('onsubmit', 'return false;');
        }
    });
    /*********************  Logic for Select 2 options **************/
    $(".gis-pr-form-select-2").select2({
        placeholder: "Select",
        dropdownAutoWidth: true,
        dropdownCssClass: "gis-pr-form-select-2",
    });

});

/************ Blog unlock function **************/
$(".gis-pr-continue-btn").on("click", function () {
    $(".gis-pr-blog-unlock").css("height", "auto");
    $(".gis-pr-continue-btn").css("display", "none");
});

/********************* PR Calculator start ******************/
$(document).on('change', 'input:radio[name="PrCalculator[maritalStatus]"]', function () {
    if (this.checked && this.value == 1) {
        addRequire();
        $('.spouseData').show();
    } else {
        removeRequire();
        $('.spouseData').hide();
    }
});
function addRequire() {
    var spouseData = $('.spouseData');
    $('.spouseData #prSpouseLanguageProficiency').attr("aria-required", "true");
    $('.spouseData #prSpouseWorkedInCanada').attr("aria-required", "true");
    $('.spouseData #prSpouseStudiedInCanada').attr("aria-required", "true");
    for (i = 0; i < spouseData.length; i++) {
        $('.spouseData .form-group').addClass('required');
        $('.spouseData .help-block').html("");
    }
}
function removeRequire() {
    $("input[type=radio][name='PrCalculator[spouseLanguageProficiency]']").prop('checked', false);
    $("input[type=hidden][name='PrCalculator[spouseLanguageProficiency]']").val('');

    $("input[type=radio][name='PrCalculator[spouseWorkedInCanada]']").prop('checked', false);
    $("input[type=hidden][name='PrCalculator[spouseWorkedInCanada]']").val('');

    $("input[type=radio][name='PrCalculator[spouseStudiedInCanada]']").prop('checked', false);
    $("input[type=hidden][name='PrCalculator[spouseStudiedInCanada]']").val('');

    sessionStorage.setItem('PrCalculator[spouseLanguageProficiency]', '');
    sessionStorage.setItem('PrCalculator[spouseWorkedInCanada]', '');
    sessionStorage.setItem('PrCalculator[spouseStudiedInCanada]', '');


    var spouseData = $('.spouseData');
    for (i = 1; i <= spouseData.length; i++) {
        $('.spouseData .form-group').removeClass('has-error required');
        $('.spouseData .help-block').html('');
    }

}
/********************* PR Calculator end ******************/


/************CRS Calculator input show and hide*************/

function crsTimeline() {
    var numItems = $('.gis-pr-step');
    var timeline = $('.gis-pr-timeline-line');
    var lastTimeline = $('.gis-pr-step-number');
    var lastTimelineCheck = $('.gis-pr-step-check');

    if (typeof ($('#CrsMaritalStatus').val()) != undefined && $('#CrsMaritalStatus').val() != '') {
        var spouseData = $(".spouseData").is(":visible");
        if (!spouseData) {
            $(lastTimeline['0']).addClass('active');
            $(lastTimelineCheck['0']).addClass('checked');
            $(timeline['0']).addClass('active');
        }
        else {
            if ($('input:radio[name="CrsCalculator[citizenship]"]:checked').val() == 1) {
                $(lastTimeline['0']).addClass('active');
                $(lastTimelineCheck['0']).addClass('checked');
                $(timeline['0']).addClass('active');
            }
            else if ($('input:radio[name="CrsCalculator[citizenship]"]:checked').val() == 0 && $('input:radio[name="CrsCalculator[accompaniedBySpouse]"]').is(':checked')) {
                $(lastTimeline['0']).addClass('active');
                $(lastTimelineCheck['0']).addClass('checked');
                $(timeline['0']).addClass('active');
            }
            else {
                $(lastTimeline['0']).removeClass('active');
                $(lastTimelineCheck['0']).removeClass('checked');
                $(timeline['0']).removeClass('active');
            }
        }

    }
    if ((typeof $('#CrsAge').val() != undefined && ($('#CrsAge').val() != '')) && (typeof $('#CrsEducation').val() != undefined && ($('#CrsEducation').val() != '')) && ($('input:radio[name="CrsCalculator[canadianDegree]"]:checked').val())) {
        var canadianEducation = $(".canadianEducation").is(":visible");
        if (!canadianEducation) {
            $(lastTimeline['1']).addClass('active');
            $(lastTimelineCheck['1']).addClass('checked');
            $(timeline['1']).addClass('active');
        }
        else if ($("#CrsCanadaEducation").val() != '') {
            $(lastTimeline['1']).addClass('active');
            $(lastTimelineCheck['1']).addClass('checked');
            $(timeline['1']).addClass('active');
        }
        else {
            $(lastTimeline['1']).removeClass('active');
            $(lastTimelineCheck['1']).removeClass('checked');
            $(timeline['1']).removeClass('active');
        }
    }
    if ($('input:radio[name="CrsCalculator[latestResult]"]:checked').val() == 1) {
        for (hideStep = 3; hideStep < numItems.length; hideStep++) {
            $(numItems[hideStep]).show();
        }
        $(timeline['2']).removeClass('active');
        $(timeline['2']).show();
        $(lastTimeline['2']).removeClass('active');
        $(lastTimelineCheck['2']).removeClass('checked');

        if ((typeof ($('#crsPrimaryReadingScore').val()) != undefined && $('#crsPrimaryReadingScore').val() != '') && (typeof ($('#crsPrimaryWritingScore').val()) != undefined && $('#crsPrimaryWritingScore').val() != '') && (typeof ($('#crsPrimaryListeningScore').val()) != undefined && $('#crsPrimaryListeningScore').val() != '') && (typeof ($('#crsPrimarySpeakingScore').val()) != undefined && $('#crsPrimarySpeakingScore').val() != '')) {
            if ($('#crsSecondaryLanguage').val() != 'NA') {
                if ((typeof ($('#crsSecondaryReadingScore').val()) != undefined && $('#crsSecondaryReadingScore').val() != '') && (typeof ($('#crsSecondaryWritingScore').val()) != undefined && $('#crsSecondaryWritingScore').val() != '') && (typeof ($('#crsSecondaryListeningScore').val()) != undefined && $('#crsSecondaryListeningScore').val() != '') && (typeof ($('#crsSecondarySpeakingScore').val()) != undefined && $('#crsSecondarySpeakingScore').val() != '')) {
                    $(lastTimeline['2']).addClass('active');
                    $(lastTimelineCheck['2']).addClass('checked');
                    $(timeline['2']).addClass('active');
                }
            }
            else {
                $(lastTimeline['2']).addClass('active');
                $(lastTimelineCheck['2']).addClass('checked');
                $(timeline['2']).addClass('active');
            }
        }
    }
    if ($('input:radio[name="CrsCalculator[latestResult]"]:checked').val() == 0) {
        $(lastTimeline['2']).addClass('active');
        $(lastTimelineCheck['2']).addClass('checked');
        $(timeline['2']).addClass('active');
        for (hideStep = 3; hideStep < numItems.length; hideStep++) {
            $(numItems[hideStep]).hide();
        }
        $(timeline['2']).hide();
    }

    if ((typeof $('#CrsSkilled').val() != undefined && ($('#CrsSkilled').val() != '')) && (typeof $('#CrsForeignSkilled').val() != undefined && ($('#CrsForeignSkilled').val() != '')) && ($('input:radio[name="CrsCalculator[certificate]"]:checked').val())) {
        $(lastTimeline['3']).addClass('active');
        $(lastTimelineCheck['3']).addClass('checked');
        $(timeline['3']).addClass('active');
    }
    if (($('input:radio[name="CrsCalculator[validJob]"]:checked').val()) && ($('input:radio[name="CrsCalculator[nomination]"]:checked').val()) && ($('input:radio[name="CrsCalculator[relativeInCanada]"]:checked').val())) {
        var noc = $(".noc").is(":visible");
        if (!noc) {
            $(lastTimeline['4']).addClass('active');
            $(lastTimelineCheck['4']).addClass('checked');
        }
        else if ($("#CrsNoc").val() != "") {
            $(lastTimeline['4']).addClass('active');
            $(lastTimelineCheck['4']).addClass('checked');
        }
        else {
            $(lastTimeline['4']).removeClass('active');
            $(lastTimelineCheck['4']).removeClass('checked');
        }
    }
}

// martital status div
$(document).on('change', '#CrsMaritalStatus', function () {
    var married = $(this).val();
    if (typeof married != undefined && (married == 'Married' || married == 'Common Law')) {
        $('.spouseData').show();
    } else {
        $('.spouseData').hide();
    }
    crsTimeline();
});

/// Age and education div
$(document).on('change', '#CrsAge', function () {
    crsTimeline();
});
$(document).on('change', '#CrsEducation', function () {
    crsTimeline();
});
$(document).on('change', 'input:radio[name="CrsCalculator[canadianDegree]"]', function () {
    if (this.checked && this.value == 1) {
        $('.canadianEducation').show();
    } else {
        $('.canadianEducation').hide();
    }
    crsTimeline();
});
$(document).on('change', '#CrsCanadaEducation', function () {
    crsTimeline();
});
//official Language
$(document).on('change', 'input:radio[name="CrsCalculator[latestResult]"]', function () {
    if (this.checked && this.value == 1) {
        $('.primaryTestResult').show();
        $('.notEligible').hide();
        $('.submitForm').attr('disabled', false);
        $('.skipForm').show();
        $("#seperateDiv1").addClass("seperateDiv");
        $('.next-form-btn').show();
        crsTimeline();

    } else {
        $('.primaryTestResult').hide();
        $('.skipForm').hide();
        $('.notEligible').show();
        $('.submitForm').attr('disabled', true);
        $("#seperateDiv1").removeClass("seperateDiv");
        $('.next-form-btn').hide();
        // var elem = document.getElementById("notEligible");
        // elem.scrollIntoView();
        $('html, body').animate({
            scrollTop: $("#notEligible").offset().top
        }, 100);
        crsTimeline();
        $("#showScore").hide();
    }
});
$(document).on('change', '#crsPrimaryExam', function () {
    var primaryExam = $(this).val();
    populateSecondaryExam(primaryExam);
});
$(document).on('change', '#crsPrimaryReadingScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsPrimaryWritingScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsPrimaryListeningScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsPrimarySpeakingScore', function () {
    crsTimeline();
});

$(document).on('change', '#crsSecondaryLanguage', function () {
    var secondaryLanguage = $(this).val();
    showSecondaryExamScores(secondaryLanguage);
    crsTimeline();
});
$(document).on('change', '#crsSecondaryReadingScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsSecondaryWritingScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsSecondaryListeningScore', function () {
    crsTimeline();
});
$(document).on('change', '#crsSecondarySpeakingScore', function () {
    crsTimeline();
});
//work Experience
$(document).on('change', '#CrsSkilled', function () {
    crsTimeline();
});
$(document).on('change', '#CrsForeignSkilled', function () {
    crsTimeline();
});
$(document).on('change', 'input:radio[name="CrsCalculator[certificate]"]', function () {
    crsTimeline();
});
//Additional Info
$(document).on('change', 'input:radio[name="CrsCalculator[validJob]"]', function () {
    if (this.checked && this.value == 1) {
        $('.noc').show();
    } else {
        $('.noc').hide();
    }
    crsTimeline();
});
$(document).on('change', 'input:radio[name="CrsCalculator[nomination]"]', function () {
    crsTimeline();
});
$(document).on('change', 'input:radio[name="CrsCalculator[relativeInCanada]"]', function () {
    crsTimeline();
});

$(document).on('change', 'input:radio[name="CrsCalculator[citizenship]"]', function () {
    crsTimeline();
    if (this.checked && this.value == 1) {
        $('.accompanyYou').hide();
    } else {
        $('.accompanyYou').show();
    }
});
$(document).on('change', 'input:radio[name="CrsCalculator[accompaniedBySpouse]"]', function () {
    crsTimeline();

});
$(document).on('change', '#CrsSpouseLatestResult', function () {
    crsTimeline();
    if ($(this).val() == 1) {
        $('.spouseLanguageTest').show();
    } else {
        $('.spouseLanguageTest').hide();
    }
});
$(document).on('change', '#crsSpousePrimaryExam', function () {
    var spousePrimaryExam = $(this).val();
    populateSpouseSecondaryExam(spousePrimaryExam)
});
$(document).on('change', '#CrsNoc', function () {
    crsTimeline();

});

function populateSecondaryExam(primaryExam) {
    if (typeof primaryExam != undefined) {
        let primaryExamSection = examSection[primaryExam];
        $.each(primaryExamSection, function (section, value) {
            let dropdownValues = '<option value="">Select</option>';
            $.each(value, function (key, bandScore) {
                dropdownValues += '<option value="' + key + '">' + bandScore + '</option>';
            });
            $('#crsPrimary' + section + 'Score').html(dropdownValues);
        });
        let updatedExamTypes = {};
        if (primaryExam == 'IELTS' || primaryExam == 'CELPIP') {
            updatedExamTypes['TEF'] = 'TEF Canada';
            updatedExamTypes['TCF'] = 'TCF Canada';
        } else {
            updatedExamTypes['IELTS'] = 'IELTS';
            updatedExamTypes['CELPIP'] = 'CELPIP - G';
        }
        updatedExamTypes['NA'] = 'Not Applicable';

        let secondaryExamDropdownValues = '<option value="">Select</option>';
        $.each(updatedExamTypes, function (examKey, examName) {
            secondaryExamDropdownValues += '<option value="' + examKey + '">' + examName + '</option>';
        });
        $('#crsSecondaryLanguage').html(secondaryExamDropdownValues);
    }
}
function showSecondaryExamScores(secondaryLanguage) {
    if (typeof secondaryLanguage != undefined && secondaryLanguage != 'NA' && secondaryLanguage != '') {
        let secondaryLanguageSection = examSection[secondaryLanguage];
        $.each(secondaryLanguageSection, function (section, value) {
            let dropdownValues = '<option value="">Select</option>';
            $.each(value, function (key, bandScore) {
                dropdownValues += '<option value="' + key + '">' + bandScore + '</option>';
            });
            $('#crsSecondary' + section + 'Score').html(dropdownValues);
        });
        $('.secondaryScore').show();
        $("#seperateDiv").addClass("seperateDiv");
    } else {
        $('.secondaryScore').hide();
        $("#seperateDiv").removeClass("seperateDiv");
    }
}
function populateSpouseSecondaryExam(spousePrimaryExam) {
    if (typeof spousePrimaryExam != undefined && spousePrimaryExam != 'NA') {
        let spousePrimaryExamSection = examSection[spousePrimaryExam];
        $.each(spousePrimaryExamSection, function (section, value) {
            let dropdownValues = '<option value="">Select</option>';
            $.each(value, function (key, bandScore) {
                dropdownValues += '<option value="' + key + '">' + bandScore + '</option>';
            });
            $('#crsSpousePrimary' + section + 'Score').html(dropdownValues);
        });
    }
}
document.querySelectorAll('select.form-control, .selectCourse').forEach((select) => {
    select.addEventListener('change', (e) => {
        e.target.style.border = '1px solid #d8d8d8'
    })
})