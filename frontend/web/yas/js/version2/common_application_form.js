$(document).ready(function () {

    const nameInputLead = document.querySelector('#name');
    const emailInputLead = document.querySelector('#email');
    const mobileInputLead = document.querySelector('#mobile');
    const currentCity = document.querySelector('#current_location');
    const course = document.querySelector('#course');
    const degree = document.querySelector('#qualification');
    const submitButtonLead = document.querySelector('#want-to-get-in');

    $('.city select').select2({
        placeholder: "Select/Enter Your Current City",
        name: 'current_location',
        ajax: {
            url: "/ajax/lead-cities",
            dataType: "json",
            type: "GET",
            data: function (params) {
                var queryParameters = {
                    term: params.term
                }
                return queryParameters;
            },
            processResults: function (data) {
                var states = data.states || [];
                var results = states.map(function (state) {
                    return {
                        text: state.state_name,
                        children: state.cities.map(function (city) {
                            return {
                                id: city.id,
                                text: city.text
                            };
                        })
                    };
                });

                return { results: results };
            },
        },
    });

    $('.course select').select2({
        placeholder: 'Select Course Category interested in',
        name: 'course',
        minimumInputLength: 1,
        ajax: {
            url: "/ajax/common-application-form-courses",
            dataType: "json",
            type: "GET",
            data: function (params) {
                var queryParameters = {
                    term: params.term
                }
                return queryParameters;
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (item) {
                        return {
                            text: item.text,
                            id: item.id,
                        };
                    }),
                };
            },
        },
    });


    $('.degree select').select2({
        placeholder: "Select Highest Qualification",
        name: 'qualification',
        ajax: {
            url: "/ajax/common-application-form-degree",
            dataType: "json",
            type: "GET",
            data: function (params) {
                var queryParameters = {
                    term: params.term
                }
                return queryParameters;
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (item) {
                        return {
                            text: item.text,
                            id: item.id,
                        };
                    }),
                };
            },
        },
    });

    //name validation
    $("#name").on("input", function (e) {
        var inputValue = $(this).val();
        var regex = /^[A-Za-z ]+$/;

        if (!regex.test(inputValue)) {
            var nameValue = inputValue.replace(/[^A-Za-z ]/g, '');
            $(this).val(nameValue);
        }
    });

    //mobile validation
    if (document.querySelector("#mobile") !== null) {
        document.querySelector("#mobile").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '')
        });
    }

    //email validation
    if (document.querySelector("#email") !== null) {
        document.querySelector("#email").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
            if (e.target.value.match(/@/g)) {
                if ((e.target.value.match(/\./g) || []).length > 2) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            } else {
                if ((e.target.value.match(/\./g) || []).length > 1) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            }

            var email = $('#email').val();
            let strLst = email.slice(email.indexOf("@") + 1, email.length)

            if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
                $(".errorMsgEmail").html("Email is not a valid email address.");
            } else {
                $(".errorMsgEmail").html("");
            }
        })
    }

    //submit button
    function submitButtonFunctionality() {
        if (nameInputLead.value.length !== 0
            && (emailInputLead.value.length !== 0
                && $(".errorMsgEmail").html() == '')
            && (mobileInputLead.value.length == 10)
            && currentCity.value.length !== 0
            && course.value.length !== 0
            && degree.value.length !== 0
        ) {
            submitButtonLead.removeAttribute('disabled');
        } else {
            submitButtonLead.setAttribute('disabled', true);
        }
    }

    $("#name, #email, #mobile, #current_location, #course, #qualification").bind("change keyup", function (event) {
        submitButtonFunctionality()
    });

    //submit lead
    if (submitButtonLead !== null) {
        submitButtonLead.addEventListener('click', (e) => {
            e.preventDefault();
            var form = $('#common_application_lead_form');
            $.ajax({
                url: '/common-application-form/submit',
                data: form.serialize(),
                dataType: 'json',
                method: 'POST',
                beforeSend: function () {
                    $('.primaryBtn').prop('disabled', true);
                },
                error: function (xhr, err) {
                    $('.primaryBtn').prop('disabled', false)
                    displayErrorsLead('Something went wrong, please try again!')
                },
                complete: function () {
                    $('.primaryBtn').prop('disabled', false);
                },
                success: function (data) {
                    if (data.success == true) {
                        localStorage.setItem('common_application_id', data.id);
                        sponsoredCollege(form);
                    } else {
                        displayErrorsLead(data.message)
                    }
                }
            });
        });
    }

    function sponsoredCollege(form) {
        $.ajax({
            url: '/common-application-form/sponsor',
            data: form.serialize(),
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                $('.primaryBtn').prop('disabled', false)
                displayErrorsLead('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (data) {
                if (data.success == true) {
                    //scroll to the div clg_list_suggested
                    $('html, body').animate({
                        scrollTop: $("#clg_list_suggested").offset().top
                    }, 1000);

                    $("#clg_list_suggested").show();
                    $("#clg_list_suggested").html(data.html);
                    $("#clg_no_list_suggested").hide();
                    $("input[name='redirectionUrl']").val(data.redirectionUrl);
                } else {
                    $("#clg_no_list_suggested").show();
                    $("#clg_no_list_suggested").html(data.message);
                    $("#clg_list_suggested").hide();
                }
            }
        });
    }

    //save checked college ids in lead table
    $(document).on('click', '#request_submit_application', function (e) {
        e.preventDefault();
        var checkedColleges = getCheckedColleges();
        $.ajax({
            url: '/common-application-form/submit',
            data: { checkedColleges: checkedColleges, id: localStorage.getItem('common_application_id') },
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                $('.primaryBtn').prop('disabled', false)
                displayErrorsLead('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (data) {
                if (data.success == true) {
                    localStorage.removeItem('common_application_id');
                    if ($("input[name='redirectionUrl']").val() !== '') {
                        window.location.href = $("input[name='redirectionUrl']").val();
                    }
                } else {
                    displayErrorsLead(data.message)
                }
            }
        });
    });

    //enable request submit apllication if checkbox is checked
    $(document).on('change', '.select_college', function () {
        if ($(".select_college:checked").length > 0) {
            $("#request_submit_application").prop("disabled", false);
        } else {
            $("#request_submit_application").prop("disabled", true);
        }
    });

    //get checked colleges
    function getCheckedColleges() {
        var checkedColleges = [];
        $.each($("input[name='clg_id[]']:checked"), function () {
            checkedColleges.push($(this).val());
        });
        return checkedColleges;
    }

    function displayErrorsLead(errors = undefined) {
        $('.validationError').remove();
        $('.errorMsg').html('');
        $('select, input').removeClass('errorInputField');
        if (typeof errors === 'object') {
            for (const [key, value] of Object.entries(errors)) {
                $('select[name="' + key + '"], input[name="' + key + '"]').parent().append('<p class="error validationError">' + value + '</p>');
                $('select[name="' + key + '"], input[name="' + key + '"]').addClass('errorInputField');
            }
        }

        if (typeof errors === 'string') {
            $('.errorMsg').html(errors);
        }
    }

});