var mobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

if (window.location.href.indexOf("/news") > -1 && mobileDevice && gmu.config.page_category == 'news-cta') {
    //news leadform using js
    $.get('/news-lead-form', {}, function (leadFormContainerNews) {
        $("#lead-form-js-new").html(leadFormContainerNews);
        inputErrorClearonFocusLeadForm()

        $('.streamCategory select').select2({
            placeholder: "Stream Interested",
            name: 'stream',
            ajax: {
                url: "/ajax/lead-stream",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                        entity: gmu.config.entity,
                        entity_id: gmu.config.entity_id,
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        $('.selectCity select').select2({
            placeholder: "City you live in",
            name: 'current_city',
            ajax: {
                url: "/ajax/lead-cities",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    var states = data.states || [];
                    var results = states.map(function (state) {
                        return {
                            text: state.state_name,
                            children: state.cities.map(function (city) {
                                return {
                                    id: city.id,
                                    text: city.text
                                };
                            })
                        };
                    });

                    return { results: results };
                },
            },
        });

        // Function to enable/disable the select dropdown
        function toggleLevelSelect(enabled) {
            var $levelSelect = $('#selectLevelNews'); // select dropdown
            if (enabled) {
                $levelSelect.prop('disabled', false);
            } else {
                $levelSelect.prop('disabled', true);
            }
        }

        $('.streamCategory select').on('change', function (e) {
            toggleLevelSelect(true);
        });

        $('.levelCategory select').select2({
            placeholder: "Level Interested",
            name: 'inputLevel',
            ajax: {
                url: "/ajax/lead-level",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                        entity: gmu.config.entity,
                        stream_id: $("#selectStreamNews").val(), // selected stream ID
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        //article page lead auto capture
        $("body").on('click', '.newsLeadValue', function () {
            var articleId = $(this).attr("data-entityid");
            var entity = gmu.config.entity;

            if (articleId == '') {
                return '';
            }
            $.ajax({
                type: 'POST',
                url: '/ajax/article-stream-level',
                data: { id: articleId, entity: entity },
                dataType: "json",
                success: function (response) {
                    if (response.success === true && response.stream_id !== "" || response.level !== '') {
                        toggleStreamFieldArticleNews(response.stream_id, response.level);
                    }

                }
            });
        });

        function toggleStreamFieldArticleNews(stream_id = '', level = '') {
            let highest_qualification_degree_id = ['9', '10', '11'];
            if (stream_id == '' || stream_id == '22' || $.inArray(level, highest_qualification_degree_id) !== -1) {
                $(".inputStreamContainer").show();
            } else {
                $(".streamCategory").hide();
                $('.streamCategory select').append($('<option>', {
                    value: stream_id,
                    selected: true
                }));
            }
            toggleLevel(level);
            EnableSubmitButtonForloggedInUsers();
        }

        function toggleLevel(level) {
            if (level !== '' && level !== '12') {
                $(".levelCategory").hide();
                $('.levelCategory select').append($('<option>', {
                    value: level,
                    selected: true,
                })).prop('disabled', false);
            } else {
                $(".levelCategory").show();
                $("#selectLevelNews").prop("disabled", false);
            }
        }

        function loadNewsLeadData(lead) {
            if (lead == 1) {
                $('.headingText').html("");
                document.querySelector('input[name="utm_source"]').value = gmu.config.utm_source ?? '';
                document.querySelector('input[name="utm_medium"]').value = gmu.config.utm_medium ?? '';
                document.querySelector('input[name="utm_campaign"]').value = gmu.config.utm_campaign ?? '';
            }
            var url = document.querySelectorAll('meta[property="og:url"]')[0].content;
            var ctaLoaction = lead !== 1 ? lead.dataset.ctalocation : 'auto-pop-up';
            var ctaText = lead !== 1 ? lead.dataset.ctatext : 'Auto Pop Up';
            var entity = lead !== 1 ? gmu.config.entity : 'news';
            var entity_id = lead !== 1 ? gmu.config.entity_id : 0;
            var url = lead !== 1 ? window.location.href : url;
            var sub_type = lead !== 1 ? gmu.config.entity_subtype : 'detail-page';

            $(".pageMask").css("display", "block");
            $("body").css("overflowY", "hidden");
            $(".leadFormContainerNews, #signup-form-news").css("display", "block");
            $('input[name="entity"]').val(entity);
            $('input[name="entity_id"]').val(entity_id);
            $('input[name="url"]').val(url);
            $('input[name="entity_sub_type"]').val(sub_type);
            $('input[name="cta_location"]').val(ctaLoaction);
            $('input[name="cta_text"]').val(ctaText);
            $('input[name="is_lead"]').val("yes");

            if (lead !== undefined && lead !== 1 && lead.dataset.subheadingtext) {
                $('.subHeadingText').html(lead.dataset.subheadingtext);
            } else {
                $('.subHeadingText').html('Get details and latest updates');
            }

            if (lead !== undefined && lead !== 1 && lead.dataset.leadformtitle) {
                $('.headingText').append(lead.dataset.leadformtitle);
            } else {
                $('.headingText').append('REGISTER NOW TO APPLY');
            }
        }

        //auto pop up
        if (getCookie('gmu_leadform') != '1') {
            // if (window.location.href.indexOf("immigration") > -1) {
            //     alert("your url contains the name franky");
            // }
            var leadTimeOut = setTimeout(() => {
                isModalOpenNews = true;
                document.addEventListener('touchmove', preventDefaultScrollNews, { passive: false });
                $(".leadFormContainerNews").css("display", "block");
                $(".pageMask").css("display", "block");
                $("body").css("overflowY", "hidden");
                $(".headerCTAPair").css("z-index", 0);
                document.querySelector('#lead-form-js-new').style.display = "block";
                loadNewsLeadData(1);
            }, 30000);
        }

        $("body").on('click', '.loginNews', function () {
            $(".leadFormContainerNews").css("display", "none");
            $(".subscribeSectionNews").css("display", "block");
        });

        $("body").on('click', '.logInOptionNews', function () {
            $(".leadFormContainerNews, #signup-form-news").css("display", "block");
            $(".subscribeSectionNews").css("display", "none");
        });

        $("body").on('click', '.changeNumber', function () {
            $(".leadFormContainerNews, #login-form-news").css("display", "none");
            $(".subscribeSectionNews, #otp-form-news").css("display", "block");
        });

        //open news lead form
        $('body').on('click', '.js-open-lead-form-news', function (e) {
            isModalOpenNews = true;
            document.addEventListener('touchmove', preventDefaultScrollNews, { passive: false });
            $(".headerCTAPair").css("z-index", 0);
            $('#signup-form-news').trigger("reset");
            clearTimeout(leadTimeOut);
            $(".headingText").html("");
            $(".leadFormContainerNews .leadFormDiv .userInputs .row .col-md-6 select").css("color", "#989898");
            document.querySelector('#lead-form-js-new').style.display = "block";
            loadNewsLeadData(this);
        });

        //name validation
        $(".txtOnlyNews").keypress(function (e) {
            var key = e.keyCode;
            var regex = /^[A-Za-z ]+$/;
            var isValid = regex.test(String.fromCharCode(key));
            if (!isValid) {
                e.preventDefault();
            }
        });

        $('.otpInputsNews').find('input').each(function () {
            $(this).attr('maxlength', 1);
            $(this).on('keyup', function (e) {
                var id = $(this).attr('id')
                var parent = $($(this).parent());

                if (e.keyCode === 8 || e.keyCode === 37) {
                    var prev = parent.find('input#' + $(this).data('previous'));
                    $("#" + id).attr('data-count', 1);

                    if (prev.length) {
                        $(prev).select();
                    }
                } else if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 96 && e.keyCode <= 105) || e.keyCode === 39) {
                    var next = parent.find('input#' + $(this).data('next'));

                    if (next.length) {
                        $(next).select();
                    } else {
                        if (parent.data('autosubmit')) {
                            parent.submit();
                        }
                    }
                } else {
                    $(this).val('');
                }
            });
        });

        $('.digit').on('keypress', function (e) {
            var id = $(this).attr('id')
            var dataCount = $("#" + id).attr('data-count');
            var count = parseInt(dataCount) + 1;
            $("#" + id).attr('data-count', count);
            if (count > 2) {
                e.preventDefault();
            }
        });

        //resend otp
        var resend = true;
        $("body").on('click', '#resendOtp', function (e) {
            e.preventDefault();
            if (!resend) {
                return false;
            }
            $.post('/ajax/resend-otp', $("#lead-form").serialize(), function (response) {
                if (response.success) {
                    resend = false;
                    setResendInterval();
                }
            })
        });

        var $htmlOrBody = $('html, body'), // scrollTop works on <body> for some browsers, <html> for others
            scrollTopPadding = 8;

        $('.otp-phone-news').focus(function () {
            // get textarea's offset top position
            var textareaTop = $(this).offset().top;
            // scroll to the textarea
            $htmlOrBody.scrollTop(textareaTop - scrollTopPadding);
        });

        //get field info
        const nameInputLead = document.querySelector('#formNameNews');
        const emailInputLead = document.querySelector('#formEmailNews');
        const mobileInputLead = document.querySelector('#signup-phone-news');
        const streamSelectLead = document.querySelector('#selectStreamNews');
        const levelSelectLead = document.querySelector('#selectLevelNews');
        const citySelectLead = document.querySelector('#selectCityNews');
        const cityInputIp = document.querySelector('#selectCityNewsIp');
        const submitButtonLead = document.querySelector('.newsSubmit');


        function EnableSubmitButtonForloggedInUsers() {
            if (gmu.config.isLoggedIn == true) {
                if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainerNewsMobile .errorMsg").html() == '') !== 0 && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0) {
                    submitButtonLead.removeAttribute('disabled');
                } else {
                    submitButtonLead.setAttribute('disabled', true);
                }
            }
        }

        //validtion for mobile
        $(".otp-phone-news, .signup-phone-news").keypress(function (e) {
            var mobNum = $(this).val();
            var key = e.keyCode;
            var regex = /^[0-9]+$/;
            var isValid = regex.test(String.fromCharCode(key));
            if (!isValid || (
                jQuery.inArray(String.fromCharCode(key), ['9', '8', '7', '6']) == -1 &&
                mobNum.length == 0)) {
                e.preventDefault();
            } else {
                if (mobNum.length >= 10) {
                    e.preventDefault();
                }
            }
        });

        //validtion for mobile
        if (document.querySelector(".signup-phone-news") !== null) {
            document.querySelector(".signup-phone-news").addEventListener('input', (e) => {
                e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '');
                if (e.target.value.length < 10) {
                    $(".errorMsgMobile").html("phone number should contain 10 digits");
                } else {
                    $(".errorMsgMobile").html("");
                }
            });
        }

        //email validation
        document.querySelector(".emailTxtOnlyNews").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
            if (e.target.value.match(/@/g)) {
                if ((e.target.value.match(/\./g) || []).length > 2) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            } else {
                if ((e.target.value.match(/\./g) || []).length > 1) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            }
            var email = $('.emailTxtOnlyNews').val();
            let strLst = email.slice(email.indexOf("@") + 1, email.length)

            // String contains "@" after the first character and "@" is not the last character
            if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
                $(".errorMsgEmailNews").html("Email is not a valid email address.");

            } else {
                $('.errorMsgEmailNews').html('');
            }
        })

        //Function to check if all fields are filled
        $(".streamCategory, .levelCategory, .newsMobileName, .newsMobilePhone, .newsMobileCity, .inputEmailContainerNewsMobile").bind("change keyup", function (event) {
            if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainerNewsMobile .errorMsgEmailNews").html() == '') && mobileInputLead.value.length == 10 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0) {
                submitButtonLead.removeAttribute('disabled');
            } else {
                submitButtonLead.setAttribute('disabled', true);
            }
        });

        $("#otp-form-news").submit(function (e) {
            e.preventDefault()
            displayErrorsLead()
            var csrf = $("input[name='_csrf-frontend']").val();
            var phone = $("#otp-phone-news").val();
            $.ajax({
                url: '/site/send-otp',
                data: { phone: phone, '_csrf-frontend': csrf },
                dataType: 'json',
                method: 'POST',
                beforeSend: function () {
                    $('.primaryBtn').prop('disabled', true);
                },
                error: function (xhr, err) {
                    $('.primaryBtn').prop('disabled', false)
                    displayErrors('Something went wrong, please try again!')
                },
                complete: function () {
                    $('.primaryBtn').prop('disabled', false);
                },
                success: function (data) {
                    if (data.success == true) {
                        $('.subscribeSectionNews, #login-form-news').css("display", "block");
                        $('#mobileNum').html($('#otp-phone-news').val())
                        $('#otp-form-news').hide()
                        $('#login-form-news').show()
                        otpTimer(data.data.expiresIn);
                    }
                    displayErrors(data.message)

                }
            });
        });

        $("#signup-form-news").submit(function (e) {
            e.preventDefault()
            displayErrorsLead()
            var form = $(this)
            $.ajax({
                url: '/site/signup',
                data: form.serialize(),
                dataType: 'json',
                method: 'POST',
                beforeSend: function () {
                    $('.primaryBtn').prop('disabled', true);
                },
                error: function (xhr, err) {
                    $('.primaryBtn').prop('disabled', false)
                    displayErrors('Something went wrong, please try again!')
                },
                complete: function () {
                    $('.primaryBtn').prop('disabled', false);
                },
                success: function (data) {
                    if (data.success == true) {
                        if (data.isLead == 1 && $("input[name='is_lead']").val() == "yes" && data.loggedIn == 1 && data.numberChange == 0) {
                            $('#otp-form-news, #login-form-news').css("display", "none");
                            $('.thankYouMsgNews, .subscribeSectionNews').css("display", "block");
                        }
                        if (data.isLead !== 1 || data.numberChange == 1) {
                            $('.subscribeSectionNews, #login-form-news').css("display", "block");
                            $('#otp-form-news').css("display", "none");
                            $('#otp-phone-news').val($('#signup-phone-news').val())
                            $("#otp-form-news").submit();
                        }

                        if (data.nameChange == 1 && data.isLead == 1 && data.numberChange == 1 && data.sessionStart == 0) {
                            location.reload(true);
                        }
                        $('#signup-form-news').hide()
                    } else {
                        displayErrors(data.message)
                    }

                }
            });

        });

        $("#login-form-news").submit(function (e) {
            e.preventDefault()
            displayErrorsLead()
            var otp = $("input[name='digit[]']").map(function () { return $(this).val(); }).get().join('')
            $('input[name="otp"]').val(otp)
            $('#login-phone-news').val($('#otp-phone-news').val())
            var form = $(this)
            $.ajax({
                url: '/site/verify-otp-news',
                data: form.serialize(),
                dataType: 'json',
                method: 'POST',
                beforeSend: function () {
                    $('.primaryBtn').prop('disabled', true);
                },
                error: function (xhr, err) {
                    $('.primaryBtn').prop('disabled', false)
                    displayErrors('Something went wrong, please try again!')
                },
                complete: function () {
                    $('.primaryBtn').prop('disabled', false);
                },
                success: function (data) {
                    if (data.success == true) {
                        $('.thankYouMsgNews, .closeLeadFormContainerThankYou').css("display", "block");
                        $('.leadFormContainerNews, #login-form-news, .pageMask, .closeLeadFormContainer').css("display", "none");
                        setCookie('gmu_leadform', 1, 10);
                        displayErrors(data.message)
                    } else {
                        displayErrors(data.message)
                    }

                }
            });
        });

    });

    $("body").on('click', '.closeLeadFormContainerThankYou , .closeLeadForm', function () {
        document.body.style.height = 'auto';
        document.body.style.overflowY = 'auto';
        location.reload(true);
    })

    //news lead form changes
    $('body').on("change", '[name="stream"], [name="level"]', function () {
        var idName = this.id;
        $("#" + idName).css("color", "#333333")
    });
} else {
    if (window.location.href.includes('/user-profile') == false || window.location.href.includes('/college-compare') == false) {
        $.get('/lead-form-new', {}, function (leadFormContainerV2Lead) {
            $("#lead-form-js-new").html(leadFormContainerV2Lead);
            inputErrorClearonFocusLeadForm();

            $('.inputCityContainer select').select2({
                placeholder: "City you live in",
                name: 'inputCourse',
                ajax: {
                    url: "/ajax/lead-cities",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        var queryParameters = {
                            term: params.term
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        var states = data.states || [];
                        var results = states.map(function (state) {
                            return {
                                text: state.state_name,
                                children: state.cities.map(function (city) {
                                    return {
                                        id: city.id,
                                        text: city.text
                                    };
                                })
                            };
                        });

                        return { results: results };
                    },
                },
            });

            $('.inputStreamContainer select').select2({
                placeholder: "Stream Interested",
                name: 'inputStream',
                ajax: {
                    url: "/ajax/lead-stream",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        if (gmu.config.entity == 'college-listing') {
                            var entity_id = $("#leadform-entity_id").val();
                        } else {
                            var entity_id = gmu.config.entity_id;
                        }
                        var queryParameters = {
                            term: params.term,
                            entity: gmu.config.entity,
                            entity_id: entity_id,
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            // Function to enable/disable the select dropdown
            function toggleLevelSelect(enabled) {
                var $levelSelect = $('#interested_level_lead'); // select dropdown
                if (enabled) {
                    $levelSelect.prop('disabled', false);
                } else {
                    $levelSelect.prop('disabled', true);
                }
            }

            $('.inputStreamContainer select').on('change', function (e) {
                if (gmu.config.entity == 'college' || gmu.config.entity == 'college-listing') {
                    $('#interested_level_lead').val('').trigger('change');
                    if (gmu.config.entity == 'college-listing') {
                        var id = localStorage.getItem('collegeId');
                    } else {
                        var id = gmu.config.entity_id;
                    }

                    $.ajax({
                        url: '/ajax/lead-level',
                        data: {
                            entity: gmu.config.entity,
                            stream_id: $("#interested_stream_lead").val(), // selected stream ID
                            college_id: id
                        },
                        dataType: "json",
                        type: "GET",
                        success: function (data) {
                            if (data && data.length === 1) {
                                var $option = $("<option selected></option>").val(data[0].id).text(data[0].text);
                                $('#interested_level_lead').append($option).trigger('change');
                            }

                        }
                    });
                }
                toggleLevelSelect(true);
            });

            $('.inputLevelContainer select').select2({
                placeholder: "Level Interested",
                name: 'inputLevel',
                ajax: {
                    url: "/ajax/lead-level",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        if (gmu.config.entity == 'college-listing') {
                            var id = localStorage.getItem('collegeId');
                        } else {
                            var id = gmu.config.entity_id;
                        }
                        var queryParameters = {
                            term: params.term,
                            entity: gmu.config.entity,
                            stream_id: $("#interested_stream_lead").val(), // selected stream ID
                            college_id: id
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('.eduBudgetContainer select').select2({
                placeholder: "Education Budget",
                minimumResultsForSearch: -1
            });

            $('.highestQualContainer select').select2({
                placeholder: "Select your Highest Qualification",
                minimumResultsForSearch: -1
            });

            $('select[name="boardEntityTenth"]').select2({
                placeholder: "Select Board",
                name: 'boardEntity',
                ajax: {
                    url: "/ajax/board-list",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        var queryParameters = {
                            term: params.term,
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('select[name="boardEntityTwelve"]').select2({
                placeholder: "Select Board",
                name: 'boardEntityTwelve',
                ajax: {
                    url: "/ajax/board-list",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        var queryParameters = {
                            term: params.term,
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('select[name="boardEntityDiploma"]').select2({
                placeholder: "Select Board",
                name: 'boardEntityDiploma',
                ajax: {
                    url: "/ajax/board-list",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        var queryParameters = {
                            term: params.term,
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('.inputCourseGraduation select').select2({
                placeholder: "Course",
                name: 'graduationCourse',
                ajax: {
                    url: "/ajax/course-list",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        var queryParameters = {
                            term: params.term,
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('.inputCoursePostGraduation select').select2({
                placeholder: "Course",
                name: 'postGraduationCourse',
                ajax: {
                    url: "/ajax/course-list",
                    dataType: "json",
                    type: "GET",
                    data: function (params) {
                        var queryParameters = {
                            term: params.term,
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('select[name="graduateCollege"]').select2({
                placeholder: "Select College",
                name: 'graduateCollege',
                minimumInputLength: 3,
                ajax: {
                    url: "/ajax/college-list",
                    dataType: "json",
                    type: "GET",
                    minimumInputLength: 3,
                    data: function (params) {
                        var queryParameters = {
                            term: params.term,
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('select[name="postGraduateCollege"]').select2({
                placeholder: "Select College",
                name: 'postGraduateCollege',
                minimumInputLength: 3,
                ajax: {
                    url: "/ajax/college-list",
                    dataType: "json",
                    type: "GET",
                    minimumInputLength: 3,
                    data: function (params) {
                        var queryParameters = {
                            term: params.term,
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: item.text,
                                    id: item.id,
                                };
                            }),
                        };
                    },
                },
            });

            $('body').on('click', '.js-open-lead-form-new', function (e) {
                isModalOpenLead = true;
                document.addEventListener('touchmove', preventDefaultScrollLead(event), { passive: false });
                e.preventDefault();
                // $("body").css("overflowY", "hidden");
                setScrollPosition()
                if (typeof autoPopUpTimeOut !== 'undefined' && autoPopUpTimeOut !== null) {
                    clearTimeout(autoPopUpTimeOut);
                }
                $('.textDivHeading').html("");
                $('.textDivSubHeading').html("");
                document.querySelector('#lead-form-js-new').style.display = "block";
                document.querySelector('.pageMask').style.display = "block";
                var leadFormTitle = this.dataset.leadformtitle ?? '';
                document.querySelector('#interested_location').value = this.dataset.interestedlocation ?? null;
                document.querySelector('#leadform-state_id').value = this.dataset.stateid ?? '';
                document.querySelector('#leadform-state_name').value = this.dataset.statename ?? '';
                document.querySelector('#cta_location').value = this.dataset.ctalocation ?? '';
                document.querySelector('#cta_text').value = this.dataset.ctatext ?? '';
                document.querySelector('#entity_subtype').value = gmu.config.entity_subtype;
                document.querySelector('#entity_type').value = gmu.config.entity_type;
                document.querySelector('#leadform-entity').value = gmu.config.entity;
                document.querySelector('#leadform-entity_id').value = gmu.config.entity_id;
                document.querySelector("#durl").value = this.dataset.durl ?? '';
                document.querySelector("#dynamic_redirection").value = this.dataset.dynamic_redirection ?? '';
                document.querySelector("#activity_id").value = sessionStorage.getItem('activity_id') ?? '';
                // document.querySelector("#leadform-specialization_id").value = this.dataset.specializationid ?? '';

                if (this.dataset.subheadingtext) {
                    $('.textDivSubHeading').html(this.dataset.subheadingtext);
                } else {
                    $('.textDivSubHeading').html('Get details and latest updates');
                }

                if (leadFormTitle && typeof leadFormTitle == 'string') {
                    $('.textDivHeading').append(this.dataset.leadformtitle);
                } else {
                    $('.textDivHeading').append('REGISTER NOW TO APPLY');
                }
            });

            //auto pop up
            if (getCookie('gmu_leadform') != '1' && gmu.config.showLeadForm && gmu.config.show_lead_form != 1 && gmu.config.isLoggedIn == false && (window.location.href.includes('/review/create') == false) && window.location.href.includes('/user-profile') == false && gmu.config.statusCode != '404' && /[^/]*$/.exec($(location).attr('pathname'))[0].indexOf('predictor') < 1) {
                autoPopUpTimeOut = setTimeout(function () {
                    if (!($('#login-form-js').is(':visible'))) {
                        isModalOpenLead = true;
                        document.addEventListener('touchmove', preventDefaultScrollLead(), { passive: false });
                        if (($(".mobileMenu").width() / $('.mobileMenu').parent().width() * 100) != 100) {
                            if ($(".textDivHeading").html() == "") {
                                $(".textDivHeading").html("REGISTER NOW TO APPLY");
                                $('.textDivSubHeading').html('Get details and latest updates');
                            }
                            document.querySelector('#lead-form-js-new').style.display = "block";
                            document.querySelector('.pageMask').style.display = "block";
                            document.querySelector('#interested_location').value = gmu.config.interested_location ?? null;
                            document.querySelector('#cta_location').value = gmu.config.cta_location ?? '';
                            document.querySelector('#cta_text').value = 'Auto Pop Up';
                            document.querySelector('#entity_subtype').value = gmu.config.entity_subtype;
                            document.querySelector('#entity_type').value = gmu.config.entity_type;
                            document.querySelector("#activity_id").value = sessionStorage.getItem('activity_id') ?? '';
                            document.querySelector('#leadform-entity').value = gmu.config.entity;
                            document.querySelector('#leadform-entity_id').value = gmu.config.entity_id;
                            document.querySelector('#leadform-utm_source').value = gmu.config.utm_source ?? '';
                            document.querySelector('#leadform-utm_medium').value = gmu.config.utm_medium ?? '';
                            document.querySelector('#leadform-utm_campaign').value = gmu.config.utm_campaign ?? '';
                            document.querySelector('#course_id').value = gmu.config.course_id ?? '';
                            document.querySelector('#program_id').value = gmu.config.program_id ?? '';
                            $("body").css("overflowY", "hidden");
                            setScrollPosition()

                            if (gmu.config.entity == 'college-listing') {
                                var streamSlug = gmu.config.sponsor_params.stream;
                                var courseSlug = gmu.config.sponsor_params.course;
                            }

                            var auto_pop_entity_id = gmu.config.entity_id;
                            var entity = gmu.config.entity == 'board-sample-paper' ? 'board' : gmu.config.entity;

                            if (entity == '') {
                                return '';
                            }

                            var url = window.location.href;
                            var examSlug = '';
                            var matchingItems = '';

                            //get the first course type starts
                            var params = new URL(url).searchParams;
                            var courseType = params.get('course_type');

                            // If courseType is not null, get the first value
                            var matchingItems = collegeFilterExamCourseType(courseType);
                            if (matchingItems !== undefined) {
                                toggleLevel(matchingItems[0].value);
                            }
                            //get the first course type ends

                            //get the first exam selected starts
                            var pathname = new URL(url).pathname;
                            // Extract the string between "colleges-accepting-" and "-score-in-india"
                            var startIndex = pathname.indexOf("colleges-accepting-") + "colleges-accepting-".length;
                            var endIndex = pathname.indexOf("-score-in-india");
                            if (startIndex !== -1 && endIndex !== -1) {
                                var examSlug = pathname.substring(startIndex, endIndex);
                            }
                            //get the first exam selected ends

                            $.ajax({
                                type: 'POST',
                                url: '/ajax/lead-auto-pop-up',
                                data: { auto_pop_entity_id: auto_pop_entity_id, entity: entity, course: (courseSlug !== null ? courseSlug : ''), stream: (streamSlug !== undefined ? streamSlug : ''), examSlug: examSlug, course_type: matchingItems == undefined ? '' : matchingItems[0].value },
                                dataType: "json",
                                success: function (response) {
                                    if (response.success == true) {
                                        if (gmu.config.entity == 'exam' || gmu.config.entity == 'course' || gmu.config.pageName == 'ci' || gmu.config.pageName == 'program') {
                                            if (response.success == true && response.stream_id !== "") {
                                                toggleStreamLevelFields(response.stream_id, response.level)
                                            }
                                        } else if (gmu.config.entity == 'college-listing') {
                                            if (response.success == true && response.data.length !== 0 && response.data.stream_id !== "" && response.data.level !== "") {
                                                toggleStreamLevelFields(response.data.stream_id, response.data.level)
                                            } else if (response.success == true && response.data.length !== 0 && response.data.stream_id !== "" && response.data.level == "") {
                                                toggleStreamField(response.data.stream_id);
                                                toggleLevelSelect(true);
                                                EnableSubmitButtonForloggedInUsers()
                                            }
                                            if (response.data.exam_id !== "") {
                                                $("#college_filter_exam").val(response.data.exam_id)
                                            }
                                        } else if (gmu.config.entity == 'articles' || gmu.config.entity == 'news') {
                                            if (response.success == true && response.stream_id !== "") {
                                                toggleStreamLevelFields(response.stream_id, response.level)
                                            }
                                        } else if (gmu.config.entity == 'board') {
                                            toggleBoardLevel()
                                        }

                                        //auto fetch course, program and degree id
                                        if (gmu.config.pageName == 'ci' || gmu.config.pageName == 'program') {
                                            $("#course_id").val(gmu.config.course_id);
                                            $("#program_id").val(gmu.config.program_id);
                                            $("#page_name").val(gmu.config.pageName);
                                        } else if (gmu.config.entity == 'exam') {
                                            if (response.course_id !== "0") {
                                                $("#course_id").val(response.course_id);
                                            }
                                            $("#degree_id").val(response.level);
                                        }
                                    }

                                }
                            });
                        }
                    }
                }, 30000);
            }

            function toggleStreamLevelFields(stream_id, level = '') {
                toggleStreamField(stream_id, level);
            }

            function toggleStreamField(stream_id, level) {
                //22 -> others
                let highest_qualification_degree_id = ['9', '10', '11'];

                if (stream_id == '' || stream_id == '22' || $.inArray(level, highest_qualification_degree_id) !== -1) {
                    $(".inputStreamContainer").show();
                } else {
                    $(".inputStreamContainer").hide();
                    $('.inputStreamContainer select').append($('<option>', {
                        value: stream_id,
                        selected: true
                    }));
                }
                toggleLevel(level)
                EnableSubmitButtonForloggedInUsers();
            }

            function toggleLevel(level = '') {
                //12 -> others
                if (level !== '' && level !== '12') {
                    $(".inputLevelContainer").hide();
                    $('.inputLevelContainer select').append($('<option>', {
                        value: level,
                        selected: true,
                    })).prop('disabled', false);
                } else {
                    $(".inputLevelContainer").show();
                    $("#interested_level_lead").prop("disabled", false);
                }
            }

            function toggleBoardLevel() {
                if (gmu.config.board_level == "10") {
                    $(".inputLevelContainer").hide();
                    $('.inputLevelContainer select').append($('<option>', {
                        value: 4, //10th id from degree table
                        selected: true,
                    })).prop('disabled', false);
                    EnableSubmitButtonForloggedInUsers()
                } else if (gmu.config.board_level == "12") {
                    $(".inputLevelContainer").hide();
                    $('.inputLevelContainer select').append($('<option>', {
                        value: 1, //12th id from degree table
                        selected: true,
                    })).prop('disabled', false);
                    EnableSubmitButtonForloggedInUsers()
                }
            }

            // college and course page lead auto capture
            $("body").on('click', '.leadCourseCapture', function () {
                var id = $(this).closest('.lead-cta').attr('id');
                var program = $(this).closest('.lead-cta').attr('data-program') ?? $(this).attr('data-program');
                var leadCta = $(this).closest('.lead-cta').attr('data-lead_cta');
                var location = $(this).closest('.lead-cta').attr('data-location');
                var description = $(this).closest('.lead-cta').attr('data-description');
                var image = $(this).closest('.lead-cta').attr('data-image');
                var streamid = $(this).closest('.lead-cta').attr('data-streamid');
                var entity = $("#leadform-entity").val();
                var product_mapping_entity = gmu.config.product_mapping_entity == "false" ? null : gmu.config.product_mapping_entity;

                if ($(this).closest('.lead-cta').attr('data-course') !== undefined) {
                    var course = $(this).closest('.lead-cta').attr('data-course');
                } else {
                    var course = gmu.config.entity == 'course' ? $(this).attr('data-entityid') : id;
                }

                if (gmu.config.pageName == "courses-fees") {
                    $("#course_id").val(id);
                    if (leadCta == 7 || leadCta == 8 || leadCta == 9) {
                        var course = $(this).closest('.lead-cta').attr('data-courseId');
                        $("#course_id").val(course);
                        $("#program_id").val($(this).closest('.lead-cta').attr('data-programId'));
                        $("#page_name").val(gmu.config.pageName);
                    }
                }

                if (gmu.config.pageName == 'ci' || gmu.config.pageName == 'program') {
                    var course = gmu.config.course_id;
                    var programId = gmu.config.program_id !== '' ? gmu.config.program_id : $(this).closest('.lead-cta').attr('data-programId');
                    if (leadCta == 14 || leadCta == 16) {
                        mobileDevice ? $("#leadform-cta_location").val('colleges_course_information_wap_lead_' + id + '_card_left_cta8') : $("#leadform-cta_location").val('colleges_course_information_web_lead_' + id + '_card_center_cta8');
                    }
                    if (leadCta == 15) {
                        mobileDevice ? $("#leadform-cta_location").val('colleges_course_information_web_lead_' + id + '_card_right_cta7') : $("#leadform-cta_location").val('colleges_course_information_web_lead_' + id + '_card_right_cta7');
                    }
                    if (leadCta == 17) {
                        mobileDevice ? $("#leadform-cta_location").val('colleges_program_information_' + program + '_wap_lead_mtf_cta1') : $("#leadform-cta_location").val('colleges_program_information_' + program + '_web_lead_mtf_cta1');
                    }
                    $("#course_id").val(gmu.config.course_id);
                    $("#program_id").val(programId);
                    $("#page_name").val(gmu.config.pageName);
                } else if (gmu.config.entity == 'college' && gmu.config.college_course_count !== "") {
                    var course = gmu.config.college_course_count;
                    $("#course_id").val(gmu.config.college_course_count);
                } else if (gmu.config.pageName == 'info') {
                    $("#course_id").val(id);
                }

                //data pushed to lead attributes
                if (gmu.config.entity == 'course_stream' && gmu.config.pageName !== 'course-category') {
                    $("#leadform-cta_location").val(location);
                    $("#leadform-entity_id").val(id);
                    $(".subHeadingText").text(description);
                    $("#leadform-image").attr("src", image);
                } if (gmu.config.pageName == 'course-category') {
                    var stream = $(this).closest('.lead-cta').attr('data-streamid');
                    $(".subHeadingText").text(description);
                    $("#leadform-entity_id").val(id);
                    $("#cta_location").val(location);
                }

                $.ajax({
                    type: 'POST',
                    url: '/ajax/lead-course-capture',
                    data: { id: course, program: program, entity: entity, product_mapping_entity: product_mapping_entity },
                    dataType: "json",
                    success: function (response) {
                        if (response.success == true && response.stream_id !== "") {
                            toggleStreamLevelFields(response.stream_id, response.level)
                        } else if (gmu.config.entity == 'course_stream' && response.success == false && stream !== '') {
                            toggleStreamLevelFields(stream, '')
                        } else {
                            $(".inputStreamContainer").show();
                            $(".inputLevelContainer").show();
                        }

                    }
                });
            });

            //exam page lead auto capture
            $("body").on('click', '.examLeadValue', function () {
                var examId = gmu.config.pageName == 'exam-category' ? $(this).closest('.lead-cta').attr('data-entityid') : $(this).attr("data-entityid");
                var location = $(this).closest('.lead-cta').attr('data-location');
                var description = $(this).closest('.lead-cta').attr('data-description');
                var image = $(this).closest('.lead-cta').attr('data-image');

                //data pushed to lead attributes filter page
                if (gmu.config.pageName == 'exam-category') {
                    $("#cta_location").val(location);
                    $("#leadform-entity_id").val(examId);
                    $(".subHeadingText").text(description);
                    $("#leadform-image").attr("src", image);
                }
                if (examId == '') {
                    return '';
                }
                $.ajax({
                    type: 'POST',
                    url: '/ajax/exam-stream-level',
                    data: { exam_id: examId },
                    dataType: "json",
                    success: function (response) {
                        if (response.success == true) {
                            toggleStreamLevelFields(response.stream_id, response.level);
                        }
                        $("#degree_id").val(response.level);
                        if (response.course_id !== "0") {
                            $("#course_id").val(response.course_id);
                        }
                    }
                });
            });

            //article page lead auto capture
            $("body").on('click', '.articleLeadValue, .newsLeadValue', function () {
                var articleId = $(this).attr("data-entityid");
                var entity = gmu.config.entity;

                if (articleId == '') {
                    return '';
                }
                $.ajax({
                    type: 'POST',
                    url: '/ajax/article-stream-level',
                    data: { id: articleId, entity: entity },
                    dataType: "json",
                    success: function (response) {
                        if (response.success === true && response.stream_id !== "" || response.level !== '') {
                            toggleStreamLevelFields(response.stream_id, response.level);
                        }

                    }
                });
            });

            //board page lead hide stream and level for 10th and open board
            $("body").on('click', '.leadBoardAutoCapture', function () {
                toggleBoardLevel();
            });

            //college filter page lead auto fetch
            $("body").on('click', '.collegeFilterLead', function () {
                var courseSlug = $(this).attr("data-course") ?? null;
                var id = $(this).closest('.leadFilterData').attr('data-slug');
                localStorage.setItem('collegeId', id);
                if ($(this).attr("data-entity") == 'all-colleges' && $(this).attr("data-entityid") == '0') {
                    var stream = $(this).attr("data-stream") ?? null;
                    var courseId = $(this).attr("data-courseSlug") ?? null;
                } else {
                    var stream = $(this).closest('.leadFilterData').attr('data-stream');
                    var courseId = $(this).closest('.leadFilterData').attr('data-course');
                }
                var location = $(this).closest('.leadFilterData').attr('data-ctalocation');
                var state = $(this).closest('.leadFilterData').attr('data-stateid');
                var city = $(this).closest('.leadFilterData').attr('data-cityid');
                var entity = $(this).closest('.leadFilterData').attr('data-filter') ?? '';
                var title = $(this).closest('.leadFilterData').attr('data-title');
                var description = $(this).closest('.leadFilterData').attr('data-description');
                var image = $(this).closest('.leadFilterData').attr('data-image');

                var url = window.location.href;
                var examSlug = '';
                var matchingItems = '';

                //get the first course type starts
                var params = new URL(url).searchParams;
                var courseType = params.get('course_type');
                var matchingItems = collegeFilterExamCourseType(courseType);
                if (matchingItems !== undefined) {
                    toggleLevel(matchingItems[0].value);
                }
                //get the first course type ends

                //get the first exam selected starts
                var pathname = new URL(url).pathname;

                // Extract the string between "colleges-accepting-" and "-score-in-india"
                var startIndex = pathname.indexOf("colleges-accepting-") + "colleges-accepting-".length;
                var endIndex = pathname.indexOf("-score-in-india");
                if (startIndex !== -1 && endIndex !== -1) {
                    var examSlug = pathname.substring(startIndex, endIndex);
                }
                //get the first exam selected ends

                $.ajax({
                    type: 'POST',
                    url: '/ajax/all-college-lead-values',
                    data: { college_id: id ?? '', course: courseId ?? '', courseSlug: courseSlug ?? '', stream: stream, examSlug: examSlug, course_type: matchingItems == undefined ? '' : matchingItems[0].value },
                    dataType: "json",
                    success: function (response) {
                        //data pushed to lead attributes
                        $("#interested_location").val(city);
                        $("#leadform-state_id").val(state);
                        $("#cta_location").val(location);
                        $("#leadform-entity_id").val(id);
                        $("#leadform-entity").val(entity);
                        $(".headingText").text(title);
                        $(".subHeadingText").text(description);
                        $("#leadform-image").attr("src", image);
                        $("#course_id").val(courseId);

                        if (response.success == true && response.data.length !== 0 && response.data.stream_id !== "" && response.data.level !== "") {
                            toggleStreamLevelFields(response.data.stream_id, response.data.level)
                        } else if (response.success == true && response.data.length !== 0 && response.data.stream_id !== "" && response.data.level == "") {
                            toggleStreamField(response.data.stream_id);
                            toggleLevelSelect(true);
                            EnableSubmitButtonForloggedInUsers()
                        } else if (response.success == true && response.data.length !== 0 && response.data.level !== "") {
                            $(".inputStreamContainer").show();
                            $(".inputLevelContainer").hide();
                            $('.inputLevelContainer select').append($('<option>', {
                                value: response.data.level,
                                selected: true,
                            })).prop('disabled', false);
                        } else if (response.data.length == 0) {
                            $(".inputStreamContainer").show();
                            $(".inputLevelContainer").show();
                        }
                        if (response.data.exam_id !== "") {
                            $("#college_filter_exam").val(response.data.exam_id)
                        }
                    }
                });
            });

            function collegeFilterExamCourseType(courseType) {
                // If courseType is not null, get the first value
                if (courseType !== null) {
                    var firstValue = courseType.split(',')[0]; // Split by comma and get the first value

                    var data = [
                        { key: 'bachelors', value: 1 },
                        { key: 'masters', value: 2 },
                        { key: 'doctorate', value: 3 },
                        { key: 'diploma', value: 4 },
                        { key: 'postgraduate-diploma', value: 5 },
                        { key: 'certificate', value: 6 },
                        { key: 'postgraduate-certificate', value: 7 },
                    ];

                    // Filter the array based on key and value
                    var matchingItems = data.filter(function (item) {
                        return item.key === firstValue;
                    });
                    return matchingItems;
                }
            }
            //lead auto fetch end

            //email validation
            if (document.querySelector("#formEmail") !== null) {
                document.querySelector("#formEmail").addEventListener('input', (e) => {
                    e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
                    if (e.target.value.match(/@/g)) {
                        if ((e.target.value.match(/\./g) || []).length > 2) {
                            e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                        }
                    } else {
                        if ((e.target.value.match(/\./g) || []).length > 1) {
                            e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                        }
                    }

                    var email = $('#formEmail').val();
                    let strLst = email.slice(email.indexOf("@") + 1, email.length)

                    if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
                        $(".errorMsgEmail").html("Email is not a valid email address.");
                        $('.mobileNumberField').prop("disabled", true);
                        $('.OTPField').prop("disabled", true);
                        $('.sendOtpButton').css("pointer-events", "none");
                    } else {
                        if (nameInputLead.value.length > 0 && streamSelectLead.value.length > 0 && levelSelectLead.value.length > 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0)) {
                            $('.mobileNumberField').prop("disabled", false);
                            $('.OTPField').prop("disabled", false);
                            $('.sendOtpButton').css("pointer-events", "unset");
                        } else {
                            $('.sendOtpButton').css("pointer-events", "unset");
                        }
                        $('.errorMsgEmail').html('');
                    }
                })
            }

            //name validation
            $(".formName").on("input", function (e) {
                var inputValue = $(this).val();
                var regex = /^[A-Za-z ]+$/;

                if (!regex.test(inputValue)) {
                    var nameValue = inputValue.replace(/[^A-Za-z ]/g, '');
                    $(this).val(nameValue);
                }
            });

            //lead form mobile validation
            if (document.querySelector("#formMobile") !== null) {
                document.querySelector("#formMobile").addEventListener('input', (e) => {
                    e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '')
                });
            }

            //get field info
            const nameInputLead = document.querySelector('#formName');
            const emailInputLead = document.querySelector('#formEmail');
            const mobileInputLead = document.querySelector('#formMobile');
            const streamSelectLead = document.querySelector('#interested_stream_lead');
            const levelSelectLead = document.querySelector('#interested_level_lead');
            const citySelectLead = document.querySelector('#leadform-interested_location');
            const cityInputIp = document.querySelector('#current_city_ip');
            const otpInputLead = document.querySelector('#OTPField');
            const submitButtonLead = document.querySelector('#firstScreenSubmit');

            //Function to check if all fields are filled
            $(".streamClass, .levelClass, .nameClass, .emailClass, .cityClass, .mobileNumberField, .OTPField").bind("change keyup", function (event) {
                if (gmu.config.isLoggedIn == false) {
                    if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0 && (otpInputLead.value.length == 4 && $(".inputOTPContainer .validationError").html() == '')) {
                        submitButtonLead.removeAttribute('disabled');
                    } else {
                        submitButtonLead.setAttribute('disabled', true);
                    }
                } else {
                    submitButtonFunctionality()
                }
            });

            function submitButtonFunctionality() {
                if (localStorage.getItem("phone") !== $("input[name='hiddenNumber']").val()) {
                    if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0 && (otpInputLead.value.length == 4 && $(".inputOTPContainer .validationError").html() == '')) {
                        submitButtonLead.removeAttribute('disabled');
                    } else {
                        submitButtonLead.setAttribute('disabled', true);
                    }
                } else {
                    if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0) {
                        submitButtonLead.removeAttribute('disabled');
                    } else {
                        submitButtonLead.setAttribute('disabled', true);
                    }
                }
            }

            function mobileFieldFunctionality() {
                if (nameInputLead.value.length > 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && streamSelectLead.value.length > 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && levelSelectLead.value.length > 0) {
                    $('.mobileNumberField').prop("disabled", false);
                } else {
                    $('.mobileNumberField').prop("disabled", true);
                }
            }

            function EnableSubmitButtonForloggedInUsers() {
                if (gmu.config.isLoggedIn == true) {
                    submitButtonFunctionality();
                    mobileFieldFunctionality();
                }
            }

            $(".streamClass, .levelClass, .nameClass, .emailClass, .cityClass").bind("change keyup", function (event) {
                mobileFieldFunctionality();
            });

            $('input[type="date"]').change(function () {
                $(this).attr('style', 'color: #282828!important');
            });

            $("body").on("change", "#educationBudget, #interested_stream_lead, #leadform-interested_location", function () {
                $('.sponsorCollegeLead').empty();
            });

            if (gmu.config.isLoggedIn == true) {
                localStorage.setItem('phone', $("input[name='phone']").val());
            }

            let backButton = document.querySelector('.backButton');
            if (backButton !== null) {
                backButton.addEventListener("click", () => {
                    if ($("input[name='inputOTP']").val() !== '') {
                        $(".OTPField").attr('disabled', true);

                    }
                });
            }

            //function to navigate back to the forms
            document.querySelectorAll(".backButton").forEach((formNavigationBtn) => {
                // Add a click event listener to all the prev and next the button.
                formNavigationBtn.addEventListener("click", () => {
                    // Get the value of the step.

                    var stepNumber = parseInt(formNavigationBtn.getAttribute("step_number"));
                    if (localStorage.getItem('sponsorCollege') == 0 && stepNumber == 3) {
                        var stepNumber = 2;
                    } else if (localStorage.getItem('sponsorCollege') == 1 && stepNumber == 3) {
                        var stepNumber = 3;
                    }

                    // Call the function to navigate to the target form step.
                    navigateToFormStep(stepNumber);
                });
            });

            // Function to navigate between form steps
            const navigateToFormStep = (stepNumber) => {
                if (stepNumber == 1) {
                    $('.textDivHeading').css("display", "block");
                    $('.textDivHeadingNew').css("display", "none");
                } else if (stepNumber == 2) {
                    $('.textDivHeading').css("display", "none");
                    $('.textDivHeadingNew').css("display", "block");
                    $('.textDivHeadingNew').html("Add Your Exam Details To Get Personalized Recommendations");
                } else if (stepNumber == 3) {
                    $('.textDivHeading').css("display", "none");
                    $('.textDivHeadingNew').css("display", "block");
                    $('.textDivHeadingNew').html("Pick 1 or More Colleges To Speed Up Your Admission Process");
                } else if (stepNumber == 4) {
                    $('.textDivHeading').css("display", "none");
                    $('.textDivHeadingNew').css("display", "block");
                    $('.textDivHeadingNew').html("Add Your Academic Details To Improve Your Admission Chances");
                }
                // Hide all form steps
                document.querySelectorAll(".form-step").forEach((formStepElement) => {
                    formStepElement.classList.add("inactiveStep");
                });
                // Show the current form step (as passed to the function).
                const leadSteps = document.querySelector("#step-" + stepNumber);

                if (leadSteps !== null) {
                    document.querySelector("#step-" + stepNumber).classList.remove("inactiveStep");
                }
                $(".errorExamMsg").html("");
            };

            // SendOTPButton toggle
            var sendOtpButton = document.querySelector('.sendOtpButton');
            var OtpField = document.querySelector('.inputOTPContainer');
            if (document.querySelector('.mobileNumberField') !== null) {
                document.querySelector('.mobileNumberField').addEventListener('input', (e) => {
                    $("input[name='hiddenNumber']").val("")
                    if (e.target.value.length === 10) {
                        $(".otpclass .validationError").remove();
                        $('.otpclass input').removeClass('errorInputField');
                        $(".OTPField").attr('disabled', false);
                        if ($(".spriteIcon").hasClass("tickIcon") == true) {
                            $(".otpclass").append("<span class='otpTimer'></span>");
                            $(".otpclass").find(".tickIcon").remove();
                        }
                        document.querySelector('.inputOTPContainer').style.display = 'block';
                        if ($("input[name='hiddenNumber']").val() !== $("input[name='phone']").val()) {
                            $('.stepOne').click();
                            $.ajax({
                                url: '/lead-v3/send-otp-lead',
                                data: { phone: e.target.value, is_lead: 1 },
                                dataType: 'json',
                                method: 'POST',
                                beforeSend: function () {
                                    $('.primaryBtn').prop('disabled', true);
                                },
                                error: function (xhr, err) {
                                    $('.primaryBtn').prop('disabled', false)
                                    displayErrors('Something went wrong, please try again!')
                                },
                                complete: function () {
                                    $('.primaryBtn').prop('disabled', false);
                                },
                                success: function (data) {
                                    if (data) {
                                        if (data.is_registered_user == true) {
                                            disableOtpField()
                                        } else {
                                            otpTimerLead(30);
                                            $(".otpTimer").css("display", "block");
                                        }
                                    }
                                }
                            });
                        }
                    } else {
                        disableOtpField();
                        $(".OTPField").val("");
                    }
                })
            }
            //radio button logic to hide exam score fields
            document.querySelectorAll('.examItem > input').forEach((input) => {
                input.addEventListener("change", (event) => {
                    if (document.querySelector('#labelIDNo').checked === true) {
                        document.querySelector('.examScoreContainer').style.display = 'none';
                        document.querySelector('.datepickerContainer').style.display = 'none';
                    } else {
                        // document.querySelector('.examScoreContainer').style.display = 'block';
                        if ($('input[name="scheduledExamOptions[]"]:checked').val() == 'yes') {
                            document.querySelector('.datepickerContainer').style.display = 'none';
                            document.querySelector('.scoreInputContainer').style.display = 'block';
                            $(".examScoreContainer .scoreExam").css("display", "flex")
                            $(".examScoreContainer .dateExam").css("display", "none")
                        } else {
                            document.querySelector('.scoreInputContainer').style.display = 'none';
                            document.querySelector('.datepickerContainer').style.display = 'block';
                            $(".examScoreContainer .scoreExam").css("display", "none")
                            $(".examScoreContainer .dateExam").css("display", "flex")
                        }
                    }
                })
            })

            if (document.querySelector('.OTPField') !== null) {
                document.querySelector('.OTPField').addEventListener('input', (e) => {
                    e.preventDefault();
                    if (e.target.value.length === 4) {
                        var otp = $("input[name='inputOTP']").val();
                        var mobile = $("input[name='phone']").val();
                        var stream = $('select[name="inputStream"] option:selected').val();
                        var level = $('select[name="inputLevel"] option:selected').val()
                        var student_id = localStorage.getItem('studentId') ?? '';
                        var preference_id = localStorage.getItem('preferenceId') ?? '';
                        var entity = gmu.config.entity

                        $.ajax({
                            url: '/lead-v3/verify-otp',
                            data: { otp: otp, entity: entity, mobile: mobile, inputStream: stream, inputLevel: level, student_id: student_id, preference_id: preference_id },
                            dataType: 'json',
                            method: 'POST',
                            success: function (data) {
                                if (data.success == true) {
                                    localStorage.setItem('verified', 1);
                                    setCookie('gmu_leadform', 1, 10);
                                    $('.errorMsg').html('');
                                    $('.validationError').html('');
                                    $(".otpTimer").remove();
                                    $(".otpclass").append("<span class='spriteIcon tickIcon'></span>");
                                    getExamAcademicDataV3(data);
                                    // moveLabelIntoFlatPickr()
                                    addValidationToWholeNumberExamSection();
                                    if ($("input[name='hiddenNumber']").val() !== $("input[name='phone']").val() && nameInputLead.value.length !== 0
                                        && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '')
                                        && mobileInputLead.value.length !== 0 && streamSelectLead.value.length !== 0
                                        && levelSelectLead.value.length !== 0 && (otpInputLead.value.length == 4
                                            && ($(".inputOTPContainer .validationError").length == 0) || $(".inputOTPContainer .validationError").html() == '')) {
                                        document.querySelector('.headerAndForm').style.display = 'none';
                                        document.querySelector('.finalScreen').style.display = 'flex';
                                        $('#step-1').addClass('formSubmitted');
                                    }
                                    $("input[name='hiddenNumber']").val($("input[name='phone']").val())
                                    sendOtpButton.style.display = 'none';
                                    if ($("#rank_predictor_lead_value").val() === "1") {
                                        fetchRankPredict()
                                    }
                                    if (localStorage.getItem('reviewReadMoreId')) {
                                        localStorage.setItem('loggedIn', "Yes");
                                    }
                                    if (($('#durl').length) != 0 || ($('#dynamic_redirection').length) != 0) {
                                        if ($("#durl").val() != '') {
                                            dynamicPdfUrlRedirect($("#durl").val());
                                            getScrollPosition()
                                        }

                                        if ($("#dynamic_redirection").val() != '') {
                                            dynamicPdfUrlRedirect($("#dynamic_redirection").val());
                                            getScrollPosition()
                                        }
                                    }

                                } else {
                                    $(".stepOne").attr('disabled', 'disabled')
                                    displayErrorsLead(data.message)
                                }
                            }
                        });
                    }
                });
            }

            if (sendOtpButton !== null) {
                sendOtpButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    var mobile = $("input[name='phone']").val();
                    var student_id = localStorage.getItem('studentId') ?? '';

                    $.ajax({
                        url: '/lead-v3/resend-otp-lead',
                        data: { phone: mobile, is_lead: 1, student_id: student_id },
                        dataType: 'json',
                        method: 'POST',
                        success: function (data) {
                            if (data.success == true) {
                                document.querySelector('.inputOTPContainer').style.display = 'block';
                                $(".otpTimer").css("display", "block");
                                otpTimerLead(30);
                                sendOtpButton.textContent = 'Resend';
                                sendOtpButton.style.display = 'none';
                            } else {
                                displayErrorsLead(data.message)
                            }
                        }
                    });
                })
            }

            window.onbeforeunload = function () {
                localStorage.removeItem('leadId');
                localStorage.removeItem('sponsorCollege');
                localStorage.removeItem('student_college_shortlist_id');
                localStorage.removeItem('activityId');
                localStorage.removeItem('phone');
                localStorage.removeItem('verified');
                localStorage.removeItem('checkedDataIds');
                localStorage.removeItem('onClickUrl');
                localStorage.removeItem('collegeId');
            };

            // Close Dialog
            if (document.querySelector('.closeDialog') !== null) {
                localStorage.removeItem('collegeId');
                document.querySelector('.closeDialog').addEventListener('click', () => {
                    document.querySelector('#lead-form-js-new').style.display = "none";
                    var onClickUrl = localStorage.getItem('onClickUrl') == null ? '' : localStorage.getItem('onClickUrl');
                    if (onClickUrl) {
                        var leadForm = $('.js-open-lead-form-new');
                        leadForm.each(function () {
                            $(this).attr('onclick', onClickUrl);
                        });
                    }
                    isModalOpenLead = false;
                    $(".textDivHeadingNew").html("");
                    $(".errorMsgEmail").html("");
                    $('.inputStreamContainer select option').remove();
                    $('.inputLevelContainer select option').remove();
                    $("#leadform-interested_location").val('').trigger('change');
                    $("#interested_level_lead").prop("disabled", true);
                    $(".pageMask").css("display", "none");
                    $(".otpclass").css("display", "none");
                    $("body").css("overflowY", "unset");
                    document.querySelector('body').style.position = 'unset';
                    document.querySelector('body').style.height = 'unset';
                    getScrollPosition();
                    document.querySelector('body').style.bottom = 'unset';
                    document.querySelector('body').style.right = 'unset';
                    document.querySelector('body').style.left = 'unset';

                    //check to loggedin Through review readMore
                    if ((localStorage.getItem('loggedIn') === 'Yes')) {
                        localStorage.setItem('singupClicked', "Yes");
                        scrollToClickedReview();
                    }
                    $('.signupModalForm').trigger("reset");
                    inputErrorClearonFocusLeadForm()
                    localStorage.removeItem('onClickUrl');
                    if ($('#step-1').hasClass('formSubmitted')) {
                        var student_id = localStorage.getItem('studentId') ?? '';
                        var activity_id = localStorage.getItem('activityId') ?? '';
                        var csrf = $("input[name='_csrf-frontend']").val();
                        $.ajax({
                            url: '/lead-v3/student-session-activate',
                            data: { student_id: student_id, activity_id: activity_id },
                            dataType: 'json',
                            method: 'POST',
                            beforeSend: function () {
                                $('.primaryBtn').prop('disabled', true);
                            },
                            error: function (xhr, err) {
                                $('.primaryBtn').prop('disabled', false)
                                displayErrorsLead('Something went wrong, please try again!')
                            },
                            complete: function () {
                                $('.primaryBtn').prop('disabled', false);
                            },
                            success: function (data) {
                                if (data.success == true) {
                                    setCookie('gmu_leadform', 1, 10);
                                    location.reload(true);
                                } else {
                                    document.querySelector('#lead-form-js-new').style.display = "none";
                                }
                            }
                        });
                    } else {
                        document.querySelector('#lead-form-js-new').style.display = "none";
                        return false;
                    }
                })
            }

            //unset all the style when esc key is pressed, after opening lead form
            $(document).keydown(function (e) {
                if (e.keyCode == 27) {
                    $("body").css("overflowY", "unset");
                    $(".headingText").html("");
                    $(".pageMask").css("display", "none");
                    location.reload(true);
                };
            });

            //Lead Form utm trigger changes
            //$(document).ready(function () {
            setTimeout(function () {
                if (gmu.config.show_lead_form) {
                    var url = document.querySelectorAll('meta[property="og:url"]')[0].content ?? window.location.href;
                    $('.closeLeadFormContainer').attr('style', 'display: none !important');
                    $('.headingText').append('REGISTER NOW TO APPLY');
                    $('.thankYouMsg').append('<i class="spriteIcon closeLeadFormUtm " style="display: block !important"></i>');
                    $("body #leadform-url").val(url);
                    $('body #leadform-entity').val(gmu.config.entity);
                    $('body #leadform-entity_id').val(gmu.config.entity_id);
                    $('body ##cta_location').val(gmu.config.cta_location);
                    $('body #leadform-utm_source').val(gmu.config.utm_source);
                    $('body #leadform-utm_medium').val(gmu.config.utm_medium);
                    $('body #leadform-utm_campaign').val(gmu.config.utm_campaign);
                    document.querySelector('#lead-form-js-new').style.display = "block";
                }
            }, 1000);

            //utm source leadform fadeout
            $("body").on("click", ".closeLeadFormUtm", function () {
                // $(".leadFormContainer").fadeOut();
                document.querySelector('#lead-form-js-new').style.display = "none";
            });

            $("body").bind("change keyup", "#educationBudget, .leadExamValues", function (event) {
                if ($(".errorEducationMsg").html() !== '') {
                    $(".errorEducationMsg").html("");
                }
                if ($(".errorExamMsg").html() !== '') {
                    $(".errorExamMsg").html("");
                }
            });

            //submit forms starts
            $("#firstScreenSubmit").click(function (e) {
                e.preventDefault();
                $(".errorEducationMsg").html("");
                var student_activity_parent_id = sessionStorage.getItem('activity_id') ?? '';
                var form = $('#firstScreen');
                var lead_id = localStorage.getItem('leadId') ?? '';
                var student_id = localStorage.getItem('studentId') ?? '';
                var preference_id = localStorage.getItem('preferenceId') ?? '';
                var activity_id = localStorage.getItem('activityId') ?? '';
                var student_college_shortlist_id = localStorage.getItem('student_college_shortlist_id') ?? '';
                var product_mapping_entity = gmu.config.product_mapping_entity == "false" ? null : gmu.config.product_mapping_entity;
                var product_mapping_entity_id = gmu.config.product_mapping_entity_id ?? '';
                var verified = localStorage.getItem('verified') ?? '';

                $.ajax({
                    url: '/lead-v3/screen-one',
                    data: form.serialize() + "&lead_id=" + lead_id + "&verified=" + verified + "&product_mapping_entity=" + product_mapping_entity + "&product_mapping_entity_id=" + product_mapping_entity_id + "&student_id=" + student_id + "&preference_id=" + preference_id + "&activity_id=" + activity_id + "&student_college_shortlist_id=" + student_college_shortlist_id + "&student_activity_parent_id=" + student_activity_parent_id,
                    dataType: 'json',
                    method: 'POST',
                    beforeSend: function () {
                        $('.primaryBtn').prop('disabled', true);
                    },
                    error: function (xhr, err) {
                        $('.primaryBtn').prop('disabled', false)
                        displayErrorsLead('Something went wrong, please try again!')
                    },
                    complete: function () {
                        $('.primaryBtn').prop('disabled', false);
                    },
                    success: function (data) {
                        if (data.success == true) {
                            if ($("#rank_predictor_lead_value").val() === "1") {
                                fetchRankPredict()
                            }
                            if (localStorage.getItem('reviewReadMoreId')) {
                                localStorage.setItem('loggedIn', "Yes");
                            }
                            // if (($('#durl').length) != 0 || ($('#dynamic_redirection').length) != 0) {
                            //     if ($("#durl").val() != '') {
                            //         dynamicPdfUrlRedirect($("#durl").val());
                            //         getScrollPosition()
                            //     }

                            //     if ($("#dynamic_redirection").val() != '') {
                            //         dynamicPdfUrlRedirect($("#dynamic_redirection").val());
                            //         getScrollPosition()
                            //     }
                            // }

                            if ($(".otpclass .validationError").html() !== undefined) {
                                $(".otpclass .validationError").html("Please enter the valid otp")
                                return false;
                            }
                            displayErrorsLead();
                            var response = {
                                'mobile': $("input[name='phone']").val(),
                                'name': $("input[name='name']").val(),
                                'email': $("input[name='email']").val(),
                                'stream': $("input[name='inputStream']").val(),
                                'level': $("input[name='inputLevel']").val(),
                                'qualification': data.highest_qualification,
                                'specialization': data.specialization,
                                'current_location': $("input[name='current_city']").val(),
                                'state_id': $("input[name='state_id']").val(),
                                'state_name': $("input[name='state_name']").val(),
                                'interested_course': data.courseId,
                                'cta_location': data.cta_location,
                                'source_url': data.source_url,
                                'user_status': data.user_status,
                                'user_type': data.user_type,
                            };
                            webEngageEvent(gmu.config.entity + '_lead_submit', response);
                            $("body").css("overflowY", "hidden");
                            localStorage.setItem('activityId', data.student_activity_id);
                            localStorage.setItem('session_activity_id', data.student_activity_id);
                            localStorage.setItem('preferenceId', data.student_prefrence_id);
                            localStorage.setItem('leadId', data.lead_id);
                            localStorage.setItem('studentId', data.student_id);
                            localStorage.setItem('student_college_shortlist_id', data.student_college_shortlist_id);
                            if (data.is_mobile_verified == 1 && gmu.config.isLoggedIn == true && data.numberChange == 0) {
                                $("input[name='inputOTP']").val(data.otp);
                                $(".otpTimer").remove();
                                $(".otpclass").append("<span class='spriteIcon tickIcon'></span>");
                            }
                            if (data.numberChange == 0 || data.numberChange == 1) {
                                if ($('input[name="scheduledExamOptions[]"]:checked').val() == 'booked') {
                                    document.querySelector('.scoreInputContainer').style.display = 'none';
                                    document.querySelector('.datepickerContainer').style.display = 'block';
                                    $(".signupModal .examScoreContainer .scoreExam").css("display", "none")
                                    $(".signupModal .examScoreContainer .dateExam").css("display", "none")
                                }
                                getExamAcademicDataV3(data);
                            }

                            addValidationToWholeNumberExamSection();

                            if (gmu.config.isLoggedIn == true) {
                                $(".messageOneLeadFormSubmit").html("Thank you for your interest.")
                                if (localStorage.getItem('phone') == $("input[name='phone']").val()) {
                                    document.querySelector('.headerAndForm').style.display = 'none';
                                    document.querySelector('.finalScreen').style.display = 'flex';
                                    $('#step-1').addClass('formSubmitted');
                                } else if (nameInputLead.value.length !== 0 && emailInputLead.value.length !== 0 && mobileInputLead.value.length !== 0 && streamSelectLead.value.length !== 0 && $("input[name='hiddenNumber']").val() == $("input[name='phone']").val()) {
                                    document.querySelector('.headerAndForm').style.display = 'none';
                                    document.querySelector('.finalScreen').style.display = 'flex';
                                    $('#step-1').addClass('formSubmitted');
                                }
                            } else if (nameInputLead.value.length !== 0 && emailInputLead.value.length !== 0 && mobileInputLead.value.length !== 0 && streamSelectLead.value.length !== 0 && (otpInputLead.value.length == 4 && $(".inputOTPContainer .validationError").html() == '')) {
                                $(".messageOneLeadFormSubmit").html("Sign-up successful.")
                                document.querySelector('.headerAndForm').style.display = 'none';
                                document.querySelector('.finalScreen').style.display = 'flex';
                            }
                        } else {
                            if ($(".inputEmailContainer .errorMsg").html() !== '') {
                                delete data.message.phone;
                            }
                            displayErrorsLead(data.message)
                        }

                    }
                });
            });

            $("#secondScreenSubmit").click(function (e) {
                e.preventDefault();
                //check if third screen college is checked or not
                var form = $("#secondScreen");
                var lead_id = localStorage.getItem('leadId') ?? '';
                var student_id = localStorage.getItem('studentId') ?? '';
                var preference_id = localStorage.getItem('preferenceId') ?? '';
                var activity_id = localStorage.getItem('activityId') ?? '';
                var student_college_shortlist_id = localStorage.getItem('student_college_shortlist_id') ?? '';
                var stream = $('select[name="inputStream"] option:selected').val();
                var educationBudget = $('select[name="educationBudget"] option:selected').val();
                if (gmu.config.entity_type == "college-listing") {
                    var entity = document.querySelector('#leadform-entity').value
                    var entity_id = document.querySelector('#leadform-entity_id').value
                } else {
                    var entity = gmu.config.entity;
                    var entity_id = gmu.config.entity_id;
                }

                if ($('input[name="scheduledExamOptions[]"]:checked').val() == 'yes') {
                    var examScores = [];
                    $("[name='examScore[]']").each(function () {
                        examScores.push($(this).val());
                    });

                    var exam = examScores.slice(0, 4);

                    let empty_exam_values = exam.every((item) => {
                        return item === ''
                    })

                    if (empty_exam_values == true) {
                        $('.errorExamMsg').html('Type Atleat one Exam Score');
                        return false;
                    }
                }
                if ($('select[name="educationBudget"]').val() == '') {
                    $('.errorEducationMsg').html('Education Budget Cannot be blank');
                    return false;
                }

                $.ajax({
                    url: '/lead-v3/screen-two',
                    data: form.serialize() + "&entity=" + entity + "&entity_id=" + entity_id + "&stream=" + stream + "&lead_id=" + lead_id + "&student_id=" + student_id + "&preference_id=" + preference_id + "&activity_id=" + activity_id + "&student_college_shortlist_id=" + student_college_shortlist_id + "&educationBudget=" + educationBudget,
                    dataType: 'json',
                    method: 'POST',
                    beforeSend: function () {
                        $('.primaryBtn').prop('disabled', true);
                    },
                    error: function (xhr, err) {
                        $('.primaryBtn').prop('disabled', false)
                        displayErrorsLead('Something went wrong, please try again!')
                    },
                    complete: function () {
                        $('.primaryBtn').prop('disabled', false);
                    },
                    success: function (data) {
                        $("body").css("overflowY", "hidden");
                        if (data.success == true) {
                            var sponsorCollegeLead = document.getElementsByClassName('sponsorCollegeLead');
                            var childElementCount = sponsorCollegeLead[0].childElementCount;

                            if (data.sponsorCollege.length !== 0 && childElementCount === 0) {
                                localStorage.setItem('sponsorCollege', 1);
                                $(".sponsorCollegeLead").html(data.sponsorCollege);
                                navigateToFormStep(3);
                                showStoredCheckedColleges();
                                // $(".signupModal").css("overflow", "auto")
                            } else if (childElementCount === 1) {
                                navigateToFormStep(3);
                                showStoredCheckedColleges();
                            } else {
                                localStorage.setItem('sponsorCollege', 0);
                                if ($('select[name="inputLevel"] option:selected').val() == '1') { //bachelors
                                    navigateToFormStep(4);
                                    $(".tenthClass").css("display", "block");
                                    $(".twelveClass").css("display", "block");
                                } else if ($('select[name="inputLevel"] option:selected').val() == '4') { //diploma
                                    navigateToFormStep(4);
                                    $(".tenthClass").css("display", "block");
                                    $(".diplomaClass").css("display", "block");
                                } else if ($('select[name="inputLevel"] option:selected').val() == '2') { //masters
                                    navigateToFormStep(4);
                                    $(".twelveClass").css("display", "block");
                                    $(".gradutaionDetails").css("display", "block");
                                } else {
                                    navigateToFormStep(4);
                                    $(".gradutaionDetails").css("display", "block");
                                    $(".postGradutaionDetails").css("display", "block");
                                }
                            }
                        } else {
                            return false;
                        }
                    }
                });

            });

            $("body").on('change', '.leadFormRecommendationScreen', function () {
                var dataId = $(this).data('id');
                var checkedDataIds = JSON.parse(localStorage.getItem('checkedDataIds')) || [];

                // Check if the checkbox is checked
                if ($(this).prop('checked')) {
                    // Store the data-id in local storage as part of an array
                    if (checkedDataIds.indexOf(dataId) === -1) {
                        checkedDataIds.push(dataId);
                    }
                    localStorage.setItem('checkedDataIds', JSON.stringify(checkedDataIds));
                } else {
                    // If the checkbox is unchecked, remove the data-id from local storage
                    var index = checkedDataIds.indexOf(dataId);
                    if (index !== -1) {
                        checkedDataIds.splice(index, 1);
                    }
                    localStorage.setItem('checkedDataIds', JSON.stringify(checkedDataIds));
                }
            });

            $("#thirdScreenSubmit").click(function (e) {
                $("body").css("overflowY", "hidden");
                // navigateToFormStep(4);
                e.preventDefault()
                displayErrorsLead()
                $(".errorExamMsg").html("")
                var form = $("#thirdScreen");
                var student_id = localStorage.getItem('studentId') ?? '';
                var activity_id = localStorage.getItem('activityId') ?? '';
                var csrf = $("input[name='_csrf-frontend']").val();

                var college_id = $.map($("input[name='checked[]']"), function (e) {
                    if ($(e).is(':checked')) {
                        return ($(e).attr('data-id'));
                    }
                });
                $.ajax({
                    url: '/lead-v3/screen-three',
                    data: { college_id: college_id, '_csrf-frontend': csrf, student_id: student_id, activity_id: activity_id },
                    dataType: 'json',
                    method: 'POST',
                    beforeSend: function () {
                        $('.primaryBtn').prop('disabled', true);
                    },
                    error: function (xhr, err) {
                        $('.primaryBtn').prop('disabled', false)
                        displayErrorsLead('Something went wrong, please try again!')
                    },
                    complete: function () {
                        $('.primaryBtn').prop('disabled', false);
                    },
                    success: function (data) {
                        if ($('select[name="inputLevel"] option:selected').val() == '1') { //bachelors
                            navigateToFormStep(4);
                            $(".tenthClass").css("display", "block");
                            $(".twelveClass").css("display", "block");
                        } else if ($('select[name="inputLevel"] option:selected').val() == '4') { //diploma
                            navigateToFormStep(4);
                            $(".tenthClass").css("display", "block");
                            $(".diplomaClass").css("display", "block");
                        } else if ($('select[name="inputLevel"] option:selected').val() == '2') { //masters
                            navigateToFormStep(4);
                            $(".twelveClass").css("display", "block");
                            $(".gradutaionDetails").css("display", "block");
                        } else {
                            navigateToFormStep(4);
                            $(".gradutaionDetails").css("display", "block");
                            $(".postGradutaionDetails").css("display", "block");
                        }
                        // $(".signupModal").css("overflow", "visible")
                    }
                });
            });

            $("#fourthScreenSubmit").click(function (e) {
                $("body").css("overflowY", "hidden");
                // if ($('.yearError').length !== 0) {
                //     return false;
                // }
                e.preventDefault()
                var form = $("#fourthScreen");
                var student_id = localStorage.getItem('studentId') ?? '';
                var level = $('select[name="inputLevel"] option:selected').val();
                var stream = $('select[name="inputStream"] option:selected').val();
                var entity = gmu.config.entity;

                $.ajax({
                    url: '/lead-v3/screen-four',
                    data: form.serialize() + "&entity=" + entity + "&student_id=" + student_id + "&level=" + level + "&stream=" + stream,
                    dataType: 'json',
                    method: 'POST',
                    beforeSend: function () {
                        $('.primaryBtn').prop('disabled', true);
                    },
                    error: function (xhr, err) {
                        $('.primaryBtn').prop('disabled', false)
                        displayErrorsLead('Something went wrong, please try again!')
                    },
                    complete: function () {
                        $('.primaryBtn').prop('disabled', false);
                    },
                    success: function (data) {
                        if (data.success == true) {
                            if ($("#rank_predictor_lead_value").val() === "1") {
                                fetchRankPredict()
                            }
                            if (localStorage.getItem('reviewReadMoreId')) {
                                localStorage.setItem('loggedIn', "Yes");
                            }

                            document.querySelector('.headerAndForm').style.display = 'none';
                            document.querySelector('.finalScreen').style.display = 'flex';
                            if (($('#durl').length) != 0 || ($('#dynamic_redirection').length) != 0) {
                                if ($("#durl").val() != '') {
                                    dynamicPdfUrlRedirect($("#durl").val());
                                    getScrollPosition()
                                }

                                if ($("#dynamic_redirection").val() != '') {
                                    dynamicPdfUrlRedirect($("#dynamic_redirection").val());
                                    getScrollPosition()
                                }
                            }
                            document.querySelector('body').style.overflowY = 'auto';

                            setTimeout(function () {
                                document.location.reload(true);
                            }, 2000)
                        } else {
                            displayErrorsLead(data.message)
                        }
                    }
                });

            });
            // submit form end

            function disableOtpField() {
                sendOtpButton.style.display = 'none';
                OtpField.style.display = 'none';
                // sendOtpButton.classList.remove('disabledSendOtpButton');
            }
        });
    }
}

//change the passign years based on the previous selected year
// $("body").on("change", ".10thBoardPassingYear", function () {
//     // if ($(".12thBoardPassingYear").val() !== null) {
//     //     return false;
//     // }
//     let defaultOption = new Option("Passing Year", "", true, true);
//     $('.12thBoardPassingYear').append(defaultOption);
//     $(".12thBoardPassingYear option:first").attr("disabled", "true");
//     $("select[name='yearValueTwelve']").attr('style', 'color: #989898!important')
//     $('.12thBoardPassingYear option').remove();
//     var tenthPassingYear = $(this).val();
//     var currentYear = parseInt(tenthPassingYear) + parseInt(2)
//     for (let i = 0; i <= 8; i++) {
//         let twelevePassingYearDropDown = new Option(currentYear + i, currentYear + i)
//         $('.12thBoardPassingYear').append(twelevePassingYearDropDown)
//     }
// });

// $("body").on("change", ".12thBoardPassingYear", function () {
//     // if ($(".graduationPassingYear").val() !== null) {
//     //     return false;
//     // }
//     let defaultOption = new Option("Passing Year", "", true, true);
//     $('.graduationPassingYear').append(defaultOption);
//     $(".graduationPassingYear option:first").attr("disabled", "true");
//     $("select[name='yearValueGraduation']").attr('style', 'color: #989898!important')
//     $('.graduationPassingYear option').remove();
//     var twelevePassingYear = $(this).val();
//     var currentYear = parseInt(twelevePassingYear) + parseInt(2)
//     for (let i = 0; i <= 8; i++) {
//         let graduationPassingYearDropDown = new Option(currentYear + i, currentYear + i)
//         $('.graduationPassingYear').append(graduationPassingYearDropDown)
//     }
// });

// $("body").on("change", ".graduationPassingYear", function () {
//     // if ($(".postGraduationPassingYear").val() !== null) {
//     //     return false;
//     // }
//     let defaultOption = new Option("Passing Year", "", true, true);
//     $('.postGraduationPassingYear').append(defaultOption);
//     $(".postGraduationPassingYear option:first").attr("disabled", "true");
//     $("select[name='yearValuePostGraduation']").attr('style', 'color: #989898!important')
//     $('.postGraduationPassingYear option').remove();
//     var twelevePassingYear = $(this).val();
//     var currentYear = parseInt(twelevePassingYear) + parseInt(2)
//     for (let i = 0; i <= 8; i++) {
//         let postGraduationPassingYearDropDown = new Option(currentYear + i, currentYear + i)
//         $('.postGraduationPassingYear').append(postGraduationPassingYearDropDown)
//     }
// });
function showStoredCheckedColleges() {
    var storedDataIds = JSON.parse(localStorage.getItem('checkedDataIds')) || [];

    storedDataIds.forEach(function (dataId) {
        // Find the checkboxes with the corresponding data-ids and check them
        $('.leadFormRecommendationScreen[data-id="' + dataId + '"]').prop('checked', true);
    });
}

function webEngageEvent(eventName, response) {
    webengage.user.login(response.mobile);
    webengage.user.setAttribute('we_email', response.email + '@gmail.com');
    webengage.user.setAttribute('we_phone', response.mobile);
    webengage.user.setAttribute('we_first_name', response.name);
    webengage.user.setAttribute('we_whatsapp_opt_in', true); //WhatsApp
    webengage.user.setAttribute('Source_Url', response.source_url ? response.source_url : '');
    webengage.user.setAttribute('Source', response.source ? response.source : '');
    webengage.user.setAttribute('User_Status', response.user_status ? response.user_status : '');
    webengage.user.setAttribute('User_Type', response.user_type ? response.user_type : '');
    var eventData = [];
    eventData['name'] = response.name;
    if (gmu.config.entity == 'college') {
        eventData['is_sponsor_college'] = 'No';
        if (response.is_sponsorCollege) {
            eventData['is_sponsor_college'] = 'Yes';
        }
        eventData['state_name'] = response.state_name;
        eventData['state_id'] = response.state_id;
    }
    eventData['entity'] = gmu.config.entity;
    eventData['entity_name'] = gmu.config.entity_name;
    eventData['is_opt_verified'] = 'Yes';
    eventData['device'] = 'web/wap';
    eventData['email'] = response.email ? response.email : '';
    eventData['stream'] = response.stream ? response.stream : '';
    eventData['level'] = response.level ? response.level : '';
    eventData['phone'] = response.mobile;
    eventData['highest_qualification'] = response.qualification ? parseInt(response.qualification) : '';
    eventData['specialization'] = response.specialization ? parseInt(response.specialization) : '';
    eventData['page_url'] = gmu.config.page_url;
    eventData['previous_url'] = gmu.config.previous_url;
    eventData['city_current_location'] = response.current_location ? parseInt(response.current_location) : '';
    eventData['interested_course'] = response.interested_course ? parseInt(response.interested_course) : '';
    eventData['cta_location'] = response.cta_location ? response.cta_location : '';
    eventData['utm_source'] = gmu.config.utm_source;
    eventData['utm_campaign'] = gmu.config.utm_campaign;
    eventData['utm_medium'] = gmu.config.utm_medium;
    var eventDataJson = Object.assign({}, eventData);

    webengage.track(eventName, eventDataJson);
}

webengage.track("user-tracking", {
    "entity": gmu.config.entity,
    "entity_name": gmu.config.entity_name,
    "page_url": gmu.config.page_url,
    "entity_id": gmu.config.entity_id
});


function getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');

    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1);

        if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
        }
    }
    return "";
}

function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    var expires = "expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + "; " + expires;
}

function addValidationToWholeNumberExamSection() {
    $('.examScoreBox').keypress(function (e) {
        if (this.value.indexOf(".") === -1) {
            if (this.value.length === 5) {
                e.preventDefault();
                return false;
            }
        }
        else {
            return true;
        }
    });
}

function getExamAcademicDataV3(data) {
    if (data.examLead.length == 0) {
        $(".examScoreContainer, .scheduledExamText, .scheduledExamContainer").css("display", "none");
        $(".scheduledExamRadio[value='no']").prop('checked', true);
    } else {
        $(".scoreInputContainer, .scheduledExamText, .scheduledExamContainer").css("display", "block");
        $(".scheduledExamRadio[value='yes']").prop('checked', true);
    }
    $(".ajaxExamLead").html(data.examLead);
    if (data.userInfo.educationBudgetId !== "") {
        $(".eduBudgetContainer .select2-selection__rendered").text(data.userInfo.educationBudgetValue).trigger('change');
        $("select[name='educationBudget'] option[value=" + data.userInfo.educationBudgetId + "]").attr("selected", "selected");
    }
    if (data.userInfo !== "") {
        preFetchAcademicDetailsV3(data.userInfo.data);
    }
}

function preFetchAcademicUserDetailV3(academics) {
    if (academics !== undefined) {
        $.each(academics, function (index, values) {
            $.each(values, function (key, value) {
                if (index == 0 && values.entity_id !== "") {
                    var $option = $("<option selected></option>").val(values.entity_id).text(values.entity_name);
                    $("select[name=boardEntityTenth]").append($option).trigger('change');
                    $("select[name=yearValueTenth]").val(values.passing_year).trigger('change');
                    $("input[name='studentMarkTenth']").val(values.marks)
                    $("input[name='studentMarkTenth'], select[name='yearValueTenth']").attr('style', 'color: #282828!important')
                }
                if (index == 1 && values.entity_id !== "") {
                    var $option = $("<option selected></option>").val(values.entity_id).text(values.entity_name);
                    $("select[name=boardEntityTwelve]").append($option).trigger('change');
                    $("select[name=yearValueTwelve]").val(values.passing_year).trigger('change');
                    $("input[name='studentmarktweleve']").val(values.marks)
                    $("select[name=tweleveSpecialization]").val(values.qualification_domain_id).trigger('change');
                    $("input[name='studentmarktweleve'], select[name='yearValueTwelve']").attr('style', 'color: #282828!important')
                }
                if (index == 2 && values.entity_id !== "") {
                    var $option = $("<option selected></option>").val(values.entity_id).text(values.entity_name);
                    $("select[name=boardEntityDiploma]").append($option).trigger('change');
                    $("select[name=yearValueDiploma]").val(values.passing_year).trigger('change');
                    $("input[name='studentMarkDiploma']").val(values.marks)
                    $("input[name='studentMarkDiploma'], select[name='yearValueDiploma']").attr('style', 'color: #282828!important')
                }
                if (index == 3 && values.entity_id !== "") {
                    var $option = $("<option selected></option>").val(values.entity_id).text(values.entity_name);
                    $("select[name=graduateCollege]").append($option).trigger('change');
                    $("select[name=yearValueGraduation]").val(values.passing_year).trigger('change');
                    $("input[name='graduationMarks']").val(values.marks)
                    var $option = $("<option selected></option>").val(values.qualification_domain_id).text(values.coursName);
                    $("select[name=graduationCourse]").append($option).trigger('change');
                    $("input[name='graduationMarks'], select[name='yearValueGraduation']").attr('style', 'color: #282828!important')
                }
                if (index == 4 && values.entity_id !== "") {
                    var $option = $("<option selected></option>").val(values.entity_id).text(values.entity_name);
                    $("select[name=postGraduateCollege]").append($option).trigger('change');
                    $("select[name=yearValuePostGraduation]").val(values.passing_year).trigger('change');
                    $("input[name='postGraduationMarks']").val(values.marks)
                    var $option = $("<option selected></option>").val(values.qualification_domain_id).text(values.coursName);
                    $("select[name=postGraduationCourse]").append($option).trigger('change');
                    $("input[name='postGraduationMarks'], select[name='yearValuePostGraduation']").attr('style', 'color: #282828!important')
                }
            });
        });
    }
}

function otpTimerLead(remaining) {
    var m = Math.floor(remaining / 60);
    var s = remaining % 60;

    m = m < 10 ? '0' + m : m;
    s = s < 10 ? '0' + s : s;

    if (document.querySelector('.otpTimer') !== null) {
        document.querySelector('.otpTimer').innerHTML = m + ':' + s;
    }
    remaining -= 1;

    if (remaining >= 0) {
        var timeOutId = setTimeout(function () {
            otpTimerLead(remaining);
        }, 1000);
        if (document.querySelector(".mobileNumberField").value.length < 10) {
            clearTimeout(timeOutId);
        }
        return;
    } else {
        // sendOtpButton.classList.remove('disabledSendOtpButton');
        if ($(".spriteIcon").hasClass("tickIcon") == false) {
            document.querySelector('.sendOtpButton').textContent = 'Resend';
            $(".otpTimer").css("display", "none");
            $(".sendOtpButton").css("display", "block");
        }
    }
}

function displayErrorsLead(errors = undefined) {
    $('.validationError').remove();
    $('.errorMsg').html('');
    $('select, input').removeClass('errorInputField');
    if (typeof errors === 'object') {
        for (const [key, value] of Object.entries(errors)) {
            $('select[name="' + key + '"], input[name="' + key + '"]').parent().append('<p class="error validationError">' + value + '</p>');
            $('select[name="' + key + '"], input[name="' + key + '"]').addClass('errorInputField');
            // if ($('.' + key).length !== 0) {
            //     $('.' + key + ' .select2-selection').addClass('errorInputField');
            // }
        }
    }

    if (typeof errors === 'string') {
        $('.errorMsg').html(errors);
    }
}

function preFetchAcademicDetailsV3(data) {
    if (data !== undefined) {
        $.each(data.exam, function (i, ob) {
            $.each(ob, function (ind, obj) {
                if (ind == 'marks' && obj !== null) {
                    $(".mark_" + i).val(obj);
                }
                if (ind == 'exam_appearing_date' && obj !== null) {
                    $(".scheduledExamRadio[value='booked']").prop('checked', true);
                    document.querySelector('.scoreInputContainer').style.display = 'none';
                    document.querySelector('.datepickerContainer').style.display = 'block';
                    $(".signupModal .examScoreContainer .scoreExam").css("display", "none")
                    $(".signupModal .examScoreContainer .dateExam").css("display", "flex")
                    date = obj.split(' ')[0];
                    $(".date_" + i).val(date)
                }
            });
        });
    }
    preFetchAcademicUserDetailV3(data.academics);
}

function inputErrorClearonFocusLeadForm() {
    $(".form-group").focusin(function () {
        $(this).find('.help-block').html('')
        $(this).find('.validationError').html('')
    });
}

//otp validation
function validateOtpInput(input) {
    // Remove any non-numeric characters from the input value
    input.value = input.value.replace(/[^0-9]/g, '');

    if (input.value.length > 4) {
        input.value = input.value.slice(0, 4);
    }
}

let isModalOpenNews = false;
function preventDefaultScrollNews(event) {
    if (isModalOpenNews) {
        document.body.style.height = '100vh';
        document.body.style.overflowY = 'hidden';
        document.body.style.position = 'fixed';
    }
}

let isModalOpenLead = false;
function preventDefaultScrollLead(event) {
    if (isModalOpenLead) {
        document.body.style.height = '100vh';
        document.body.style.overflowY = 'hidden';
        if (/iPhone|iPod|iPad/.test(navigator.userAgent)) {
            document.body.style.position = 'fixed';
        }
        if (event) {
            event.preventDefault();
        }
    }

}
//set leadform pop up position
function setScrollPosition() {
    localStorage.setItem('scrollPosition', window.pageYOffset);
}

//remove popup position
function removeScrollPosition() {
    if (localStorage.getItem('scrollPosition')) {
        localStorage.removeItem('scrollPosition');
    }
}

//local storage of pageOffset
function getScrollPosition() {
    if (localStorage.getItem('scrollPosition')) {
        window.scrollTo(0, localStorage.getItem('scrollPosition'));
    }
    removeScrollPosition();
}
