// $(".faq_answer").hide();
// $(".faq_question").click(function () {
//   $(this).toggleClass("downAngle");
//   $(this)
//     .next(".faq_answer")
//     .slideToggle("slow")
//     .siblings(".faq_answer:visible")
//     .slideUp("slow");
// });

// var faq_answer = document.querySelector(".faq_answer");
// var faq_question = document.querySelector(".faq_question");
// faq_question.addEventListener('click', showFunction);

// function showFunction(event){
//   console.log(event);
// }

    var acc = document.getElementsByClassName("faq_question");
    var i;
    for (i = 0; i < acc.length; i++) {
      acc[i].addEventListener("click", function() {
        const active = document.querySelector(".faq_question.downAngle");
        if (active) {
          active.classList.remove('downAngle'); // remove active class from accordions

        }
        if (active !== this) {
          this.classList.toggle("downAngle");
          if(this.classList.contains('downAngle')) {
            this.nextElementSibling.style.display = 'block';
          }else{
            this.nextElementSibling.style.display = 'none';
          }
        }
      });
    }
