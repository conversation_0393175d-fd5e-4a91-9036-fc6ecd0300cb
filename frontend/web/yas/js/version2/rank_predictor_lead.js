var mobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

if (window.location.href.includes('/user-profile') == false) {
    inputErrorClearonFocusLeadForm();

    $('.inputMobileContainerRank select').select2({
        dropdownParent: $('.mobileContainerCodeDiv'),
        placeholder: "+91",
        minimumResultsForSearch: -1
    });

    $('.inputCityContainerRank select').select2({
        placeholder: 'City You Live In',
        ajax: {
            url: "/ajax/lead-cities",
            dataType: "json",
            type: "GET",
            data: function (params) {
                var queryParameters = {
                    term: params.term
                }
                return queryParameters;
            },
            processResults: function (data) {
                var states = data.states || [];
                var results = states.map(function (state) {
                    return {
                        text: state.state_name,
                        children: state.cities.map(function (city) {
                            return {
                                id: city.id,
                                text: city.text
                            };
                        })
                    };
                });

                return { results: results };
            },
        },
    });

    $('.inputStreamContainerRank select').select2({
        placeholder: "Stream Interested",
        name: 'inputStream',
        ajax: {
            url: "/ajax/lead-stream",
            dataType: "json",
            type: "GET",
            data: function (params) {
                var entity_id = gmu.config.entity_id;
                var queryParameters = {
                    term: params.term,
                    entity: gmu.config.entity,
                    entity_id: entity_id,
                }
                return queryParameters;
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (item) {
                        return {
                            text: item.text,
                            id: item.id,
                        };
                    }),
                };
            },
        },
    });

    $('.inputStreamContainerRank select').on('change', function (e) {
        toggleLevelSelect(true);
    });

    $('.inputLevelContainerRank select').select2({
        placeholder: "Level Interested",
        name: 'inputLevel',
        ajax: {
            url: "/ajax/lead-level",
            dataType: "json",
            type: "GET",
            data: function (params) {
                var id = gmu.config.entity_id;
                var queryParameters = {
                    term: params.term,
                    entity: gmu.config.entity,
                    stream_id: $("#interested_stream_lead_rank").val(), // selected stream ID
                    entity_id: id,
                    page_name: gmu.config.pageName ?? ''
                }
                return queryParameters;
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (item) {
                        return {
                            text: item.text,
                            id: item.id,
                        };
                    }),
                };
            },
        },
    });

    //CTA on click open the lead form
    $('body').on('click', '.js-open-rank-lead-form', function (e) {
        // isModalOpenLead = true;
        // document.addEventListener('touchmove', preventDefaultScrollLead(event), { passive: false });
        e.preventDefault();
        setScrollPosition();

        if (typeof autoPopUpTimeOut !== 'undefined' && autoPopUpTimeOut !== null) {
            clearTimeout(autoPopUpTimeOut);
        }

        $('.textDivHeading').html("");
        $('.textDivSubHeading').html("");

        document.querySelector('.predict__rank__form__section').style.display = "block";

        document.querySelector('#cta_location').value = "exam_" + gmu.config.pageName + "_mtf_lead";
        document.querySelector('#cta_text').value = gmu.config.entity_subtype;
        document.querySelector('#entity_subtype').value = gmu.config.entity_subtype;
        document.querySelector('#entity_type').value = "exam";
        document.querySelector('#leadform-entity').value = "exam";
        document.querySelector('#leadform-entity_id').value = gmu.config.entity_id;
        document.querySelector("#durl").value = '';
        document.querySelector("#dynamic_redirection").value = '';
        document.querySelector("#activity_id").value = sessionStorage.getItem('activity_id') ?? '';

        $('.textDivHeading').append('Please fill the below details to receive a detailed report of your rank');
    });

    //lead auto fetch starts
    //exam page lead auto capture
    $("body").on('click', '.examLeadValue', function () {
        var examId = gmu.config.pageName == 'exam-category' ? $(this).closest('.lead-cta').attr('data-entityid') : $(this).attr("data-entityid");
        var location = $(this).closest('.lead-cta').attr('data-location');
        var description = $(this).closest('.lead-cta').attr('data-description');
        var image = $(this).closest('.lead-cta').attr('data-image');

        //data pushed to lead attributes filter page
        if (gmu.config.pageName == 'exam-category') {
            $("#cta_location").val(location);
            $("#leadform-entity_id").val(examId);
            $(".subHeadingText").text(description);
            $("#leadform-image").attr("src", image);
        }
        if (examId == '') {
            return '';
        }
        $.ajax({
            type: 'POST',
            url: '/ajax/exam-stream-level',
            data: { exam_id: examId },
            dataType: "json",
            success: function (response) {
                if (response.success == true) {
                    toggleStreamField(response.stream_id, response.level, response.specialization_id);
                }
                $("#degree_id").val(response.level);
                if (response.course_id !== "0") {
                    $("#course_id").val(response.course_id);
                }
            }
        });
    });
    //lead auto fetch end

    //lead validations starts
    //get field info
    const nameInputLead = document.querySelector('#formNameRank');
    const emailInputLead = document.querySelector('#formEmailRank');
    const mobileInputLead = document.querySelector('#formMobileRank');
    const streamSelectLead = document.querySelector('#interested_stream_lead_rank');
    const levelSelectLead = document.querySelector('#interested_level_lead_rank');
    const citySelectLead = document.querySelector('#leadform-interested_location_rank');
    const cityInputIp = document.querySelector('#current_city_ip_rank');
    const submitButtonLead = document.querySelector('#firstScreenSubmitRank');

    //email validation
    if (document.querySelector("#formEmailRank") !== null) {
        document.querySelector("#formEmailRank").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
            if (e.target.value.match(/@/g)) {
                if ((e.target.value.match(/\./g) || []).length > 2) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            } else {
                if ((e.target.value.match(/\./g) || []).length > 1) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            }

            var email = $('#formEmailRank').val();
            let strLst = email.slice(email.indexOf("@") + 1, email.length)

            if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
                $(".errorMsgEmailRank").html("Email is not a valid email address.");
                $('.mobileNumberFieldRank').prop("disabled", true);
                $('.validEmailIcon').css("display", "none");
            } else {
                if (nameInputLead.value.length > 0 && streamSelectLead.value.length > 0 && levelSelectLead.value.length > 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0)) {
                    $('.mobileNumberFieldRank').prop("disabled", false);
                }

                $('.validEmailIcon').attr('style', 'display: block !important');
                $('.errorMsgEmailRank').html('');
            }
        })
    }

    //name validation
    $(".formNameRank").on("input", function (e) {
        var inputValue = $(this).val();
        var regex = /^[A-Za-z ]+$/;

        if (!regex.test(inputValue)) {
            var nameValue = inputValue.replace(/[^A-Za-z ]/g, '');
            $(this).val(nameValue);
        }
    });

    //lead form mobile validation
    if (document.querySelector("#formMobileRank") !== null) {
        document.querySelector("#formMobileRank").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '')
            console.log(e.target.value.length);

            if (e.target.value.length == 10) {
                $(".inputMobileContainerRank .validationError").html("");
                $('.inputMobileContainerRank .errorInputField').attr('style', 'border: solid 1px #d8d8d8 !important');
            }
        });

    }
    //lead validations ends

    //Function to check if all fields are filled for submit button functionality || screen one
    $(".streamClassRank, .levelClassRank, .nameClassRank, .emailClassRank, .cityClassRank, .mobileNumberFieldRank").bind("change keyup", function (event) {
        if (gmu.config.isLoggedIn == false) {
            if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainerRank .errorMsg").html() == '') && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0) {
                submitButtonLead.removeAttribute('disabled');
            } else {
                submitButtonLead.setAttribute('disabled', true);
            }
        }
    });

    //Function to check if all fields are filled for mobile button functionality
    $(".streamClassRank, .levelClassRank, .nameClassRank, .emailClassRank, .cityClassRank").bind("change keyup", function (event) {
        mobileFieldFunctionality();
    });

    window.onbeforeunload = function () {
        localStorage.removeItem('leadId');
        localStorage.removeItem('sponsorCollege');
        localStorage.removeItem('student_college_shortlist_id');
        localStorage.removeItem('activityId');
        localStorage.removeItem('phone');
        localStorage.removeItem('verified');
        localStorage.removeItem('onClickUrl');
        localStorage.removeItem('collegeId');
    };

    //submit forms starts
    $("#firstScreenSubmitRank").click(function (e) {
        e.preventDefault();
        var student_activity_parent_id = sessionStorage.getItem('activity_id') ?? '';
        var form = $('#firstScreenRank');
        var lead_id = localStorage.getItem('leadId') ?? '';
        var student_id = localStorage.getItem('studentId') ?? '';
        var preference_id = localStorage.getItem('preferenceId') ?? '';
        var activity_id = localStorage.getItem('activityId') ?? '';
        var student_college_shortlist_id = localStorage.getItem('student_college_shortlist_id') ?? '';
        var product_mapping_entity = gmu.config.product_mapping_entity == "false" ? null : gmu.config.product_mapping_entity;
        var product_mapping_entity_id = gmu.config.product_mapping_entity_id ?? '';
        var verified = localStorage.getItem('verified') ?? '';

        $.ajax({
            url: '/lead-v4/screen-one',
            data: form.serialize() + "&lead_id=" + lead_id + "&verified=" + verified + "&product_mapping_entity=" + product_mapping_entity + "&product_mapping_entity_id=" + product_mapping_entity_id + "&student_id=" + student_id + "&preference_id=" + preference_id + "&activity_id=" + activity_id + "&student_college_shortlist_id=" + student_college_shortlist_id + "&student_activity_parent_id=" + student_activity_parent_id,
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                $('.primaryBtn').prop('disabled', false)
                displayErrorsLead('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (data) {
                if (data.success == true) {
                    displayErrorsLead();
                    $("body").css("overflowY", "hidden");

                    localStorage.setItem('activityId', data.student_activity_id);
                    localStorage.setItem('session_activity_id', data.student_activity_id);
                    localStorage.setItem('preferenceId', data.student_prefrence_id);
                    localStorage.setItem('leadId', data.lead_id);
                    localStorage.setItem('studentId', data.student_id);
                    localStorage.setItem('student_college_shortlist_id', data.student_college_shortlist_id);

                    document.querySelector('.predict__rank__form__section').style.display = "none";

                    fetchRankPredict($('#firstScreenSubmitRank').attr('rankType'));
                    activateStudentSession();
                } else {
                    displayErrorsLead(data.message)
                }
            }
        });
    });
    // submit form end

    //lead form functions starts

    function submitButtonFunctionality() {
        if (localStorage.getItem("phone") !== $("input[name='hiddenNumber']").val()) {
            if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0 && (otpInputLead.value.length == 4 && $(".inputOTPContainer .validationError").html() == '')) {
                submitButtonLead.removeAttribute('disabled');
            } else {
                submitButtonLead.setAttribute('disabled', true);
            }
        } else {
            if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && mobileInputLead.value.length !== 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0) {
                submitButtonLead.removeAttribute('disabled');
            } else {
                submitButtonLead.setAttribute('disabled', true);
            }
        }

        if ($('.admissionRadio').is(':checked') && specializationInputLead.value.length !== 0) {
            submitButtonLeadSecondScreen.removeAttribute('disabled');
        } else {
            submitButtonLeadSecondScreen.setAttribute('disabled', true);
        }
    }

    function mobileFieldFunctionality() {
        if (nameInputLead.value.length > 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainer .errorMsg").html() == '') && streamSelectLead.value.length > 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && levelSelectLead.value.length > 0) {
            $('.mobileNumberFieldRank').prop("disabled", false);
        } else {
            $('.mobileNumberFieldRank').prop("disabled", true);
        }
    }

    function EnableSubmitButtonForloggedInUsers() {
        if (gmu.config.isLoggedIn == true) {
            submitButtonFunctionality();
            mobileFieldFunctionality();
        }
    }

    // Function to enable/disable the select dropdown
    function toggleLevelSelect(enabled) {
        var $levelSelect = $('#interested_level_lead_rank'); // select dropdown
        if (enabled) {
            $levelSelect.prop('disabled', false);
        } else {
            $levelSelect.prop('disabled', true);
        }
    }

    function toggleStreamField(stream_id = '', level = '', specialization_id = '') {
        //22 -> others
        let highest_qualification_degree_id = ['9', '10', '11'];

        if (stream_id == '' || stream_id == '22' || $.inArray(level, highest_qualification_degree_id) !== -1) {
            $(".inputStreamContainerRank").show();
        } else {
            $(".inputStreamContainerRank").hide();
            $('.inputStreamContainerRank select').append($('<option>', {
                value: stream_id,
                selected: true
            }));
        }
        toggleLevel(level);
    }

    function toggleLevel(level = '') {
        //12 -> others
        if (level !== '' && level !== '12') {
            $(".inputLevelContainerRank").hide();
            $('.inputLevelContainerRank select').append($('<option>', {
                value: level,
                selected: true,
            })).prop('disabled', false);
        } else {
            $(".inputLevelContainerRank").show();
            $("#interested_level_lead").prop("disabled", false);
        }
    }

    function inputErrorClearonFocusLeadForm() {
        $(".form-group").focusin(function () {
            $(this).find('.help-block').html('')
            $(this).find('.validationError').html('')
        });
    }

    function displayErrorsLead(errors = undefined) {
        $('.validationError').remove();
        $('.errorMsg').html('');
        $('select, input').removeClass('errorInputField');
        if (typeof errors === 'object') {
            for (const [key, value] of Object.entries(errors)) {
                $('select[name="' + key + '"], input[name="' + key + '"]').parent().append('<p class="error validationError">' + value + '</p>');
                $('select[name="' + key + '"], input[name="' + key + '"]').addClass('errorInputField');
            }
        }
    
        if (typeof errors === 'string') {
            $('.errorMsg').html(errors);
        }
    }

    function activateStudentSession () {
        var student_id = localStorage.getItem('studentId') ?? '';
        var activity_id = localStorage.getItem('activityId') ?? '';
        var csrf = $("input[name='_csrf-frontend']").val();
        $.ajax({
            url: '/lead-v4/student-session-activate',
            data: { student_id: student_id, activity_id: activity_id },
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                $('.primaryBtn').prop('disabled', false)
                displayErrorsLead('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (data) {
                if (data.success == true) {
                    setCookie('gmu_leadform', 1, 10);
                    location.reload(true);
                } else {
                    document.querySelector('#lead-form-js-new').style.display = "none";
                }
            }
        });
    }
    //lead form functions ends
}