jQuery(document).ready(function ($) {
  //detail page js starts
  $(".cdev").circlos();
});

function isMobileRequest() {
  var userAgent = navigator.userAgent.toLowerCase(); // Get the user-agent string and convert to lowercase

  // Regular expressions for detecting mobile devices
  var mobileRegex =
    /(android|bb\d+|meego|mobile|iphone|ipod|blackberry|symbian|palm|windows ce|bada|opera mini|iemobile|windows phone|kindle)/i;

  // Check if the user-agent matches the mobile regex
  if (mobileRegex.test(userAgent)) {
    return true; // It's a mobile device
  } else {
    return false; // It's not a mobile device
  }
}
var SITE_URL = 'https://getmyuni.com/';
var mobile_request = isMobileRequest() ? 1 : 0;


function logout() {
  $.post('/site/logout', function (data) {
    $('#logout-form').html(data)
  });

  $('#logoutForm').submit();

}

$('.closeLeadForm').click(function () {
  $.get('/site/logged-in', function (data) {
    console.log(data);
    if (data.data.name) {
      var userName = truncateName(data.data.name, 20);
      $("#registerNew").css("display", "none");
      $('#loggedInUser').replaceWith('<li class="sa_dropdown registerNew"><a class="registerWelcomeMsg">Hi ' + userName + '<span class="caretWhite spriteIcon"></span></a><ul class="login-options"><span class="spriteIcon whiteCaretIcon"></span><a href="/user-profile"><li style="display: block;"><span class="spriteIcon myProfileIcon"></span>My Profile</li></a><a href="/site/logout" title="Logout" data-method="post"><li><span class="spriteIcon logoutIcon"></span>Logout</li></a></ul></li>');
      $('.user-text-mobile').html('Hi ' + userName + '');
      $('#mobileRegisterNew').hide();
      $('#mobileLogout').html('<div class="hamburgerMenuOptions"><a href="/user-profile" class="myProfileOption"><div style="display: block"><span class="spriteIcon myProfileIcon"></span>My Profile</div></a><a  onclick="javascript:logout()" title="Logout" class="logOutOption"><div><span class="spriteIcon logoutIcon"></span>Log Out</div></a></div>');
      gmu.config.isLoggedIn = true;
    }
  });
});
// slide_menu start
$('.nav_open').click(function () {
  $(".slider-menu").css({
    "width": "80%",
    "display": "block"
  });
  $(".closeMenu").show();
  $(".blur-background").show();
  $(".blur-background").css("width", "100%");

  $('.blur-background').on('scroll mousewheel touchmove', stopScrolling);

  function stopScrolling(e) {
    e.preventDefault();
    e.stopPropagation();
    return false;
  }
});


$("body").on("click", ".sliderText", function () {
  $(this).next().css("width", "80%");
  $(".closeMenu").css("display", "block");

});

$("body").on("click", ".closeMenu", function () {
  $(".slider-menu, .slider-submenu").css("width", "0%");
  $(this).hide();
  $(".blur-background").css("width", "0%");
});

$(".nav_close").click(function () {
  $(".slider-menu").toggle(
    "slide",
    {
      direction: "left",
    },
    100
  );
  $(".blur-background").hide();
});

$(document).mouseup(function (e) {
  var container = $(".slider-menu");
  var blurbg = $(".blur-background");
  var submenu = $(".slider-submenu");

  // if the target of the click isn't the container nor a descendant of the container
  if (!container.is(e.target) && container.has(e.target).length === 0) {
    container.hide();
  }
  if (!container.is(e.target) && container.has(e.target).length === 0) {
    blurbg.hide();
  }
  if (!container.is(e.target) && container.has(e.target).length === 0) {
    submenu.hide();
  }
});
// slide_menu end

// search menu starts
$("#navbar-searchboxNNH").click(function () {
  $(".slider-search").toggle();
});
$(".navbar-searchbox").focus(function () {
  $(".slider-search").toggle();
});
$("#search-button").click(function () {
  $(".slider-search").toggle();
});
$(".search-toggle").click(function () {
  $(".slider-search").toggle();
});
// end of search menu

// on scroll navbar color change
$(document).ready(function () {
  var is_mobile_request = mobile_request;

  //  Get SVG icons
  $.ajax({
    type: "GET",
    url: "https://www.getmyuni.com/assets/images/abroad_college/sa_master_sprite.svg",
    dataType: "text",
    success: function (response) {
      if (response) {
        $(".sa_master_svg").append(
          '<div style="visibility: hidden; position: absolute; height: 0; width: 0;">' +
          response +
          "</div>"
        );
      }
    }
  });

  //    Get SVG icons
  $.ajax({
    type: "GET",
    url: "https://www.getmyuni.com/assets/images/master_sprite.svg?1.0.1",
    dataType: "text",
    success: function (response) {
      if (response) {
        $("body").append(
          '<div style="visibility: hidden; position: absolute; height: 0; width: 0;">' +
          response +
          "</div>"
        );
      }
    },
  });

  $(window).scroll(function () {
    var scroll = $(window).scrollTop();

    if (is_mobile_request == 1) {
      if (scroll > 10) {
        $(".navbar-default").removeClass("navbar-fixed-top");
        $(".heading-banner").css("margin-top", "-100px");

        if ($("#whiteheader .navbar-toggle").css("display") == "block") {
          $("#whiteheader .navbar-header").css(
            "background",
            "linear-gradient(45deg, #4374b9, #ee424f)"
          );
        } else {
          $("#gradient_header").css(
            "background",
            "linear-gradient(45deg, #4374b9, #ee424f)"
          );
        }
      } else {
        if ($("#whiteheader .navbar-toggle").css("display") == "block") {
          $("#whiteheader .navbar-header").css("background", "transparent");
        } else {
          $("#gradient_header").css("background", "transparent");
        }
      }
    } else {
      if (scroll > 100) {
        $("#gradient_header").css(
          "background",
          "linear-gradient(45deg, #4374b9, #ee424f)"
        );
      } else {
        $("#gradient_header").css("background", "transparent");
      }
    }
  });
  $(this).scrollTop(0);
});
// on scroll navbar color change ends

//header_navigation_new script
$(document).ready(function () {
  var id_arr = ["eng", "mng", "mdcl", "sci", "cmr", "rsc", "more"];
  var blog_arr = [
    "Paramedical",
    "Pharmacy",
    "Architecture",
    "Fashion",
    "Design",
    "Hospitality",
    "Law",
    "VeterinaryScience",
    "VocationalCourses",
    "Arts",
    "ComputerApplication",
    "Dental",
    "Education",
  ];
  var url_arr = {
    eng: "engineering-colleges",
    mng: "management-colleges",
    mdcl: "medical-colleges",
    sci: "science-colleges",
    cmr: "commerce-colleges",
    rsc: "",
    more: "",
  };

  $(document.body).on("click", ".navigation_header_dropdown", function () {
    var id = $(this).attr("id");
    if (id == "rsc" || id == "more") {
    } else {
      window.location.href = "https://getmyuni.com/" + url_arr[id];
    }
  });

  $(".navigation_header_dropdown").on("hidden.bs.dropdown", function (e) {
    var id = $(this).attr("id");
    $("#" + id + " span").removeClass("drop_border");
  });

  $(document.body).on("mouseover", ".moremenu", function () {
    $.each(blog_arr, function (i, val) {
      $("#hnn-mainclass #" + val + "-blog").addClass("display_none");
      $("#hnn-mainclass #" + val + " a").removeClass("fontBold");
      $("#hnn-mainclass #" + val).removeClass("hoverbg");
    });

    var id = $(this).attr("id");
    $("#hnn-mainclass #" + id + "-blog").removeClass("display_none");
    $("#hnn-mainclass #" + id + " a").addClass("fontBold");
    $("#hnn-mainclass #" + id).addClass("hoverbg");
  });

  $(document.body).on("mouseover", ".navigation_header_dropdown", function () {
    $.each(id_arr, function (i, val) {
      $("#hnn-mainclass #" + val).removeClass("open");
      $("#hnn-mainclass #" + val + " span").removeClass("drop_border");
    });

    var id = $(this).attr("id");
    $("#hnn-mainclass #" + id).addClass("open");
    $("#hnn-mainclass #" + id + " span").addClass("drop_border");
  });

  $(document.body).on(
    "mouseout",
    "#hnn-mainclass .dropdown-menu,#hnn-mainclass .dropdown",
    function () {
      $.each(id_arr, function (i, val) {
        $("#hnn-mainclass #" + val).removeClass("open");
        $("#hnn-mainclass #" + val + " span").removeClass("drop_border");
      });
    }
  );
});

$('body').on('keyup', '.sa_college_search', function (e) {
  var country = $(this).data('country');
  var className = this.className;
  countrySearches('.' + className, country);
});

function countrySearches(autocompleteElement, country) {
  $(autocompleteElement).autocomplete({
    source: function (query, response) {
      $.ajax({
        type: "GET",
        url: "/ajax/sa-college-search",
        data: { q: query['term'], country: country },
        dataType: "json",
        success: function (data) {
          if (data) {
            response(
              $.map(data, function (item) {
                console.log(item);
                return {
                  label: item.name,
                  value: item.name,
                  link: item.url,
                };
              })
            );
          } else {
            response(null);
          }
        },
      });
    },
    select: function (event, ui) {
      $(".sa_college_search").val(ui.item.label);
      window.location.href = ui.item.link;
    },
  });
};

// Custom Slider Card scripts
$(".btn_carousel_left").click(function () {
  let item = $(
    ".carousel_section .slide_container > .box_container:first-child"
  );
  $(".slide_container").animate(
    {
      scrollLeft:
        "-=" +
        (parseInt(item.css("width")) +
          parseInt(item.css("margin-right")) * 2 +
          4) +
        "px",
    },
    "fast",
    function () {
      $(".btn_carousel_right").show();
    }
  );
});
$(".btn_carousel_right").click(function () {
  let item = $(
    ".carousel_section .slide_container > .box_container:first-child"
  );
  $(".slide_container").animate(
    {
      scrollLeft:
        "+=" +
        (parseInt(item.css("width")) +
          parseInt(item.css("margin-right")) * 2 +
          4) +
        "px",
    },
    "fast",
    slider_right_toggle
  );
});

$(document).ready(function () {
  if (!mobile_request) {
    $(".slide_container").animate(
      {
        scrollLeft: 360,
      },
      1000,
      "linear"
    );
  } else {
    $(".slide_container").animate(
      {
        scrollLeft: 225,
      },
      1000,
      "linear"
    );
  }
  //    Get SVG icons
  $.ajax({
    type: "GET",
    url: SITE_URL + "assets/images/abroad_college/sa_master_sprite.svg",
    dataType: "text",
    success: function (response) {
      if (response) {
        $(".sa_master_svg").append(
          '<div style="visibility: hidden; position: absolute; height: 0; width: 0;">' +
          response +
          "</div>"
        );
      }
    },
  });

  //    Get SVG icons
  $.ajax({
    type: "GET",
    url: SITE_URL + "assets/images/master_sprite.svg?1.0.1",
    dataType: "text",
    success: function (response) {
      if (response) {
        $("body").append(
          '<div style="visibility: hidden; position: absolute; height: 0; width: 0;">' +
          response +
          "</div>"
        );
      }
    },
  });

  //show collegs on landing page college widget
  $("body").on("click", ".btn_show_more", function (e) {
    let page = this.dataset.page;
    let country = this.dataset.country;

    $('#filter-loader').show();
    $('.btn_show_more').removeAttr("data-page");

    $.get('/ajax/show-abroad-colleges', { country_id: country, page: page }, function (response) {
      console.log(response);
      if (response.college == null) {
        $(".btn_show_more").hide();
      }
      setTimeout(removePreLoader, 400)
      $('.slide_container').append(response.college);
      $('.more.card').appendTo('.slide_container');
      $(".btn_show_more").attr("data-page", response.pageNo);
    });
  });
  //   var html = "";
  //   $.ajax({
  //     url: SITE_URL + "AbroadColleges/load_more_colleges/" + country,
  //     type: "POST",
  //     data: { start: start },
  //     success: function (data) {
  //       if (data) {
  //         var ClgData = $.parseJSON(data);
  //         $.each(ClgData, function (index, value) {
  //           html = '<div class="box_container card">';
  //           html += '<div class="img_container">';
  //           html +=
  //             '<span class="background" style="background-image: url(' +
  //             value["banner"] +
  //             ');"></span>';
  //           html +=
  //             '<span class="logo" style="background-image: url(' +
  //             value["logo"] +
  //             ');"></span>';
  //           html += "</div>";
  //           html +=
  //             '<h2 class="title"><a href="' +
  //             SITE_URL +
  //             country +
  //             "/university/" +
  //             value["college_slug"] +
  //             '">' +
  //             value["college_name"] +
  //             "</a></h2>";
  //           html += '<p class="location">' + value["location"] + "</p>";
  //           html += '<div class="course_container">';
  //           html += '<div class="course">';
  //           html += '<p class="title">Undergraduate</p>';
  //           html +=
  //             '<p class="count">' +
  //             (value["undergraduate"] !== null ? value["undergraduate"] : "0") +
  //             " Courses</p>";
  //           html += "</div>";
  //           html += '<div class="course">';
  //           html += '<p class="title">Postgraduate</p>';
  //           html +=
  //             '<p class="count">' +
  //             (value["postgraduate"] !== null ? value["postgraduate"] : "0") +
  //             " Courses</p>";
  //           html += "</div>";
  //           html += '<div class="course">';
  //           html += '<p class="title">Doctorate</p>';
  //           html +=
  //             '<p class="count">' +
  //             (value["doctorate"] !== null ? value["doctorate"] : "0") +
  //             " Courses</p>";
  //           html += "</div>";
  //           html += "</div>";
  //           html += '<button class="btn_apply">Apply Now</button>';
  //           html += "</div>";
  //           $(".slide_container .box_container:last-child").before(html);
  //         });
  //       } else {
  //         $(".btn_show_more").hide();
  //       }
  //     },
  //   });
  //   start += 6;
  //   // let item = $('.slide_container .box_container:first-child');
  //   // let cards = 15;
  //   // for (let i = 0; i < cards; i++) {
  //   //     $('.slide_container .box_container:last-child').before(item.clone());
  //   // }
  //   $(".btn_carousel_right").show();
  // });
});

var rt_id = null;
$(window).resize(function () {
  clearTimeout(rt_id);
  rt_id = setTimeout(slider_right_toggle, 500);
});
function slider_right_toggle() {
  let item = $(".btn_show_more");
  if (
    window.innerWidth >
    item[0].getBoundingClientRect().x + parseInt(item.css("width"))
  ) {
    $(".btn_carousel_right").hide();
  } else {
    $(".btn_carousel_right").show();
  }
}

//faq
$(".faqAnswer").hide();
$(".faqQuestion").click(function () {
  $(this).toggleClass("changeAngle");
  $(this).next().slideToggle("slow")
    .siblings(".faqAnswer:visible").slideUp("slow");
});

//submenu toggele in mobile
$('.sa_menu').click(function () {
  $(this).next('.vertical-container').find('.sa_sub_menu').slideToggle();
});

$('.mob_menu_font').click(function () {
  $('.sa_menu').next('.vertical-container').find('.sa_sub_menu').slideToggle();
  var hrefAttr = $(this).data('href');
  $(".mob_menu_font").removeClass("mob_menu_font_active");
  var $mob_menu_font = $(this);
  var sidemenu = $mob_menu_font.data("sidemenu");
  $("." + sidemenu).addClass("mob_menu_font_active");
  $('html, body').animate({
      scrollTop: $(hrefAttr).offset().top
    }, 'slow');
});
//submenu toggele in mobile script-End

//detail page
$(".left_menu").click(function () {
  var hrefAttr = $(this).data('href');
  $(".left_menu").removeClass("sa_sidemenu_active");
  var $left_menu = $(this);
  var sidemenu = $left_menu.data("sidemenu");
  $("." + sidemenu).addClass("sa_sidemenu_active");
  $('html, body').animate({
    scrollTop: $(hrefAttr).offset().top
  }, 'slow');
});


$(".StreamButtons").click(function () {
  $(".HeadoneBox").css("display", "none");
  $(".HeadtwoButton").css("display", "none");
  $(".showButton").css("display", "none");
  var $StreamButtons = $(this);
  var stream = $StreamButtons.data("stream");
  console.log(stream);
  if (stream == 'all') {
    $('.HeadoneBox').css("display", "block");
    $('.HeadtwoButton').css("display", "inline-block");
  } else {
    $('.' + stream + '_HeadoneBox').css("display", "block");
    $('.' + stream + '_htbutton').css("display", "inline-block");
  }
});

$(".DegreeShowMore").click(function () {
  var $DegreeShowMore = $(this);
  var degree = $DegreeShowMore.data("degree");
  $("." + degree + "ShowAll").css("display", "inline-block");
  $("." + degree + "DegreeShowMore").css("display", "none");
});

$(".showButton").click(function () {
  var $showButton = $(this);
  var headoneSlug = $showButton.data("headone_slug");
  $("." + headoneSlug + "_ShowAll").css("display", "inline-block");
  $("." + headoneSlug + "_ShowMore").css("display", "none");
});

$(".showButtonProgram").click(function () {
  var $ShowMore = $(this);
  var $ShowMore = $ShowMore.data("headone");
  $("." + $ShowMore + "ShowAll").css("display", "inline-block");
  $("." + $ShowMore + "ShowMore").css("display", "none");    
});

$('body').on('click', '.HeadtwoButton', function (event) {
  event.preventDefault();

  var headtwo = $(this);
  var degree = headtwo.data("degree");
  var stream = headtwo.data("stream");
  var course = headtwo.data("headone");
  var specialization = headtwo.data("headtwo");
  var collegeId = headtwo.data("collegeid");

  $.ajax({
    type: 'POST',
    url: '/ajax/sa-specialization-card-data',
    data: { collegeId: collegeId, degree: degree, stream: stream, course: course, specialization: specialization },
    dataType: "json",
    success: function (data) {
      if (data.status) {
        $('body').append(data.html);
        $(".popopup").css("display", "block")
        $("body").css("overflow", "hidden")
      }
    },
    complete: function () {
    }
  });
});

$(document).on('click', '.closeSpecialization', function () {
  $(".popopup").css("display", "none");
  $(".modal-backdrop").css("display", "none");
  $("body").css("overflow", "auto");
});

$(document).on('click', '.specilaizationCardCta', function () {
  $(".popopup").css("z-index", 0);
  $(".modal-backdrop").css("display", "none");
  $("body").css("overflow", "auto");
});

$(".DegreeButton").click(function () {
  $(".spec-content-list-button_white").css("display", "none");
  $(".DegreeButton").removeClass("bws_by_pgrm_active");

  var $DegreeButton = $(this);
  var degree = $DegreeButton.data("degree");
  $("." + degree + "_bbp_active").addClass("bws_by_pgrm_active");
  if (degree == 'all') {
    $(".spec-content-list-button_white").css("display", "inline-block");
    $(".HeadoneBox").css("display", "block");
  } else {
    $("." + degree + "_headone").css("display", "inline-block");
  }
});

$(".headone_click").click(function () {
  $(".HeadoneBox").css("display", "none");
  var $HeadoneButtons = $(this);
  var HeadoneButtons = $HeadoneButtons.data("stream");

  $('.' + HeadoneButtons).css("display", "block");
});

