//landing page starts
function shareOnWhatsApp() {
    const text = "Check out this College Predictor tool to find colleges based on your rank: " + window.location.href;
    const encodedText = encodeURIComponent(text);
    const whatsappUrl = "https://api.whatsapp.com/send?text=" + encodedText;

    const isMobile = /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);

    if (isMobile) {
        // On mobile, open in the same tab
        window.location.href = whatsappUrl;
    } else {
        // On desktop, open in a new tab
        window.open(whatsappUrl, "_blank");
    }
}

function copyLink() {
    const el = document.createElement('textarea');
    el.value = window.location.href;
    document.body.appendChild(el);
    el.select();
    document.execCommand('copy');
    document.body.removeChild(el);

    // Show a notification
    alert("Link copied to clipboard!");
}

let currentFilters = {
    rank: null,
    category: null,
    course: '',
    collegeType: '',
    state: '',
    page: 1
};

// Function to initialize filters
function initializeFilters() {
    const rankInput = $('input[name="rank"]');
    const categoryInput = $('input[name="category"]');

    if (rankInput.length && categoryInput.length) {
        currentFilters.rank = rankInput.val();
        currentFilters.category = categoryInput.val();
    } else {
        currentFilters.rank = getCookie('college_predictor_rank');
        currentFilters.category = getCookie('college_predictor_category');
    }

    currentFilters.course = $('#course-filter').val() || '';
    currentFilters.collegeType = $('#college-type-filter').val() || '';
    currentFilters.state = $('#state-filter').val() || '';

    $('.filter-select').on('change', function () {
        const filterName = $(this).attr('name');
        currentFilters[filterName] = $(this).val();

        // Reset page to 1 when filters change
        currentFilters.page = 1;

        loadCollegesPredictor();
    });

    // Check if we're on the generic predictor page
    const isGenericPredictor = window.location.pathname.includes('/college-predictor');

    // Load colleges if we have rank and category OR we're on the generic predictor page
    if ($('#itemContainer .predictor-box').length === 0 &&
        (currentFilters.rank && currentFilters.category || isGenericPredictor)) {
        loadCollegesPredictor();
    }
}

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}

function loadCollegesPredictor(isLoadMore = false) {
    $('#filter-loader').show();

    if (!isLoadMore) {
        currentFilters.page = 1;
    }

    // Determine the correct URL based on the current page
    let ajaxUrl = window.location.pathname;

    $.ajax({
        url: ajaxUrl,
        type: 'GET',
        data: currentFilters,
        success: function (response) {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(response, 'text/html');

            if (!isLoadMore) {
                // Replace the entire content
                $('#itemContainer').html('');

                // Update the college count in the header
                const totalCountElement = htmlDoc.querySelector('.marks h2');
                if (totalCountElement) {
                    $('.marks h2').text(totalCountElement.textContent);
                }
            }

            const newColleges = htmlDoc.querySelectorAll('#itemContainer .predictor-box');

            if (newColleges.length > 0) {
                newColleges.forEach(function (college) {
                    $('#itemContainer').append(college.outerHTML);
                });

                $('#loadMoreCollegePredictor').text('View More colleges');

                // Show the filter if it was previously hidden
                $("#predictor-filter-container").css("display", "block");

                const hasNext = htmlDoc.querySelector('.load-more') !== null;
                if (!hasNext) {
                    $('.load-more').hide();
                } else {
                    $('.load-more').show();
                }

                // Remove rank and category from session storage after loading colleges
                sessionStorage.removeItem('college_predictor_rank');
                sessionStorage.removeItem('college_predictor_category');
            } else {
                if (!isLoadMore) {
                    $('#itemContainer').html('<div class="no-results" style="text-align: center; padding: 20px;">No colleges found matching your criteria.</div>');
                    // Hide the filter when no results
                    // $("#predictor-filter-container").css("display", "none");
                    // If no results, set count to 0
                    $('.marks h2').text('0');
                }
                $('.load-more').hide();
            }

            $('#filter-loader').hide();
        },
        error: function () {
            $('#loadMoreCollegePredictor').text('View More colleges');
            alert('Error loading colleges. Please try again.');
            $('#filter-loader').hide();
        }
    });
}

$('body').on('click', '#loadMoreCollegePredictor', function () {
    currentFilters.page += 1;
    $('#loadMoreCollegePredictor').text('Loading...');
    loadCollegesPredictor(true);
});
//landing page ends

//lead form
$(document).ready(function () {
    if (gmu.config.entity_type == 'college-predictor') {
        // Initialize filter handlers
        initializeFilters();
    }

    // Check if we need to load college predictor after page refresh
    if (sessionStorage.getItem('load_college_predictor_after_refresh') === 'true') {
        sessionStorage.removeItem('load_college_predictor_after_refresh');

        // Get stored rank and category
        var rank = sessionStorage.getItem('college_predictor_rank');
        var category = sessionStorage.getItem('college_predictor_category');
        var examSlug = gmu.config.entity == 'exam' ? gmu.config.entity_slug : '';

        if (rank && category) {
            // Load the college predictor results
            loadCollegePredictorResults(rank, category, examSlug);
        }
    }

    // Initialize select2 for cutoff category dropdown
    $('.cut-off-category').select2({
        placeholder: "Select Category",
        allowClear: false,
        minimumResultsForSearch: 6,
        ajax: {
            url: "/ajax/get-cutoff-category",
            dataType: "json",
            type: "GET",
            data: function (params) {
                var queryParameters = {
                    term: params.term,
                }
                return queryParameters;
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (item) {
                        return {
                            text: item.text,
                            id: item.id,
                        };
                    }),
                };
            },
        },
    });

    $(".college_predictor_rank, .cut-off-category").bind("change keyup", function () {
        var rank = $("#college_predictor_rank").val();
        var category = $("#cut-off-category").val();

        if (rank !== '' && category !== '') {
            $("#addInputFirstScreenSubmit").prop("disabled", false);
        } else {
            $("#addInputFirstScreenSubmit").prop("disabled", true);
        }
    });

    function enableCollegePredictorLeadSubmitButton() {
        if (nameInputLeadCollegePredictor.value.length !== 0 && (emailInputLeadCollegePredictor.value.length !== 0 && $(".errorMsgEmailCollegePredictor").html() == '') && mobileInputLeadCollegePredictor.value.length == 10) {
            $("#addInputSecondScreenSubmit").prop("disabled", false);
        } else {
            $("#addInputSecondScreenSubmit").prop("disabled", true);
        }
    }

    const nameInputLeadCollegePredictor = document.querySelector('#leadform-name_college_predictor');
    const emailInputLeadCollegePredictor = document.querySelector('#leadform-email_college_predictor');
    const mobileInputLeadCollegePredictor = document.querySelector('#leadform-phone_college_predictor');

    $("#leadform-name_college_predictor, #leadform-email_college_predictor, #leadform-phone_college_predictor").bind("keyup", function () {
        enableCollegePredictorLeadSubmitButton();
    });


    //name validation
    $("#leadform-name_college_predictor").on("input", function (e) {
        var inputValue = $(this).val();
        var regex = /^[A-Za-z ]+$/;

        if (!regex.test(inputValue)) {
            var nameValue = inputValue.replace(/[^A-Za-z ]/g, '');
            $(this).val(nameValue);
        }
    });

    //lead form mobile validation
    if (document.querySelector("#leadform-phone_college_predictor") !== null) {
        document.querySelector("#leadform-phone_college_predictor").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '')
        });
    }

    if (document.querySelector("#leadform-email_college_predictor") !== null) {
        document.querySelector("#leadform-email_college_predictor").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
            if (e.target.value.match(/@/g)) {
                if ((e.target.value.match(/\./g) || []).length > 2) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            } else {
                if ((e.target.value.match(/\./g) || []).length > 1) {
                    e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                }
            }

            var email = $('#leadform-email_college_predictor').val();
            let strLst = email.slice(email.indexOf("@") + 1, email.length)

            if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
                $(".errorMsgEmailCollegePredictor").html("Email is not a valid email address.");
            } else {
                $('.errorMsgEmailCollegePredictor').html('');
            }
        })
    }


    $("#addInputFirstScreenSubmit").click(function (e) {
        e.preventDefault();

        var rank = $("#college_predictor_rank").val();
        var category = $("#cut-off-category").val();

        $(".addInputFirstScreenClass").hide();

        // Set hidden inputs in second form
        $("#college_predictor_rank_value").val(rank);
        $("#college_predictor_rank_category").val(category);

        // Show the second form
        $(".addInputSecondScreenClass").show();

        if (gmu.config.isLoggedIn == true) {
            enableCollegePredictorLeadSubmitButton()
        }
    });

    function setCookie(cname, cvalue, exdays) {
        var cookieString = cname + "=" + encodeURIComponent(cvalue) + "; path=/";
        if (exdays) {
            var d = new Date();
            d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
            cookieString += "; expires=" + d.toUTCString();
        }
        document.cookie = cookieString;
    }

    $("#addInputSecondScreenSubmit").click(function (e) {
        e.preventDefault();
        var student_activity_parent_id = sessionStorage.getItem('activity_id') ?? '';
        var form = $('#addInputSecondScreen');
        var lead_id = localStorage.getItem('leadId') ?? '';
        var student_id = localStorage.getItem('studentId') ?? '';
        var preference_id = localStorage.getItem('preferenceId') ?? '';
        var activity_id = localStorage.getItem('activityId') ?? '';
        var product_mapping_entity = gmu.config.product_mapping_entity == "false" ? null : gmu.config.product_mapping_entity;
        var product_mapping_entity_id = gmu.config.product_mapping_entity_id ?? '';
        var verified = localStorage.getItem('verified') ?? '';

        // Store form data in session storage to use after page refresh
        var rank = $("#college_predictor_rank_value").val();
        var category = $("#college_predictor_rank_category").val();
        sessionStorage.setItem('college_predictor_rank', rank);
        sessionStorage.setItem('college_predictor_category', category);
        setCookie("college_predictor_rank", rank);
        setCookie("college_predictor_category", category);

        $.ajax({
            url: '/lead-v4/screen-one',
            data: form.serialize() + "&lead_id=" + lead_id + "&verified=" + verified + "&product_mapping_entity=" + product_mapping_entity + "&product_mapping_entity_id=" + product_mapping_entity_id + "&student_id=" + student_id + "&preference_id=" + preference_id + "&activity_id=" + activity_id + "&student_activity_parent_id=" + student_activity_parent_id,
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function () {
                $('.primaryBtn').prop('disabled', false)
                displayErrorsLead('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (data) {
                localStorage.setItem('activityId', data.student_activity_id);
                localStorage.setItem('studentId', data.student_id);

                // Set a flag to indicate we need to load college predictor after refresh
                sessionStorage.setItem('load_college_predictor_after_refresh', 'true');

                // Check if user is logged in
                if (!gmu.config.isLoggedIn) {
                    activateStudentSessionPredictor(true);
                } else {
                    // User is already logged in, proceed with loading college predictor
                    currentFilters.rank = rank;
                    currentFilters.category = category;
                    examSlug = gmu.config.entity == 'exam' ? gmu.config.entity_slug : '';

                    if (data.numberChange == 1 || data.nameChange == 1) {
                        activateStudentSessionPredictor(true);
                    } else {
                        loadCollegePredictorResults(rank, category, examSlug);
                    }
                }
            }
        });
    });

    // Function to load college predictor results via AJAX
    function loadCollegePredictorResults(rank, category, examSlug) {

        // Show loading indicator
        $("#college-predictor-results-container").html('<div class="loading-indicator" style="text-align: center; padding: 20px;">Loading college predictor results...</div>');
        $("#college-predictor-results-container").show();

        // Scroll to results container
        $('html, body').animate({
            scrollTop: $("#college-predictor-results-container").offset().top - 100
        }, 500);

        // Make AJAX request to get college predictor data
        $.ajax({
            url: '/college-predictor/ajax-predictor',
            data: {
                rank: rank,
                category: category,
                examSlug: examSlug
            },
            dataType: 'json',
            method: 'GET',
            success: function (response) {
                if (response && response.success) {
                    // Hide the forms
                    $(".addInputFirstScreenClass").hide();
                    $(".addInputSecondScreenClass").hide();

                    // Clear the loading indicator
                    $("#college-predictor-results-container").html('');

                    // Create the structure for the results
                    var resultsHtml = '<h2 class="predictor-header-text">Colleges based on your marks</h2>' +
                        '<div class="predictor-main">' +
                        '<div class="predictor-tool" id="predictor-filter-container"></div>' +
                        '<div id="predictor-list-container"></div>' +
                        '</div>';

                    // Add the structure to the container
                    $("#college-predictor-results-container").html(resultsHtml);

                    // Now update the filter and list containers
                    $("#predictor-filter-container").html(response.filterHtml);
                    $("#predictor-list-container").html(response.listHtml);

                    // Check if there are any colleges in the response
                    if ($(response.listHtml).find('.predictor-box').length === 0) {
                        // No colleges found, hide the filter
                        // $("#predictor-filter-container").css("display", "none");
                        $("#predictor-list-container").html('<div class="no-results" style="text-align: center; padding: 20px;">No colleges found matching your criteria.</div>');
                    }

                    // Initialize filter handlers for the newly loaded content
                    initializeFiltersInResults();

                    // Remove rank and category from session storage after loading colleges
                    sessionStorage.removeItem('college_predictor_rank');
                    sessionStorage.removeItem('college_predictor_category');
                } else {
                    $("#college-predictor-results-container").html('<div class="error-message" style="text-align: center; padding: 20px; color: red;">Error: Invalid response from server. Please try again.</div>');
                }
            },
            error: function (xhr, status, error) {
                $("#college-predictor-results-container").html('<div class="error-message" style="text-align: center; padding: 20px; color: red;">Error loading college predictor results. Please try again.</div>');
            }
        });
    }

    // Initialize filter handlers for the results container
    function initializeFiltersInResults() {
        // Handle filter changes
        $("#college-predictor-results-container .filter-select").on('change', function () {
            // Get rank and category from hidden inputs instead of session storage
            var rank = $("#college_predictor_rank_value").val();
            var category = $("#college_predictor_rank_category").val();
            var course = $("#course-filter").val();
            var collegeType = $("#college-type-filter").val();
            var state = $("#state-filter").val();
            var examSlug = gmu.config.entity == 'exam' ? gmu.config.entity_slug : '';

            // Show loading indicator
            $("#predictor-list-container").html('<div class="loading-indicator" style="text-align: center; padding: 20px;">Updating results...</div>');

            // Make AJAX request with updated filters
            $.ajax({
                url: '/college-predictor/ajax-predictor',
                data: {
                    rank: rank,
                    category: category,
                    course: course,
                    collegeType: collegeType,
                    state: state,
                    examSlug: examSlug
                },
                dataType: 'json',
                method: 'GET',
                success: function (response) {
                    if (response.success) {
                        // Update the filter container first to ensure dropdowns are in sync
                        $("#predictor-filter-container").html(response.filterHtml);

                        // Then update the list container
                        if ($(response.listHtml).find('.predictor-box').length > 0) {
                            $("#predictor-list-container").html(response.listHtml);
                            // Show the filter if it was previously hidden
                            $("#predictor-filter-container").css("display", "block");
                        } else {
                            $("#predictor-list-container").html('<div class="no-results" style="text-align: center; padding: 20px;">No colleges found matching your criteria.</div>');
                            // Hide the filter when no results
                            // $("#predictor-filter-container").css("display", "none");
                        }

                        // Reattach event handlers to the new filter elements
                        initializeFiltersInResults();
                    }
                },
                error: function () {
                    $("#predictor-list-container").html('<div class="error-message" style="text-align: center; padding: 20px; color: red;">Error updating results. Please try again.</div>');
                }
            });
        });

        // Handle load more button
        $('body').on('click', '#loadMoreCollegePredictor', function () {
            // Get rank and category from hidden inputs instead of session storage
            var rank = $("#college_predictor_rank_value").val();
            var category = $("#college_predictor_rank_category").val();
            var course = $("#course-filter").val();
            var collegeType = $("#college-type-filter").val();
            var state = $("#state-filter").val();
            var examSlug = gmu.config.entity == 'exam' ? gmu.config.entity_slug : '';
            var page = parseInt($(this).data('page') || 1) + 1;

            // Update button text
            $(this).text('Loading...');

            // Make AJAX request for next page
            $.ajax({
                url: '/college-predictor/ajax-predictor',
                data: {
                    rank: rank,
                    category: category,
                    course: course,
                    collegeType: collegeType,
                    state: state,
                    examSlug: examSlug,
                    page: page
                },
                dataType: 'json',
                method: 'GET',
                success: function (response) {
                    if (response.success) {
                        // Append new colleges to the existing list
                        var $newColleges = $(response.listHtml).find('.predictor-box');
                        $("#itemContainer").append($newColleges);

                        // Update or remove the load more button
                        if (response.hasNext) {
                            $("#loadMoreCollegePredictor").text('View More colleges').data('page', page);
                        } else {
                            $(".load-more").remove();
                        }
                    }
                },
                error: function () {
                    $("#loadMoreCollegePredictor").text('View More colleges');
                    alert('Error loading more colleges. Please try again.');
                }
            });
        });
    }
});

function activateStudentSessionPredictor(isReferesh = null) {
    var student_id = localStorage.getItem('studentId') ?? '';
    var activity_id = localStorage.getItem('activityId') ?? '';
    var csrf = $("input[name='_csrf-frontend']").val();
    $.ajax({
        url: '/lead-v4/student-session-activate',
        data: { student_id: student_id, activity_id: activity_id },
        dataType: 'json',
        method: 'POST',
        beforeSend: function () {
            $('.primaryBtn').prop('disabled', true);
        },
        error: function (xhr, err) {
            $('.primaryBtn').prop('disabled', false)
            displayErrorsLead('Something went wrong, please try again!')
        },
        complete: function () {
            $('.primaryBtn').prop('disabled', false);
        },
        success: function (data) {
            if (data.success == true) {
                setCookie('gmu_leadform', 1, 10);
                if (isReferesh) {
                    location.reload(true);
                }
            }
        }
    });
}