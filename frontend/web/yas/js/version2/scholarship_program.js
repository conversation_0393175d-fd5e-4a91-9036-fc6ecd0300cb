$(document).ready(function () {

    $('#want-to-get-in').on('click', function () {
        $('.error-msg').remove();
        const mobile = $('#mobile').val().trim();
        const email = $('#email').val().trim();
        const mobileRegex = /^[6-9]\d{9}$/;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        let hasError = false;

        // Validate mobile number
        if (!mobileRegex.test(mobile)) {
            $('#mobile').after('<div class="error-msg" style="color:red; font-size:12px; margin-top:4px;">Enter a valid 10-digit mobile number starting with 6-9.</div>');
            alert('Please enter a valid mobile number.');
            hasError = true;
        }

        // Validate email
        if (!emailRegex.test(email)) {
            $('#email').after('<div class="error-msg" style="color:red; font-size:12px; margin-top:4px;">Enter a valid email address.</div>');
            alert('Please enter a valid email address.');
            hasError = true;
        }

        if (hasError) return;

        const form = $('#leadForm')[0];
        const formData = new FormData(form); // More accurate than .serialize()

        $.ajax({
            url: '/scholarship-program/submit-scholarship',
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function (response) {
                if (response.success) {
                    $('#lead_container').hide();
                    $('#answer_container').slideDown();
                } else {
                    $('.error-msg').remove();
                    if (response.errors && typeof response.errors === 'object') {
                        Object.keys(response.errors).forEach(function (field) {
                            const inputField = $('[name="' + field + '"]');

                            if (inputField.length) {
                                const isSelect2 = inputField.hasClass('select2-hidden-accessible');
                                const targetField = isSelect2 ? inputField.next('.select2') : inputField;
                                targetField.parent().find('.error-msg').remove();
                                targetField.next('.error-msg').remove();
                                targetField.after(
                                    '<div class="error-msg" style="color:red; font-size:12px; margin-top:4px;">' +
                                    response.errors[field] +
                                    '</div>'
                                );
                            } else {
                                console.warn('Field not found in DOM:', field);
                            }
                        });
                    }
                    console.log(response.errors);
                }
            }
        });
    });
    $('#answer_save').on('click', function () {
        $('.error-msg').remove();

        const answer = $('#scholarship_answer').val().trim();
        if (!answer) {
            $('#scholarship_answer').after(
                '<div class="error-msg" style="color:red; font-size:12px; margin-top:4px;">Please enter your answer.</div>'
            );
            alert('Please enter your answer before submitting.');
            return;
        }

        $.post('/scholarship-program/submit-answer', {
            scholarship_answer: answer
        }, function (response) {
            if (response.success) {
                $('#answer_container').hide();
                $('#social_container').slideDown();
            } else {
                alert('Could not save answer. Please try again.');
            }
        });
    });

    $('#answer_skip').on('click', function () {
        $('#answer_container').hide();
        $('#social_container').slideDown();
    });

    // City select2
    $('.selectCity select').select2({
        placeholder: "Select/Enter Your Current City",
        ajax: {
            url: "/ajax/lead-cities",
            dataType: "json",
            type: "GET",
            delay: 250,
            data: function (params) {
                return {
                    term: params.term
                };
            },
            processResults: function (data) {
                return {
                    results: (data.states || []).map(function (state) {
                        return {
                            text: state.state_name,
                            children: state.cities.map(function (city) {
                                return {
                                    id: city.id,
                                    text: city.text
                                };
                            })
                        };
                    })
                };
            }
        }
    });

    // College select2
    $('#college').select2({
        placeholder: "Current College",
        minimumInputLength: 1,
        ajax: {
            url: "/ajax/college-list",
            dataType: "json",
            type: "GET",
            minimumInputLength: 1,
            data: function (params) {
                var queryParameters = {
                    term: params.term,
                }
                return queryParameters;
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (item) {
                        return {
                            text: item.text,
                            id: item.id,
                        };
                    }),
                };
            }
        }
    });

    // Stream select2
    $('#stream').select2({
        placeholder: "Select Course Category Interested In",
        minimumInputLength: 1,
        ajax: {
            url: "/ajax/stream-list",
            dataType: "json",
            type: "GET",
            delay: 250,
            data: function (params) {
                return {
                    query: params.term
                };
            },
            processResults: function (data) {
                return {
                    results: data.results || []
                };
            }
        }
    });

    // Degree select2
    $('#degree').select2({
        placeholder: "Select/Enter Highest Qualification",
        minimumInputLength: 1,
        ajax: {
            url: "/ajax/degree-list",
            dataType: "json",
            type: "GET",
            delay: 250,
            data: function (params) {
                return {
                    query: params.term
                };
            },
            processResults: function (data) {
                return {
                    results: data.results || []
                };
            }
        }
    });
});