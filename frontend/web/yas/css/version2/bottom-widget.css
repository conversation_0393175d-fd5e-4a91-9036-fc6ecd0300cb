/* Pagination */
.pagination,
ul.pagination {
    text-align: center;
    padding: 0;
    margin: 0;
    margin-bottom: 10px;
    width: 100%;
    display: block;
}

.pagination span,
ul.pagination span {
    min-width: 36px;
    height: 36px;
    display: inline-block;
    padding: 6px 8px;
    font-size: 14px;
    line-height: 24px;
    color: #989898;
    text-decoration: none;
    border-radius: 3px;
    border: var(--border-line);
    overflow: hidden;
    -webkit-transition: 0.2s ease;
    transition: 0.2s ease;
}

.pagination li,
ul.pagination li {
    text-align: center;
    display: inline-block;
    margin: 0 2px;
    height: 35px;
}

.pagination li a,
ul.pagination li a {
    min-width: 36px;
    height: 36px;
    display: block;
    padding: 6px 8px;
    font-size: 14px;
    line-height: 24px;
    color: #989898;
    text-decoration: none;
    border-radius: 3px;
    border: var(--border-line);
    overflow: hidden;
    -webkit-transition: 0.2s ease;
    transition: 0.2s ease;
}

.pagination li a:hover,
ul.pagination li a:hover {
    color: var(--color-white);
    border: 0.2px solid var(--color-red);
    background: var(--color-red);
}

.pagination li.active,
ul.pagination li.active {
    background: var(--color-red);
    border-radius: 3px;
    color: var(--color-white);
    border: 0.2px solid var(--color-red);
    font-weight: var(--font-semibold);
}

/* .pagination li.active a,
ul.pagination li.active a {
    
} */

.pagination li.active span,
ul.pagination li.active span {
    color: var(--color-white);
    border: 0.2px solid var(--color-red);
    font-weight: var(--font-semibold);
}

.pagination ul {
    margin: 0;
    padding: 0;
}

/* Comment Section */
.write-comment {
    background: rgba(51, 51, 51, 0.6);
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    overflow: auto;
    display: none;
}

.write-comment .commentSection {
    max-width: 60%;
    margin: 50px auto;
    padding: 25px;
    margin-top: 125px;
    position: relative;
}

.write-comment .commentSection .commentForm {
    padding-bottom: 0;
    border: none;
}

.write-comment .commentSection .commentForm .col-md-6 {
    flex-basis: 48.5%;
    padding: 0;
    margin-right: 13px;
}

.write-comment .commentSection .commentForm .col-md-6:nth-of-type(2n) {
    margin-right: 0;
}

.write-comment .commentSection h2 {
    font-size: 24px;
    line-height: 24px;
    padding: 0;
    padding-bottom: 15px;
    font-weight: var(--font-500);
    text-transform: uppercase;
    background: 0 0;
    margin: 0;
}

.write-comment .commentSection .closeForm {
    position: absolute;
    right: 25px;
    top: 25px;
}


/* Page Footer */
.pageFooter {
    color: var(--color-white);
    margin-top: 20px;
}

.pageFooter p {
    font-size: 14px;
    line-height: 24px;
    color: var(--color-white);
}

.pageFooter .socialMedia {
    margin: 0;
    padding: 0;
    padding: 20px 0;
}

.pageFooter .socialMedia li {
    display: inline-block;
    margin-right: 5px;
}

.pageFooter .socialMedia li:first-child {
    display: block;
    padding-bottom: 10px;
    font-size: 14px;
    line-height: 20px;
    margin: 0;
    color: var(--color-white);
}

.pageFooter .socialMedia li a {
    width: 28px;
    height: 28px;
    display: block;
    border-radius: 50px;
}

.pageFooter .copyrightsText {
    padding-top: 20px;
    border-top: 1px solid #c4c4c4;
}

.pageFooter .copyrightsText a {
    color: var(--color-white);
    text-decoration: none;
}

.pageFooter .footerPrimarySection {
    background: #273553;
    padding: 20px 0;
}

.pageFooter .footerPrimarySection .row {
    margin: 0;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    justify-content: space-between;
}

.pageFooter .footerPrimarySection .contactInfo {
    padding: 0;
    margin: 0;
    display: inline-block;
}

.pageFooter .footerPrimarySection .contactInfo li {
    display: inline-block;
    padding: 0 10px;
}

.pageFooter .footerPrimarySection .contactInfo li a {
    color: var(--color-white);
}

.pageFooter .footerPrimarySection .socialMedia {
    padding: 0;
    display: inline-block;
    margin-right: 55px;
}

.pageFooter .footerPrimarySection .socialMedia li {
    display: -webkit-inline-box;
    display: inline-flex;
    vertical-align: middle;
}

.pageFooter .footerPrimarySection .socialMedia li:first-child {
    display: inline-block;
    padding: 0;
    font-weight: 500;
}

.pageFooter .footerSecondSection {
    background: #1f2b45;
    padding: 20px 0 20px 0 !important;
}

.pageFooter .footerSecondSection .copyrightsText {
    border: none;
    padding: 0;
}

.pageFooter .footerSecondSection .row {
    -webkit-box-align: center;
    align-items: center;
    margin: 0;
    -webkit-box-pack: justify;
    justify-content: space-between;
}

.pageFooter .footerSecondSection ul {
    padding: 0;
    margin: 0;
}

.pageFooter .footerSecondSection ul li {
    display: inline-block;
    padding: 0 8px;
}

.pageFooter .footerSecondSection ul li a {
    color: var(--color-white);
    font-size: 14px;
    display: block;
    line-height: 28px;
}

.footerCcontentDiv h3 {
    font-size: 14px;
    line-height: 20px;
    color: var(--color-white);
    padding-bottom: 10px;
}

.footerCcontentDiv ul {
    margin: 0;
    padding: 0;
    padding-bottom: 20px;
}

.footerCcontentDiv ul li {
    list-style-type: none;
    font-size: 14px;
    line-height: 28px;
}

.footerCcontentDiv ul li a {
    color: var(--color-white);
    text-decoration: none;
}

/* Bottom slider widget */
.latestInfoSection,
.otherEntranceExams {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    padding-bottom: 0;
    margin-bottom: 20px;
}

.latestInfoSection a,
.otherEntranceExams a {
    text-decoration: none;
}

.latestInfoSection h2,
.otherEntranceExams h2 {
    padding: 10px 20px;
    font-size: 18px;
    line-height: 24px;
    background: #f5f5f5;
    margin: 0;
    margin-bottom: 20px;
}

.latestInfoSection h2.row,
.otherEntranceExams h2.row {
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
}

.latestInfoSection h2 a,
.otherEntranceExams h2 a {
    font-size: 14px;
    color: var(--color-red);
    text-decoration: none;
}

.latestInfoSection.row,
.otherEntranceExams.row {
    margin: 0;
}

.latestInfoSection .latestInfoDiv,
.otherEntranceExams .latestInfoDiv {
    flex-basis: 23.7%;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 6px;
    border: var(--border-line);
    overflow: hidden;
}

.latestInfoSection .latestInfoDiv figure,
.otherEntranceExams .latestInfoDiv figure {
    margin: 0;
}

.latestInfoSection .latestInfoDiv:nth-of-type(4n + 0),
.otherEntranceExams .latestInfoDiv:nth-of-type(4n + 0) {
    margin-right: 0;
}

.latestInfoSection .latestInfoTxt,
.otherEntranceExams .latestInfoTxt {
    padding: 16px;
}

.latestInfoSection .latestInfoTxt p,
.latestInfoSection .latestInfoTxt a,
.otherEntranceExams .latestInfoTxt p,
.otherEntranceExams .latestInfoTxt a {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: var(--font-500);
    text-decoration: none;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 58px;
    margin-bottom: 10px;
}

.latestInfoSection .latestInfoTxt a,
.otherEntranceExams .latestInfoTxt a {
    margin: 0;
}

.latestInfoSection .latestInfoTxt a:hover,
.otherEntranceExams .latestInfoTxt a:hover {
    color: #3d8ff2;
    text-decoration: underline;
}

.latestInfoSection .latestInfoTxt p:last-child,
.otherEntranceExams .latestInfoTxt p:last-child {
    color: #989898;
    margin: 0;
    font-weight: 400;
}

.latestInfoSection .latestInfoTxt p:last-child a,
.otherEntranceExams .latestInfoTxt p:last-child a {
    color: #989898;
    margin: 0;
    font-weight: 400;
}

.latestInfoList.row,
.otherEntranceExams .row {
    margin: 0;
}

.latestInfoSection .latestInfoList .latestInfoTxt p {
    color: #282828;
    font-weight: 500;
}

/* FAQ SECTION */
.faq_section {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin-bottom: 20px;
}

.faq_section h2 {
    padding: 10px 20px;
    font-size: 18px;
    line-height: 24px;
    color: var(--primary-font-color);
    background: #f5f5f5;
    margin-bottom: 20px;
}

.faq_section .faqDiv {
    border-radius: 4px;
    border: var(--border-line);
}

.faq_section p {
    padding: 11px 24px;
    font-size: 16px;
    line-height: 26px;
    border-bottom: var(--border-line);
}

.faq_section .faq_question {
    position: relative;
    cursor: pointer;
    -webkit-transition: 0.4s ease;
    transition: 0.4s ease;
}

.faq_section .faq_question:after {
    content: " ";
    background: url(../../images/master_sprite.webp);
    width: 12px;
    height: 21px;
    position: absolute;
    right: 17px;
    top: 12px;
    background-position: 591px -71px;
    -webkit-transition: 0.2s ease;
    transition: 0.2s ease;
}

.faq_section .faq_question.downAngle:after {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
}

.faq_section .faq_answer {
    border-left: 5px solid var(--color-red);
    font-size: 15px;
    background: #fafbfc;
}

.faqDiv .faq_answer p,
.faqDiv .faq_answer li,
.faqDiv .faq_answer a {
    font-size: 15px;
    line-height: 26px;
    font-weight: 400;
    border: none;
}

.faqDiv .faq_answer {
    padding: 11px 24px;
}

.faqDiv .faq_answer p {
    padding: 0;
    padding-bottom: 5px;
}

/* EXAM SLIDER BOTTOM WIDGET */
.examInfoSlider {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    padding-bottom: 0;
    margin-bottom: 20px;
    position: relative;
}

.examInfoSlider .scrollLeft {
    top: 58%;
}

.examInfoSlider .scrollRight {
    top: 52%;
}

.examInfoSlider .row {
    margin: 0 -10px;
    display: flex;
    overflow: auto;
    flex-wrap: nowrap;
    white-space: nowrap;
}

.examInfoSlider .row::-webkit-scrollbar {
    height: 5px;
    display: none;
}

.examInfoSlider .row .col-md-6 {
    padding: 0 10px;
}

.examInfoSlider .row .col-md-4 {
    max-width: 33%;
    padding: 0 10px;
    display: inline-block;
    width: 100%;
    white-space: normal;
    vertical-align: middle;
    overflow: auto;
}

.examInfoSlider h2 {
    padding: 10px 20px;
    font-size: 18px;
    line-height: 24px;
    background: #f5f5f5;
    margin: 0;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.examInfoSlider h2.row {
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
    margin: 0;
    margin-bottom: 20px;
}

.examInfoSlider h2 a {
    font-size: 14px;
    color: var(--color-red);
    text-decoration: none;
}

.examInfoSlider .examCategoryInfo {
    border-radius: 4px;
    border: var(--border-line);
    margin-bottom: 20px;
}

.examInfoSlider .examCategoryInfo img {
    max-width: 64px;
    border-radius: 50%;
    margin-right: 25px;
    height: 64px;
}

.examInfoSlider .examCategoryInfo .examInfoDates {
    flex-basis: calc(100% - 90px);
}

.examInfoSlider .examCategoryInfo .row {
    padding: 20px;
    display: -webkit-box;
    display: flex;
    border-bottom: var(--border-line);
    margin: 0;
    min-height: 119px;
}

.examInfoSlider .examCategoryInfo p {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: var(--font-semibold);
}

.examInfoSlider .examCategoryInfo p:first-child {
    font-size: 16px;
    line-height: 24px;
    padding-bottom: 4px;
}

.examInfoSlider .examCategoryInfo p span {
    color: #989898;
    font-weight: 400;
    font-size: 13px;
}

.examInfoSlider .examCategoryInfo .examCriteria {
    padding: 20px;
    padding-bottom: 12px!important;
}

.examInfoSlider .examCategoryInfo .examCriteria ul {
    padding: 0;
    margin: 0;
}

.examInfoSlider .examCategoryInfo .examCriteria ul li {
    display: inline-block;
}

.examInfoSlider .examCategoryInfo .examCriteria ul li a {
    border-radius: 3px;
    display: block;
    text-decoration: none;
    color: var(--anchor-textclr);
    font-size: 14px;
    line-height: 24px;
    text-transform: uppercase;
    font-weight: var(--font-500);
    padding: 4.55px 10px 4.55px 10px;
    background: #3D8FF217;
    border: 0.5px solid #3D8FF2
}

.examCriteria ul {
    display: -webkit-box;
    display: flex;
    flex-wrap: wrap;
}

.examCriteria ul li {
    margin-right: 16px;
    text-align: center;
}

/* COMMENT SECTION */

.commentSection {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin-bottom: 20px;
    border-bottom: var(--border-line);
}

.commentSection h2 {
    color: var(--primary-font-color);
    padding: 10px 24px;
    font-size: 18px;
    line-height: 24px;
    background: #f5f5f5;
    font-weight: var(--font-semibold);
    margin-bottom: 20px;
}

.commentSection .commentForm {
    padding-bottom: 20px;
    border-bottom: var(--border-line);
}

.commentSection .commentForm .row {
    margin: 0;
}

.commentSection .commentForm .col-md-6 {
    flex-basis: 49%;
    padding: 0;
    margin-right: 20px;
}

.commentSection .commentForm .col-md-6:nth-of-type(2n + 0) {
    margin-right: 0;
}

.commentSection .commentForm .form-group {
    margin-bottom: 20px;
}

.commentSection .commentForm input,
.commentSection .commentForm textarea {
    padding: 11px 20px;
    border-radius: 4px;
    border: var(--border-line);
    line-height: 24px;
    width: 100%;
}

.commentSection .commentText {
    border: var(--border-line);
    border-radius: 6px;
}

.commentSection .commentText p {
    color: var(--primary-font-color);
    font-size: 15px;
    line-height: 26px;
    padding: 20px;
}

.commentSection h3 {
    font-size: 16px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: var(--font-bold);
    padding: 10px 0;
    padding-bottom: 20px;
}

.commetByStudent .row {
    margin: 0;
}

.commetByStudent .col-md-1 {
    padding: 0;
    flex-basis: 48px;
}

.commetByStudent {
    margin: 30px 0;
}

.commetByStudent .col-md-11 {
    flex-basis: calc(100% - 48px);
    max-width: calc(100% - 48px);
}

.commetByStudent img {
    max-width: 48px;
    border-radius: 50%;
}

.commetByStudent p {
    font-size: 15px;
    line-height: 26px;
    padding-bottom: 10px;
}

.commetByStudent .reviewer-name {
    font-size: 13px;
    line-height: 20px;
    color: var(--primary-font-color);
    font-weight: var(--font-bold);
    padding: 0;
}

.commetByStudent .updatedTime {
    color: #aaa;
    padding-bottom: 10px;
    line-height: 20px;
    padding: 0;
    padding-bottom: 10px;
}

.commetByStudent .replyTxt {
    font-size: 14px;
    line-height: 24px;
    color: var(--color-red);
    font-weight: var(--font-bold);
    text-decoration: none;
}

.commetByStudent .commentByOrganization {
    padding: 20px 0;
    border-top: var(--border-line);
    margin-top: 20px;
}

.commetByStudent .commentByOrganization:last-child {
    border-bottom: var(--border-line);
}

.commetByStudent .commentByOrganization p {
    padding: 0;
}

.commetByStudent .commentByOrganization p.updatedTime {
    padding-bottom: 10px;
}

.latestInfoSection .latestInfoTxt {
    padding-bottom: 0px !important;
}

.examInfoSlider h2 {
    background-color: transparent !important;
    padding-left: 5px !important;
}

.examSliderCardsCtn{
    padding: 0 40px;
}

.examSliderCards {
    padding: 10px 5px 24px 5px!important;
    margin-top: 16px !important;
    overflow: auto;
}

.examInfoSlider .examSliderCards .examCategoryInfo {
    border: 0px !important;
}

.examInfoSlider .examSliderCards .examCategoryInfo .row {
    border-bottom: 0px !important;
}

.col-md-4.examSliders {
    border: none !important;
    margin-right: 16px;
    border-radius: 4px 0px 0px 0px;
    background: #FFFFFF;
    box-shadow: 0px 0px 9px 1px #00000014;
    padding: 0px !important;
}

.two-cardDisplay {
    padding: 0 23px;
    margin-top: 16px;
}

ul.examCardBtn {
    display: flex;
    gap: 10px;
    padding: 0px 20px !important;
}

.interestedExam{
    position: relative;
}

.examCriteria ul li {
    margin-right: 0px !important;
}

.examInfoSlider .examSliders .examCategoryInfo .examCriteria {
    padding: 0px !important;
}

.examSliderCards .examSliders .clgLogo {
    margin-left: 0;
    border-radius: 4px;
    background: #FFFFFF;
    border: 0.5px solid #00000080;
    max-width: 72px;
    width: 72px;
    height: 72px;
}

@media(max-width:1023px) {

    /* Comment Section */
    .write-comment .commentSection {
        max-width: 95%;
        padding: 20px;
        margin-top: 70px;
    }

    .write-comment .commentSection h2 {
        font-size: 16px;
        padding-bottom: 10px;
        font-weight: var(--font-semibold);
    }

    .write-comment .commentSection .closeForm {
        right: 20px;
        top: 20px;
    }

    .write-comment .commentSection .commentForm .col-md-6 {
        flex-basis: 100%;
        margin-right: 0;
        padding-bottom: 10px;
    }

    .write-comment .commentSection .commentForm .col-md-6 input,
    .write-comment .commentSection .commentForm .col-md-6 textarea {
        padding: 7px;
    }

    .write-comment .commentSection .commentForm .commentBtn {
        display: block;
        width: 100%;
    }

    /* Footer section */
    .pageFooter {
        padding-bottom: 70px;
    }

    .pageFooter .footerPrimarySection {
        padding: 20px 10px;
    }

    .pageFooter .footerPrimarySection .socialMedia {
        margin: 22px 0;
    }

    .pageFooter .footerPrimarySection .contactInfo li {
        padding: 0;
        padding-bottom: 10px;
    }

    .pageFooter .footerPrimarySection .contactInfo li:last-child {
        padding-bottom: 0;
    }

    .pageFooter .footerSecondSection {
        padding: 20px 10px;
        padding-bottom: 60px;
    }

    .pageFooter .footerSecondSection ul {
        margin-bottom: 10px;
    }

    .pageFooter .footerSecondSection ul li {
        display: block;
        padding: 0;
        font-size: 14px;
    }

    .pageFooter .footerSecondSection .copyrightsText {
        font-size: 12px;
        line-height: 24px;
    }

    /* Bottom slider widget */
    .latestInfoSection {
        padding: 10px;
    }

    .latestInfoSection h2 {
        font-size: 15px;
        padding: 10px;
        margin-bottom: 10px;
        font-weight: var(--font-semibold);
    }

    .latestInfoSection h2 a {
        display: none;
    }

    .latestInfoList.row,
    .otherEntranceExams .row {
        overflow: auto;
        white-space: nowrap;
        display: block;
    }

    .latestInfoSection .latestInfoDiv {
        margin-right: 10px;
        margin-bottom: 0;
        display: inline-block !important;
        width: 70%;
        white-space: normal;
        vertical-align: middle;
        overflow: auto;
        text-align: left;
    }

    .latestInfoSection .latestInfoDiv img {
        display: block;
    }

    .latestInfoSection .latestInfoDiv .viewAllDiv {
        min-height: 262px;
    }

    .latestInfoSection .latestInfoTxt {
        padding: 10px;
    }


    .latestInfoSection .latestInfoDiv:nth-of-type(4n + 0),
    .otherEntranceExams .latestInfoDiv:nth-of-type(4n + 0),
    .otherExamDiv:nth-of-type(6n + 0) {
        margin-right: 10px;
    }

    /* FAQ SECTION */
    .faq_section {
        padding: 10px;
    }

    .faq_section h2 {
        padding: 8px 10px;
        margin-bottom: 10px;
        font-size: 15px;
        font-weight: var(--font-semibold);
    }

    .faq_section p {
        font-size: 15px;
        padding: 10px;
        padding-left: 5px;
    }

    .faq_section .faq_answer {
        color: #787878;
        padding: 10px;
    }

    .faq_section .faq_question {
        padding-right: 25px;
    }

    .faq_section .faq_question:after {
        right: 10px;
    }


    /* EXAM SLIDER BOTTOM WIDGET */
    .examInfoSlider {
        padding: 10px;
    }

    .examInfoSlider .scrollLeft,
    .examInfoSlider .scrollRight,
    .examInfoSlider .rightLst,
    .examInfoSlider .leftLst {
        display: none !important;
    }

    .examInfoSlider .examCategoryInfo {
        margin-bottom: 0;
    }

    .examInfoSlider h2 {
        padding: 8px 10px;
        font-size: 15px;
        margin-bottom: 10px;
        font-weight: var(--font-semibold);
    }

    .examInfoSlider h2 a {
        display: none;
    }

    .examInfoSlider h2.row {
        margin-bottom: 10px;
        display: block;
        padding: 8px 10px;
    }

    .examInfoSlider .MultiCarousel-inner {
        overflow: auto;
        white-space: nowrap;
        display: block;
        padding: 0 10px;
    }

    .examInfoSlider .row{
        padding: 0 10px;
    }

    .examInfoSlider .row .col-md-6,
    .examInfoSlider .row .col-md-4,
    .examInfoSlider .row .item,
    .examInfoSlider .MultiCarousel-inner .col-md-6,
    .examInfoSlider .MultiCarousel-inner .col-md-4,
    .examInfoSlider .MultiCarousel-inner .item {
        flex-shrink: 0;
        float: none;
        margin-right: 10px;
        margin-bottom: 0;
        display: inline-block !important;
        width: 86%;
        max-width: 86%;
        white-space: normal;
        vertical-align: middle;
        overflow: auto;
        padding: 0;
    }

    .examInfoSlider .examInfoSlider .MultiCarousel-inner {
        padding: 0;
    }

    .examInfoSlider .examCategoryInfo .row {
        padding: 10px;
        display: block;
        border-bottom: none;
        min-height: 177px;
    }

    .examInfoSlider .examCategoryInfo img {
        max-width: 72px;
        margin-bottom: 10px;
    }

    .examInfoSlider .examCategoryInfo p:first-child {
        font-size: 14px;
    }

    .examInfoSlider .examCategoryInfo .examCriteria {
        padding: 0 10px;
    }

    .examInfoSlider .examCategoryInfo .examCriteria ul {
        display: block;
    }

    .examInfoSlider .examCategoryInfo .examCriteria ul li {
        margin-right: 10px;
    }

    .examInfoSlider .examCategoryInfo .examCriteria ul lili:nth-of-type(2n + 0) {
        margin-right: 10px;
    }

    .examInfoSlider .examCategoryInfo .examCriteria ul li a {
        font-size: 13px;
        margin-bottom: 10px;
    }

    /* COMMENT SECTION */
    .commentSection {
        padding: 10px;
    }

    .commentSection h2 {
        font-size: 15px;
        padding: 10px;
        font-weight: var(--font-semibold);
        margin-bottom: 10px;
    }

    .commentSection .commentForm .col-md-6 {
        padding-bottom: 10px;
        flex-basis: 100%;
        margin-right: 0;
    }

    .commentSection .commentForm .form-group {
        margin-bottom: 0;
    }

    .commentSection .commentForm .commentBtn {
        display: block;
        margin-top: 10px;
    }

    .commetByStudent .col-10 {
        padding-right: 0;
    }

    .commetByStudent .updatedTime {
        padding-bottom: 10px;
    }

    .examSliders .row {
        min-height: unset !important;
    }

    .examSliders ul.examCardBtn {
        padding: 0px 10px !important;
    }

    .examSliderCardsCtn{
        padding: 0px;
    }    
    .two-cardDisplay {
        padding: 0;
    }
}