.college__Landing__Hero__Section {
  background: #ffffff;
  border: 0.2px solid rgba(166, 166, 166, 0.44);
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  margin-top: 10px;
  padding: 28px 30px;
}
.college__Landing__Hero__Section h1 {
  font-size: 24px;
  line-height: 38px;
  padding-bottom: 20px;
  font-weight: normal;
}
.college__Landing__Hero__Section .updated-info.row {
  margin: 0;
}
.college__Landing__Hero__Section .updated-info.row img {
  width: 44px;
  height: 44px;
  margin-right: 6px;
}
.college__Landing__Hero__Section .updated-info.row .authorAndDate {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.college__Landing__Hero__Section .updated-info.row .authorAndDate .authorName {
  font-size: 14px;
  font-weight: 500;
  line-height: 16px;
  color: #282828;
  text-transform: none;
  align-self: flex-end;
}
.college__Landing__Hero__Section .updated-info.row .authorAndDate p {
  font-size: 12px;
  font-weight: normal;
  line-height: 14px;
  color: #787878;
  padding-bottom: 0;
  display: block;
  flex-basis: 100%;
  align-self: flex-start;
  margin-top: 4px;
}
.college__Landing__Hero__Section .updated-info.row .authorAndDate .verifiedBlueTickIcon {
  background-position: -673px -557px;
  width: 17px;
  height: 16px;
  vertical-align: top;
  transform: scale(0.85);
  margin-left: 4px;
  align-self: flex-end;
}
.college__Landing__Hero__Section .college__Landing__Hero__Section__Heading {
  font-size: 24px;
  font-weight: 400;
  line-height: 38px;
  margin-bottom: 8px;
  padding-bottom: 0px;
}
.college__Landing__Hero__Section .col-md-5,
.college__Landing__Hero__Section .col-md-4 {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.college__Landing__Hero__Section p {
  font-size: 14px;
  line-height: 24px;
  padding-bottom: 10px;
}
.college__Landing__Hero__Section .primaryBtn {
  padding: 5px 20px;
}

.filterSidebarSection {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
  padding-bottom: 0;
}
.filterSidebarSection .row {
  margin: 0;
}
.filterSidebarSection p,
.filterSidebarSection li,
.filterSidebarSection a,
.filterSidebarSection button {
  font-size: 14px;
  line-height: 24px;
}
.filterSidebarSection .clearAll {
  color: var(--color-red);
  cursor: pointer;
}
.filterSidebarSection .foundesults {
  justify-content: space-between;
  padding-bottom: 20px;
}

.filterCategoryName {
  margin: 0 -20px;
  padding: 8px 20px;
  font-weight: 500;
  background: #f5f5f5;
  position: relative;
  cursor: pointer;
}
.filterCategoryName:after {
  content: " ";
  background: url("../../images/master_sprite.png");
  width: 12px;
  height: 16px;
  position: absolute;
  right: 17px;
  top: 12px;
  background-position: 652px -94px;
  transition: 0.2s ease;
  transform: rotate(-90deg);
}
.filterCategoryName.down_angle:after {
  transform: rotate(90deg);
}

.filterDiv {
  padding: 16px 0;
}
.filterDiv button {
  color: #787878;
  padding: 5px 8px;
  border-radius: 24px;
  border: var(--border-line);
  background: var(--color-white);
  margin-right: 5px;
  margin-bottom: 16px;
  font-weight: 500;
  cursor: initial;
}
.filterDiv .closeIcon {
  cursor: pointer;
}
.filterDiv input[type=checkbox] {
  margin: 0;
  margin-top: 3px;
  vertical-align: middle;
  flex-basis: 16px;
}
.filterDiv ul {
  padding: 0;
  margin: 0;
  max-height: 120px;
  overflow: auto;
}
.filterDiv ul li {
  list-style-type: none;
  padding-bottom: 8px;
  display: flex;
}
.filterDiv ul::-webkit-scrollbar {
  width: 5px;
}
.filterDiv ul::-webkit-scrollbar-thumb {
  background: #ccc;
}
.filterDiv ul::-webkit-scrollbar-track {
  background: #f1f1f1;
}
.filterDiv label {
  color: #787878;
  padding-left: 10px;
  cursor: pointer;
  vertical-align: middle;
  flex-basis: calc(100% - 16px);
}

.filterSearch input[type=text] {
  border: var(--border-line);
  padding: 7px 16px;
  margin-bottom: 16px;
  line-height: 24px;
  font-size: 14px;
  width: 100%;
  border-radius: 2px;
}

.filter__selected__container {
  display: flex;
  gap: 10px;
  align-items: center;
}
.filter__selected__container .filter__selected {
  border: 1px solid #FEC0C2;
  background: #FFFFFF;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: unset;
}
.filter__selected__container .remove__filter {
  cursor: pointer;
}
.filter__selected__container .filter__selected__name {
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
}

.sort__row__container {
  display: flex;
  justify-content: space-between;
  margin: 14px 0;
}
.sort__row__container .filtered__college_count {
  font-size: 18px;
  font-weight: 500;
  line-height: 38px;
}
.sort__row__container .sortBy__select2__container {
  display: flex;
  background: #FFFFFF;
  border: 1px solid #D8D8D8;
  padding: 0px 5px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  padding-right: 15px;
  height: 26px;
}
.sort__row__container .sortBy__select2__container span {
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  color: #787878;
  white-space: nowrap;
  margin-right: 4px;
}
.sort__row__container .sortBy__select2__container select {
  border: none;
  cursor: pointer;
  color: #282828;
  font-weight: 500;
}

input#autoComplete {
  padding: 9px;
  height: auto;
  padding-left: 40px;
  font-size: 14px;
  line-height: 24px;
  width: 100%;
  border-radius: 3px;
  border: var(--border-line);
  background: url("../../images/search-icon.png") no-repeat;
  background-position: 15px 48%;
  color: var(--primary-font-color);
  background-color: #fff;
  font-weight: 500;
}
input#autoComplete::placeholder {
  color: #989898;
  opacity: 1;
  font-weight: normal;
}
input#autoComplete:focus {
  background-size: auto;
  outline: none;
}
input#autoComplete:focus::-webkit-input-placeholder {
  padding-left: 0px;
  font-size: 14px;
}

.searchBar {
  position: relative;
}

#autoComplete_list {
  max-width: 480px;
  border-radius: 0;
  margin: 0;
  position: absolute;
  max-height: 205px;
  overflow: auto;
  width: 100%;
  background: #fff;
  z-index: 3;
  left: 0;
  top: 46px;
}
#autoComplete_list li.no_result {
  list-style-type: none;
}

.autoComplete_list {
  padding: 0;
  margin: 0;
  max-width: 480px;
  border: var(--border-line);
}
.autoComplete_list li {
  list-style-type: none;
  padding: 3px 5px;
  cursor: pointer;
}
.autoComplete_list li:hover {
  background: rgba(0, 0, 0, 0.15);
}
.autoComplete_list a {
  text-decoration: none;
}

.filtered__colleges__list .college__card__new {
  padding: 30px 25px;
  background: #fff;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
  margin-top: 20px;
}
.filtered__colleges__list .college__card__new .card__header__row {
  display: flex;
}
.filtered__colleges__list .college__card__new .card__header__row .like__compare__grid {
  display: flex;
  gap: 10px;
  flex-grow: 1;
  justify-content: flex-end;
}
.filtered__colleges__list .college__card__new .card__header__row .like__compare__grid .heart__icon, .filtered__colleges__list .college__card__new .card__header__row .like__compare__grid .compare__icon {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  border: 1px solid #d8d8d8;
  cursor: pointer;
}
.filtered__colleges__list .college__card__new .highlight__cta__row {
  display: flex;
  margin-top: 18px;
}
.filtered__colleges__list .college__card__new.featured__card {
  position: relative;
}
.filtered__colleges__list .college__card__new.featured__card::before {
  position: absolute;
  top: -5px;
  left: 25px;
  content: "Featured";
  width: 80px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FFC318;
  font-size: 12px;
  font-weight: 500;
  line-height: 14.06px;
  border-radius: 4px;
  border-top-left-radius: 0;
}
.filtered__colleges__list .college__card__new.featured__card::after {
  position: absolute;
  top: -5px;
  z-index: -1;
  left: 20px;
  content: "";
  display: inline-block;
  width: 0px;
  height: 0px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid #D9A613;
}
.filtered__colleges__list .college__detail__grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, auto));
  gap: 10px;
}
.filtered__colleges__list .college__image {
  border: 1px solid #D8D8D8;
  width: 50px;
  height: 50px;
  border-radius: 4px;
}
.filtered__colleges__list .college__detail__row {
  display: flex;
  flex-wrap: wrap;
}
.filtered__colleges__list .college__detail__row .college__name {
  color: #3D8FF2;
  font-size: 16px;
  font-weight: 500;
  align-self: flex-end;
  flex-basis: 100%;

}
.filtered__colleges__list .college__detail__row .list__style {
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  text-align: left;
  color: #282828;
  position: relative;
  padding-left: 10px;
  margin-left: 10px;
}
.filtered__colleges__list .college__detail__row .list__style:first-child {
  padding: 0;
  margin:0px;
}
.filtered__colleges__list .college__detail__row .list__style:not(:first-child)::before {
  width: 4.5px;
  height: 4.5px;
  border-radius: 50%;
  background-color: #d8d8d8;
  content: "";
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: -5px;
}
.filtered__colleges__list .college__detail__row .list__style.college__rank {
  background: linear-gradient(277.79deg, #0966C2 39.25%, #114272 97.59%);
  color: white;
  padding: 1px 2px;
  border-radius: 3px;
  width: 60px;
  margin-left: 10px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
  font-size: 12px;
  font-weight: 700;
  line-height: 20px;
}
.filtered__colleges__list .college__detail__row .list__style.college__rank::before {
  left: -10px;
}
.filtered__colleges__list .highlights__grid {
  flex-grow: 1;
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.filtered__colleges__list .highlight__div {
  display: flex;
  flex-direction: column;
  max-width: 150px;
}
.filtered__colleges__list .highlight__div .highlight__name {
  color: #989898;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}
.filtered__colleges__list .highlight__div .highlight__value {
  color: #282828;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.filtered__colleges__list .highlight__div .highlight__value h3 {
  color: #282828;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.filtered__colleges__list .cta__grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 10px;
  grid-template-rows: repeat(2, minmax(0, 1fr));
}
.filtered__colleges__list .cta__div {
  min-width: 115px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  line-height: 24px;
  cursor: pointer;
}
.filtered__colleges__list .cta__div.apply__now {
  background: #FF4E53;
  color: #fff;
}
.filtered__colleges__list .cta__div.download__brochure {
  border: 1px solid #FF4E53;
  color: #ff4e53;
}

.filter__by__exam {
  border: 1px solid #CFE1F1;
  padding: 30px 25px;
  background: linear-gradient(360deg, #F0F8FF 4.12%, #E4F2FF 97.06%);
  margin-top: 18px;
}
.filter__by__exam .heading__row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 18px;
}
.filter__by__exam .exam__row__heading {
  font-size: 16px;
  font-weight: 500;
  line-height: 18.75px;
}
.filter__by__exam .view__all__exams {
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  line-height: 16.41px;
  color: #3D8FF2;
}
.filter__by__exam .view__all__exams .blue__angle__icon {
  transform: scale(1.5);
}
.filter__by__exam .exam__list {
  display: flex;
  list-style-type: none;
  flex-wrap: wrap;
  row-gap: 12px;
  column-gap: 7px;
  padding: 0;
  margin: 0;
}
.filter__by__exam .exam__list li {
  border: 1px solid #D8D8D8;
  background-color: white;
  border-radius: 20px;
  padding: 5px 12px;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

.college__carousel {
  padding: 30px;
  background: linear-gradient(180deg, #FFFFFF 0%, rgba(254, 237, 237, 0.5) 100%);
  border: 1px solid #D8D8D8;
  border-radius: 1px;
  margin-top: 15px;
  overflow: hidden;
}
.college__carousel .carousel__heading {
  font-size: 18px;
  font-weight: 500;
  line-height: 21.09px;
  margin-bottom: 20px;
}

.carousel__college__card {
  padding: 16px;
  border: 1px solid #d8d8d8;
  background-color: #fff;
  max-width: 230px;
  height: 220px;
  position: relative;
}
.carousel__college__card .carousel__card__heading {
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: #3D8FF2;
  max-width: 150px;
  display: inline-block;
}
.carousel__college__card .carousel__card__image {
  width: 40px;
  height: 40px;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
  float: right;
}
.carousel__college__card .carousel__card__accredition,
.carousel__college__card .carousel__card__location {
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  color: #282828;
}
.carousel__college__card .carousel__card__rating {
  color: #282828;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  position: relative;
  padding-left: 10px;
  display: inline-flex;
  align-items: center;
  gap: 3px;
}
.carousel__college__card .carousel__card__rating::before {
  width: 4.5px;
  height: 4.5px;
  border-radius: 50%;
  background-color: #d8d8d8;
  content: "";
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
}
.carousel__college__card .carousel__highlights {
  display: flex;
  justify-content: space-between;
  margin: 14px 0;
}
.carousel__college__card .carousel__highlights .value {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.carousel__college__card .carousel__highlights .label {
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}
.carousel__college__card .primaryBtn {
  width: 100%;
  width: calc(100% - 20px);
  position: absolute;
  left: 7px;
  bottom: 10px;
}

.carousel-container {
  overflow: hidden;
  width: 900px;
  /* Adjust based on how many items you want visible */
}

.carousel-track {
  display: flex;
  gap: 16px;
  transition: transform 0.5s ease-in-out;
}

.carousel-item {
  min-width: 230px;
  /* Adjust based on the number of visible items */
  box-sizing: border-box;
}

.carousel-dots {
  text-align: center;
  margin-top: 10px;
}

.dot {
  display: inline-block;
  width: 7px;
  height: 7px;
  background-color: #d8d8d8;
  border-radius: 50%;
  margin: 0 5px;
  cursor: pointer;
}

.dot.active {
  background-color: #FF4E53;
}

.cta__banner {
  padding: 34px 0 34px 44px;
  background: linear-gradient(90deg, #FFFFFF 0%, #F2F9FF 100%);
  border: 1px solid #D8D8D8;
  margin-top: 20px;
  position: relative;
}
.cta__banner p:first-of-type {
  font-size: 16px;
  font-weight: 500;
  line-height: 18.75px;
}
.cta__banner p:last-of-type {
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  margin-top: 6px;
  margin-bottom: 12px;
  max-width: 300px;
}
.cta__banner img {
  position: absolute;
  bottom: 0;
  right: 0;
}

.other__colleges__container {
  padding: 30px;
  background-color: #fff;
  margin-top: 20px;
}
.other__colleges__container h2 {
  background-color: #f5f5f5;
  font-size: 18px;
  font-weight: 500;
  line-height: 38px;
  padding: 5px 20px;
  margin-bottom: 28px;
}
.other__colleges__container .customSlider {
  position: relative;
}
.other__colleges__container .customSlider .scrollRight {
  right: -20px;
}
.other__colleges__container .customSlider .scrollLeft {
  top: 50%;
  left: -20px;
}
.other__colleges__container .customSlider .row {
  margin: 0;
}
.other__colleges__container .customSlider .customSliderCards {
  display: block;
  white-space: nowrap;
  overflow: auto;
  overflow-y: hidden;
}
.other__colleges__container .customSlider .customSliderCards::-webkit-scrollbar {
  display: none;
}
.other__colleges__container .customSlider .sliderCardInfo {
  border-radius: 4px;
  border: var(--border-line);
  margin-right: 14px;
}
.other__colleges__container .customSlider .sliderCardInfo:last-child {
  margin-right: 0;
}
.other__colleges__container .four-cardDisplay .sliderCardInfo {
  width: 23.8%;
  display: inline-block;
  white-space: initial;
}
.other__colleges__container .four-cardDisplay .sliderCardInfo:nth-of-type(4n) {
  margin-right: 0;
}
.other__colleges__container .viewAllDiv {
  display: flex;
  justify-content: center;
  align-items: center;
}

.faq_section {
  margin-top: 20px;
}

.also__explore {
  padding: 28px;
  background-color: #fff;
}
.also__explore h2 {
  background-color: #f5f5f5;
  font-size: 18px;
  font-weight: 500;
  line-height: 38px;
  padding: 5px 20px;
  margin-bottom: 28px;
}
.also__explore .tab__list {
  list-style-type: none;
  display: flex;
  padding: 0;
  margin: 0;
  max-width: 100%;
  overflow-x: auto;
}
.also__explore .tab__list li {
  padding: 14px 24px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  color: #282828;
}
.also__explore .tab__list li.current__tab {
  background-color: #F2F9FF;
}
.also__explore .tab__sections {
  padding: 30px;
  background-color: #F2F9FF;
}
.also__explore .tab__sections .tab__section {
  display: none;
  gap: 20px;
}
.also__explore .tab__sections .tab__section.current__tab {
  display: flex;
}
.also__explore .tab__subsection__list {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 18px;
}
.also__explore .tab__subsection__list p {
  font-size: 14px;
  color: #282828;
  font-weight: 500;
  line-height: 24px;
}
.also__explore .tab__subsection__list ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.also__explore .tab__subsection__list ul li {
  color: #3D8FF2;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  margin-bottom: 5px;
}
.also__explore .tab__subsection__list ul li::before {
  content: "";
  background-image: url("../../images/master_sprite_2.webp");
  background-position: -346px -156px;
  width: 5px;
  height: 9px;
  display: inline-block;
  margin-right: 8px;
}

.news__update__section {
  padding: 28px;
  background-color: #fff;
  margin: 20px 0;
}
.news__update__section h2 {
  background-color: #f5f5f5;
  font-size: 18px;
  font-weight: 500;
  line-height: 38px;
  padding: 5px 20px;
  margin-bottom: 28px;
}
.news__update__section .latest__news__tabs {
  display: flex;
  margin: 0;
  padding: 0;
  list-style-type: none;
  gap: 14px;
  margin-bottom: 20px;
}
.news__update__section .latest__news__tabs li {
  border-radius: 4px;
  color: #282828;
  background-color: #fff;
  border: 1px solid #282828;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}
.news__update__section .latest__news__tabs li.current__tab {
  color: #fff;
  background: #FF4E53;
  border: none;
}
.news__update__section .recent__latest__section {
  display: none;
  flex-wrap: wrap;
  gap: 36px;
  position: relative;
}
.news__update__section .recent__latest__section.current__tab {
  display: flex;
}
.news__update__section .news__card {
  display: flex;
  gap: 15px;
  max-width: 256px;
}
.news__update__section .news__card .news__card__image {
  width: 55px;
  height: 55px;
  flex-shrink: 0;
  border-radius: 4px;
}
.news__update__section .news__card .news__text p:first-child {
  font-size: 14px;
  color: #282828;
  font-weight: 500;
  line-height: 21px;
}
.news__update__section .news__card .news__text p:last-child {
  font-size: 12px;
  color: #989898;
  font-weight: 400;
  line-height: 20px;
}
.news__update__section .load__more__row {
  width: 100%;
  position: absolute;
  bottom: 10px;
}
.news__update__section .load__more__row .load__more__button {
  margin: 0;
  position: absolute;
}
.news__update__section .load__more__row::before {
  content: "";
  display: block;
  width: 100%;
  height: 50px;
  background: linear-gradient(0deg, #FFFFFF 0%, rgba(255, 255, 255, 0.2) 86.24%);
  position: absolute;
  top: -45px;
}

.extended__footer {
  background-color: rgba(39, 53, 83, 0.8);
  padding: 50px 100px;
  display: flex;
  flex-wrap: wrap;
  row-gap: 30px;
}
.extended__footer .extended__footer__section {
  flex-basis: 25%;
}
.extended__footer .extended__footer__section h4 {
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  line-height: 22px;
}
.extended__footer .extended__footer__section ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.extended__footer .extended__footer__section ul li {
  color: #F3F2EF;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}
.extended__footer .extended__footer__section ul li:not(:last-child) {
  margin-bottom: 10px;
}
.extended__footer .extended__footer__section ul li:first-child {
  margin-top: 10px;
}

.feedback__section {
  background: #FFFEF7;
  border: 1px solid #D8D8D8;
  border-radius: 4px;
  padding: 30px 25px;
  margin-top: 20px;
  position: relative;
}
.feedback__section .feedback__image {
  position: absolute;
  bottom: 0;
  right: 0;
}
.feedback__section p:first-of-type {
  font-size: 16px;
  color: #282828;
  font-weight: 500;
  line-height: 18.75px;
}
.feedback__section p:last-of-type {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #282828;
  margin-top: 4px;
  margin-bottom: 18px;
}
.feedback__section ul {
  display: flex;
  list-style-type: none;
  margin: 0;
  padding: 0;
  gap: 10px;
  padding-bottom: 30px;
}
.feedback__section ul li {
  width: 30px;
  height: 30px;
  background-color: #fff;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
  font-size: 15.42px;
  font-weight: 400;
  line-height: 18.07px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  cursor: pointer;
}
.feedback__section ul li:first-of-type::after {
  content: "Least Likely";
  position: absolute;
  bottom: -30px;
  white-space: nowrap;
  left: 0;
  font-size: 10px;
  color: #282828;
  font-weight: 500;
  line-height: 20px;
}
.feedback__section ul li:last-of-type::after {
  content: "Most Likely";
  position: absolute;
  bottom: -30px;
  white-space: nowrap;
  left: 0;
  font-size: 10px;
  color: #282828;
  font-weight: 500;
  line-height: 20px;
  transform: translateX(-50%);
}

.mobile__version {
  display: none;
}

.pageFooter {
  margin-top: 0;
  padding-bottom: 52px;
}

.feedback__container {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 12;
}

.feedback__form {
  padding: 40px;
  max-width: 450px;
  background-color: #fff;
  border: 1px solid #d8d8d8;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 350px;
}
.feedback__form .text1 {
  font-size: 18px;
  color: #282828;
  font-weight: 500;
  line-height: 21.09px;
}
.feedback__form .text2 {
  font-size: 14px;
  color: #282828;
  font-weight: 400;
  line-height: 20px;
  margin-top: 8px;
  margin-bottom: 24px;
  text-align: center;
}
.feedback__form .text3 {
  font-size: 14px;
  color: #282828;
  font-weight: 300;
  line-height: 16.41px;
  padding-bottom: 24px;
  border-bottom: 1px dotted #d8d8d8;
  width: 100%;
  text-align: center;
  margin-bottom: 23px;
}
.feedback__form .text4 {
  font-size: 15px;
  font-weight: 400;
  line-height: 25px;
  color: #282828;
  text-align: left;
  width: 100%;
  margin-bottom: 16px;
}
.feedback__form .rating__buttons,
.feedback__form .feedback__buttons {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  list-style-type: none;
  gap: 7px;
  margin-bottom: 28px;
}
.feedback__form .rating__buttons li,
.feedback__form .feedback__buttons li {
  width: 30px;
  height: 30px;
  border-radius: 2px;
  border: 1px solid #d8d8d8;
  position: relative;
}
.feedback__form .rating__buttons li label,
.feedback__form .feedback__buttons li label {
  width: 100%;
  height: 100%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px 4px;
}
.feedback__form .rating__buttons li input,
.feedback__form .feedback__buttons li input {
  visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
}
.feedback__form .rating__buttons li input:checked + label,
.feedback__form .feedback__buttons li input:checked + label {
  border: 1px solid #FFC318;
  background: #FFC318;
}
.feedback__form .feedback__buttons {
  padding-bottom: 25px;
  margin-bottom: 0px;
  border-bottom: 1px dotted #d8d8d8;
  width:100%;
}
.feedback__form .feedback__buttons li {
  min-width: fit-content;
  flex-grow: 1;
}
.feedback__form .write__feedback {
  width: 100%;
  min-height: 100px;
  margin-bottom: 30px;
}
.feedback__form .primaryBtn {
  width: 100%;
}
.feedback__form .closeIcon {
  top: 20px;
  right: 20px;
  position: absolute;
}

.load__more__row {
  display: flex;
  align-items: center;
  justify-content: center;
}
.load__more__row .load__more__button {
  font-size: 14px;
  color: #FF4E53;
  font-weight: 700;
  background-color: #fff;
  border: 1px solid #FF4E53;
  line-height: 24px;
  padding: 8px 22px;
  max-width: 200px;
  margin-top: 20px;
  cursor: pointer;
  border-radius: 4px;
}

.spriteIcon__2 {
  display: inline-block !important;
  background: url("../../images/master_sprite_2.webp");
  text-align: left;
  overflow: hidden;
}

.red__angle__icon {
  background-position: -193px -80px;
  width: 7px;
  height: 12px;
  transform: rotate(90deg) translateY(-2px) translateX(2px);
}

.compare__icon {
  background-position: -153px -145px;
  width: 22px;
  height: 18px;
}

.heart__icon {
  background-position: -196px -145px;
  width: 20px;
  height: 17px;
}

.heart__icon:hover {
  background-position: -196px -179px;
}


.brochure__download__icon {
  background-position: -244px -154px;
  width: 12px;
  height: 12px;
  margin-right: 8px;
}

.filter__icon {
  background-position: -278px -154px;
  width: 13px;
  height: 13px;
}

.review__star__icon {
  background-position: -313px -154px;
  width: 11px;
  height: 12px;
}

.blue__angle__icon {
  background-position: -346px -156px;
  width: 5px;
  height: 9px;
}

.verified__author__icon {
  background-position: -373px -153px;
  width: 15px;
  height: 15px;
  margin-left: 5px;
  position: relative;
  top: 2px;
}

.small__close__icon {
  background-position: -244px -1142px;
  width: 13px;
  height: 13px;
  transform: scale(0.75) translateY(2px);
  cursor: pointer;
}

.black__angle__icon {
  background-position: -305px -84px;
  width: 14px;
  height: 9px;
  flex-shrink: 0;
  transform: rotate(180deg) scale(0.75) translateX(-6px);
}

.mobile__clear__filter {
  display: none;
}
/* College Landing Page General*/
.hideExamFilter {
  display: none;
}

.getSupport {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 0;
  z-index: 5;
  gap: 18px;
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
  color: #282828;
  align-items: center;
  justify-content: center;
  display: none;
  border: 1px sollid #d8d8d8;
  background-color: white;
}
/* Filter Button CSS*/
.filter__selected__container .filter__selected{
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  color: #282828;
  margin-bottom: 0px;
}
.small__close__icon{
  margin-left: 4px;
}

  /* Secondary cta in college card */
  .download__brochure button{
    border: none;
    background: none;
    width: 100%;
    height: 100%;
    font-size: 14px;
    font-weight: 700;
    line-height: 24px;
    color: #ff4e53;
}

 /* widget section margin fix */
 .faq_section{
  margin-bottom: 0px;
}
.also__explore{
  margin-top: 20px;
}
/* college card height css*/
.filtered__colleges__list .cta__div{
  max-height: 40px;
  max-width: 170px;
}
.filtered__colleges__list .cta__div, .filtered__colleges__list .cta__div button{
  padding: 0px 0px;
  }
  .other__colleges__container .customSlider .customSliderCards{
    display: flex;
}
.other__colleges__container .four-cardDisplay .sliderCardInfo{
    flex-shrink: 0;
}
.viewAllDiv{
    min-height: 100%;
}
.viewAllDiv .viewAllIcon{
  display: block !important;
}
.one-to-four{
  display: none;
}
.five-to-seven{
  display: none;
}

.show_form{
  display: flex;
 flex-direction: column;
 align-items: center;
}
.errorHtml{
  color: red;
}
.filtered__colleges__list .cta__div button{
  width: 100%;
  height: 100%;
}
.cta__banner .cta__div button{
   padding: 8px 0px;
}
.latest__title{
  font-size: 14px;
    color: #282828;
    font-weight: 500;
    line-height: 21px;
}
.top__ads{
  padding: 0px;
}
.sort__row__container .sortBy__select2__container select{
  background-position: 95% 6px !important;
  background-color: #FFFFFF;
}
.filtered__colleges__list .college__detail__row .college__name{
  margin-bottom: 4px;
  }
.filtered__colleges__list .highlights__grid{
  row-gap: 16px
  }
  .search-remove{
  position: absolute;
  top: 15px;
  right: 10px;
}

.error__position{
  position: relative;
  top:-20px;
}

.college__Landing__New{
  margin-top: 30px;
}
.feedback-text{
  text-align: center;
}
.feedback__container .lead__form__logo{
  position: absolute;
  top: 20px;
  left: 20px;
}

.college__rating{
  font-weight: 700;
  }
  .hover__tooltop {
    position: relative;
    display: inline;
    cursor: pointer;
    font-weight: 700;
    color: #3D8FF2;
  }
  .hover__tooltop:hover .hover__card {
    display: block;
  }
  
 
  .exam__accepted__div{
    display: block !important;
  }
  .review__star__icon{
    margin-right: 4px;
    }
 
  .hover__tooltop .hover__card::before{
    position: absolute;
      content: "";
      right: 20%;
      transform: rotate(45deg) translateX(-20%);
      width: 12px;
      height: 12px;
      box-shadow: -1px -1px 0 0 #e6e5e5;
      background-color: #fff;
      top: -5px;
      margin: 0 0 0 -.25em;
  }
  .hover__tooltop .hover__card{
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .32), 0 0 2px 0 rgba(0, 0, 0, .16);
    flex-wrap: wrap;
    gap: 5px;
    min-width: 150px;
    background-color: #fff;
    color: #3d8ff2;
    text-align: center;
    padding: 10px;
    margin-top: 10px;
    border-radius: 10px;
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 0;
    transform: translateX(-80%);
    transition: opacity 0.3s;
    display: none;
    font-weight: 400;
  }
  .no__value{
    padding-left: 35%;
   }
  
   .exam__accepted__div{
    min-width: 151px;
    }

@media (max-width: 1023px) {
  
  .college__Landing__New {
    margin: 0 -10px;
  }
  .also__explore {
    padding: 25px 20px;
  }
  .also__explore h2 {
    line-height: 24px;
    background-color: transparent;
    margin-bottom: 16px;
    padding: 0;
  }
  .also__explore .tab__list li {
    white-space: nowrap;
    padding: 7px 24px;
  }
  .also__explore .tab__sections {
    padding: 18px 0 26px 21px;
  }
  .also__explore .tab__sections .tab__section {
    overflow: auto;
  }
  .also__explore .tab__subsection__list {
    padding: 18px 10px 18px 20px;
    max-width: 240px;
    flex-shrink: 0;
  }
  .news__update__section {
    margin-top: 12px;
    padding: 27px 20px 23px 20px;
  }
  .news__update__section h2 {
    background-color: transparent;
    padding: 0;
    margin-bottom: 15px;
  }
  .news__update__section .latest__news__tabs {
    margin-bottom: 30px;
    gap: 10px;
  }
  .news__update__section .recent__latest__section {
    gap: 22px;
    max-height: 300px;
    overflow: auto;
  }
  .news__update__section .news__card {
    max-width: 100%;
  }
  .faq_section {
    padding: 30px 20px;
  }
  .faq_section h2 {
    background-color: transparent;
    padding: 0;
    margin-bottom: 16px;
  }
  .other__colleges__container {
    padding: 30px 0 30px 20px;
  }
  .other__colleges__container h2 {
    padding: 0;
    background-color: transparent;
    margin-bottom: 20px;
  }
  .feedback__section {
    padding: 44px 20px 36px 20px;
    overflow: hidden;
    margin: 20px -20px 0 -20px;
  }
  .feedback__section p {
    text-align: center;
  }
  .feedback__section p:last-of-type {
    margin-top: 8px;
    margin-bottom: 20px;
  }
  .feedback__section ul {
    gap: 6px;
  }
  .feedback__section ul li {
    width: 26px;
    height: 26px;
    flex-shrink: 0;
  }
  .feedback__section .feedback__image {
    right: -28px;
    top: -7px;
    transform: rotate(-21deg);
    z-index: 0;
  }
  .cta__banner {
    padding: 37px 30px;
    text-align: center;
  }
  .cta__banner img {
    display: none;
  }
  .filter__by__exam {
    padding: 30px 20px;
  }
  .filter__by__exam .heading__row {
    margin-bottom: 25px;
  }
  .filter__by__exam .exam__list {
    row-gap: 10px;
  }
  .college__Landing__Hero__Section {
    padding: 18px 20px;
    border-radius: 0 !important;
  }
  .college__Landing__Hero__Section .college__Landing__Hero__Section__Heading {
    font-size: 18px;
    font-weight: 400;
    line-height: 38px;
    margin-bottom: 10px;
  }
  .college__Landing__Hero__Section .updated-info.row {
    border-radius: 4px;
    background: var(--color-white);
  }
  .college__Landing__Hero__Section .updated-info.row img {
    float: left;
  }
  .college__Landing__Hero__Section .updated-info.row .authorAndDate .authorName {
    font-size: 12px;
  }
  .college__Landing__Hero__Section .updated-info.row .authorAndDate p {
    font-size: 12px;
  }
  .college__Landing__Hero__Section .updated-info.row .authorAndDate .verifiedBlueTickIcon {
    transform: scale(0.6);
    margin-left: 0px;
    vertical-align: middle;
  }
  .desktop__filter {
    display: none;
  }
  .mobile__white__bg {
    background-color: #fff;
    padding: 30px 20px;
    padding-top: 20px;
  }
  .mobile__white__bg .mobile__clear__filter {
    font-size: 14px;
    color: #FF4E53;
    font-weight: 600;
    line-height: 26px;
    cursor: pointer;
    display: block;
  }
  .sort__row__container {
    margin: 8px 0;
    display: flex;
  }
  .sort__row__container.mobile__version {
    margin-bottom: 20px;
  }
  .sort__row__container .filtered__college_count {
    font-size: 16px;
  }
  .sort__row__container .filter__button {
    padding: 4px 7px;
    background-color: #fff;
    border: 1px solid #D8D8D8;
    border-radius: 2px;
    flex-shrink: 0;
    cursor: pointer;
    color: #282828;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .sort__row__container .filter__button .number__of__filters {
    border-radius: 50%;
    background-color: #ff4e53;
    color: #fff;
    width: 14px;
    height: 14px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-size: 10px;
    font-weight: 700;
    line-height: 5px;
  }
  .filtered__colleges__list .college__card__new {
    padding: 22px 17px;
  }
  .filtered__colleges__list .college__card__new .college__detail__grid {
    display: flex;
    flex-direction: row-reverse;
  }
  .filtered__colleges__list .college__card__new .college__detail__grid .college__image {
    flex-shrink: 0;
  }
  .filtered__colleges__list .college__card__new .college__detail__grid .college__rating {
    padding: 0;
    width: 100%;
    display: inline-block;
  }
  .filtered__colleges__list .college__card__new .college__detail__grid .college__rating::before {
    display: none;
  }
  .filtered__colleges__list .college__card__new .highlight__cta__row {
    flex-direction: column;
    margin-top: 15px;
  }
  .filtered__colleges__list .highlights__grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 10px;
    row-gap: 12px;
    margin-bottom: 16px;
  }
  .filtered__colleges__list .cta__grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    grid-template-rows: none;
  }
  .scrollLeft,
  .scrollRight {
    display: none !important;
  }
  .other__colleges__container .four-cardDisplay .sliderCardInfo {
    width: 100%;
  }
  .extended__footer {
    padding: 30px 20px;
    flex-direction: column;
    gap: 12.5px;
  }
  .extended__footer .extended__footer__section:not(:last-child)::after {
    display: inline-block;
    content: "";
    width: 100%;
    height: 1px;
    background-color: #d8d8d8;
    opacity: 0.8;
  }
  .extended__footer .accordian__label {
    position: relative;
  }
  .extended__footer .accordian__label.downAngle::after {
    transform: rotate(90deg);
  }
  .extended__footer .accordian__label:after {
    content: " ";
    background: url("../../images/master_sprite.webp");
    position: absolute;
    right: 17px;
    top: 12px;
    background-position: -38px -119px;
    width: 6px;
    height: 10px;
    -webkit-transition: 0.2s ease;
    transition: 0.2s ease;
  }
  .feedback__form {
    max-width: 100%;
    height: 100%;
    padding: 35px 25px;
  }
  .feedback__form .rating__buttons {
    gap: 5px;
    margin-bottom: 20px;
  }
  .feedback__form .rating__buttons li {
    width: 26px;
    height: 26px;
  }
  .feedback__form .text3 {
    padding-bottom: 18px;
    margin-bottom: 20px;
  }
  .feedback__form .feedback__buttons {
    flex-wrap: wrap;
  }
  .feedback__form .write__feedback {
    min-height: 75px;
  }
  .news__update__section .load__more__button {
    bottom: 5px;
  }
  .news__update__section .load__more__row {
    bottom: 0;
  }
  .news__update__section .load__more__row::before {
    top: -95px;
    height: 100px;
  }

     /* mobile cta bottom */
     .filter-college-scholership{
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      z-index: 10;
      border: 10px solid var(--color-white) !important;
}
/* mobile sort and filter button */
.sort__row__container .filter__button{
      padding: 8px 7px;
      font-weight: 600;
}
.sort__row__container .sortBy__select2__container{
      height: auto;
}
.sort__row__container .sortBy__select2__container select{
      background-position: 95% 6px !important;
}
/* MOBILE FILTER CSS */
.mobileFilterSection {
  display: none;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 13;
  width: 100%;
  height: 100%;
  background: var(--color-white);
}
.mobileFilterSection .mobileFilterHeading {
  background: var(--color-white);
  padding: 12px 16px;
  color: var(--primary-font-color);
  line-height: 20px;
  font-size: 14px;
  text-transform: uppercase;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  border-bottom: var(--border-line);
}
.mobileFilterSection .mobileFilterHeading span {
  color: var(--color-red);
}
.filterTab {
  display: flex;
  height: 100%;
}
.filterTab .tabs {
  flex-basis: 140px;
  padding: 0;
  margin: 0;
}
.filterTab .tabs li {
  padding: 10px 16px;
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  position: relative;
}
.filterTab .tabs li.tab-link.current {
  font-weight: var(--font-semibold);
  background: #fafbfc;
}
.filterContentDiv {
  flex-basis: calc(100% - 140px);
  background: #fafbfc;
  padding: 10px;
}
.filterContentDiv form div {
  padding-bottom: 8px;
}
.filterContentDiv li,
.filterContentDiv span,
.filterContentDiv label {
  color: #787878;
  line-height: 24px;
  font-size: 14px;
  padding-left: 8px;
}
.filterContentDiv input[type=checkbox] {
  width: auto;
  display: inline-block;
  margin: 0;
  height: auto;
  width: 16px;
  height: 16px;
  padding: 0;
  vertical-align: middle;
}
.filterContentDiv input[type=text] {
  border: var(--border-line);
  padding: 8px 16px;
  border-radius: 3px;
  background: var(--color-white);
}
.filterSearch ul {
  padding: 0;
  margin: 0;
}
.filterSearch ul li {
  list-style-type: none;
}
.filterOptionDiv {
  display: -webkit-box;
  display: flex;
  position: fixed;
  width: 100%;
  bottom: 0;
  z-index: 3;
}
.filterOptionDiv button {
  flex-basis: 50%;
  font-size: 14px;
  line-height: 20px;
  border: 1px solid var(--color-red);
  background-color: #fafbfc;
  padding: 11px;
  font-weight: var(--font-semibold);
  color: var(--color-red);
  text-align: center;
  background: var(--color-white);
}
.filterOptionDiv button.applyFilter {
  background: var(--color-red);
  color: var(--color-white);
}

/*CLS css*/
.all-college-ajax{
  min-height: auto !important;
  }
  /*Filter Search UL*/
  .filterSearch ul{
    height: calc(100vh - 155px);
    overflow: auto;
  }
  /*Bottom CTA*/
  .mobile-bottom-cta{
    display: none;
    justify-content: center;
    height: 60px;
    padding: 0;
  }

  /* Applied Filter*/
  .filterTab .tabs li.appliedFilter:before {
    content: "";
    position: absolute;
    width: 5px;
    height: 5px;
    background: #ff4e53;
    top: 50%;
    transform: translate(0, -50%);
    right: 5px;
    border-radius: 50%;
  }

  /*Nirf rank and review star css fix*/
  .detail__list__mobile {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: center;
  }
  .college__affiliation {
    flex-basis: 30%;
  }
  .college__detail__grid {
    position: relative;
  }
  .college__rank {
    position: absolute !important;
    right: 0;
    bottom: 1px;
  }
  .college__rank::before {
    width: 0 !important;
  }
  .like__compare__grid .heart__icon, .like__compare__grid .compare__icon {
    vertical-align: sub;
  }

  .college__location{
    flex-basis: 60%;
  }
  .filter__selected__container{
    flex-wrap: wrap;
    }
    .filter__selected__container .filterDiv {
            padding: 0;
        display: flex;
        flex-wrap: wrap;
        row-gap: 10px;
    }

  .feedback__form .feedback__buttons li{
    flex-grow: unset;
  }

  .carousel-container {
    overflow: hidden;
    width: 290px;
}
.carousel-track{
    overflow: auto;
    max-width: 100%;
}
.carousel-dots{
    display: none;
}
.cta__banner .cta__div.primaryBtn {
  max-width: 100%;
}
.filtered__colleges__list .cta__div, .filtered__colleges__list .cta__div button{
  padding: 4px 0px;
}

.top__ads{
  margin-top: 15px;
  padding: 0px 15px;
}

.college__Landing__New{
  margin-top: 0;
}
.list__style__rating{
  margin:  0px !important;
  padding: 0px !important;
}
.list__style__rating:before{
  display: none !important;
}




.college__rank{
  background: linear-gradient(277.79deg, #0966C2 39.25%, #114272 97.59%);
    color: white;
    padding: 1px 2px;
    border-radius: 3px;
    min-width: 60px;
    margin-left: 10px;
    display: inline-flex !important;
    justify-content: center;
    align-items: center;
    font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
    font-size: 12px;
    font-weight: 700;
    line-height: 20px;
}

.filter__header__sticky{
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 4;
  padding: 10px;
  background-color: #fff;
  box-shadow: 0 4px 10px rgba(16, 24, 64, .05);
  margin-top: 0px;
}

}

/*# sourceMappingURL=collegeLandingNew.css.map */
