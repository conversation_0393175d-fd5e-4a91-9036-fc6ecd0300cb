iframe {
    border: 0;
  }
  * {
    letter-spacing: normal;
    color: var(--primary-font-color);
  }
  table td span {
    color: var(--primary-font-color) !important;
  }
  .table-responsive {
    margin-bottom: 10px;
  }
  table caption {
    caption-side: bottom;
    margin-top: 20px;
    margin-bottom: 0;
    font-size: 15px;
    line-height: 24px;
    color: var(--primary-font-color);
  }
  .tab-content.activeLink {
    display: block;
  }
  .breadcrumbDiv ul li a {
    font-weight: 500;
  }
  .p-0 {
    padding: 0;
  }
  .pb-0 {
    padding-bottom: 0;
  }
  .text-center {
    text-align: center;
  }
  .pageInfo {
    max-height: 200px;
  }
  .courseSprite {
    display: inline-block;
    background: url(../../images/course_sprite.webp);
    text-align: left;
    overflow: hidden;
    margin-right: 16px;
    width: 68px;
    height: 67px;
    vertical-align: middle;
    cursor: pointer;
  }
  .engineering {
    background-position: -123px -28px;
  }
  .management {
    background-position: -394px -117px;
  }
  .science {
    background-position: -305px -291px;
  }
  .pharmacy {
    background-position: -215px -28px;
  }
  .law {
    background-position: -305px -117px;
  }
  .education {
    background-position: -305px -200px;
  }
  .dental {
    background-position: -123px -292px;
  }
  .medical {
    background-position: -305px -27px;
  }
  .agriculture {
    background-position: -28px -292px;
  }
  .design {
    background-position: -27px -28px;
  }
  .commerce {
    background-position: -214px -202px;
  }
  .architecture {
    background-position: -215px -292px;
  }
  .arts {
    background-position: -393px -27px;
  }
  .paramedical {
    background-position: -28px -203px;
  }
  .computer {
    background-position: -122px -203px;
  }
  .mass-communication {
    background-position: -215px -116px;
  }
  .hotel-management {
    background-position: -27px -115px;
  }
  .aviation {
    background-position: -395px -202px;
  }
  .veterinary {
    background-position: -122px -115px;
  }
  .animation {
    background-position: -400px -291px;
  }
  .vocational-courses {
    background-position: -27px -374px;
  }
  .sortIcon {
    width: 20px;
    height: 20px;
    background-position: 231px -378px;
  }
  .userIconWhite {
    width: 27px;
    height: 27px;
    background-position: 537px -308px;
    margin-right: 5px;
    vertical-align: middle;
  }
  .downloadIconred {
    width: 19px;
    height: 18px;
    background-position: 535px -341px;
    vertical-align: middle;
  }
  .urlIcon {
    width: 13px;
    height: 14px;
    background-position: 302px -375px;
    vertical-align: middle;
  }
  .badgeIcon {
    width: 24px;
    height: 28px;
    background-position: 167px -262px;
    margin-right: 15px;
    margin-top: -10px;
  }
  .modeIcon,
  .courseDuratioIcon,
  .feesIcons {
    width: 20px;
    height: 20px;
    vertical-align: middle;
  }
  .feesIcons {
    background-position: 52px -346px;
  }
  .modeIcon {
    background-position: 52px -274px;
  }
  .courseDuratioIcon {
    background-position: 52px -299px;
  }
  .courseHeroSection {
    background: var(--color-white);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    border: var(--border-line);
    background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
    margin-top: 10px;
    padding: 28px 30px;
  }
  .courseHeroSection h1 {
    font-size: 24px;
    line-height: 38px;
    padding-bottom: 20px;
    font-weight: 400;
  }
  .courseHeroSection input#autoComplete {
    max-width: 480px;
    padding: 9px;
    height: auto;
    padding-left: 40px;
    font-size: 14px;
    line-height: 24px;
    width: 100%;
    border-radius: 3px;
    border: var(--border-line);
    background: url(../../images/search-icon.png) no-repeat;
    background-position: 15px 48%;
    color: var(--primary-font-color);
  }
  .courseHeroSection input#autoComplete::-moz-placeholder {
    color: #989898;
    opacity: 1;
    font-weight: 400;
  }
  .courseHeroSection input#autoComplete:-ms-input-placeholder {
    color: #989898;
    opacity: 1;
    font-weight: 400;
  }
  .courseHeroSection input#autoComplete::placeholder {
    color: #989898;
    opacity: 1;
    font-weight: 400;
  }
  .courseHeroSection input#autoComplete:focus {
    background-size: auto;
    outline: none;
  }
  .courseHeroSection input#autoComplete:focus::-webkit-input-placeholder {
    padding-left: 0;
    font-size: 14px;
  }
  .courseHeroSection .searchBar {
    margin-bottom: 20px;
    position: relative;
  }
  .courseHeroSection #autoComplete_list {
    max-width: 480px;
    border-radius: 0;
    margin: 0;
    position: absolute;
    max-height: 205px;
    overflow: auto;
    width: 100%;
    background: #fff;
    z-index: 3;
    left: 0;
    top: 46px;
  }
  .courseHeroSection #autoComplete_list li.no_result {
    list-style-type: none;
  }
  .courseHeroSection .autoComplete_list {
    padding: 0;
    margin: 0;
    max-width: 480px;
    border: var(--border-line);
    position: absolute;
    z-index: 3;
    background: #fff;
    width: 100%;
  }
  .courseHeroSection .autoComplete_list li {
    list-style-type: none;
    padding: 3px 5px;
    cursor: pointer;
  }
  .courseHeroSection .autoComplete_list li:hover {
    background: rgba(0, 0, 0, 0.15);
  }
  .courseHeroSection .autoComplete_list a {
    text-decoration: none;
  }
  .courseHeroSection .col-md-5,
  .courseHeroSection .col-md-4 {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
  }
  .courseHeroSection p {
    font-size: 14px;
    line-height: 24px;
    padding-bottom: 10px;
    font-weight: 500;
  }
  .courseHeroSection .primaryBtn {
    padding: 5px 20px;
  }
  .pageData {
    background: var(--color-white);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    border: var(--border-line);
  }
  .pageData h2 {
    font-size: 18px;
    line-height: 28px;
    padding: 8px 20px;
    margin: 0;
    margin-bottom: 20px;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    background: #f5f5f5;
    position: relative;
  }
  .pageData h2 a {
    font-size: 14px;
    line-height: 24px;
    color: var(--color-red);
    font-weight: 500;
    text-transform: capitalize;
  }
  .pageData p,
  .pageData li,
  .pageData a {
    font-size: 15px;
    line-height: 26px;
  }
  .pageData p {
    color: var(--primary-font-color);
    padding-bottom: 15px;
  }
  .pageData h3 {
    font-size: 17px;
    line-height: 24px;
    padding-bottom: 10px;
    color: var(--primary-font-color);
  }
  .pageData h4 {
    padding-bottom: 10px;
    line-height: 24px;
    font-weight: 500;
  }
  .pageData button {
    background: var(--color-red);
    font-size: 14px;
    line-height: 20px;
    color: var(--color-white);
    padding: 8px;
    font-weight: var(--font-semibold);
    border-radius: 3px;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    border: none;
    transition: 0.2s ease;
    outline: none;
    margin-bottom: 10px;
  }
  .pageData ul {
    margin: 10px 0;
  }
  .pageData ul li {
    position: relative;
    list-style-type: none;
  }
  .pageData ul li:before {
    content: "";
    background: url(../../images/master_sprite.webp);
    width: 12px;
    height: 17px;
    position: absolute;
    left: -19px;
    top: 5px;
    background-position: 651px -71px;
    z-index: 1;
  }
  .pageData .primaryBtn,
  .pageData a.primaryBtn,
  .pageData button.primaryBtn {
    padding: 5px 15px;
  }
  .readMoreDiv {
    box-shadow: none;
    border: var(--border-line);
    border-top: none;
  }
  .readMoreDiv .readMoreInfo {
    text-transform: capitalize;
  }
  .readMoreDiv.pageInfo {
    max-height: 240px;
  }
  .courseInfo .col-md-4 {
    display: block;
  }
  .courseInfo p {
    font-size: 14px;
    line-height: 24px;
    font-weight: 500;
    padding: 0;
  }
  .courseInfo p:last-child {
    color: #989898;
    font-weight: 400;
  }
  .updated-info.row {
    margin: 0;
    align-items: center;
    margin-top: 20px;
    line-height: 20px;
    font-size: 14px;
  }
  .updated-info img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 6px;
    vertical-align: middle;
  }
  .updated-info p {
    display: inline-block;
    padding: 0;
    font-weight: 400;
  }
  .updated-info .updatedBy a {
    color: var(--anchor-textclr);
  }
  .updated-info .updatedBy a.authorName {
    color: var(--primary-font-color);
  }
  .updated-info .updatedBy a:hover,
  .updated-info .authorName:hover {
    color: var(--anchor-textclr);
  }
  .pageRedirectionLinks {
    padding: 0 10px;
    border: var(--border-line);
    background: var(--color-white);
    margin-bottom: 20px;
    border-radius: 4px;
    position: relative;
  }
  .pageRedirectionLinks .btn_right,
  .pageRedirectionLinks .btn_left {
    position: absolute;
    width: 48px;
    height: 44px;
    background-color: #fff;
    text-align: center;
    vertical-align: middle;
    overflow: hidden;
    top: 0;
    cursor: pointer;
  }
  .pageRedirectionLinks .btn_left {
    left: 0;
  }
  .pageRedirectionLinks .btn_right {
    right: 0;
  }
  .pageRedirectionLinks ul {
    margin: 0;
    padding: 0;
    white-space: nowrap;
    overflow: auto;
  }
  .pageRedirectionLinks ul::-webkit-scrollbar {
    display: none;
  }
  .pageRedirectionLinks ul li {
    font-size: 14px;
    line-height: 24px;
    color: #787878;
    margin: 0 5px;
    list-style-type: none;
    display: inline-block;
  }
  .pageRedirectionLinks ul li .activeLink {
    color: var(--color-red);
    border-bottom: 3px solid var(--color-red);
    font-weight: 500;
    padding: 10px 0;
    padding-bottom: 11px;
  }
  .pageRedirectionLinks ul li a {
    color: #787878;
    text-decoration: none;
    display: inline-block;
    padding: 10px 0;
  }
  .getSupport {
    padding: 19px;
    background: var(--color-white);
    border: var(--border-line);
    border-radius: 3px;
    margin-bottom: 20px;
  }
  .getSupport .row {
    margin: 0;
    align-items: center;
    flex-wrap: nowrap;
    margin-bottom: 20px;
  }
  .getSupport img {
    width: 80px;
    height: 80px;
    margin-right: 20px;
  }
  .getSupport p {
    font-size: 18px;
    line-height: 26px;
  }
  .getSupport button {
    width: 161px;
    border-radius: 3px;
    font-size: 14px;
    line-height: 24px;
    padding: 6px;
    text-align: center;
    color: var(--color-white);
    font-weight: var(--font-bold);
    border: none;
  }
  .getSupport button.talkToExpert {
    background: var(--topheader-bg);
  }
  .getSupport button.applyNow {
    background: var(--color-red);
    margin-left: 15px;
  }
  .sideBarSection {
    background: var(--color-white);
    border: var(--border-line);
    border-radius: 4px;
    margin-bottom: 20px;
  }
  .sideBarSection .row {
    margin: 0;
    align-items: center;
  }
  .sideBarSection .sidebarHeading {
    background: #d8d8d8;
    padding: 10px 20px;
    font-size: 16px;
    line-height: 24px;
    margin: 20px;
    margin-bottom: 6px;
    font-weight: 500;
  }
  .sideBarSection .sidebarTextLink {
    flex-basis: calc(100% - 92px);
  }
  .sideBarSection .sidebarTextLink p {
    font-size: 14px;
    line-height: 24px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
  }
  .sideBarSection .sidebarTextLink .cardText {
    color: var(--primary-font-color);
  }
  .sideBarSection .sidebarTextLink .subText {
    color: #989898;
  }
  .sideBarSection p.listCard {
    font-weight: 500;
    font-size: 15px;
    line-height: 24px;
  }
  .sideBarSection .listCard {
    display: flex;
    padding: 10px 20px;
    border-bottom: var(--border-line);
  }
  .sideBarSection .listCard:hover .cardText,
  .sideBarSection .listCard:hover .subText {
    color: var(--anchor-textclr);
  }
  .sideBarSection .listCard:last-child {
    border-bottom: none;
  }
  .sideBarSection .sidebarImgDiv {
    flex-basis: 72px;
    margin-right: 20px;
  }
  .sideBarSection .sidebarImgDiv img {
    display: block;
    margin: 0 auto;
    width: 100%;
  }
  .sideBarSection .applyText {
    color: var(--anchor-textclr);
  }
  .sidebarAds .appendAdDiv {
    width: 300px;
    height: 250px;
    display: block;
    margin: 0 auto;
    margin-bottom: 20px;
  }
  .ohterCategoryArticles {
    padding-bottom: 10px;
  }
  .ohterCategoryArticles .row {
    margin: 0;
    flex-wrap: nowrap;
    overflow: auto;
  }
  .ohterCategoryArticles .row::-webkit-scrollbar {
    display: none;
  }
  .ohterCategoryArticles .categoryArticles {
    flex-basis: 8%;
    margin-right: 20px;
    margin-bottom: 10px;
  }
  .ohterCategoryArticles .categoryArticles:hover p {
    color: var(--anchor-textclr);
  }
  .ohterCategoryArticles .categoryArticles:last-child {
    margin-right: 0;
  }
  .ohterCategoryArticles .categoryArticlesImg {
    text-align: center;
    padding: 10px;
    border: var(--border-line);
    margin-bottom: 7px;
  }
  .ohterCategoryArticles .categoryArticlesImg .courseSprite {
    margin: 0;
  }
  .ohterCategoryArticles p {
    padding: 0;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
  }
  .ohterCategoryArticles a {
    text-decoration: none;
  }
  .authorName {
    font-weight: var(--font-semibold);
  }
  .courseCategoryPage h1 {
    font-size: 24px;
    line-height: 38px;
    padding-bottom: 20px;
    font-weight: 500;
    font-weight: 400;
    padding-top: 10px;
  }
  .courseCategoryPage h2 {
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 20px;
    font-weight: 500;
  }
  .courseCategoryPage .updated-info {
    margin-top: 0;
    margin-bottom: 20px;
  }
  .courseCategoryPage .pageInfo {
    max-height: 180px;
  }
  .courseHeroSection.pageData {
    margin-top: 0;
  }
  .courseHeroSection.pageData p {
    font-weight: 400;
    font-size: 15px;
    line-height: 24px;
  }
  .courseCategoryType {
    padding: 9px 10px;
    padding-bottom: 8px;
    border: var(--border-line);
    background: var(--color-white);
    margin-bottom: 20px;
    border-radius: 4px;
    color: var(--primary-font-color);
    position: relative;
  }
  .courseCategoryType .btn_left,
  .courseCategoryType .btn_right {
    height: 43px;
  }
  .courseCategoryType ul {
    margin: 0;
    padding: 0;
    white-space: nowrap;
    overflow: auto;
  }
  .courseCategoryType ul li {
    font-size: 13px;
    line-height: 20px;
    margin: 0 10px;
    list-style-type: none;
    display: inline-block;
    text-transform: uppercase;
    padding-bottom: 2px;
    color: var(--primary-font-color);
    cursor: pointer;
  }
  .courseCategoryType ul li.activeLink {
    color: var(--color-red);
    border-bottom: 3px solid var(--color-red);
    font-weight: 500;
  }
  .courseCategoryCard {
    background: var(--color-white);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    border: var(--border-line);
  }
  .courseCategoryCard .row {
    margin: 0;
  }
  .courseCategoryCard h3 {
    font-size: 18px;
    line-height: 24px;
    font-weight: 400;
    flex-basis: calc(100% - 141px);
  }
  .courseCategoryCard h3 a {
    color: var(--primary-font-color);
  }
  .courseCategoryCard h3 a:hover {
    color: var(--anchor-textclr);
  }
  .courseCategoryCard .row {
    justify-content: space-between;
    align-items: center;
  }
  .courseCategoryCard .row:first-child {
    border-bottom: var(--border-line);
    padding-bottom: 19px;
    margin-bottom: 10px;
  }
  .courseCategoryCard ul {
    padding: 0;
    margin: 0;
    display: flex;
  }
  .courseCategoryCard ul li {
    font-size: 14px;
    line-height: 24px;
    padding-right: 8px;
    margin-right: 8px;
    position: relative;
    border-right: var(--border-line);
    display: inline-block;
  }
  .courseCategoryCard ul li:last-child {
    margin-right: 0;
    border-right: 0;
  }
  .courseCategoryCard ul li .spriteIcon {
    margin-right: 5px;
  }
  .courseCategoryCard ul li span {
    color: #787878;
    font-size: 12px;
    line-height: 24px;
    margin-right: 5px;
  }
  .courseCategoryCard ul li a {
    font-weight: 500;
  }
  .courseCategoryCard ul li a span {
    font-size: 14px;
    color: #3d8ff2;
    margin: 0;
  }
  .customLogo .sidebarImgDiv {
    flex-basis: 56px;
    margin-right: 16px;
    min-height: auto;
  }
  .customLogo .sidebarTextLink {
    flex-basis: calc(100% - 72px);
  }
  .customLogo .sidebarTextLink p {
    -webkit-line-clamp: 2;
  }
  .customLogo .listCard {
    border-bottom: none;
    padding: 8px 20px;
  }
  .customLogo p.listCard {
    border-bottom: var(--border-line);
    padding: 10px 20px;
    font-size: 16px;
  }
  .newsSidebarSection,
  .articleSidebarSection {
    border-radius: 4px;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
    margin-bottom: 20px;
    background: #fff;
  }
  .newsSidebarSection ul,
  .articleSidebarSection ul {
    margin: 0;
    padding: 0;
    display: flex;
  }
  .newsSidebarSection ul li,
  .articleSidebarSection ul li {
    list-style-type: none;
    flex-basis: 50%;
    text-align: center;
    font-size: 14px;
    line-height: 24px;
    color: #787878;
    cursor: pointer;
    padding: 12px 5px;
    padding-bottom: 9px;
    border-bottom: var(--border-line);
  }
  .newsSidebarSection ul li.activeLink,
  .articleSidebarSection ul li.activeLink {
    color: var(--color-red);
    border-bottom: 3px solid var(--color-red);
    font-weight: 500;
  }
  .newsSidebarSection .tab-content.activeLink,
  .articleSidebarSection .tab-content.activeLink {
    display: block;
  }
  .recentnewsList {
    padding: 20px;
  }
  .recentnewsList .listCard:last-child .recentnewsDiv.row {
    margin: 0;
    padding: 0;
    border: none;
  }
  .recentnewsDiv.row {
    margin: 0;
    flex-wrap: nowrap;
    margin-bottom: 10px;
    border-bottom: var(--border-line);
    padding-bottom: 10px;
    align-items: center;
  }
  .sidebarImgDiv {
    flex-basis: 96px;
    margin-right: 16px;
    display: grid;
    min-height: 72px;
  }
  .sidebarImgDiv img {
    width: 96px;
    max-height: 72px;
    display: block;
    align-self: center;
  }
  .recentnewsDivText,
  .recentArticlesDivText {
    flex-basis: calc(100% - 96px - 16px);
  }
  .recentnewsDivText .sidebarTextLink,
  .recentArticlesDivText .sidebarTextLink {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: 500;
    text-decoration: none;
    display: -webkit-box;
    font-weight: 400;
    padding: 0;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    border: none;
  }
  .recentnewsDiv.row:hover .sidebarTextLink {
    text-decoration: underline;
    color: var(--anchor-textclr);
  }
  .otherCategorySection {
    position: relative;
  }
  .otherCategorySection .scrollRight {
    right: -20px;
  }
  .otherCategorySection .scrollLeft {
    top: 50%;
    left: -20px;
  }
  .fixedRedirectionLinks {
    position: fixed;
    top: 0;
    z-index: 2;
    width: 100%;
    max-width: 1206px;
    margin: 0 auto;
  }
  .brochureBtn {
    display: block;
    text-align: center;
    margin: 0 -10px;
    margin-bottom: 10px;
    border: 10px solid var(--color-white) !important;
    transition: none !important;
    padding: 0;
  }
  .customSlider {
    position: relative;
  }
  .customSlider .scrollLeft {
    top: 50%;
    left: -20px;
  }
  .customSlider .scrollRight {
    right: -20px;
  }
  .customSlider .customSliderCards {
    display: block;
    white-space: nowrap;
    overflow: auto;
  }
  .customSlider .sliderCardInfo {
    border-radius: 4px;
    border: var(--border-line);
    margin-right: 14px;
    vertical-align: middle;
    min-height: 340px;
  }
  .four-cardDisplay .sliderCardInfo {
    width: 23.8%;
    display: inline-block;
    white-space: initial;
  }
  .customSlider .sliderCardInfo:last-child {
    margin-right: 0;
  }
  .viewAllDiv {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 331px;
  }
  .viewAllDiv a {
    font-size: 14px;
    line-height: 24px;
    color: var(--color-red);
    text-align: center;
    font-weight: 600;
  }
  .viewAllDiv a {
    color: var(--color-red);
  }
  .customSlider .sliderCardInfo a {
    text-decoration: none;
  }
  .customSlider .sliderCardInfo img {
    display: block;
    height: 207px;
    width: 100%;
    border-bottom: var(--border-line);
  }
  .customSlider .sliderCardInfo .textDiv {
    padding: 20px;
  }
  .customSlider .sliderCardInfo p {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: var(--font-semibold);
    padding-bottom: 0;
    white-space: initial;
  }
  .customSlider .sliderCardInfo .widgetCardHeading {
    font-size: 14px;
    padding-bottom: 0;
    min-height: 24px;
    margin-bottom: 5px;
    font-weight: 500;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
  }
  .customSlider .sliderCardInfo .subText {
    color: #989898;
    font-weight: 400;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    padding: 0;
    padding-bottom: 5px;
    position: relative;
  }
  .viewAllStream {
    font-size: 14px;
    line-height: 24px;
    display: block;
    color: var(--color-red);
    background: var(--color-white);
    border: 1px solid #d8d8d8;
    border-radius: 4px;
    margin: 0 auto;
    margin-bottom: 20px;
    padding: 10px 40px;
    font-weight: 500;
    text-align: center;
  }

  .getSupport {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 10px;
    border-radius: 0;
    z-index: 5;
    gap: 18px;
    font-size: 15px;
    font-weight: 400;
    line-height: 22px;
    color: #282828;
    align-items: center;
    justify-content: center;
    display: none;
  }
  .getSupport .button__row__container {
      display: flex;
      gap: 13px;
      align-items: center;
  }
  .getSupport .getSupport__subheading {
      display: inline-block;
  }

  @media (max-width: 1023px) {
    .otherCategorySection .scrollLeft,
    .otherCategorySection .scrollRight {
      display: none !important;
    }
    .horizontalRectangle,
    .verticleRectangle,
    .squareDiv {
      margin-bottom: 10px;
    }
    .courseHeroSection {
      margin-top: -157px !important;
      padding: 20px;
      padding-bottom: 14px;
      margin-bottom: 10px;
      background: var(--color-white);
    }
    .courseHeroSection h1 {
      font-size: 18px;
      line-height: 24px;
      padding-bottom: 10px;
    }
    .courseHeroSection .searchBar {
      margin-bottom: 10px;
    }
    .pageData,
    .reviewsSection {
      padding: 10px;
      margin-bottom: 10px;
      word-break: break-word;
    }
    .pageData h2,
    .reviewsSection h2 {
      font-size: 15px;
      line-height: 24px;
      padding: 8px 10px;
      margin-bottom: 10px;
    }
    .pageData h2 a,
    .reviewsSection h2 a {
      display: none;
    }
    .pageData p,
    .reviewsSection p {
      padding-bottom: 10px;
    }
    .pageData ul,
    .reviewsSection ul {
      margin: 10px 0;
      padding-left: 30px;
    }
    .readMoreDiv {
      margin-bottom: 10px;
    }
    .getSupport {
      position: fixed;
      display: block;
      bottom: 0;
      left: 0;
      width: 100%;
      margin: 0;
      padding: 10px;
      border-radius: 0;
      z-index: 1;
      background: var(--color-white);
      height: 58px;
    }
    .getSupport button {
      width: 49%;
      border-radius: 2px;
      font-size: 13px;
      padding: 6px 4px;
      text-align: center;
      color: var(--color-white);
      font-weight: var(--font-bold);
      border: none;
      line-height: 24px;
    }
    .getSupport button.talkToExpert {
      background: var(--topheader-bg);
    }
    .getSupport button.applyNow {
      background: var(--color-red);
      margin-left: 0;
    }
    .sideBarSection {
      margin-bottom: 10px;
    }
    .sideBarSection .sidebarHeading {
      margin: 10px;
      margin-bottom: 0;
    }
    .sideBarSection .listCard {
      padding: 10px;
    }
    .sideBarSection .sidebarImgDiv {
      margin-right: 10px;
      flex-basis: 56px;
    }
    .sideBarSection .sidebarTextLink {
      flex-basis: calc(100% - 66px);
    }
    .sideBarSection .sidebarTextLink {
      flex-basis: calc(100% - 66px);
    }
    .sideBarSection .sidebarTextLink .cardText {
      padding-bottom: 5px;
    }
    .pageRedirectionLinks {
      border-radius: 0;
      border-right: 0;
      border-left: 0;
      margin: 0 -10px;
      margin-bottom: 10px;
      padding: 0 5px;
    }
    .pageRedirectionLinks ul li {
      padding: 0;
      margin: 0 5px;
    }
    .pageRedirectionLinks ul li .activeLink,
    .pageRedirectionLinks ul li a {
      padding: 11px 0;
    }
    .getSupport .row {
      display: none;
    }
    .getSupport button {
      width: 49%;
      border-radius: 2px;
      font-size: 13px;
      padding: 6px 4px;
      height: 36px;
    }
    .getSupport button.applyNow {
      margin-left: 0;
    }
    .updated-info {
      border-radius: 4px;
      border: var(--border-line);
      padding: 10px 20px;
      background: var(--color-white);
      margin-bottom: 10px;
    }
    .updated-info img {
      float: left;
    }
    .updated-info a {
      display: block;
    }
    .updated-info p {
      font-size: 14px;
      line-height: 24px;
    }
    .removeFixedQuickLink {
      display: none;
    }
    .ohterCategoryArticles .row {
      flex-wrap: nowrap;
      overflow: auto;
    }
    .ohterCategoryArticles .categoryArticles p {
      font-size: 14px;
    }
    .courseCategoryPage .courseHeroSection {
      padding: 20px;
      min-height: 145px;
    }
    .courseCategoryPage h1 {
      padding-top: 0;
      padding-bottom: 20px;
    }
    .courseCategoryPage h2 {
      font-size: 15px;
      line-height: 24px;
      margin-bottom: 10px;
    }
    .courseCategoryPage .updated-info {
      border: 0;
      padding: 0;
      display: block;
      margin-bottom: 10px;
    }
    .courseCategoryPage .updated-info p {
      padding: 0;
    }
    .courseCategoryType {
      padding: 0;
    }
    .courseCategoryType ul li {
      padding: 11px 0;
      padding-bottom: 9px;
      margin: 0 15px;
    }
    .courseCategoryType .btn_left,
    .courseCategoryType .btn_right {
      height: 43px;
      width: 30px;
    }
    .courseCategoryType .spriteIcon {
      margin: 11px 0;
    }
    .courseCategoryCard {
      padding: 8px 10px;
      font-size: 15px;
      line-height: 28px;
      margin-bottom: 10px;
    }
    .courseCategoryCard h3 {
      flex-basis: 100%;
      font-size: 16px;
      font-weight: 500;
      padding-bottom: 10px;
    }
    .courseCategoryCard .row {
      display: block;
    }
    .courseCategoryCard .row:first-child {
      padding-bottom: 10px;
    }
    .courseCategoryCard .courseInfo ul {
      justify-content: initial;
    }
    .courseCategoryCard .courseInfo ul li {
      border: none;
    }
    .courseCategoryCard .applyClg {
      margin: -8px -10px;
      margin-top: 10px;
    }
    .courseCategoryCard .primaryBtn {
      width: 100%;
      display: block;
      border-radius: 0 0 4px 4px;
    }
    .courseCategoryCard ul {
      justify-content: space-around;
      flex-wrap: wrap;
    }
    .courseCategoryCard ul li {
      margin: 0;
      position: relative;
    }
    .courseCategoryCard ul li a {
      font-weight: 400;
    }
    .courseCategoryCard ul li a span {
      display: none;
    }
    .customLogo p.listCard {
      padding: 10px;
    }
    .customLogo .sidebarImgDiv {
      margin-right: 10px;
    }
    .customLogo .sidebarTextLink {
      flex-basis: calc(100% - 66px);
    }
    .customLogo .sidebarTextLink .cardText {
      padding: 0;
    }
    .customLogo .listCard {
      padding: 5px 10px;
    }
    .recentnewsList {
      padding: 10px;
    }
    .recentnewsDiv.row {
      padding: 0;
      border: none;
    }
    .sidebarImgDiv {
      flex-basis: 56px;
      margin-right: 10px;
      min-height: 56px;
    }
    .sidebarImgDiv img {
      width: 56px;
      height: 56px;
    }
    .recentnewsDivText,
    .recentArticlesDivText {
      flex-basis: calc(100% - 56px - 10px);
    }
    .recentnewsDivText .sidebarTextLink,
    .recentArticlesDivText .sidebarTextLink {
      -webkit-line-clamp: 2;
    }
    .listCard:last-child .recentnewsDiv.row {
      margin: 0;
    }
    .customSlider .scrollLeft,
    .customSlider .scrollRight {
      display: none !important;
    }
    .four-cardDisplay .sliderCardInfo,
    .custom-cardDisplay .sliderCardInfo {
      margin-right: 6px;
      width: 224px;
      display: inline-block !important;
      min-height: 274px;
    }
    .four-cardDisplay .sliderCardInfo img,
    .custom-cardDisplay .sliderCardInfo img {
      height: 168px;
    }
    .four-cardDisplay .sliderCardInfo .textDiv,
    .custom-cardDisplay .sliderCardInfo .textDiv {
      padding: 10px;
    }
    .four-cardDisplay .sliderCardInfo .widgetCardHeading,
    .custom-cardDisplay .sliderCardInfo .widgetCardHeading {
      font-weight: 400;
    }
    .viewAllDiv {
      min-height: 274px;
    }
    .viewAllStream {
      width: 100%;
      padding: 5px 20px;
      margin-bottom: 10px;
    }
    .getSupport .button__row__container {
      width: 100%;
    }
    .getSupport .getSupport__subheading {
        display: none;
    }
  
  }