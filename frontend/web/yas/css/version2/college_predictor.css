/*------predict-college-----*/
.predict-college {
    position: relative;
    border-radius: 4px;
    border: 1px solid rgba(216, 216, 216, 1);
    background-color: var(--color-white);
    margin-bottom: 20px;
    overflow: hidden;
}

.field-complete {
    background-color: rgba(219, 235, 255, 1);
    color: rgba(9, 102, 194, 1);
    height: 34px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.predict-college ul {
    padding: 20px;
    margin: 0;
    list-style: none;
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: end;
}

.predict-college ul li {
    width: 32%;
}

.predict-college ul li span {
    color: var(--color-red);
    font-weight: bold;
}

.predict-college ul li input,
.predict-college ul li select {
    border: 1px solid rgba(216, 216, 216, 0.8);
    background: var(--color-white);
    border-radius: 4px;
    padding: 7px 12px;
    font-size: 14px;
    /* color: rgba(140, 149, 166, 1); */
    width: 100%;
}

.predict-college ul li label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 16px;
    line-height: 15px;
}

.predict-college ul button {
    width: 146px;
    height: 36px;
    background-color: var(--color-red);
    color: var(--color-white);
    border-radius: 3px;
    border: 0;
}

.predict-college ul li:last-child {
    justify-content: end;
    display: flex;
    width: 25%;
}

/*------predict-college-----*/

/*------engineering-exams-----*/
.engineering-exams {
    border-radius: 4px;
    border: 1px solid rgba(216, 216, 216, 1);
    background-color: var(--color-white);
    padding: 20px;
    padding-bottom: 0;
    margin-bottom: 20px;
    overflow: hidden;
}

.engineering-exams h2 {
    font-size: 16px;
    line-height: 24px;
    color: var(--primary-font-color);
    margin-bottom: 20px;
}

.other-exams ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    gap: 14px;
    flex-wrap: wrap;
}

.other-exams ul li {
    padding: 10px;
    border: 1px solid rgba(216, 216, 216, 1);
    border-radius: 4px;
    width: 241px;
    display: flex;
    align-items: center;
}

.other-exams ul li span {
    letter-spacing: 0.3px;
    font-size: 15px;
    line-height: 24px;
    font-weight: 400;
}

.college-logo {
    width: 36px;
    height: 36px;
    border: 1px solid rgba(170, 170, 170, 0.2);
    border-radius: 4px;
    padding: 5px;
    margin-right: 10px;
}

.view-predicators {
    display: block;
    margin: 20px 0;
}

.view-predicators a {
    color: var(--color-red);
    text-decoration: none;
    font-weight: 600;
    line-height: 24px;
    font-size: 14px;
}

/*------engineering-exams-----*/

/*-----share on whatsup------*/
.share-whatsup {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
    padding: 20px;
    margin-bottom: 20px;
    overflow: hidden;
}

.share-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.share-flex h3 {
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    width: 48%;
}

.share-btn {
    white-space: nowrap;
    display: flex;
    gap: 15px;
}

.whatsup {
    background-color: rgba(0, 153, 101, 1);
    width: 187px;
    height: 32px;
    border: 1px solid rgba(0, 153, 101, 1);
    color: #fff;
    border-radius: 8px;
}

.copy-link {
    border: 1px solid var(--color-red);
    color: var(--color-red);
    width: 120px;
    height: 32px;
    background-color: transparent;
    border-radius: 8px;
}

.whatsupicon,
.attachIcon {
    width: 19px;
    height: 18px;
    background-position: 718px -1200px !important;
    vertical-align: text-bottom;
    margin-right: 10px;
}

.attachIcon {
    background-position: 673px -1200px !important;
}

/*-----share on whatsup------*/

/* select2 */
.predict-college .select2-container--default .select2-selection--single {
    height: 35px !important;
    border: 1px solid rgba(216, 216, 216, 0.8);
}

.predict-college .select2-container--default .select2-selection--single .select2-selection__placeholder {
    font-size: 14px;
    font-weight: 400;
}

.predict-college button:disabled {
    background-color: rgba(255, 78, 83, 0.4);
}

.errorMsgEmailCollegePredictor {
    top: 74px;
    position: absolute;
}

@media only screen and (max-width:640px) {
    #collegePredictorFormExam .select2-container {
        width: 100%;
    }
}

@media (max-width: 1023px) {
    .field-complete {
        height: auto;
        padding: 10px;
        text-align: center;
    }

    .predict-college ul {
        flex-wrap: wrap;
    }

    .predict-college ul li,
    .predict-college ul li button,
    .predict-college ul li:last-child,
    .other-exams ul li {
        width: 100%;
    }

    .other-exams ul {
        gap: 12px;
    }

    .predict-college ul li {
        margin-bottom: 10px;
    }

    .share-flex {
        flex-wrap: wrap;
    }

    .share-flex h3 {
        width: auto;
        text-align: center;
        margin-bottom: 20px;
        padding: 0 30px;
    }

    .share-whatsup {
        background: linear-gradient(180deg, #ffffff 57%, #fffae7 100%);
        padding: 10px;
    }

    .share-btn {
        gap: 10px;
    }
}