* {
  color: var(--primary-font-color);
}

.articleSection {
  margin-bottom: 20px;
}

.articleSection h2 {
  font-size: 18px;
  line-height: 24px;
  padding: 20px 0;
}

.articleRelataedLinks {
  position: relative;
}

.articleRelataedLinks .right_angle,
.articleRelataedLinks .left_angle {
  margin: 10px 0;
}

.articleRelataedLinks .btn_right,
.articleRelataedLinks .btn_left {
  position: absolute;
  width: 40px;
  height: 40px;
  background-color: #fff;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  top: 0;
  cursor: pointer;
}

.articleRelataedLinks .btn_left {
  left: 0;
}

.articleRelataedLinks .btn_right {
  right: 0;
}

.articleRelataedLinks ul {
  margin: 0;
  padding: 0 0;
  white-space: nowrap;
  overflow: auto;
}

.articleRelataedLinks ul::-webkit-scrollbar {
  display: none;
}

.articleRelataedLinks ul li {
  display: inline-block;
  margin: 0 3px;
}

.articleRelataedLinks ul li:first-child {
  margin-left: 0;
}

.articleRelataedLinks ul li a {
  display: block;
  padding: 10px 16px;
  color: var(--primary-font-color);
  line-height: 20px;
  font-size: 13px;
  text-decoration: none;
  background: #f7f7f7;
  text-transform: uppercase;
  text-align: center;
}

.articleRelataedLinks ul li a.activeLink {
  background: var(--color-red);
  color: var(--color-white);
  font-weight: var(--font-semibold);
  border-radius: 3px 3px 0 0;
}

.generalArticleSection {
  border-radius: 4px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  padding-bottom: 0;
  border: var(--border-line);
}

.generalArticleSection .tab-content.activeLink {
  display: block;
}

.generalArticleSection h3.row {
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  margin: 0;
  margin-bottom: 20px;
}

.generalArticleSection h3 select {
  padding: 8px 12px;
  border: var(--border-line);
  color: #787878;
  background: #fafbfc;
  font-weight: var(--font-semibold);
  cursor: pointer;
  font-size: 14px;
  line-height: 24px;
  max-width: 190px;
  background-position: 95% 16px !important;
}

.generalArticleSection h3 select option {
  background: var(--color-white);
  cursor: pointer;
}

.latestInfoSection .latestInfoDiv,
.articlesByCategory .latestInfoDiv {
  flex-basis: 23.7%;
  min-height: 276px;
}

.latestInfoSection .latestInfoDiv figure {
  min-height: 196px;
  display: grid;
}

.latestInfoSection .latestInfoDiv img,
.articlesByCategory .latestInfoDiv img {
  height: 207px;
  align-self: center;
  width: 100%;
}

.articlesByCategory.row {
  margin: 0;
}

.articlesByCategory .browsedArticleDiv {
  flex-basis: 23.7%;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 6px;
  border: var(--border-line);
  overflow: hidden;
  min-height: 276px;
}

.browsedArticleDiv a:hover {
  text-decoration: none;
}

.articlesByCategory .browsedArticleDiv:nth-of-type(4n + 0) {
  margin-right: 0;
}

.articlesByCategory .browsedArticleDiv figure {
  min-height: 207px;
  display: grid;
}

.articlesByCategory .browsedArticleDiv img {
  height: 207px;
  align-self: center;
  margin: 0 auto;
}

.articlesByCategory .browsedArticleText {
  padding: 16px;
}

.articlesByCategory .browsedArticleText a {
  margin-bottom: 10px;
}

.articlesByCategory .browsedArticleText p,
.articlesByCategory .browsedArticleText a {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-500);
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 48px;
  margin-bottom: 10px;
}

.articlesByCategory .browsedArticleText a:hover {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.articlesByCategory .browsedArticleText p:last-child,
.articlesByCategory .browsedArticleText .authorNameSection {
  color: #989898;
  margin: 0;
  font-weight: 400;
}

.latestInfoSection .latestInfoTxt a,
.otherEntranceExams .latestInfoTxt a {
  margin-bottom: 10px;
}

.latestInfoTxt h3 {
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 10px;
}

.latestInfoTxt h3:hover {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.viewAllArticleDiv {
  text-align: center;
  padding: 20px 0;
  border-top: var(--border-line);
}

.viewAllArticleDiv .primaryBtn {
  line-height: 24px;
}

.bannerSection {
  margin: 20px 0;
  margin-top: 10px;
}

.bannerSection h1 {
  font-size: 24px;
  line-height: 38px;
  padding-bottom: 20px;
  font-weight: 400;
}

.latestArticleSection {
  border-radius: 4px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  padding: 0;
}

.latestArticleSection.row {
  margin: 0;
  height: 325px;
  overflow: hidden;
}

.latestArticleSection .articleDisplay {
  flex-basis: calc(100% - 280px);
  border-right: var(--border-line);
  height: 325px;
}

.latestArticleSection .articleDisplay .row {
  margin: 0;
}

.latestArticleSection .articleDisplay .row .col-md-6 {
  padding: 0;
}

.latestArticleSection .articleDisplay .row img {
  display: block;
  max-width: 463px;
  height: 325px;
  width: 100%;
}

.latestArticleSection .articleDisplay .aticleInfo {
  padding: 30px;
}

.latestArticleSection .articleDisplay .aticleInfo h2 {
  font-size: 24px;
  line-height: 38px;
  margin-bottom: 20px;
  font-weight: 400;
  padding: 0;
}

.latestArticleSection .articleDisplay .aticleInfo h2 a {
  color: var(--primary-font-color);
}

.latestArticleSection .articleDisplay .aticleInfo h2 a:hover {
  color: var(--anchor-textclr);
}

.latestArticleSection .articleDisplay .aticleInfo p,
.latestArticleSection .articleDisplay .aticleInfo .authorName {
  color: #787878;
  font-size: 15px;
  line-height: 26px;
  margin-bottom: 22px;
  max-height: 133px;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.latestInfoSection .latestInfoTxt .trendingAuthorName {
  transform: translateY(-15px);
}

.latestArticleSection .articleDisplay .aticleInfo .authorName {
  font-weight: 400 !important;
}

.latestArticleSection .articleDisplay .aticleInfo .updated-info {
  font-size: 14px;
  line-height: 24px;
}

.latestArticleSection .articleDisplay .aticleInfo .updated-info.row {
  margin: 0;
  -webkit-box-align: center;
  align-items: center;
}

.latestArticleSection .articleDisplay .aticleInfo .updated-info img {
  display: inline-block;
  width: 36px;
  height: 36px;
  margin-right: 10px;
  vertical-align: middle;
  border-radius: 50%;
}

.latestArticleSection .articleDisplay .aticleInfo .updated-info a {
  cursor: pointer;
  font-weight: var(--font-semibold);
}

.latestArticleSection .articleDisplay .aticleInfo .updated-info p,
.latestArticleSection .articleDisplay .aticleInfo .updated-info .authorName {
  margin-bottom: 0;
  min-height: auto;
  padding-left: 10px;
  color: var(--primary-font-color);
}

.latestArticleSection .articleList {
  flex-basis: 280px;
}

.latestArticleSection .articleList ul {
  margin: 0;
  padding: 0;
}

.latestArticleSection .articleList ul li {
  border-bottom: var(--border-line);
  border-left: 5px solid var(--color-white);
  padding: 8px 15px;
  list-style-type: none;
  font-size: 14px;
  line-height: 24px;
  cursor: pointer;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  display: flex;
  align-items: center;
  min-height: 64px;
}

.latestArticleSection .articleList ul li a {
  color: var(--primary-font-color);
  max-height: 48px;
  overflow: hidden;
}

.latestArticleSection .articleList ul li a:hover {
  color: var(--anchor-textclr);
}

.latestArticleSection .articleList ul li.hoverbg {
  background: #fafbfc;
  border-left: 5px solid var(--color-red);
}

.latestArticleSection .articleList ul li:last-child {
  border-bottom: none;
}

.latestInfoSection .latestInfoTxt h3,
.browsedArticleText h3 {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-500);
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 72px;
  margin-bottom: 10px;
}

.browsedArticleDiv a:hover {
  text-decoration: none;
}

.browsedArticleDiv a:hover h3,
.latestInfoDiv a:hover h3 {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.articlesByCategory .browsedArticleText p,
.articlesByCategory .browsedArticleText .authorNameSection {
  min-height: auto;
}

.latestArticleSection,
.latestInfoSection,
.generalArticleSection,
.articleRelataedLinks ul li a {
  box-shadow: none;
  border: var(--border-line);
}

.articleRelataedLinks ul li a {
  background: var(--color-white);
  border-bottom: none;
  border-radius: 4px 4px 0 0;
}

.articleRelataedLinks ul li a.activeLink {
  border: 1px solid var(--color-red);
  border-bottom: none;
}

.articleRelataedLinks {
  border-radius: 4px 0;
}

.generalArticleSection {
  border-radius: 0 0 4px 4px;
}

.articleRelataedLinks .btn_right,
.articleRelataedLinks .btn_left {
  height: 42px;
  border: var(--border-line);
}

.articleRelataedLinks .btn_left {
  border-right: none;
}

.articleRelataedLinks .btn_right {
  border-left: none;
}

.articleDisplay figure {
  border-right: var(--border-line);
}

.articlesByCategory .latestInfoDiv figure,
.latestInfoSection .latestInfoDiv figure,
.articlesByCategory .browsedArticleDiv figure {
  border-bottom: var(--border-line);
}

.latestArticleSection .aticleInfo img.gifLive,
.bannerDiv img.gifLive {
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
  max-width: 72px;
  height: 32px;
}

.latestArticleSection .aticleInfo img.gifLive,
.bannerDiv img.gifLive {
  display: block;
  margin: 0;
  height: 30px;
}

.latestArticleSection.row {
  height: auto;
}

.latestArticleSection .article1-view {
  position: relative;
  height: 100%;
}

.latestArticleSection .articleDisplay {
  flex-basis: calc(100% - 735px);
  height: 270px;
  border-right: 0;
  position: relative;
}

.latestArticleSection .articleDisplay figure {
  border: none;
  max-height: 270px;
}

.latestArticleSection .articleDisplay:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to bottom,
      rgba(255, 255, 255, 0) 0%,
      #282828 56%);
  border-radius: 4px;
}

.latestArticleSection .articleDisplay .aticleInfo {
  padding: 20px;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 1;
}

.latestArticleSection .articleDisplay .aticleInfo h2 {
  margin-bottom: 10px;
  -webkit-line-clamp: 3;
}

.latestArticleSection .articleDisplay .aticleInfo h2 a,
.latestArticleSection .articleDisplay .aticleInfo .updated-info p,
.latestArticleSection .articleDisplay .aticleInfo .updated-info a,
.latestArticleSection .articleDisplay .aticleInfo .updated-info .authorName {
  color: var(--color-white);
}

.latestArticleSection img {
  height: 270px;
}

.latestArticleSection .articleList {
  flex-basis: 735px;
  padding: 21px 31px;
  position: relative;
}

.latestArticleSection .articleList ul li {
  height: 57px;
  padding: 0;
  min-height: 57px;
  border: none;
}

.latestArticleSection .articleList ul li img {
  width: 95px;
  height: 45px;
  display: inline-block;
  border-radius: 0;
}

.latestArticleSection .articleList ul li img.gifLive {
  height: 15px;
  display: block;
}

.latestArticleSection .articleList ul li a {
  display: flex;
  width: 100%;
  align-items: center;
  max-height: 57px;
}

.latestArticleSection .articleList ul li a:hover .articleName {
  color: var(--anchor-textclr);
}

.latestArticleSection .authorName:hover {
  color: var(--anchor-textclr) !important;
}

.latestArticleSection .articleList ul li .articleName {
  padding-left: 10px;
  flex-basis: calc(100% - 155px);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  align-items: center;
  line-height: 20px;
}

.latestArticleSection .articleList ul li .articleName img.gifLive {
  width: auto;
}

.latestArticleSection .viewAll {
  position: absolute;
  right: 16px;
  height: auto;
  display: block;
  bottom: 16px;
  min-height: auto;
}

.latestArticleSection .viewAll a {
  color: var(--color-red);
  font-weight: 600;
}

@media (max-width: 1023px) {
  .authorNameText {
    display: block !important;
  }

  .articleSection h2 {
    text-align: center;
    padding-top: 0;
    font-size: 15px;
  }

  .articleRelataedLinks {
    margin: 0 -10px;
  }

  .articleRelataedLinks ul {
    padding: 0 10px;
  }

  .articleRelataedLinks ul li a {
    padding: 8px 16px;
  }

  .articleRelataedLinks .btn_left,
  .articleRelataedLinks .btn_right {
    width: 36px;
    height: 36px;
  }

  .articleRelataedLinks .left_angle,
  .articleRelataedLinks .right_angle {
    margin: 6px 0;
  }

  .generalArticleSection {
    padding: 10px;
  }

  .generalArticleSection h3 {
    font-size: 15px;
    line-height: 24px;
    white-space: normal;
  }

  .generalArticleSection h3.row {
    margin-bottom: 10px;
  }

  .generalArticleSection h3 select {
    width: 100%;
    margin-top: 10px;
  }

  .articleList {
    display: none;
  }

  .bannerSection {
    margin-top: -160px;
    border-radius: 4px;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
  }

  .bannerSection h1 {
    padding-bottom: 10px;
    font-size: 18px;
    line-height: 24px;
  }

  .latestArticleSection.row {
    display: block;
    box-shadow: none;
    height: auto;
  }

  .articleDisplay .display_none {
    display: block;
  }

  .latestArticleSection .articleDisplay {
    display: block;
    white-space: nowrap;
    overflow: auto;
    height: auto;
  }

  .latestArticleSection .articleDisplay>div {
    display: inline-block;
    width: 70%;
    white-space: initial;
    border-radius: 4px;
    border: var(--border-line);
    margin-right: 10px;
    height: 276px;
  }

  .latestArticleSection .articleDisplay .row img {
    height: 168px;
  }

  .latestArticleSection .articleDisplay .aticleInfo {
    padding: 10px;
  }

  .latestArticleSection .articleDisplay .aticleInfo h2 {
    font-size: 15px;
    line-height: 26px;
    margin-bottom: 10px;
    min-height: 52px;
  }

  .latestArticleSection .articleDisplay .aticleInfo p,
  .latestArticleSection .articleDisplay .aticleInfo .updated-info img {
    display: none;
  }

  .latestArticleSection .articleDisplay .aticleInfo .updated-info a {
    font-weight: 400;
    color: #989898;
  }

  .articlesByCategory.row {
    display: block;
    overflow: auto;
    white-space: nowrap;
  }

  .articlesByCategory .browsedArticleDiv {
    display: inline-block !important;
    width: 70%;
    margin-right: 10px;
    margin-bottom: 0;
  }

  .viewAllArticleDiv {
    display: none;
  }

  .browsedArticleText h3 {
    min-height: 72px;
  }

  .articlesByCategory .browsedArticleDiv {
    min-height: 323px;
  }

  .latestInfoSection .latestInfoDiv {
    min-height: 290px;
  }

  .articlesByCategory .browsedArticleDiv:nth-of-type(4n + 0) {
    margin-right: 10px;
  }

  .articlesByCategory .browsedArticleDiv:last-child {
    margin-right: 0;
  }

  .viewAllIcon {
    margin: 0 auto;
    margin-bottom: 10px;
  }

  .latestArticleSection {
    border: none;
  }

  .bannerSection {
    border: var(--border-line);
    box-shadow: none;
  }

  .articleRelataedLinks .btn_left,
  .articleRelataedLinks .btn_right {
    height: 38px;
  }

  .articleRelataedLinks {
    margin: 0;
  }

  .articleRelataedLinks ul {
    padding: 0;
  }

  .articleDisplay figure {
    border-right: 0;
    border-bottom: var(--border-line);
  }

  .latestArticleSection img {
    -o-object-fit: cover;
    object-fit: cover;
  }

  .latestArticleSection .articleDisplay {
    flex-basis: auto;
    height: 276px;
  }

  .latestArticleSection .articleDisplay .aticleInfo {
    padding: 10px;
  }

  .latestArticleSection .articleDisplay .aticleInfo h2 {
    line-height: 18px;
    min-height: auto;
    overflow-wrap: break-word;
  }

  .latestArticleSection .articleDisplay:after {
    display: none;
  }

  .latestArticleSection .articleDisplay>div {
    position: relative;
    vertical-align: bottom;
  }

  .latestArticleSection .articleDisplay>div:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(to bottom,
        rgba(255, 255, 255, 0) 0%,
        #282828 56%);
    border-radius: 4px;
  }

  .latestArticleSection .articleDisplay .mobileOnly {
    vertical-align: bottom;
    display: inline-block !important;
    margin-right: 0;
  }

  .latestArticleSection .articleDisplay .mobileOnly:after {
    display: none;
  }

  .latestArticleSection .articleDisplay .aticleInfo p {
    line-height: 20px;
    font-weight: 400;
  }

  .latestArticleSection .aticleInfo img.gifLive {
    max-width: 70px;
    height: 30px;
    margin-bottom: 10px;
  }

  .latestArticleSection .viewAllDiv {
    min-height: 275px;
  }

  .browsedArticleDiv .viewAllDiv {
    min-height: 350px;
  }
}