/* Secondary Header */

.headerMegaMenu {
    background: var(--color-white);
    z-index: 11;
    position: relative;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .16);
    height: 40px;
}

.headerMegaMenu .row {
    position: relative;
}

.headerMegaMenu .row.mainMenuText {
    -webkit-box-pack: end;
    justify-content: flex-end;
}

.headerMegaMenu ul {
    margin: 0;
    padding: 0;
}

.headerMegaMenu ul li {
    list-style-type: none;
    display: inline-block;
    font-size: 14px;
    line-height: 20px;
}

.headerMegaMenu ul li a {
    color: var(--primary-font-color);
    text-decoration: none;
    padding: 10px 6px;
    display: block;
}

ul.dropdown-menu {
    overflow: hidden;
    position: absolute;
    top: 40px;
    left: 0;
    font-size: 14px;
    line-height: 20px;
    background-clip: padding-box;
    border-top: 1px solid #eaeaea;
    width: 100%;
    z-index: 2;
    display: none;
}

.headerMegaMenu ul.dropdown-menu .row {
    padding-bottom: 0;
    padding-right: 10px;
}

ul.dropdown-menu li {
    display: block;
    line-height: 28px;
}

ul.dropdown-menu li.submenuhead {
    font-weight: var(--font-semibold);
    padding-bottom: 10px;
    text-transform: uppercase;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 {
    padding: 0;
    background-color: #f1f3f4;
    margin-top: 25px;
    padding: 20px;
    margin-left: -5px;
}

ul.dropdown-menu .row {
    background: var(--color-white);
    padding: 20px;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 h2 {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.43;
    color: #282828;
    background-color: #f1f3f4;
    padding: 0;
    margin: 0;
    margin-bottom: 10px;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .rightLst,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .leftLst,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .scrollLeft,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .scrollRight,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .scrollLeft,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .scrollRight {
    border: none;
    width: 40px;
    height: 40px;
    background-position: 422px -72px;
    padding: 0;
    cursor: pointer;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .leftLst,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .scrollLeft,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .scrollLeft {
    -webkit-transform: translate(0, -50%) rotate(-180deg);
    transform: translate(0, -50%) rotate(-180deg);
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .MultiCarousel .leftLst,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .MultiCarousel .rightLst,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .scrollRight,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .scrollLeft,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .scrollRight,
.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .scrollLeft {
    position: absolute;
    border-radius: 50%;
    top: calc(50% - 20px);
    outline: none;
    cursor: pointer;
    z-index: 2;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .scrollLeft {
    top: 60%;
    z-index: 1;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .scrollRight {
    top: 45%;
    z-index: 1;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider {
    position: relative;
    overflow-y: scroll;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 .customSlider .customSliderCards {
    display: flex;
    white-space: nowrap;
    overflow: auto;
}

.headerMegaMenu ul.dropdown-menu .row {
    padding-bottom: 0;
    padding-right: 10px;
    /* height: 470px; */
  }
  
  .headerMegaMenu .navigation_header_dropdown .dropdown-menu .col-md-12 {
    padding: 0px;
    background-color: #f1f3f4;
    margin-top: 25px;
    padding: 20px;
    margin-left: -5px;
  }
  
  .headerMegaMenu
    .navigation_header_dropdown
    .dropdown-menu.multi-column
    .col-md-12 {
    padding: 0px;
    background-color: #f1f3f4;
    margin-top: 25px;
    padding: 20px;
    margin-left: 0px;
  }
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .over {
  pointer-events: none;
  opacity: 1;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .rightLst,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .leftLst,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .scrollLeft,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .scrollRight,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .scrollLeft,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .scrollRight {
  border: none;
  width: 40px;
  height: 40px;
  background-position: 422px -72px;
  padding: 0;
  cursor: pointer;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .leftLst,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .scrollLeft,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .scrollLeft {
  -webkit-transform: translate(0px, -50%) rotate(-180deg);
  transform: translate(0px, -50%) rotate(-180deg);
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .MultiCarousel
  .leftLst,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .MultiCarousel
  .rightLst,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .scrollRight,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .scrollLeft,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .scrollRight,
.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .scrollLeft {
  position: absolute;
  border-radius: 50%;
  top: calc(50% - 20px);
  outline: none;
  cursor: pointer;
  z-index: 2;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .scrollRight {
  top: 45%;
  z-index: 1;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .scrollLeft {
  top: 60%;
  z-index: 1;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .customSliderCards {
  display: flex;
  white-space: nowrap;
  overflow: auto;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .sliderCardInfo {
  padding: 20px;
  border-radius: 4px;
  border: var(--border-line);
  margin-right: 14px;
  display: inline-block;
  padding: 0;
  white-space: initial;
  width: auto;
  min-height: unset;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .sliderCardInfo
  .headerSliderCard {
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  padding: 10px;
  display: flex;
  gap: 10px;
  height: 76px;
  width: 375px;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .sliderCardInfo
  .headerSliderCard
  .headerSliderLeft
  img {
  margin-top: 0px;
  width: 54px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .sliderCardInfo
  .headerSliderCard
  .headerSliderRight
  p {
  font-size: 14px;
  font-weight: normal;
  line-height: 1.86;
  letter-spacing: 0.3px;
  color: #282828;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .sliderCardInfo
  .headerSliderCard
  .headerSliderRight
  .collegeLocation
  span {
  font-size: 14px;
  font-weight: normal;
  line-height: 1.71;
  color: #989898;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .sliderCardInfo
  .headerSliderCard
  .headerSliderRight
  .collegeLocation
  .locationIcon {
  background-position: 594px -311px;
  position: static;
  vertical-align: sub;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .four-cardDisplay
  .sliderCardInfo:nth-of-type(4n + 1) {
  margin-left: 20px;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu
  .col-md-12
  .customSlider
  .four-cardDisplay
  .sliderCardInfo:first-child {
  margin-left: 0;
}

.headerMegaMenu
  .navigation_header_dropdown
  .dropdown-menu.col-md-12
  .multi-column-slider {
  padding-left: 0px;
  padding-right: 0;
  margin-right: -12px;
}

.sa_dropdown {
    cursor: pointer;
}

.sa_dropdown:hover ul.study-abroad-options {
    display: block;
}

ul.study-abroad-options {
    position: absolute;
    background: #fff;
    padding: 0;
    width: 150px;
    border: 1px solid #ccc;
    top: 50px;
    z-index: 1;
    box-shadow: 0 7px 34px rgba(0, 0, 0, .25);
    display: none;
}

.sub-header-drop {
    top: 40px !important;
    z-index: 2 !important;
}

ul.study-abroad-options li {
    display: block;
    padding: 5px 10px;
}

ul.study-abroad-options li a:hover {
    color: #3d8ff2;
}

ul.dropdown-menu.multi-column .row {
    margin: 0;
}

.multi-column .row {
    padding: 0;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu.multi-column>.row {
    padding-right: 0;
    padding-left: 0;
    min-height: 470px;
    max-height: 620px;
}

.more-left-panel {
    background: #fafafa;
    padding: 0;
    margin: -20px;
    position: relative;
    z-index: 1;
    flex-basis: 237px;
    max-width: 237px;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu.multi-column>.row .more-left-panel {
    padding-left: 20px;
}

ul.dropdown-menu li.hoverbg {
    background: var(--color-red);
    color: var(--color-white);
    font-weight: var(--font-bold);
}

ul.dropdown-menu li a {
    padding: 0;
    color: #787878;
}

.more-left-panel ul li a {
    padding: 8px 12px;
    cursor: pointer;
    color: var(--primary-font-color);
    line-height: 20px;
    text-transform: uppercase;
}

ul.dropdown-menu li.hoverbg a {
    color: var(--color-white);
    line-height: 20px;
}

ul.dropdown-menu.multi-column .col-md-10 {
    padding-right: 0;
    flex-basis: calc(100% - 160px);
}

ul.dropdown-menu .row .no_padding,
#ComputerApplication-blog .row {
    padding: 0;
}

ul.dropdown-menu.multi-column .col-md-10 .col-md-2 {
    -webkit-box-flex: 0;
    flex: 0 0 20%;
    max-width: 21%;
    width: 100%;
}

.headerMegaMenu .navigation_header_dropdown .dropdown-menu.multi-column .col-md-12 {
    padding: 0;
    background-color: #f1f3f4;
    margin-top: 25px;
    padding: 20px;
    margin-left: 0;
}

.navigation_header_dropdown:hover ul.dropdown-menu {
    display: block;
}

ul.dropdown-menu:before:hover ul.dropdown-menu {
    display: none;
}

ul.dropdown-menu li a:hover {
    color: #3d8ff2;
    text-decoration: underline;
}

.moremenu a.submenulistmoremenu:hover {
    text-decoration: none;
}

.headerMegaMenu ul li a:hover {
    color: #3d8ff2;
}

.more-left-panel ul li a:hover{
  color: #fff !important;
}

.headerMegaMenu ul li a:hover .caret {
    background-position: 651px -154px;
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
}

.headerMegaMenu ul li:hover a.menu-li-tabs {
    color: var(--color-red);
}

.headerMegaMenu ul li:hover .caret {
    background-position: 651px -154px;
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
}

/* Primary Header */

.topHeader ul {
    padding: 0;
    margin: 0;
}
.topHeader ul li {
    display: inline-block;
    font-size: 14px;
    padding: 10px 6px;
    line-height: 20px;
  }
  
  .topHeader ul li a {
    color: var(--color-white);
    text-decoration: none;
  }

  .topHeader .writeReview,
.topHeader .register {
  border-radius: 2px;
  border: solid 1px #fff;
  padding: 8px 12px;
}

.topHeader .register {
  color: var(--topheader-bg);
  background: var(--color-white);
}
.topHeader .registerNew {
    display: inline-block;
    font-size: 14px;
    padding: 10px 6px;
    line-height: 20px;
    padding-bottom: 20px;
  }
  
  .topHeader .registerNew a.registerWelcomeMsg {
    border-radius: 2px;
    border: solid 1px #fff;
    padding: 8px 12px;
  }
  
  .topHeader .registerNew .caretWhite {
    background-position: -37px -74px;
    width: 10px;
    height: 6px;
    margin-left: 5px;
  }
  
  .topHeader .registerNew:hover {
    border-radius: 3px;
    padding-bottom: 20px;
    transform: scale(1);
  }
  
  .topHeader .registerNew:hover a.registerWelcomeMsg {
    font-size: 14px;
    font-weight: 500;
    color: #282828;
    background-color: #fff;
  }
  
  .topHeader .registerNew:hover .caretWhite {
    background-position: -37px -137px;
  }
  
  .topHeader .registerNew:hover ul.login-options {
    display: block;
  }
  
  .topHeader .registerNew ul.login-options {
    position: absolute;
    background: #fff;
    padding: 0;
    width: 150px;
    border: 1px solid #ccc;
    top: 45px;
    right: 0;
    z-index: 1;
    display: none;
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16);
  }
  
  .topHeader .registerNew ul.login-options .whiteCaretIcon {
    background-position: -515px -265px;
    width: 11px;
    height: 9px;
    position: absolute;
    top: -8px;
    right: 10px;
    transform: rotate(180deg);
  }
  
  .topHeader .registerNew ul.login-options li {
    display: block;
    padding: 10px;
  }
  
  .topHeader .registerNew ul.login-options li:hover {
    background-color: rgba(9, 102, 194, 0.1);
  }
  
  .topHeader .registerNew ul.login-options a {
    font-size: 14px;
    font-weight: 400;
    color: #282828;
  }

  .homePageHeader {
    background: rgba(255, 255, 255, 0.06);
    box-shadow: none;
  }

  .homePageHeader .mainMenuText ul li a {
    color: var(--color-white);
    font-weight: 500;
  }

  .homePageHeader .menu-li-tabs .caret {
    background-position: 708px -71px;
  }

  .homePageHeader ul.dropdown-menu li a {
    color: var(--primary-font-color);
    font-weight: normal;
  }
  ul.study-abroad-options li a {
    color: #787878;
  }