* {
    color: var(--primary-font-color);
  }
  .articleSection {
    margin-bottom: 20px;
  }
  .articleSection h2 {
    font-size: 18px;
    line-height: 24px;
    padding: 20px 0;
  }
  .articleRelataedLinks {
    position: relative;
  }
  .articleRelataedLinks .right_angle,
  .articleRelataedLinks .left_angle {
    margin: 10px 0;
  }
  .articleRelataedLinks .btn_right,
  .articleRelataedLinks .btn_left {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: #fff;
    text-align: center;
    vertical-align: middle;
    overflow: hidden;
    top: 0;
    cursor: pointer;
  }
  .articleRelataedLinks .btn_left {
    left: 0;
  }
  .articleRelataedLinks .btn_right {
    right: 0;
  }
  .articleRelataedLinks ul {
    margin: 0;
    padding: 0 0;
    white-space: nowrap;
    overflow: auto;
  }
  .articleRelataedLinks ul::-webkit-scrollbar {
    display: none;
  }
  .articleRelataedLinks ul li {
    display: inline-block;
    margin: 0 3px;
  }
  .articleRelataedLinks ul li:first-child {
    margin-left: 0;
  }
  .articleRelataedLinks ul li a {
    display: block;
    padding: 10px 16px;
    color: var(--primary-font-color);
    line-height: 20px;
    font-size: 13px;
    text-decoration: none;
    background: #f7f7f7;
    text-transform: uppercase;
    text-align: center;
  }
  .articleRelataedLinks ul li a.activeLink {
    background: var(--color-red);
    color: var(--color-white);
    font-weight: var(--font-semibold);
    border-radius: 3px 3px 0 0;
  }
  .generalArticleSection {
    border-radius: 4px;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    padding-bottom: 0;
    border: var(--border-line);
  }
  .generalArticleSection .tab-content.activeLink {
    display: block;
  }
  .generalArticleSection h3.row {
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
    margin: 0;
    margin-bottom: 20px;
  }
  .generalArticleSection h3 select {
    padding: 8px 12px;
    border: var(--border-line);
    color: #787878;
    background: #fafbfc;
    font-weight: var(--font-semibold);
    cursor: pointer;
    font-size: 14px;
    line-height: 24px;
    max-width: 190px;
    background-position: 95% 16px !important;
  }
  .generalArticleSection h3 select option {
    background: var(--color-white);
    cursor: pointer;
  }
  .latestInfoSection .latestInfoDiv,
  .articlesByCategory .latestInfoDiv {
  flex-basis: 23.7%;
  min-height: 276px;
  }
  .latestInfoSection .latestInfoDiv figure {
  min-height: 196px;
  display: grid;
  }
  .latestInfoSection .latestInfoDiv img,
  .articlesByCategory .latestInfoDiv img {
  height: 207px;
  align-self: center;
  width: 100%;
  }
  .articlesByCategory.row {
  margin: 0;
  }
  .articlesByCategory .browsedArticleDiv {
  flex-basis: 23.7%;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 6px;
  border: var(--border-line);
  overflow: hidden;
  min-height: 276px;
  }
  .browsedArticleDiv a:hover {
  text-decoration: none;
  }
  .articlesByCategory .browsedArticleDiv:nth-of-type(4n + 0) {
  margin-right: 0;
  }
  .articlesByCategory .browsedArticleDiv figure {
  min-height: 207px;
  display: grid;
  }
  .articlesByCategory .browsedArticleDiv img {
  height: 207px;
  align-self: center;
  margin: 0 auto;
  }
  .articlesByCategory .browsedArticleText {
  padding: 16px;
  }
  .articlesByCategory .browsedArticleText a {
  margin-bottom: 10px;
  }
  .articlesByCategory .browsedArticleText p,
  .articlesByCategory .browsedArticleText a {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-500);
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 48px;
  margin-bottom: 10px;
  }
  .articlesByCategory .browsedArticleText a:hover {
  color: var(--anchor-textclr);
  text-decoration: underline;
  }
  .articlesByCategory .browsedArticleText p:last-child {
  color: #989898;
  margin: 0;
  font-weight: 400;
  }
  .latestInfoSection .latestInfoTxt a,
  .otherEntranceExams .latestInfoTxt a {
  margin-bottom: 10px;
  }
  .latestInfoTxt h3 {
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 10px;
  }
  .latestInfoTxt h3:hover {
  color: var(--anchor-textclr);
  text-decoration: underline;
  }
  .viewAllArticleDiv {
  text-align: center;
  padding: 20px 0;
  border-top: var(--border-line);
  }
  .viewAllArticleDiv .primaryBtn {
  line-height: 24px;
  }
  .bannerSection {
  margin: 20px 0;
  margin-top: 10px;
  }
  .bannerSection h1 {
  font-size: 24px;
  line-height: 38px;
  padding-bottom: 20px;
  font-weight: 400;
  }
  .latestArticleSection {
  border-radius: 4px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  padding: 0;
  }
  .latestArticleSection.row {
  margin: 0;
  height: 325px;
  overflow: hidden;
  }
  .latestArticleSection .articleDisplay {
  flex-basis: calc(100% - 280px);
  border-right: var(--border-line);
  height: 325px;
  }
  .latestArticleSection .articleDisplay .row {
  margin: 0;
  }
  .latestArticleSection .articleDisplay .row .col-md-6 {
  padding: 0;
  }
  .latestArticleSection .articleDisplay .row img {
  display: block;
  max-width: 463px;
  height: 325px;
  width: 100%;
  }
  .latestArticleSection .articleDisplay .aticleInfo {
  padding: 30px;
  }
  .latestArticleSection .articleDisplay .aticleInfo h2 {
  font-size: 24px;
  line-height: 38px;
  margin-bottom: 20px;
  font-weight: 400;
  padding: 0;
  }
  .latestArticleSection .articleDisplay .aticleInfo h2 a {
  color: var(--primary-font-color);
  }
  .latestArticleSection .articleDisplay .aticleInfo h2 a:hover {
  color: var(--anchor-textclr);
  }
  .latestArticleSection .articleDisplay .aticleInfo p {
  color: #787878;
  font-size: 15px;
  line-height: 26px;
  margin-bottom: 22px;
  max-height: 133px;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
  }
  .latestArticleSection .articleDisplay .aticleInfo .updated-info {
  font-size: 14px;
  line-height: 24px;
  }
  .latestArticleSection .articleDisplay .aticleInfo .updated-info.row {
  margin: 0;
  -webkit-box-align: center;
  align-items: center;
  }
  .latestArticleSection .articleDisplay .aticleInfo .updated-info img {
  display: inline-block;
  width: 36px;
  height: 36px;
  margin-right: 10px;
  vertical-align: middle;
  border-radius: 50%;
  }
  .latestArticleSection .articleDisplay .aticleInfo .updated-info a {
  cursor: pointer;
  font-weight: var(--font-semibold);
  }
  .latestArticleSection .articleDisplay .aticleInfo .updated-info p {
  margin-bottom: 0;
  min-height: auto;
  padding-left: 10px;
  color: var(--primary-font-color);
  }
  .latestArticleSection .articleList {
  flex-basis: 280px;
  }
  .latestArticleSection .articleList ul {
  margin: 0;
  padding: 0;
  }
  .latestArticleSection .articleList ul li {
  border-bottom: var(--border-line);
  border-left: 5px solid var(--color-white);
  padding: 8px 15px;
  list-style-type: none;
  font-size: 14px;
  line-height: 24px;
  cursor: pointer;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  display: flex;
  align-items: center;
  min-height: 64px;
  }
  .latestArticleSection .articleList ul li a {
  color: var(--primary-font-color);
  max-height: 48px;
  overflow: hidden;
  }
  .latestArticleSection .articleList ul li a:hover {
  color: var(--anchor-textclr);
  }
  .latestArticleSection .articleList ul li.hoverbg {
  background: #fafbfc;
  border-left: 5px solid var(--color-red);
  }
  .latestArticleSection .articleList ul li:last-child {
  border-bottom: none;
  }
  .latestInfoSection .latestInfoTxt h3,
  .browsedArticleText h3 {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-500);
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 50px;
  margin-bottom: 10px;
  max-height: 48px;
  }
  .browsedArticleDiv a:hover {
  text-decoration: none;
  }
  .browsedArticleDiv a:hover h3,
  .latestInfoDiv a:hover h3 {
  color: var(--anchor-textclr);
  text-decoration: underline;
  }
  .articlesByCategory .browsedArticleText p {
  min-height: auto;
  }
  .latestArticleSection,
  .latestInfoSection,
  .generalArticleSection,
  .articleRelataedLinks ul li a {
  box-shadow: none;
  border: var(--border-line);
  }
  .articleRelataedLinks ul li a {
  background: var(--color-white);
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  }
  .articleRelataedLinks ul li a.activeLink {
  border: 1px solid var(--color-red);
  border-bottom: none;
  }
  .articleRelataedLinks {
  border-radius: 4px 0;
  }
  .generalArticleSection {
  border-radius: 0 0 4px 4px;
  }
  .articleRelataedLinks .btn_right,
  .articleRelataedLinks .btn_left {
  height: 42px;
  border: var(--border-line);
  }
  .articleRelataedLinks .btn_left {
  border-right: none;
  }
  .articleRelataedLinks .btn_right {
  border-left: none;
  }
  .articleDisplay figure {
  border-right: var(--border-line);
  }
  .articlesByCategory .latestInfoDiv figure,
  .latestInfoSection .latestInfoDiv figure,
  .articlesByCategory .browsedArticleDiv figure {
  border-bottom: var(--border-line);
  }
  .latestArticleSection .aticleInfo img.gifLive,
  .bannerDiv img.gifLive {
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
  max-width: 72px;
  height: 32px;
  }
  .latestArticleSection .aticleInfo img.gifLive,
  .bannerDiv img.gifLive {
  display: block;
  margin: 0;
  height: 30px;
  }
  .latestArticleSection.row {
  height: auto;
  }
  .latestArticleSection .article1-view {
  position: relative;
  height: 100%;
  }
  .latestArticleSection .articleDisplay {
  flex-basis: calc(100% - 735px);
  height: 270px;
  border-right: 0;
  position: relative;
  }
  .latestArticleSection .articleDisplay figure {
  border: none;
  max-height: 270px;
  }
  .latestArticleSection .articleDisplay:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0) 0%,
    #282828 56%
  );
  border-radius: 4px;
  }
  .latestArticleSection .articleDisplay .aticleInfo {
  padding: 20px;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 1;
  }
  .latestArticleSection .articleDisplay .aticleInfo h2 {
  margin-bottom: 10px;
  -webkit-line-clamp: 3;
  }
  .latestArticleSection .articleDisplay .aticleInfo h2 a,
  .latestArticleSection .articleDisplay .aticleInfo .updated-info p {
  color: var(--color-white);
  }
  .latestArticleSection img {
  height: 270px;
  }
  .latestArticleSection .articleList {
  flex-basis: 735px;
  padding: 21px 31px;
  position: relative;
  }
  .latestArticleSection .articleList ul li {
  height: 57px;
  padding: 0;
  min-height: 57px;
  border: none;
  }
  .latestArticleSection .articleList ul li img {
  width: 95px;
  height: 45px;
  display: inline-block;
  border-radius: 0;
  }
  .latestArticleSection .articleList ul li img.gifLive {
  height: 15px;
  display: block;
  }
  .latestArticleSection .articleList ul li a {
  display: flex;
  width: 100%;
  align-items: center;
  max-height: 57px;
  }
  .latestArticleSection .articleList ul li a:hover .articleName {
  color: var(--anchor-textclr);
  }
  .latestArticleSection .articleList ul li .articleName {
  padding-left: 10px;
  flex-basis: calc(100% - 155px);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  align-items: center;
  line-height: 20px;
  }
  .latestArticleSection .articleList ul li .articleName img.gifLive {
  width: auto;
  }
  .latestArticleSection .viewAll {
  position: absolute;
  right: 16px;
  height: auto;
  display: block;
  bottom: 16px;
  min-height: auto;
  }
  .latestArticleSection .viewAll a {
  color: var(--color-red);
  font-weight: 600;
  }
  :root {
    --border-line: 1px solid #d8d8d8;
  }
  
  h3,
  h4 {
    padding-bottom: 10px;
    line-height: 24px;
  }
  
  .badgeIcon {
    width: 24px;
    height: 28px;
    background-position: 167px -262px;
    margin-right: 15px;
    margin-top: -10px;
  }
  
  .articelNote {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
  }
  
  .articelNote p {
    font-size: 15px;
    line-height: 26px;
  }
  
  .bannerImg {
    border-radius: 4px;
    margin-top: 20px;
    overflow: hidden;
    max-height: 400px;
  }
  
  .bannerImg img {
    max-height: 400px;
    width: 100%;
    display: block;
  }
  
  .dataContainer,
  .articleInfo {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin: 20px 0;
  }
  
  .dataContainer p,
  .articleInfo p,
  .articleInfo div {
    font-size: 15px;
    color: var(--primary-font-color);
    line-height: 26px;
    padding-bottom: 10px;
  }
  
  .dataContainer h2,
  .articleInfo h2 {
    padding: 8px 20px;
    line-height: 28px;
    font-size: 18px;
    font-weight: var(--font-bold);
    color: var(--primary-font-color);
    background: #f5f5f5;
    margin-bottom: 20px;
  }
  
  .dataContainer h3,
  .articleInfo h3 {
    font-size: 17px;
    line-height: 24px;
    padding-bottom: 10px;
    color: var(--primary-font-color);
  }
  
  .dataContainer h4,
  .articleInfo h4 {
    font-size: 16px;
    padding-bottom: 10px;
    line-height: 24px;
    color: var(--primary-font-color);
  }
  
  .dataContainer ul li,
  .articleInfo ul li {
    font-size: 15px;
    line-height: 26px;
    color: var(--primary-font-color);
  }
  
  .articleInfo img {
    display: block;
    text-align: center;
    margin: 0 auto;
  }
  
  .articleInfo table thead tr {
    background: #f1f3f4;
    padding: 10px;
    font-size: 15px;
    line-height: 26px;
    text-align: left;
    border-right: 0.2px solid #eaeaea;
    border-bottom: 0.2px solid #eaeaea;
    text-align: center;
  }
  
  .articleInfo table thead tr th {
    background: 0 0;
    text-align: center;
  }
  
  .articleInfo table thead tr td {
    font-weight: 600;
  }
  
  .articleInfo table tbody tr td {
    text-align: center;
  }
  
  .articleInfo table tbody tr td ul li {
    list-style: none;
  }
  
  .articleInfo caption {
    padding-top: 5px;
    text-align: center;
    font-size: 14px;
    margin-bottom: 0 !important;
    caption-side: bottom;
    color: var(--primary-font-color);
  }
  
  .articleInfo .table-responsive {
    margin-bottom: 0 !important;
  }
  
  .articleInfo button {
    display: block;
    background: var(--color-red);
    font-size: 14px;
    line-height: 20px;
    color: var(--color-white);
    padding: 8px 15px;
    font-weight: var(--font-semibold);
    border-radius: 3px;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    border: none;
    -webkit-transition: 0.2s ease;
    transition: 0.2s ease;
    outline: none;
    margin: 0 auto;
  }
  
  .table-responsive::-webkit-scrollbar {
    -webkit-appearance: none;
  }
  
  .table-responsive::-webkit-scrollbar:horizontal {
    height: 8px;
  }
  
  .table-responsive::-webkit-scrollbar-thumb {
    background-color: #d8dbdd;
    border-radius: 10px;
    border: 2px solid #fff;
  }
  
  .table-responsive::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #fff;
  }
  
  .faq_section h2 {
    padding: 10px 20px;
    font-size: 18px;
    line-height: 24px;
    color: var(--primary-font-color);
    background: #f5f5f5;
    margin-bottom: 20px;
  }
  
  .faq_section .faqDiv {
    border-radius: 4px;
    border: var(--border-line);
  }
  
  .faq_section p {
    padding: 11px 24px;
    font-size: 16px;
    line-height: 26px;
    border-bottom: var(--border-line);
  }
  
  .faq_section .faq_question {
    position: relative;
    cursor: pointer;
    -webkit-transition: 0.4s ease;
    transition: 0.4s ease;
  }
  
  .faq_section .faq_answer {
    border-left: 5px solid var(--color-red);
    font-size: 15px;
    background: #fafbfc;
  }
  
  .getSupport {
    padding: 19px;
    background: var(--color-white);
    border: var(--border-line);
    border-radius: 3px;
    margin-bottom: 20px;
  }
  
  .getSupport .row {
    margin: 0;
    align-items: center;
    flex-wrap: nowrap;
    margin-bottom: 20px;
  }
  
  .getSupport button {
    flex: 1;
  }
  
  .getSupport img {
    width: 80px;
    height: 80px;
    margin-right: 20px;
  }
  
  .getSupport p {
    font-size: 18px;
    line-height: 26px;
  }
  
  .getSupport button {
    width: 161px;
    border-radius: 3px;
    font-size: 14px;
    line-height: 24px;
    padding: 6px;
    text-align: center;
    color: var(--color-white);
    font-weight: var(--font-bold);
    border: none;
  }
  
  .getSupport button.talkToExpert {
    background: var(--topheader-bg);
  }
  
  .getSupport button.applyNow {
    background: var(--color-red);
    margin-left: 15px;
    width: 160px;
  }
  
  .trendingArtilce p,
  .recentArticles p,
  .recentnews p,
  .trendingNews p {
    padding: 10px 20px;
    font-size: 16px;
    line-height: 24px;
    border-bottom: var(--border-line);
    font-weight: var(--font-semibold);
  }
  
  .trendingArtilerDiv.row,
  .recentArticlesDiv.row,
  .recentnewsDiv.row {
    margin: 0;
    flex-wrap: nowrap;
    margin-bottom: 10px;
    border-bottom: var(--border-line);
    padding-bottom: 10px;
    -webkit-box-align: center;
    align-items: center;
    cursor: pointer;
  }
  
  .trendingArtilerDiv.row:hover .sidebarTextLink,
  .recentArticlesDiv.row:hover .sidebarTextLink,
  .recentnewsDiv.row:hover .sidebarTextLink,
  .trendingArtilerDiv.row:hover a,
  .recentArticlesDiv.row:hover a,
  .recentnewsDiv.row:hover a {
    text-decoration: underline;
    color: var(--anchor-textclr);
  }
  
  .listCard:last-child .trendingArtilerDiv.row,
  .listCard:last-child .recentArticlesDiv.row,
  .listCard:last-child .recentnewsDiv.row {
    margin-bottom: 0;
    border: none;
    padding: 0;
  }
  
  .trendingArtilerDiv img,
  .recentArticlesDiv img {
    width: 96px;
    max-height: 72px;
    display: block;
    align-self: center;
  }
  
  .sidebarImgDiv {
    flex-basis: 96px;
    margin-right: 16px;
    display: grid;
    min-height: 72px;
  }
  
  .trendingArtileText,
  .recentArticlesDivText {
    flex-basis: calc(100% - 96px - 16px);
  }
  
  .trendingArtilerDiv .trendingArtileText a,
  .trendingArtilerDiv .recentArticlesDivText a,
  .trendingArtilerDiv .recentnewsDivText a,
  .recentArticlesDiv .trendingArtileText a,
  .recentArticlesDiv .recentArticlesDivText a,
  .recentArticlesDiv .recentnewsDivText a,
  .recentnewsDiv .trendingArtileText a,
  .recentnewsDiv .recentArticlesDivText a,
  .recentnewsDiv .recentnewsDivText a,
  .sidebarTextLink {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: 500;
    text-decoration: none;
    overflow: hidden;
  }
  
  .trendingArtilerDiv .trendingArtileText .sidebarTextLink,
  .trendingArtilerDiv .recentArticlesDivText .sidebarTextLink,
  .trendingArtilerDiv.recentnewsDivText .sidebarTextLink,
  .recentArticlesDiv .recentnewsDivText .sidebarTextLink,
  .recentArticlesDiv .trendingArtileText .sidebarTextLink,
  .recentArticlesDiv .recentArticlesDivText .sidebarTextLink,
  .recentnewsDiv .trendingArtileText .sidebarTextLink,
  .recentnewsDiv .recentArticlesDivText .sidebarTextLink,
  .recentnewsDiv .recentnewsDivText .sidebarTextLink {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: 500;
    text-decoration: none;
    font-weight: 400;
    padding: 0;
    border: none;
  }
  
  .trendingArtilerDiv .trendingArtileText a:hover,
  .trendingArtilerDiv .recentArticlesDivText a:hover,
  .trendingArtilerDiv .recentnewsDivText a:hover,
  .recentArticlesDiv .trendingArtileText a:hover,
  .recentArticlesDiv .recentArticlesDivText a:hover,
  .recentArticlesDiv .recentnewsDivText a:hover,
  .recentnewsDiv .trendingArtileText a:hover,
  .recentnewsDiv .recentArticlesDivText a:hover,
  .recentnewsDiv .recentnewsDivText a:hover {
    color: #337ab7;
    text-decoration: underline;
  }
  
  .articleSidebarSection,
  .newsSidebarSection {
    border-radius: 4px;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
    margin-bottom: 20px;
  }
  
  .articleSidebarSection .tab-content.activeLink,
  .newsSidebarSection .tab-content.activeLink {
    display: block;
  }
  
  .articleSidebarSection ul,
  .newsSidebarSection ul {
    margin: 0;
    padding: 0;
    display: -webkit-box;
    display: flex;
  }
  
  .articleSidebarSection ul li,
  .newsSidebarSection ul li {
    list-style-type: none;
    flex-basis: 100%;
    text-align: center;
    font-size: 14px;
    line-height: 24px;
    color: #787878;
    cursor: pointer;
    padding: 12px 5px;
    padding-bottom: 9px;
    border-bottom: var(--border-line);
    font-weight: var(--font-semibold);
  }
  
  .articleSidebarSection ul li.recentHeading {
    width: 100%;
    flex: 1;
    text-align: left;
    padding: 12px 20px;
  }
  
  .articleSidebarSection ul li.activeLink,
  .newsSidebarSection ul li.activeLink {
    color: var(--color-red);
    border-bottom: 3px solid var(--color-red);
  }
  
  .trendingArtilerList,
  .recentArticlesList,
  .recentnewsList {
    max-height: 490px;
    overflow: auto;
    padding: 20px;
  }
  
  .trendingArtilerList::-webkit-scrollbar,
  .recentArticlesList::-webkit-scrollbar,
  .recentnewsList::-webkit-scrollbar {
    width: 5px;
  }
  
  .trendingArtilerList::-webkit-scrollbar-thumb,
  .recentArticlesList::-webkit-scrollbar-thumb,
  .recentnewsList::-webkit-scrollbar-thumb {
    background: #ccc;
  }
  
  .faqDiv .faq_answer p,
  .faqDiv .faq_answer li,
  .faqDiv .faq_answer a {
    font-size: 15px;
    line-height: 26px;
    font-weight: 400;
    border: none;
  }
  
  .faqDiv .faq_answer {
    padding: 11px 24px;
  }
  
  .faqDiv .faq_answer p {
    padding: 0;
    padding-bottom: 5px;
  }
  
  .bannerDiv,
  .registerLatestArticle {
    margin-top: 10px;
  }
  
  .bannerDiv,
  .registerLatestArticle,
  .articleSidebarSection,
  .newsSidebarSection,
  .articelNote,
  .articleInfo,
  .faq_section,
  .contentProvider,
  .commentSection,
  .relatedArticles {
    box-shadow: none;
    border: var(--border-line);
    background: var(--color-white);
  }
  
  .recentnewsDivText,
  .recentArticlesDivText {
    flex-basis: calc(100% - 96px - 16px);
  }
  
  .pageDescription {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin: 10px 0;
    color: var(--primary-font-color);
    padding: 30px;
  }
  
  .pageDescription .liveIcon {
    background-position: -27px -378px;
    margin-right: 10px;
    width: 70px;
    height: 35px;
    float: left;
  }
  
  .pageDescription h1 {
    font-size: 24px;
    line-height: 38px;
    padding-bottom: 20px;
    font-weight: 400;
  }
  
  .updatedBy {
    padding-right: 5px;
  }
  
  .updated-info.row {
    margin: 0;
    align-items: center;
    line-height: 20px;
    font-size: 14px;
  }
  
  .updated-info.row img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 10px;
    vertical-align: middle;
  }
  
  .updated-info.row .updatedBy p {
    display: inline-block;
    font-weight: var(--font-semibold);
  }
  
  .updated-info.row .updatedDetails {
    display: flex;
    align-items: center;
    flex-grow: 2;
  }
  
  .updated-info.row a {
    text-decoration: none;
    color: var(--anchor-textclr);
  }
  
  .updated-info.row ul {
    margin: 0;
    padding: 0;
    margin-left: 10px;
    right: -43px;
    z-index: 3;
  }
  
  .updated-info.row ul p {
    display: inline-block;
  }
  
  .updated-info.row ul li {
    display: inline-block;
    margin-right: 5px;
    vertical-align: middle;
  }
  
  .examRelataedLinks {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    padding: 0;
    margin-bottom: 20px;
    position: relative;
  }
  
  .examRelataedLinks .btn_left,
  .examRelataedLinks .btn_right {
    position: absolute;
    width: 48px;
    height: 48px;
    background-color: #fff;
    text-align: center;
    vertical-align: middle;
    overflow: hidden;
    top: 0;
    cursor: pointer;
  }
  
  .examRelataedLinks .btn_left {
    left: 0;
  }
  
  .examRelataedLinks .btn_right {
    right: 0;
  }
  
  .examRelataedLinks ul {
    margin: 0;
    padding: 0 30px;
    white-space: nowrap;
    overflow: auto;
  }
  
  .examRelataedLinks ul::-webkit-scrollbar {
    display: none;
  }
  
  .examRelataedLinks ul li {
    display: inline-block;
    padding: 0 10px;
  }
  
  .examRelataedLinks ul li a {
    display: block;
    padding: 12px 0;
    padding-bottom: 9px;
    border-bottom: 3px solid var(--color-white);
    color: #787878;
    line-height: 24px;
    font-size: 14px;
    text-decoration: none;
  }
  
  .examRelataedLinks ul li a.activeLink {
    color: var(--color-red);
    border-bottom: 3px solid var(--color-red);
    font-weight: var(--font-semibold);
  }
  
  .articelNote {
    font-size: 15px;
    font-weight: 400;
    font-stretch: normal;
    font-style: italic;
    line-height: 1.73;
    letter-spacing: 0.3px;
    text-align: left;
    color: #282828;
    border-left: solid #ff4e53 6px;
  }
  
  .getSupport {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .getSupport button.freeScholarship {
    background: var(--topheader-bg);
  }
  
  .getSupport button.applyScholarship {
    background: var(--color-red);
    margin-left: 14px;
  }
  
  .trendingArtilce {
    border-radius: 4px;
    box-shadow: none;
    background: #fff;
    margin-bottom: 20px;
  }
  
  .trendingArtilce p {
    padding: 10px 20px;
    font-size: 16px;
    line-height: 24px;
    border-bottom: var(--border-line);
    font-weight: var(--font-semibold);
  }
  
  .trendingArtilerList {
    padding: 20px;
  }
  
  .trendingArtilerDiv.row {
    margin: 0;
    flex-wrap: nowrap;
    margin-bottom: 16px;
    align-items: center;
  }
  
  .trendingArtilerDiv.row:last-child {
    margin-bottom: 0;
  }
  
  .trendingArtilerDiv.row .dateLabel {
    background: #fff;
    width: 56px;
    height: 56px;
    color: #fb7739;
    font-weight: var(--font-semibold);
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    margin-right: 16px;
    border-radius: 4px;
    padding: 7px;
    border: 1px solid #fb7739;
  }
  
  .trendingArtilerDiv.row .dateLabel span {
    font-size: 24px;
    color: inherit;
  }
  
  .trendingArtilerDiv img {
    max-width: 74px;
    max-height: 56px;
  }
  
  .trendingArtilerDiv .trendingArtileText a {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: 500;
    text-decoration: none;
  }
  
  .trendingArtilerDiv .trendingArtileText a:hover {
    color: #3d8ff2;
    text-decoration: underline;
  }
  
  .trendingArtilerDiv .trendingArtileText p {
    font-size: 14px;
    line-height: 24px;
    color: #989898;
    padding: 0;
    font-weight: 400;
    border: none;
  }
  
  .quickLinks {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    padding: 0;
  }
  
  .quickLinks h2 {
    font-size: 18px;
    line-height: 28px;
    background: #f5f5f5;
    padding: 8px 20px;
    color: var(--primary-font-color);
    text-transform: uppercase;
  }
  
  .quickLinks ul {
    padding: 10px 20px;
    margin: 0;
    max-height: 440px;
    overflow: auto;
  }
  
  .quickLinks ul::-webkit-scrollbar {
    width: 5px;
  }
  
  .quickLinks ul::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  .quickLinks ul::-webkit-scrollbar-thumb {
    background: #ccc;
  }
  
  .quickLinks ul li {
    list-style-type: none;
  }
  
  .quickLinks ul li:last-child a {
    border-bottom: none;
  }
  
  .quickLinks ul li a {
    display: block;
    font-size: 14px;
    line-height: 24px;
    border-bottom: var(--border-line);
    text-decoration: none;
    color: var(--primary-font-color);
    padding: 8px 0;
  }
  
  .quickLinks ul li a:hover {
    color: #3d8ff2;
    text-decoration: underline;
  }
  
  .clgWithCourse {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .clgWithCourse h2 {
    font-size: 18px;
    line-height: 28px;
    background: #f5f5f5;
    padding: 8px 20px;
    color: var(--primary-font-color);
    text-transform: uppercase;
    margin-bottom: 20px;
  }
  
  .clgWithCourse h2.row {
    justify-content: space-between;
    margin: 0;
    margin-bottom: 20px;
  }
  
  .clgWithCourse h2 a {
    font-size: 14px;
    color: var(--color-red);
    text-decoration: none;
  }
  
  .liveApllicationForms {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .liveApllicationForms h2 {
    font-size: 18px;
    line-height: 28px;
    background: #f5f5f5;
    padding: 8px 20px;
    color: var(--primary-font-color);
    text-transform: uppercase;
    margin-bottom: 20px;
    margin-left: 0;
    margin-right: 0;
  }
  
  .liveApllicationFormsInner .row,
  .clgWithCourseInner .row {
    margin: 0;
  }
  
  .liveApllicationFormsInner .row .applicationDiv,
  .liveApllicationFormsInner .row .clgWithCourseDiv,
  .clgWithCourseInner .row .applicationDiv,
  .clgWithCourseInner .row .clgWithCourseDiv {
    margin-right: 20px;
    flex-basis: 18.6%;
  }
  
  .liveApllicationFormsInner .row .applicationDiv:last-child,
  .liveApllicationFormsInner .row .clgWithCourseDiv:last-child,
  .clgWithCourseInner .row .applicationDiv:last-child,
  .clgWithCourseInner .row .clgWithCourseDiv:last-child {
    margin-right: 0;
  }
  
  .liveApllicationFormsInner {
    position: relative;
  }
  
  .liveApllicationFormsInner .scrollRight,
  .liveApllicationFormsInner .scrollLeft {
    top: 50%;
  }
  
  .liveApllicationFormsInner .scrollRight {
    right: -20px;
  }
  
  .liveApllicationFormsInner .scrollLeft {
    left: -20px;
    transform: translate(0px, -50%) rotate(180deg);
  }
  
  .liveApllicationFormsInner .row {
    flex-wrap: nowrap;
    overflow: auto;
    white-space: nowrap;
  }
  
  .liveApllicationFormsInner .row::-webkit-scrollbar {
    display: none;
  }
  
  .liveApllicationFormsInner .row .applicationDiv {
    width: 18.6%;
  }
  
  .liveApllicationFormsInner .row .applicationDiv:last-child {
    margin: 0;
  }
  
  .liveApllicationFormsInner .row .applicationDiv {
    border-radius: 4px;
    border: var(--border-line);
    padding: 20px 11px;
    text-align: center;
  }
  
  .liveApllicationFormsInner .row .applicationDiv a {
    text-decoration: none;
  }
  
  .liveApllicationFormsInner .row .applicationDiv figure {
    margin-bottom: 20px;
  }
  
  .liveApllicationFormsInner .row .applicationDiv img {
    max-width: 80px;
    min-height: 80px;
    margin: 0 auto;
    display: block;
  }
  
  .liveApllicationFormsInner .row .applicationDiv p,
  .liveApllicationFormsInner .row .applicationDiv a {
    font-size: 14px;
    line-height: 24px;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .liveApllicationFormsInner .row .applicationDiv p:first-child,
  .liveApllicationFormsInner .row .applicationDiv a:first-child {
    font-weight: var(--font-semibold);
  }
  
  .liveApllicationFormsInner .row .applicationDiv p a,
  .liveApllicationFormsInner .row .applicationDiv a a {
    margin-bottom: 0;
  }
  
  .liveApllicationFormsInner .row .applicationDiv p.clgName,
  .liveApllicationFormsInner .row .applicationDiv a.clgName {
    min-height: 50px;
  }
  
  .applicationDiv a.clgName,
  a.clgName {
    color: #333;
  }
  
  .applicationDiv a.clgName:hover {
    text-decoration: underline;
    cursor: pointer;
    color: #3d8ff2;
  }
  
  .trendingArtilerDiv img,
  .recentArticlesDiv img {
    max-height: 72px;
    align-self: center;
  }
  
  .applicationDiv p {
    -webkit-line-clamp: 1;
    min-height: 24px;
  }
  
  .applicationDiv p.course-fees {
    margin: 0;
    color: #989898;
  }
  
  .liveApllicationFormsInner .row .applicationDiv p span,
  .liveApllicationFormsInner .row .applicationDiv a span {
    color: #989898;
    display: block;
  }
  
  .liveApllicationFormsInner .row .applicationDiv .applicaticationFormBtn,
  .liveApllicationFormsInner .row .applicationDiv a.applicaticationFormBtn {
    display: block;
    width: 100%;
    margin-top: 10px;
    margin-bottom: 10px;
    border: none;
    color: var(--color-white);
  }
  
  .liveApllicationFormsInner .row .applicationDiv .applicaticationFormBtn a,
  .liveApllicationFormsInner .row .applicationDiv a.applicaticationFormBtn a {
    color: var(--color-white);
    margin: 0;
  }
  
  .clgWithCourseList {
    margin: 0;
  }
  
  .clgWithCourseDiv {
    border-radius: 4px;
    border: var(--border-line);
    overflow: hidden;
    padding: 20px;
    text-align: center;
  }
  
  .clgWithCourseDiv img {
    max-width: 80px;
    min-height: 80px;
    margin: 0 auto;
    display: block;
  }
  
  .clgWithCourseDiv figure {
    text-align: center;
    margin-bottom: 20px;
  }
  
  .clgWithCourseDiv p,
  .clgWithCourseDiv a {
    font-size: 14px;
    line-height: 24px;
    padding-bottom: 10px;
    text-decoration: none;
  }
  
  .clgWithCourseDiv p.clgName,
  .clgWithCourseDiv a.clgName {
    font-weight: var(--font-semibold);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 50px;
    padding: 0;
    margin-bottom: 14px;
  }
  
  .clgWithCourseDiv .avgFee {
    font-weight: var(--font-semibold);
  }
  
  .clgWithCourseDiv .avgFee span {
    color: #989898;
    display: block;
    font-weight: 400;
  }
  
  .clgWithCourseDiv button {
    border-radius: 3px;
    border: solid 1px var(--anchor-textclr);
    display: block;
    padding: 5px;
    text-align: center;
    background: var(--color-white);
    color: var(--anchor-textclr);
    font-size: 14px;
    line-height: 24px;
    font-weight: var(--font-500);
    width: 100%;
    margin-bottom: 10px;
  }
  
  .clgWithCourseDiv button:last-child {
    margin-bottom: 0;
  }
  
  .latestInfoListContainer {
    position: relative;
  }
  
  .latestInfoListContainer .latestInfoList.row {
    flex-wrap: nowrap;
  }
  
  .latestInfoListContainer .latestInfoList.row .latestInfoDiv {
    min-width: 23.7%;
  }
  
  iframe {
    border: 0;
  }
  
  .fancybox-slide--video .fancybox-content {
    max-width: 750px;
    max-height: 400px;
    margin: 0 auto;
  }
  
  * {
    letter-spacing: normal;
    color: var(--primary-font-color);
  }
  
  table td span {
    color: var(--primary-font-color) !important;
  }
  
  .table-responsive {
    margin-bottom: 10px;
  }
  
  table caption {
    caption-side: bottom;
    margin-top: 20px;
    margin-bottom: 0;
    font-size: 15px;
    line-height: 24px;
    color: var(--primary-font-color);
  }
  
  .tab-content.activeLink {
    display: block;
  }
  
  .breadcrumbDiv ul li a {
    font-weight: 500;
  }
  
  .pageInfo {
    max-height: 200px;
  }
  
  .faq_section.pageInfo {
    max-height: 400px;
  }
  
  .modeIcon,
  .courseDuratioIcon,
  .feesIcons {
    width: 20px;
    height: 20px;
    vertical-align: middle;
  }
  
  .feesIcons {
    background-position: 52px -346px;
  }
  
  .modeIcon {
    background-position: 52px -274px;
  }
  
  .courseDuratioIcon {
    background-position: 52px -299px;
  }
  
  .customSlider {
    position: relative;
  }
  
  .customSlider .scrollRight {
    right: -20px;
  }
  
  .customSlider .scrollLeft {
    top: 50%;
    left: -20px;
  }
  
  .customSlider .row {
    margin: 0;
  }
  
  .customSlider .customSliderCards {
    display: block;
    white-space: nowrap;
    overflow: auto;
  }
  
  .customSlider .customSliderCards::-webkit-scrollbar {
    display: none;
  }
  
  .customSlider .sliderCardInfo {
    border-radius: 4px;
    border: var(--border-line);
    margin-right: 14px;
    vertical-align: middle;
    min-height: 303px;
  }
  
  .customSlider .sliderCardInfo:last-child {
    margin-right: 0;
  }
  
  .customSlider .sliderCardInfo img {
    display: block;
    height: 207px;
    width: 100%;
    border-bottom: var(--border-line);
  }
  
  .customSlider .sliderCardInfo .clgLogo {
    max-width: 72px;
    height: 72px;
    display: block;
    margin-right: 20px;
  }
  
  .customSlider .sliderCardInfo .textDiv {
    padding: 20px;
  }
  
  .customSlider .sliderCardInfo .textDiv .collegeLogo {
    width: 56px;
    height: 56px;
    border-radius: 4px;
    margin-top: -60px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
    margin-bottom: 5px;
    display: block;
  }
  
  .customSlider .sliderCardInfo p {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: var(--font-semibold);
    padding-bottom: 0;
    white-space: initial;
  }
  
  .customSlider .sliderCardInfo p span {
    color: #989898;
    font-weight: 400;
    font-size: 13px;
  }
  
  .customSlider .sliderCardInfo .widgetCardHeading {
    font-size: 14px;
    padding-bottom: 0;
    min-height: 24px;
    margin-bottom: 5px;
    font-weight: 500;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
  }
  
  .customSlider .sliderCardInfo .subText {
    color: #989898;
    font-weight: 400;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    padding: 0;
    padding-bottom: 5px;
    position: relative;
  }
  
  .customSlider .sliderCardInfo .subText:last-child {
    padding-bottom: 20px;
  }
  
  .customSlider .sliderCardInfo .subText .spriteIcon {
    position: initial;
    vertical-align: middle;
  }
  
  .customSlider .sliderCardInfo a {
    text-decoration: none;
  }
  
  .sideBarSection {
    background: var(--color-white);
    border: var(--border-line);
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  .sideBarSection .row {
    margin: 0;
    align-items: center;
  }
  
  .sideBarSection .sidebarHeading {
    background: #d8d8d8;
    padding: 10px 20px;
    font-size: 16px;
    line-height: 24px;
    margin: 20px;
    margin-bottom: 6px;
    font-weight: 500;
  }
  
  .sideBarSection .sidebarTextLink {
    flex-basis: calc(100% - 92px);
  }
  
  .sideBarSection .sidebarTextLink p {
    font-size: 14px;
    line-height: 24px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
  }
  
  .sideBarSection .sidebarTextLink .cardText {
    color: var(--primary-font-color);
  }
  
  .sideBarSection .sidebarTextLink .subText {
    color: #989898;
  }
  
  .sideBarSection p.listCard {
    font-weight: 500;
    font-size: 15px;
    line-height: 24px;
  }
  
  .sideBarSection .listCard {
    display: flex;
    padding: 10px 20px;
    border-bottom: var(--border-line);
  }
  
  .sideBarSection .listCard:hover .cardText,
  .sideBarSection .listCard:hover .subText {
    color: var(--anchor-textclr);
  }
  
  .sideBarSection .listCard:last-child {
    border-bottom: none;
  }
  
  .sideBarSection .sidebarImgDiv {
    flex-basis: 72px;
    margin-right: 20px;
  }
  
  .sideBarSection .sidebarImgDiv img {
    display: block;
    margin: 0 auto;
    width: 100%;
  }
  
  .sideBarSection .applyText {
    color: var(--anchor-textclr);
  }
  
  .sidebarImgDiv img {
    width: 96px;
    max-height: 72px;
    display: block;
    align-self: center;
  }
  
  .getSupport button.articleScholarship {
    /* width: auto; */
    margin-left: 0;
  }
  
  .four-cardDisplay .sliderCardInfo {
    margin-right: 14px;
  }
  
  .four-cardDisplay .sliderCardInfo {
    width: 23.8%;
    display: inline-block;
    padding: 0;
    white-space: initial;
  }
  
  .four-cardDisplay .sliderCardInfo:nth-of-type(4n) {
    margin-right: 0;
  }
  
  .four-cardDisplay .sliderCardInfo:nth-of-type(4n + 1) {
    margin-left: 20px;
  }
  
  .four-cardDisplay .sliderCardInfo:first-child {
    margin-left: 0;
  }
  
  .four-cardDisplay .sliderCardInfo img {
    display: block;
    width: 100%;
  }
  
  .four-cardDisplay .sliderCardInfo figure {
    display: grid;
    height: 207px;
    border-bottom: var(--border-line);
  }
  
  .four-cardDisplay .sliderCardInfo figure img {
    align-self: center;
    height: auto;
    max-height: 207px;
  }
  
  .four-cardDisplay .sliderCardInfo p {
    font-size: 14px;
    line-height: 24px;
  }
  
  .four-cardDisplay .sliderCardInfo h3,
  .four-cardDisplay .sliderCardInfo .widgetCardHeading {
    font-size: 14px;
    line-height: 24px;
    padding-bottom: 0;
    min-height: 48px;
    margin-bottom: 5px;
    font-weight: 500;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 2;
  }
  
  .four-cardDisplay .sliderCardInfo .subText {
    color: #989898;
    font-weight: 400;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    padding: 20px;
  }
  
  .four-cardDisplay .sliderCardInfo .spriteIcon {
    position: initial;
    vertical-align: middle;
    margin-right: 5px;
  }
  
  .four-cardDisplay .sliderCardInfo .textDiv {
    padding: 20px;
  }
  
  .four-cardDisplay .sliderCardInfo .collegeLogo {
    width: 56px;
    height: 56px;
    border-radius: 4px;
    margin-top: -60px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
    margin-bottom: 5px;
  }
  
  .four-cardDisplay a {
    text-decoration: none;
  }
  
  .four-cardDisplay .displayCard:hover h3,
  .four-cardDisplay a:hover .widgetCardHeading {
    color: var(--anchor-textclr);
    text-decoration: underline;
  }
  
  .audio-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    align-items: center;
  }
  
  .audio-container audio {
    width: 100%;
    border-radius: 10px;
  }
  
  .audio-text {
    flex-basis: 100%;
    margin-bottom: 10px;
    font-size: 20px;
    font-weight: 600;
  }
  
  .webpSpriteIcon {
    display: inline-block !important;
    background: url(/yas/images/master_sprite.webp);
    text-align: left;
    overflow: hidden;
  }
  
  .authorInfoAndTranslateBtn {
    display: flex;
    justify-content: space-between;
  }
  
  .translateIcon1 {
    background-position: -635px -876px;
    width: 27px;
    height: 24px;
    vertical-align: middle;
    margin-right: 10px;
    display: inline-block;
  }
  
  .translateIcon2 {
    background-position: -676px -876px;
    width: 27px;
    height: 24px;
    vertical-align: middle;
    margin-right: 10px;
    display: inline-block;
  }
  
  .authorInfoAndTranslateBtn .updated-info.row {
    margin-top: 0;
  }
  
  .authorInfoAndTranslateBtn .translateBtn {
    padding: 5px 10px;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.73;
    color: #3d8ff2;
    border-radius: 3px;
    border: solid 1px #d8d8d8;
    background-color: #fff;
    text-transform: none;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
  }
  
  /* Missed CSS Fix */
  .custom-cardDisplay .sliderCardInfo {
    width: 275px;
    display: inline-block;
    padding: 0;
    white-space: initial;
  }
  
  .pageData p,
  .pageData li,
  .pageData a {
    font-size: 15px;
    line-height: 26px;
  }
  
  .two-cardDisplay .sliderCardInfo {
    width: 48.4%;
    display: inline-block;
    padding: 20px;
    margin-right: 18px;
  }
  
  .two-cardDisplay .sliderCardInfo:last-child {
    margin-right: 0;
  }
  
  .two-cardDisplay a {
    color: var(--primary-font-color);
    font-weight: 600;
  }
  
  .two-cardDisplay a:hover {
    color: var(--anchor-textclr);
  }
  
  .two-cardDisplay .customSliderCards .sliderCardInfo {
    min-height: 180px;
  }
  
  .two-cardDisplay .viewAllDiv {
    min-height: 160px;
  }
  
  .custom-cardDisplay .viewAllDiv {
    min-height: inherit;
  }
  
  .custom-cardDisplay .viewAllDiv {
    min-height: 303px;
  }
  
  .two-cardDisplay .viewAllIcon {
    background-position: 424px -73px;
    margin: 0 auto;
    margin-bottom: 5px;
    width: 44px;
    height: 40px;
  }
  
  .viewAllDiv a {
    color: var(--color-red);
  }
  
  .otherCategorySection {
    position: relative;
    padding-bottom: 10px;
  }
  
  .ohterCategoryArticles .row {
    margin: 0;
    flex-wrap: nowrap;
    overflow: auto;
  }
  
  .ohterCategoryArticles .row::-webkit-scrollbar {
    display: none;
  }
  
  .ohterCategoryArticles .categoryArticles {
    flex-basis: 8%;
    margin-right: 20px;
    margin-bottom: 10px;
  }
  
  .ohterCategoryArticles .categoryArticles:hover p {
    color: var(--anchor-textclr);
  }
  
  .ohterCategoryArticles .categoryArticles:last-child {
    margin-right: 0;
  }
  
  .ohterCategoryArticles .categoryArticlesImg {
    text-align: center;
    padding: 10px;
    border: var(--border-line);
    margin-bottom: 7px;
  }
  
  .ohterCategoryArticles .categoryArticlesImg .courseSprite {
    margin: 0;
  }
  
  .ohterCategoryArticles p {
    padding: 0;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
  }
  
  .ohterCategoryArticles a {
    text-decoration: none;
  }
  
  .ohterCategoryArticles .row {
    flex-wrap: nowrap;
    overflow: auto;
  }
  
  .ohterCategoryArticles .categoryArticles p {
    font-size: 14px;
  }
  
  .otherCategorySection .scrollRight {
    right: -20px;
  }
  
  .otherCategorySection .scrollLeft {
    top: 50%;
    left: -20px;
  }
  
  .pageData {
    background: var(--color-white);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    border: var(--border-line);
  }
  
  .pageData h2 {
    font-size: 18px;
    line-height: 28px;
    padding: 8px 20px;
    margin: 0;
    margin-bottom: 20px;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    background: #f5f5f5;
    text-transform: uppercase;
    position: relative;
  }
  
  .pageData h2 a {
    font-size: 14px;
    line-height: 24px;
    color: var(--color-red);
    font-weight: 500;
    text-transform: capitalize;
    display: block;
  }
  
  .pageData h2.row {
    display: flex;
  }
  
  .pageData p,
  .pageData li,
  .pageData a {
    font-size: 15px;
    line-height: 26px;
  }
  
  .pageData p {
    color: var(--primary-font-color);
    padding-bottom: 15px;
  }
  
  .pageData h3 {
    font-size: 17px;
    line-height: 24px;
    padding-bottom: 10px;
    color: var(--primary-font-color);
  }
  
  .pageData h4 {
    padding-bottom: 10px;
    line-height: 24px;
    font-weight: 500;
  }
  
  .courseSprite {
    display: inline-block;
    background: url(../../images/course_sprite.webp);
    text-align: left;
    overflow: hidden;
    margin-right: 16px;
    width: 68px;
    height: 67px;
    vertical-align: middle;
    cursor: pointer;
  }
  
  .engineering {
    background-position: -123px -28px;
  }
  
  .management {
    background-position: -394px -117px;
  }
  
  .science {
    background-position: -305px -291px;
  }
  
  .pharmacy {
    background-position: -215px -28px;
  }
  
  .law {
    background-position: -305px -117px;
  }
  
  .education {
    background-position: -305px -200px;
  }
  
  .dental {
    background-position: -123px -292px;
  }
  
  .medical {
    background-position: -305px -27px;
  }
  
  .agriculture {
    background-position: -28px -292px;
  }
  
  .design {
    background-position: -27px -28px;
  }
  
  .commerce {
    background-position: -214px -202px;
  }
  
  .architecture {
    background-position: -215px -292px;
  }
  
  .arts {
    background-position: -393px -27px;
  }
  
  .paramedical {
    background-position: -28px -203px;
  }
  
  .computer {
    background-position: -122px -203px;
  }
  
  .mass-communication {
    background-position: -215px -116px;
  }
  
  .hotel-management {
    background-position: -27px -115px;
  }
  
  .aviation {
    background-position: -395px -202px;
  }
  
  .veterinary {
    background-position: -122px -115px;
  }
  
  .animation {
    background-position: -400px -291px;
  }
  
  .vocational-courses {
    background-position: -27px -374px;
  }
  
  .photoGallery .row {
    margin: 0;
  }
  
  .photoGallery h2.row {
    margin-bottom: 20px;
  }
  
  .photoGallery .picture {
    flex-basis: 23.7%;
    border-radius: 50%;
    margin-right: 20px;
    border-radius: 4px;
    border: var(--border-line);
    overflow: hidden;
  }
  
  .photoGallery .picture img {
    height: 206px;
    display: block;
    margin: 0 auto;
  }
  
  .photoGallery .picture:last-child,
  .photoGallery .picture:nth-of-type(4n) {
    margin-right: 0;
  }
  
  /*GMU-471*/
  .getSupport {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 10px;
    border-radius: 0px;
    z-index: 5;
    /*display: flex;*/
    gap: 18px;
    font-size: 15px;
    font-weight: 400;
    line-height: 22px;
    color: #282828;
    align-items: center;
    justify-content: center;
    height: 58px;
    display: none;
  }
  
  .getSupport .getSupport__subheading {
    display: inline-block;
  }
  
  .getSupport .button__row__container {
    display: flex;
    gap: 13px;
    align-items: center;
  }
  
  .getSupport .row {
    display: none;
  }
  
  .getSupport button {
    border-radius: 2px;
    font-size: 13px;
    padding: 6px 4px;
    width: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
  
  }
  
  .getSupport button:last-child {
    margin-left: 0;
  }
  
  .button__row__container {
    min-width: 300px;
  }
  
  .container nav {
    width: 100%;
    padding: 0px 15px;
  }
  
  /**/
  .article-aside {
    height: 100%;
    padding-bottom: 20px;
  }
  
  .articleInfo p:has(img)+p {
    text-align: center;
  }
  
  .table-content-ctn {
    border-radius: 4px;
    border: 1px solid #d8d8d8;
    margin-bottom: 40px;
  }
  
  .table-content-heading-article {
    background-color: #f5f5f5;
    padding: 10px 16px;
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    line-height: 24px;
    color: #282828;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
  }
  
  .downArrowIcon,
  .upArrowIcon {
    background-position: -151px -125px;
    width: 18px;
    height: 11px;
  }
  
  .table-content-article {
    padding: 0px 0px 10px 0px;
    margin: 0 0 10px 0;
    max-height: 200px;
    overflow-y: auto;
  }
  
  .table-content-article li {
    list-style-type: none;
    position: relative;
    padding: 0 30px;
    margin-top: 10px;
  }
  
  .rotate {
    transform: rotate(180deg);
    top: 0px !important;
  }
  
  a {
    color: #3d8ff2;
    text-decoration: none;
    cursor: pointer;
  }
  
  .table-content-ctn ::-webkit-scrollbar {
    width: 8px;
  }
  
  .table-content-ctn ::-webkit-scrollbar-thumb {
    background: #d8d8d8;
    border-radius: 3px;
    -webkit-box-shadow: inset 0 0 5px #d8d8d8;
  }
  
  .table-content-article {
    margin: 0;
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-out, padding 0.4s ease-out;
  }
  
  .table-content-article.open {
    max-height: 200px;
    padding: 10px 0;
    overflow: auto;
  }
  
  ul.table-content-article.open li:before {
    left: 5px;
  }
  
  .faq_answer p {
    line-height: 26px !important;
  }
  
  .faq_answer span {
    font-size: 15px !important;
    background-color: unset !important;
  }
  
  .faq_answer li {
    white-space: unset !important;
    font-size: 15px !important;
  }
  
  /* Scroll Top CSS */
  .scrollToh2CSS {
    scroll-margin-top: 53px;
  }
  
  .authorAndDate {
    margin-bottom: 10px;
  }

  .latestInfoSection h2{
    padding-left: 0px !important;
  }
  
  @media (max-width: 1023px) {
  
    .four-cardDisplay .sliderCardInfo,
    .custom-cardDisplay .sliderCardInfo {
      margin-right: 6px;
      width: 224px;
      display: inline-block !important;
      min-height: 274px;
    }
  
    .four-cardDisplay .sliderCardInfo:nth-of-type(4n),
    .custom-cardDisplay .sliderCardInfo:nth-of-type(4n) {
      margin-right: 6px;
    }
  
    .four-cardDisplay .sliderCardInfo:last-child,
    .custom-cardDisplay .sliderCardInfo:last-child {
      margin-right: 0;
    }
  
    .four-cardDisplay .sliderCardInfo img,
    .custom-cardDisplay .sliderCardInfo img {
      height: 168px;
    }
  
    .four-cardDisplay .sliderCardInfo .textDiv,
    .custom-cardDisplay .sliderCardInfo .textDiv {
      padding: 10px;
    }
  
    .four-cardDisplay .sliderCardInfo .widgetCardHeading,
    .custom-cardDisplay .sliderCardInfo .widgetCardHeading {
      font-weight: 400;
    }
  
    .four-cardDisplay .sliderCardInfo.mobileOnly,
    .custom-cardDisplay .sliderCardInfo.mobileOnly {
      vertical-align: bottom;
    }
  
    .four-cardDisplay .sliderCardInfo+.mobileOnly .viewAllDiv {
      min-height: 266px;
    }
  
    .sideBarSection {
      margin-bottom: 10px;
    }
  
    .sideBarSection .sidebarHeading {
      margin: 10px;
      margin-bottom: 0;
    }
  
    .sideBarSection .listCard {
      padding: 10px;
    }
  
    .sideBarSection .sidebarImgDiv {
      margin-right: 10px;
      flex-basis: 56px;
    }
  
    .sideBarSection .sidebarTextLink {
      flex-basis: calc(100% - 66px);
    }
  
    .sideBarSection .sidebarTextLink .cardText {
      padding-bottom: 5px;
    }
  
    .viewAllDiv {
      min-height: 274px;
    }
  
    .viewAllDiv {
      min-height: 274px;
    }
  
    .sidebarImgDiv {
      flex-basis: 56px;
      margin-right: 10px;
      min-height: 56px;
    }
  
    .sidebarImgDiv img {
      width: 56px;
      height: 56px;
    }
  
    .listCard:last-child .recentnewsDiv.row {
      margin: 0;
    }
  
    .fixedExamRelatedDiv {
      border: none;
      box-shadow: 0 2px 4px 0 rgb(0 0 0/12%);
    }
  
    .fixedExamRelatedDiv {
      position: fixed;
      top: 0;
      z-index: 2;
      width: 100%;
      max-width: 1206px;
      margin: 0 auto;
      height: 50px;
      margin-left: -10px;
    }
  
    .containerMargin {
      margin-top: -155px;
    }
  
    /*.getSupport {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      margin: 0;
      padding: 10px;
      border-radius: 0;
      z-index: 1;
    }
  
    .getSupport .row {
      display: none;
    }
  
    .getSupport button {
      width: 49%;
      border-radius: 2px;
      font-size: 13px;
      padding: 6px 4px;
    }
  
    .getSupport button.applyScholarship {
      margin-left: 0;
    }*/
  
    .examInfo {
      padding: 10px;
    }
  
    .examInfo h2 {
      padding: 8px 10px;
      font-size: 15px;
      margin-bottom: 10px;
      text-transform: uppercase;
    }
  
    .examInfo h3 {
      font-size: 15px;
    }
  
    .liveApllicationFormsInner .row,
    .clgWithCourseInner .row {
      overflow: auto;
      white-space: nowrap;
      display: block;
    }
  
    .liveApllicationFormsInner .row .applicationDiv,
    .liveApllicationFormsInner .row .clgWithCourseDiv,
    .clgWithCourseInner .row .applicationDiv,
    .clgWithCourseInner .row .clgWithCourseDiv {
      margin-right: 10px;
      margin-bottom: 0;
      display: inline-block !important;
      width: 86%;
      max-width: 86%;
      white-space: normal;
      vertical-align: middle;
      overflow: auto;
    }
  
    .liveApllicationFormsInner .clgWithCourseDiv .viewAllDiv,
    .clgWithCourseInner .clgWithCourseDiv .viewAllDiv {
      min-height: 350px;
    }
  
    .clgWithCourseInner .row .clgWithCourseDiv:nth-of-type(5n + 0) {
      margin-right: 10px;
    }
  
    .pageDescription {
      padding: 20px;
    }
  
    .pageDescription .liveIcon {
      width: 40px;
      height: 17px;
      background-position: -26px -329px;
      vertical-align: text-top;
      float: none;
    }
  
    .pageDescription h1 {
      font-size: 18px;
      font-weight: 400;
      line-height: 1.56;
      color: #282828;
      padding-bottom: 10px;
      display: inline;
    }
  
    .pageDescription .updated-info.row {
      align-items: flex-start;
      flex-wrap: nowrap;
    }
  
    .pageDescription .updated-info.row .updatedDetails {
      flex-wrap: wrap;
    }
  
    .pageDescription .updated-info.row ul {
      position: unset;
      margin-left: 0;
    }
  
    .sideBarSection {
      margin-bottom: 10px;
      width: 100%;
    }
  
    .sideBarSection .sidebarHeading {
      margin: 10px;
      margin-bottom: 0;
    }
  
    .sideBarSection .listCard {
      padding: 10px;
    }
  
    .sideBarSection .sidebarImgDiv {
      flex-basis: 56px;
      margin-right: 10px;
    }
  
    .sideBarSection .sidebarImgDiv img {
      width: 56px;
      height: 56px;
    }
  
    .sideBarSection .sidebarTextLink {
      flex-basis: calc(100% - 66px);
    }
  
    .sideBarSection .sidebarTextLink p {
      font-size: 12px;
      line-height: 16px;
    }
  
    .sideBarSection .sidebarTextLink .applyText {
      padding-top: 5px;
    }
  
    .customSlider .scrollLeft,
    .customSlider .scrollRight {
      display: none !important;
    }
  
    .blueBgDiv {
      background: var(--topheader-bg);
      width: 100%;
      height: 167px;
    }
  
    .bannerImg img {
      max-height: 400px;
    }
  
    /* .getFreeScholorship {
      margin-top: 20px;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    } */
  
    /* .getFreeScholorship.fixedScholarshipBtn {
      position: fixed;
      width: 100%;
      left: 0;
      top: 60px;
      z-index: 2;
      background: var(--color-white);
      padding: 0;
      border: 10px solid var(--color-white);
      margin: 0;
    }
  
    .getFreeScholorship .primaryBtn {
      display: block;
      font-weight: var(--font-bold);
    } */
  
    .dataContainer,
    .articleInfo {
      padding: 10px;
    }
  
    .dataContainer h2,
    .articleInfo h2 {
      font-size: 15px;
      padding: 8px 10px;
      margin-bottom: 10px;
    }
  
    .faq_section {
      padding: 10px;
    }
  
    .faq_section h2 {
      margin-bottom: 10px;
    }
  
    .faq_section p {
      font-size: 15px;
      padding: 10px;
      padding-left: 5px;
    }
  
    .faq_section .faq_answer {
      color: #787878;
      padding: 10px;
    }
  
    .trendingArtilce p,
    .recentArticles p,
    .recentnews p,
    .trendingNews p {
      padding: 10px;
    }
  
    .trendingArtilerList,
    .recentArticlesList,
    .recentnewsList {
      padding: 10px;
    }
  
    .setAlarmDiv .primaryBtn {
      border-radius: 0;
    }
  
    .pageDescription .updated-info.row ul {
      z-index: 0;
    }
  
    .articleInfo .latestUpdates {
      border-radius: 4px;
      margin-bottom: 20px;
    }
  
    .articleInfo .latestUpdates .cardHeading {
      font-size: 15px;
      font-weight: 500;
      line-height: 26px;
      padding-bottom: 10px;
    }
  
    .articleInfo .latestUpdates ul {
      margin: 0;
      padding-left: 20px;
    }
  
    .articleInfo .latestUpdates ul li {
      position: relative;
    }
  
    .articleInfo .latestUpdates ul li:before {
      content: "";
      position: absolute;
      width: 15px;
      height: 15px;
      left: -20px;
      top: 5px;
      background: url(../../images/master_sprite.webp);
      background-position: 339px -374px !important;
    }
  
    .articleInfo .latestUpdates ul li span {
      font-size: 14px;
      color: #ff4e53;
    }
  
    .articleInfo .latestUpdates ul li a {
      cursor: pointer;
      color: #3d8ff2;
      text-decoration: none;
    }
  
    .articleInfo .latestUpdates {
      padding: 10px;
    }
  
    .audio-text {
      font-size: 16px;
    }
  
    .authorInfoAndTranslateBtn {
      flex-wrap: wrap;
      gap: 10px;
    }
  
    .translateBtn {
      flex-basis: 100%;
    }
  
    /* Fixes */
    .otherCategorySection .scrollLeft,
    .otherCategorySection .scrollRight {
      display: none !important;
    }
  
    .pageData,
    .reviewsSection {
      padding: 10px;
      margin-bottom: 10px;
      word-break: break-word;
    }
  
    .pageData h2,
    .reviewsSection h2 {
      font-size: 18px;
      line-height: 24px;
      padding: 8px 10px;
      margin-bottom: 10px;
    }
  
    .pageData h2,
    .pageData h3 {
      background: 0 0;
      padding-left: 0;
      font-size: 15px;
    }
  
    .pageData p,
    .reviewsSection p {
      padding-bottom: 10px;
    }
  
    .two-cardDisplay .sliderCardInfo {
      padding: 10px;
      width: 271px;
      margin-right: 6px;
    }
  
    .two-cardDisplay .sliderCardInfo .row {
      display: block;
    }
  
    .two-cardDisplay .sliderCardInfo .clgLogo {
      margin-bottom: 10px;
      margin-right: 0;
    }
  
    .two-cardDisplay .sliderCardInfo p:first-child {
      font-size: 14px;
    }
  
    .getSupport button.applyNow {
      margin-left: 0;
      width: 49%;
    }
  
    .photoGallery h2.row {
      margin-bottom: 10px;
    }
  
    .photoGallery .row {
      display: block;
      overflow: auto;
      white-space: nowrap;
    }
  
    .photoGallery .picture {
      display: inline-block;
      width: 224px;
      margin-right: 5px;
    }
  
    .photoGallery .picture.mobileOnly {
      display: inline-block !important;
      margin-left: 5px;
    }
  
    .photoGallery .picture .viewAllDiv {
      min-height: 170px;
    }
  
    .photoGallery .picture img {
      height: 168px;
      width: 100%;
    }
  
    .getSupport .getSupport__subheading {
      display: none;
    }
  
    .getSupport .button__row__container {
      width: 100%;
    }
  
    .getSupport button {
      flex-grow: 1;
    }
  
    .container nav {
      padding: 0px;
    }
  
    .getSupport .button__row__container {
      gap: 5px;
    }
  
    .getSupport {
      padding: 3px;
    }
  
    .getSupport button {
      padding: 6px 0px;
      font-size: 12px;
    }
  }
  .pageHeading {
    padding: 20px 0;
  }
  
  .pageHeading h1 {
    font-weight: normal;
    font-size: 24px;
    line-height: 38px;
  }
  
  .catgegoryArticle {
    border-radius: 4px;
    box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    margin-bottom: 20px;
  }
  
  .catgegoryArticle .row {
    margin: 0;
  }
  
  .catgegoryArticle .articleBanner {
    flex-basis: 309px;
    display: flex;
    align-items: center;
    border-right: var(--border-line);
  }
  
  .catgegoryArticle .articleBanner img {
    max-width: 309px;
    max-height: 232px;
    display: block;
    cursor: pointer;
  }
  
  .catgegoryArticle .articleText {
    flex-basis: calc(100% - 310px);
    padding: 20px;
  }
  
  .catgegoryArticle .articleText h2 {
    font-size: 16px;
    line-height: 24px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 2;
    margin-bottom: 10px;
    font-weight: 500;
    background-color: white;
  }
  
  .catgegoryArticle .articleText h2 a {
    text-decoration: none;
    color: var(--primary-font-color);
  }
  
  .catgegoryArticle .articleText h2 a:hover {
    color: var(--anchor-textclr);
    text-decoration: underline;
  }
  
  .catgegoryArticle .articleText p {
    font-size: 15px;
    line-height: 26px;
    color: #787878;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 3;
    margin-bottom: 20px;
  }
  
  .catgegoryArticle .updated-info {
    font-size: 14px;
    line-height: 20px;
  }
  
  .catgegoryArticle .updated-info.row {
    -webkit-box-align: center;
            align-items: center;
  }
  
  .catgegoryArticle .updated-info .updatedBy {
    padding-right: 5px;
  }
  
  .catgegoryArticle .updated-info .updatedBy img {
    width: 36px;
    height: 36px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
    border-radius: 50%;
  }
  
  .catgegoryArticle .updated-info .updatedBy .authorName {
    font-weight: var(--font-semibold);
    /* cursor: pointer; */
    display: inline-block;
    vertical-align: middle;
  }
  
  .catgegoryArticle .updated-info p {
    margin-bottom: 0;
    line-height: 20px;
  }
  
  .registerLatestArticle {
    border-radius: 4px;
    box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .registerLatestArticle .row {
    -webkit-box-align: center;
            align-items: center;
    flex-wrap: nowrap;
    margin: 0;
  }
  
  .registerLatestArticle img {
    max-width: 84px;
  }
  
  .registerLatestArticle p {
    font-size: 18px;
    line-height: 26px;
    margin-left: 20px;
  }
  
  .registerLatestArticle .registerNow {
    display: block;
    width: 100%;
    margin-top: 20px;
    font-weight: var(--font-semibold);
  }
  
  .getSupport {
    padding: 19px;
    background: var(--color-white);
    border: var(--border-line);
    border-radius: 3px;
    margin-bottom: 20px;
  }
  
  .getSupport .row {
    margin: 0;
    align-items: center;
    flex-wrap: nowrap;
    margin-bottom: 20px;
  }
  
  .getSupport img {
    width: 80px;
    height: 80px;
    margin-right: 20px;
  }
  
  .getSupport p {
    font-size: 18px;
    line-height: 26px;
  }
  
  .getSupport button {
    width: 161px;
    border-radius: 3px;
    font-size: 14px;
    line-height: 24px;
    padding: 6px;
    text-align: center;
    color: var(--color-white);
    font-weight: var(--font-bold);
    border: none;
  }
  
  .getSupport button.talkToExpert {
    background: var(--topheader-bg);
  }
  
  .getSupport button.applyNow {
    background: var(--color-red);
    margin-left: 0px;
    width: 172px;
  }
  
  .articleSidebarSection {
    border-radius: 4px;
    box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
    margin-bottom: 20px;
  }
  
  .articleSidebarSection .tab-content.activeLink {
    display: block;
  }
  
  .articleSidebarSection ul {
    margin: 0;
    padding: 0;
    display: -webkit-box;
    display: flex;
  }
  
  .articleSidebarSection ul li {
    list-style-type: none;
    flex-basis: 100%;
    text-align: center;
    font-size: 14px;
    line-height: 24px;
    color: #787878;
    cursor: pointer;
    padding: 12px 5px;
    padding-bottom: 9px;
    border-bottom: var(--border-line);
    font-weight: var(--font-semibold);
  }
  
  .articleSidebarSection ul li.activeLink {
    color: var(--color-red);
    border-bottom: 3px solid var(--color-red);
  }
  
  .trendingArtilerList,
  .recentArticlesList {
    max-height: 490px;
    overflow: auto;
    padding: 20px;
  }
  
  .trendingArtilerList::-webkit-scrollbar-thumb,
  .recentArticlesList::-webkit-scrollbar-thumb {
    background: #ccc;
  }
  
  .trendingArtilerList::-webkit-scrollbar,
  .recentArticlesList::-webkit-scrollbar {
    width: 5px;
  }
  
  .trendingArtilerDiv.row,
  .recentArticlesDiv.row {
    margin: 0;
    flex-wrap: nowrap;
    margin-bottom: 10px;
    border-bottom: var(--border-line);
    padding-bottom: 10px;
    -webkit-box-align: center;
            align-items: center;
  }
  .recentArticlesDiv.row:hover h3, 
  .trendingArtilerDiv.row:hover h3{
    color: var(--anchor-textclr);
  }
  .listCard:last-child .trendingArtilerDiv.row,
  .listCard:last-child .recentArticlesDiv.row {
    margin-bottom: 0;
    border: none;
    padding: 0;
  }
  
  .trendingArtilerDiv img,
  .recentArticlesDiv img {
    width: 96px;
    max-height: 72px;
    display: block;
    align-self: center;
  }
  .sidebarImgDiv{
    flex-basis: 96px;
    margin-right: 16px;
    display: grid;
    min-height: 72px;
  }
  .trendingArtileText, .recentArticlesDivText{
    flex-basis: calc(100% - 96px - 16px);
  }
  
  .trendingArtilerDiv .trendingArtileText .sidebarTextLink,
  .trendingArtilerDiv .recentArticlesDivText .sidebarTextLink,
  .recentArticlesDiv .trendingArtileText .sidebarTextLink,
  .recentArticlesDiv .recentArticlesDivText .sidebarTextLink,
  .trendingArtilerDiv .trendingArtileText h3,
  .trendingArtilerDiv .recentArticlesDivText h3,
  .recentArticlesDiv .trendingArtileText h3,
  .recentArticlesDiv .recentArticlesDivText h3{
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: 500;
    text-decoration: none;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 2;
    padding: 0;
    font-weight: normal;
  }
  
  .trendingArtilerDiv .trendingArtileText a:hover,
  .trendingArtilerDiv .recentArticlesDivText a:hover,
  .recentArticlesDiv .trendingArtileText a:hover,
  .recentArticlesDiv .recentArticlesDivText a:hover {
    text-decoration: underline;
    color: var(--anchor-textclr);
  }
  .trendingArtilerDiv.row:hover .sidebarTextLink, 
  .recentArticlesDiv.row:hover .sidebarTextLink,
   .recentnewsDiv.row:hover .sidebarTextLink, 
   .trendingArtilerDiv.row:hover a, 
   .recentArticlesDiv.row:hover a,
   .recentnewsDiv.row:hover a{
    text-decoration: underline;
    color: var(--anchor-textclr);
  }
  
  .pageFooter {
    padding-bottom: 0;
  }
  
  
  /* theme css  */
  .pageHeading {
    padding-top: 10px;
  }
  .catgegoryArticle,
  .registerLatestArticle,
  .registerLatestArticle,
  .articleSidebarSection {
    box-shadow: none;
    border: var(--border-line);
    background: var(--color-white);
  }
  .examTypeDiv,
  .pagination li,
  ul.pagination li {
    background: var(--color-white);
  }
  .pagination li.active,
  ul.pagination li.active a {
    background: var(--color-red);
  }
  @media (max-width: 1023px) {
    .pageHeading {
      margin-top: -160px;
      border-radius: 4px;
      box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
      background-color: var(--color-white);
      padding: 20px;
      margin-bottom: 10px;
    }
  
    .pageHeading h1 {
      font-size: 18px;
      line-height: 28px;
    }
  
    .categoryArticlesList {
      padding: 10px;
      border-radius: 4px;
      box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
      background-color: var(--color-white);
    }
  
    .catgegoryArticle {
      box-shadow: none;
      border: var(--border-line);
      margin-bottom: 10px;
    }
  
    .catgegoryArticle .row {
      display: block;
    }
  
    .catgegoryArticle .articleBanner img {
      max-width: 100%;
      width: 100%;
      cursor: pointer;
    }
  
    .catgegoryArticle .articleText {
      padding: 10px;
    }
  
    .catgegoryArticle .articleText h2 {
      font-size: 15px;
      line-height: 25px;
      -webkit-line-clamp: 3;
    }
  
    .catgegoryArticle .articleText p {
      font-size: 14px;
      line-height: 24px;
      -webkit-line-clamp: 5;
      margin-bottom: 10px;
    }
  
    .catgegoryArticle .updated-info.row {
      display: -webkit-box;
      display: flex;
    }
  
    .catgegoryArticle .updated-info p {
      margin-bottom: 0px;
    }
  
    .registerLatestArticle {
      display: none;
    }
  
    .setAlarmDiv {
      position: fixed;
      bottom: 0;
      left: 0;
      margin: 0;
      z-index: 2;
      padding: 0;
      width: 100%;
    }
  
    /* thtme css  */
    .categoryArticlesList {
      box-shadow: none;
      border: var(--border-line);
      background: var(--color-white);
    }
    .catgegoryArticle .articleBanner{
      border-right: 0px;
      border-bottom: var(--border-line);
      justify-content: center;
    }
    
    .getSupport {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      margin: 0;
      padding: 10px;
      border-radius: 0px;
      z-index: 1;
    }
  
    .getSupport .row {
      display: none;
    }
  
    .getSupport button {
      width: 49%;
      border-radius: 2px;
      font-size: 13px;
      padding: 6px 4px;
    }
  
    .getSupport button.applyNow {
      margin-left: 0;
      width: 162px;
    }
  }
  
  
  @media (min-width: 1023px) and (max-width: 1236px) {
    .col-md-4 .getSupport button {
      width: 125px;
    }
  }
  @media (max-width: 1023px) {
  .articleSection h2 {
    text-align: center;
    padding-top: 0;
    font-size: 15px;
  }
  .articleRelataedLinks {
    margin: 0 -10px;
  }
  .articleRelataedLinks ul {
    padding: 0 10px;
  }
  .articleRelataedLinks ul li a {
    padding: 8px 16px;
  }
  .articleRelataedLinks .btn_left,
  .articleRelataedLinks .btn_right {
    width: 36px;
    height: 36px;
  }
  .articleRelataedLinks .left_angle,
  .articleRelataedLinks .right_angle {
    margin: 6px 0;
  }
  .generalArticleSection {
    padding: 10px;
  }
  .generalArticleSection h3 {
    font-size: 15px;
    line-height: 24px;
    white-space: normal;
  }
  .generalArticleSection h3.row {
    margin-bottom: 10px;
  }
  .generalArticleSection h3 select {
    width: 100%;
    margin-top: 10px;
  }
  .articleList {
    display: none;
  }
  .bannerSection {
    margin-top: -160px;
    border-radius: 4px;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
  }
  .bannerSection h1 {
    padding-bottom: 10px;
    font-size: 18px;
    line-height: 24px;
  }
  .latestArticleSection.row {
    display: block;
    box-shadow: none;
    height: auto;
  }
  .articleDisplay .display_none {
    display: block;
  }
  .latestArticleSection .articleDisplay {
    display: block;
    white-space: nowrap;
    overflow: auto;
    height: auto;
  }
  .latestArticleSection .articleDisplay > div {
    display: inline-block;
    width: 70%;
    white-space: initial;
    border-radius: 4px;
    border: var(--border-line);
    margin-right: 10px;
    height: 276px;
  }
  .latestArticleSection .articleDisplay .row img {
    height: 168px;
  }
  .latestArticleSection .articleDisplay .aticleInfo {
    padding: 10px;
  }
  .latestArticleSection .articleDisplay .aticleInfo h2 {
    font-size: 15px;
    line-height: 26px;
    margin-bottom: 10px;
    min-height: 52px;
  }
  .latestArticleSection .articleDisplay .aticleInfo p,
  .latestArticleSection .articleDisplay .aticleInfo .updated-info img {
    display: none;
  }
  .latestArticleSection .articleDisplay .aticleInfo .updated-info a {
    font-weight: 400;
    color: #989898;
  }
  .articlesByCategory.row {
    display: block;
    overflow: auto;
    white-space: nowrap;
  }
  .articlesByCategory .browsedArticleDiv {
    display: inline-block !important;
    width: 70%;
    margin-right: 10px;
    margin-bottom: 0;
  }
  .viewAllArticleDiv {
    display: none;
  }
  .browsedArticleText h3 {
    min-height: 72px;
  }
  .articlesByCategory .browsedArticleDiv {
    min-height: 323px;
  }
  .latestInfoSection .latestInfoDiv {
    min-height: 290px;
  }
  .articlesByCategory .browsedArticleDiv:nth-of-type(4n + 0) {
    margin-right: 10px;
  }
  .articlesByCategory .browsedArticleDiv:last-child {
    margin-right: 0;
  }
  .viewAllIcon {
    margin: 0 auto;
    margin-bottom: 10px;
  }
  .latestArticleSection {
    border: none;
  }
  .bannerSection {
    border: var(--border-line);
    box-shadow: none;
  }
  .articleRelataedLinks .btn_left,
  .articleRelataedLinks .btn_right {
    height: 38px;
  }
  .articleRelataedLinks {
    margin: 0;
  }
  .articleRelataedLinks ul {
    padding: 0;
  }
  .articleDisplay figure {
    border-right: 0;
    border-bottom: var(--border-line);
  }
  .latestArticleSection img {
    -o-object-fit: cover;
    object-fit: cover;
  }
  .latestArticleSection .articleDisplay {
    flex-basis: auto;
    height: 276px;
  }
  .latestArticleSection .articleDisplay .aticleInfo {
    padding: 10px;
  }
  .latestArticleSection .articleDisplay .aticleInfo h2 {
    line-height: 18px;
    min-height: auto;
    overflow-wrap: break-word;
  }
  .latestArticleSection .articleDisplay:after {
    display: none;
  }
  .latestArticleSection .articleDisplay > div {
    position: relative;
    vertical-align: bottom;
  }
  .latestArticleSection .articleDisplay > div:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 0%,
      #282828 56%
    );
    border-radius: 4px;
  }
  .latestArticleSection .articleDisplay .mobileOnly {
    vertical-align: bottom;
    display: inline-block !important;
    margin-right: 0;
  }
  .latestArticleSection .articleDisplay .mobileOnly:after {
    display: none;
  }
  .latestArticleSection .articleDisplay .aticleInfo p {
    line-height: 20px;
    font-weight: 400;
  }
  .latestArticleSection .aticleInfo img.gifLive {
    max-width: 70px;
    height: 30px;
    margin-bottom: 10px;
  }
  .latestArticleSection .viewAllDiv {
    min-height: 275px;
  }
  .browsedArticleDiv .viewAllDiv {
    min-height: 350px;
  } 
  }