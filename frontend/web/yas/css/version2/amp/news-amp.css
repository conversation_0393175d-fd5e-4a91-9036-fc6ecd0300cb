html {
    box-sizing: border-box;
}

*,
::after,
::before {
    box-sizing: inherit
}

.container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto
}

.row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px
}

.col-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%
}

.col-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%
}

.col-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
}

.col-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%
}

.col-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%
}

.col-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
}

.col-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%
}

.col-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%
}

.col-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
}

.col-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%
}

.col-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%
}

.col-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
}


@media (min-width:768px) {
    .bannerDiv .authorName {
        padding-left: 10px;
    }

    .col-md {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -ms-flex-positive: 1;
        flex-grow: 1;
        min-width: 0;
        max-width: 100%
    }


    .col-md-auto {
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: 100%
    }

    .col-md-1 {
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%
    }

    .col-md-2 {
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%
    }

    .col-md-3 {
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-md-4 {
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%
    }

    .col-md-5 {
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%
    }

    .col-md-6 {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-md-7 {
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%
    }

    .col-md-8 {
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%
    }

    .col-md-9 {
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .col-md-10 {
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%
    }

    .col-md-11 {
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%
    }

    .col-md-12 {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
}

/* amp styling */


amp-selector[role=tablist].tabs-with-flex {
    display: flex;
    flex-wrap: wrap;
}

amp-selector[role=tablist].tabs-with-flex [role=tab] {
    flex-grow: 1;
    text-align: center;
    text-align: center;
    padding: 10px 15px;
    background-color: #f1f1f1;
    border-radius: 2px 2px 0px 0px;
    cursor: pointer;
    margin-right: 3px;
    font-weight: 700;
}

amp-selector[role=tablist].tabs-with-flex [role=tab]:last-child {
    margin-right: 0;
}

amp-selector[role=tablist].tabs-with-flex [role=tab][selected] {
    outline: none;
    background: var(--color-red);
    color: var(--color-white);
}

amp-selector[role=tablist].tabs-with-flex [role=tabpanel] {
    display: none;
    width: 100%;
    order: 1;
    padding: var(--space-4);
}

amp-selector[role=tablist].tabs-with-flex [role=tab][selected]+[role=tabpanel] {
    display: block;
}

amp-selector[role=tablist].tabs-with-flex [role=tab][selected]+[role=tabpanel] input {
    width: 100%;
    padding: 5px 10px;
    font-size: 15px;
    height: 40px;
    outline: none;
}

amp-selector[role=tablist].tabs-with-selector {
    display: flex;
}

amp-selector[role=tablist].tabs-with-selector [role=tab][selected] {
    outline: none;
}

amp-selector[role=tablist].tabs-with-selector {
    display: flex;
}

amp-selector[role=tablist].tabs-with-selector [role=tab] {
    width: 100%;
    text-align: center;
    padding: var(--space-1);
}

amp-selector.tabpanels [role=tabpanel] {
    display: none;
    padding: var(--space-4);
}

amp-selector.tabpanels [role=tabpanel][selected] {
    outline: none;
    display: block;
}

amp-nested-menu [amp-nested-submenu-open],
amp-nested-menu [amp-nested-submenu-close] {
    border: none;
    background: transparent;
    padding: 0;
    position: relative;
    width: 100%;
    text-align: left;
    height: auto;
    outline: none;
}

amp-nested-menu [amp-nested-submenu-close] {
    background: #fafbfc;
    font-size: 14px;
    line-height: 20px;
    text-transform: uppercase;
    padding: 12px 20px;
    margin: -20px;
    margin-bottom: 10px;
    outline: none;
}

body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
     font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}
/* 
* {
    letter-spacing: 0.3px;
    font-family: "Roboto", sans-serif;
} */

a {
    color: #3d8ff2;
    text-decoration: none;
}

.pb-8 {
    padding-bottom: 8px;
}

:root {
    --font-bold: 700;
    --font-semibold: 600;
    --font-500: 500;
    --fontsize-14: 14px;
    --fontsize-15: 15px;
    --primary-font-color: #333333;
    --color-red: #ff4e53;
    --color-white: #ffffff;
    --anchor-textclr: #3d8ff2;
    --transition: 0.2s ease;
    --border-line: 1px solid #eaeaea;
    --footer-bg: #273553;
    --topheader-bg: #545ebd;
}

li {
    font-size: 15px;
    line-height: 28px;
}

html {
    scroll-behavior: smooth;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
}

h2 {
    font-weight: var(--font-semibold);
}

.container {
    max-width: 1236px;
}

img {
    max-width: 100%;
    height: auto;
}

figure {
    margin: 0;
}

figcaption {
    text-align: center;
    padding: 15px 0;
    font-size: 15px;
}

.table-responsive {
    overflow: auto;
    border-radius: 4px;
    margin-bottom: 20px;
}

table {
    border-spacing: 0;
    width: 100%;
    color: #333333;
    border: 0.2px solid #eaeaea;
    border-bottom: 0px;
}

table th {
    background: #f1f3f4;
    padding: 10px;
    font-size: 15px;
    line-height: 26px;
    text-align: left;
    border-right: 0.2px solid #eaeaea;
    border-bottom: 0.2px solid #eaeaea;
}

table th:last-child {
    border-right: none;
}

table td {
    font-size: 14px;
    line-height: 24px;
    padding: 11px;
    border-right: 0.2px solid #eaeaea;
    border-bottom: 0.2px solid #eaeaea;
    /* min-width: 130px; */
}

table td:last-child {
    border-right: none;
}

caption {
    color: #5a5cc3;
    margin-bottom: 10px;
    font-size: 15px;
}

.no_padding {
    padding: 0;
}

.no_margin {
    margin: 0;
}

.autoComplete_list {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: var(--color-white);
    border: 1px solid #ccc;
}

.autoComplete_list li.no_result {
    list-style-type: none;
}

.autoComplete_result {
    margin: 0;
    border-radius: 0px;
    font-size: 15px;
}

.breadcrumbDiv ul {
    margin: 0;
    padding: 0;
    display: block;
    white-space: nowrap;
    overflow: auto;

}

.breadcrumbDiv ul li {
    font-size: 13px;
    display: inline-block;
    line-height: 20px;
    color: var(--color-white);
}

.breadcrumbDiv ul li a {
    text-decoration: none;
    position: relative;
    padding-right: 13px;
    color: var(--color-white);
    font-weight: var(--font-semibold);
}

.breadcrumbDiv ul li a:after {
    content: "";
    background: url("/yas/images/master_sprite.webp");
    width: 10px;
    height: 12px;
    position: absolute;
    right: 0;
    bottom: 1px;
    background-position: 707px -118px;
}

.primaryBtn,
a.primaryBtn,
button.primaryBtn {
    display: inline-block;
    background: var(--color-red);
    font-size: 14px;
    line-height: 20px;
    color: var(--color-white);
    padding: 8px 15px;
    font-weight: var(--font-semibold);
    border-radius: 3px;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    border: none;
    -webkit-transition: 0.2s ease;
    transition: 0.2s ease;
    outline: none;
}

.fweight-500 {
    font-weight: 500;
}

.nextStorySection {
    padding: 20px;
    margin-bottom: 20px;
    border: var(--border-line);
    background: var(--color-white);
}

.nextStorySection .row {
    margin: 0px;
    align-items: center;
}

.nextStorySection .imgDiv {
    flex-basis: 120px;
    margin-right: 20px;
}

.nextStorySection .imgDiv img {
    border-radius: 4px;
    width: 100%;
    height: 95px;
    display: block;
}

.nextStorySection .textDiv {
    flex-basis: calc(100% - 140px);
}

.nextStorySection p {
    font-size: 15px;
    line-height: 28px;
}

.nextStorySection p a,
.nextStorySection .clickLink {
    color: var(--color-red);
    font-weight: 500;
    cursor: pointer;
}

.nextStorySection .clickLink:hover {
    text-decoration: underline;
}

.nextStorySection .nextStoryHeading {
    line-height: 38px;
    padding-bottom: 10px;
    font-weight: 500;
    color: var(--primary-font-color);
}

.nextStorySection .nextStoryHeading:hover {
    text-decoration: underline;
    color: var(--anchor-textclr);
    cursor: pointer;
}

@media (max-width: 1023px) {

    /* news lead css starts */
    .page-header {
        height: 46px;
    }

    .page-header .topHeader {
        padding: 7px 20px;
        height: inherit;
    }

    .page-header .headerLogo {
        width: 121px;
        height: 31px;
        background-position: -416px -803px;
        vertical-align: middle;
        transform: none;
        margin-left: 20px;
    }

    .page-header .headerSubscribeButton {
        padding: 2px 8px;
        border-radius: 4px;
        background-color: #fff;
        width: 103px;
        text-decoration: none;
    }

    .page-header .headerSubscribeButton .bellIcon {
        width: 16px;
        height: 16px;
        background-position: -276px -882px;
        vertical-align: middle;
    }

    .page-header .headerSubscribeButton p {
        display: inline;
        font-size: 12px;
        font-weight: bold;
        line-height: 2.17;
        color: #0966c2;
        margin-left: 10px;
    }

    .headerToolDiv {
        padding: 10px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        border: solid 1px #d8d8d8;
        background-image: linear-gradient(to right, #fff 0%, #fffbec 100%);
        border-left: 6px solid #ff4e53;
        margin-bottom: 10px;
        position: sticky;
        top: 0px;
        z-index: 0;
    }

    .headerToolDiv h2 {
        font-size: 15px;
        font-weight: 600;
        line-height: 26px;
        color: #282828;
    }

    .headerToolDiv .headerToolCTADiv {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-top: 5px;
    }

    .headerToolDiv .headerToolCTADiv p {
        font-size: 15px;
        font-weight: 400;
        line-height: 24px;
        color: #282828;
        max-width: 250px;
    }

    .headerToolDiv .headerToolCTADiv button {
        flex-basis: 96px;
        flex-shrink: 0;
        height: 30px;
        border-radius: 3px;
        background-color: #ff4e53;
        border: none;
        font-size: 14px;
        font-weight: bold;
        line-height: 1.71;
        color: #fff;
    }

    .headerCTAPair {
        display: flex;
        padding: 10px;
        background-color: #fff;
        margin-bottom: 10px;
        position: sticky;
        top: 0px;
        border: solid 1px #d8d8d8;
        z-index: 5;
    }

    .headerCTAPair button {
        flex: 1;
        border-radius: 3px;
        height: 34px;
        border: none;
        font-size: 14px;
        font-weight: bold;
        line-height: 1.71;
    }

    .headerCTAPair button:first-child {
        border: solid 1px #0966c2;
        background-color: #fff;
        margin-right: 10px;
        color: #0966c2;
    }

    .headerCTAPair button:last-child {
        background-color: #ff4e53;
        color: #fff;
    }

    .leadFormContainerNews,
    .logInPage {
        animation: slideIn 1000ms ease-in-out;
        position: fixed;
        left: 0;
        bottom: 0;
        right: 0;
    }

    @keyframes slideIn {
        from {
            transform: translateY(100vh);
        }

        to {
            transform: translateY(0vh);
        }
    }

    .closeLeadFormContainer {
        position: absolute;
        right: 10px;
        top: 11px;
        z-index: 8;
    }

    .closeLeadFormContainer .closeLeadForm {
        background-position: -99px -805px;
        width: 28px;
        height: 28px;
        border: none;
    }

    .leadFormContainerNews {
        top: 50px;
        height: auto;
        width: 100%;
        left: 10px;
        overflow: hidden;
        border-radius: 4px;
        z-index: 20;
    }

    .leadFormContainerNews .leadFormDiv {
        margin-top: 0px;
    }

    .leadFormContainerNews .leadFormDiv .headingText {
        text-transform: none;
        font-weight: 500;
    }

    /* .leadFormContainerNews .leadFormDiv .userInputs {
        display: none;
    } */

    .leadFormContainerNews .leadFormDiv .userInputs .row .col-md-6 {
        width: 100%;
    }

    /* .leadFormContainerNews .leadFormDiv .userInputs .row .col-md-6 select {
        color: #989898;
    } */

    .leadFormContainerNews .leadFormDiv .userInputs .row .mobileNumber .dialCodeDiv {
        background: none;
        padding-left: 10px;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .row.m-0 {
        margin: 0;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .checkbox-group {
        padding-left: 0;
        padding-right: 0;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .checkbox-group label {
        font-size: 12px;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .checkbox-group input[type=checkbox] {
        accent-color: #ff4e53;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .formSumbitBtn {
        width: 100%;
        padding: 0;
        padding-bottom: 10px;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .locationRow {
        margin: 0;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .locationRow .formLocationIcon {
        background-position: -253px -882px;
        width: 13px;
        height: 17px;
        margin-right: 12px;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .locationRow p {
        padding-bottom: 10px;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .locationRow .locationCity {
        color: #282828;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .locationRow .locationChange {
        color: #3d8ff2;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .accountExist {
        justify-content: center;
        /* margin-top: 10px; */
    }

    .leadFormContainerNews .leadFormDiv .userInputs .accountExist p {
        font-size: 14px;
        font-weight: normal;
        line-height: 1.71;
        color: #787878;
        padding-bottom: 0;
    }

    .subscribeSectionNews {
        position: relative;
        background: var(--color-white);
    }

    .align-items-center {
        align-items: center;
    }

    .pageBody {
        display: flex;
        height: 100%;
        justify-content: space-between;
        flex-direction: column;
        /* padding: 10px 0; */
        padding-top: 20px;
    }

    .logInPage {
        position: fixed;
        top: unset;
        left: unset;
        bottom: 0;
        z-index: 5;
        height: auto;
        width: 100%;
        overflow: hidden;
        border-radius: 4px;
        display: block;
        padding: 0px;
    }

    .logInPage .container {
        padding: 0 20px;
    }

    .logInPage .right-col {
        width: 100%;
    }

    .logInPage .right-col p {
        font-size: 14px;
        line-height: 24px;
        padding-bottom: 10px;
    }

    .logInPage .right-col h2 {
        font-size: 24px;
        line-height: 30px;
        font-weight: 500;
    }

    .logInPage .right-col .userForm {
        max-width: 350px;
        margin: 0 auto;
    }

    .logInPage .right-col .userForm .userIcon,
    .logInPage .right-col .userForm .mailIcon,
    .logInPage .right-col .userForm .locationIcon,
    .logInPage .right-col .userForm .bookIcon,
    .logInPage .right-col .userForm .capIcon {
        top: 12px;
    }

    .logInPage .right-col p {
        color: #787878;
    }

    .logInPage .right-col p a {
        color: var(--color-red);
        font-weight: 500;
    }

    .logInPage .right-col .disclaimer {
        font-size: 14px;
        font-weight: normal;
        line-height: 1.71;
        color: #787878;
        padding-bottom: 10px;
    }

    .logInPage .right-col .logInOptionNews {
        text-align: center;
        margin-left: 45px;
    }

    .logInPage img {
        margin: 0 auto;
        margin-bottom: 30px;
        margin-top: 20px;
        display: block;
    }

    .logInPage .optSection p {
        font-size: 14px;
        font-weight: normal;
        line-height: 1.71;
        color: #787878;
    }

    .logInPage .optSection .changeNumber {
        color: #ff4e53;
    }

    .logInPage .optSection .headingText {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.5;
        color: #282828;
        text-transform: uppercase;
    }

    .logInPage .optSection .pb-0 {
        padding-bottom: 0;
    }

    .logInPage .optSection .numberInputs {
        padding-bottom: 20px;
    }

    .logInPage .optSection .numberInputs input {
        border: 1px solid #d8d8d8;
        padding: 0;
        height: 46px;
        max-width: 60px;
        width: 60px;
    }

    .logInPage .thankYouMsgNews {
        padding: 0;
    }

    .logInPage .thankYouMsgNews .thankYouIcon {
        width: 100px;
        height: 100px;
        background-position: -253px -931px;
        margin-left: 88px;
    }

    .logInPage .thankYouMsgNews .thankYouText p:first-child {
        font-size: 24px;
        font-weight: 600;
        font-stretch: normal;
        line-height: 1.5;
        color: #282828;
        text-align: center;
    }

    .logInPage .thankYouMsgNews .thankYouText p:last-child {
        font-size: 16px;
        font-weight: normal;
        line-height: 1.5;
        color: #282828;
        text-align: center;
    }

    .formField {
        position: relative;
        max-width: 100%;
        padding-bottom: 15px;
    }

    .formField .row.m-0 {
        margin: 0;
        position: relative;
    }

    .formField input,
    .formField select,
    .formField .select2-container {
        padding: 7px 12px;
        padding-left: 41px;
        border-radius: 4px;
        border: var(--border-line);
        outline: none;
        width: 100%;
        font-size: 13px;
        line-height: 24px;
        background: var(--color-white);
        color: var(--primary-font-color);
    }

    .formField input::placeholder,
    .formField select::placeholder,
    .formField .select2-container::placeholder {
        color: #989898;
    }

    .formField select {
        background-position: 96% 15px;
    }

    .formField .numberInput {
        flex-basis: calc(100% - 90px);
    }

    .formField .numberInput input {
        padding-left: 12px;
        width: 100%;
        border-radius: 0 4px 4px 0;
        width: 100%;
        height: 40px;
    }

    .formField .numberInput .form-group {
        margin-bottom: 10px;
    }

    .dialCodeDiv {
        flex-basis: 90px;
        position: relative;
        border-radius: 4px 0 0 4px;
        border: var(--border-line);
        border-right: 0px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .dialCodeDiv .dialCode {
        color: #989898;
        font-size: 14px;
        line-height: 24px;
    }

    .dialCodeDiv img {
        margin: inherit;
    }

    .userForm .form-group {
        margin-bottom: 20px;
    }

    .userForm .primaryBtn {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
        margin-right: 0px;
    }

    .leadFormContainerNews {
        display: block;
        bottom: 0;
        left: unset;
        top: unset;
    }

    .pageMask {
        background: rgba(0, 0, 0, 0.5);
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        display: block;
        overflow-y: hidden;
        z-index: 10;
    }

    #leadpopup {
        background-color: #0966c2;
        color: #fff;
    }

    .leadFormContainerNews .leadFormDiv .userInputs .accountExist p span,
    p.logInOptionNews span {
        font-weight: 600;
        color: #ff4e53;
    }

    .leadFormDiv .mobileNumber .numberInput {
        padding-left: 0px;
    }

    .leadFormDiv .error {
        border: none;
    }

    [visible-when-invalid] {
        font-size: 12px;
    }

    /* news lead css ends */

    .latestInfoList .latestInfoDiv .viewAllDiv {
        min-height: 290px;
    }

    .nextStorySection {
        padding: 10px;
    }

    .nextStorySection .nextStoryHeading {
        line-height: 24px;
        padding: 0;
    }

    .nextStorySection .row {
        padding-bottom: 10px;
    }

    .nextStorySection .fweight-500 {
        padding-bottom: 10px;
    }

    .nextStorySection .imgDiv {
        margin-right: 13px;
    }

    .nextStorySection .textDiv {
        flex-basis: calc(100% - 133px);
    }

    .row {
        margin: 0 -10px;
    }

    .container {
        padding: 0 10px;
    }

    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-auto,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-sm-auto,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-md-auto,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-lg-auto,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xl-auto {
        padding: 0 10px;
    }

    .horizontalRectangle,
    .verticleRectangle,
    .squareDiv {
        display: block;
        margin-bottom: 20px;
    }

    .horizontalRectangle .appendAdDiv,
    .verticleRectangle .appendAdDiv {
        width: 300px;
        min-height: 50px;
        max-height: 250px;
        margin: 0 auto;
    }
}

.spriteIcon {
    display: inline-block;
    background: url('/yas/images/master_sprite.webp');
    text-align: left;
    overflow: hidden;
}

.viewAllIcon {
    width: 77px;
    height: 76px;
    background-position: 358px -73px;
    margin-bottom: 10px;
    display: block;
    margin-bottom: 10px;
}

.headerLogo,
.footerLogo {
    width: 179px;
    height: 44px;
    background-position: 256px -72px;
}

.searchIcon {
    width: 21px;
    height: 23px;
    background-position: 535px -72px;
    vertical-align: middle;
}

.hambergerIcon {
    width: 27px;
    height: 20px;
    background-position: 476px -72px;
    margin-left: 0px;
    vertical-align: middle;
    border: none;
    padding: 0;
}

.guestPic img {
    width: 72px;
    height: 72px;
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    border-radius: 50%;
}

.cancelIcon {
    width: 24px;
    height: 24px;
    background-position: 535px -132px;
    cursor: pointer;
}

.alarmIcon {
    width: 24px;
    height: 27px;
    margin-right: 8px;
    vertical-align: middle;
    background-position: 474px -166px;
}

.angle_left {
    width: 13px;
    height: 19px;
    background-position: 653px -131px;
    vertical-align: middle;
    margin-right: 5px;
}

.fbIcon {
    background-position: 476px -200px;
}

.twitterIcon {
    background-position: 476px -234px;
}

.instaIcon {
    background-position: 476px -268px;
}

.linkdIn {
    background-position: 476px -302px;
}

.youtubeIcon {
    background-position: 476px -364px;
}

.phoneIcon {
    width: 24px;
    height: 24px;
    background-position: 536px -246px;
    vertical-align: middle;
    margin-right: 7px;
}

.whiteMailIcon {
    width: 24px;
    height: 24px;
    background-position: 536px -219px;
    vertical-align: middle;
    margin-right: 7px;
}

.thankYouIcon {
    width: 123px;
    height: 123px;
    background-position: 255px -117px;
}

.topHeader .row {
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    justify-content: space-between;
}

.advanceSearch {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    background: #f9f9f9;
    z-index: 5;
}

.searchSection {
    max-width: 730px;
    margin: 60px auto;
}

.searchSection .search_heading {
    margin: 0;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    justify-content: space-between;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
}


.pageFooter {
    color: var(--color-white);
    margin-top: 20px;
    margin-bottom: 0;
}

.pageFooter p {
    color: var(--color-white);
}

.pageFooter .socialMedia {
    margin: 0;
    padding: 0;
    padding: 20px 0;
}

.pageFooter .socialMedia li {
    display: inline-block;
    margin-right: 5px;
}

.pageFooter .socialMedia li:first-child {
    display: block;
    padding-bottom: 10px;
    font-size: 14px;
    line-height: 20px;
    margin: 0;
    color: var(--color-white);
}

.pageFooter .socialMedia li a {
    width: 28px;
    height: 28px;
    display: block;
    border-radius: 50px;
}

.pageFooter .copyrightsText a {
    color: var(--color-white);
    text-decoration: none;
}

.pageFooter .footerPrimarySection {
    background: #273553;
}

.pageFooter .footerPrimarySection .row {
    margin: 0;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    justify-content: space-between;
}

.pageFooter .footerPrimarySection .contactInfo {
    padding: 0;
    margin: 0;
    display: inline-block;
}

.pageFooter .footerPrimarySection .contactInfo li {
    display: inline-block;
    padding: 0 10px;
}

.pageFooter .footerPrimarySection .contactInfo li a {
    color: var(--color-white);
}

.pageFooter .footerPrimarySection .socialMedia {
    padding: 0;
    display: inline-block;
    margin-right: 55px;
}

.pageFooter .footerPrimarySection .socialMedia li {
    display: -webkit-inline-box;
    display: inline-flex;
    vertical-align: middle;
}

.pageFooter .footerPrimarySection .socialMedia li:first-child {
    display: inline-block;
    padding: 0;
    font-weight: 500;
}

.pageFooter .footerSecondSection {
    background: #1f2b45;
    padding: 20px 0;
}

.pageFooter .footerSecondSection .copyrightsText {
    border: none;
    padding: 0;
}

.pageFooter .footerSecondSection .row {
    -webkit-box-align: center;
    align-items: center;
    margin: 0;
    -webkit-box-pack: justify;
    justify-content: space-between;
}

.pageFooter .footerSecondSection ul {
    padding: 0;
    margin: 0;
}

.pageFooter .footerSecondSection ul li a {
    color: var(--color-white);
    font-size: 14px;
    display: block;
    line-height: 28px;
}

.latestInfoSection {
    border-radius: 4px;
    background-color: var(--color-white);
    padding-bottom: 0;
    margin-bottom: 20px;
    padding: 10px;
    box-shadow: none;
    border: var(--border-line);
}

.latestInfoSection a {
    text-decoration: none;
}

.latestInfoSection h2 {
    line-height: 24px;
    background: #f5f5f5;
    margin: 0;
    text-transform: uppercase;
    font-size: 15px;
    padding: 10px;
    margin-bottom: 10px;
    font-weight: var(--font-semibold);
}



.latestInfoSection.row {
    margin: 0;
}

.latestInfoSection .latestInfoDiv figure {
    margin: 0;
    min-height: 168px;
}

.latestInfoSection .latestInfoDiv:last-child {
    margin-right: 0;
}


.latestInfoSection .latestInfoTxt p {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: var(--font-500);
    text-decoration: none;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 58px;
    margin-bottom: 10px;
}

.latestInfoSection .latestInfoTxt p:last-child {
    color: #989898;
    margin: 0;
    font-weight: normal;
}

.leadFormDiv .error {
    border: 1px solid #ff0000;
}

.has-error>input {
    border: 1px solid #ff0000;
}

.has-error>textarea {
    border: 1px solid #ff0000;
}

.formField {
    position: relative;
}

.thankYouMsg {
    text-align: center;
    background: var(--color-white);
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 3px;
}

.thankYouMsg .thankYouText {
    font-size: 24px;
    line-height: 26px;
    color: var(--primary-font-color);
    padding: 0;
    padding-top: 20px;
}

.leadFormContainer,
.writeAnswerForm {
    background: rgba(51, 51, 51, 0.6);
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    overflow: auto;
}

.leadFormDiv {
    max-width: 794px;
    margin: 50px auto;
    position: relative;
    border-radius: 4px;
    background: var(--color-white);
    padding: 35px;
    margin-top: 125px;
}

.leadFormDiv .formSumbitBtn {
    text-align: right;
}

.leadFormDiv .col-md-6 {
    padding: 0 10px;
}

.leadFormDiv .mobileNumber.row {
    margin: 0;
}

.leadFormDiv p {
    color: #787878;
    font-size: 16px;
    line-height: 28px;
    padding-bottom: 20px;
}

.leadFormDiv .headingText {
    font-size: 24px;
    line-height: 24px;
    padding-bottom: 10px;
    font-weight: var(--font-500);
    color: var(--primary-font-color);
    text-transform: uppercase;
}

.leadFormDiv .primaryBtn {
    font-size: 14px;
    line-height: 24px;
}

.leadFormDiv .form-group {
    padding-bottom: 20px;
    position: relative;
}

.leadFormDiv input,
.leadFormDiv select,
.leadFormDiv textarea,
.leadFormDiv .dialCodeDiv {
    padding: 11px 12px;
    padding-left: 41px;
    border-radius: 4px;
    border: var(--border-line);
    outline: none;
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    background: var(--color-white);
    min-height: 48px;
    color: var(--primary-font-color);
}

.leadFormDiv input::-webkit-input-placeholder,
.leadFormDiv select::-webkit-input-placeholder,
.leadFormDiv textarea::-webkit-input-placeholder,
.leadFormDiv .dialCodeDiv::-webkit-input-placeholder {
    color: #989898;
}

.leadFormDiv input::-moz-placeholder,
.leadFormDiv select::-moz-placeholder,
.leadFormDiv textarea::-moz-placeholder,
.leadFormDiv .dialCodeDiv::-moz-placeholder {
    color: #989898;
}

.leadFormDiv input:-ms-input-placeholder,
.leadFormDiv select:-ms-input-placeholder,
.leadFormDiv textarea:-ms-input-placeholder,
.leadFormDiv .dialCodeDiv:-ms-input-placeholder {
    color: #989898;
}

.leadFormDiv input::-ms-input-placeholder,
.leadFormDiv select::-ms-input-placeholder,
.leadFormDiv textarea::-ms-input-placeholder,
.leadFormDiv .dialCodeDiv::-ms-input-placeholder {
    color: #989898;
}

.leadFormDiv input::placeholder,
.leadFormDiv input .dialCode,
.leadFormDiv select::placeholder,
.leadFormDiv select .dialCode,
.leadFormDiv textarea::placeholder,
.leadFormDiv textarea .dialCode,
.leadFormDiv .dialCodeDiv::placeholder,
.leadFormDiv .dialCodeDiv .dialCode {
    color: #989898;
}

.leadFormDiv select::-webkit-input-placeholder {
    color: #989898;
}

.leadFormDiv select::-moz-placeholder {
    color: #989898;
}

.leadFormDiv select:-ms-input-placeholder {
    color: #989898;
}

.leadFormDiv select::-ms-input-placeholder {
    color: #989898;
}

.leadFormDiv select::placeholder {
    color: #989898;
}

.leadFormDiv select:first-child {
    color: #989898;
}

.leadFormDiv .countryCode {
    flex-basis: 101px;
    position: relative;
}

.leadFormDiv .countryCode input,
.leadFormDiv .countryCode .dialCodeDiv {
    background: #eaeaea;
    border-radius: 4px 0 0 4px;
    border-right: var(--border-line);
    text-align: center;
    padding-left: 12px;
}

.leadFormDiv .numberInput {
    flex-basis: calc(100% - 101px);
}

.leadFormDiv .numberInput input {
    border-left: none;
    border-radius: 0 4px 4px 0;
    padding-left: 12px;
}


.leadFormDiv .select2-container {
    width: 100%;
}

.leadFormDiv .select2-container--default .select2-selection--single .select2-selection__rendered {
    padding: 11px 12px;
    padding-left: 41px;
    border-radius: 4px;
    border: var(--border-line);
    outline: none;
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    background: var(--color-white);
    min-height: 48px;
    background-image: url(/yas/images/select-angle.png?9962fd8a0aedc3b373e4c9d4a758f3db);
    background-repeat: no-repeat;
    background-position: 95% 19px;
}

.leadFormDiv .select2-container--default .select2-selection--single .select2-selection__clear,
.leadFormDiv span.select2-selection__arrow {
    display: none;
}

.leadFormDiv .select2-container--default .select2-selection--single {
    border: none;
    height: auto;
    outline: none;
}

.leadFormDiv input.select2-search__field {
    height: 35px;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: var(--border-line);
    height: 35px;
    outline: none;
}



.checkbox-group {
    padding-bottom: 10px;
}

.checkbox-group input {
    width: 16px;
    height: 16px;
    min-height: auto;
    padding: 0;
    margin: 0;
    border-radius: 0px;
    vertical-align: middle;
    cursor: pointer;
    margin-right: 12px;
}

.checkbox-group label {
    font-size: 13px;
    line-height: 28px;
    color: #787878;
}

.numberInputs {
    padding-bottom: 30px;
}

.numberInputs input {
    max-width: 80px;
    margin-right: 18px;
    border-radius: var(--border-line);
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    padding: 12px;
    color: #989898;
    padding-left: 12px;
}

.numberInputs input:last-child {
    margin-right: 0;
}

.optSection .row {
    margin: 0;
    -webkit-box-align: center;
    align-items: center;
}

.optSection .primaryBtn {
    margin-right: 20px;
}

.optSection a {
    color: var(--color-red);
    text-decoration: none;
    font-size: 14px;
    line-height: 24px;
}

.userName input {
    position: relative;
}

.userIcon,
.mailIcon,
.locationIcon,
.bookIcon,
.capIcon {
    width: 20px;
    height: 19px;
    position: absolute;
    z-index: 1;
    left: 12px;
    top: 14px;
}

.userIcon {
    background-position: 595px -238px;
}

.mailIcon {
    background-position: 594px -262px;
}

.locationIcon {
    background-position: 594px -311px;
}

.bookIcon {
    background-position: 595px -285px;
}

.capIcon {
    background-position: 595px -333px;
}

.help-block {
    color: var(--color-red);
    font-size: 12px;
    padding-top: 5px;
}

.discussionForum .moreAnswer a:hover,
.discussionForum .writeAnswer a:hover {
    color: var(--color-white);
}

.discussionForum a {
    color: var(--primary-font-color);
}

.discussionForum a:hover {
    color: var(--anchor-textclr);
}

.discussionForum .moreAnswer:hover {
    color: var(--color-white);
}

.discussionForum .writeAnswer:hover {
    color: var(--color-red);
}


.faqDiv .faq_answer p,
.faqDiv .faq_answer li,
.faqDiv .faq_answer a {
    font-size: 15px;
    line-height: 26px;
    font-weight: normal;
    border: none;
}

.faqDiv .faq_answer {
    padding: 10px;
}

.faqDiv .faq_answer p {
    padding: 0;
    padding-bottom: 5px;
}

#otpResponseText {
    padding-top: 6px;
    padding-bottom: 0px;
    color: var(--color-red);
}

/* form img  */
.formHeadingDiv.row {
    margin: 0;
    -webkit-box-align: center;
    align-items: center;
    padding-bottom: 20px;
    padding-right: 15px;
}

.formHeadingDiv .formImg {
    flex-basis: 100px;
    margin-right: 20px;
    /* box-shadow: rgb(0 0 0 / 12%) 0px 1px 3px, rgb(0 0 0 / 24%) 0px 1px 2px; */
    border-radius: 50%;
    min-height: 100px;
    display: flex;
    align-items: center;
}

.formHeadingDiv .formImg img {
    max-width: 62px;
    max-height: 62px;
    display: block;
    margin: 0 auto;
    border-radius: 50%;
}

.formHeadingDiv .formHeading {
    flex-basis: calc(100% - 120px);
}

.formHeadingDiv .headingText {
    padding-right: 20px;
}

.formHeadingDiv p {
    padding: 0;
}

/* form img ends   */

.examInfo ul li,
.pageInfo ul li,

.browseByCategory ul li,
.articleInfo ul li {
    position: relative;
    list-style-type: none;
}

.examInfo ul li:before,
.pageInfo ul li:before,
.browseByCategory ul li:before,
.articleInfo ul li::before {
    content: "";
    background: url("/yas/images/master_sprite.webp");
    width: 12px;
    height: 17px;
    position: absolute;
    left: -19px;
    top: 5px;
    background-position: 651px -71px;
    z-index: 1;
}

.tagsDiv ul li::before {
    display: none;
}

.latestInfoSection .latestInfoTxt p {
    -webkit-line-clamp: initial;
    max-height: 100%;
}



.latestInfoSection .latestInfoDiv {
    position: relative;
}

.latestInfoSection amp-img {
    height: 196px;
}


.latestInfoDiv.no-shadow:after {
    display: none;
}

span.getmyuniLogoIcon {
    background-position: -660px -1062px;
    width: 62px;
    height: 62px;
}

@media (max-width: 1023px) {
    .clgWithCourse h2.row {
        white-space: inherit;
    }

    .pageInfo {
        max-height: 250px;
    }

    .leftLst {
        -webkit-transform: translate(0px, -50%) rotate(-180deg);
        transform: translate(0px, 0%) rotate(-180deg);
    }

    .contactUs {
        position: fixed;
        bottom: 56px;
        left: 10px;
        z-index: 3;
    }

    .setAlarmDiv {
        padding: 10px;
        margin: 0 -10px;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
        margin-bottom: 10px;
        font-weight: var(--font-semibold);
        margin: 0px -10px;
        position: fixed;
        bottom: 0;
        z-index: 1;
        background: var(--color-white);
        width: 100%;
        padding: 0;
    }

    .setAlarmDiv .primaryBtn {
        display: block;
        font-weight: var(--font-semibold);
        width: 100%;
    }

    .pageFooter .footerPrimarySection {
        padding: 20px 10px;
    }

    .pageFooter .footerPrimarySection .socialMedia {
        margin: 22px 0;
    }

    .pageFooter .footerPrimarySection .contactInfo li {
        padding: 0;
        padding-bottom: 10px;
    }

    .pageFooter .footerPrimarySection .contactInfo li:last-child {
        padding-bottom: 0px;
    }

    .pageFooter .footerSecondSection {
        padding: 20px 10px;
        padding-bottom: 60px;
    }

    .pageFooter .footerSecondSection ul {
        margin-bottom: 10px;
    }

    .pageFooter .footerSecondSection ul li {
        display: block;
        padding: 0;
        font-size: 14px;
    }

    .pageFooter .footerSecondSection .copyrightsText {
        font-size: 12px;
        line-height: 24px;
    }

    .breadcrumbDiv {
        padding: 8px;
    }
}

.topHeader {
    padding: 8px 20px;
    position: relative;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 4;
}

.topHeader ul,
.headerMegaMenu ul {
    display: none;
}

.slider-menu-option .row {
    -webkit-box-pack: space-evenly;
    justify-content: space-evenly;
    margin: 0;
}

.slider-menu-option,
amp-sidebar {
    background: var(--color-white);
}

.guestDisplay {
    padding: 20px;
    background: #fafbfc;
    text-align: center;
}

.guestDisplay p.welcomeText {
    line-height: 20px;
    font-size: 14px;
    padding-bottom: 20px;
    color: var(--primary-font-color);
}

.guestDisplay .writeReview,
.guestDisplay .register {
    color: var(--color-red);
    border: 1px solid var(--color-red);
    background: var(--color-white);
    font-size: 12px;
    padding: 10px 8px;
    text-decoration: none;
    font-weight: var(--font-semibold);
    text-transform: uppercase;
}

.guestDisplay .register {
    background: var(--color-red);
    color: var(--color-white);
    margin-left: 10px;
}

.closeMenu {
    position: absolute;
    right: 10px;
    top: 10px;
}

.slider-menu-option ul {
    margin: 0;
    padding: 20px;
    background: #fff;
}

.slider-menu-option ul li {
    list-style-type: none;
    border-bottom: 1px solid #eaeaea;
    padding: 9px 0;
    background: #fff;
    line-height: initial;
    min-height: 20px;
    vertical-align: middle;
}

.slider-menu-option ul.no_padding {
    padding: 0;
}

.slider-menu-option ul li:focus,
.slider-menu-option ul li a:focus {
    outline: none;
}

.slider-menu-option ul li a {
    color: var(--primary-font-color);
    font-size: 14px;
    line-height: 20px;
    text-decoration: none;
    display: block;
    position: relative;
}

.slider-menu-option ul li a.logOutOption {
    color: var(--color-red);
    font-weight: var(--font-semibold);
}

.slide_menu_colleges:after,
.study_abroad:after,
.slide_menu_courses:after,
.slide_menu_exams:after,
.slide_menu_boards:after,
.slide_menu_resources:after {
    content: " ";
    background: url("/yas/images/master_sprite.webp");
    width: 12px;
    height: 21px;
    position: absolute;
    right: 10px;
    background-position: 652px -93px;
}

.backToMenu,
.slider-menu-option ul li.backToMenu {
    background: #fafbfc;
    font-size: 14px;
    line-height: 20px;
    text-transform: uppercase;
    padding: 12px 20px;
}

.slider-menu-option ul li.backToMenu {
    border: none;
    margin: -20px;
    margin-bottom: 0;
}

.latestInfoList.row {
    overflow: auto;
    white-space: nowrap;
    display: block;
    margin: 0;
}

.latestInfoSection .latestInfoDiv {
    margin-right: 10px;
    margin-bottom: 0px;
    display: inline-block;
    width: 70%;
    white-space: normal;
    vertical-align: middle;
    overflow: auto;
    text-align: left;
    border-radius: 6px;
    border: var(--border-line);
    min-height: 274px;
}

.latestInfoSection .latestInfoDiv img {
    display: block;
    width: auto;
    margin: 0 auto;
    height: 168px;
    object-fit: cover;
}

.latestInfoSection .latestInfoDiv .viewAllDiv,
.article-view .viewAllDiv {
    min-height: 262px;
}

.latestInfoSection .latestInfoTxt {
    padding: 10px;
}

.viewAllDiv {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    text-align: center;
    min-height: 277px;
}

.viewAllDiv a {
    text-decoration: none;
    line-height: 24px;
    font-size: 14px;
    color: var(--color-red);
    font-weight: var(--font-semibold);
}

.leadFormDiv {
    padding: 20px;
    max-width: 95%;
    margin: 20px auto;
    margin-top: 65px;
}

.leadFormDiv p {
    font-size: 12px;
    line-height: 20px;
}

.leadFormDiv .headingText {
    font-size: 16px;
    line-height: 24px;
    font-weight: var(--font-semibold);
    padding-bottom: 5px;
}

.leadFormDiv input,
.leadFormDiv select,
.leadFormDiv .dialCodeDiv {
    padding: 7px;
    padding-left: 42px;
    min-height: 40px;
    font-size: 13px;
    line-height: 24px;
}

.leadFormDiv .form-group {
    padding-bottom: 10px;
}

.leadFormDiv .primaryBtn {
    width: 100%;
    display: block;
    padding: 6px;
}

.leadFormDiv .countryCode,
.leadFormDiv .dialCodeDiv {
    flex-basis: 90px;
}

.leadFormDiv .numberInput {
    flex-basis: calc(100% - 90px);
}

.checkbox-group {
    padding-bottom: 10px;
}

.checkbox-group input {
    padding: 0;
    min-height: auto;
    margin-right: 5px;
}

.checkbox-group label {
    font-size: 13px;
    line-height: 24px;
}

.userIcon,
.mailIcon,
.locationIcon,
.bookIcon,
.capIcon {
    top: 9px;
}

.numberInputs input {
    max-width: 19.6%;
    padding-left: 8px;
    margin-right: 12px;
}

.leadFormDiv .primaryBtn {
    margin-right: 0;
}

.optSection .primaryBtn {
    margin-bottom: 20px;
}

.optSection .row {
    -webkit-box-pack: center;
    justify-content: center;
}

.closeLeadForm {
    right: 20px;
    top: 20px;
    background: url("/yas/images/closeform.png");
    position: initial;
    width: 16px;
    height: 16px;
    cursor: pointer;
    outline: none;
}

.formHeadingDiv .formImg {
    flex-basis: 56px;
    margin-right: 10px;
    min-height: 56px;
}

.formHeadingDiv .formImg img {
    max-width: 32px;
    max-height: 32px;
    border-radius: 50%;
}

.formHeadingDiv p {
    padding: 0;
}

.formHeadingDiv .formHeading {
    flex-basis: calc(100% - 72px);
}

.formHeadingDiv .headingText {
    padding-right: 8px;
}


.headerLogo {
    -webkit-transform: scale(0.86);
    transform: scale(0.86);
}

.headerMegaMenu {
    height: 0;
}

.leadFormContainer {
    background: var(--color-white);
    z-index: 4;
}

.leadFormDiv {
    max-width: 100%;
    margin: 0 auto;
    margin-top: 50px;
}

.leadFormHeader {
    background: var(--topheader-bg);
    padding: 3px 20px;
    position: fixed;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 1;
}

.leadFormHeader .row {
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
    margin-right: 0;
}


.leadFormDiv .closeLeadForm {
    display: none;
}

/* news css starts here  */

body {
    background: #f3f2ef;
}



.bannerDiv,
.articelNote,
.articleInfo,
.faq_section,
.contentProvider,
.commentSection,
.relatedArticles,
.registerLatestArticle,
.newsSidebarSection {
    box-shadow: none;
    background: var(--color-white);
    border: var(--border-line);
    border-radius: 4px;
    margin-bottom: 20px;
}

.articelNote {
    border-left: 6px solid var(--color-red);
}

.articleCardInfo {
    background: #f0f8ff;
}

.tabelOfContent {
    background: #f0f8ff;
}

.newsSidebarSection h2 {
    font-size: 14px;
    line-height: 24px;
    margin: 0;
    background: #f5f5f5;
    text-transform: uppercase;
    padding: 12px 19px;
    border-bottom: var(--border-line);
}

.latestInfoTxt h3 {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: var(--font-semibold);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 72px;
    margin-bottom: 10px;
    padding: 0;
    text-decoration: none;
}

:root {
    --border-line: 1px solid #d8d8d8;
}

.recentnewsDivText {
    flex-basis: calc(100% - 96px - 16px);
}

.trendingArtilerDiv.row,
.recentArticlesDiv.row,
.recentnewsDiv.row {
    margin: 0;
    flex-wrap: nowrap;
    margin-bottom: 10px;
    border-bottom: var(--border-line);
    padding-bottom: 10px;
    -webkit-box-align: center;
    align-items: center;
    cursor: pointer;
}

.recentnewsList {
    overflow: initial;
    padding: 10px;
    max-height: 100%;
}

.categoryArticlesList {
    margin-bottom: 20px;
}

.articleCardInfo img {
    margin: 0 auto;
}

.viewAllDiv a {
    white-space: normal;
}

/* detail page css starts here  */
.breadcrumbDiv,
.topHeader {
    background: #0966c2;
}

.bannerDiv {
    margin: 20px 0;
    padding: 20px;
    border-radius: 4px;
    background-color: var(--color-white);
}



.bannerDiv .updated-info.row {

    margin: 0;
    margin-top: 10px;
    -webkit-box-align: center;
    line-height: 20px;
    font-size: 14px;
}

.bannerDiv .updatedBy {
    padding-right: 10px;
}

.bannerDiv .updated-info img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    vertical-align: middle;
}

.bannerDiv .updatedBy a {
    color: var(--primary-font-color);
    font-weight: var(--font-semibold);
    cursor: pointer;
}

.bannerDiv .updated-info a {
    text-decoration: none;
    padding-left: 10px;
}

.bannerDiv .updated-info p {
    font-size: 14px;
    line-height: 20px;
    color: var(--primary-font-color);
    display: inline-block;
}

.bannerDiv p,
.bannerDiv ul {
    padding-left: 50px;
}

.bannerDiv p {
    padding-bottom: 5px;
}

.bannerDiv .updated-info ul {
    margin: 0;
    padding: 0;
    margin-left: 10px;
}

.bannerDiv .updated-info ul p {
    padding-bottom: 0;
    padding-left: 40px;
}

.bannerDiv .updated-info ul li {
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
}

.bannerDiv .updated-info ul li a {
    vertical-align: middle;
}

.greyFbIcon {
    width: 26px;
    height: 26px;
    background-position: 475px -98px;
}

.greyTwitterIcon {
    width: 26px;
    height: 26px;
    background-position: 475px -132px;
}

.articelNote {
    border-radius: 4px;
    background-color: var(--color-white);
    padding: 10px;
}

.articelNote p {
    font-size: 15px;
    line-height: 26px;
}

.bannerImg {
    border-radius: 4px;
    margin-top: 20px;
    overflow: hidden;
    max-height: 200px;
}

.bannerImg img {
    max-height: 200px;
    width: 100%;
    display: block;
}

.articleInfo {
    border-radius: 4px;
    background-color: var(--color-white);
    margin: 20px 0;
    padding: 10px;
}

.articleInfo p {
    font-size: 15px;
    color: var(--primary-font-color);
    line-height: 26px;
    padding-bottom: 10px;
}

.tabelOfContent {
    border-radius: 6px;
    border: var(--border-line);
    margin: 20px 0;
}

.tabelOfContent ul {
    margin: 0;
    padding: 10px 30px;
}

.tabelOfContent ul li {
    color: var(--primary-font-color);
    line-height: 32px;
    list-style-type: none;
    padding: 0;
}

.updatedBy amp-img {
    vertical-align: middle;
}

.articleInfo h2 {
    line-height: 28px;
    font-weight: var(--font-bold);
    color: var(--primary-font-color);
    background: #f5f5f5;
    font-size: 15px;
    padding: 8px 10px;
    margin-bottom: 10px;
}

.articleInfo h3 {
    font-size: 17px;
    line-height: 24px;
    padding-bottom: 10px;
    color: var(--primary-font-color);
}

.tabelOfContent h2 {
    background: transparent;
    border-bottom: var(--border-line);
}


.articleInfo h4 {
    font-size: 16px;
    padding-bottom: 10px;
    line-height: 24px;
    color: var(--primary-font-color);
}

.articleCardInfo {
    text-align: center;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: block;
}

.articleCardInfo h2 {
    background: 0 0;
    margin-bottom: 10px;
    color: var(--primary-font-color);
    line-height: 32px;
    padding: 0;
    margin-bottom: 10px;
    font-size: 20px;
}

.tagsDiv {
    padding: 0;
}

.tagsDiv ul {
    padding: 0;
}

.tagsDiv ul li:first-child {
    display: block;
    margin: 0;
    padding-bottom: 10px;
}

.tagsDiv ul li {
    display: inline-block;
    padding-bottom: 0;
    font-size: 14px;
    font-weight: var(--font-bold);
    line-height: 20px;
    margin-right: 0;
}

.tagsDiv ul li a {
    padding: 8px 15px;
    background: var(--color-white);
    color: var(--anchor-textclr);
    text-decoration: none;
    border-radius: 2px;
    display: block;
    border: var(--border-line);
}

.faq_section {
    border-radius: 4px;
    background-color: var(--color-white);
    padding: 10px;
    margin-bottom: 20px;
}

.faq_section h2 {
    padding: 10px 20px;
    font-size: 18px;
    line-height: 24px;
    color: var(--primary-font-color);
    background: #f5f5f5;
    font-weight: var(--font-semibold);
    margin-bottom: 10px;
}

.faq_section .faqDiv {
    border-radius: 4px;
    border: var(--border-line);
}

.faq_section .faq_question:after {
    content: " ";
    background: url("/yas/images/master_sprite.webp");
    width: 12px;
    height: 21px;
    position: absolute;
    right: 10px;
    top: 12px;
    background-position: 591px -71px;
    -webkit-transition: .2s ease;
    transition: .2s ease;
}

.faq_section .faq_question {
    position: relative;
    cursor: pointer;
    -webkit-transition: .4s ease;
    transition: .4s ease;
    font-size: 15px;
    padding: 10px;
    padding-left: 5px;
    padding-right: 25px;
    border: none;
    line-height: 26px;
    border-bottom: var(--border-line);
    background: var(--color-white);
    font-weight: normal;
    outline: none;
}

.faq_section .faq_answer {
    border-left: 5px solid var(--color-red);
    font-size: 15px;
    background: #fafbfc;
    line-height: 26px;
    color: #787878;
    border-bottom: var(--border-line);

}

.trendingArtilerDiv.row,
.recentnewsDiv.row {
    margin: 0;
    flex-wrap: nowrap;
    margin-bottom: 10px;
    border-bottom: var(--border-line);
    padding-bottom: 10px;
    -webkit-box-align: center;
    align-items: center;
    cursor: pointer;
}

.sidebarImgDiv {
    flex-basis: 96px;
    margin-right: 16px;
    display: grid;
    min-height: 72px;
}

.trendingArtilerDiv img,
.recentnewsDiv img {
    width: 96px;
    max-height: 72px;
    display: block;
    align-self: center;
}

.trendingArtileText {
    flex-basis: calc(100% - 96px - 16px);
}

.sidebarTextLink {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: 500;
    text-decoration: none;
    display: -webkit-box;
    padding: 0;
    -webkit-box-orient: vertical;
    overflow: hidden;
    border: none;
    -webkit-line-clamp: initial;
}

.listCard:last-child .trendingArtilerDiv.row,
.listCard:last-child .recentnewsDiv.row {
    margin-bottom: 0;
    border: none;
    padding-bottom: 0;
}

.newsSidebarSection amp-selector[role=tablist].tabs-with-flex [role=tab][selected] {
    color: var(--color-red);
    border-bottom: 3px solid var(--color-red);
    background: var(--color-white);
    font-size: 14px;
    line-height: 24px;
    padding: 12px 5px;
    padding-bottom: 9px;
    font-weight: var(--font-semibold);
    outline: none;
}

.newsSidebarSection amp-selector[role=tablist].tabs-with-flex [role=tab] {
    background: var(--color-white);
    list-style-type: none;
    text-align: center;
    font-size: 14px;
    line-height: 24px;
    color: #787878;
    cursor: pointer;
    padding: 12px 5px;
    padding-bottom: 9px;
    border-bottom: var(--border-line);
    font-weight: var(--font-semibold);
}

.contentProvider {
    padding: 10px;
    border-radius: 4px;
    background-color: var(--color-white);
    display: -webkit-box;
    display: flex;
    margin-bottom: 20px;
}

.contentProvider .profilePic {
    margin-right: 10px;
    flex-basis: 48px;
}

.contentProvider .profilePic img {
    max-width: 48px;
    border-radius: 50%;
}

.contentProvider .profileInfo .name {
    font-size: 15px;
    line-height: 26px;
    font-weight: var(--font-bold);
}

.contentProvider .profileInfo .position {
    font-size: 14px;
    line-height: 24px;
    color: #aaa;
    padding-bottom: 10px;
}

.contentProvider .profileInfo p {
    font-size: 15px;
    line-height: 26px;
}

.profileInfo {
    flex-basis: calc(100% - 48px - 10px);
}

.commentSection {
    border-radius: 4px;
    background-color: var(--color-white);
    margin-bottom: 20px;
    border-bottom: var(--border-line);
    padding: 10px;
}


.commentSection h2 {
    color: var(--primary-font-color);
    line-height: 24px;
    background: #f5f5f5;
    font-size: 15px;
    padding: 10px;
    font-weight: var(--font-semibold);
    margin-bottom: 10px;
}

.commentForm {
    padding-bottom: 20px;
    border-bottom: var(--border-line);
}

.commentForm .form-group {
    margin-bottom: 0;
}

.commentForm .row {
    margin: 0;
}

.commentForm .col-md-6 {
    padding: 0;
    padding-bottom: 10px;
    flex-basis: 100%;
    margin-right: 0;
}

.commentForm input,
.commentForm textarea {
    padding: 11px 20px;
    border-radius: 4px;
    border: var(--border-line);
    line-height: 24px;
    width: 100%;
}

.commentForm .commentBtn {
    display: block;
    margin-top: 10px;
}

.commentSection h3 {
    font-size: 16px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: var(--font-bold);
    padding: 10px 0;
}

.commetByStudent {
    margin: 30px 0;
}

.commetByStudent .row {
    margin: 0;
    margin-bottom: 20px;
}

.commetByStudent .row:last-child {
    margin-bottom: 0;
}

.commetByStudent .col-md-1 {
    padding: 0;
    flex-basis: 48px;
}

.commetByStudent img {
    max-width: 48px;
    border-radius: 50%;
}

.commetByStudent .col-10 {
    padding-right: 0;
    flex-basis: calc(100% - 48px);
    max-width: calc(100% - 48px);
}

.commetByStudent p {
    font-size: 15px;
    line-height: 26px;
    padding-bottom: 10px;
}

.commetByStudent .reviewer-name {
    font-size: 13px;
    line-height: 20px;
    color: var(--primary-font-color);
    font-weight: var(--font-bold);
    padding: 0;
}

.commetByStudent .updatedTime {
    color: #aaa;
    line-height: 20px;
    padding: 0;
    font-size: 15px;
    padding-bottom: 10px;
}

.commetByStudent .replyTxt {
    font-size: 14px;
    line-height: 24px;
    color: var(--color-red);
    font-weight: var(--font-bold);
    text-decoration: none;
}

.replyIcon {
    width: 22px;
    height: 18px;
    background-position: 533px -105px;
    vertical-align: middle;
}

.commetByStudent .commentByOrganization {
    padding: 20px 0;
    border-bottom: var(--border-line);
    border-top: var(--border-line);
    margin-top: 20px;
}

.advertise {
    font-size: 11px;
    text-align: center;
    line-height: 10px;
    width: 100%;
    color: #b7b7b7;
    margin-bottom: 5px;
}

.articleDisplay .viewAllDiv {
    min-height: 275px;
    margin-right: 0;
}

.latestArticleSection .articleDisplay .mobileOnly {
    margin-right: 0;
}

.authorName {
    font-weight: var(--font-semibold);
    display: inline-block;
}

.leadFormDiv .col-md-6 {
    padding: 0 10px;
    width: 100%;
    flex-basis: 100%;
}

.pagination .active span,
ul.pagination .active span,
ul.pagination span {
    min-width: 36px;
    height: 36px;
    display: inline-block;
    padding: 6px 8px;
    font-size: 14px;
    line-height: 24px;
    color: #fff;
    border-radius: 3px;
    overflow: hidden;
}

ul.pagination span {
    color: #989898;
    border: var(--border-line);
}

.breadcrumbDiv ul {
    display: block;
}

.breadcrumbDiv ul li {
    display: inline;
    word-break: break-all;
}

.w-100 {
    width: 100%;
}

/*
 //Live Update CSS Date-07-07-2022
*/
.liveUpdateSection .whiteDot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--color-white);
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
}

.liveUpdateSection .liveUpdateHeading {
    background: var(--color-red);
    color: var(--color-white);
    padding: 8px 20px;
    font-size: 18px;
    line-height: 28px;
    font-weight: 500;
    margin-bottom: 20px;
}

.latestNewsList {
    position: relative;
    padding-left: 22px;
}

.latestNewsList:before {
    content: "";
    position: absolute;
    width: 1px;
    height: calc(100% - 15px);
    transform: translate(0px, -10px);
    left: 8px;
    bottom: 0;
    border-left: 1.2px dashed var(--color-red);
}

.lastestNewsDiv {
    padding-bottom: 10px;
    border-bottom: var(--border-line);
    margin-bottom: 20px;
    position: relative;
}

.lastestNewsDiv:last-child {
    margin-bottom: 0px;
    padding-bottom: 0px;
}

.lastestNewsDiv .newsUpdatedTime {
    position: relative;
}

.lastestNewsDiv .newsUpdatedTime:before {
    content: "";
    position: absolute;
    width: 18px;
    height: 18px;
    top: 3px;
    left: -22px;
    z-index: 1;
    background-image: url("/yas/images/master_sprite.webp");
    background-position: 593px -400px;
    background-color: var(--color-white);
}

.sidebarTextLink amp-anim {
    margin-right: 5px;
}

.shareSection {
    padding: 10px;
    margin-bottom: 20px;
    border: var(--border-line);
    background: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    border-radius: 4px;
}

/* Share Section*/
.shareSection div {
    display: flex;
    align-items: center;
    gap: 10px;
}

.shareSection div p {
    font-stretch: normal;
    font-style: normal;
    line-height: 1.33;
    letter-spacing: 0.3px;
    text-align: left;
    color: #282828;
    margin: 0px;
    font-size: 15px;
    font-weight: 600;
}

.shareSection div p span {
    color: #3d8ff2;
}

.followUs {
    border-radius: 4px;
    border: solid 1px #d8d8d8;
    background-color: #fff;
    flex-basis: 100%;
    justify-content: center;
}

.followUs p {
    margin: 0px;
}

.shareSection {
    gap: 10px;
}

/*  Banner Design for task 5438 */
.bannerDiv h1 {
    font-size: 18px;
    color: #282828;
    text-align: left;
    line-height: 1.56;
    letter-spacing: 0.3px;
    font-weight: bold;

}

.bannerDiv h2 {
    font-size: 15px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.73;
    text-align: left;
    color: #282828;
    margin: 10px 0 10px;
}

.shareIcon {
    width: 36px;
    height: 36px;
    transform: scale(0.85);
}

.whatsappIcon {
    background: url(/yas/images/WhatsApp.svg) no-repeat;
}

.gmailIcon {
    background: url(/yas/images/Gmail.svg) no-repeat;
    cursor: pointer;
}

.copyClipboardIcon {
    background: url(/yas/images/Copy-clipboard.svg) no-repeat;
    cursor: pointer;
}

.googleNewsIcon {
    background: url(/yas/images/Google-news.svg) no-repeat;
}

.bigTwitterIcon {
    background: url(/yas/images/Twitter.svg) no-repeat;
}

#custom-tooltip {
    padding: 5px 12px;
    background-color: #000000df;
    border-radius: 4px;
    color: #fff;
}

.bannerDivMargin {
    margin-top: 10px;
}

.newsDetailsBreadCrumb {
    background-color: initial;
}

.newsDetailsBreadCrumb div ul li {
    color: #282828;
}

.newsDetailsBreadCrumb div ul li a {
    color: #282828;
}

.newsDetailsBreadCrumb div ul li a::after {
    background: url(/yas/images/master_sprite.webp);
    background-position: -36px -150px;
}

.topSearcheSection {
    background-color: var(--color-white);
    padding: 8px 0;
}

.topSearcheSection .topsearchtext {
    margin-right: 20px;
    font-size: 14px;
    font-weight: bold;
    line-height: 20px;
    color: #0966c2;
    margin-right: 10px;
}

.topSearcheSection .container .row {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    overflow: hidden;
    margin: 0px;
}

.topSearcheSection .container .row p {
    padding-bottom: 0px;
}

.topSearchDiv {
    flex-basis: calc(100% - 135px);
    position: relative;
}

.topSearchDiv .scrollLeft,
.topSearchDiv .scrollRight {
    display: none;
    background-color: var(--color-white);
    background-position: 653px -289px;
    bottom: 0px;
    width: 15px;
    left: 0px;
    border-radius: 0px;
    height: 20px;
    top: initial;
    cursor: pointer;
    transform: none;
    z-index: 1;
}

.topSearchDiv .scrollRight {
    background-position: 653px -311px;
    left: auto;
    right: 0px;
}

.topSearchDiv ul {
    margin: 0;
    padding: 0;
    display: block;
    white-space: nowrap;
    overflow: auto;
}

.topSearchDiv ul::-webkit-scrollbar {
    display: none;
}

.topSearchDiv ul li {
    display: inline-block;
    white-space: initial;
    padding: 0 5px;
}

.topSearchDiv ul li:first-child {
    padding-left: 0px;
}

.topSearchDiv ul li a {
    font-size: 14px;
    line-height: 20px;
    display: block;
    color: var(--primary-font-color);
    font-weight: normal;
}

.topSearchDiv ul li a:hover {
    color: var(--anchor-textclr);
}

.verifiedBlueTickIcon {
    background: url(/yas/images/master_sprite.webp?b5965618277cb72aa2882736b150fec5);
    width: 16px;
    height: 16px;
    background-position: -673px -557px;
    vertical-align: text-top;
    transform: scale(0.65);
}

.bannerDiv .updated-info.row {
    align-items: flex-start;
    flex-wrap: nowrap;
}

.bannerDiv .updated-info.row .updatedBy img {
    float: left;
}

.bannerDiv .updated-info.row .authorAndDate {
    display: flex;
    flex-wrap: wrap;
}

.bannerDiv .updated-info.row .authorAndDate p {
    flex-basis: 100%;
    font-size: 12px;
    padding-left: 0px;
}

.bannerDivMargin h2 {
    font-size: 15px;
    font-weight: normal;
    font-stretch: normal;
    font-style: italic;
    line-height: 1.73;
    letter-spacing: 0.3px;
    text-align: left;
    color: #787878;
    margin: 20px 0px;
    padding-left: 10px;
    border-left: 2px solid #787878;
}

.bannerDiv .updated-info a {
    padding-left: 0px;
    text-decoration: none;
    color: #282828;
    font-size: 12px;
    font-weight: bold;
}

.shareSection a {
    display: flex;
    align-items: center;
    gap: 10px;
}

.shareSection a p {
    margin: 6px 10px 6px 0;
    font-size: 15px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.33;
    letter-spacing: 0.3px;
    text-align: left;
    color: #282828;
}

.shareSection a p span {
    color: #3d8ff2;
}

.latestInfoSection .latestInfoTxt p {
    color: black;
}

.latestInfoSection .latestInfoDiv .articleAuthorName {
    padding: 0px 0px 0px 10px;
    color: #989898;
    margin: 0;
    font-weight: normal;
    font-size: 14px;
}

.latestInfoSection .latestInfoTxt .latestInfoTxt-title {
    -webkit-line-clamp: 2;
}

.topSearcheSection .topSearchDiv ul li {
    margin-left: 0px
}

.pageFooter {
    padding-bottom: 0px;
}

.articleInfo table {
    display: block;
    overflow: auto;
}