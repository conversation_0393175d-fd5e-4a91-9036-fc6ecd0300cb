.pageHeading {
    margin-top: -160px;
    border-radius: 4px;
    background-color: var(--color-white);
    padding: 20px;
    margin-bottom: 10px
}

.pageHeading h1 {
    font-size: 18px;
    line-height: 28px;
    font-weight: 400;
}

.categoryArticlesContainer {
    margin-top: 0;
}

.categoryArticlesList {
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 0 4px 0 rgb(0 0 0 / 12%);
    background-color: var(--color-white);
    margin-bottom: 20px;
}

.catgegoryArticle {
    border-radius: 4px;
    box-shadow: none;
    background-color: var(--color-white);
    margin-bottom: 20px;
    background: var(--color-white);
    border: var(--border-line);
    /* overflow: hidden; */
}

.catgegoryArticle:last-child {
    margin-bottom: 0;
}

.catgegoryArticle .row {
    margin: 0;
    display: block;
}

.catgegoryArticle .articleBanner,
.latestArticleSection .articleDisplay .row figure {
    border-bottom: var(--border-line);
    border-right: none;
}

.catgegoryArticle .articleBanner img {
    max-width: 100%;
    width: 100%;
    cursor: pointer;
    max-height: 232px;
    display: block;

}

.catgegoryArticle .articleText {
    flex-basis: calc(100% - 310px);
    padding: 10px;
}

.catgegoryArticle .articleText h2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 15px;
    line-height: 25px;
    -webkit-line-clamp: 3;
}

.catgegoryArticle .articleText h2 a {
    text-decoration: none;
    color: var(--primary-font-color);
}

.catgegoryArticle .articleText p {
    color: #787878;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-size: 15px;
    line-height: 26px;
    -webkit-line-clamp: 5;
    margin-bottom: 10px;
}

.catgegoryArticle .updated-info {
    font-size: 14px;
    line-height: 20px;
}

.catgegoryArticle .updated-info.row {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
}

.updated-info .updatedBy {
    padding-right: 5px;
    display: flex;
    align-items: center;
}

.updated-info .updatedBy img {
    width: 36px;
    height: 36px;
    display: inline-block;
    vertical-align: middle;
    border-radius: 50%;
}

.updated-info .updatedBy .authorName {
    font-weight: var(--font-semibold);
    display: inline-block;
    color: var(--primary-font-color);
    vertical-align: middle;
    margin-left: 5px;
}

.catgegoryArticle .updated-info p {
    margin-bottom: 0;
}

.sidebarImgDiv {
    flex-basis: 96px;
    margin-right: 16px;
    display: grid;
    min-height: 72px;
}

.listCard:last-child .recentnewsDiv.row {
    margin-bottom: 0;
    border: none;
    padding-bottom: 0;
}

.sidebarTextLink {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: 500;
    text-decoration: none;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 3;
    padding: 0;
    font-weight: 400;
}

/* category css ends here  */

.newsSidebarSection amp-selector[role=tablist].tabs-with-flex [role=tab][selected] {
    color: var(--color-red);
    border-bottom: 3px solid var(--color-red);
    background: var(--color-white);
    font-size: 14px;
    line-height: 24px;
    padding: 12px 5px;
    padding-bottom: 9px;
    font-weight: var(--font-semibold);
    outline: none;
}

.newsSidebarSection amp-selector[role=tablist].tabs-with-flex [role=tab] {
    background: var(--color-white);
    list-style-type: none;
    text-align: center;
    font-size: 14px;
    line-height: 24px;
    color: #787878;
    cursor: pointer;
    padding: 12px 5px;
    padding-bottom: 9px;
    border-bottom: var(--border-line);
    font-weight: var(--font-semibold);
}

.pagination,
ul.pagination {
    text-align: center;
    padding: 0;
    margin: 0;
    margin-bottom: 10px;
    width: 100%;
    display: block;
    margin-bottom: 20px;
}

.pagination ul {
    margin: 0;
    padding: 0;
}

.pagination li,
ul.pagination li {
    text-align: center;
    display: inline-block;
    margin: 0 2px;
    height: 35px;
    vertical-align: middle;
}

.pagination li a,
ul.pagination li a {
    min-width: 36px;
    height: 36px;
    display: block;
    padding: 6px 8px;
    font-size: 14px;
    line-height: 24px;
    color: #989898;
    text-decoration: none;
    border-radius: 3px;
    border: var(--border-line);
    overflow: hidden;
    -webkit-transition: .2s ease;
    transition: .2s ease;
    background: var(--color-white);
}

.pagination li.active,
ul.pagination li.active {
    background: var(--color-red);
    border-radius: 3px;
}

.pagination li.active a,
.ul.pagination li.active a {
    background: var(--color-red);
    color: var(--color-white);
    border: .2px solid var(--color-red);
    font-weight: var(--font-semibold);
}