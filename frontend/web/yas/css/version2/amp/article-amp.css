html {
  box-sizing: border-box;
}

:root {
  --font-bold: 700;
  --font-semibold: 600;
  --font-500: 500;
  --fontsize-14: 14px;
  --fontsize-15: 15px;
  --primary-font-color: #333333;
  --color-red: #ff4e53;
  --color-white: #ffffff;
  --anchor-textclr: #3d8ff2;
  --transition: 0.2s ease;
  --border-line: 1px solid #d8d8d8;
  --footer-bg: #273553;
  --topheader-bg: #0966c2;
  --border-line: 1px solid #d8d8d8;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: "Roboto", sans-serif;
  background: #f3f2ef;
}

*,
::after,
::before {
  box-sizing: inherit
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto
}

.row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px
}

/*Article Page Description  Author block*/
.pageDescription {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .12);
  background-color: #ffffff;
  padding: 20px;
  margin: 10px 0;
  color: var(--primary-font-color);
  padding: 20px;
}

.pageDescription,
.heroSection {
  background: 0 0;
}

.row {
  display: -webkit-box;
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.col-12,
.col-md-12,
.col-md-4,
.col-md-8 {
  position: relative;
  width: 100%;
  padding: 0 10px;
}

.pageDescription h1 {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.56;
  color: #282828;
  padding-bottom: 10px;
  display: block;
  margin: 0 0 5px 0;
}

.authorInfoAndTranslateBtn {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.updated-info.row {
  margin: 0;
  align-items: flex-start;
  line-height: 20px;
  font-size: 14px;
  flex-wrap: nowrap;
}

.updated-info.row img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 10px;
  vertical-align: middle;
}

.updated-info.row .updatedDetails {
  display: flex;
  align-items: center;
  flex-grow: 2;
  flex-wrap: wrap;
}

.updatedBy {
  padding-right: 5px;
}

.updated-info.row .updatedBy p {
  display: inline-block;
  font-weight: var(--font-semibold);
}

.updated-info.row a {
  text-decoration: none;
  color: var(--anchor-textclr);
}

.updated-info.row ul {
  padding: 0;
  right: -43px;
  z-index: 0;
  position: unset;
  margin: 0;
}

.updated-info.row ul p {
  display: inline-block;
}

.updated-info.row ul li {
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
  position: relative;
  list-style-type: none;
  font-size: 15px;
  line-height: 28px;
}

.spriteIcon {
  display: inline-block;
  background-image: url("/yas/images/master_sprite.webp");
  text-align: left;
  overflow: hidden;
}

.greyFbIcon {
  width: 26px;
  height: 26px;
  background-position: 475px -98px;
}

.greyTwitterIcon {
  width: 26px;
  height: 26px;
  background-position: 475px -132px;
}

/*************** Article Prduct Link ************/
.examRelataedLinks {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .12);
  background-color: var(--color-white);
  /*padding: 20px;*/
  padding: 0;
  margin-bottom: 20px;
  position: relative;
}

.container .stickyNavCls,
.quickLinks,
.sidebarAds {
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 0;
  width: 100%;
}

.examRelataedLinks ul {
  margin: 0;
  padding: 0 30px;
  white-space: nowrap;
  overflow: auto;
}

.examRelataedLinks ul li {
  display: inline-block;
  padding: 0 10px;
  font-size: 15px;
  line-height: 28px;
}

.examRelataedLinks ul li a {
  display: block;
  padding: 12px 0;
  padding-bottom: 9px;
  border-bottom: 3px solid var(--color-white);
  color: #787878;
  line-height: 24px;
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
}

/*************** Article Description Link ************/
.articelNote {
  font-size: 15px;
  font-weight: 400;
  font-stretch: normal;
  font-style: italic;
  line-height: 1.73;
  letter-spacing: .3px;
  text-align: left;
  color: #282828;
  border-left: solid #ff4e53 6px;
  box-shadow: none;
  /* border: var(--border-line); */
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
}

.articelNote p {
  font-size: 15px;
  line-height: 26px;
}

.bannerImg {
  border-radius: 4px;
  margin-top: 20px;
  overflow: hidden;
  max-height: 400px;
}

/** Article TOC */
.table-content-ctn {
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  margin-bottom: 40px;
}

.table-content-heading-article {
  background-color: #f5f5f5;
  padding: 10px 16px;
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
  color: #282828;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.downArrowIcon,
.upArrowIcon {
  background-position: -151px -125px;
  width: 18px;
  height: 11px;
}

.table-content-article {
  padding: 0px 0px 10px 0px;
  margin: 0 0 10px 0;
  max-height: 200px;
  overflow-y: auto;
}

.table-content-article li {
  list-style-type: none;
  position: relative;
  padding: 0 30px;
  margin-top: 10px;
}

.rotate {
  transform: rotate(180deg);
  top: 0px;
}

a {
  color: #3d8ff2;
  text-decoration: none;
  cursor: pointer;
}

.table-content-ctn ::-webkit-scrollbar {
  width: 8px;
}

.table-content-ctn ::-webkit-scrollbar-thumb {
  background: #d8d8d8;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 5px #d8d8d8;
}

.table-content-article {
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s ease-out, padding 0.4s ease-out;
}

.table-content-article.open {
  max-height: 200px;
  padding: 10px 0;
  overflow: auto;
}

ul.table-content-article.open li:before {
  left: 5px;
}

.articleInfo ul li::before {
  content: "";
  background: url('/yas/images/master_sprite.webp');
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

/****Article Page Content******/
.dataContainer,
.articleInfo {
  border-radius: 4px;
  padding: 10px;
  margin: 20px 0;
  box-shadow: none;
  border: var(--border-line);
  background: var(--color-white);
}

.dataContainer p,
.articleInfo p,
.articleInfo div {
  font-size: 15px;
  color: var(--primary-font-color);
  line-height: 26px;
  padding-bottom: 10px;
}

.dataContainer h2,
.articleInfo h2 {
  line-height: 28px;
  font-weight: var(--font-bold);
  color: var(--primary-font-color);
  background: #f5f5f5;
  font-size: 15px;
  padding: 8px 10px;
  margin-bottom: 10px;
}
.scrollToh2CSS {
  scroll-margin-top: 53px;
}
.articleInfo ul li {
  position: relative;
  list-style-type: none;
  font-size: 15px;
  line-height: 26px;
  color: var(--primary-font-color);
}
.articleInfo ul li::before {
  content: "";
  background: url('/yas/images/master_sprite.webp');
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

.articleInfo p, .articleInfo div {
  font-size: 15px;
  color: var(--primary-font-color);
  line-height: 26px;
  padding-bottom: 10px;
}

.table-responsive {
  overflow-x: auto;
  border-radius: 4px;
  margin-bottom: 10px;
}
.articleInfo h3 {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}
table {
  border-spacing: 0;
  width: 100%;
  color: #333;
  border: .2px solid #eaeaea;
  border-bottom: 0;
  border-collapse: collapse;
}
.articleInfo caption {
  padding-top: 5px;
  text-align: center;
  font-size: 14px;
  margin-bottom: 0;
  caption-side: bottom;
  color: var(--primary-font-color);
  margin-top: 20px;
  line-height: 24px;
}

.articleInfo table thead tr {
  background: #f1f3f4;
  padding: 10px;
  font-size: 15px;
  line-height: 26px;
  text-align: left;
  border-right: .2px solid #eaeaea;
  border-bottom: .2px solid #eaeaea;
  text-align: center;
  color: var(--primary-font-color);
  font-weight: 700;
  border: .2px solid #eaeaea;
}

.articleInfo table thead tr th {
  background: 0 0;
  text-align: center;
  padding: 10px;
  font-size: 15px;
  line-height: 26px;
  border-right: .2px solid #eaeaea;
  border-bottom: .2px solid #eaeaea;
}
.articleInfo table tbody tr td {
  text-align: center;
}

table td:first-child {
  border-right: .2px solid #eaeaea;
}
table td {
  font-size: 14px;
  line-height: 24px;
  padding: 11px;
  border-bottom: .2px solid #eaeaea;
  min-width: 130px;
}

/**Article FAQ */
.faq_section {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .12);
  background-color: var(--color-white);
  padding: 10px;
  margin-bottom: 20px;
}
.faq_section h2 {
  line-height: 24px;
  color: var(--primary-font-color);
  background: #f5f5f5;
  padding: 8px 10px;
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: var(--font-semibold);
}
.faq_section .faqDiv {
  border-radius: 4px;
  border: var(--border-line);
}
.faq_section .faq_question {
  position: relative;
  cursor: pointer;
  -webkit-transition: 0.4s ease;
  transition: 0.4s ease;
  padding-right: 25px;
}

.faq_section p {
  padding: 10px;
  font-size: 15px;
  line-height: 26px;
  padding-left: 5px;
  border-bottom: var(--border-line);
}


.faq_section .faq_answer {
  border-left: 5px solid var(--color-red);
  font-size: 15px;
  background: #fafbfc;
  color: #787878;
  padding: 10px;
}
.faq_section .faq_question:after {
  content: " ";
  background: url('/yas/images/master_sprite.webp');
  width: 12px;
  height: 21px;
  position: absolute;
  right: 10px;
  top: 12px;
  background-position: 591px -71px;
  -webkit-transition: 0.2s ease;
  transition: 0.2s ease;
}

/*************** Recent Article ***************/

.registerLatestArticle, .articleSidebarSection, .newsSidebarSection, .articleInfo, .faq_section, .contentProvider, .commentSection, .relatedArticles {
  box-shadow: none;
  border: var(--border-line);
  background: var(--color-white);
  border-radius: 4px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, .12);
  margin-bottom: 20px;
}

.articleSidebarSection ul, .newsSidebarSection ul {
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: flex;
}

.articleSidebarSection ul li, .newsSidebarSection ul li {
  list-style-type: none;
  flex-basis: 50%;
  text-align: center;
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  cursor: pointer;
  padding: 12px 5px;
  padding-bottom: 9px;
  border-bottom: var(--border-line);
  font-weight: var(--font-semibold);
}

.articleSidebarSection ul li.activeLink, .newsSidebarSection ul li.activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
}

.articleSidebarSection .tab-content.activeLink, .newsSidebarSection .tab-content.activeLink {
  display: block;
}

.trendingArtilerList, .recentArticlesList, .recentnewsList {
  max-height: 490px;
  overflow: auto;
  padding: 20px;
}

.trendingArtilerDiv.row, .recentArticlesDiv.row, .recentnewsDiv.row {
  margin: 0;
  flex-wrap: nowrap;
  margin-bottom: 10px;
  border-bottom: var(--border-line);
  padding-bottom: 10px;
  -webkit-box-align: center;
  align-items: center;
  cursor: pointer;
}

.sidebarImgDiv {
  flex-basis: 96px;
  margin-right: 16px;
  display: grid;
  min-height: 72px;
}

.sidebarImgDiv img {
  width: 96px;
  max-height: 72px;
  display: block;
  align-self: center;
}

.recentnewsDivText, .recentArticlesDivText {
  flex-basis: calc(100% - 96px - 16px);
}

.recentArticlesDiv .recentArticlesDivText .sidebarTextLink{
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: 500;
  text-decoration: none;
  font-weight: 400;
  padding: 0;
  border: none;
}

.squareDiv {
  display: block;
  margin-bottom: 20px;
}

.quickLinks {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .12);
  background-color: var(--color-white);
  padding: 20px;
  padding: 0;
}

.quickLinks h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  text-transform: uppercase;
}

.quickLinks ul {
  padding: 10px 20px;
  margin: 0;
  max-height: 440px;
  overflow: auto;
}

.quickLinks ul li {
  list-style-type: none;
}

.quickLinks ul li a {
  display: block;
  font-size: 14px;
  line-height: 24px;
  border-bottom: var(--border-line);
  text-decoration: none;
  color: var(--primary-font-color);
  padding: 8px 0;
}

.article-aside {
  height: 100%;
  padding-bottom: 20px;
}

.sideBarSection .listCard {
  display: flex;
  padding: 10px 20px;
  border-bottom: var(--border-line);
}

.sideBarSection .row {
  margin: 0;
  align-items: center;
}

.sideBarSection .sidebarImgDiv img {
  display: block;
  margin: 0 auto;
  width: 100%;
}

.sideBarSection .sidebarHeading {
  background: #d8d8d8;
  padding: 10px 20px;
  font-size: 16px;
  line-height: 24px;
  margin: 20px;
  margin-bottom: 6px;
  font-weight: 500;
}

.sideBarSection {
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 4px;
  margin-bottom: 20px;
}

.sideBarSection .sidebarTextLink p {
  font-size: 14px;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}


.sideBarSection .sidebarTextLink .cardText{
  font-weight: 600;
}

.sideBarSection .sidebarTextLink .subText {
  color: #989898;
}

.sideBarSection .applyText {
  color: var(--anchor-textclr);
}


/**************** College *************************/

.clgWithCourse {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0,0,0,.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px
}

.clgWithCourse h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  text-transform: uppercase;
  margin-bottom: 20px
}

.clgWithCourse h2.row {
  justify-content: space-between;
  margin: 0;
  margin-bottom: 20px
}

.clgWithCourse h2 a {
  font-size: 14px;
  color: var(--color-red);
  text-decoration: none
}

.clgWithCourseList {
  margin: 0
}

.clgWithCourseDiv {
  border-radius: 4px;
  border: var(--border-line);
  overflow: hidden;
  padding: 20px;
  text-align: center
}

.clgWithCourseDiv img {
  max-width: 80px;
  min-height: 80px;
  margin: 0 auto;
  display: block
}

.clgWithCourseDiv figure {
  text-align: center;
  margin-bottom: 20px
}

.clgWithCourseDiv p,.clgWithCourseDiv a {
  font-size: 14px;
  line-height: 24px;
  padding-bottom: 10px;
  text-decoration: none
}

.clgWithCourseDiv p.clgName,.clgWithCourseDiv a.clgName {
  font-weight: var(--font-semibold);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 50px;
  padding: 0;
  margin-bottom: 14px
}

.clgWithCourseDiv .avgFee {
  font-weight: var(--font-semibold)
}

.clgWithCourseDiv .avgFee span {
  color: #989898;
  display: block;
  font-weight: 400
}

.clgWithCourseDiv button {
  border-radius: 3px;
  border: solid 1px var(--anchor-textclr);
  display: block;
  padding: 5px;
  text-align: center;
  background: var(--color-white);
  color: var(--anchor-textclr);
  font-size: 14px;
  line-height: 24px;
  font-weight: var(--font-500);
  width: 100%;
  margin-bottom: 10px
}

.clgWithCourseDiv button:last-child {
  margin-bottom: 0
}

.latestInfoListContainer {
  position: relative
}

.latestInfoListContainer .latestInfoList.row {
  flex-wrap: nowrap
}

.latestInfoListContainer .latestInfoList.row .latestInfoDiv {
  min-width: 23.7%
}

.viewAllIcon {
  width: 77px;
  height: 76px;
  background-position: 358px -73px;
  margin-bottom: 10px;
  display: block;
  margin-bottom: 10px;
}

.clgWithCourseDiv p,.clgWithCourseDiv a {
  font-size: 14px;
  line-height: 24px;
  padding-bottom: 10px;
  text-decoration: none
}

.clgWithCourseDiv p.clgName,.clgWithCourseDiv a.clgName {
  font-weight: var(--font-semibold);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 50px;
  padding: 0;
  margin-bottom: 14px
}

.clgWithCourseDiv .avgFee {
  font-weight: var(--font-semibold)
}

.clgWithCourseDiv .avgFee span {
  color: #989898;
  display: block;
  font-weight: 400
}

a.ctaBtn, button.ctaBtn, .ctaBtn {
  border-radius: 3px;
  border: solid 1px var(--anchor-textclr);
  display: block;
  padding: 5px;
  text-align: center;
  background: var(--color-white);
  color: var(--anchor-textclr);
  font-size: 14px;
  line-height: 24px;
  font-weight: var(--font-500);
  width: 100%;
  margin-bottom: 10px;
  text-decoration: none;
  text-transform: uppercase;
  cursor: pointer;
}

.applicationDiv a.clgName, a.clgName {
  color: #333;
}

/****************** Exam Info ********************/

.examInfoSlider {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0,0,0,.12);
  background-color: var(--color-white);
  padding: 20px;
  padding-bottom: 0;
  margin-bottom: 20px;
  position: relative
}

.examInfoSlider .scrollLeft {
  top: 58%
}

.examInfoSlider .scrollRight {
  top: 52%
}

.examInfoSlider .row {
  margin: 0 -10px;
  display: block;
  overflow: auto;
  white-space: nowrap
}

.examInfoSlider .row::-webkit-scrollbar {
  height: 5px;
  display: none
}

.examInfoSlider .row .col-md-6 {
  padding: 0 10px
}

.examInfoSlider .row .col-md-4 {
  max-width: 33%;
  padding: 0 10px;
  display: inline-block;
  width: 100%;
  white-space: normal;
  vertical-align: middle;
  overflow: auto
}

.examInfoSlider h2 {
  padding: 10px 20px;
  font-size: 18px;
  line-height: 24px;
  background: #f5f5f5;
  margin: 0;
  margin-bottom: 20px;
  text-transform: uppercase
}

.examInfoSlider h2.row {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  margin: 0;
  margin-bottom: 20px
}

.examInfoSlider h2 a {
  font-size: 14px;
  color: var(--color-red);
  text-decoration: none
}

.examInfoSlider .examCategoryInfo {
  border-radius: 4px;
  border: var(--border-line);
  margin-bottom: 20px
}

.examInfoSlider .examCategoryInfo img {
  max-width: 64px;
  border-radius: 50%;
  margin-right: 25px;
  height: 64px
}

.examInfoSlider .examCategoryInfo .examInfoDates {
  flex-basis: calc(100% - 90px)
}

.examInfoSlider .examCategoryInfo .row {
  padding: 20px;
  display: -webkit-box;
  display: flex;
  border-bottom: var(--border-line);
  margin: 0;
  min-height: 119px
}

.examInfoSlider .examCategoryInfo p {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-semibold)
}

.examInfoSlider .examCategoryInfo p:first-child {
  font-size: 16px;
  line-height: 24px;
  padding-bottom: 4px
}

.examInfoSlider .examCategoryInfo p span {
  color: #989898;
  font-weight: 400;
  font-size: 13px
}

.examInfoSlider .examCategoryInfo .examCriteria {
  padding: 20px;
  padding-bottom: 0
}

.examInfoSlider .examCategoryInfo .examCriteria ul {
  padding: 0;
  margin: 0
}

.examInfoSlider .examCategoryInfo .examCriteria ul li {
  display: inline-block
}

.examInfoSlider .examCategoryInfo .examCriteria ul li a {
  padding: 5px 7px;
  border-radius: 3px;
  border: 1px solid var(--anchor-textclr);
  display: block;
  text-decoration: none;
  color: var(--anchor-textclr);
  font-size: 14px;
  line-height: 24px;
  text-transform: uppercase;
  font-weight: var(--font-500);
  margin-bottom: 16px
}

.examCriteria ul {
  display: -webkit-box;
  display: flex;
  flex-wrap: wrap
}

.examCriteria ul li {
  margin-right: 16px;
  text-align: center
}


@media (max-width: 1023px) {
  .container nav {
      padding: 0;
  }

  .pageDescription {
    background-color: #fff;
    padding: 20px;
  }

  .row {
      margin: 0 -10px;
  }

  .pageDescription h1 {
      font-size: 18px;
      font-weight: 400;
      line-height: 1.56;
      color: #282828;
      padding-bottom: 10px;
      display: inline;
  }

  .authorInfoAndTranslateBtn {
      flex-wrap: wrap;
      gap: 10px;
  }

  .pageDescription .updated-info.row .updatedDetails {
      flex-wrap: wrap;
  }

  .pageDescription .updated-info.row {
    margin-top: 10px;
      align-items: flex-start;
      flex-wrap: nowrap;
  }

  .pageDescription .updated-info.row ul {
      z-index: 0;
  }

  .pageDescription .updated-info.row ul {
      position: unset;
      margin: 0;
  }

  .trendingArtilerList, .recentArticlesList, .recentnewsList {
      padding: 10px;
  }

  .sidebarImgDiv {
      flex-basis: 56px;
      margin-right: 10px;
      min-height: 56px;
  }

  .sidebarImgDiv img {
      width: 56px;
      height: 56px;
  }

  .horizontalRectangle, .verticleRectangle, .squareDiv {
      display: block;
      margin-bottom: 20px;
  }

  /************** Sidebar Live Application ****************/

  .sideBarSection {
      margin-bottom: 10px;
      width: 100%;
  }

  .badgeIcon {
      width: 24px;
      height: 28px;
      background-position: 167px -262px;
      margin-right: 15px;
      margin-top: -10px;
  }

  .sideBarSection .sidebarHeading {
      margin: 10px;
      margin-bottom: 0;
  }

  .overflow-scroll {
      overflow: auto;
  }

  .sideBarSection .listCard {
      padding: 10px;
  }

  .sideBarSection .sidebarImgDiv {
      flex-basis: 56px;
      margin-right: 10px;
  }

  .sideBarSection .sidebarImgDiv img {
      width: 56px;
      height: 56px;
  }

  .sideBarSection .sidebarTextLink .cardText {
      padding-bottom: 5px;
      color: var(--primary-font-color);
  }

  .sideBarSection .sidebarTextLink {
      flex-basis: calc(100% - 66px);
  }

  .sideBarSection .sidebarTextLink p {
      font-size: 12px;
      line-height: 16px;
  }

  .sideBarSection .sidebarTextLink .applyText {
      padding-top: 5px;
  }

  /************** College ***********************/

  .liveApllicationFormsInner .row,.clgWithCourseInner .row {
      overflow: auto;
      white-space: nowrap;
      display: block
  }

  .liveApllicationFormsInner .row .applicationDiv,.liveApllicationFormsInner .row .clgWithCourseDiv,.clgWithCourseInner .row .applicationDiv,.clgWithCourseInner .row .clgWithCourseDiv {
      margin-right: 10px;
      margin-bottom: 0;
      display: inline-block;
      width: 86%;
      max-width: 86%;
      white-space: normal;
      vertical-align: middle;
      overflow: auto
  }

  .liveApllicationFormsInner .clgWithCourseDiv .viewAllDiv,.clgWithCourseInner .clgWithCourseDiv .viewAllDiv {
      min-height: 350px
  }

  .clgWithCourseInner .row .clgWithCourseDiv:nth-of-type(5n+0) {
      margin-right: 10px
  }

  .examInfoSlider {
      padding: 10px
  }

  .examInfoSlider .scrollLeft,.examInfoSlider .scrollRight,.examInfoSlider .rightLst,.examInfoSlider .leftLst {
      display: none;
  }

  .examInfoSlider .examCategoryInfo {
      margin-bottom: 0
  }

  .examInfoSlider h2 {
      padding: 8px 10px;
      font-size: 15px;
      margin-bottom: 10px;
      font-weight: var(--font-semibold)
  }

  .examInfoSlider h2 a {
      display: none
  }

  .examInfoSlider h2.row {
      margin-bottom: 10px;
      display: block;
      padding: 8px 10px
  }

  .examInfoSlider .row,.examInfoSlider .MultiCarousel-inner {
      overflow: auto;
      white-space: nowrap;
      display: block;
      padding: 0 10px
  }

  .examInfoSlider .row .col-md-6,.examInfoSlider .row .col-md-4,.examInfoSlider .row .item,.examInfoSlider .MultiCarousel-inner .col-md-6,.examInfoSlider .MultiCarousel-inner .col-md-4,.examInfoSlider .MultiCarousel-inner .item {
      float: none;
      margin-right: 10px;
      margin-bottom: 0;
      display: inline-block;
      width: 86%;
      max-width: 86%;
      white-space: normal;
      vertical-align: middle;
      overflow: auto;
      padding: 0
  }

  .examInfoSlider .examInfoSlider .MultiCarousel-inner {
      padding: 0
  }

  .examInfoSlider .examCategoryInfo .row {
      padding: 10px;
      display: block;
      border-bottom: none;
      min-height: 177px
  }

  .examInfoSlider .examCategoryInfo img {
      max-width: 72px;
      margin-bottom: 10px
  }

  .examInfoSlider .examCategoryInfo p:first-child {
      font-size: 14px
  }

  .examInfoSlider .examCategoryInfo .examCriteria {
      padding: 0 10px
  }

  .examInfoSlider .examCategoryInfo .examCriteria ul {
      display: block
  }

  .examInfoSlider .examCategoryInfo .examCriteria ul li {
      margin-right: 10px
  }

  .examInfoSlider .examCategoryInfo .examCriteria ul lili:nth-of-type(2n+0) {
      margin-right: 10px
  }

  .examInfoSlider .examCategoryInfo .examCriteria ul li a {
      font-weight: 600;
      font-size: 13px;
      margin-bottom: 10px
  }
}


/**Comment Section */

.commentSection {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .12);
  background-color: var(--color-white);
  padding: 10px;
  margin-bottom: 20px;
  border-bottom: var(--border-line);
}
.commentSection h2 {
  color: var(--primary-font-color);
  padding: 10px 24px;
  line-height: 24px;
  background: #f5f5f5;
  font-weight: var(--font-semibold);
  font-size: 15px;
  padding: 10px;
  margin-bottom: 10px;
}
.commentSection .commentForm {
  padding-bottom: 20px;
  border-bottom: var(--border-line);
}
.commentSection .commentForm .row {
  margin: 0;
}
.commentSection .commentForm .form-group {
  margin-bottom: 0;
}
.commentForm textarea {
  padding: 11px 20px;
  border-radius: 4px;
  border: var(--border-line);
  line-height: 24px;
  width: 100%;
}
.help-block {
  color: var(--color-red);
  font-size: 12px;
  padding-top: 5px;
}
.commentSection .commentForm .form-group {
  margin-bottom: 0;
}

.commentSection .commentForm input, .commentSection .commentForm textarea {
  padding: 11px 20px;
  border-radius: 4px;
  border: var(--border-line);
  line-height: 24px;
  width: 100%;
}

.commentSection .commentForm .col-md-6 {
  padding-bottom: 10px;
  flex-basis: 100%;
  margin-right: 0;
}

.commentSection .commentForm .form-group {
  margin-bottom: 0;
}
.col-lg-11{
  padding: 0 10px;
  position: relative;
  width: 100%;
}
.primaryBtn, a.primaryBtn, button.primaryBtn {
  background: var(--color-red);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-white);
  padding: 8px 15px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  transition: 0.2s ease;
  outline: none;
}
.commentSection .commentForm .commentBtn {
  display: block;
  margin-top: 10px;
}
/************Header CSS*****************/
.page-header {
  height: 46px;
}

.page-header .topHeader {
  background: var(--topheader-bg);
  padding: 7px 20px;
  position: relative;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 5;
  height: inherit;
}

.topHeader .row {
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
}

.page-header .headerLogo {
  width: 121px;
  height: 31px;
  background-position: -416px -803px;
  vertical-align: middle;
  transform: none;
  margin-left: 20px;
}

.page-header .headerSubscribeButton {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #fff;
  width: 103px;
  text-decoration: none;
}

.page-header .headerSubscribeButton .bellIcon {
  width: 16px;
  height: 16px;
  background-position: -276px -882px;
  vertical-align: middle;
}

.page-header .headerSubscribeButton p {
  display: inline;
  font-size: 12px;
  font-weight: bold;
  line-height: 2.17;
  color: #0966c2;
  margin-left: 10px;
}

.headerToolDiv {
  padding: 10px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border: solid 1px #d8d8d8;
  background-image: linear-gradient(to right, #fff 0%, #fffbec 100%);
  border-left: 6px solid #ff4e53;
  margin-bottom: 10px;
  position: sticky;
  top: 0px;
  z-index: 0;
}

.headerToolDiv h2 {
  font-size: 15px;
  font-weight: 600;
  line-height: 26px;
  color: #282828;
}

.headerToolDiv .headerToolCTADiv {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: 5px;
}

.headerToolDiv .headerToolCTADiv p {
  font-size: 15px;
  font-weight: 400;
  line-height: 24px;
  color: #282828;
  max-width: 250px;
}

.headerToolDiv .headerToolCTADiv button {
  flex-basis: 96px;
  flex-shrink: 0;
  height: 30px;
  border-radius: 3px;
  background-color: #ff4e53;
  border: none;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.71;
  color: #fff;
}

.headerCTAPair {
  display: flex;
  padding: 10px;
  background-color: #fff;
  margin-bottom: 10px;
  position: sticky;
  top: 0px;
  border: solid 1px #d8d8d8;
  z-index: 35;
}

.headerCTAPair button {
  flex: 1;
  border-radius: 3px;
  height: 34px;
  border: none;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.71;
}

.headerCTAPair button:first-child {
  border: solid 1px #0966c2;
  background-color: #fff;
  margin-right: 10px;
  color: #0966c2;
}

.headerCTAPair button:last-child {
  background-color: #ff4e53;
  color: #fff;
}

.hambergerIcon {
  width: 27px;
  height: 20px;
  background-position: 476px -72px;
  margin-left: 0;
  vertical-align: middle;
}

.searchIcon {
  width: 21px;
  height: 23px;
  background-position: 535px -72px;
  vertical-align: middle;
}

/****Footer****/

.pageFooter {
  color: var(--color-white);
  margin-top: 20px;
  contain-intrinsic-size: auto 500px;
  content-visibility: auto;
}

.pageFooter .footerPrimarySection {
  background: #273553;
  padding: 20px 10px;
}

.container {
  padding: 0 10px;
}

.pageFooter .footerPrimarySection .row {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
}

img {
  max-width: 100%;
  height: auto;
}

.pageFooter .footerPrimarySection .socialMedia {
  padding: 0;
  display: inline-block;
  margin: 22px 0;
}

.pageFooter .footerPrimarySection .socialMedia li:first-child {
  display: inline-block;
  padding: 0;
  font-weight: 500;
  vertical-align: middle;
  font-size: 14px;
  line-height: 20px;
  margin: 0;
  color: var(--color-white);
}

.pageFooter .footerPrimarySection .socialMedia li {
  display: -webkit-inline-box;
  display: inline-flex;
  vertical-align: middle;
  margin-right: 5px;
}

.pageFooter .socialMedia li a {
  width: 28px;
  height: 28px;
  border-radius: 50px;
}

.fbIcon {
  background-position: 476px -200px;
}

.instaIcon {
  background-position: 476px -268px;
}

.linkdIn {
  background-position: 476px -302px;
}

.youtubeIcon {
  background-position: 476px -364px;
}

.twitterIcon {
  background-position: 476px -234px;
}

.pageFooter .footerPrimarySection .contactInfo {
  padding: 0;
  margin: 0;
  display: inline-block;
}

.pageFooter .footerPrimarySection .contactInfo li {
  display: inline-block;
  padding: 0;
  padding-bottom: 10px;
}

.phoneIcon {
  width: 24px;
  height: 24px;
  background-position: 536px -246px;
  vertical-align: middle;
  margin-right: 7px;
}

.whiteMailIcon {
  width: 24px;
  height: 24px;
  background-position: 536px -219px;
  vertical-align: middle;
  margin-right: 7px;
}

.pageFooter .footerPrimarySection .contactInfo li a {
  color: var(--color-white);
}

.headerLogo, .footerLogo {
  width: 179px;
  height: 44px;
  background-position: 256px -72px;
}

li {
  font-size: 15px;
  line-height: 28px;
}

.pageFooter .footerSecondSection {
  background: #1f2b45;
  padding: 20px 10px;
  padding-bottom: 60px;
} 

.pageFooter .footerSecondSection .row {
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  align-items: center;
  margin: 0;
  -webkit-box-pack: justify;
  justify-content: space-between;
}

.pageFooter .footerSecondSection ul {
  padding: 0;
  margin-bottom: 10px;
}

.pageFooter .footerSecondSection .copyrightsText {
  border: none;
  padding: 0;
  font-size: 12px;
  line-height: 24px;
}

.pageFooter .footerSecondSection ul li {
  display: block;
  padding: 0;
  font-size: 14px;
}

.pageFooter .footerSecondSection ul li a {
  color: var(--color-white);
  font-size: 14px;
  display: block;
  line-height: 28px;
}

/****************** Bottom Breadcrumb **********************/

.breadcrumbDiv, .topHeader {
  background: #0966c2;
  padding: 8px;
}

.container .newsDetailsBreadCrumb {
  background-color: initial;
  padding: 8px;
}

.breadcrumbDiv ul {
  margin: 0;
  padding: 0;
  display: block;
  white-space: nowrap;
  overflow: auto;
}

.breadcrumbDiv ul li {
  display: inline;
  font-size: 13px;
  line-height: 20px;
  word-break: break-all;
}

.newsDetailsBreadCrumb div ul li {
  color: #282828;
}

.breadcrumbDiv ul li a {
  text-decoration: none;
  position: relative;
  padding-right: 13px;
  color: var(--color-white);
  font-weight: var(--font-semibold);
}

.newsDetailsBreadCrumb div ul li a {
  color: #282828;
}

.breadcrumbDiv ul li a:after {
  content: "";
  background: url(/yas/images/master_sprite.webp);
  width: 10px;
  height: 12px;
  position: absolute;
  right: 0;
  bottom: 1px;
  background-position: -36px -150px;
}

/************************ Related News **************************/

.latestInfoSection {
  border-radius: 4px;
  background-color: var(--color-white);
  margin-bottom: 20px;
  padding: 10px;
  box-shadow: none;
  border: var(--border-line);
}

.latestInfoSection h2 {
  line-height: 24px;
  background: #f5f5f5;
  margin: 0;
  text-transform: uppercase;
  font-size: 15px;
  padding: 10px;
  margin-bottom: 10px;
  font-weight: var(--font-semibold);
}

.latestInfoList.row {
  overflow: auto;
  white-space: nowrap;
  display: block;
  margin: 0;
}

.latestInfoSection .latestInfoDiv {
  position: relative;
  margin-right: 10px;
  margin-bottom: 0px;
  display: inline-block;
  width: 70%;
  white-space: normal;
  vertical-align: middle;
  overflow: auto;
  text-align: left;
  border-radius: 6px;
  border: var(--border-line);
  min-height: 274px;
}

.latestInfoSection a {
  text-decoration: none;
}

.latestInfoSection .latestInfoDiv figure {
  margin: 0;
  min-height: 168px;
}

.latestInfoSection .latestInfoDiv img {
  display: block;
  width: auto;
  margin: 0 auto;
  height: 168px;
  object-fit: cover;
}

.latestInfoSection .latestInfoTxt {
  padding: 10px;
}


.latestInfoSection .latestInfoTxt p:last-child {
  color: #282828;
  margin: 0;
  font-weight: normal;
}

.latestInfoSection .latestInfoTxt .latestInfoTxt-title {
  -webkit-line-clamp: 2;
}

.latestInfoSection .latestInfoTxt p {
  max-height: 100%;
}

.latestInfoSection .latestInfoTxt p {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-500);
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 58px;
  margin-bottom: 10px;
}

.latestInfoSection .latestInfoDiv .articleAuthorName {
  padding: 0px 0px 0px 10px;
  color: #989898;
  margin: 0;
  font-weight: normal;
  font-size: 14px;
}

.authorAndDate {
  margin-bottom: 10px;
}

.authorAndDate .authorName {
  font-size: 14px;
  font-weight: 500;
  padding-left: 10px;
  line-height: 1.43;
  color: #282828;
  text-transform: none;
}

.latestInfoSection a, .otherEntranceExams a {
  text-decoration: none;
}

.widgetAuthorName {
  position: relative;
  color: #989898;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  padding-left: 10px;
  font-size: 14px;
  padding-top: 0px;
  margin-bottom: 3px;
}

.scrollToTop {
  position: fixed;
  right: 20px;
  bottom: 100px;
  z-index: 5;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50px;
  background-image: url('/yas/images/master_sprite.webp');
  background-position: 448px -395px;
  box-shadow: 0 4px 4px rgb(0 0 0 / 25%);
  border: none;
  padding: 0;
}


/******************** Search Box **********************/

.advanceSearch {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  background: #f9f9f9;
  z-index: 5;
}

.searchSection {
  max-width: 730px;
  margin: 60px auto;
}

.searchSection .search_heading {
  display: flex;
  margin: 0;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 20px;
}

.cancelIcon {
  width: 24px;
  height: 24px;
  background-position: 535px -132px;
  cursor: pointer;
}

amp-selector[role=tablist].tabs-with-flex {
  display: flex;
  flex-wrap: wrap;
}

amp-selector[role=tablist].tabs-with-flex [role=tab] {
  flex-grow: 1;
  text-align: center;
  text-align: center;
  padding: 10px 15px;
  background-color: #f1f1f1;
  border-radius: 2px 2px 0px 0px;
  cursor: pointer;
  margin-right: 3px;
  font-weight: 700;
}

amp-selector[role=tablist].tabs-with-flex [role=tab][selected] {
  outline: none;
  background: var(--color-red);
  color: var(--color-white);
}

amp-selector[role=tablist].tabs-with-flex [role=tab][selected]+[role=tabpanel] {
  display: block;
}

amp-selector[role=tablist].tabs-with-flex [role=tabpanel] {
  display: none;
  width: 100%;
  order: 1;
  padding: var(--space-4);
}

amp-selector[role=tablist].tabs-with-flex [role=tab][selected]+[role=tabpanel] input {
  width: 100%;
  padding: 5px 10px;
  font-size: 15px;
  height: 40px;
  outline: none;
}

/****Table Wrap***/
table {
  margin: auto;
  border-collapse: collapse;
  overflow-x: auto;
  display: block;
  width: fit-content;
  max-width: 100%;
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, .1);
}

/**************** Lead Form **********************/

.pageMask {
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: block;
  overflow-y: hidden;
  z-index: 10;
}

.leadFormContainerNews {
  animation: slideIn 1000ms ease-in-out;
  position: fixed;
  right: 0;
  display: block;
  bottom: 0;
  left: unset;
  top: unset;
  height: auto;
  width: 100%;
  overflow: hidden;
  border-radius: 4px;
  z-index: 20;
}

.closeLeadFormContainer {
  position: absolute;
  right: 10px;
  top: 11px;
  z-index: 8;
}

.closeLeadFormContainer .closeLeadForm {
  background-position: -99px -805px;
  width: 28px;
  height: 28px;
  border: none;
}

.webpSpriteIcon {
  display: inline-block;
  background: url(/yas/images/master_sprite.webp);
  text-align: left;
  overflow: hidden;
}

.leadFormDiv {
  position: relative;
  border-radius: 4px;
  background: var(--color-white);
  padding: 20px;
  max-width: 100%;
  margin: 0 auto;
  margin-top: 0px;
}

.leadFormDiv input, .leadFormDiv select, .leadFormDiv .dialCodeDiv {
  padding: 7px;
  /* padding-left: 42px; */
  min-height: 40px;
  font-size: 13px;
  line-height: 24px;
  border-radius: 4px;
  border: var(--border-line);
  outline: none;
  width: 100%;
  background: var(--color-white);
  color: var(--primary-font-color);
}

.leadFormDiv input, .leadFormDiv select{
  padding-left: 42px;
}

.leadFormDiv .dialCodeDiv {
  padding-left: 14;
  flex-basis: 90px;
  position: relative;
  height: 40px;
  border-radius: 4px 0 0 4px;
  padding-left: 0px;
  border-right: var(--border-line);
  text-align: center;
}

.formHeadingDiv.row {
  margin: 0;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding-bottom: 20px;
  padding-right: 15px;
}

.formHeadingDiv .formImg {
  flex-basis: 56px;
  margin-right: 10px;
  min-height: 56px;
  display: flex;
  align-items: center;
  border-radius: 50%;
}

span.getmyuniLogoIcon {
  background-position: -660px -1062px;
  width: 62px;
  height: 62px;
}

.formHeadingDiv .formHeading {
  flex-basis: calc(100% - 72px);
}

.leadFormContainerNews .leadFormDiv .headingText {
  text-transform: none;
  font-weight: 500;
  padding-right: 8px;
  font-size: 16px;
  line-height: 24px;
  padding-bottom: 5px;
  color: var(--primary-font-color);
  padding: 0;
}

.formHeadingDiv p {
  padding: 0;
  font-size: 12px;
  line-height: 20px;
  color: #787878;
}

.leadFormContainerNews .leadFormDiv .userInputs .row .col-md-6 {
  width: 100%;
  padding: 0 10px;
  flex-basis: 100%;
}

.leadFormDiv .form-group {
  padding-bottom: 10px;
  position: relative;
}

[visible-when-invalid]:not(.visible), form [submit-error], form [submit-success], form [submitting] {
  display: none;
}

[visible-when-invalid] {
  font-size: 12px;
  color: red;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 {
  margin: 0;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .checkbox-group {
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 10px;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .checkbox-group input[type=checkbox] {
  accent-color: #ff4e53;
}

.checkbox-group input {
  padding: 0;
  min-height: auto;
  margin-right: 5px;
  width: 16px;
  height: 16px;
  margin: 0;
  border-radius: 0px;
  vertical-align: middle;
  cursor: pointer;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .checkbox-group label {
  font-size: 12px;
  line-height: 24px;
  color: #787878;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .formSumbitBtn {
  width: 100%;
  padding: 0;
  padding-bottom: 10px;
  text-align: right;
}

.primaryBtn, a.primaryBtn, button.primaryBtn {
  color: var(--color-white);
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: 0.2s ease;
  outline: none;
  font-size: 14px;
  line-height: 24px;
  width: 100%;
  display: block;
  padding: 6px;
  margin-right: 0;
  background: var(--color-red);
}

button.primaryBtn[disabled] {
  border: 1px solid #e9acac;
  background: #e9acac;
}

.userIcon, .mailIcon, .locationIcon, .bookIcon, .capIcon {
  width: 20px;
  height: 19px;
  position: absolute;
  z-index: 1;
  left: 12px;
  top: 14px;
}

.full-star {
  width: 12px;
  height: 12px;
  background-position: 651px -173px;
  vertical-align: middle;
}

.empty-star {
  width: 12px;
  height: 12px;
  background-position: 651px -217px;
  vertical-align: middle;
}

.userIcon {
  background-position: 595px -238px;
}

.mailIcon {
  background-position: 594px -262px;
}

.locationIcon {
  background-position: 594px -311px;
}

.bookIcon {
  background-position: 595px -285px;
}

.userIcon, .mailIcon, .locationIcon, .bookIcon, .capIcon {
  top: 9px;
}

.leadFormDiv .mobileNumber.row {
  margin: 0;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.leadFormDiv .countryCode, .leadFormDiv .dialCodeDiv {
  flex-basis: 90px;
  position: relative;
}

.leadFormDiv .numberInput {
  padding-left: 0px;
  flex-basis: calc(100% - 90px);
}

.leadFormDiv .numberInput input {
  border-left: none;
  border-radius: 0 4px 4px 0;
  padding-left: 12px;
}

/*********College Article*********/
.custom-cardDisplay .sliderCardInfo {
  width: 275px;
  display: inline-block;
  padding: 0;
  white-space: initial;
}

.pageData p,
.pageData li,
.pageData a {
  font-size: 15px;
  line-height: 26px;
}

.two-cardDisplay .sliderCardInfo {
  width: 48.4%;
  display: inline-block;
  padding: 20px;
  margin-right: 18px;
}

.two-cardDisplay .sliderCardInfo:last-child {
  margin-right: 0;
}

.two-cardDisplay a {
  color: var(--primary-font-color);
  font-weight: 600;
}

.two-cardDisplay a:hover {
  color: var(--anchor-textclr);
}

.two-cardDisplay .customSliderCards .sliderCardInfo {
  min-height: 180px;
}

.two-cardDisplay .viewAllDiv {
  min-height: 160px;
}

.custom-cardDisplay .viewAllDiv {
  min-height: inherit;
}

.custom-cardDisplay .viewAllDiv {
  min-height: 303px;
}

.two-cardDisplay .viewAllIcon {
  background-position: 424px -73px;
  margin: 0 auto;
  margin-bottom: 5px;
  width: 44px;
  height: 40px;
}

.viewAllDiv a {
  color: var(--color-red);
}

.otherCategorySection {
  position: relative;
  padding-bottom: 10px;
}

.ohterCategoryArticles .row {
  margin: 0;
  flex-wrap: nowrap;
  overflow: auto;
}

.ohterCategoryArticles .row::-webkit-scrollbar {
  display: none;
}

.ohterCategoryArticles .categoryArticles {
  flex-basis: 8%;
  margin-right: 20px;
  margin-bottom: 10px;
}

.ohterCategoryArticles .categoryArticles:hover p {
  color: var(--anchor-textclr);
}

.ohterCategoryArticles .categoryArticles:last-child {
  margin-right: 0;
}

.ohterCategoryArticles .categoryArticlesImg {
  text-align: center;
  padding: 10px;
  border: var(--border-line);
  margin-bottom: 7px;
}

.ohterCategoryArticles .categoryArticlesImg .courseSprite {
  margin: 0;
}

.ohterCategoryArticles p {
  padding: 0;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
}

.ohterCategoryArticles a {
  text-decoration: none;
}

.ohterCategoryArticles .row {
  flex-wrap: nowrap;
  overflow: auto;
}

.ohterCategoryArticles .categoryArticles p {
  font-size: 14px;
}

.otherCategorySection .scrollRight {
  right: -20px;
}

.otherCategorySection .scrollLeft {
  top: 50%;
  left: -20px;
}

.pageData {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.pageData h2 {
  font-size: 18px;
  line-height: 28px;
  padding: 8px 20px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  text-transform: uppercase;
  position: relative;
}

.pageData h2 a {
  font-size: 14px;
  line-height: 24px;
  color: var(--color-red);
  font-weight: 500;
  text-transform: capitalize;
  display: block;
}

.pageData h2.row {
  display: flex;
}

.pageData p,
.pageData li,
.pageData a {
  font-size: 15px;
  line-height: 26px;
}

.pageData p {
  color: var(--primary-font-color);
  padding-bottom: 15px;
}

.pageData h3 {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}

.pageData h4 {
  padding-bottom: 10px;
  line-height: 24px;
  font-weight: 500;
}

.courseSprite {
  display: inline-block;
  background: url('/yas/images/course_sprite.webp');
  text-align: left;
  overflow: hidden;
  margin: 0;
  width: 68px;
  height: 67px;
  vertical-align: middle;
  cursor: pointer;
}

.engineering {
  background-position: -123px -28px;
}

.management {
  background-position: -394px -117px;
}

.science {
  background-position: -305px -291px;
}

.pharmacy {
  background-position: -215px -28px;
}

.law {
  background-position: -305px -117px;
}

.education {
  background-position: -305px -200px;
}

.dental {
  background-position: -123px -292px;
}

.medical {
  background-position: -305px -27px;
}

.agriculture {
  background-position: -28px -292px;
}

.design {
  background-position: -27px -28px;
}

.commerce {
  background-position: -214px -202px;
}

.architecture {
  background-position: -215px -292px;
}

.arts {
  background-position: -393px -27px;
}

.paramedical {
  background-position: -28px -203px;
}

.computer {
  background-position: -122px -203px;
}

.mass-communication {
  background-position: -215px -116px;
}

.hotel-management {
  background-position: -27px -115px;
}

.aviation {
  background-position: -395px -202px;
}

.veterinary {
  background-position: -122px -115px;
}

.animation {
  background-position: -400px -291px;
}

.vocational-courses {
  background-position: -27px -374px;
}

.photoGallery .row {
  margin: 0;
}

.photoGallery h2.row {
  margin-bottom: 20px;
}

.photoGallery .picture {
  flex-basis: 23.7%;
  border-radius: 50%;
  margin-right: 20px;
  border-radius: 4px;
  border: var(--border-line);
  overflow: hidden;
}

.photoGallery .picture img {
  height: 206px;
  display: block;
  margin: 0 auto;
}

.photoGallery .picture:last-child,
.photoGallery .picture:nth-of-type(4n) {
  margin-right: 0;
}

.four-cardDisplay .sliderCardInfo,
  .custom-cardDisplay .sliderCardInfo {
    margin-right: 6px;
    width: 224px;
    display: inline-block;
    min-height: 274px;
  }

  .four-cardDisplay .sliderCardInfo:nth-of-type(4n),
  .custom-cardDisplay .sliderCardInfo:nth-of-type(4n) {
    margin-right: 6px;
  }

  .four-cardDisplay .sliderCardInfo:last-child,
  .custom-cardDisplay .sliderCardInfo:last-child {
    margin-right: 0;
  }

  .four-cardDisplay .sliderCardInfo img,
  .custom-cardDisplay .sliderCardInfo img {
    height: 168px;
  }

  .four-cardDisplay .sliderCardInfo .textDiv,
  .custom-cardDisplay .sliderCardInfo .textDiv {
    padding: 10px;
  }

  .four-cardDisplay .sliderCardInfo .widgetCardHeading,
  .custom-cardDisplay .sliderCardInfo .widgetCardHeading {
    font-weight: 400;
  }

  .four-cardDisplay .sliderCardInfo.mobileOnly,
  .custom-cardDisplay .sliderCardInfo.mobileOnly {
    vertical-align: bottom;
  }

  .four-cardDisplay .sliderCardInfo+.mobileOnly .viewAllDiv {
    min-height: 266px;
  }

  .sideBarSection {
    margin-bottom: 10px;
  }

  .sideBarSection .sidebarHeading {
    margin: 10px;
    margin-bottom: 0;
  }

  .sideBarSection .listCard {
    padding: 10px;
  }

  .sideBarSection .sidebarImgDiv {
    margin-right: 10px;
    flex-basis: 56px;
  }

  .sideBarSection .sidebarTextLink {
    flex-basis: calc(100% - 66px);
  }

  .sideBarSection .sidebarTextLink .cardText {
    padding-bottom: 5px;
  }

  .viewAllDiv {
    min-height: 274px;
  }

  .viewAllDiv {
    min-height: 274px;
  }

  .sidebarImgDiv {
    flex-basis: 56px;
    margin-right: 10px;
    min-height: 56px;
  }

  .sidebarImgDiv img {
    width: 56px;
    height: 56px;
  }

  .listCard:last-child .recentnewsDiv.row {
    margin: 0;
  }

  .fixedExamRelatedDiv {
    border: none;
    box-shadow: 0 2px 4px 0 rgb(0 0 0/12%);
  }

  .fixedExamRelatedDiv {
    position: fixed;
    top: 0;
    z-index: 2;
    width: 100%;
    max-width: 1206px;
    margin: 0 auto;
    height: 50px;
    margin-left: -10px;
  }

  .containerMargin {
    margin-top: -155px;
  }

 
  .examInfo {
    padding: 10px;
  }

  .examInfo h2 {
    padding: 8px 10px;
    font-size: 15px;
    margin-bottom: 10px;
    text-transform: uppercase;
  }

  .examInfo h3 {
    font-size: 15px;
  }
  .otherCategorySection .scrollLeft,
  .otherCategorySection .scrollRight {
    display: none  ;
  }

  .pageData,
  .reviewsSection {
    padding: 10px;
    margin-bottom: 10px;
    word-break: break-word;
  }

  .pageData h2,
  .reviewsSection h2 {
    font-size: 18px;
    line-height: 24px;
    padding: 8px 10px;
    margin-bottom: 10px;
  }

  .pageData h2,
  .pageData h3 {
    background: 0 0;
    padding-left: 0;
    font-size: 15px;
  }

  .pageData p,
  .reviewsSection p {
    padding-bottom: 10px;
  }

  .two-cardDisplay .sliderCardInfo {
    padding: 10px;
    width: 271px;
    margin-right: 6px;
  }

  .two-cardDisplay .sliderCardInfo .row {
    display: block;
  }

  .two-cardDisplay .sliderCardInfo .clgLogo {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .two-cardDisplay .sliderCardInfo p:first-child {
    font-size: 14px;
  }

  .getSupport button.applyNow {
    margin-left: 0;
    width: 49%;
  }

  .photoGallery h2.row {
    margin-bottom: 10px;
  }

  .photoGallery .row {
    display: block;
    overflow: auto;
    white-space: nowrap;
  }

  .photoGallery .picture {
    display: inline-block;
    width: 224px;
    margin-right: 5px;
  }

  .photoGallery .picture.mobileOnly {
    display: inline-block  ;
    margin-left: 5px;
  }

  .photoGallery .picture .viewAllDiv {
    min-height: 170px;
  }

  .photoGallery .picture img {
    height: 168px;
    width: 100%;
  }

  .getSupport .getSupport__subheading {
    display: none;
  }


  .customSlider {
    position: relative;
  }
  
  .customSlider .scrollRight {
    right: -20px;
  }
  
  .customSlider .scrollLeft {
    top: 50%;
    left: -20px;
  }
  
  .customSlider .row {
    margin: 0;
  }
  
  .customSlider .customSliderCards {
    display: block;
    white-space: nowrap;
    overflow: auto;
  }
  
  .customSlider .customSliderCards::-webkit-scrollbar {
    display: none;
  }
  
  .customSlider .sliderCardInfo {
    border-radius: 4px;
    border: var(--border-line);
    margin-right: 14px;
    vertical-align: middle;
    min-height: 303px;
  }
  
  .customSlider .sliderCardInfo:last-child {
    margin-right: 0;
  }
  
  .customSlider .sliderCardInfo img {
    display: block;
    height: 207px;
    width: 100%;
    border-bottom: var(--border-line);
  }
  
  .customSlider .sliderCardInfo .clgLogo {
    max-width: 72px;
    height: 72px;
    display: block;
    margin-right: 20px;
  }
  
  .customSlider .sliderCardInfo .textDiv {
    padding: 20px;
  }
  
  .customSlider .sliderCardInfo .textDiv .collegeLogo {
    width: 56px;
    height: 56px;
    border-radius: 4px;
    margin-top: -60px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
    margin-bottom: 5px;
    display: block;
  }
  
  .customSlider .sliderCardInfo p {
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    font-weight: var(--font-semibold);
    padding-bottom: 0;
    white-space: initial;
  }
  
  .customSlider .sliderCardInfo p span {
    color: #989898;
    font-weight: 400;
    font-size: 13px;
  }
  
  .customSlider .sliderCardInfo .widgetCardHeading {
    font-size: 14px;
    padding-bottom: 0;
    min-height: 24px;
    margin-bottom: 5px;
    font-weight: 500;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
  }
  
  .customSlider .sliderCardInfo .subText {
    color: #989898;
    font-weight: 400;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    padding: 0;
    padding-bottom: 5px;
    position: relative;
  }
  
  .customSlider .sliderCardInfo .subText:last-child {
    padding-bottom: 20px;
  }
  
  .customSlider .sliderCardInfo .subText .spriteIcon {
    position: initial;
    vertical-align: middle;
  }
  
  .customSlider .sliderCardInfo a {
    text-decoration: none;
  }
  .articleHeader{
    margin-top: 157px;
  }
  figure { margin :0px } 
  .four-cardDisplay .sliderCardInfo+.mobileOnly .viewAllDiv {height: 100%;display: flex;justify-content: center;align-items: center;}
  .customSlider .customSliderCards{display:flex}
  .customSlider .sliderCardInfo {flex-shrink:0}
  .sliderCardInfo+.mobileOnly .viewAllDiv {height: 100%;display: flex;justify-content: center;align-items: center;}

 
