iframe {
  border: 0;
}

* {
  letter-spacing: normal;
  color: var(--primary-font-color);
}

table td span {
  color: var(--primary-font-color) !important;
}

.table-responsive {
  margin-bottom: 10px;
}

table caption {
  caption-side: bottom;
  margin-top: 20px;
  margin-bottom: 0;
  font-size: 15px;
  line-height: 24px;
  color: var(--primary-font-color);
}

.tab-content.activeLink {
  display: block;
}

.breadcrumbDiv ul li a {
  font-weight: 500;
}

.p-0 {
  padding: 0;
}

.pb-0 {
  padding-bottom: 0;
}

.text-center {
  text-align: center;
}

.pageInfo {
  max-height: 200px;
}

.faq_section.pageInfo {
  max-height: 400px;
}

.courseSprite {
  display: inline-block;
  background: url(../../images/course_sprite.webp);
  text-align: left;
  overflow: hidden;
  margin-right: 16px;
  width: 68px;
  height: 67px;
  vertical-align: middle;
  cursor: pointer;
}

.engineering {
  background-position: -123px -28px;
}

.management {
  background-position: -394px -117px;
}

.science {
  background-position: -305px -291px;
}

.pharmacy {
  background-position: -215px -28px;
}

.law {
  background-position: -305px -117px;
}

.education {
  background-position: -305px -200px;
}

.dental {
  background-position: -123px -292px;
}

.medical {
  background-position: -305px -27px;
}

.agriculture {
  background-position: -28px -292px;
}

.design {
  background-position: -27px -28px;
}

.commerce {
  background-position: -214px -202px;
}

.architecture {
  background-position: -215px -292px;
}

.arts {
  background-position: -393px -27px;
}

.paramedical {
  background-position: -28px -203px;
}

.computer {
  background-position: -122px -203px;
}

.mass-communication {
  background-position: -215px -116px;
}

.hotel-management {
  background-position: -27px -115px;
}

.aviation {
  background-position: -395px -202px;
}

.veterinary {
  background-position: -122px -115px;
}

.animation {
  background-position: -400px -291px;
}

.vocational-courses {
  background-position: -27px -374px;
}

.userIconWhite {
  width: 27px;
  height: 27px;
  background-position: 537px -308px;
  margin-right: 5px;
  vertical-align: middle;
}

.downloadIconred {
  width: 19px;
  height: 18px;
  background-position: 535px -341px;
  vertical-align: middle;
}

.courseDetailPage .pageInfo {
  max-height: 450px;
}

.courseHeroSection {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
  background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
  margin-top: 10px;
  padding: 28px 30px;
}

.courseHeroSection .primaryBtn {
  padding: 5px 20px;
}

.pageData {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.pageData h2 {
  font-size: 18px;
  line-height: 28px;
  padding: 8px 20px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  position: relative;
}

/* Scroll Top CSS */
.scrollToh2CSS {
  scroll-margin-top: 53px;
}

.table-content {
  margin: 0;
  margin-bottom: 20px;
}

/**/
.pageData h2 a {
  font-size: 14px;
  line-height: 24px;
  color: var(--color-red);
  font-weight: 500;
  text-transform: capitalize;
}

.pageData p,
.pageData li,
.pageData a {
  font-size: 15px;
  line-height: 26px;
}

.pageData p {
  color: var(--primary-font-color);
  padding-bottom: 15px;
}

.pageData h3 {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}

.pageData h4 {
  padding-bottom: 10px;
  line-height: 24px;
  font-weight: 500;
}

.pageData button {
  background: var(--color-red);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-white);
  padding: 8px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  transition: 0.2s ease;
  outline: none;
  margin-bottom: 10px;
}

.pageData ul {
  margin: 10px 0;
}

.pageData ul li {
  position: relative;
  list-style-type: none;
}

.pageData ul li:before {
  content: "";
  background: url(../../images/master_sprite.webp);
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

.pageData .primaryBtn,
.pageData a.primaryBtn,
.pageData button.primaryBtn {
  padding: 5px 15px;
}

.readMoreDiv {
  box-shadow: none;
  border: var(--border-line);
  border-top: none;
}

.readMoreDiv .readMoreInfo {
  text-transform: capitalize;
}

.readMoreDiv.pageInfo {
  max-height: 240px;
}

.customSlider {
  position: relative;
}

.customSlider .scrollRight {
  right: -20px;
}

.customSlider .scrollLeft {
  top: 50%;
  left: -20px;
}

.customSlider .row {
  margin: 0;
  align-items: center;
}

.customSlider .customSliderCards {
  display: flex;
  flex-wrap: nowrap;
  white-space: nowrap;
  overflow: auto;
}

.customSlider .customSliderCards::-webkit-scrollbar {
  display: none;
}

.customSlider .sliderCardInfo {
  border-radius: 4px;
  border: var(--border-line);
  margin-right: 14px;
  vertical-align: middle;
  min-height: 303px;
}

.customSlider .sliderCardInfo:last-child {
  margin-right: 0;
}

.customSlider .sliderCardInfo img {
  display: block;
  height: 207px;
  width: 100%;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
  padding: 2px;
  background-color: #fff;
  box-shadow: 3px 3px 5px 0 rgba(0, 0, 0, 0.05);
  object-fit: cover;
  /* border-bottom: var(--border-line); */
}

.customSlider .sliderCardInfo .clgLogo {
  max-width: 72px;
  height: 72px;
  display: block;
  margin-right: 20px;
}

.customSlider .sliderCardInfo .textDiv {
  padding: 20px;
}

.customSlider .sliderCardInfo .textDiv .collegeLogo {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  margin-top: -60px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 5px;
  display: block;
}

.customSlider .sliderCardInfo p {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-semibold);
  padding-bottom: 0;
  white-space: initial;
}

.customSlider .sliderCardInfo p span {
  color: #989898;
  font-weight: 400;
  font-size: 13px;
}

.customSlider .sliderCardInfo .widgetCardHeading {
  font-size: 14px;
  padding-bottom: 0;
  min-height: 24px;
  margin-bottom: 5px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.customSlider .sliderCardInfo .subText {
  color: #989898;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  padding: 0;
  padding-bottom: 5px;
  position: relative;
}

.customSlider .sliderCardInfo .subText:last-child {
  padding-bottom: 0;
}

.customSlider .sliderCardInfo .subText .spriteIcon {
  position: initial;
  vertical-align: middle;
}

.customSlider .sliderCardInfo a {
  text-decoration: none;
}

.four-cardDisplay .sliderCardInfo {
  width: 23.8%;
  display: inline-block;
  white-space: initial;
}

.displayCard:hover .widgetCardHeading {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.updated-info.row {
  margin: 0;
  align-items: center;
  margin-bottom: 20px;
  line-height: 20px;
  font-size: 14px;
}

.updated-info img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 6px;
  vertical-align: middle;
}

.updated-info p {
  display: inline-block;
  padding: 0;
  font-weight: 400;
}

.updated-info .updatedBy a {
  color: var(--anchor-textclr);
}

.updated-info .updatedBy a.authorName {
  color: var(--primary-font-color);
}

.updated-info .updatedBy a:hover,
.updated-info .authorName:hover {
  color: var(--anchor-textclr);
}

.pageRedirectionLinks {
  padding: 0 10px;
  border: var(--border-line);
  background: var(--color-white);
  margin-bottom: 20px;
  border-radius: 4px;
  position: relative;
}

.pageRedirectionLinks .btn_right,
.pageRedirectionLinks .btn_left {
  position: absolute;
  width: 48px;
  height: 44px;
  background-color: #fff;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  top: 0;
  cursor: pointer;
}

.pageRedirectionLinks .btn_left {
  left: 0;
}

.pageRedirectionLinks .btn_right {
  right: 0;
}

.pageRedirectionLinks ul {
  margin: 0;
  padding: 0;
  white-space: nowrap;
  overflow: auto;
}

.pageRedirectionLinks ul::-webkit-scrollbar {
  display: none;
}

.pageRedirectionLinks ul li {
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  margin: 0 5px;
  list-style-type: none;
  display: inline-block;
}

.pageRedirectionLinks ul li .activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
  font-weight: 500;
  padding: 10px 0;
  padding-bottom: 11px;
}

.pageRedirectionLinks ul li a {
  color: #787878;
  text-decoration: none;
  display: inline-block;
  padding: 10px 0;
}

.custom-cardDisplay .sliderCardInfo {
  flex-shrink: 0;
  width: 275px;
  display: inline-block;
  padding: 0;
  white-space: initial;
}

.viewAllDiv {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  min-height: 331px;
}

.viewAllDiv a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  line-height: 24px;
  color: var(--color-red);
  text-align: center;
  font-weight: 600;
}

.two-cardDisplay .sliderCardInfo {
  flex-shrink: 0;
  width: 48.4%;
  display: inline-block;
  padding: 20px;
  margin-right: 18px;
}

.two-cardDisplay .sliderCardInfo:last-child {
  margin-right: 0;
}

.two-cardDisplay .customSliderCards .sliderCardInfo {
  min-height: auto;
}

.getSupport {
  padding: 19px;
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 3px;
  margin-bottom: 20px;
}

.getSupport .row {
  margin: 0;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}

.getSupport img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}

.getSupport p {
  font-size: 18px;
  line-height: 26px;
}

.getSupport button {
  width: 161px;
  border-radius: 3px;
  font-size: 14px;
  line-height: 24px;
  padding: 6px;
  text-align: center;
  color: var(--color-white);
  font-weight: var(--font-bold);
  border: none;
}

.getSupport button.talkToExpert {
  background: var(--topheader-bg);
}

.getSupport button.applyNow {
  background: var(--color-red);
  margin-left: 15px;
}

.sideBarSection {
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 4px;
  margin-bottom: 20px;
}

.sideBarSection .row {
  margin: 0;
  align-items: center;
}

.sideBarSection .sidebarHeading {
  background: #d8d8d8;
  padding: 10px 20px;
  font-size: 16px;
  line-height: 24px;
  margin: 20px;
  margin-bottom: 6px;
  font-weight: 500;
}

.sideBarSection .sidebarTextLink {
  flex-basis: calc(100% - 92px);
}

.sideBarSection .sidebarTextLink p {
  font-size: 14px;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.sideBarSection .sidebarTextLink .cardText {
  color: var(--primary-font-color);
}

.sideBarSection .sidebarTextLink .subText {
  color: #989898;
}

.sideBarSection p.listCard {
  font-weight: 500;
  font-size: 15px;
  line-height: 24px;
}

.sideBarSection .listCard {
  display: flex;
  padding: 10px 20px;
  border-bottom: var(--border-line);
}

.sideBarSection .listCard:hover .cardText,
.sideBarSection .listCard:hover .subText {
  color: var(--anchor-textclr);
}

.sideBarSection .listCard:last-child {
  border-bottom: none;
}

.sideBarSection .sidebarImgDiv {
  flex-basis: 72px;
  margin-right: 20px;
}

.sideBarSection .sidebarImgDiv img {
  display: block;
  margin: 0 auto;
  width: 100%;
}

.sideBarSection .applyText {
  color: var(--anchor-textclr);
}

.sidebarAds .appendAdDiv {
  width: 300px;
  height: 250px;
  display: block;
  margin: 0 auto;
  margin-bottom: 20px;
}

.ohterCategoryArticles .row::-webkit-scrollbar {
  display: none;
}

.ohterCategoryArticles .categoryArticles {
  flex-basis: 8%;
  margin-right: 20px;
  margin-bottom: 10px;
}

.ohterCategoryArticles .categoryArticles:hover p {
  color: var(--anchor-textclr);
}

.ohterCategoryArticles .categoryArticles:last-child {
  margin-right: 0;
}

.ohterCategoryArticles .categoryArticlesImg {
  text-align: center;
  padding: 10px;
  border: var(--border-line);
  margin-bottom: 7px;
}

.ohterCategoryArticles .categoryArticlesImg .courseSprite {
  margin: 0;
}

.ohterCategoryArticles p {
  padding: 0;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
  font-size: 14px;
}

.ohterCategoryArticles a {
  text-decoration: none;
}

.authorName {
  font-weight: var(--font-semibold);
}

.fixedRedirectionLinks {
  position: fixed;
  top: 0;
  z-index: 2;
  width: 100%;
  max-width: 1206px;
  margin: 0 auto;
}

.chartDiv {
  height: 300px;
  width: 100%;
  position: relative;
}

.chartDiv:before {
  content: "";
  position: absolute;
  width: 74px;
  height: 10px;
  right: 0;
  bottom: 0;
  z-index: 8;
  background: #fff;
}

.canvasjs-chart-credit {
  display: none;
}

.newsSidebarSection,
.articleSidebarSection {
  border-radius: 4px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
  margin-bottom: 20px;
  background: #fff;
}

.newsSidebarSection ul,
.articleSidebarSection ul {
  margin: 0;
  padding: 0;
  display: flex;
}

.newsSidebarSection ul li,
.articleSidebarSection ul li {
  list-style-type: none;
  flex-basis: 50%;
  text-align: center;
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  cursor: pointer;
  padding: 12px 5px;
  padding-bottom: 9px;
  border-bottom: var(--border-line);
}

.newsSidebarSection ul li.activeLink,
.articleSidebarSection ul li.activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
  font-weight: 500;
}

.newsSidebarSection .tab-content.activeLink,
.articleSidebarSection .tab-content.activeLink {
  display: block;
}

.recentnewsList {
  padding: 20px;
}

.recentnewsList .listCard:last-child .recentnewsDiv.row {
  margin: 0;
  padding: 0;
  border: none;
}

.recentnewsDiv.row {
  margin: 0;
  flex-wrap: nowrap;
  margin-bottom: 10px;
  border-bottom: var(--border-line);
  padding-bottom: 10px;
  align-items: center;
}

.sidebarImgDiv {
  flex-basis: 96px;
  margin-right: 16px;
  display: grid;
  min-height: 72px;
}

.sidebarImgDiv img {
  width: 96px;
  max-height: 72px;
  display: block;
  align-self: center;
}

.recentnewsDivText,
.recentArticlesDivText {
  flex-basis: calc(100% - 96px - 16px);
}

.recentnewsDivText .sidebarTextLink,
.recentArticlesDivText .sidebarTextLink {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: 500;
  text-decoration: none;
  display: -webkit-box;
  font-weight: 400;
  padding: 0;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  border: none;
}

.recentnewsDiv.row:hover .sidebarTextLink {
  text-decoration: underline;
  color: var(--anchor-textclr);
}

.two-cardDisplay .customSliderCards .sliderCardInfo,
.two-cardDisplay .viewAllDiv {
  min-height: 74px;
}

.custom-cardDisplay .viewAllDiv {
  min-height: 303px;
}

.custom-cardDisplay .viewAllDiv {
  min-height: 303px;
}

.two-cardDisplay .viewAllIcon {
  background-position: 424px -73px;
  margin: 0 auto;
  margin-bottom: 5px;
  width: 44px;
  height: 40px;
}

.two-cardDisplay a {
  color: var(--primary-font-color);
  font-weight: 600;
}

.two-cardDisplay a:hover {
  color: var(--anchor-textclr);
}

.viewAllDiv a {
  color: var(--color-red);
}

.otherCategorySection {
  position: relative;
}

.otherCategorySection .scrollRight {
  right: -20px;
}

.otherCategorySection .scrollLeft {
  top: 50%;
  left: -20px;
}

.subNavDropDown a {
  display: inline-block !important;
}

.subNavDropDown:hover .caret {
  background-position: 651px -154px;
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
}

.subNavDropDown .subNavDropDownMenu {
  display: none;
  margin: 0;
  position: absolute;
  z-index: 2;
  padding: 5px 20px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.24);
  background-color: #fff;
}

.subNavDropDown .subNavDropDownMenu li {
  display: block;
  color: #787878;
  cursor: pointer;
}

.subNavDropDown .subNavDropDownMenu li:not(:last-child) {
  margin-bottom: 5px;
}

.subNavDropDown .subNavDropDownMenu li:hover {
  color: #ff4e53;
}

.subNavDropDown:hover .subNavDropDownMenu {
  display: block;
}

.btn_left,
.btn_right {
  z-index: 1;
}

.mobileSubNavDropDownMenu {
  display: none;
}

.subNavDropDown .subNavDropDownMenu li a {
  padding: 0;
  color: #787878;
}

.subNavDropDown .subNavDropDownMenu li a:hover {
  color: #ff4e53;
}

.pageRedirectionLinks .caret {
  padding: 0;
  border: 0;
  line-height: unset;
}

.subNavActive {
  color: #ff4e53 !important;
}

.commonHeroSection {
  margin-top: 20px;
  padding: 20px;
}

.commonHeroSection .col-md-4 {
  display: block;
}

.commonHeroSection .heroHeader {
  align-items: center;
  margin: 0;
}

.commonHeroSection .heroHeader .headingContainer {
  margin-left: 10px;
}

.commonHeroSection .heroHeader .headingContainer h1 {
  font-size: 24px;
  font-weight: 400;
  line-height: 1.58;
  color: #282828;
  padding-bottom: 0;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.commonHeroSection .helpfulInfo {
  margin: 0;
  margin-top: 6px;
  padding: 5px 10px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  background-color: rgba(9, 102, 194, 0.1);
  max-width: fit-content;
}

.commonHeroSection .helpfulInfo .helpfulItem span {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.71;
  color: #282828;
}

.commonHeroSection .helpfulInfo .helpfulItem .calenderIcon {
  background-position: -395px -931px;
  width: 15px;
  height: 16px;
  vertical-align: middle;
  margin-right: 5px;
}

.commonHeroSection .helpfulInfo .helpfulItem .durationIcon {
  background-position: -420px -931px;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin-right: 5px;
}

.commonHeroSection .helpfulInfo .helpfulItem .coinIcon {
  background-position: -446px -931px;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin-right: 5px;
  margin-left: 20px;
}

.commonHeroSection .helpfulInfo .helpfulItem:first-child .spriteIcon {
  margin-left: 0;
}

.commonHeroSection .ctaColumn {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
}

.commonHeroSection .ctaColumn .ctaRow {
  display: flex;
  gap: 20px;
  justify-content: flex-end;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton {
  max-width: 184px;
  height: 36px;
  border-radius: 4px;
  border: solid 1px #ff4e53;
  background-color: rgba(255, 78, 83, 0.1);
  font-size: 16px;
  font-weight: 700;
  line-height: 1.5;
  color: #ff4e53;
  flex-grow: 1;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton .applyNowIcon {
  background-position: -543px -330px;
  width: 20px;
  height: 20px;
  vertical-align: middle;
  margin-left: 12px;
}

.commonHeroSection .ctaColumn .ctaRow .brochureButton {
  max-width: 126px;
  height: 36px;
  border-radius: 4px;
  background-color: #ff4e53;
  flex-grow: 1;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.71;
  color: #fff;
  padding: 5px 12px;
}

.commonHeroSection .ctaColumn .ctaRow .brochureButton .brochureButtonIcon {
  background-position: -513px -355px;
  width: 16px;
  height: 16px;
  margin-left: 10px;
  vertical-align: middle;
}

.pageData .courseSubpageText {
  padding-bottom: 0;
}

.updated-info.row img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.updated-info.row .authorAndDate .authorName {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.43;
  color: #282828;
  text-transform: none;
}

.updated-info.row .authorAndDate p {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.29;
  color: #787878;
  padding-bottom: 0;
  margin-top: 2px;
  display: block;
}

.updated-info.row .authorAndDate .verifiedBlueTickIcon {
  background-position: -673px -557px;
  width: 17px;
  height: 16px;
  vertical-align: top;
  transform: scale(0.85);
  margin-left: 8px;
}

.pageData .latestUpdates ul li span {
  font-weight: 500;
  font-size: 15px;
}

.pageData .latestUpdates ul li a {
  font-size: 15px;
  font-weight: 500;
}

.pageData .latestUpdates {
  margin-top: 10px;
  margin-bottom: 0px;
  padding-left: 0;
}

.pageData .latestUpdates .cardHeading {
  color: #282828;
}

.tableIcon {
  vertical-align: middle;
  margin-right: 20px;
}

.degreeIcon {
  background-position: -414px -882px;
  width: 24px;
  height: 17px;
}

.clockIcon {
  background-position: -346px -878px;
  width: 24px;
  height: 24px;
  transform: scale(0.8);
}

.ageIcon {
  background-position: -690px -325px;
  width: 24px;
  height: 12px;
}

.percentIcon {
  background-position: -648px -373px;
  width: 24px;
  height: 16px;
}

.feesIcon {
  background-position: -448px -878px;
  width: 24px;
  height: 24px;
  transform: scale(0.8);
}

.employmentIcon {
  background-position: -610px -324px;
  width: 20px;
  height: 16px;
}

.opportunityIcon {
  background-position: -610px -350px;
  width: 20px;
  height: 14px;
}

.courseDetailsTable table td:first-child {
  min-width: 270px;
  padding-left: 20px;
  text-align: left;
}

.courseDetailsTable .table-responsive {
  margin-bottom: 0;
}

.readMoreDiv,
.showMoreCourseWrap {
  max-height: 36px;
  padding: 5px;
}

.readMoreInfo,
.showMoreCourseCard {
  padding: 0;
}

.readMoreDiv:after,
.showMoreCourseWrap:after {
  top: -45px;
}

.courseDetailsTable table td:last-child {
  padding-left: 40px;
  text-align: left;
}

.pageData p:last-child {
  padding-bottom: 0;
}

.applyRedIcon {
  width: 20px;
  height: 20px;
  background-position: -165px -594px;
  vertical-align: middle;
  margin-left: 12px;
}

.topHeader .writeReview,
.topHeader .register {
  border-radius: 3px;
  font-weight: 500;
}

.ohterCategoryArticles {
  padding-bottom: 10px;
}

.ohterCategoryArticles .otherCategorySection {
  position: relative;
}

.ohterCategoryArticles .otherCategorySection .scrollLeft {
  top: 50%;
  left: -20px;
}

.ohterCategoryArticles .otherCategorySection .scrollRight {
  right: -20px;
}

.ohterCategoryArticles .row {
  margin: 0;
  flex-wrap: nowrap;
  overflow: auto;
}

.ohterCategoryArticles .row::-webkit-scrollbar {
  display: none;
}

.pageData h2 {
  text-transform: none;
}

.courseDetailsTable tr td:last-child {
  font-weight: 500;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton {
  max-width: 165px;
  font-weight: 500;
  font-size: 14px;
}

.commonHeroSection .ctaColumn .ctaRow .brochureButton {
  max-width: 165px;
  font-weight: 500;
  font-size: 14px;
}

.pageFooter {
  margin: 0;
}

.two-cardDisplay a {
  color: var(--primary-font-color);
  font-weight: 600;
}

.imgContainer .courseSprite {
  margin-right: 0;
}

.second-row-content {
  display: flex;
  justify-content: space-between;
}

.cta-mobile {
  padding-left: 0;
  padding-right: 0;
}

.commonHeroSection .helpfulInfo .helpfuItem .calenderIcon:last-of-type {
  margin-left: 10px;
}

table thead tr th,
table thead tr td,
table th {
  background-color: #0d3d63;
  color: #fff;
}

table th,
table td {
  text-align: left;
}

table thead strong {
  color: inherit;
}

.table-responsive table thead tr td p {
  color: #fff;
}

.table-responsive table thead p strong {
  color: #fff;
}

.pageData h2 {
  margin-top: 20px;
}

.pageData h2:first-of-type {
  margin-top: 0;
}

.scrollToTop {
  bottom: 155px;
  right: 30px;
}

.pageData img {
  margin: 0 auto;
  display: block;
}

.updatedBy img {
  display: inline-block;
}

.customSlider .sliderCardInfo .clgLogo {
  margin-left: 0;
  border-radius: 4px;
  border: 0.5px 0px 0px 0px;
  background: #FFFFFF;
  border: 0.5px solid #00000080
}

.customSlider .sliderCardInfo .textDiv .collegeLogo {
  margin-left: 0;
  margin-right: 0;
}

.commonHeroSection .heroHeader {
  flex-wrap: nowrap;
}

.signupModal .inputGrid .modalInputContainer .select2-container .selection {
  margin-top: 0;
  font-size: 14px;
  font-weight: 400;
}

.applyWhiteIconCta {
  width: 20px;
  height: 20px;
  background-position: -191px -594px;
  vertical-align: middle;
  margin-left: 12px;
}

.applyWhiteIconCta {
  position: static !important;
  top: 127px;
  right: 62px;
}

/*cls class*/
/*.lead-cta-course-cls{
  height:195px;
  display:inline-block;
}*/
/**/
/*GMU-471*/
.getSupport {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 0px;
  z-index: 5;
  /*display: flex;*/
  gap: 18px;
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
  color: #282828;
  align-items: center;
  justify-content: center;
  display: none;
}

.getSupport .getSupport__subheading {
  display: inline-block;
}

.getSupport .button__row__container {
  display: flex;
  gap: 13px;
  align-items: center;
}

.getSupport .row {
  display: none;
}

.getSupport button {
  width: 49%;
  border-radius: 2px;
  font-size: 13px;
  padding: 6px 4px;
  width: 149px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.getSupport button:last-child {
  margin-left: 0;
}

.discussionForumSection {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.discussionForumSection h2 {
  font-size: 18px;
  line-height: 28px;
  padding: 8px 20px;
  margin: 0;
  margin-bottom: 20px;
  font-weight: 500;
  background: #f5f5f5;
  text-transform: uppercase;
  position: relative;
}

/* additional widgets - course inlinks */

.customSlider .sliderCardInfo .examCriteria ul {
  padding: 0px;
  padding-bottom: 0;
  border-top: none;
  margin: 0;
}

.customSlider .sliderCardInfo .examCriteria ul:has(li) {
  padding: 20px;
  padding-bottom: 0;
  border-top: 1px solid #d8d8d8;
  margin-left: -20px;
  margin-right: -20px;
  margin-top: 20px;
}

.customSlider .sliderCardInfo .examCriteria ul li {
  display: inline-block;
}

.customSlider .sliderCardInfo .examCriteria ul li::before {
  content: none;
}

.customSlider .sliderCardInfo .examCriteria ul li a {
  padding: 5px 7px;
  border-radius: 3px;
  border: 1px solid var(--anchor-textclr);
  display: block;
  text-decoration: none;
  color: var(--anchor-textclr);
  font-size: 14px;
  line-height: 24px;
  text-transform: uppercase;
  font-weight: var(--font-500);
  padding: 4.55px 10px 4.55px 10px;
  background: #3D8FF217;
  border: 0.5px solid #3D8FF2
}

/**/

.table-content-heading {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
  margin-left: 14px;
  margin-top: 14px;
}

.table-content li:before {
  content: "";
  background: url(../../images/master_sprite.webp);
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

.table-content li {
  font-size: 14px;
  line-height: 24px;
  position: relative;
  list-style-type: none;
}

/**/
.table-content-ctn {
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  margin-bottom: 40px;
}

.table-content-heading-article {
  background-color: #f5f5f5;
  padding: 10px 16px;
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
  color: #282828;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.downArrowIcon,
.upArrowIcon {
  background-position: -151px -125px;
  width: 18px;
  height: 11px;
}

.table-content-article {
  padding: 0px 0px 10px 0px;
  margin: 0 0 10px 0;
  max-height: 200px;
  overflow-y: auto;
}

.table-content-article li {
  list-style-type: none;
  position: relative;
  padding: 0 30px;
  margin-top: 10px;
}

.rotate {
  transform: rotate(180deg);
  top: 0px !important;
}

a {
  color: #3d8ff2;
  text-decoration: none;
  cursor: pointer;
}

.table-content-ctn ::-webkit-scrollbar {
  width: 8px;
}

.table-content-ctn ::-webkit-scrollbar-thumb {
  background: #d8d8d8;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 5px #d8d8d8;
}

.table-content-article {
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s ease-out, padding 0.4s ease-out;
}

.table-content-article.open {
  max-height: 200px;
  padding: 10px 0;
  overflow: auto;
}

ul.table-content-article.open li:before {
  left: 5px;
}

.latestInfoDiv .authorAndDate {
  margin-bottom: 10px;
}

.pageData h2 {
  background-color: transparent !important;
  padding-left: 5px !important;
}

.examSliderCards {
  padding: 10px 5px !important;
}

.customSliderCards .examSliders {
  flex-shrink: 0;
  border: none !important;
  border-radius: 4px 0px 0px 0px;
  background: #FFFFFF;
  box-shadow: 0px 0px 9px 1px #00000014;
}

.two-cardDisplay {
  margin-top: 16px;
}

ul.examCardBtn {
  display: flex;
  gap: 10px;
}

.examCriteria ul li {
  margin-right: 0px !important;
}

.customSlider .sliderCardInfo.examSliders .examCriteria ul:has(li) {
  border-top: 0px;
  padding: 0 20px;
}

@media (max-width: 1023px) {

  .otherCategorySection .scrollLeft,
  .otherCategorySection .scrollRight {
    display: none !important;
  }

  .horizontalRectangle,
  .verticleRectangle,
  .squareDiv {
    margin-bottom: 10px;
  }

  .courseHeroSection {
    margin-top: -157px !important;
    padding: 20px;
    padding-bottom: 14px;
    margin-bottom: 10px;
    background: var(--color-white);
  }

  .courseHeroSection h1 {
    font-size: 18px;
    line-height: 24px;
    padding-bottom: 10px;
  }

  .courseHeroSection .searchBar {
    margin-bottom: 10px;
  }

  .pageData,
  .reviewsSection {
    padding: 10px;
    margin-bottom: 10px;
    word-break: break-word;
  }

  .pageData h2,
  .reviewsSection h2 {
    font-size: 15px;
    line-height: 24px;
    padding: 8px 10px;
    margin-bottom: 10px;
  }

  .pageData h2 a,
  .reviewsSection h2 a {
    display: none;
  }

  .pageData p,
  .reviewsSection p {
    padding-bottom: 10px;
  }

  .pageData ul,
  .reviewsSection ul {
    margin: 10px 0;
    padding-left: 30px;
  }

  .readMoreDiv {
    margin-bottom: 10px;
  }

  .four-cardDisplay .sliderCardInfo,
  .custom-cardDisplay .sliderCardInfo {
    margin-right: 6px;
    width: 224px;
    display: inline-block !important;
    min-height: 274px;
  }

  .four-cardDisplay .sliderCardInfo:nth-of-type(4n),
  .custom-cardDisplay .sliderCardInfo:nth-of-type(4n) {
    margin-right: 6px;
  }

  .four-cardDisplay .sliderCardInfo:last-child,
  .custom-cardDisplay .sliderCardInfo:last-child {
    margin-right: 0;
  }

  .four-cardDisplay .sliderCardInfo img,
  .custom-cardDisplay .sliderCardInfo img {
    height: 168px;
  }

  .four-cardDisplay .sliderCardInfo .textDiv,
  .custom-cardDisplay .sliderCardInfo .textDiv {
    padding: 10px;
  }

  .four-cardDisplay .sliderCardInfo .widgetCardHeading,
  .custom-cardDisplay .sliderCardInfo .widgetCardHeading {
    font-weight: 400;
  }

  .four-cardDisplay .sliderCardInfo.mobileOnly,
  .custom-cardDisplay .sliderCardInfo.mobileOnly {
    vertical-align: bottom;
  }

  .four-cardDisplay .sliderCardInfo+.mobileOnly .viewAllDiv {
    min-height: 266px;
  }

  /*.getSupport {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 10px;
    border-radius: 0;
    z-index: 1;
    display: none;
    background: var(--color-white);
    height: 58px;
  }
  .getSupport button {
    width: 49%;
    border-radius: 2px;
    font-size: 13px;
    padding: 6px 4px;
    text-align: center;
    color: var(--color-white);
    font-weight: var(--font-bold);
    border: none;
    line-height: 24px;
  }*/
  .getSupport button.talkToExpert {
    background: var(--topheader-bg);
  }

  .getSupport button.applyNow {
    background: var(--color-red);
    margin-left: 0;
  }

  .sideBarSection {
    margin-bottom: 10px;
  }

  .sideBarSection .sidebarHeading {
    margin: 10px;
    margin-bottom: 0;
  }

  .sideBarSection .listCard {
    padding: 10px;
  }

  .sideBarSection .sidebarImgDiv {
    margin-right: 10px;
    flex-basis: 56px;
  }

  .sideBarSection .sidebarTextLink {
    flex-basis: calc(100% - 66px);
  }

  .sideBarSection .sidebarTextLink .cardText {
    padding-bottom: 5px;
  }

  .pageRedirectionLinks {
    border-radius: 0;
    border-right: 0;
    border-left: 0;
    margin: 0 -10px;
    margin-bottom: 10px;
    padding: 0 5px;
  }

  .pageRedirectionLinks ul li {
    padding: 0;
    margin: 0 5px;
  }

  .pageRedirectionLinks ul li .activeLink,
  .pageRedirectionLinks ul li a {
    padding: 11px 0;
  }

  .getSupport .row {
    display: none;
  }

  .getSupport button {
    width: 49%;
    border-radius: 2px;
    font-size: 13px;
    padding: 6px 4px;
    height: 36px;
  }

  .getSupport button.applyNow {
    margin-left: 0;
  }

  .customSlider .scrollLeft,
  .customSlider .scrollRight {
    display: none !important;
  }

  .two-cardDisplay .sliderCardInfo {
    padding: 10px;
    width: 271px;
    margin-right: 6px;
  }

  .two-cardDisplay .sliderCardInfo .row {
    display: block;
  }

  .two-cardDisplay .sliderCardInfo .clgLogo {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .two-cardDisplay .sliderCardInfo p:first-child {
    font-size: 14px;
  }

  .updated-info {
    border-radius: 4px;
    border: var(--border-line);
    padding: 10px 20px;
    background: var(--color-white);
    margin-bottom: 10px;
  }

  .updated-info img {
    float: left;
  }

  .updated-info a {
    display: block;
  }

  .updated-info p {
    font-size: 14px;
    line-height: 24px;
  }

  .removeFixedQuickLink {
    display: none;
  }

  .ohterCategoryArticles .row {
    flex-wrap: nowrap;
    overflow: auto;
  }

  .ohterCategoryArticles .categoryArticles p {
    font-size: 14px;
  }

  .viewAllDiv {
    min-height: 274px;
  }

  .recentnewsList {
    padding: 10px;
  }

  .recentnewsDiv.row {
    padding: 0;
    border: none;
  }

  .sidebarImgDiv {
    flex-basis: 56px;
    margin-right: 10px;
    min-height: 56px;
  }

  .sidebarImgDiv img {
    width: 56px;
    height: 56px;
  }

  .recentnewsDivText,
  .recentArticlesDivText {
    flex-basis: calc(100% - 56px - 10px);
  }

  .recentnewsDivText .sidebarTextLink,
  .recentArticlesDivText .sidebarTextLink {
    -webkit-line-clamp: 2;
  }

  .listCard:last-child .recentnewsDiv.row {
    margin: 0;
  }

  .two-cardDisplay {
    padding: 0;
  }

  .two-cardDisplay .customSliderCards .sliderCardInfo {
    min-height: 180px;
  }

  .two-cardDisplay .viewAllDiv {
    min-height: 160px;
  }

  .custom-cardDisplay .viewAllDiv {
    min-height: inherit;
  }

  .mobileSubNavDropDownMenu {
    background: rgba(0, 0, 0, 0.119215);
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
    display: none;
  }

  .mobileSubNavDropDownDiv {
    position: fixed;
    height: auto;
    bottom: 0;
    left: 0;
    overflow: auto;
    border-radius: 4px 4px 0 0;
    width: 100%;
    background: #fff;
    z-index: 3;
  }

  .mobileSubNavDropDownDiv ul {
    margin: 0;
    padding: 0 20px;
  }

  .mobileSubNavDropDownDiv ul li {
    font-size: 14px;
    line-height: 24px;
    border-bottom: 1px solid #d8d8d8;
    display: flex;
    color: #787878;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }

  .mobileSubNavDropDownDiv ul li a {
    background: 0 0;
    border: none;
    color: #787878;
  }

  .mobileSubNavDropDownMenu.current {
    display: block;
  }

  .examRelataedLinks .mobileSubNavDropDownDiv ul {
    height: unset;
  }

  .mobileSubNavDropDownDiv ul li a {
    padding: 0;
  }

  .subNavDropDown:hover .caret {
    background-position: -438px -1089px;
    -webkit-transform: unset;
    transform: unset;
  }

  .mobileSubNavDropDown .caret {
    background-position: -382px -1057px;
    width: 25px;
    height: 37px;
    margin-left: 2px;
    margin-right: -10px;
    position: relative;
    bottom: 3px;
    transform: none !important;
  }

  .examRelataedLinks ul {
    overflow-y: hidden;
  }

  .subNavDropDown:has(.activeLink:hover):hover .caret {
    background-position: -382px -1057px;
  }

  .second-row-content {
    display: block !important;
  }

  .commonHeroSection {
    margin-top: 0 !important;
  }

  .courseCategoryPage {
    margin-top: -157px;
  }

  .getSupport .applyNowCourse .applyWhiteIconCta {
    position: static;
    margin-left: 5px;
  }

  .cta-mobile {
    margin-top: 10px;
  }

  .pageData.pageInfo {
    max-height: 200px;
  }

  .commonHeroSection .helpfulInfo .helpfulItem .coinIcon {
    margin-left: 10px;
  }

  .scrollToTop {
    bottom: 140px;
    right: 23px;
  }

  .commonHeroSection {
    padding: 10px;
    padding-bottom: 14px;
    margin: 0 -10px 10px;
    background: #fff;
  }

  .commonHeroSection h1 {
    font-size: 18px;
    line-height: 24px;
    padding-bottom: 10px;
  }

  .updated-info.row {
    border-radius: 4px;
    background: var(--color-white);
    margin-bottom: 10px;
    border: none;
    padding: 0;
  }

  .updated-info.row img {
    float: left;
  }

  .updated-info.row .authorAndDate .authorName {
    font-size: 12px;
  }

  .updated-info.row .authorAndDate p {
    font-size: 12px;
  }

  .updated-info.row .authorAndDate .verifiedBlueTickIcon {
    transform: scale(0.6);
    margin-left: 0;
    vertical-align: middle;
  }

  .commonHeroSection {
    padding: 10px;
    margin-top: 0;
    margin: 0 -10px 10px;
    background: #fff;
  }

  .commonHeroSection .heroHeader .imgContainer {
    flex-shrink: 0;
  }

  .commonHeroSection .heroHeader .headingContainer h1 {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.5;
  }

  .commonHeroSection .helpfulInfo {
    margin-top: 10px;
  }

  .commonHeroSection .helpfulInfo .examDateItem {
    flex-grow: 1;
    text-align: center;
  }

  .commonHeroSection .ctaColumn .ctaRow {
    gap: 10px;
  }

  .commonHeroSection .ctaColumn .ctaRow .brochureButton,
  .commonHeroSection .ctaColumn .ctaRow .applyNowButton {
    max-width: unset;
    flex: 1;
  }

  .headerLogo {
    transform: scale(0.8);
  }

  .page-header,
  .topHeader {
    height: 46px;
  }

  .topHeader {
    padding: 2px 20px;
  }

  .pageRedirectionLinks ul li .activeLink,
  .pageRedirectionLinks ul li a {
    padding: 8px 0;
  }

  .pageRedirectionLinks .btn_right,
  .pageRedirectionLinks .btn_left {
    height: 40px;
  }

  .right_angle,
  .left_angle {
    margin: 9px 0;
  }

  .courseDetailsTable table {
    table-layout: fixed;
  }

  .courseDetailsTable table td {
    border-right: none;
    border-bottom: none;
    vertical-align: top;
  }

  .courseDetailsTable table td span {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.43;
    color: #282828;
  }

  .courseDetailsTable table tr:last-child td {
    border-bottom: 0.2px solid #eaeaea;
  }

  .courseDetailsTable table td:first-child {
    min-width: unset;
  }

  .tableIcon {
    margin-right: 10px;
  }

  .pageData .latestUpdates {
    margin: 0;
  }

  .commonHeroSection .ctaColumn .ctaRow .applyNowButton,
  .commonHeroSection .ctaColumn .ctaRow .brochureButton {
    font-weight: 500;
  }

  .breadcrumbDiv {
    background: unset;
  }

  .breadcrumbDiv ul li {
    color: #282828;
  }

  .breadcrumbDiv ul li a {
    color: #282828;
  }

  .breadcrumbDiv ul li a::after {
    background-position: 707px -150px !important;
  }

  .commonHeroSection .helpfulInfo .helpfuItem {
    width: 100%;
  }

  .commonHeroSection .ctaColumn .ctaRow .applyNowButton,
  .commonHeroSection .ctaColumn .ctaRow .brochureButton {
    font-size: 14px;
  }

  .pageData p,
  .pageData li,
  .pageData a {
    font-size: 14px;
  }

  .courseDetailsTable table td:last-child {
    padding-left: 0;
  }

  .courseDetailsTable .ageIcon,
  .courseDetailsTable .employmentIcon,
  .courseDetailsTable .feesIcon,
  .courseDetailsTable .percentIcon {
    float: left;
    margin-bottom: 20px;
    margin-top: 5px;
  }

  .courseDetailsTable .ageIcon {
    margin-bottom: 32px;
  }

  .pageRedirectionLinks ul li .activeLink,
  .pageRedirectionLinks ul li a {
    padding: 7px 0;
  }

  .pageRedirectionLinks .btn_right,
  .pageRedirectionLinks .btn_left {
    height: 37px;
  }

  .pageRedirectionLinks .right_angle,
  .pageRedirectionLinks .left_angle {
    margin: 9px 0;
  }

  .otherCategorySection .scrollLeft,
  .otherCategorySection .scrollRight {
    display: none !important;
  }

  .examInfoSlider h2.row,
  .clgWithCourse h2.row,
  .liveApllicationForms h2.row {
    text-transform: none;
    background: 0 0;
    padding: 0;
  }

  .latestInfoSection h2,
  .otherEntranceExams h2,
  .headingRevamp h2 {
    background: 0 0;
    padding: 0;
    text-transform: none;
  }

  .commonHeroSection .helpfulInfo {
    max-width: 100%;
  }

  .courseDetailsTable table td:first-child {
    padding-left: 10px;
  }

  div.articleSidebarSection,
  div.newsSidebarSection {
    margin-bottom: 10px;
  }

  .pageFooter {
    margin-top: 0;
  }

  .examRelataedLinks ul,
  .pageRedirectionLinks ul {
    overflow-y: hidden;
  }

  iframe {
    width: 300px;
    height: 150px;
  }

  .blueBgDiv {
    display: none !important;
  }

  .course-borad-design {
    display: block !important;
  }

  .course-borad-design-breadcrumb {
    background-color: #0966c2;
  }

  .course-borad-design-breadcrumb ul li a {
    color: #fff;
  }

  .course-borad-design-breadcrumb ul li {
    color: #fff;
  }

  .course-borad-design-breadcrumb ul li a::after {
    background-position: -38px -119px !important;
  }

  .courseHeroSection-landing {
    margin-top: -157px !important;
  }

  .commonHeroSection .helpfulInfo .helpfuItem .calenderIcon:last-of-type {
    margin-left: 0;
  }

  .commonHeroSection .helpfulInfo .helpfuItem .calenderIcon:last-of-type::before {
    content: "\A";
    white-space: pre;
  }

  .pageData h2,
  .pageData h3 {
    background: 0 0;
    padding-left: 0;
    font-size: 15px;
  }

  .discussionForumSection .qnaAnswer {
    background: none;
    border: none;
  }

  .discussionForumSection .gmuLable {
    float: left;
  }

  .discussionForumSection .answerBy span.year {
    margin-left: 44px;
    margin-top: 5px;
  }

  .discussionForumSection .writeAnswer {
    background-color: #0966c2;
  }

  .discussionForumSection .writeAnswer,
  .discussionForumSection .moreAnswer {
    width: 50%;
    max-width: 145px;
    white-space: nowrap;
  }

  .discussionForumSection {
    padding: 10px;
    margin-bottom: 10px;
  }

  .getSupport .getSupport__subheading {
    display: none;
  }

  .getSupport .button__row__container {
    width: 100%;
  }

  /* additional widgets - course inlink */

  .customSlider .sliderCardInfo .examCriteria ul {
    display: block;
  }

  .customSlider .sliderCardInfo .examCriteria ul {
    padding: 0px;
    padding-bottom: 0;
    border-top: none;
    margin: 0;
  }

  .customSlider .sliderCardInfo .examCriteria ul:has(li) {
    padding: 0 10px;
    padding-top: 10px;
    margin-left: -10px;
    margin-right: -10px;
    margin-top: 10px;
  }

  .customSlider .sliderCardInfo .examCriteria ul li {
    margin-right: 10px;
  }

  .customSlider .sliderCardInfo .examCriteria ul li a {
    font-size: 13px;
  }

  .examSliders .examCardBtn {
    display: flex !important;
  }
}

@media only screen and (max-width: 600px) {
  .commonHeroSection .heroHeader .headingContainer h1 {
    -webkit-line-clamp: 3;
  }

  .selection {
    margin-top: 15vh;
  }

  .updated-info .authorName {
    display: inline;
  }
}