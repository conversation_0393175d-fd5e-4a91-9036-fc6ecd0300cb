@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

.page-header,
.pageFooter,
.scrollToTop {
    display: none !important;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

h1,
h2,
h3,
h4,
h5 {
    padding: 0;
    margin: 0;
    font-weight: 600;
    line-height: normal;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

body,
p {
    font-family: "Roboto", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    font-variation-settings: "wdth" 100;
    color: #000000;
    line-height: 1.5;
}

h1 {
    font-size: 50px;
    font-weight: 700;
}

h2 {
    font-size: 36px;
    font-weight: 600;
}

.button-style {
    background: #EE424F;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.section-space {
    margin-top: 70px;
}

/* header */
.container {
    max-width: 1280px;
    width: 100%;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
}

header {
    text-align: center;
    padding: 20px 15px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* banner */
.hero-banner {
    height: 600px;
    background-image: url(/../yas/images/clp/generic_one/banner-img.jpg);
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-banner .banner-overlay {
    background: linear-gradient(90deg, rgba(13, 63, 100, 0.9) 11%, rgba(26, 127, 202, 0.4) 71%);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.hero-banner .container {
    display: flex;
    justify-content: space-between;
}

.hero-banner .banner-left-content {
    max-width: 490px;
    width: 100%;
    color: white;
}

.hero-banner .banner-left-content h1 {
    font-size: 50px;
    line-height: 56px;
    font-weight: 700;
}

.hero-banner .banner-left-content h1>span {
    color: #EE424F;
}

.hero-banner .banner-left-content .top-college {
    margin-top: 10px;
}

.hero-banner .banner-left-content .top-college>span {
    background: url(/../yas/images/clp/generic/top-college-bg.png) top left no-repeat;
    width: 417px;
    height: 56px;
    color: #0D3F64;
    font-weight: 700;
    font-size: 28px;
    line-height: 50px;
    display: inline-block;
    padding: 0 10px;
    position: relative;
    background-size: cover;
}

.hero-banner .banner-left-content .jobready-block {
    margin-top: 20px;
}

.hero-banner .banner-left-content .jobready-block .jobready-head {
    font-size: 20px;
    line-height: 30px;
    font-weight: 600;
}

.hero-banner .banner-left-content .jobready-block>ul {
    list-style: none;
    padding: 0;
    margin: 8px 0 0;
}

.hero-banner .banner-left-content .jobready-block>ul>li {
    position: relative;
    padding-left: 40px;
    margin-bottom: 10px;
    font-weight: 400;
    font-size: 16px;
}

.hero-banner .banner-left-content .jobready-block>ul>li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    background-color: #EE424F;
    border-radius: 2px;
}

.hero-banner .banner-left-content .jobready-block>ul>li::after {
    content: '';
    position: absolute;
    left: 3px;
    top: 8px;
    width: 10px;
    height: 4px;
    border-left: 2px solid white;
    border-bottom: 2px solid white;
    transform: rotate(-45deg);
}

.hero-banner .banner-right-content {
    max-width: 368px;
    width: 100%;
    background-color: white;
    padding: 30px 24px;
    border-radius: 4px;
}

.hero-banner .banner-right-content .form-head {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
}

.hero-banner .form-body-block {
    margin-top: 20px;
}

.hero-banner .form-body-block .banner-form-inner {
    position: relative;
    margin-bottom: 16px;
}

.hero-banner .form-body-block .banner-form-inner>span {
    position: absolute;
    display: block;
    width: 100%;
    bottom: -17px;
    left: 0;
    font-size: 11px;
    color: #F8382A;
}

.hero-banner .form-body-block input {
    border: 1px solid #ADB5BD;
    border-radius: 8px;
    width: 100%;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    padding: 13px 24px;
}

.hero-banner .form-body-block input:focus {
    border-color: #F5A623;
}

.hero-banner .form-body-block input:focus-visible {
    outline: none;
}

.hero-banner .submit-btn {
    background-color: #EE424F;
    border-radius: 8px;
    width: 100%;
    margin-top: 10px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    border: 0;
    padding: 10px;
    color: white;
    cursor: pointer;
}

.input-error {
    border-color: #F8382A !important;
}

/* Why GetMyUni? */
.why-getmyuni {
    position: relative;
}

.why-getmyuni h2 {
    text-align: center;
}

.why-getmyuni-inner {
    display: flex;
    justify-content: space-between;
    column-gap: 90px;
    margin-top: 40px;
}

.why-getmyuni-inner .detail-block {
    max-width: 33.33%;
    width: 100%;
    margin: 0 20px;
    display: flex !important;
    flex-wrap: wrap;
    justify-content: center;
}

.why-getmyuni-inner .detail-block .img-block {
    text-align: center;
    margin-bottom: 30px;
}

.why-getmyuni-inner .detail-block .text-block {
    text-align: center;
    font-size: 24px;
    line-height: 30px;
    font-weight: 500;
}

.why-getmyuni .explore-now-btn {
    text-align: center;
    margin-top: 20px;
}
/* Streams Offered */
.streams-offered {
    position: relative;
    padding: 70px 0;
    background-color: #F5F5F5;
}

.streams-offered h2 {
    text-align: center;
}

.streams-offered h2 span {
    font-size: 18px;
    color: #212529;
    font-weight: 400;
    line-height: 24px;
    margin-top: 10px;
    display: block;
}

.streams-offered-inner {
    display: flex;
    justify-content: space-between;
    column-gap: 20px;
    margin-top: 40px;
}

.streams-offered-inner .detail-block {
    max-width: 412px;
    height: 267px;
    width: 100%;
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box, linear-gradient(to bottom, #0A62C7, #F5A623) border-box;
    margin: 0 15px;
}

.streams-offered-inner .detail-block .img-overlay {
    background: linear-gradient(0deg, rgba(245, 166, 35, 0.14) 0%, rgba(255, 255, 255, 0.14) 84%);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.streams-offered-inner .detail-block .img-block {
    text-align: center;
    width: 100%;
    height: 190px;
    position: relative;
}

.streams-offered-inner .detail-block .img-block img {
    object-fit: cover;
}

.streams-offered-inner .detail-block .text-block {
    font-size: 24px;
    line-height: 24px;
    font-weight: 700;
    text-transform: uppercase;
    color: #212529;
    width: 100%;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.streams-offered .explore-now-btn {
    text-align: center;
    margin-top: 30px;
}

.streams-offered .explore-now-btn>a {
    display: inline-block;
}

/* Programs Offered */
.programs-offered {
    background: url(/../yas/images/clp/generic/bg-programs.jpg) no-repeat center;
    padding-top: 70px;
    padding-bottom: 70px;
    background-size: cover;
}

.programs-offered h2 {
    text-align: center;
}

.programs-offered h2>span {
    line-height: 24px;
    font-size: 18px;
    font-weight: 400;
    display: block;
    margin-top: 10px;
}

.programs-offered-slider {
    margin-top: 30px;
}

.programs-offered-slider.slick-slider {
    margin-bottom: 0;
}

.programs-offered-slider.slick-slider .slick-list.draggable, .streams-offered-inner.slick-slider .slick-list.draggable, .why-getmyuni-inner.slick-slider .slick-list.draggable {
    padding-bottom: 70px;
}


.programs-offered-slider.slick-slider .slick-dots, .streams-offered-inner.slick-slider .slick-dots, .why-getmyuni-inner.slick-slider .slick-dots {
    bottom: 20px;
}

.programs-offered-slider.slick-slider .slick-dots li, .streams-offered-inner.slick-slider .slick-dots li, .why-getmyuni-inner.slick-slider .slick-dots li {
    width: 10px;
    height: 10px;
    list-style: none;
}
.programs-offered-slider.slick-slider .slick-dots li>button, .streams-offered-inner.slick-slider .slick-dots li>button, .why-getmyuni-inner.slick-slider .slick-dots li>button {
    width: 10px;
    height: 10px;
    background-color: #999fa3;
    border-radius: 100%;
}

.programs-offered-slider.slick-slider .slick-dots li.slick-active>button{
    background-color: #0c65a6;
}
.streams-offered-inner.slick-slider .slick-dots li.slick-active>button {
    background-color: #F5A623;
}

.programs-offered-slider.slick-slider .slick-dots li>button::before, .streams-offered-inner.slick-slider .slick-dots li>button::before, .why-getmyuni-inner.slick-slider .slick-dots li>button::before {
    display: none;
}

.programs-card {
    width: 100%;
    position: relative;
    margin: 0 10px;
}

.programs-card .program-img {
    border-radius: 12px;
    overflow: hidden;
    width: 100%;
    height: 268px;
    overflow: hidden;
    box-shadow: 1px 103px 57px -54px rgba(0, 0, 0, 0.6);
}

.programs-card .program-card-text {
    background-color: #0D3F64;
    font-weight: 400;
    font-size: 24px;
    line-height: 30px;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border-radius: 12px;
    position: relative;
    margin: -40px auto 0;
    max-width: 90%;
    width: 100%;
}

.programs-offered .fee-detail-button {
    text-align: center;
    margin-top: 10px;
}

/* our partner */
.our-partner-section h2 {
    text-align: center;
}

.our-partner-slider {
    margin-top: 30px;
}

.our-partner-slider.slick-slider .slick-track {
    display: flex;
    align-items: center;
}

.our-partner-slider .college-logo-block {
    margin: 0 20px;
}

.our-partner-slider .college-logo-block img {
    width: 100%;
    height: 68px;
    object-fit: scale-down;
}

.our-partner-section .talk-expert-button {
    text-align: center;
    margin-top: 20px;
}

/* Submit Your Application */
.submit-application-section {
    background: linear-gradient(90deg, rgba(13, 63, 100, 1) 60%, rgba(26, 127, 202, 1) 100%);
    padding: 50px 0;
}

.submit-application-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.submit-application-inner .submit-app-left {
    width: calc(100% - 395px);
    color: white;
    font-size: 28px;
    font-weight: 600;
    line-height: normal;
}

.submit-application-inner .submit-app-left>span {
    background-color: white;
    color: #0D3F64;
}

.submit-application-inner .submit-app-left>br {
    display: none;
}

.submit-application-inner .submit-app-btn {
    width: 305px;
}

.submit-application-inner .submit-app-btn>button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

/* footer */
footer {
    background-color: #212529;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    line-height: 54px;
}

/* for modal */
.overflow-hide {
    overflow: hidden;
}

.modal-overlay-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1;
    display: none;
    align-items: center;
    overflow-y: auto;
}

.modal-body {
    padding: 0;
    margin: auto;
    height: auto;
    max-width: 370px;
    width: 100%;
    background: white;
    border-radius: 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, .4);
    position: relative;
    z-index: 300;
    overflow-x: auto;
    padding: 25px;
    border-radius: 5px;
}

.modal-body .modal-head {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    color: #1E1E1E;
}

.modal-body .modal-head>span {
    font-size: 16px;
    font-weight: 400;
    display: block;
}

.modal-body .form-content {
    margin-top: 20px;
}

.modal-body .form-content .form-field-block {
    position: relative;
}

.modal-body .form-content .form-field-block .form-error {
    position: absolute;
    display: block;
    width: 100%;
    bottom: 0;
    left: 0;
    font-size: 11px;
    color: #F8382A;
}

.modal-body .form-content .field-style {
    border: 1px solid #ADB5BD;
    border-radius: 8px;
    width: 100%;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    padding: 12px 24px;
}

.modal-body .form-content .field-style:focus {
    border-color: #F5A623;
}

.modal-body .form-content .field-style:focus-visible {
    outline: none;
}

.modal-body .form-content .field-style.select-arrow {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url(../images/select-arrow.png) no-repeat right 10px top 21px;
}

.modal-button {
    font-size: 18px;
    font-weight: 600;
    background-color: #EE424F;
    width: 100%;
    border-radius: 8px;
    padding: 10px 0;
    border: none;
    cursor: pointer;
    color: white;
    margin-top: 10px;
}

#modal-close {
    font-size: 28px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    line-height: 10px;
    font-weight: 300;
}

.modal-overlay-box.show-modal {
    display: flex;
}

/* Slider */
.slick-slider {
    position: relative;

    display: block;
    box-sizing: border-box;

    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    -webkit-touch-callout: none;
    -khtml-user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
}

.slick-list {
    position: relative;

    display: block;
    overflow: hidden;

    margin: 0;
    padding: 0;
}

.slick-list:focus {
    outline: none;
}

.slick-list.dragging {
    cursor: pointer;
    cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.slick-track {
    position: relative;
    top: 0;
    left: 0;

    display: block;
    margin-left: auto;
    margin-right: auto;
}

.slick-track:before,
.slick-track:after {
    display: table;

    content: '';
}

.slick-track:after {
    clear: both;
}

.slick-loading .slick-track {
    visibility: hidden;
}

.slick-slide {
    display: none;
    float: left;

    height: 100%;
    min-height: 1px;
}

[dir='rtl'] .slick-slide {
    float: right;
}

.slick-slide img {
    display: block;
}

.slick-slide.slick-loading img {
    display: none;
}

.slick-slide.dragging img {
    pointer-events: none;
}

.slick-initialized .slick-slide {
    display: block;
}

.slick-loading .slick-slide {
    visibility: hidden;
}

.slick-vertical .slick-slide {
    display: block;

    height: auto;

    border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
    display: none;
}

.stick-button {
    padding: 15px 0;
}

.stick-button p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.button-style {
    background: #F5A623;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.button-blue {
    background-color: #0D3F64;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.text-center {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.our-campus-section h2 {
    text-align: center;
    color: #212529;
    margin-bottom: 50px;
}

h2 {
    font-size: 36px;
    font-weight: 600;
}

.our-campus-slider.slick-slider {
    margin-bottom: 0;

}

h2>span {
    font-size: 18px;
    color: #212529;
    font-weight: 400;
    line-height: 24px;
    margin-top: 10px;
    display: block;
}

.programs-offered h2 {
    text-align: center;
}

.section-image .slick-slide img {
    display: block;
    width: 90%;
    object-fit: cover;
    height: 173px;
    position: absolute;
    border-radius: 12px;
    z-index: 1;
    inset: auto 0 auto 0;
    margin: 10px auto;
}

.campus-card {
    width: 100%;
    position: relative;
    margin: 0 10px;
    transition: 0.5s;
    border-radius: 12px;
    background-color: #fff;
    border: 1px solid #F5A623;
    transition: 0.5s;
}

.campus-card .campus-img {
    border-radius: 12px;
    overflow: hidden;
    width: 100%;
    height: 190px;
}

.campus-card .campus-card-text {
    font-weight: 500;
    font-size: 20px;
    line-height: 30px;
    text-align: center;
    padding: 15px 15px;
    color: #212529;
    border-radius: 100px;
    margin: 0 auto;
    max-width: 285px;
    width: 100%;
    /* display: flex; */
    height: 100%;
}

.view-toggle.button-style {
    display: none;
}

.select2-container {
    width: 100% !important;
}

.select2-container .select2-selection--single {
    height: 44px !important
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding: 8px 20px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 7px !important;
    right: 8px !important;
}

.select2-container--default .select2-selection--single {
    border-radius: 8px !important;
}

.hero-banner .form-body-block .banner-form-inner span.select2 {
    position: static;
}
/* media start here */
@media only screen and (max-width:1200px) {
    .why-getmyuni-inner .detail-block .text-block {
        font-size: 17px;
        line-height: 30px;
    }

    .why-getmyuni-inner .detail-block .img-block img {
        width: 68px;
    }

    .programs-card .program-card-text {
        font-size: 18px;
        line-height: 24px;
    }
}

@media only screen and (max-width:992px) {
    .hero-banner .container {
        column-gap: 40px;
    }

    .hero-banner .banner-left-content {
        max-width: 100%;
        width: calc(100% - 360px);
    }

    .hero-banner .banner-left-content h1 {
        font-size: 40px;
    }

    .hero-banner .banner-left-content .top-college>span {
        font-size: 24px;
        width: 100%;
        background-size: contain;
    }

    .hero-banner .banner-right-content {
        max-width: 100%;
        width: 333px;
    }

    .submit-application-inner {
        flex-direction: column;
        gap: 30px;
    }

    .submit-application-inner .submit-app-left {
        width: 90%;
        text-align: center;
    }

    .submit-application-inner .submit-app-left>br {
        display: block;
    }
}

@media only screen and (max-width:767px) {
    h2 {
        font-size: 30px;
    }

    .section-space {
        margin-top: 40px;
    }

    .hero-banner {
        align-items: flex-start;
        height: 595px;
    }

    .hero-banner .container {
        flex-direction: column;
    }

    .hero-banner .banner-left-content {
        width: 100%;
        padding: 40px 10px 0;
    }

    .hero-banner .banner-left-content h1 {
        text-align: center;
        font-size: 36px;
        line-height: 40px;
    }

    .hero-banner .banner-left-content .top-college {
        width: 325px;
        margin: 5px auto 0;
    }

    .hero-banner .banner-left-content .top-college>span {
        font-size: 20px;
        height: 45px;
        line-height: 40px;
        text-align: center;
    }

    .hero-banner .banner-left-content .jobready-block {
        width: 85%;
        margin: 20px auto;
    }

    .hero-banner .banner-left-content .jobready-block .jobready-head {
        font-size: 20px;
    }

    .hero-banner .banner-left-content .jobready-block>ul>li {
        line-height: 22px;
        margin-bottom: 7px;
    }

    .hero-banner .banner-right-content {
        width: 100%;
    }

    section.why-getmyuni.section-space {
        margin-top: 160px;
    }

    .why-getmyuni-inner {
        flex-direction: column;
        row-gap: 40px;
    }

    .why-getmyuni-inner .detail-block {
        max-width: 100%;
    }

    .programs-offered.section-space {
        margin-top: 0;
        padding-top: 40px;
        padding-bottom: 40px;
    }

    .programs-offered-slider .slick-list {
        padding: 0 10% 0 0;
    }

    .submit-application-section {
        padding: 40px 0;
    }

    .submit-application-inner .submit-app-left {
        font-size: 24px;
    }
}