.blueBgDiv {
  height: 0px !important;
}

/*****************************************************************/
/********************* Banner Section ****************************/
/*****************************************************************/

.gisSpriteIcon {
  display: inline-block;
  background: url("../../images/master-sprite-gis.webp");
  text-align: left;
  overflow: hidden;
}

.rightArrowIcon {
  background-position: -537px -554px;
  width: 14px;
  height: 14px;
}

.container-fluid {
  padding: 0;
}

.gis__immigrationBanner {
  height: 600px;
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.6) 100%,
      rgba(196, 196, 196, 0.4) 0%,
      rgba(196, 196, 196, 0.4) 100%),
    url(../../images/gis_banner3.webp);
  background-size: 100% 150%;
  background-repeat: no-repeat;
}

.gis__immigrationBanner.canadaBanner {
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.6) 100%,
      rgba(196, 196, 196, 0.4) 0%,
      rgba(196, 196, 196, 0.4) 100%),
    url(../../images/gis_banner3.webp);
}

.gis__immigrationBanner.australiaBanner {
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.6) 100%,
      rgba(196, 196, 196, 0.4) 0%,
      rgba(196, 196, 196, 0.4) 100%),
    url(../../images/banner-australia.png);
}

.gis__immigrationBanner.austriaBanner {
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.6) 100%,
      rgba(196, 196, 196, 0.4) 0%,
      rgba(196, 196, 196, 0.4) 100%),
    url(../../images/banner-austria.png);
}

.gis__immigrationBanner.germanyBanner {
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.6) 100%,
      rgba(196, 196, 196, 0.4) 0%,
      rgba(196, 196, 196, 0.4) 100%),
    url(../../images/banner-germany.png);
}

.gis__immigrationBanner.swedenBanner {
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.6) 100%,
      rgba(196, 196, 196, 0.4) 0%,
      rgba(196, 196, 196, 0.4) 100%),
    url(../../images/banner-sweeden.png);
}

.gis__immigrationBanner.uaeBanner {
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.6) 100%,
      rgba(196, 196, 196, 0.4) 0%,
      rgba(196, 196, 196, 0.4) 100%),
    url(../../images/banner-sweeden.png);
}

.gis__immigrationBanner .container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
}

.gis__immigrationBanner__title {
  max-width: 938px;/*864px;*/
  font-size: 52px;
  /*55.36px;*/
  font-weight: 300;
  line-height: 66.44px;
  text-align: left;
  color: #fff;
}

.gis__immigrationBanner__subTitle {
  margin: 20px 0;
  font-size: 20px;
  /*25px;*/
  font-weight: 400;
  line-height: 33px;
  letter-spacing: -0.02em;
  text-align: left;
  color: #fff;
}

.gis__immigrationBanner__subTitleEnd {
  padding: 6px;
  font-size: 20.09px;
  font-weight: 400;
  line-height: 25.83px;
  text-align: center;
  color: #fff;
  background: #0966c2;
}

.gis__immigrationBanner__getBtn {
  margin-top: 40px;
  width: 136px;
  height: 41px;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 700;
  line-height: 27px;
  text-align: center;
  outline: none;
  border: none;
  color: #fff;
  background: #ff4e53;
}

.gispages {
  background: #fff;
}

.spriteIcon__2 {
  display: inline-block !important;
  background: url(../../images/master-sprite-gis.webp);
  text-align: left;
  overflow: hidden;
}

.gis__immigrationBanner__title span {
  width: 92px;
  height: 59px;
  transform: scale(0.9);
  position: relative;
  top: 5px;
}

.canadaFlag {
  background-position: -580px -230px;
}

.australiaFlag {
  background-position: -457px -230px;
}

.germanyFlag {
  background-position: -220px -630px;
}

.austriaFlag {
  background-position: -339px -226px;
}

.swedenFlag {
  background-position: -220px -547px;
}

.uaeFlag {
  background-position: -223px -224px;
}

.canadaEligibilityIcon {
  background-position: -430px -607px;
  width: 72px;
  height: 72px;
}

.australiaEligibilityIcon {
  background-position: -336px -606px;
  width: 72px;
  height: 72px;
}

.gis__economyIcon {
  background-position: -20px -559px;
  width: 63px;
  height: 59px;
}

.gis__employmentIcon {
  background-position: -19px -645px;
  width: 57px;
  height: 60px;
}

.gis__healthCareIcon {
  background-position: -559px -705px;
  width: 59px;
  height: 59px;
}

.gis__educationIcon {
  background-position: -117px -442px;
  width: 55px;
  height: 57px;
}

.gis__crimeRateIcon {
  background-position: -117px -358px;
  width: 64px;
  height: 59px;
}

.gis__cultureIcon {
  background-position: -220px -318px;
  width: 59px;
  height: 60px;
}

.gis__teleIcon {
  background-position: -349px -555px;
  width: 15px;
  height: 15px;
  margin-right: 6px;
}

.gis__emailIcon {
  background-position: -686px -640px;
  width: 15px;
  height: 11px;
  margin-right: 6px;
}

.gis__section__head {
  padding-top: 90px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gis__section__head h2 {
  font-size: 30px;
  font-weight: 600;
  line-height: 38px;
  text-align: center;
  margin-bottom: 15px;
}

.gis__section__head p {
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  text-align: center;
  max-width: 1046px;
}

.gis__countryCard__list {
  margin-top: 40px;
  gap: 22px;
}

.gis__countryCard__list .gis__countryCard {
  max-width: 183px;
  height: 205px;
  border-radius: 10px;
  border: 1px solid #dbdbdb;
  background: #ffffff;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.gis__countryCard__list .gis__countryCard .gis__countryCard__iconContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gis__countryCard__list .gis__countryCard p {
  padding: 9px;
  background: rgba(255, 78, 83, 0.0392156863);
  color: #282828;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
  height: 85px;
}

.gis__section__banner {
  padding: 25px 25px 25px 36px;
  background: #f6f6f6;
  border: 1px solid #e7e7e7;
  margin-top: 72px;
  position: relative;
}

.gis__section__banner p,
.gis__section__banner ul li,
.gis__section__banner table td,
.gis__section__banner table th {
  font-size: 18px;
  font-weight: 400;
  line-height: 26px;
  color: #282828;
  position: relative;
}

.gis__section__banner table th {
  font-weight: 600;
  background-color: white;
}

.gis__section__banner table td {
  background-color: #fff;
}

.gis__section__banner::before {
  position: absolute;
  content: " ";
  width: 6px;
  height: 100px;
  background: #0966c2;
  border-radius: 1px;
  left: 18px;
  top: 31px;
}

.gis__section__banner .table-responsive {
  width: 100%;
  margin-top: 15px;
}

.gis__section__bannerContent {
  max-height: 100px;
  overflow-y: hidden;
  transition: max-height 0.3s ease;
}

.gis__section__bannerContent.expanded {
  max-height: none;
}

.gis__section__bannerContent h2 {
  margin-top: 20px;
}

.gis__section__bannerContent h3 {
  margin-top: 15px;
}

.gis__section__bannerReadMore {
  display: flex;
  align-items: center;
  justify-content: center;
}

.gis__readMoreBtn {
  border: none;
  outline: none;
  background-color: transparent;
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
  color: #ff615d;
  margin-top: 10px;
}

.gis__salaryContainer {
  justify-content: center;
  flex-wrap: wrap;
}

.gis__salaryContainer .gis__salaryContainer__dropdown {
  margin-top: 30px;
  max-width: 700px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  padding: 30px;
  display: flex;
  width: 100%;
  gap: 17px;
}

.gis__salaryContainer .gis__salaryContainer__dropdown .checkSalarySelect {
  height: 48px;
  max-width: 430px;
  border-radius: 4px;
  border: solid 1px #eaeaea;
  background-color: #fafbfc;
  padding: 9px 16px;
  background-size: 16px 8px;
}

.gis__salaryContainer .gis__salaryContainer__dropdown .salaryFilterButton {
  height: 48px;
  width: 190px;
  border-radius: 4px;
  background-color: #ff615d;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.5;
  color: #fff;
  border: none;
}

.gis__salaryContainer #gis__salaryContainer__chart {
  flex-basis: 100%;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  margin-top: 30px;
}

.gis__tabber__immigration ul {
  margin: 0;
  padding: 0;
  text-align: center;
}

.gis__tabber__immigration ul li {
  list-style-type: none;
  border-radius: 30px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  font-size: 16px;
  line-height: 13px;
  border: solid 1px #eaeaea;
  display: inline-block;
  padding: 11px 24px;
  margin-right: 5px;
  background-color: #fff;
  cursor: pointer;
  max-height: 36px;
}

.gis__tabber__immigration ul li.current {
  background: #ff615d;
  color: #fff;
  font-weight: 600;
}

.pathRow {
  border: 1px solid #d8d8d8;
  border-radius: 8px;
}

.pathRow .pathAccordion {
  background-color: #fff;
  cursor: pointer;
  width: 100%;
  text-align: left;
  border: none;
  outline: none;
  transition: 0.4s;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.5;
  color: #282828;
  padding: 18px 20px;
}

.pathRow .active {
  font-weight: 600;
  border: 1px solid #ff4e53;
  background-color: #f5f5f5;
}

.pathRow .active+.pathPanel {
  background-color: #f5f5f5;
}

.pathRow .pathAccordion:after {
  content: "";
  background-image: url(../../images/master-sprite-gis.webp);
  background-position: -598px -407px;
  width: 14px;
  height: 9px;
  float: right;
  margin-left: 5px;
  margin-top: 10px;
  transform: scale(1.1);
}

.pathRow .active:after {
  transform: scale(1.1) rotate(180deg);
}

.pathRow .pathPanel {
  padding-left: 18px;
  background-color: white;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
  background-color: #fff;
  padding-right: 77px;
  border-bottom: 1px solid #d8d8d8;
}

.pathRow .pathPanel:last-child {
  border-bottom: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.pathRow .pathPanel .pathAccordianContent {
  margin-top: 13px;
  margin-bottom: 24px;
}

.pathPanel.show {
  max-height: fit-content;
  border-top: 1px solid rgb(216, 216, 216);
  background-color: #f5f5f5;
}

.pathRow .pathPanel .pathAccordianContent p {
  font-size: 16px;
  font-weight: normal;
  line-height: 1.5;
  color: #282828;
  margin-bottom: 8px;
}

.pathRow .pathPanel .pathAccordianContent p:last-child {
  margin-bottom: 0px;
}

.pathRow button:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.pathRow button:last-of-type {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.gis__tabber__immigration ul li {
  box-shadow: none;
  border: 1px solid #ff615d;
}

.gis__tabber__immigrationContent {
  margin-top: 30px;
}

.gis__hexagonSection {
  padding: 50px 0 100px 0;
  position: relative;
}

.gis__hexagonSection::before {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.4;
  content: " ";
  width: 100%;
  height: 100%;
  display: block;
  background-image: url(../../images/hexagon__bg.png);
  background-repeat: no-repeat;
  background-size: cover;
}

.gis__hexagonSection .gis__verticalMidAlign {
  display: flex;
  align-items: center;
}

.gis__hexagonSection .gis__section__head {
  padding: 0;
}

.gis__hexagonSection .gis__section__head h2 {
  text-align: left;
  font-weight: 500;
}

.gis__hexagonSection .gis__section__head p {
  text-align: left;
  color: #595656;
}

.gis__hexagonSection .gis__section__head .gis__hexagonSection__ctaButton {
  align-self: flex-start;
  margin-top: 30px;
  background-color: #ff4e53;
  height: 52px;
  min-width: 251px;
  border: none;
  padding: 6px 24px 6px 24px;
  border-radius: 4px;
  font-size: 18px;
  color: #fff;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
}

.hexagonContainer {
  width: 100%;
  background-color: transparent;
  padding: 30px 22px;
}

.hexagon-gallery {
  max-width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-auto-rows: 84px;
  grid-gap: 20px;
  margin-top: 30px;
  row-gap: 80px;
}

.hex {
  position: relative;
  width: 170px;
  height: 200px;
  background-color: #d8d8d8;
  -webkit-clip-path: polygon(50% 0%,
      100% 25%,
      100% 75%,
      50% 100%,
      0% 75%,
      0% 25%);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.hex:first-child {
  grid-row-start: 1;
  grid-column: 2 / span 2;
}

.hex:first-child .hexInside {
  background-color: #f3f3f3;
}

.hex:nth-child(2) {
  grid-row-start: 1;
  grid-column: 4 / span 2;
}

.hex:nth-child(2) .hexInside {
  background-color: #e8f4ff;
}

.hex:nth-child(3) {
  grid-row-start: 2;
  grid-column: 1 / span 2;
}

.hex:nth-child(3) .hexInside {
  background-color: #e8f4ff;
}

.hex:nth-child(4) {
  grid-row-start: 2;
  grid-column: 3 / span 2;
  background-color: #fff;
}

.hex:nth-child(4) .hexInside {
  border: none;
  background-color: #fff;
  background-size: cover;
  background-position: center;
}

.hex:nth-child(4) .hexInside.canadaIcon {
  background-image: url(../../images/canada_hexagon.png);
}

.hex:nth-child(4) .hexInside.australiaIcon {
  background-image: url(../../images/australia_hexagon.png);
}

.hex:nth-child(4) .hexInside.austriaIcon {
  background-image: url(../../images/austria_hexagon.png);
}

.hex:nth-child(4) .hexInside.germanyIcon {
  background-image: url(../../images/germany_hexagon.png);
}

.hex:nth-child(4) .hexInside.swedenIcon {
  background-image: url(../../images/sweeden_hexagon.png);
}

.hex:nth-child(4) .hexInside.uaeIcon {
  background-image: url(../../images/uae-hexagon.png);
}

.hex:nth-child(5) {
  grid-row-start: 2;
  grid-column: 5 / span 2;
}

.hex:nth-child(5) .hexInside {
  background-color: #f3f3f3;
}

.hex:nth-child(6) {
  grid-row-start: 3;
  grid-column: 2 / span 2;
}

.hex:nth-child(6) .hexInside {
  background-color: #f3f3f3;
}

.hex:nth-child(7) {
  grid-row-start: 3;
  grid-column: 4 / span 2;
}

.hex:nth-child(7) .hexInside {
  background-color: #e8f4ff;
}

.hexInside {
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: white;
  -webkit-clip-path: polygon(50% 0%,
      100% 25%,
      100% 75%,
      50% 100%,
      0% 75%,
      0% 25%);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.hexInside p {
  font-size: 13px;
  font-weight: 600;
  line-height: 1.4;
  text-align: center;
  color: #282828;
}

.hexProgresssIcon {
  background-position: -26px -735px;
  width: 46px;
  height: 48px;
  margin-bottom: 15px;
}

.hexServiceIcon {
  background-position: -218px -725px;
  width: 48px;
  height: 48px;
  margin-bottom: 15px;
}

.hexExpertIcon {
  background-position: -121px -603px;
  width: 51px;
  height: 51px;
  margin-bottom: 15px;
}

.hexSupportIcon {
  background-position: -123px -530px;
  width: 48px;
  height: 48px;
  margin-bottom: 15px;
}

.hexRiskIcon {
  background-position: -123px -292px;
  width: 50px;
  height: 47px;
  margin-bottom: 15px;
}

.hexExamIcon {
  background-position: -23px -428px;
  width: 51px;
  height: 49px;
  margin-bottom: 15px;
}

.gis__faq {
  background: linear-gradient(0deg, #f3f2ef, #f3f2ef),
    linear-gradient(0deg, #f3f2ef, #f3f2ef);
  padding-bottom: 50px;
}

.gis__faq .pathRow {
  margin-top: 50px;
}

/*****************************************************************/
/************************* Journey Section ***********************/
/*****************************************************************/

.profileIcon {
  background-position: -441px -731px;
  width: 24px;
  height: 28px;
}

.languageIcon {
  background-position: -680px -728px;
  width: 25px;
  height: 25px;
}

.educationIcon {
  background-position: -539px -470px;
  width: 28px;
  height: 28px;
}

.prIcon {
  background-position: -308px -721px;
  width: 28px;
  height: 28px;
}

.invitationIcon {
  background-position: -372px -725px;
  width: 29px;
  height: 28px;
}

.gis__journeyCtn {
  padding: 80px 0;
  margin-top: 80px;
  background: #0966C20A;
}

.gis__immigrationService__gridView {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.gis__immigrationService__gridJourneyItem:first-child {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: start;
}

.gis__immigrationService__gridJourneyItem:first-child h2 {
  font-size: 30px;
  font-weight: 700;
  line-height: 38px;
  letter-spacing: 0.30000001192092896px;
  text-align: left;
  color: #414141;
}

.gis__immigrationService__gridJourneyItem:first-child p {
  margin-top: 10px;
  font-size: 19px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  color: #414141;
}

.gis__immigrationService__gridJourneyItem:not(:first-child) {
  width: 100%;
  height: 234px;
  padding: 20px;
  display: flex;
  justify-content: start;
  flex-direction: column;
  align-items: start;
  border-radius: 16px;
  background-color: #fff;
  box-shadow: 0px 0px 5.28px 0px #0000000f;
}

.gis__immigrationService__gridJourneyItem__icon {
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #0966c217;
}

.gis__immigrationService__gridJourneyItem h2 {
  margin-top: 16px;
  font-size: 20px;
  font-weight: 700;
  line-height: 14.09px;
  letter-spacing: -0.02em;
  text-align: left;
  color: #000;
  line-height: 1.2;
}

.gis__immigrationService__gridJourneyItem p {
  font-size: 15px;
  font-weight: 400;
  line-height: 19.1px;
  text-align: left;
  margin-top: 10px;
  color: #989898;
}

/*****************************************************************/
/*********************** Fly Abroad 2 Section ********************/
/*****************************************************************/

.videoPlayIcon {
  background-position: -322px -393px;
  width: 60px;
  height: 60px;
}

.gis__abroadCtn {
  padding: 80px 0;
  background: #0966c20d;
}

.gis__abroadFlexBox {
  margin-top: 40px;
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

.gis__abroadFlexItem {
  width: 276px;
  height: 271px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #eaeaea;
  overflow: hidden;
}

.gis__abroadFlexItem__img {
  position: relative;
  width: 100%;
  height: 207px;
}

.gis__abroadFlexItem__img button {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 60px;
  width: 60px;
  outline: none;
  border: none;
  border-radius: 50%;
  background-color: transparent;
  transform: translate(-50%, -50%);
}

.gis__abroadFlexItem__img img {
  width: 100%;
  height: 100%;
}

.gis__abroadFlexItem__body {
  padding: 20px;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.3px;
  text-align: left;
}

/*****************************************************************/
/******************* Immigration News Section ********************/
/*****************************************************************/

.gis__immigrationNewsCtn {
  padding: 80px 0;
  background-image: url(../../images/gis_newsBg.webp);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 0% 100%;
}

.gis__immigrationNewsCtn .gis__immigrationService__gridView {
  margin-top: 40px;
}

.gis__immigrationService__newsItem {
  width: 100%;
  height: 374px;
  border-radius: 28px;
  border: 1px solid #efefef;
  box-shadow: 0px 0px 9px 0px #0000001a;
  overflow: hidden;
}

.gis__newsItem__body {
  padding: 20px;
}

.gis__newsItem__body h3,
.gis__immigrationTipsItem__body h3 {
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  text-align: left;
  color: #414141;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 10px;
}

.gis__newsItem__body h4,
.gis__immigrationTipsItem__body h4,
.gis__newsItem__body p,
.gis__immigrationTipsItem__body p {
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  color: #989898;
}

.gis__newsItem__body p {
  font-weight: 400;
  margin-top: 5px;
}

.gis__immigrationViewBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;
}

/*****************************************************************/
/******************* Immigration Tips Section ********************/
/*****************************************************************/

.gis__immigrationTipsCtn {
  padding: 80px 0;
  background: #f5f9fd;
}

.gis__immigrationTips__flex {
  margin-top: 40px;
  display: flex;
  gap: 28px;
  align-items: center;
  justify-content: center;
}

.gis__immigrationTips__flexItem {
  width: 100%;
  height: 302px;
  border-radius: 13px;
  background-color: #fff;
  border: 1px solid #efefef;
  overflow: hidden;
}

.gis__immigrationTipsItem__img {
  width: 100%;
  height: 174px;
  overflow: hidden;
  background-color: #989898;
}

.gis__immigrationTipsItem__img img {
  height: 100%;
  width: 100%;
}

.gis__immigrationTipsItem__body {
  padding: 16px;
}

.gis__immigrationTipsItem__body h3 {
  margin-bottom: 2px;
}

.gis__immigrationTipsItem__body h4,
.gis__immigrationTipsItem__body p {
  font-size: 14.5px;
}

.gis__immigrationTipsItem__body p {
  font-weight: 400;
}

.gis__immigrationTitle {
  font-size: 30px;
  font-weight: 700;
  line-height: 38px;
  letter-spacing: 0.3px;
  text-align: center;
  color: #414141;
}

.gis__immigrationSubTitle {
  margin-top: 10px;
  font-size: 20px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
  color: #414141;
}
.gis__immigrationViewBtn a {
  width: 130px;
  height: 44px;
  font-size: 14px;
  font-weight: 700;
  line-height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  background: #ff4e53;
  text-decoration: none;
}

@media (max-width: 1023px) {
  .blueBgDiv {
    height: 0px !important;
  }

  /*****************************************************************/
  /********************* Banner Section ****************************/
  /*****************************************************************/

  .container-fluid {
    padding: 0;
  }

  .gis__immigrationBanner {
    height: 400px;
  }

  .gis__immigrationBanner__title {
    max-width: unset;
    font-size: 28px;
    line-height: 33px;
  }

  .gis__immigrationBanner__subTitle {
    margin: 12px 0;
    font-size: 18px;
    line-height: 24px;
  }

  .gis__immigrationBanner__subTitleEnd {
    padding: 4px;
    font-size: 16px;
    line-height: 25px;
  }

  /************* Immigration banner section Country page*****************/
  .gis__immigrationBanner__title span {
    width: 63px;
    height: 30px;
  }

  .canadaFlag {
    background-position: -516px -401px
  }

  .australiaFlag {
    background-position: -217px -410px;
  }

  .swedenFlag {
    background-position: -146px -816px;
  }

  .germanyFlag {
    background-position: -20px -818px;
  }

  .austriaFlag {
    background-position: -213px -816px;
  }

  .uaeFlag {
    background-position: -83px -817px;
  }

  .gis__section__head {
    padding-top: 40px;
  }

  .gis__section__head h2 {
    font-size: 24px;
    font-weight: 600;
    line-height: 36px;
    margin-bottom: 10px;
  }

  .gis__section__head p {
    font-size: 18px;
    font-weight: 400;
    line-height: 26px;
  }

  .gis__countryCard__list {
    margin-top: 30px;
    margin-left: 0;
    gap: 10px;
    margin-right: 0;
  }

  .gis__countryCard__list .gis__countryCard {
    flex: 1;
    min-width: 47%;
    height: 180px;
  }

  .gis__countryCard__list .gis__countryCard p {
    font-size: 14px;
  }

  .gis__section__banner {
    margin-top: 38px;
    padding: 45px 28px 38px 36px;
  }

  .gis__section__banner::before {
    top: 47px
  }

  .gis__section__banner p,
  .gis__section__banner ul li,
  .gis__section__banner table td,
  .gis__section__banner table th {
    font-size: 16px;
    line-height: 1.5;
    color: #282828;
  }

  .gis__salaryContainer {
    margin: 0;
  }

  .gis__salaryContainer .gis__salaryContainer__dropdown {
    padding: 20px 14px;
  }

  .gis__salaryContainer .salaryFilterButton {
    height: 48px;
    width: 100%;
    border-radius: 4px;
    background-color: #ff615d;
    font-size: 16px;
    font-weight: bold;
    line-height: 1.5;
    color: #fff;
    border: none;
    margin-top: 16px;
    max-width: 300px;
  }

  .gis__tabber__immigration {
    width: 100%;
    overflow-x: auto;
  }

  .gis__tabber__immigration ul {
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    display: flex;
    gap: 8px;
  }

  .gis__tabber__immigration ul li {
    padding: 6px;
    height: 36px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin: 0;
  }

  .gis__hexagonSection .gis__section__head .gis__hexagonSection__ctaButton {
    margin-top: 38px;
    width: 100%;
  }

  .hexagonContainer {
    padding: 0;
  }

  .hexagon-gallery {
    row-gap: 20px;
  }

  .hexagon-gallery .hex {
    width: 100px;
    height: 115px;
  }

  .hexagon-gallery .hex .hexInside .spriteIcon__2 {
    margin: 0 !important;
    transform: scale(0.8);
  }

  .hexagon-gallery .hex .hexInside p {
    font-size: 10px;
  }

  .gis__faq {
    padding-bottom: 38px;
  }

  .gis__faq .pathRow {
    margin-top: 30px;
  }



  .gis__tabber__immigration {
    overflow: auto;
  }

  /*****************************************************************/
  /************************* Journey Section ***********************/
  /*****************************************************************/

  .gis__journeyCtn {
    padding: 40px 0;
    margin-top: 40px;
  }

  .gis__immigrationService__gridView {
    grid-template-columns: repeat(1, 1fr);
    gap: 30px;
  }

  .gis__journeyCtn .gis__immigrationService__gridView {
    gap: 24px;
  }

  .gis__immigrationService__gridJourneyItem:first-child {
    align-items: center;
  }

  .gis__immigrationService__gridJourneyItem:first-child h2 {
    font-size: 24px;
    text-align: center;
  }

  .gis__immigrationService__gridJourneyItem:first-child p {
    margin-top: 10px;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    color: #414141;
  }

  .gis__immigrationService__gridJourneyItem:not(:first-child) {
    height: 196px;
    padding: 16px;
  }

  .gis__immigrationService__gridJourneyItem__icon {
    width: 38px;
    height: 38px;
  }

  .gis__immigrationService__gridJourneyItem h2 {
    margin-top: 14px;
    font-size: 16px;
    line-height: 11.09px;
  }

  .gis__immigrationService__gridJourneyItem p {
    font-size: 12px;
    line-height: 16.1px;
  }

  /*****************************************************************/
  /*********************** Fly Abroad 2 Section ********************/
  /*****************************************************************/

  .gis__abroadCtn {
    padding: 40px 0;
  }

  .gis__abroadFlexBox {
    margin-top: 40px;
    justify-content: start;
    width: 100%;
    overflow-x: auto;
  }

  .gis__abroadFlexItem {
    flex-shrink: 0;
  }

  /*****************************************************************/
  /******************* Immigration News Section ********************/
  /*****************************************************************/

  .gis__immigrationNewsCtn {
    padding: 40px 0;
  }

  .gis__immigrationNewsCtn .gis__immigrationService__gridView {
    display: flex;
    margin-top: 40px;
    width: 100%;
    overflow-x: auto;
    gap: 24px;
  }

  .gis__immigrationService__newsItem {
    flex-shrink: 0;
    width: 266px;
    height: 276px;
  }

  .gis__newsItem__body {
    padding: 14px;
  }

  .gis__newsItem__body h3,
  .gis__immigrationTipsItem__body h3 {
    font-size: 15px;
    line-height: 17px;
  }

  .gis__newsItem__body h4,
  .gis__immigrationTipsItem__body h4,
  .gis__newsItem__body p,
  .gis__immigrationTipsItem__body p {
    font-size: 14px;
    line-height: 18px;
  }

  /*****************************************************************/
  /******************* Immigration Tips Section ********************/
  /*****************************************************************/

  .gis__immigrationTipsCtn {
    padding: 40px 0;
    background: #f5f9fd;
  }

  .gis__immigrationTips__flex {
    margin-top: 40px;
    justify-content: start;
    width: 100%;
    overflow-x: auto;
    gap: 24px;
  }

  .gis__immigrationTips__flexItem {
    flex-shrink: 0;
    width: 270px;
    height: 280px;
    border-radius: 13px;
    background-color: #fff;
    border: 1px solid #efefef;
    overflow: hidden;
  }

  .gis__immigrationTipsItem__body h3 {
    margin-bottom: 2px;
  }

  .gis__immigrationTitle {
    font-size: 24px;
  }

  .gis__immigrationSubTitle {
    font-size: 16px;
    line-height: 20px;
  }
}