* {
  letter-spacing: normal;
  color: var(--primary-font-color);
}

.table-responsive {
  margin-bottom: 10px;
  text-align: center;
}

a strong {
  color: var(--anchor-textclr);
}

table td span {
  color: var(--primary-font-color) !important;
}

.breadcrumbDiv ul li a {
  font-weight: 500;
}

.p-0 {
  padding: 0;
}

.pb-0 {
  padding-bottom: 0;
}

.text-center {
  text-align: center;
}

.pageInfo {
  max-height: 500px;
}

.faq_section.pageInfo {
  max-height: 400px;
}

table td {
  border-bottom: 0.2px solid #d8d8d8;
}

table tr td:last-child {
  border-right: 0.2px solid #eaeaea;
}

.boardsheroSection {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
  background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
  padding: 28px;
  margin-bottom: 20px;
  margin-top: 10px;
}

.boardsheroSection h1 {
  font-size: 24px;
  line-height: 38px;
  font-weight: 400;
  padding-bottom: 20px;
}

.boardsheroSection button.primaryBtn {
  padding: 5px 12px;
}

.boardsheroSection input#autoComplete {
  max-width: 480px;
  padding: 10px;
  padding-left: 40px;
  font-size: 14px;
  line-height: 24px;
  width: 100%;
  border-radius: 3px;
  border: var(--border-line);
  background: url(../../../yas/images/search-icon.png) no-repeat;
  background-position: 15px 48%;
  color: var(--primary-font-color);
}

.boardsheroSection input#autoComplete::-moz-placeholder {
  color: #989898;
  opacity: 1;
  font-weight: 500;
}

.boardsheroSection input#autoComplete:-ms-input-placeholder {
  color: #989898;
  opacity: 1;
  font-weight: 500;
}

.boardsheroSection input#autoComplete::placeholder {
  color: #989898;
  opacity: 1;
  font-weight: 500;
}

.boardsheroSection input#autoComplete:focus {
  background-size: auto;
  outline: none;
}

.boardsheroSection input#autoComplete:focus::-webkit-input-placeholder {
  padding-left: 0;
  font-size: 14px;
}

.boardsheroSection #autoComplete_list {
  max-width: 480px;
  border-radius: 0;
  margin: 0;
  position: absolute;
  max-height: 205px;
  overflow: auto;
  width: 100%;
  background: #fff;
  z-index: 3;
  left: 0;
  top: 46px;
}

.boardsheroSection #autoComplete_list li.no_result {
  list-style-type: none;
}

.boardsheroSection .searchBar {
  position: relative;
}

.boardsheroSection .searchBar input {
  margin-bottom: 0;
}

.boardsheroSection .autoComplete_list {
  padding: 0;
  margin: 0;
  max-width: 480px;
  border: var(--border-line);
}

.boardsheroSection .autoComplete_list li {
  list-style-type: none;
  padding: 3px 5px;
  cursor: pointer;
}

.boardsheroSection .autoComplete_list li:hover {
  background: rgba(0, 0, 0, 0.15);
}

.boardsheroSection .autoComplete_list a {
  text-decoration: none;
}

.boardsheroSection .col-md-5 {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.pageData {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.pageData h2 {
  font-size: 18px;
  line-height: 28px;
  padding: 8px 20px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  text-transform: uppercase;
  position: relative;
}

.pageData h2 {
  text-transform: none;
}

.pageData h2 a {
  font-size: 14px;
  line-height: 24px;
  color: var(--color-red);
  font-weight: 500;
  text-transform: capitalize;
}

.pageData caption {
  padding-top: 10px;
  text-align: center;
  caption-side: bottom;
  margin-bottom: 0;
  font-size: 14px;
  color: var(--primary-font-color);
}

.pageData img {
  margin: 0 auto;
  display: block;
}

.pageData p,
.pageData li,
.pageData a {
  font-size: 15px;
  line-height: 26px;
}

.pageData p,
.pageData p span {
  color: var(--primary-font-color);
  padding-bottom: 15px;
}

.pageData h3 {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}

.pageData h4 {
  padding-bottom: 10px;
  line-height: 24px;
  font-weight: 500;
}

.pageData button {
  background: var(--color-red);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-white);
  padding: 8px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  transition: 0.2s ease;
  outline: none;
  margin-bottom: 10px;
}

.pageData ul li {
  position: relative;
  list-style-type: none;
}

.pageData ul li:before {
  content: "";
  background: url(/yas/images/master_sprite.webp);
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

.boardsList {
  margin: 0;
}

.boardsListCard {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
  flex-basis: 32.2%;
  margin-right: 20px;
}

.boardsListCard:nth-of-type(3n) {
  margin-right: 0;
}

.boardsListCard .row {
  margin: 0 -20px;
  align-items: center;
  border-bottom: var(--border-line);
  padding: 0 20px;
  padding-bottom: 20px;
}

.boardsListCard .boardsLogo {
  width: 64px;
  height: 64px;
  margin-right: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
}

.boardsListCard .boardsLogo img {
  display: block;
}

.boardsListCard h3 {
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
  flex-basis: calc(100% - 64px - 16px);
}

.boardsListCard h3 a {
  color: var(--primary-font-color);
}

.boardsListCard h3 a:hover {
  color: var(--anchor-textclr);
}

.boardsListCard ul {
  padding: 0 20px;
  margin-bottom: 0;
  max-height: 96px;
  overflow: auto;
}

.boardsListCard ul::-webkit-scrollbar {
  width: 5px;
}

.boardsListCard ul::-webkit-scrollbar-thumb {
  background: #d8d8d8;
}

.boardsListCard ul::-webkit-scrollbar-track {
  background: #f1f3f4;
}

.boardsListCard ul li {
  font-size: 15px;
  line-height: 32px;
}

.boardsListCard ul li a {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.customSlider {
  position: relative;
}

.customSlider .scrollRight {
  right: -20px;
}

.customSlider .scrollLeft {
  top: 50%;
  left: -20px;
}

.customSlider .row {
  margin: 0;
}

.customSlider .customSliderCards {
  display: block;
  white-space: nowrap;
  overflow: auto;
}

.customSlider .customSliderCards::-webkit-scrollbar {
  display: none;
}

.customSlider .sliderCardInfo {
  border-radius: 4px;
  border: var(--border-line);
  margin-right: 14px;
}

.customSlider .sliderCardInfo:last-child {
  margin-right: 0;
}

.customSlider .sliderCardInfo img {
  display: block;
  margin: 0;
  height: 207px;
  width: 100%;
}

.customSlider .sliderCardInfo .clgLogo {
  max-width: 72px;
  height: 72px;
  display: block;
  margin-right: 20px;
}

.customSlider .sliderCardInfo .textDiv {
  padding: 20px;
}

.customSlider .sliderCardInfo .textDiv .collegeLogo {
  width: 56px;
  height: 56px;
  margin: 0;
  border-radius: 4px;
  margin-top: -60px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 5px;
  display: block;
}

.customSlider .sliderCardInfo p {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-semibold);
  padding-bottom: 0;
  white-space: initial;
}

.customSlider .sliderCardInfo p span {
  color: #989898;
  font-weight: 400;
  font-size: 13px;
}

.customSlider .sliderCardInfo p:first-child {
  font-size: 16px;
  line-height: 24px;
  padding-bottom: 2px;
}

.customSlider .sliderCardInfo .widgetCardHeading {
  font-size: 14px;
  padding-bottom: 0;
  min-height: 48px;
  margin-bottom: 5px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
}

.customSlider .sliderCardInfo .subText {
  color: #989898;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  padding: 0;
  position: relative;
}

.customSlider .sliderCardInfo .subText .spriteIcon {
  position: initial;
  vertical-align: middle;
}

.customSlider .sliderCardInfo a {
  text-decoration: none;
}

.customSlider .sliderCardInfo a:hover .widgetCardHeading {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.customSlider .sliderCardInfo a:hover .subText {
  text-decoration: none;
}

.customSlider .sliderCardInfo img {
  cursor: pointer;
}

.four-cardDisplay .sliderCardInfo {
  width: 23.8%;
  display: inline-block;
  white-space: initial;
}

.four-cardDisplay .sliderCardInfo:nth-of-type(4n) {
  margin-right: 0;
}

.four-cardDisplay .sliderCardInfo img {
  display: block;
  height: 207px;
  width: 100%;
}

.displayCard:hover .widgetCardHeading {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

a {
  text-decoration: none;
  color: var(--anchor-textclr);
}

.pageData .btn,
.pageData button.btn {
  color: var(--primary-font-color);
  border: var(--border-line);
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 13px;
  margin-right: 6px;
  margin-bottom: 7px;
  display: inline-block;
  cursor: pointer;
  text-decoration: none;
  background: var(--color-white);
}

.pageData .btn:hover,
.pageData button.btn:hover {
  background: #f2f2f2;
}

.pageData iframe {
  display: block;
  margin: 0 auto;
}

.webpSpriteIcon {
  display: inline-block !important;
  background: url(/yas/images/master_sprite.webp);
  text-align: left;
  overflow: hidden;
}

.authorInfoAndTranslateBtn {
  display: flex;
  justify-content: space-between;
}

.translateIcon1 {
  background-position: -635px -876px;
  width: 27px;
  height: 24px;
  vertical-align: middle;
  margin-right: 10px;
  display: inline-block;
}

.translateIcon2 {
  background-position: -676px -876px;
  width: 27px;
  height: 24px;
  vertical-align: middle;
  margin-right: 10px;
  display: inline-block;
}

.authorInfoAndTranslateBtn .updated-info.row {
  margin-top: 0;
}

.authorInfoAndTranslateBtn .translateBtn {
  padding: 3px 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.73;
  color: #3d8ff2;
  border-radius: 3px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  text-transform: none;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  height: 36px;
}

.boardsheroSection button.primaryBtn{
  flex-shrink: 0;
}
.authorInfoAndTranslateBtn .translateBtn{
flex-shrink: 0;
}
/*GMU-471*/
.getSupport {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 0px;
  z-index: 5;
  display: flex;
  gap: 18px;
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
  color: #282828;
  align-items: center;
  justify-content: center;
}
.getSupport .getSupport__subheading {
  display: inline-block;
}
.getSupport .button__row__container {
  display: flex;
  gap: 13px;
  align-items: center;
}
.getSupport .row {
  display: none;
}
.getSupport button {
  width: 49%;
  border-radius: 2px;
  font-size: 13px;
  padding: 6px 4px;
  width: 149px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.getSupport button:last-child {
  margin-left: 0;
}
.authorAndDate .authorName {
  padding: 0 16px;
}
.widgetAUthorName {
  margin-bottom: 0px;
}

/************************* New Footer Design ***********************/

.boardsMenu{
  margin-top: 16px;
  border-top: 1px solid white;
}

.boardsMenu .heading.accor-box-head {
  color: #fff;
  border: 0;
  padding: 17px 0 17px 0;
  -webkit-tap-highlight-color: transparent;
}

.boardsMenu .heading.accor-box-head h4 {
  margin: 0;
  color: #fff;
  font-weight: bold !important;
}

.boardsMenu .box {
  display: table;
  width: 100%;
  border-radius: 2px;
}

.boardsMenu .accordionLink {
  margin: 0 -5px;
}

.boardsMenu ul, .boardsMenu ol, .boardsMenu li {
  padding: 0;
  list-style: none;
}

.boardsMenu .accordionLink li {
  padding: 5px 18px 5px 0px;
  display: inline-block;
  vertical-align: top;
}

.boardsMenu .nesteddiv>ul>li {
  width: 224px;
  position: relative;
}

.boardsMenu .nesteddiv li .toggle {
  overflow: hidden;
  height: 31px;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-line-clamp: 1;
  font-weight: 400;
  width: 190px;
  display: block;
  color: #fff !important;
}

.boardsMenu .popUp {
  position: relative;
  width: 98%;
  margin-top: 10px;
  height: auto;
  display: none;
  padding: 20px;
  margin-bottom: 20px;
  top: 7px;
  border-radius: 5px;
  left: 0;
  border: 0.5px solid #FFFFFF;
}

.boardsMenu .popUp ul {
  margin: 0 -5px;
}

.boardsMenu .parentUl{
  padding: 0px 5px;
  margin: 0px 5px;
}

.boardsMenu .popUp li {
  width: auto !important;
  position: relative;
  padding: 0 5px !important;
  vertical-align: top;
}

.boardsMenu .popUp li span {
  color: #fff !important;
  font-size: 14px;
}

.boardsMenu .nesteddiv li span {
  cursor: pointer;
  font-weight: 600;
}

.boardsMenu .popUp li a {
  line-height: 11px;
  font-size: 14px;
  position: relative;
  color: #F3F2EF !important;
}

.boardsMenu .popUp li:not(:last-child) a:before {
  position: absolute;
  content: '';
  width: 1px;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  top: 0;
  right: -6px;
}

.boardsMenu .activeLink::before {
  content: '';
  width: 20px;
  height: 20px;
  border: solid #fff;
  border-width: 0 1px 1px 0;
  display: inline-block;
  background-color: #273553;
  position: absolute;
  transform: rotate(-135deg);
  -webkit-transform: rotate(-135deg);
  top: 43px;
  z-index: 1;
  left: 24px;
}
/** end of board Menu*/

/**/
@media (max-width: 1023px) {
  /*GMU-471*/
  .getSupport button{
    flex-grow: 1;
}
  /**/
  .fixedRedirectionLinks {
    border: 0px;
    border-bottom: var(--border-line);
  }

  iframe {
    max-width: 100%;
    overflow: auto;
  }

  .table-responsive::-webkit-scrollbar {
    width: 5px;
  }

  .table-responsive::-webkit-scrollbar:vertical {
    width: 10px;
  }

  .table-responsive::-webkit-scrollbar:horizontal {
    width: 10px;
  }

  .table-responsive::-webkit-scrollbar-thumb {
    background-color: #d8d8d8;
    border-radius: 5px;
    border: 2px solid #fff;
  }

  .horizontalRectangle,
  .verticleRectangle,
  .squareDiv {
    margin-bottom: 10px;
  }

  .topHeader {
    height: 60px;
  }

  .setAlarmDiv {
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
    z-index: 1;
    margin: 0;
    background: var(--color-white);
    box-shadow: rgba(0, 0, 0, 0.12) 0 2px 4px 0;
    padding: 10px;
  }

  .boardsheroSection {
    margin-top: -157px;
    padding: 20px;
    margin-bottom: 10px;
  }

  .boardsheroSection h1 {
    font-size: 18px;
    line-height: 28px;
    padding-bottom: 10px;
  }

  .pageData {
    padding: 10px;
    margin-bottom: 10px;
  }

  .pageData h2 {
    font-size: 15px;
    line-height: 24px;
    padding: 8px 10px;
    margin-bottom: 10px;
  }

  .pageData h2 a {
    display: none;
  }

  .pageData p {
    padding-bottom: 10px;
  }

  .pageData ul {
    margin: 10px 0;
    padding-left: 30px;
  }

  .boardsListCard {
    margin-right: 0;
    margin-bottom: 10px;
    flex-basis: 100%;
    padding: 10px;
  }

  .boardsListCard .row {
    padding: 10px;
    padding-top: 0;
    margin: 0 -10px;
  }

  .boardsListCard .boardsLogo {
    width: 56px;
    height: 56px;
    margin-right: 10px;
  }

  .boardsListCard h3 {
    flex-basis: calc(100% - 56px - 10px);
  }

  .boardsListCard ul {
    margin-top: 10px;
    padding-right: 0;
    max-height: 100%;
  }

  .four-cardDisplay .sliderCardInfo,
  .custom-cardDisplay .sliderCardInfo {
    margin-right: 6px;
    width: 224px;
    display: inline-block !important;
  }

  .four-cardDisplay .sliderCardInfo:nth-of-type(4n),
  .custom-cardDisplay .sliderCardInfo:nth-of-type(4n) {
    margin-right: 6px;
  }

  .four-cardDisplay .sliderCardInfo:last-child,
  .custom-cardDisplay .sliderCardInfo:last-child {
    margin-right: 0;
  }

  .four-cardDisplay .sliderCardInfo img,
  .custom-cardDisplay .sliderCardInfo img {
    height: 168px;
  }

  .four-cardDisplay .sliderCardInfo .textDiv,
  .custom-cardDisplay .sliderCardInfo .textDiv {
    padding: 10px;
  }

  .four-cardDisplay .sliderCardInfo .widgetCardHeading,
  .custom-cardDisplay .sliderCardInfo .widgetCardHeading {
    font-weight: 400;
  }

  .four-cardDisplay .sliderCardInfo.mobileOnly,
  .custom-cardDisplay .sliderCardInfo.mobileOnly {
    vertical-align: bottom;
  }

  .four-cardDisplay .sliderCardInfo+.mobileOnly .viewAllDiv {
    min-height: 266px;
  }

  .pageRedirectionLinks {
    border-radius: 0px;
    border-right: 0px;
    border-left: 0px;
    margin: 0 -10px;
    margin-bottom: 10px;
    padding: 0px 5px;
  }

  .pageRedirectionLinks ul li {
    padding: 0;
    margin: 0 5px;
  }

  .pageRedirectionLinks ul li .activeLink,
  .pageRedirectionLinks ul li a {
    padding: 11px 0;
  }

  /*.getSupport {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 10px;
    border-radius: 0px;
    z-index: 1;
  }

  .getSupport .row {
    display: none;
  }

  .getSupport button {
    width: 49%;
    border-radius: 2px;
    font-size: 13px;
    padding: 6px 4px;
  }

  .getSupport button.applyNow {
    margin-left: 0;
  }*/

  .customSlider .scrollLeft,
  .customSlider .scrollRight {
    display: none !important;
  }

  .translateBtn {
    flex-basis: 100%;
  }

  .mobileSubNavDropDownMenu {
    background: rgba(0, 0, 0, 0.119215);
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
    display: none;
  }

  .mobileSubNavDropDownDiv {
    position: fixed;
    height: auto;
    bottom: 0;
    left: 0;
    overflow: auto;
    border-radius: 4px 4px 0 0;
    width: 100%;
    background: #fff;
    z-index: 3;
  }

  .mobileSubNavDropDownDiv ul {
    margin: 0;
    padding: 0 20px;
  }

  .mobileSubNavDropDownDiv ul li {
    font-size: 14px;
    line-height: 24px;
    border-bottom: 1px solid #d8d8d8;
    display: flex;
    color: #787878;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }

  .mobileSubNavDropDownDiv ul li a {
    background: none;
    border: none;
    color: #787878;
  }

  .mobileSubNavDropDownMenu.current {
    display: block;
  }

  .examRelataedLinks .mobileSubNavDropDownDiv ul {
    height: unset;
  }

  .mobileSubNavDropDownDiv ul li a {
    padding: 0;
  }

  .subNavDropDown:hover .caret {
    background-position: -438px -1089px;
    -webkit-transform: unset;
    transform: unset;
  }

  .mobileSubNavDropDown .caret {
    background-position: -382px -1057px;
    width: 25px;
    height: 37px;
    margin-left: 2px;
    margin-right: -10px;
    position: relative;
    bottom: 3px;
    transform: none !important;
  }

  .examRelataedLinks ul {
    overflow-y: hidden;
  }

  .subNavDropDown:has(.activeLink:hover):hover .caret {
    background-position: -382px -1057px;
  }
  
  .second-row-date{
    min-height: 46px;
  }

  .getSupport .getSupport__subheading {
    display: none;
  }
  .getSupport .button__row__container{
    width: 100%;
  }

  /************************* New Footer Design ***********************/

  .boardsMenu .nesteddiv>ul>li {
    width: 100%;
}

.boardsMenu .accordionLink li{
    padding: 5px 5px 5px 0px;
}

.boardsMenu .heading.accor-box-head{
    padding: 17px 0 0px 0;
}

.boardsMenu .popUp{
    padding: 0px 5px;
    border: 0;
}

.boardsMenu .nesteddiv li .toggle{
    padding: 0 5px;
    margin: 0px;
    width: 100%;
}

.boardsMenu .heading.accor-box-head h4{
    margin-bottom: 16px;
}

.boardsMenu .popUp li a{
    white-space: nowrap;
}
}