.webpSpriteIcon {
    display: inline-block !important;
    background: url("../../images/master_sprite.webp");
    text-align: left;
    overflow: hidden;
}

.page-header {
    height: 46px;
}

.page-header .topHeader {
    padding: 7px 20px;
    height: inherit;
}

.page-header .headerLogo {
    width: 121px;
    height: 31px;
    background-position: -416px -803px;
    vertical-align: middle;
    transform: none;
    margin-left: 20px;
}

.page-header .headerSubscribeButton {
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #fff;
    width: 103px;
    text-decoration: none;
}

.page-header .headerSubscribeButton .bellIcon {
    width: 16px;
    height: 16px;
    background-position: -276px -882px;
    vertical-align: middle;
}

.page-header .headerSubscribeButton p {
    display: inline;
    font-size: 12px;
    font-weight: bold;
    line-height: 2.17;
    color: #0966c2;
    margin-left: 10px;
}

.headerToolDiv {
    padding: 10px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border: solid 1px #d8d8d8;
    background-image: linear-gradient(to right, #fff 0%, #fffbec 100%);
    border-left: 6px solid #ff4e53;
    margin-bottom: 10px;
    position: sticky;
    top: 0px;
    z-index: 0;
}

.headerToolDiv h2 {
    font-size: 15px;
    font-weight: 600;
    line-height: 26px;
    color: #282828;
}

.headerToolDiv .headerToolCTADiv {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: 5px;
}

.headerToolDiv .headerToolCTADiv p {
    font-size: 15px;
    font-weight: 400;
    line-height: 24px;
    color: #282828;
    max-width: 250px;
}

.headerToolDiv .headerToolCTADiv button {
    flex-basis: 96px;
    flex-shrink: 0;
    height: 30px;
    border-radius: 3px;
    background-color: #ff4e53;
    border: none;
    font-size: 14px;
    font-weight: bold;
    line-height: 1.71;
    color: #fff;
}

.headerCTAPair {
    display: flex !important;
    padding: 10px;
    background-color: #fff;
    margin-bottom: 10px;
    position: sticky;
    top: 0px;
    border: solid 1px #d8d8d8;
    z-index: 1;
}

.headerCTAPair button {
    flex: 1;
    border-radius: 3px;
    height: 34px;
    border: none;
    font-size: 14px;
    font-weight: bold;
    line-height: 1.71;
}

.headerCTAPair button:first-child {
    border: solid 1px #0966c2;
    background-color: #0966c2;
    margin-right: 5px;
    color: #fff;
}

.headerCTAPair button:last-child {
    background-color: #ff4e53;
    color: #fff;
}

.leadFormContainerNews,
.logInPage {
    animation: slideIn 1000ms ease-in-out;
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes slideIn {
    from {
        transform: translateY(100vh);
    }

    to {
        transform: translateY(0vh);
    }
}

.closeLeadFormContainer {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 8;
}

.closeLeadFormContainerThankYou {
    position: absolute;
    right: -7px;
    top: -8px;
    z-index: 8;
}

.closeLeadFormContainer .closeLeadForm,
.closeLeadFormContainerThankYou .closeLeadForm {
    background-position: -99px -805px;
    width: 28px;
    height: 28px;
}

.flagIcon {
    background-position: -329px -167px !important;
    width: 27px;
    height: 18px;
    margin-right: 5px;
    vertical-align: sub;
}

.leadFormContainerNews {
    top: 50px;
    height: auto;
    width: 100%;
    left: 10px;
    overflow: hidden;
    border-radius: 4px;
    z-index: 20;
}

.leadFormContainerNews .leadFormDiv {
    margin-top: 0px;
}

.leadFormContainerNews .leadFormDiv .headingText {
    text-transform: none;
    font-weight: 500;
}

/* .leadFormContainerNews .leadFormDiv .userInputs {
    display: none;
} */

.leadFormContainerNews .leadFormDiv .userInputs .row .col-md-6 {
    width: 100%;
}

.leadFormContainerNews .leadFormDiv .userInputs .row .col-md-6 select {
    color: #989898;
}

.leadFormContainerNews .leadFormDiv .userInputs .row .mobileNumber .dialCodeDiv {
    background: none;
    padding-left: 10px;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 {
    margin: 0;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .checkbox-group {
    padding-left: 0;
    padding-right: 0;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .checkbox-group label {
    font-size: 12px;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .checkbox-group input[type=checkbox] {
    accent-color: #ff4e53;
}

.leadFormContainerNews .leadFormDiv .userInputs .row.m-0 .formSumbitBtn {
    width: 100%;
    padding: 0;
}

.leadFormContainerNews .leadFormDiv .userInputs .locationRow {
    margin: 0;
}

.leadFormContainerNews .leadFormDiv .userInputs .locationRow .formLocationIcon {
    background-position: -253px -882px;
    width: 13px;
    height: 17px;
    margin-right: 12px;
}

.leadFormContainerNews .leadFormDiv .userInputs .locationRow p {
    padding-bottom: 10px;
}

.leadFormContainerNews .leadFormDiv .userInputs .locationRow .locationCity {
    color: #282828;
}

.leadFormContainerNews .leadFormDiv .userInputs .locationRow .locationChange {
    color: #3d8ff2;
}

.leadFormContainerNews .leadFormDiv .userInputs .accountExist {
    justify-content: center;
    /* margin-top: 10px; */
}

.leadFormContainerNews .leadFormDiv .userInputs .accountExist p {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #787878;
    padding-bottom: 0;
}

.subscribeSectionNews {
    position: relative;
    background: var(--color-white);
}

.align-items-center {
    align-items: center;
}

.pageBody {
    display: flex;
    height: 100%;
    justify-content: space-between;
    flex-direction: column;
    /* padding: 10px 0; */
    padding-top: 20px;
}

.logInPage {
    position: fixed;
    top: unset;
    left: unset;
    bottom: 0;
    z-index: 20;
    min-height: 300px;
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
    display: none;
    padding: 0px !important;
}

.logInPage .container {
    padding: 0 20px;
}

.logInPage .right-col {
    width: 100%;
}

.logInPage .right-col p {
    font-size: 14px;
    line-height: 24px;
    padding-bottom: 10px;
}

.logInPage .right-col h2 {
    font-size: 24px;
    line-height: 30px;
    font-weight: 500;
}

.logInPage .right-col .userForm {
    max-width: 350px;
    margin: 0 auto;
}

.logInPage .right-col .userForm .userIcon,
.logInPage .right-col .userForm .mailIcon,
.logInPage .right-col .userForm .locationIcon,
.logInPage .right-col .userForm .bookIcon,
.logInPage .right-col .userForm .capIcon {
    top: 12px;
}

.logInPage .right-col p {
    color: #787878;
}

.logInPage .right-col p a {
    color: var(--color-red);
    font-weight: 500;
}

.logInPage .right-col .disclaimer {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #787878;
    padding-bottom: 10px;
}

.logInPage .right-col .logInOptionNews {
    text-align: center;
    margin-left: 45px;
}

.logInPage img {
    margin: 0 auto;
    margin-bottom: 30px;
    margin-top: 20px;
    display: block;
}

.logInPage .optSection p {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    color: #787878;
}

.logInPage .optSection .changeNumber {
    color: #ff4e53;
}

.logInPage .optSection .headingText {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.5;
    color: #282828;
    text-transform: uppercase;
}

.logInPage .optSection .pb-0 {
    padding-bottom: 0;
}

.logInPage .optSection .numberInputs {
    padding-bottom: 20px;
}

.logInPage .optSection .numberInputs input {
    border: 1px solid #d8d8d8;
    padding: 0;
    height: 46px;
    max-width: 60px;
    width: 60px;
}

.logInPage .thankYouMsgNews {
    padding: 0;
}

.logInPage .thankYouMsgNews .thankYouIcon {
    width: 100px;
    height: 100px;
    background-position: -253px -931px !important;
    margin-left: 117px;
}

.logInPage .thankYouMsgNews .thankYouText p:first-child {
    font-size: 24px;
    font-weight: 600;
    font-stretch: normal;
    line-height: 1.5;
    color: #282828;
    text-align: center;
}

.logInPage .thankYouMsgNews .thankYouText p:last-child {
    font-size: 16px;
    font-weight: normal;
    line-height: 1.5;
    color: #282828;
    text-align: center;
}

.formField {
    position: relative;
    max-width: 100%;
    padding-bottom: 15px;
}

.formField .row.m-0 {
    margin: 0;
    position: relative;
}

.formField input,
.formField select,
.formField .select2-container {
    padding: 7px 12px;
    padding-left: 41px;
    border-radius: 4px;
    border: var(--border-line);
    outline: none;
    width: 100%;
    font-size: 13px;
    line-height: 24px;
    background: var(--color-white);
    color: var(--primary-font-color);
}

.formField input::placeholder,
.formField select::placeholder,
.formField .select2-container::placeholder {
    color: #989898;
}

.formField select {
    background-position: 96% 15px !important;
}

.formField .numberInput {
    flex-basis: calc(100% - 90px);
}

.formField .numberInput input {
    padding-left: 12px;
    width: 100%;
    border-radius: 0 4px 4px 0;
    width: 100%;
    height: 40px;
}

.formField .numberInput .form-group {
    margin-bottom: 10px;
}

.dialCodeDiv {
    flex-basis: 90px;
    position: relative;
    border-radius: 4px 0 0 4px;
    border: var(--border-line);
    border-right: 0px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dialCodeDiv .dialCode {
    color: #989898;
    font-size: 14px;
    line-height: 24px;
}

.dialCodeDiv img {
    margin: inherit;
}

.userForm .form-group {
    margin-bottom: 20px;
}

.userForm .primaryBtn {
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
    margin-right: 0px;
}

.leadFormContainerNews {
    display: none;
    bottom: 0;
    left: unset;
    top: unset;
}

.pageMask {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: none;
    overflow-y: hidden;
    z-index: 10;
}

.leadpopup {
    background-color: #0966c2 !important;
    color: #fff !important;
}

.leadFormContainerNews .leadFormDiv .userInputs .accountExist p span,
p.logInOptionNews span {
    font-weight: 600;
    color: #ff4e53;
}

.leadFormDiv .mobileNumber .numberInput {
    padding-left: 0px;
}

.leadFormDiv .error {
    border: none !important;
}

.formHeadingDiv .formHeading {
    flex-basis: calc(100% - 66px);
}

.whiteDownloadIcon {
    width: 19px;
    height: 18px;
    background-position: 233px -353px;
    vertical-align: text-bottom;
    margin-left: 4px;
}

.alarmIcon {
    width: 24px;
    height: 27px;
    margin-right: 8px;
    vertical-align: middle;
    background-position: 474px -166px;
}

.validationError {
    padding-bottom: 0px !important;
    color: #ff4e53 !important;
}

.leadFormDiv .select2-container--default .select2-selection--single .select2-selection__rendered {
    padding: 0;
    padding-left: 8px;
    padding-right: 20px;
    border: none;
    min-height: unset;
    background-position: 95% 9px !important;
}

p.error {
    color: var(--color-red) !important;
}

.emailTxtOnlyNews {
    /* width: calc(100% - 90px) !important; */
}

.mobileNewsEmailAlignment {
    height: 40px !important;
    position: relative;
    display: flex;
    align-items: center;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    font-size: 13px;
}

p.error.errorMsg.errorMsgMobile,
p.error.errorMsg.errorMsgEmailNews {
    top: 38px;
}

button.primaryBtn.newsSubmit:disabled {
    background-color: rgba(255, 78, 83, 0.4);
}

.logInPage .optSection p.errorMsg {
    top: 144px;
}