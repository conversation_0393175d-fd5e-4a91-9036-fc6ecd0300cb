:root {
  --white-color: #fff;
  --border-gray: #d8d8d8;
  --primary-black-color: #282828;
  --light-gray-color: #989898;
  --primary-red-color: #ff4e3a;
  --background-gray-color: #f5f5f5;
}

.fancybox-slide--video .fancybox-content {
  max-width: 750px;
  max-height: 400px;
  margin: 0 auto;
}

h2, .pageData h2{
  font-size: 18px;
  padding: 8px 20px;
}
* {
  letter-spacing: normal;
}

.collegeHeighlights table thead tr,
.collegeHeighlights table th {
  text-align: center;
}

table td span {
  color: var(--primary-font-color);
}

table td {
  position: relative;
}

input[type="radio"] {
  appearance: none;
  border: 1px solid #dedfdf;
  width: 16px;
  height: 16px;
  display: inline-block;
  position: relative;
  cursor: pointer;
  margin-left: 0;
  padding: 0;
}

input[type="radio"]:checked:after {
  content: "";
  display: block;
  position: absolute;
  top: 1px;
  left: 5px;
  width: 5px;
  height: 10px;
  background: var(--color-red);
  border-style: solid;
  border-color: var(--color-white);
  border-width: 0 2px 2px 0;
  transform: scale(1) rotate(45deg);
  transition: 0.2s ease;
}

input[type="radio"]:checked {
  background: var(--color-red);
  border: 1px solid var(--color-red);
}

.breadcrumbDiv ul li a {
  font-weight: 500;
}

.p-0 {
  padding: 0;
}

.pb-0 {
  padding-bottom: 0;
}

.text-center {
  text-align: center;
}

.getPopup {
  cursor: pointer;
}

.readMoreInfo {
  text-transform: capitalize;
}

.faq_section {
  box-shadow: none;
  border: var(--border-line);
}

.faq_section.pageInfo {
  max-height: 410px;
}

.readMoreDiv,
.showMoreCourseWrap {
  box-shadow: none;
  border: var(--border-line);
  border-top: none;
}

.pageInfo {
  max-height: 270px;
}

.locationPinIcon {
  width: 22px;
  height: 27px;
  vertical-align: middle;
  background-position: 265px -295px;
  margin-right: 5px;
}

.urlIcon {
  width: 13px;
  height: 14px;
  background-position: 302px -375px;
  vertical-align: middle;
  margin-right: 10p;
}

.qnaIcon {
  width: 19px;
  height: 19px;
  vertical-align: middle;
  background-position: 232px -323px;
  margin-right: 5px;
}

.whiteDownloadIcon,
.redDownloadIcon {
  width: 19px;
  height: 18px;
  background-position: 233px -353px;
  vertical-align: text-bottom;
  margin-left: 4px;
}

.redDownloadIcon {
  background-position: 536px -364px;
}

.blueDownloadIcon {
  width: 19px;
  height: 18px;
  background-position: 624px -326px;
  vertical-align: text-bottom;
  margin-right: 5px;
}

.redDownloadIcon {
  width: 19px;
  height: 18px;
  background-position: 537px -365px;
  vertical-align: text-bottom;
  margin-right: 5px;
}

.writeIcon {
  width: 24px;
  height: 23px;
  vertical-align: middle;
  margin: 0 2px 0 8px;
  background-position: 203px -328px;
}

.badgeIcon {
  width: 24px;
  height: 28px;
  background-position: 167px -262px;
  margin-right: 15px;
  margin-top: -10px;
}

.heighlightsIcons1,
.heighlightsIcons2,
.heighlightsIcons3,
.heighlightsIcons4,
.heighlightsIcons5,
.heighlightsIcons6,
.heighlightsIcons7,
.heighlightsIcons8 {
  width: 30px;
  height: 30px;
  vertical-align: middle;
  margin-right: 10px;
  transform: scale(0.9);
}

.heighlightsIcons1 {
  background-position: 429px -223px;
}

.heighlightsIcons2 {
  background-position: 429px -271px;
}

.heighlightsIcons3 {
  background-position: 429px -312px;
}

.heighlightsIcons4 {
  background-position: 429px -353px;
}

.heighlightsIcons5 {
  background-position: 392px -222px;
}

.heighlightsIcons6 {
  background-position: 392px -264px;
}

.heighlightsIcons7 {
  background-position: 392px -312px;
}

.heighlightsIcons8 {
  background-position: 392px -357px;
}

.mobileOnly.favriteIcon {
  display: inline-block;
  height: 36px;
  width: 36px;
  background-position: -645px -269px;
}

.interestedIcon {
  width: 20px;
  height: 20px;
  background-position: -313px -595px;
  vertical-align: middle;
}

.multipleUserIcon {
  width: 19px;
  height: 19px;
  vertical-align: middle;
  background-position: -338px -595px;
  margin-right: 10px;
}

.questionIcon {
  width: 19px;
  height: 19px;
  vertical-align: middle;
  background-position: -511px -322px;
  margin-right: 5px;
}

.verifiedIcon {
  width: 20px;
  height: 20px;
  vertical-align: middle;
  background-position: -671px -554px;
  transform: scale(0.7);
}

.verifiedSpan {
  display: block;
  color: #388e3c;
  font-size: 12px;
  margin-top: 10px;
}

.verifiedShieldIcon {
  background: url(../../images/master_sprite.webp);
  height: 16px;
  width: 16px;
  background-position: -705px -554px;
  vertical-align: text-bottom;
}

.newHeroButtons {
  width: 165px;
  padding-left: 0 !important;
  padding-right: 0 !important;
  position: relative;
}

.applyRedIcon {
  width: 20px;
  height: 20px;
  background-position: -165px -594px;
  position: absolute;
  top: 7px;
  right: 30px;
}

.applyWhiteIcon {
  width: 20px;
  height: 20px;
  background-position: -188px -593px;
  position: absolute;
  top: 8px;
  right: 22px;
}

.redDownloadIcon {
  width: 20px;
  height: 20px;
  background-position: -209px -364px;
  vertical-align: text-bottom;
}

.unlikeBtn {
  width: 22px;
  height: 18px;
  background-position: -673px -515px;
  vertical-align: middle;
  cursor: pointer;
}

.likeBtn {
  width: 22px;
  height: 19px;
  vertical-align: middle;
  cursor: pointer;
  background-position: -703px -516px;
}

.courseHighlightIcon {
  width: 24px;
  height: 24px;
  background-image: url(../../images/master_sprite.webp) !important;
  vertical-align: top;
  margin-right: 20px;
}

.courseHighlight1 {
  background-position: -312px -878px;
}

.courseHighlight2 {
  background-position: -346px -878px;
}

.courseHighlight3 {
  background-position: -380px -878px;
}

.courseHighlight4 {
  background-position: -414px -878px;
}

.courseHighlight5 {
  background-position: -448px -878px;
}

.newWriteIcon {
  background-position: -293px -1064px;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  vertical-align: middle;
}

.newAcademicIcon {
  background-position: -328px -1064px;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  vertical-align: middle;
}

.newRankIcon {
  background-position: -651px -373px;
  width: 20px;
  height: 16px;
  margin-right: 20px;
  vertical-align: middle;
}

/* Facilities Section */
.facilities .spriteIcon {
  width: 33px;
  height: 30px;
  vertical-align: middle;
  margin-right: 10px;
}

.facilities .playground {
  background-position: -27px -1151px;
}

.facilities .computer-labs {
  background-position: 126px -119px;
}

.facilities .hostel {
  background-position: 85px -119px;
}

.facilities .medical {
  background-position: 47px -119px;
}

.facilities .cafeteria {
  background-position: 47px -200px;
}

.facilities .gym {
  background-position: 126px -160px;
}

.facilities .laboratory {
  background-position: 84px -160px;
}

.facilities .transport {
  background-position: 48px -160px;
}

.facilities .library {
  background-position: 85px -202px;
}

.facilities .sports {
  background-position: 126px -202px;
}

.facilities .auditorium {
  background-position: 438px -9px;
}

.facilities .campus {
  background-position: 403px -9px;
}

.facilities .security {
  background-position: 366px -9px;
}

.facilities .wi-fi {
  background-position: 331px -9px;
}

.facilities .canteen {
  background-position: 283px -10px;
}

.facilities .banks {
  background-position: 246px -10px;
}

.facilities .centre {
  background-position: 211px -10px;
}

.facilities .e-classroom {
  background-position: 174px -10px;
}

.facilities .communication {
  background-position: 134px -10px;
}

.facilities .swimming-pool {
  background-position: 99px -10px;
}

.facilities .classrooms {
  background-position: 63px -10px;
}

.facilities ul {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
}

.facilities ul li {
  flex-basis: 24%;
  margin-right: 10px;
  margin-bottom: 20px;
}

.facilities ul li:nth-of-type(4n) {
  margin-right: 0;
}

.facilities ul li:before {
  display: none;
}

.facilitySection {
  max-height: 550px;
}

.locationIconBlue {
  background-position: 345px -316px;
}

.phoneIconBlue {
  background-position: 344px -342px;
}

.mailIconBlue {
  background-position: 309px -315px;
}

.webIconBlue {
  background-position: 308px -342px;
}

.sortIcon {
  width: 20px;
  height: 20px;
  background-position: 231px -378px;
}

.clgIcon {
  width: 24px;
  height: 28px;
  background-position: 266px -259px;
  vertical-align: middle;
  margin-right: 10px;
}

.rankIcon {
  width: 16px;
  height: 24px;
  background-position: 262px -331px;
  vertical-align: middle;
  margin-right: 10px;
}

.whiteLabel {
  width: 18px;
  height: 22px;
  background-position: 232px -289px;
  margin-right: 10px;
  vertical-align: middle;
}

.blueLabel {
  width: 18px;
  height: 22px;
  margin-right: 10px;
  vertical-align: middle;
  background-position: 132px -322px;
}

.assureIcon {
  width: 20px;
  height: 24px;
  background-position: 262px -363px;
  vertical-align: middle;
  margin-right: 10px;
}

.favriteIcon {
  width: 28px;
  height: 28px;
  background-position: 206px -290px;
  vertical-align: middle;
  margin-right: 8px;
}

.claimIcon {
  width: 24px;
  height: 24px;
  background-position: 205px -260px;
  vertical-align: middle;
  margin-right: 8px;
}

.unlikeBtn {
  width: 22px;
  height: 18px;
  background-position: 165px -367px;
  vertical-align: middle;
  cursor: pointer;
}

.likeBtn {
  width: 22px;
  height: 19px;
  vertical-align: middle;
  cursor: pointer;
  background-position: 200px -367px;
}

.modeIcon {
  background-position: 52px -274px;
}

.courseDuratioIcon {
  background-position: 52px -299px;
}

.seatsIcon {
  background-position: 52px -323px;
}

.feesIcons {
  background-position: 52px -346px;
}

.examTypeIcon {
  background-position: 50px -371px;
}

.ratingIcon {
  background-position: 93px -373px;
}

.newCapIcon {
  background-position: -27px -1067px;
  width: 20px;
  height: 14px;
  margin-right: 20px;
  vertical-align: middle;
}

.newSocialIcon {
  background-position: -62px -1068px;
  width: 16px;
  height: 12px;
  margin-right: 20px;
  vertical-align: middle;
}

.newClockIcon {
  background-position: -93px -1064px;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  vertical-align: middle;
}

.newFlagIcon {
  background-position: -158px -1066px;
  width: 20px;
  height: 15px;
  margin-right: 20px;
  vertical-align: middle;
}

.newInstituteIcon {
  background-position: -188px -1065px;
  width: 20px;
  height: 18px;
  margin-right: 20px;
  vertical-align: middle;
}

.newEmblemIcon {
  background-position: -223px -1063px;
  width: 20px;
  height: 22px;
  margin-right: 20px;
  vertical-align: middle;
}

.newLinkIcon {
  background-position: -128px -1068px;
  width: 20px;
  height: 12px;
  margin-right: 20px;
  vertical-align: middle;
}

.newRupeeIcon {
  background-position: -258px -1064px;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  vertical-align: middle;
}

.redCaret {
  width: 12px;
  height: 10px;
  background-position: 651px -154px;
  transform: rotate(180deg);
  margin-left: 5px;
}

.noSticky .fixedQuickLinksDiv {
  position: initial;
  margin-top: 0 !important;
}

.noSticky .sidebarAds {
  margin-top: 0 !important;
}

.stickyAds {
  position: sticky;
  top: 80px;
  margin-bottom: 30px;
  width: 328px;
}

.stickyAds .sidebarAds {
  position: static;
  max-width: 328px;
}

.sidebarLinks.overflow-scroll {
  max-height: 479px;
}

button.admissionGuid,
a.admissionGuid {
  background: var(--color-white);
  color: var(--color-red);
  border: 1px solid var(--color-red);
}

.primaryBtn.blueBg {
  background: #0966c2;
  color: var(--color-white) !important;
}

.courseInfo .moreCourse span {
  color: var(--primary-font-color);
  display: none;
}

.limitCard {
  max-height: 860px;
  overflow: hidden;
}

.cutoffLimitCard {
  max-height: 759px;
  overflow: hidden;
}

.sidebarTextLink {
  word-break: break-all;
}

/* College Hero Section */
.clgInfoHeroSection {
  margin: 20px 0;
  margin-top: 30px;
  border-radius: 4px;
  border: var(--border-line);
  border-radius: 4px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.collegeInfo {
  padding: 20px;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center;
  position: relative;
  color: var(--color-white);
  border-radius: 4px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  height: 146px;
}

.collegeInfo:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.collegeInfo .row {
  margin: 0;
  position: relative;
  flex-wrap: nowrap;
}

.collegeInfo h1 {
  font-size: 24px;
  line-height: 38px;
  padding-bottom: 10px;
  color: var(--color-white);
  font-weight: 500;
}

.collegeInfo ul {
  margin: 0;
  padding: 0;
}

.collegeInfo ul li {
  display: inline-block;
  font-size: 14px;
  line-height: 20px;
  position: relative;
  padding-right: 25px;
  font-weight: 500;
}

.collegeInfo ul li a {
  color: var(--color-white);
}

.collegeInfo ul li:after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: var(--color-white);
  right: 7px;
  top: 45%;
  transform: translate(0px, -45%);
}

.collegeInfo ul li:last-child {
  margin-right: 0;
}

.collegeInfo ul li:last-child:after {
  display: none;
}

.collegeLogo {
  max-width: 72px;
  max-height: 72px;
  margin-right: 10px;
  display: inline-block;
}

.heroSectionSubDiv {
  background: var(--color-white);
  margin: 0;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.heroSectionSubDiv .primaryBtn {
  padding: 5px 20px;
  font-size: 16px;
  line-height: 24px;
  border: 1px solid var(--color-red);
  border-radius: 4px;
}

.heroSectionSubDiv .writeReview,
.lead-cta button.writeReview {
  color: var(--color-red);
  background: var(--color-white);
  margin-right: 15px;
  border-radius: 3px;
  border: solid 1px #ff4e53;
  background-color: rgba(255, 78, 83, 0.1);
  font-size: 14px !important;
  font-weight: 500 !important;
  text-align: left;
  padding-left: 36px !important;
}

.updated-info {
  font-size: 14px;
  line-height: 20px;
}

.updated-info img {
  width: 40px;
  height: 40px;
  margin-right: 9px;
  border-radius: 50%;
  vertical-align: middle;
}

.heroSectionSubDiv {
  border-radius: 3px;
  font-size: 14px !important;
  font-weight: 500 !important;
  text-align: left;
}

.heroSectionSubDiv .brochureBtn {
  font-size: 14px;
  font-weight: 500;
}

/* Subpage navbar */
.collegeRelataedLinks {
  padding: 12px 10px;
  padding-bottom: 8px;
  border: var(--border-line);
  background: var(--color-white);
  margin-bottom: 20px;
  border-radius: 4px;
  position: relative;
  height: 53px;
}

.collegeRelataedLinks ul {
  margin: 0;
  padding: 0;
  white-space: nowrap;
  overflow: auto;
  overflow-y: hidden;
}

.collegeRelataedLinks ul::-webkit-scrollbar {
  display: none;
}

.collegeRelataedLinks ul li {
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  margin: 0 10px;
  list-style-type: none;
  display: inline-block;
  padding-bottom: 2px;
}

.collegeRelataedLinks ul li .activeLink {
  color: var(--color-red);
  padding-bottom: 2px;
  border-bottom: 3px solid var(--color-red);
  font-weight: 500;
}

.collegeRelataedLinks ul li a {
  color: #787878;
  text-decoration: none;
  display: inline-block;
}

.subNavDropDown a {
  display: inline-block !important;
}

.subNavDropDown:hover .caret {
  background-position: 651px -154px;
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
}

.subNavDropDown .subNavDropDownMenu {
  display: none;
  margin: 0;
  position: absolute;
  z-index: 2;
  padding: 5px 20px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.24);
  background-color: #fff;
}

.subNavDropDown .subNavDropDownMenu li {
  display: block;
  color: #787878;
  cursor: pointer;
}

.subNavDropDown .subNavDropDownMenu li:not(:last-child) {
  margin-bottom: 5px;
}

.subNavDropDown .subNavDropDownMenu li:hover {
  color: #ff4e53;
}

.subNavDropDown:hover .subNavDropDownMenu {
  display: block;
}

.btn_left,
.btn_right {
  z-index: 1;
}

.mobileSubNavDropDownMenu {
  display: none;
}

.subNavDropDown .subNavDropDownMenu li a {
  padding: 0;
  color: #787878;
}

.subNavDropDown .subNavDropDownMenu li a:hover {
  color: #ff4e53;
}

.collegeRelataedLinks .caret {
  padding: 0;
  border: 0;
  line-height: unset;
}

.fixedExamRelatedDiv {
  position: fixed;
  top: 0;
  z-index: 2;
  width: 100%;
  max-width: 1206px;
  margin: 0 auto;
}

/* Pagedata */
.pageData {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.pageData h2 {
  line-height: 28px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  position: relative;
}

.pageData h2 a {
  font-size: 14px;
  line-height: 24px;
  color: var(--color-red);
  font-weight: 500;
  text-transform: capitalize;
}

.pageData p,
.pageData li,
.pageData a {
  font-size: 14px;
  line-height: 24px;
  word-break: break-word;
  overflow-wrap: anywhere;
}

.pageData p {
  color: #282828;
  padding-bottom: 15px;
  font-weight: 400;
}

.pageData h3 {
  font-size: 15px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}

.pageData h4 {
  padding-bottom: 10px;
  line-height: 24px;
  font-weight: 500;
}

.pageData button {
  background: var(--color-red);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-white);
  padding: 8px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  transition: 0.2s ease;
  outline: none;
  margin-bottom: 10px;
}

.pageData .greyBtn {
  color: #787878;
  padding: 5px 20px;
  border-radius: 24px;
  border: var(--border-line);
  background: var(--color-white);
  margin-right: 5px;
  margin-bottom: 8px;
  font-weight: 500;
  display: inline-block;
  text-decoration: none;
}

.pageData .greyBtn:hover {
  background: #f2f2f2;
}

.pageData ul li {
  position: relative;
  list-style-type: none;
}

.pageData ul li:before {
  content: "";
  background: url(../../images/master_sprite.webp);
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

/* Latest updates */
.latestUpdates {
  border-radius: 4px;
  margin-bottom: 20px;
  padding: 10px;
}

.latestUpdates .cardHeading {
  font-size: 15px;
  font-weight: 500;
  line-height: 26px;
  padding-bottom: 10px;
}

.latestUpdates ul {
  margin: 0;
  padding-left: 20px;
}

.latestUpdates ul li {
  position: relative;
}

.latestUpdates ul li:before {
  content: "";
  position: absolute;
  width: 15px;
  height: 15px;
  left: -20px;
  top: 5px;
  background: url(../../images/master_sprite.webp);
  background-position: 339px -374px;
}

.whatsNew ul li:before {
  background: url(../../images/master_sprite.webp) !important;
}

.whatsNew ul li ul li {
  color: #282828;
}

.whatsNew ul li ul li:before {
  content: '';
  background-color: black !important;
  border-radius: 50%;
  display: inline-block;
  width: 7px !important;
  height: 7px !important;
  margin-left: 4px;
  margin-top: 3px;
}

.pageData .whatsNew {
  background: #f0f8ff;
}

.whatsNew h2.cardHeading {
  padding-left: 0px;
  padding-bottom: 0px !important;
  background-color: #f0f8ff;
}

.latestUpdates ul li span {
  font-size: 14px;
  color: #ff4e53;
}

.latestUpdates ul li a {
  cursor: pointer;
  color: #3d8ff2;
  text-decoration: none;
}

/* Tables */
.collegeHeighlights table img,
.collegeRankings table img,
.subText table img {
  max-width: 60px;
  max-height: 23px;
  display: inline-block;
  vertical-align: middle;
}

.collegeHeighlights table td:first-child {
  min-width: 260px;
}

.collegeHeighlights table td:last-child {
  font-weight: 500;
}

.collegeHeighlights table td {
  text-align: left;
}

.collegeRankings table {
  text-align: center;
}

.admissionHighlights table .primaryBtn {
  display: block;
  margin: 0 auto;
  width: 100%;
}

.admissionPageData.pageInfo {
  max-height: 630px;
}

.table-responsive table thead tr {
  background-color: #0d3d63;
  color: #fff;
}

table td:first-child {
  text-align: left;
}

table td {
  position: relative;
  text-align: center;
  min-width: 180px;
}

table td span {
  color: #787878;
}

.table-responsive tbody td p {
  padding-bottom: 0;
}

.collegeHeighlights .table-responsive {
  margin-bottom: 0;
}

.collegeHeighlights .table-responsive table td {
  padding-left: 20px;
}

table td:first-child,
table tr td:last-child {
  border-right: 1px solid #eaeaea;
}

.courseFeesTable tbody td:first-child {
  font-weight: 500;
}

.table-responsive .programPageTable thead tr {
  color: #fff;
}

.table-responsive .programPageTable thead tr th {
  background-color: #0d3d63;
}

.table-responsive thead th p {
  color: #fff;
  padding-bottom: 0;
}

.table-responsive thead th {
  background-color: #0d3d63;
}

.collegeHeighlights .courseFeesTable td:last-child {
  font-weight: 500;
  padding: 20px 0 20px 40px;
}

table thead td span {
  color: #fff;
}

table thead td p span {
  color: #fff;
}

.tableLayoutFixed {
  table-layout: fixed;
}

button.getFees {
  background: 0 0;
}

.courseFeesTable.collegeCourseTable {
  table-layout: fixed;
}

.collegeRankings .rankTable img {
  max-height: fit-content;
}

.collegeRankings .rankTable img {
  max-height: fit-content;
}

.rankTable thead tr {
  font-weight: 500;
}

.collegeRankings table.rankTable img {
  max-width: 100px;
  width: unset;
}

.readMoreDiv {
  padding-top: 6px;
}

/* getSupport 471 */
.getSupport {
  padding: 19px;
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 3px;
  margin-bottom: 20px;
}

.getSupport .row {
  margin: 0;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}

.getSupport img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}

.getSupport p {
  font-size: 18px;
  line-height: 26px;
}

.getSupport button {
  width: 161px;
  border-radius: 3px;
  font-size: 14px;
  line-height: 24px;
  padding: 6px;
  text-align: center;
  color: var(--color-white);
  font-weight: var(--font-bold);
  border: none;
}

.getSupport button.talkToExpert {
  background: var(--topheader-bg);
}

.getSupport button.applyNow {
  background: var(--color-red);
  margin-left: 15px;
}

.getSupport .applyNow .applyRedIcon {
  display: none !important;
}

.applyNowButtonContainer .textBlue .applyRedIcon {
  width: 20px;
  height: 20px;
  background-position: -188px -593px;
  position: absolute;
  top: 8px;
  right: 22px;
}

/* Widgets */
.customSlider {
  position: relative;
}

.customSlider .scrollRight {
  right: -20px;
  z-index: 0;
}

.customSlider .scrollLeft {
  top: 50%;
  left: -20px;
}

.customSlider .row {
  margin: 0;
}

.customSlider .customSliderCards {
  display: block;
  white-space: nowrap;
  overflow: auto;
}

.customSlider .customSliderCards::-webkit-scrollbar {
  display: none;
}

.customSlider .sliderCardInfo {
  padding: 20px;
  border-radius: 4px;
  border: var(--border-line);
  margin-right: 20px;
}

.customSlider .sliderCardInfo:last-child {
  margin-right: 0;
}

.customSlider .sliderCardInfo .clgLogo {
  max-width: 72px;
  height: 72px;
  display: block;
  margin-right: 20px;
}

.customSlider .sliderCardInfo p {
  padding-bottom: 0;
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-semibold);
}

.customSlider .sliderCardInfo p span {
  color: #989898;
  font-weight: 400;
  font-size: 13px;
}

.customSlider .sliderCardInfo p:first-child {
  font-size: 16px;
  line-height: 24px;
  padding-bottom: 2px;
}

.customSlider .sliderCardInfo p:first-child a {
  color: var(--primary-font-color);
}

.customSlider .sliderCardInfo p:first-child a:hover {
  color: var(--anchor-textclr);
}

.two-cardDisplay .sliderCardInfo {
  width: 48.4%;
  display: inline-block;
}

.four-cardDisplay .sliderCardInfo {
  margin-right: 14px;
}

.four-cardDisplay .sliderCardInfo {
  width: 23.8%;
  display: inline-block;
  padding: 0;
  white-space: initial;
}

.four-cardDisplay .sliderCardInfo:nth-of-type(4n) {
  margin-right: 0;
}

.four-cardDisplay .sliderCardInfo:nth-of-type(4n + 1) {
  margin-left: 20px;
}

.four-cardDisplay .sliderCardInfo {
  margin-right: 14px;
}

.four-cardDisplay .sliderCardInfo:first-child {
  margin-left: 0;
}

.four-cardDisplay .sliderCardInfo img {
  display: block;
  width: 100%;
}

.four-cardDisplay .sliderCardInfo figure {
  display: grid;
  height: 207px;
  border-bottom: var(--border-line);
}

.four-cardDisplay .sliderCardInfo figure img {
  align-self: center;
  height: auto;
  max-height: 207px;
}

.four-cardDisplay .sliderCardInfo p {
  font-size: 14px;
  line-height: 24px;
}

.four-cardDisplay .sliderCardInfo h3,
.four-cardDisplay .sliderCardInfo .widgetCardHeading {
  font-size: 15px;
  line-height: 24px;
  padding-bottom: 0;
  min-height: 48px;
  margin-bottom: 5px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
}

.four-cardDisplay .sliderCardInfo .subText {
  color: #989898;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  padding: 20px;
}

.four-cardDisplay .sliderCardInfo .spriteIcon {
  position: initial;
  vertical-align: middle;
  margin-right: 5px;
}

.four-cardDisplay .sliderCardInfo .textDiv {
  padding: 20px;
}

.four-cardDisplay .sliderCardInfo .collegeLogo {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  margin-top: -60px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 5px;
}

.four-cardDisplay a {
  text-decoration: none;
}

.four-cardDisplay .displayCard:hover h3 {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.collegeReviewPage .four-cardDisplay .sliderCardInfo,
.collegeListpageBody .four-cardDisplay .sliderCardInfo {
  width: 23.4%;
}

.collegeReviewPage .customSlider .sliderCardInfo,
.collegeListpageBody .customSlider .sliderCardInfo {
  margin-right: 14px;
}

.collegeReviewPage .customSlider .sliderCardInfo:last-child,
.collegeReviewPage .customSlider .sliderCardInfo:nth-of-type(4n),
.courseAndFeePage .customSlider .sliderCardInfo:last-child,
.collegeListpageBody .customSlider .sliderCardInfo:last-child,
.collegeListpageBody .customSlider .sliderCardInfo:nth-of-type(4n) {
  margin-right: 0;
}

.collegeReviewPage .customSlider .sliderCardInfo:nth-of-type(4n + 1),
.collegeListpageBody .customSlider .sliderCardInfo:nth-of-type(4n + 1) {
  margin-left: 14px;
}

.collegeReviewPage .customSlider .sliderCardInfo:first-child,
.courseAndFeePage .customSlider .sliderCardInfo:first-child,
.collegeListpageBody .customSlider .sliderCardInfo:first-child {
  margin-left: 0;
}

.collegeReviewPage .four-cardDisplay .sliderCardInfo figure,
.collegeListpageBody .four-cardDisplay .sliderCardInfo figure {
  height: 146px;
}

.collegeReviewPage .four-cardDisplay .sliderCardInfo figure img,
.courseAndFeePage .four-cardDisplay .sliderCardInfo figure img,
.collegeListpageBody .four-cardDisplay .sliderCardInfo figure img {
  align-self: center;
}

.collegeReviewPage .four-cardDisplay .sliderCardInfo .collegeLogo,
.collegeListpageBody .four-cardDisplay .sliderCardInfo .collegeLogo {
  height: 56px;
}

.collegeReviewPage .four-cardDisplay .textDiv,
.collegeListpageBody .four-cardDisplay .textDiv {
  padding: 10px;
  padding-top: 20px;
}

.collegeReviewPage .four-cardDisplay .textDiv,
.collegeListpageBody .four-cardDisplay .textDiv,
.collegeReviewPage .four-cardDisplay .subText {
  padding-left: 10px;
}

.collegeReviewPage .four-cardDisplay .sliderCardInfo figure img,
.collegeListpageBody .four-cardDisplay .sliderCardInfo figure img {
  max-height: 146px;
}

.three-cardDisplay a {
  text-decoration: none;
}

.three-cardDisplay .sliderCardInfo {
  margin-right: 14px;
  width: 36.8%;
  display: inline-block;
  padding: 0;
  white-space: initial;
}

.three-cardDisplay .sliderCardInfo img {
  display: block;
  width: 100%;
}

.three-cardDisplay .sliderCardInfo:nth-of-type(3n + 1) {
  margin-left: 20px;
}

.three-cardDisplay .sliderCardInfo:first-child {
  margin-left: 0;
}

.three-cardDisplay .sliderCardInfo figure {
  display: grid;
  height: 207px;
  border-bottom: var(--border-line);
}

.three-cardDisplay .sliderCardInfo figure img {
  align-self: center;
  height: auto;
  max-height: 207px;
}

.three-cardDisplay .sliderCardInfo .textDiv {
  padding: 20px;
}

.three-cardDisplay .sliderCardInfo .collegeLogo {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  margin-top: -60px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 5px;
}

.three-cardDisplay .sliderCardInfo .widgetCardHeading {
  font-size: 14px;
  line-height: 24px;
  padding-bottom: 0;
  min-height: 48px;
  margin-bottom: 5px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
}

.three-cardDisplay .sliderCardInfo .subText {
  color: #989898;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  padding: 20px;
}

.three-cardDisplay .sliderCardInfo .spriteIcon {
  position: initial;
  vertical-align: middle;
  margin-right: 5px;
}

.sideBarSection {
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 4px;
  margin-bottom: 20px;
}

.sideBarSection .sidebarHeading {
  background: #d8d8d8;
  padding: 10px 20px;
  font-size: 15px;
  line-height: 24px;
  margin: 20px;
  margin-bottom: 6px;
  font-weight: 500;
}

.sideBarSection .row {
  margin: 0;
}

.sideBarSection .listCard {
  display: block;
  padding: 10px 20px;
  border-bottom: var(--border-line);
}

.sideBarSection .listCard:hover .cardText,
.sideBarSection .listCard:hover .subText {
  color: var(--anchor-textclr);
}

.sideBarSection .listCard:last-child {
  border-bottom: none;
}

.sideBarSection .sidebarImgDiv {
  flex-basis: 72px;
  margin-right: 20px;
}

.sideBarSection .sidebarImgDiv img {
  display: block;
  margin: 0 auto;
  width: 100%;
}

.sideBarSection .sidebarTextLink {
  flex-basis: calc(100% - 92px);
}

.sideBarSection .sidebarTextLink p {
  font-size: 15px;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.sideBarSection .sidebarTextLink .cardText {
  color: var(--primary-font-color);
}

.sideBarSection .sidebarTextLink .subText {
  padding-top: 3px;
  color: #989898;
  position: relative;
}

.sideBarSection .sidebarTextLink .subText .locationIcon {
  position: static;
  vertical-align: middle;
}

.sideBarSection .sidebarTextLink .applyText {
  color: var(--anchor-textclr);
}

.sidebarAds {
  padding: 0 20px;
  padding-top: 15px;
  width: 382px;
}

/* Review section */
.reviewsSection {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.reviewsSection h2 {
  line-height: 28px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  position: relative;
}

.allReviews {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.allReviews.row {
  margin: 0;
  margin-bottom: 20px;
}

.allReviews .col-md-3,
.allReviews .col-md-4 {
  text-align: center;
}

.allReviews .ratingHeading {
  font-size: 18px;
  line-height: 28px;
  font-weight: 500;
  padding-bottom: 10px;
}

.allReviews .ratingHeading span {
  font-size: 14px;
  color: #787878;
}

.allReviews .avgRating {
  font-size: 50px;
  line-height: 38px;
  padding: 20px 0;
  padding-top: 10px;
  font-weight: var(--font-bold);
}

.allReviews .subText {
  color: #787878;
  font-size: 14px;
  line-height: 28px;
  margin-top: 8px;
  font-weight: 500;
}

.allReviews .ratings {
  line-height: 25px;
}

.allReviews .ratings .full-star,
.allReviews .ratings .half-star,
.allReviews .ratings .empty-star {
  transform: scale(1.5);
  margin: 0 4px;
}

.bargraphBody {
  background: #c4c4c4;
  width: 100px;
  height: 4px;
  margin: 0 6px;
}

.valueIndicator {
  background: #ffc318;
  height: 4px;
}

.reviewRation {
  align-items: center;
  justify-content: center;
}

.reviewRation p {
  font-size: 15px;
  line-height: 28px;
}

.componentRatio .row {
  margin: 0 -15px;
  margin-bottom: 5px;
}

.componentRatio .row:last-child {
  margin-bottom: 0;
}

.componentRatio span {
  margin-left: 5px;
}

.componentRatio .spriteIcon {
  margin: 0 1px;
}

.componentRatio p {
  line-height: 24px;
  font-size: 15px;
  font-weight: 400;
}

.redirectreviewCard {
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.redirectreviewCard p {
  font-size: 12px;
  line-height: 24px;
}

a.viewAllReviewsBtn {
  display: block;
  max-width: 180px;
  margin: 0 auto;
}

.collegeReviewPage .allReviews .col-md-3,
.courseAndFeePage .allReviews .col-md-3,
.collegeListpageBody .allReviews .col-md-3 {
  padding: 0;
}

.collegeReviewPage .allReviews .col-md-3 {
  flex: 0 0 18%;
  max-width: 18%;
}

.collegeReviewPage .allReviews .col-md-5 {
  flex: 1 0 41.666667%;
  max-width: 48.666667%;
  padding-right: 0;
}

.collegeReviewPage .viewAllReviewsBtn,
.courseAndFeePage .viewAllReviewsBtn,
.collegeListpageBody .viewAllReviewsBtn {
  display: block;
  margin: 0 auto;
}

.collegeReviewPage .redirectreviewCard a,
.courseAndFeePage .redirectreviewCard a,
.collegeListpageBody .redirectreviewCard a {
  color: var(--color-red);
}

.collegeListpageBody .reviewCard {
  max-height: 315px;
}

.moreReviews .reviewCard {
  display: flex;
  flex-direction: column;
  width: inherit;
  padding: 20px;
  background-color: #fff;
  gap: 10px;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
  margin-bottom: 20px;
}

.moreReviews .reviewCard .reviewerDetailCardHeader {
  line-height: 1.5;
}

.moreReviews .reviewCard .reviewerDetailCardHeader img {
  height: 40px;
  width: 40px;
  background-color: #d8d8d8;
  border-radius: 50%;
  vertical-align: text-top;
  float: left;
  margin-right: 10px;
}

.moreReviews .reviewCard .reviewerDetailCardHeader .verifiedSpan {
  float: right;
  color: #388e3c;
}

.moreReviews .reviewCard .reviewerDetailCardHeader span {
  font-size: 14px;
}

.reviewCard .reviewerDetailCardHeader .reviewLandingHeader {
  margin-left: 50px !important;
}

.moreReviews .reviewCard .reviewerDetailCardHeader .reviewerNameCardHeader {
  font-size: 15px;
  font-weight: 500;
  color: #282828;
}

.moreReviews .reviewCard .reviewerDetailCardHeader span {
  font-size: 14px;
  color: #787878;
}

.verifiedShieldIcon {
  background: url(/yas/images/master_sprite.webp);
  height: 16px;
  width: 16px;
  background-position: -705px -554px;
  vertical-align: text-bottom;
}

.reviewerHeaderContent.reviewLandingHeader span:not(.reviewerNameCardHeader):before {
  content: "\2022";
  margin-left: 5px;
  margin-right: 5px;
}

.starRating {
  display: flex;
  align-items: center;
}

.moreReviews .reviewCard .reviewerDetailCardHeader .starRating {
  display: inline-flex !important;
}

.moreReviews .reviewCard .reviewerDetailCardHeader .starRating span {
  color: #787878;
}

.stars {
  margin: 0;
  padding: 0;
}

.reviewerDetailCardHeader .full-star,
.reviewerDetailCardHeader .half-star,
.reviewerDetailCardHeader .empty-star {
  margin-right: 2px;
  margin-left: 1px;
}

.starRating .full-star,
.starRating .half-star,
.starRating .empty-star {
  vertical-align: text-top !important;
  margin-left: 1px;
  margin-right: 2px;
}

.full-star,
.half-star,
.empty-star {
  width: 17px;
  height: 17px;
  vertical-align: text-bottom;
}

.full-star {
  background-position: 129px -246px;
}

.half-star {
  background-position: 99px -246px;
}

.empty-star {
  background-position: 72px -246px;
}

.moreReviews .reviewCard .ratingList div {
  font-size: 12px;
  color: #787878;
  margin: 0 5px 0 0;
}

.moreReviews .reviewCard .ratingList div b {
  color: #282828;
}

.moreReviews .reviewCard .ratingList {
  display: grid;
  grid-template-columns: repeat(6, max-content);
  justify-items: start;
}

.moreReviews .reviewCard .ratingList div:not(:first-child) b::before {
  vertical-align: middle;
}

.moreReviews .reviewCard .ratingList div:not(:first-child) b::before {
  color: #787878;
  font-size: 16px;
  margin-left: 4px;
  margin-right: 6px;
  content: "\2022 ";
}

.moreReviews .reviewCard p {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.71;
  color: #282828;
}

.moreReviews .reviewCard .redirectreviewCard {
  margin-left: -20px;
  margin-right: -20px;
  margin-bottom: -20px;
  padding: 20px;
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: #fafbfc;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px;
}

.moreReviews .reviewCard .redirectreviewCard a {
  font-size: 14px;
  font-weight: 700;
  color: #ff4e53;
}

.moreReviews .reviewCard .redirectreviewCard p {
  font-size: 14px;
  font-weight: 700;
  color: #ff4e53;
  cursor: pointer;
}

.moreReviews .reviewCard .redirectreviewCard a .urlIcon {
  margin-left: 5px;
}

.moreReviews .reviewCard .redirectreviewCard a .urlIcon {
  width: 13px;
  height: 14px;
  background-position: 302px -375px;
  vertical-align: text-bottom;
}

.moreReviews .reviewCard .verifiedSpan {
  margin-top: 0;
}

.reviewerHeaderContent.reviewLandingHeader span:first-child:before {
  content: none;
}

.starRating ul.stars {
  margin-left: 5px;
}

.moreReviews .reviewCard .ratingList .full-star {
  margin-left: 5px;
  margin-right: 5px;
}

.reviewCard p span.reviewCate {
  color: #282828 !important;
  font-weight: 500;
}

.reviewContentBox {
  max-height: 110px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.redirectreviewCard.readMoreIcon p .showMoreIcon {
  content: " ";
  background: url(../../images/master_sprite.webp);
  width: 12px;
  height: 21px;
  background-position: 591px -71px;
  transform: rotate(90deg) scale(0.7);
  vertical-align: top;
}

.reviewValue {
  width: 19px;
  display: inline-block;
  line-height: 18px;
}

/* Gallery Page Related */
.photoGallery .row {
  margin: 0;
}

.photoGallery h2.row {
  margin-bottom: 20px;
}

.photoGallery .picture {
  flex-basis: 23.7%;
  border-radius: 50%;
  margin-right: 20px;
  border-radius: 4px;
  border: var(--border-line);
  overflow: hidden;
}

.photoGallery .picture img {
  height: 206px;
  display: block;
  margin: 0 auto;
}

.photoGallery .picture:last-child,
.photoGallery .picture:nth-of-type(4n) {
  margin-right: 0;
}

.galleryImageList {
  margin: 0;
  margin-bottom: 10px;
}

.galleryImage {
  border-radius: 4px;
  border: var(--border-line);
  flex-basis: 24%;
  margin-right: 10px;
  margin-bottom: 10px;
  overflow: hidden;
}

.galleryImage:nth-of-type(4n) {
  margin-right: 0;
}

.galleryImage img {
  display: block;
  width: 100%;
  height: 140px;
}

.collegeVideos {
  margin: 0;
}

.collegeVideos .collegeVideoCard {
  flex-basis: 31.5%;
  margin-right: 20px;
  margin-bottom: 20px;
  position: relative;
  border: var(--border-line);
  overflow: hidden;
  border-radius: 4px;
}

.collegeVideos .collegeVideoCard:nth-of-type(3n) {
  margin-right: 0;
}

.collegeVideos .collegeVideoCard img {
  height: 178px;
  display: block;
  width: 100%;
}

.playBtnIcon {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  z-index: 2;
  background: url(../../images/playBtn.png) no-repeat;
}

/* Contact Us Section */
.contsctUs iframe {
  width: 100%;
  display: block;
  height: 408px;
  border: none;
}

.contsctUs p {
  font-size: 16px;
  line-height: 20px;
  padding-bottom: 20px;
  font-weight: 500;
}

.contsctUs ul {
  margin: 0;
  padding: 0;
}

.contsctUs ul li {
  font-size: 14px;
  line-height: 20px;
  padding-bottom: 10px;
  display: flex;
}

.contsctUs ul li .address {
  flex-basis: calc(100% - 30px);
}

.contsctUs ul li:last-child {
  padding-bottom: 0;
}

.contsctUs ul li a {
  color: var(--primary-font-color);
}

.contsctUs ul li:before {
  display: none;
}

.contsctUs ul .spriteIcon {
  width: 23px;
  height: 22px;
  vertical-align: middle;
  margin-right: 8px;
}

/* Sidebar section */
.collegeReviewPage .sideBarSection .sidebarHeading,
.courseAndFeePage .sideBarSection .sidebarHeading,
.collegeListpageBody .sideBarSection .sidebarHeading {
  padding: 10px 18px;
}

.collegeReviewPage .sideBarSection .sidebarImgDiv,
.courseAndFeePage .sideBarSection .sidebarImgDiv,
.collegeListpageBody .sideBarSection .sidebarImgDiv {
  flex-basis: 56px;
  margin-right: 10px;
}

.collegeReviewPage .sideBarSection .sidebarImgDiv img,
.courseAndFeePage .sideBarSection .sidebarImgDiv img,
.collegeListpageBody .sideBarSection .sidebarImgDiv img {
  max-height: 56px;
}

.collegeReviewPage .sideBarSection .sidebarTextLink,
.courseAndFeePage .sideBarSection .sidebarTextLink,
.collegeListpageBody .sideBarSection .sidebarTextLink {
  flex-basis: calc(100% - 66px);
}

.collegeReviewPage .sideBarSection .sidebarTextLink p,
.courseAndFeePage .sideBarSection .sidebarTextLink p,
.collegeListpageBody .sideBarSection .sidebarTextLink p {
  font-size: 12px;
  line-height: 16px;
  -webkit-line-clamp: 2;
}

.collegeReviewPage .sideBarSection .sidebarTextLink p .subtext,
.courseAndFeePage .sideBarSection .sidebarTextLink p .subtext,
.collegeListpageBody .sideBarSection .sidebarTextLink p .subtext {
  font-size: 11px;
  -webkit-line-clamp: 1;
}

.collegeReviewPage .sideBarSection .sidebarTextLink .applyText,
.courseAndFeePage .sideBarSection .sidebarTextLink .applyText,
.collegeListpageBody .sideBarSection .sidebarTextLink .applyText {
  line-height: 24px;
  margin-top: 5px;
}

.collegeReviewPage .sidebarAds,
.courseAndFeePage .sidebarAds,
.collegeListpageBody .sidebarAds {
  padding: 0;
}

.sideBarSection p.listCard {
  font-weight: 500;
  font-size: 15px;
  line-height: 24px;
}

.sideBarSection .sidebarTextLink p {
  font-size: 14px;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

/* Other */
.collegeListpageBody .getSupport {
  display: none;
}

.courseAndFeePage .componentRatio .col-md-7 {
  padding-right: 10px;
}

.lg-pr-0 {
  padding-right: 0;
}

/* Filter */
.mobileSortandFilter,
.mobileFilterSection,
.mobileSortSection {
  display: none;
}

.filterSection {
  padding: 20px;
  background-color: #fff;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
}

.filterSection .filterSectionSelection {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.filterSection .filterSectionSelection button {
  padding: 8px 12px;
  border: solid 1px #d8d8d8;
  border-radius: 24px;
  position: relative;
  background-color: transparent;
}

.filterSection .filterSectionSelection button .closeIcon {
  background-position: -91px -237px;
  width: 15px;
  height: 15px;
}

.filterSection .filterSectionForm {
  margin-bottom: 15px;
}

.filterSection .searchBar {
  position: relative;
  margin-bottom: 10px;
}

.filterSection .searchBar input {
  width: 100%;
  border-radius: 3px;
  border: solid 1px #eaeaea;
  background-color: #f5f5f5;
  height: 40px;
  padding-left: 36px;
}

.filterSection .searchBar .searchIcon {
  background-position: -151px -210px;
  position: absolute;
  top: 8px;
  left: 10px;
}

.filterSection .filterRow {
  margin: 0;
  justify-content: space-between;
  margin-bottom: 10px;
  align-items: center;
}

.filterSection .filterRow span {
  font-size: 14px;
  font-weight: 400;
  color: #282828;
}

.filterSection .filterRow a {
  font-size: 12px;
  font-weight: 400;
  color: #ff4e53;
  cursor: pointer;
}

.filterSection .filterRadioButtons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.filterSection .filterRadioButtons .filterRadioContainer {
  padding: 2px 9px 1px;
  border-radius: 3px;
  border: solid 1px #282828;
  position: relative;
  height: 29px;
}

.filterSection .filterRadioButtons .filterRadioContainer input {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: calc(100% + 1px);
  z-index: 10;
  padding: 0;
  margin: 0;
  top: 0;
  left: 0;
}

.filterSection .filterRadioButtons .filterRadioContainer label {
  cursor: pointer;
  position: absolute;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  top: -1px;
  left: -1px;
  text-align: center;
  line-height: 1.8;
  border-radius: 3px;
  font-size: 15px;
}

.filterSection .filterRadioButtons .filterRadioContainer input:checked+label {
  background: #ff4e53;
  font-size: 15px;
  font-weight: 600;
  color: #fff;
}

.filterSection .filterCheckButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  border-bottom: none;
}

.filterSection .filterCheckButtons .filterCheckContainer input {
  margin: 0 2px 0 0;
  vertical-align: middle;
}

.filterSection .filterCheckButtons .filterCheckContainer label {
  margin: 0 0 0 5px;
  font-size: 14px;
  font-weight: 400;
  color: #282828;
}

.filterSection .filterSectionForm .tabs {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  text-decoration: none;
  list-style-type: none;
  margin-bottom: 10px;
  align-items: center;
}

.filterSection .filterSectionForm .tabs .tab-link {
  padding: 0 6px 4px;
  border-radius: 3px;
  border: solid 1px #282828;
  position: relative;
  height: 29px;
  font-size: 15px;
  font-weight: 400;
  text-align: center;
  color: #282828;
  cursor: pointer;
  text-align: center;
}

.filterSection .filterSectionForm .tabs .tab-link.current {
  background-color: #ff4e53;
  font-size: 15px;
  font-weight: 600;
  text-align: center;
  color: #fff;
  border: 1px solid #ff4e53;
}

.filterSection .filterSectionForm .filterContentDiv .tab-content.current {
  display: flex !important;
  position: relative;
}

.filterSection .filterSectionForm .filterContentDiv .tab-content {
  display: none !important;
}

.filterSection .filterCheckButtons .filterCheckContainer input {
  width: 16px;
  padding: 0;
  height: 16px;
}

li.filterCheckContainer {
  display: inline;
  margin: 0 8px 0 0;
  white-space: nowrap;
}

.filterCheckContainer.hideCourse,
.filterCheckContainer.courseViewLess,
.filterCheckContainer.hideBranch,
.filterCheckContainer.branchViewLess {
  display: none;
}

.filterCheckContainer.courseMore,
.filterCheckContainer.branchMore {
  margin: 8px;
  color: #3d8ff2;
  cursor: pointer;
  font-weight: 500;
}

#branchViewLess {
  position: absolute;
  right: 23px;
  color: #3d8ff2;
  top: 1px;
  cursor: pointer;
  font-weight: 500;
}

#courseViewLess {
  right: 23px;
  color: #3d8ff2;
  top: 1px;
  cursor: pointer;
  margin-left: 9px;
  font-weight: 500;
}

.courseSelection {
  width: 100% !important;
  top: -20px !important;
}

.courseSelection ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  max-height: 150px;
  padding: 0 !important;
  overflow: auto;
  z-index: 9999;
  border: solid 1px #eaeaea;
  background-color: #f5f5f5;
}

.courseSelection ul li a {
  display: block;
  background: #fff;
  color: #333;
  text-align: left;
  padding: 6px 0 6px 6px;
  font-size: 14px;
  border-bottom: #eaeaea 1px solid;
}

.filterCheckButtons .form-group ul {
  padding-left: 0;
  margin: 0;
}

.filterSearch {
  width: 21px;
  height: 23px;
  background-position: 535px -72px;
  vertical-align: middle;
}

.filterSection .searchBar .filterSearch {
  background-position: -151px -210px;
  position: absolute;
  top: 8px;
  left: 10px;
}

.closeIconHover {
  top: 5px;
  right: 5px;
  position: absolute;
  z-index: 10;
  width: 15px;
  height: 14px;
  margin-left: 8px;
  background-position: 652px -237px;
  vertical-align: middle;
  cursor: pointer;
}

/* Course and fees page course card div */
.courseTypeDiv {
  background: var(--color-white);
  border-radius: 4px;
  padding: 10px 0;
  border: var(--border-line);
}

.courseTypeDiv .row {
  margin: 0;
  justify-content: space-between;
  align-items: flex-end;
}

.courseTypeDiv .row:first-child {
  padding-bottom: 15px;
  border-bottom: var(--border-line);
  margin-bottom: 15px;
  align-items: flex-start;
}

.courseTypeDiv .courseTypeTitle {
  flex-basis: calc(100% - 160px);
}

.courseTypeDiv .textBlue {
  color: var(--anchor-textclr);
  cursor: pointer;
  padding: 0;
  border: none;
  background: 0 0;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
}

.courseTypeDiv .courseName {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
}

.courseTypeDiv .courseName a {
  color: var(--primary-font-color);
}

.courseTypeDiv p {
  font-size: 15px;
  line-height: 24px;
  padding-left: 2px;
}

.courseTypeDiv p span {
  color: var(--anchor-textclr);
}

.courseTypeDiv .getFee {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.courseTypeDiv .courseInfo {
  flex-basis: 75%;
}

.courseTypeDiv .courseInfo .tooltipIconText {
  border: var(--border-line);
}

.courseTypeDiv .courseInfo .tooltipIconText:before {
  border: var(--border-line);
  border-right: none;
  border-bottom: none;
}

.courseTypeDiv .courseInfo .tooltipIcon.tooltipAngle {
  border: none;
  width: 18px;
  height: 14px;
  margin-right: 0;
  background-position: 95px -323px;
}

.courseTypeDiv .courseInfo .col-md-2,
.courseTypeDiv .courseInfo .col-md-3,
.courseTypeDiv .courseInfo .col-md-4 {
  padding: 0;
}

.courseTypeDiv .courseInfo .row {
  padding: 0;
  border: none;
  margin: 0;
  justify-content: flex-start;
}

.courseTypeDiv .courseInfo .col-md-3 {
  flex-basis: 20%;
  max-width: 20%;
}

.courseTypeDiv .courseInfo .spriteIcon {
  width: 20px;
  height: 17px;
  vertical-align: middle;
  margin-right: 3px;
}

.courseTypeDiv .courseStructure {
  color: #787878;
  font-size: 12px;
  line-height: 24px;
}

.courseTypeList .summary {
  padding-bottom: 15px;
}

.courseTypeList .loadMoreList,
.collegeListpageBody .loadMoreList {
  font-size: 14px;
  line-height: 24px;
  padding: 5px;
  border-radius: 3px;
  border: 1px solid var(--color-red);
  background: var(--color-white);
  max-width: 460px;
  margin: 0 auto;
  margin-bottom: 20px;
  color: var(--color-red);
  text-align: center;
  font-weight: 500;
  cursor: pointer;
}

.clgCourseAndFeeSection {
  max-height: unset;
}

.courseTypeMaster {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.courseTypeMaster.pageInfo {
  max-height: 860px;
}

.courseTypeMaster h2 {
  line-height: 28px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  text-transform: uppercase;
  position: relative;
}

.courseTypeMaster .courseTypeDiv:last-child {
  margin-bottom: 0;
  border-bottom: 0;
  padding-bottom: 0 !important;
}

.courseTypeMaster .primaryBtn {
  min-width: 136px;
  text-align: center;
  padding: 6px 12px;
}

.courseTypeMaster .row {
  align-items: center;
}

.courseTypeMaster .courseMain {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  min-height: 86px;
}

.courseTypeMaster .courseMain .courseDetails .courseHeading {
  font-size: 18px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.6;
  letter-spacing: 0.3px;
  text-align: left;
  color: #0d3d63;
  margin-bottom: 5px;
  background-color: transparent;
  padding-left: 0;
  text-transform: none;
}

.courseTypeMaster .courseMain .courseDetails .courseHeading a {
  color: #0d3d63;
}

.courseTypeMaster .courseMain .courseDetails .courseRating {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 10px;
}

.courseTypeMaster .courseMain .courseDetails .courseRating .textBlue {
  color: #3d8ff2;
}

.courseTypeMaster .courseMain .courseDetails .courseItems {
  margin-bottom: 5px;
  margin-top: 3px;
}

.courseTypeMaster .courseMain .courseDetails .courseItems span {
  font-size: 15px;
  font-weight: 400;
  color: #282828;
}

.courseTypeMaster .courseMain .courseDetails .courseExamAccepted {
  font-size: 15px;
  font-weight: 400;
  color: #282828;
  max-width: 350px;
}

.courseTypeMaster .courseMain .courseDetails .courseExamAccepted span,
.courseExams .courseExamAccepted span {
  font-weight: 500;
  color: #3d8ff2;
}

.courseTypeMaster .courseMain .courseData {
  position: relative;
  flex-grow: 1;
}

.courseTypeMaster .courseMain .courseData div {
  text-align: end;
  font-size: 15px;
  font-weight: 400;
  color: #282828;
}

.courseTypeMaster .courseMain .courseData div:first-child {
  padding-bottom: 50px;
}

.courseTypeMaster .courseMain .courseData .courseFee {
  font-size: 18px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.44;
  letter-spacing: 0.3px;
  text-align: left;
  color: #ff4e53;
}

.courseTypeMaster .courseMain .courseData .courseDuration {
  font-size: 15px;
  font-weight: 400;
  color: #787878;
}

.courseTypeMaster .courseMain .courseData .avgFees {
  display: block;
  margin-top: 3px;
}

.courseTypeMaster .courseMain .courseData .btnDiv {
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  gap: 20px;
  max-height: 36px;
}

.courseTypeMaster .courseMain .courseData .btnDiv .interestedButton {
  border-radius: 3px;
  border: solid 1.3px #ff4e53;
  background-color: rgba(255, 78, 83, 0.1);
  font-size: 14px;
  font-weight: 700;
  color: #ff4e53;
}

.courseTypeMaster .allCoursesHeading {
  padding: 8px 20px;
  background: #f5f5f5;
  margin: 10px 0 0;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.73;
  letter-spacing: 0.3px;
  text-align: left;
  color: #282828;
  text-transform: none;
}

.courseTypeDiv {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
  border: none;
  border-bottom: 1px solid #d8d8d8;
}

.courseTypeDiv .courseNameAndEligibility {
  flex-basis: 48%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}

.courseTypeDiv .courseNameAndEligibility p,
.courseTypeDiv .courseNameAndEligibility h3 {
  font-size: 15px;
  font-weight: 500;
  color: #0d3d63;
  line-height: 1.63;
  margin-bottom: 10px;
  max-width: 300px;
}

.courseTypeDiv .courseNameAndEligibility p a,
.courseTypeDiv .courseNameAndEligibility h3 a {
  color: #0d3d63;
}

.courseTypeDiv .detailedFee {
  flex-basis: 30%;
}

.courseTypeDiv .courseNameAndEligibility a {
  font-size: 16px;
  font-weight: 500;
  margin-top: 3px;
}

.courseTypeDiv .courseFee {
  font-size: 18px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.44;
  letter-spacing: 0.3px;
  text-align: left;
  color: #ff4e53;
  margin-bottom: 5px;
}

.courseTypeDiv .courseDuration {
  font-size: 15px;
  font-weight: 400;
  color: #787878;
}

.courseTypeDiv .applyNowButtonContainer {
  position: relative;
  align-self: flex-start;
  flex-basis: 20%;
  text-align: right;
}

.courseTypeDiv .applyNowButtonContainer button {
  width: 145px;
  height: 36px;
  border-radius: 3px;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 23px 5px 5px;
  text-align: center;
  color: var(--color-white);
  border: none;
  background-color: #0966c2;
}

.courseTypeDiv .applyNowButtonContainer .writeIcon {
  position: absolute;
  top: 8px;
  right: 8px;
}

.courseListContainer.pageInfo {
  max-height: 1000px;
}

.courseListContainer {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.courseListContainer h2 {
  font-weight: 500;
  line-height: 28px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 0;
  background: #f5f5f5;
  position: relative;
}

.courseListContainer .courseListCard {
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.courseListContainer .courseListCard .courseDetailsRow {
  display: flex;
}

.courseListContainer .courseListCard .courseDetailsRow .leftDetails {
  flex-basis: 70%;
}

.courseListContainer .courseListCard .courseDetailsRow .leftDetails h3,
.courseListContainer .courseListCard .courseDetailsRow .leftDetails h3 a {
  font-size: 15px;
  font-weight: 600;
  color: #0d3d63;
  margin-bottom: 10px;
  line-height: 1.6;
}

.courseListContainer .courseListCard .courseDetailsRow .leftDetails .courseRating {
  margin-right: 8px;
}

.courseListContainer .courseListCard .courseDetailsRow .leftDetails .courseRating .textBlue {
  font-size: 15px;
  font-weight: 600;
  color: #3d8ff2;
}

.courseListContainer .courseListCard .courseDetailsRow .leftDetails .courseRating .full-star {
  vertical-align: text-top;
}

.courseListContainer .courseListCard .courseDetailsRow .leftDetails .courseExamAccepted {
  margin-top: 10px;
}

.courseListContainer .courseListCard .courseDetailsRow .leftDetails .courseExamAccepted span {
  font-size: 15px;
  font-weight: 500;
  color: #3d8ff2;
}

.courseListContainer .courseListCard .courseDetailsRow .rightDetails {
  flex-basis: 30%;
  text-align: right;
}

.courseListContainer .courseListCard .courseDetailsRow .rightDetails p {
  font-size: 18px;
  font-weight: 600;
  color: #ff4e53;
}

.courseListContainer .courseListCard .courseDetailsRow .rightDetails p span {
  font-size: 15px;
  font-weight: 400;
  color: #787878;
}

.courseListContainer .courseListCard .courseDetailsRow .rightDetails button {
  font-size: 15px;
  font-weight: 700;
  color: #3d8ff2;
  background: 0 0;
  border: none;
  margin-top: 4px;
}

.courseListContainer .courseListCard .specialisationRow {
  margin-top: 10px;
}

.courseListContainer .courseListCard .specialisationRow ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
  display: inline-flex;
  flex-wrap: wrap;
  vertical-align: middle;
  gap: 8px;
}

.courseListContainer .courseListCard .specialisationRow ul li {
  font-size: 15px;
  font-weight: 600;
}

.courseListContainer .courseListCard .specialisationRow ul li a {
  color: #0d3d63;
  font-weight: 500;
}

.courseListContainer .courseListCard .specialisationRow ul li::before {
  content: none;
}

.courseListContainer .courseListCard .specialisationRow ul li:first-child {
  color: #282828;
  font-weight: 500;
  font-size: 16px;
}

.courseListContainer .courseListCard .specialisationRow ul li:last-child {
  color: #3d8ff2;
  cursor: pointer;
}

.courseListContainer .courseListCard .buttonRow {
  display: flex;
  gap: 20px;
  margin-top: 15px;
}

.courseListContainer .courseListCard .buttonRow .admissionButton {
  border-radius: 3px;
  background-color: #0966c2;
}

.courseAndFeeDiv.pageInfo {
  max-height: 500px;
}

.courseTypeMaster .courseMain .courseDetails .courseHeading {
  padding: 0 !important;
}

.courseTypeDiv .courseName {
  max-width: 300px;
}

.courseExamAccepted .moreCourse span:not(:last-child):after,
.admissionHighlights .moreCourse span:not(:last-child):after,
.scholarshipTable .moreCourse span:not(:last-child):after,
.collegeHeighlights .moreCourse span:not(:last-child):after {
  content: ",";
}

.courseListCard .specialisationRow li:not(:last-child)::after {
  content: "|";
  color: #0d3d63;
  margin-left: 5px;
}

.courseListCard .specialisationRow li:nth-last-child(2)::after {
  content: none;
}

.courseListCard .specialisationRow li:first-child::after {
  content: none;
}

.whiteInterestedIcon {
  width: 20px;
  height: 20px;
  background-position: -139px -594px;
  vertical-align: middle;
}

.courseListContainer .courseListCard .specialisationRow ul li {
  line-height: 20px;
}

.infoBroucherButton {
  flex-basis: 161px;
}

.moreCoursesLinkContainer {
  text-align: center;
  margin-top: 20px;
}

a.moreCoursesLink {
  border-radius: 3px;
  border: solid 1px #ff4e53;
  background-color: rgba(255, 78, 83, 0.1);
  padding: 6px 36px 6px 35px;
  font-size: 14px;
  font-weight: 700;
  color: #ff4e53;
  text-decoration: none;
  cursor: pointer;
  display: inline-block;
  max-width: 185px;
  max-height: 36px;
  line-height: 22px;
}

.moreCoursesLink .urlIcon {
  margin-left: 8px;
  margin-bottom: 2px;
}

.courseListContainer .courseListCard .specialisationRow ul li:not(:first-child):after {
  content: "|";
}

.courseListContainer .courseListCard .specialisationRow ul li:last-child::after {
  content: none;
}

.primaryBtn:hover,
a.primaryBtn:hover,
button.primaryBtn:hover {
  box-shadow: none;
}

.courseDetailsRow .leftDetails .courseItems span:not(:first-child)::before {
  content: "\2022";
  margin-left: 8px;
  margin-right: 4px;
}

/* Program Page */
.programPage .feesIcons,
.programPage .examTypeIcon,
.programPage .seatsIcon,
.programPage .blueLabel,
.programPage .courseDuratioIcon,
.programPage .modeIcon {
  width: 20px;
  height: 17px;
  margin-right: 15px;
  vertical-align: middle;
}

.programPage .textBlue {
  color: var(--anchor-textclr);
  cursor: pointer;
}

.programInfoDiv .feesIcons {
  background-position: -448px -878px;
  width: 24px;
  height: 24px;
  margin-right: 20px;
}

.programInfoDiv .courseDuratioIcon {
  background-position: -346px -878px;
  width: 24px;
  height: 24px;
  margin-right: 20px;
}

.programInfoDiv .seatsIcon {
  margin-right: 20px;
}

.programInfoDiv .modeIcon {
  background-position: -414px -882px;
  width: 24px;
  height: 17px;
  margin-right: 20px;
}

.programInfoDiv .eligibilityIcon {
  background-position: -380px -878px;
  width: 24px;
  height: 24px;
  margin-right: 20px;
  vertical-align: middle;
}

.programInfoDiv .approvalIcon {
  background-position: -617px -324px;
  width: 10px;
  height: 16px;
  margin-right: 20px;
  vertical-align: middle;
}

.programInfoDiv table {
  color: #282828;
  table-layout: fixed;
}

.programInfoDiv table td {
  padding-top: 20px;
  padding-bottom: 20px;
}

.programInfoDiv table td:first-child {
  padding-left: 20px;
}

.programInfoDiv table td a {
  font-size: 14px;
  cursor: pointer;
}

.programInfoDiv.pageData.pageInfo {
  color: #282828;
}

.programInfoDiv.pageData.pageInfo p {
  color: #282828;
}

.pageData.programInfoDiv table tr td:last-child {
  min-width: 534px;
  text-align: left;
  padding-left: 39px;
  font-size: 14px;
  font-weight: 500;
  color: #282828;
}

.programInfoDiv .feesIcons {
  background-position: -448px -878px;
  width: 24px;
  height: 24px;
  margin-right: 20px;
}

.programInfoDiv .courseDuratioIcon {
  background-position: -346px -878px;
  width: 24px;
  height: 24px;
  margin-right: 20px;
}

.programInfoDiv .seatsIcon {
  margin-right: 20px;
}

.programInfoDiv .modeIcon {
  background-position: -414px -882px;
  width: 24px;
  height: 17px;
  margin-right: 20px;
}

.programInfoDiv .eligibilityIcon {
  background-position: -380px -878px;
  width: 24px;
  height: 24px;
  margin-right: 20px;
  vertical-align: middle;
}

.programInfoDiv .approvalIcon {
  background-position: -617px -324px;
  width: 10px;
  height: 16px;
  margin-right: 20px;
  vertical-align: middle;
}

.programInfoDiv table {
  color: #282828;
  table-layout: fixed;
}

.programInfoDiv table td {
  padding-top: 20px;
  padding-bottom: 20px;
}

.programInfoDiv table td:first-child {
  padding-left: 20px;
}

.programInfoDiv table td a {
  font-size: 14px;
  cursor: pointer;
}

.programInfoDiv.pageData.pageInfo {
  color: #282828;
}

.programInfoDiv.pageData.pageInfo p {
  color: #282828;
}

.pageData.programInfoTable.centerAlignLastCell table td {
  min-width: unset;
}

.pageData h2 {
  color: #282828;
}

.programInfoDiv .latestUpdates {
  padding: 0;
}

.programInfoDiv .latestUpdates li,
.programInfoDiv .latestUpdates a,
.programInfoDiv .latestUpdates span {
  font-size: 14px;
  font-weight: 500;
}

.programInfoDiv p {
  padding-bottom: 20px;
}

.programInfoDiv p:last-child {
  padding-bottom: 0;
}

.programInfoTable table thead td {
  padding: 10px 0 10px 20px;
  text-align: left;
  font-size: 15px;
}

.programInfoTable table thead td p {
  font-weight: 500;
  line-height: 1.85;
  color: #fff;
  text-align: center;
  padding: 0;
}

.programInfoTable table thead td p:first-child {
  font-size: 15px;
}

.programInfoTable table thead td p:last-child {
  font-size: 13px;
}

.programInfoTable table tbody td {
  padding: 16px;
  padding-left: 20px;
  text-align: left;
  font-size: 15px;
}

.programInfoTable table tbody td p {
  font-weight: 500;
  line-height: 1.73;
  text-align: left;
  padding: 0;
}

.programInfoTable table tbody td p:first-child {
  font-size: 15px;
  color: #282828;
}

.programInfoTable table tbody td p:last-child {
  font-size: 13px;
  color: #787878;
}

.centerAlignLastCell table thead td:last-child {
  text-align: center;
  padding-left: 0;
}

.centerAlignLastCell table tbody td:last-child {
  text-align: center;
  color: #282828;
}

.centerAlignButton {
  display: flex;
  justify-content: center;
}

.centerAlignButton .downloadButton {
  width: 262px;
  margin: 0;
  margin-top: 20px;
}

.programInfoTable .primaryBtn {
  width: 152px;
  margin-top: 8px;
}

table.importantEventsAndDatesTable tbody td:last-child {
  padding: 20px 50px;
}

/* breadcrumbs */
.breadcrumbDiv {
  background-color: unset;
}

.breadcrumbDiv .homeIcon {
  height: 15px;
  width: 23px;
  background-position: -243px -596px;
  vertical-align: text-bottom;
}

.breadcrumbDiv .homeIcon::after {
  right: -1px;
  background-position: 707px -150px;
}

.breadcrumbDiv li {
  color: #000 !important;
}

.breadcrumbDiv li a {
  color: #000 !important;
  font-weight: 700;
}

.breadcrumbDiv li a::after {
  background: url(../../images/master_sprite.webp) !important;
  background-position: 707px -150px !important;
}

/* Scholarship */
.scholarshipTable.mobileOnly+.readMoreDiv {
  display: none;
}

.scholarshipTable.desktopOnly {
  margin-bottom: 30px;
}

.scholarshipTable.desktopOnly .table-responsive .table-responsive {
  margin-bottom: 0;
}

.scholarshipTable.desktopOnly .moreCoursesLinkContainer {
  text-align: center;
}

.scholarshipTable.desktopOnly .moreCoursesLink {
  border-radius: 3px;
  border: solid 1px #ff4e53;
  background-color: rgba(255, 78, 83, 0.1);
  padding: 6px 36px 6px 35px;
  font-size: 14px;
  font-weight: 700;
  color: #ff4e53;
  text-decoration: none;
  cursor: pointer;
  display: inline-block;
  max-width: 185px;
  min-height: 36px;
}

.scholarshipTable.desktopOnly .moreCoursesLink .urlIcon {
  margin-left: 8px;
}

.hideExam,
.programHideExam {
  display: none;
}

.examMore {
  cursor: pointer;
  color: #3d8ff2 !important;
}

.hideSpecialization {
  display: none;
}

/* CI Page */
.ciPageTable td:nth-child(1) {
  min-width: 300px;
}

.ciPageTable td:nth-child(2) {
  min-width: 211px;
}

.ciPageTable td:nth-child(3) {
  min-width: 230px;
}

.ciPageTable+.readMoreDiv {
  height: 36px;
}

.ciPageTable+.readMoreDiv .readMoreInfo {
  line-height: 32px;
}

.ciPageList .allCoursesHeading {
  font-size: 18px;
}

.ciPageList .courseTypeDiv:last-child {
  border-bottom: none;
  padding-bottom: 0 !important;
}

.ciPageList .courseTypeDiv .courseNameAndEligibility p {
  margin-bottom: 8px;
}

.ciPageList .courseTypeDiv .courseNameAndEligibility .courseItems {
  font-size: 15px;
  font-weight: 400;
  line-height: 1.6;
  color: #282828;
  margin-bottom: 0;
}

.ciPageList .courseTypeDiv .courseNameAndEligibility .courseItems span {
  color: inherit;
}

.ciPageList .courseTypeDiv .detailedFee .viewDetailedFee {
  font-size: 15px;
  font-weight: 500;
  line-height: 1.73;
}

/* Other */
.readMoreInfo,
.showMoreCourseCard {
  padding: 0;
}

.readMoreDiv,
.showMoreCourseWrap {
  max-height: 36px;
  padding: 5px;
}

.faq_section .faq_answer {
  background: #fff;
  color: #282828;
  padding-left: 20px !important;
}

.readMoreDiv:after,
.showMoreCourseWrap:after {
  top: -45px;
}

.pageData .showMoreCourseCard {
  font-weight: var(--font-semibold);
  color: var(--color-red);
  font-size: 15px;
  line-height: 26px;
  cursor: pointer;
  text-decoration: none;
}

/* Cutoff card */
section.pageData.pageInfo.cutOffDetailSection {
  max-height: 1000px;
}

.cutOffDetailSection .cutOffDetailSectionPara {
  border-bottom: 1px solid #d8d8d8;
  margin-bottom: 20px;
}

.cutOffCard {
  text-align: center;
  border-bottom: 1px solid #d8d8d8;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.cutOffCard .cutOffCardHeading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.cutOffCard .cutOffMainHeading {
  text-align: left;
}

.cutOffCard .cutOffCardHeading h3 {
  font-size: 15px;
  font-weight: 400;
  color: #0d3d63;
  padding-bottom: 0;
}

.cutOffCard .cutOffCardHeading select {
  padding: 6px 15px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  width: 111px;
  height: 36px;
  background-position: 95% 14px !important;
  font-size: 14px;
  font-weight: 400;
  border-radius: 3px;
}

.cutOffCard .cutOffCardHeading select:first-child {
  margin-right: 5px;
  width: fit-content;
  padding-right: 25px;
}

.cutOffCard .primaryBtn {
  width: 262px;
  height: 36px;
  margin-top: 20px;
  line-height: 22px;
}

.cutOffCard .primaryBtn .whiteDownloadIcon {
  background-position: 233px -355px;
}

.cutOffCard .cutoffMatserType {
  padding-bottom: 20px;
  border-bottom: 1px solid #d8d8d8;
  margin-bottom: 20px;
}

.cutOffCard .cutoffMatserType:last-child {
  padding-bottom: 0;
  border-bottom: none;
  margin-bottom: 0;
}

.cutOffCard .primaryBtn {
  margin-bottom: 0;
}

.cutoffLimitCardIntable {
  max-height: 336px;
  overflow: hidden;
}

.cutoffTableMargin:not(:last-of-type) {
  margin-bottom: 20px;
}

/* Placement Page */
.placementDiv {
  color: #282828;
}

.placementDiv .placementList {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;
}

.placementDiv .placementList li {
  font-size: 15px;
  font-weight: 400;
  line-height: 1.73;
  color: #282828;
}

.placementDiv .placementList li::before {
  background: 0 0;
  position: static;
}

.placementDiv .placementList li:not(:first-child)::before {
  content: "|";
  margin-right: 5px;
  margin-left: 5px;
}

.placementDiv .viewAllCompanies {
  text-decoration: none;
  cursor: pointer;
}

.placementDiv .centerAlignButton {
  border-top: 1px solid #d8d8d8;
  margin-top: 20px;
}

.placementDiv .listHeading {
  font-size: 15px;
  line-height: 1.73;
  color: #282828;
  padding-bottom: 10px;
}

.pageData .listHeading {
  padding-bottom: 0;
}

.programInfoTable .primaryBtn {
  width: 152px;
  margin-top: 8px;
}

.sidebarAds .appendAdDiv {
  width: 300px;
  height: 250px;
  display: block;
  margin: 0 auto;
  margin-bottom: 20px;
}

.cutOffCard .cutoffMatserType:last-child {
  padding-bottom: 0;
  border-bottom: none;
  margin-bottom: 0;
}

.cutOffCard .primaryBtn {
  margin-bottom: 0;
}

.newWriteIcon {
  background-position: -293px -1064px;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  vertical-align: middle;
}

.newAcademicIcon {
  background-position: -328px -1064px;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  vertical-align: middle;
}

.newRankIcon {
  background-position: -651px -373px;
  width: 20px;
  height: 16px;
  margin-right: 20px;
  vertical-align: middle;
}

.subNavDropDown a {
  display: inline-block !important;
}

.pageData .showMoreCourseCard {
  font-weight: var(--font-semibold);
  color: var(--color-red);
  font-size: 15px;
  line-height: 26px;
  cursor: pointer;
  text-decoration: none
}

.cutoffLimitCardIntable {
  max-height: 336px;
  overflow: hidden;
}

.cutoffTableMargin:not(:last-of-type) {
  margin-bottom: 20px;
}

/* .cutoffTableMargin+.showMoreCourseWrap{
  margin-bottom: 0px;
} */
.collegeRankings .rankTable img {
  max-height: fit-content;
}

.subNavDropDown:hover .caret {
  background-position: 651px -154px;
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
}

.subNavDropDown .subNavDropDownMenu {
  display: none;
  margin: 0;
  position: absolute;
  z-index: 2;
  padding: 5px 20px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.24);
  background-color: #fff;
}

.subNavDropDown .subNavDropDownMenu li {
  display: block;
  color: #787878;
  cursor: pointer;
}

.subNavDropDown .subNavDropDownMenu li:not(:last-child) {
  margin-bottom: 5px;
}

.subNavDropDown .subNavDropDownMenu li:hover {
  color: #ff4e53;
}

.subNavDropDown:hover .subNavDropDownMenu {
  display: block;
}

.btn_left,
.btn_right {
  z-index: 1;
}

.mobileSubNavDropDownMenu {
  display: none;
}

.subNavDropDown .subNavDropDownMenu li a {
  padding: 0;
  color: #787878;
}

.subNavDropDown .subNavDropDownMenu li a:hover {
  color: #ff4e53;
}

.collegeRankings .rankTable img {
  max-height: fit-content;
}

.rankTable thead tr {
  font-weight: 500;
}

.collegeRankings table.rankTable img {
  max-width: 100px;
  width: unset;
}

.readMoreDiv {
  padding-top: 6px;
}

.collegeRelataedLinks .caret {
  padding: 0;
  border: 0;
  line-height: unset;
}

.subNavActive {
  color: #ff4e53 !important;
}

/* clS issue css*/
.aside-college {
  min-height: 500px;
}

.newsSidebarSection {
  height: 540px;
  /* margin-top: 20px;*/
}

#liveApplicationForm {
  min-height: 360px;
}

.articleSidebarSection {
  height: 540px;
}

/*.lead-cta-cls{
  height: 190px;
  display: inline-block;
}*/
.row-cls {
  min-height: 3066px;
}

.programInfoDiv-cls {
  min-height: 432px;
}

.heroSectionSubDiv {
  height: 56px;
}

.lead-cta-cls-button {
  /*display: inline-block;
  height: 36px;*/
}

.filterHeading {
  font-size: 18px;
  line-height: 28px;
  padding: 8px 20px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  position: relative;
}

.collegeRelataedLinks .btn_right,
.collegeRelataedLinks .btn_left {
  position: absolute;
  width: 48px;
  height: 45px;
  background-color: #fff;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  top: 0;
  cursor: pointer;
}

.collegeRelataedLinks .btn_right {
  right: 0;
}

.getSupport {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 0px;
  z-index: 5;
  /*display: flex;*/
  gap: 18px;
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
  color: #282828;
  align-items: center;
  justify-content: center;
  display: none;
}

.getSupport .getSupport__subheading {
  display: inline-block;
}

.getSupport .button__row__container {
  display: flex;
  gap: 13px;
  align-items: center;
}

.getSupport .row {
  display: none;
}

.getSupport button {
  width: 49%;
  border-radius: 2px;
  font-size: 13px;
  padding: 6px 4px;
  width: 149px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.getSupport button:last-child {
  margin-left: 0;
}

.signupModal input[type="radio"]:checked:after {
  content: none;
}

/* college-compare css */
/* college compare button css */
.selectedCollege__heading_anchor:hover {
  text-decoration: none;
}

.collegeInfo {
  padding-right: 30px;
}

.collegeInfo__flexContent {
  justify-content: space-between;
}

.heroSection__leftSide {
  display: flex;
}

.heroSection__rightSide {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.compareIcon {
  background-position: -693px -148px;
  width: 36px;
  height: 36px;
  margin-bottom: 8px;
  background-color: var(--white-color);
  border-radius: 50%;
}

.compareText {
  font-size: 14px;
  font-weight: 700;
  line-height: 12px;
}

.compareCloseIcon {
  background-position: -11px -10px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}


.collegeCompare__container {
  animation: slideIn 1000ms ease-in-out;
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: var(--white-color);
  box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.1019607843);
  padding: 14px 0;
  letter-spacing: 0.3px;
  z-index: 11;
  display: none;
}

.collegeCompare__headingContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
}

.collegeCompare__headingContainer .compareCollege__heading {
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
}

@keyframes slideIn {
  from {
    transform: translateY(100vh);
  }

  to {
    transform: translateY(0vh);
  }
}

.collegeCompare__selectCollegeScreen__container {
  border-top: 1px solid var(--border-gray);
  border-bottom: 1px solid var(--border-gray);
}

.collegeCompare__compareButton__container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  padding-bottom: 0;
}

.collegeCompare__compareButton__container .collegeCompare__compareButton:disabled,
button.drawer__close__submit:disabled {
  opacity: 0.4;
}

.collegeCompare__compareButton__container .collegeCompare__compareButton {
  border: none;
  border-radius: 4px;
  background: var(--primary-red-color);
  color: var(--white-color);
  display: flex;
  width: 280px;
  padding: 7px 18px;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 700;
  line-height: 22px;
  letter-spacing: 0.3px;
  text-transform: capitalize;
}

.selectCollegeScreen__panels {
  display: flex;
  position: relative;
}

.selectCollegeScreen__panels:after {
  content: " ";
  display: inline-block;
  position: absolute;
  width: 1px;
  height: 100%;
  background-color: var(--border-gray);
  top: 0;
  left: 50%;
}

.selectCollegeScreen__panels .selectionPanel {
  flex-basis: 50%;
  padding: 22px 0;
  display: flex;
  gap: 24px;
}

.selectCollegeScreen__leftPanel {
  /* border-right: 1px solid var(--border-gray); */
  position: relative;
}

.selectCollegeScreen__rightPanel {
  justify-content: center;
}

.selectionPanel .selectionLogo__div {
  border-radius: 4px;
  border: 1px solid var(--border-gray);
  width: 56px;
  height: 56px;
  background: var(--white-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.selection__Inputs {
  flex-grow: 1;
  max-width: 360px;
  display: flex;
  flex-direction: column;
  gap: 18px;
  position: relative;
}

.selection__Inputs .clearSelection {
  width: 20px;
  height: 20px;
  /* border-radius: 50%;
  border: 2px solid var(--primary-black-color); */
  position: absolute;
  top: -10px;
  right: -30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  display: none;
}

.selection__Inputs .clearIcon {
  /* background-position: -244px -1142px;
  width: 13px;
  height: 13px; */
  transform: scale(0.7);
}

.selection__Input__Box {
  height: 44px;
  position: relative;
}

.selection__Input__Box .select2,
.selection__Input__Box .select2-container .select2-selection--single,
.selection__Input__Box .select2-selection__rendered,
.selection__Input__Box.selection__Select__Program .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 100%;
  width: 100% !important;
}

.selection__Input__Box.selection__Search__College .select2-selection__arrow {
  display: none;
}

.selection__Input__Box .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
  transform: scale(1.5) rotate(180deg);
}

.selection__Input__Box .select2-container--default .select2-selection--single {
  border: 1px solid var(--border-gray);
}

.selection__Input__Box .select2-container--default .select2-selection--single .select2-selection__arrow b {
  background-image: url("https://www.getmyuni.com/yas/images/select-angle.png");
  background-color: transparent;
  background-size: contain;
  border: none !important;
  width: 9px;
  height: 6px;
  top: 20px;
  right: 16px;
  margin: 0;
  left: unset;
  transform: scale(1.5);
}

.selection__Input__Box .select2-container--default .select2-selection--single .select2-selection__rendered {
  display: flex;
  align-items: center;
  padding-left: 42px;
}

.selection__Input__Box .select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: var(--light-gray-color);
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0.3px;
}

.selection__Input__Box .searchIcon {
  background-position: -152px -214px;
  width: 16px;
  height: 16px;
  top: 14px;
  left: 16px;
  position: absolute;
}

.selection__Input__Box .courseIcon {
  background-position: -306px -634px;
  width: 18px;
  height: 14px;
  position: absolute;
  top: 14px;
  left: 16px;
}

.selection__Inputs .selectedCollege {
  display: none;
  gap: 20px;
}

.selection__Inputs .selectedCollege.value__selected {
  display: flex;
}

.selectedCollege .selectedCollege__heading {
  color: var(--primary-black-color);
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.selectedCollege .selectedCollege__subheading {
  font-size: 12px;
  font-weight: 400;
  line-height: 24px;
  color: var(--primary-black-color);
}

.selectedProgram .selectedProgram__heading {
  color: var(--primary-black-color);
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.selectedProgram .editIcon,
.selectedCollege .editIcon {
  /* background-position: -280px -1140px;
  width: 16px;
  height: 16px; */
  cursor: pointer;
  flex-shrink: 0;
}

.selection__Inputs .selectedProgram {
  display: none;
  gap: 26px;
  align-items: center;
}

.selection__Inputs .selectedProgram.value__selected {
  display: flex;
}

.versusText {
  color: var(--primary-black-color);
  font-size: 12px;
  font-weight: 500;
  line-height: normal;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
  border-radius: 50%;
  border: 1px solid var(--border-gray);
  padding: 5px;
  color: var(--primary-black-color);
  font-size: 12px;
  font-weight: 500;
  background-color: var(--white-color);
  z-index: 1;
}

.collegeCompare__drawer__mobile .drawercloseIcon svg,
.collegeCompare__drawer__mobile .drawercloseIcon path {
  pointer-events: none;
}

.editIcon svg,
.editIcon path {
  pointer-events: none;
}

.selectedProgram .editIcon,
.selectedCollege .editIcon {
  background-position: -42px -43px;
  width: 16px;
  height: 16px;
}

.selection__Inputs .clearIcon {
  background-position: -9px -43px;
  width: 20px;
  height: 20px;
}

.selection__img {
  background-position: -89px -128px;
  width: 44px;
  height: 49px;
}

/* College compare utility panel css ends*/
.discussionForumSection {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.discussionForumSection h2 {
  line-height: 28px;
  margin: 0;
  margin-bottom: 20px;
  font-weight: 500;
  background: #f5f5f5;
  text-transform: uppercase;
  position: relative;
}

.timelineModal {
  padding: 40px;
  width: 794px;
  height: 503px;
  border-radius: 4px;
  border: none;
}

.timelineModal::backdrop {
  background-color: rgba(51, 51, 51, 0.6);
}

.timelineModal:focus-visible {
  outline: none;
}

.timelineModal .timelineModal--closeIcon {
  background-position: -91px -335px;
  width: 16px;
  height: 16px;
  position: absolute;
  top: 40px;
  right: 40px;
  cursor: pointer;
}

.timelineModal .timelineModal--heading {
  font-size: 24px;
  font-weight: 500;
  color: #282828;
  margin-bottom: 30px;
}

.timelineList {
  padding: 0;
  margin: 0;
  list-style-type: none;
}

.timelineList li {
  padding-left: 20px;
  border-left: 6px solid #d9d9d9;
  position: relative;
  padding-bottom: 20px;
}

.timelineList li:first-child {
  padding-top: 0;
}

.timelineList li:first-child .circleSpot {
  top: 0;
}

.timelineList li:first-child h3 {
  max-width: 514px;
}

.timelineList li:last-child {
  border-image: linear-gradient(to bottom, #ff4e53 0%, #ff4e53 30%, #fff 30%, #fff 100%) 1/0px 0px 0px 6px;
  border-top: none;
  padding-bottom: 0;
}

.timelineList li .circleSpot {
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #d9d9d9;
  position: absolute;
  top: 5px;
  left: -9px;
  border-radius: 50%;
}

.timelineList li h3 {
  font-size: 15px;
  margin-bottom: 5px;
  font-weight: 500;
  line-height: 1.6;
  color: #989898;
}

.timelineList li p {
  font-size: 12px;
  font-weight: normal;
  line-height: 1.67;
  color: #989898;
}

.timelineList li.activeDate {
  border-left: 6px solid #ff4e53;
}

.timelineList li.activeDate h3 {
  font-size: 15px;
  font-weight: 500;
  line-height: 1.6;
  color: #282828;
}

.timelineList li.activeDate .circleSpot {
  width: 10px;
  height: 10px;
  padding: 5px;
  border: solid 2px #fff;
  background-color: #ff4e53;
  outline: 2px solid #d9d9d9;
  left: -10px;
}

.timelineList li.presentDate {
  background-color: #ffedee;
  box-shadow: 40px 0px 0px 0px #ffedee, -40px 0px 0px 0px #ffedee;
  border-left-style: dashed;
  padding-top: 10px;
  padding-bottom: 10px;
}

.timelineList li.presentDate p {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.67;
  color: #282828;
}

.timelineList li.presentDate .circleSpot {
  width: 20px;
  height: 20px;
  padding: 7px 5px 7px 5px;
  background-color: #ff4e53;
  left: -12px;
  top: 10px;
}

.timelineList li.presentDate .circleSpot::after {
  content: "";
  display: block;
  position: absolute;
  top: 2px;
  left: 6px;
  width: 5px;
  height: 10px;
  background: var(--color-red);
  border-style: solid;
  border-color: var(--color-white);
  -o-border-image: initial;
  border-image: initial;
  border-width: 0px 2px 2px 0px;
  opacity: 1;
  transform: scale(1) rotate(45deg);
  transition: 0.2s ease;
  transition: opacity 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms, transform 80ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
}

.timelineList .presentDate+.activeDate {
  padding-top: 20px;
}

.timelineList .presentDate+.activeDate .circleSpot {
  top: 24px;
}

.showTooltip {
  color: #000000;
  text-decoration: none;
}

.showTooltip:hover {
  color: #000000;
  text-decoration: underline;
  cursor: pointer;
}

.articlesDisplay #collegeArticlesBtn,
.articlesDisplay #collegeNewsBtn {
  position: relative;
  justify-content: center;
  margin: 0 auto;
  min-width: 150px;
}

.read-less {
  transform: rotate(270deg) scale(0.7) translateX(-3px) !important;

}

/* Style the tooltip */
.tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
  color: #3d8ff2;
}


/* Style the tooltip */
.tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
  color: #3d8ff2;
}

/* Style the tooltip text */
.tooltip .tooltiptext {
  visibility: hidden;
  display: flex;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.32), 0 0 2px 0 rgba(0, 0, 0, 0.16);
  flex-wrap: wrap;
  gap: 5px;
  min-width: 150px;
  background-color: #fff;
  color: #3d8ff2;
  text-align: center;
  padding: 10px;
  margin-top: 10px;
  border-radius: 10px;
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 0;
  transform: translateX(-80%);
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip .tooltiptext::before {
  position: absolute;
  content: "";
  right: 20%;
  transform: rotate(45deg) translateX(-20%);
  width: 12px;
  height: 12px;
  box-shadow: -1px -1px 0 0 #e6e5e5;
  background-color: #fff;
  top: -5px;
  margin: 0 0 0 -0.25em;
}

.tooltip .tooltiptext>span:not(:last-child):after {
  content: ",";
  display: inline-block;
  cursor: pointer;
  margin-left: -2px;
}

.courseFeesTable tr td:last-child button,
.courseFeesTable tr td:last-child button {
  white-space: nowrap;
  width: 100%;
  margin-bottom: 0;
}

.courseAndFeeDiv .table-responsive thead tr td,
.courseFeesTable thead tr td {
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
}

.college-info-cta button.writeReview {
  margin: 0;
  padding: 8px !important;
  text-align: center;
}

.showMoreCourseWrap.expanded:after {
  display: none;
  /* or content: none; */
}

.course-fee-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.course-title {
  display: flex;
  flex-direction: column;
}

.cutoffTableMargin button.getFees {
  color: var(--color-red);
  margin-bottom: 0;
  padding: 0px;
  margin-top: 10px;
  text-align: left;
}

@media (max-width: 1023px) {

  /* popup undeline */
  .showTooltip {
    text-decoration: underline;
  }

  /* clS issue css*/
  .row-cls {
    min-height: 4225px;
  }

  .programInfoDiv-cls {
    min-height: 262px;
  }

  /* College Rank Table */
  .collegeRankings table.rankTable img {
    width: unset;
    max-width: 80px;
  }

  .courseFeesTable.rankTable tbody tr td:nth-of-type(2) {
    font-weight: 400;
  }

  .collegeRankings .rankTable img {
    max-height: fit-content;
  }

  .rankTable {
    table-layout: fixed;
  }

  .applyNowButtonContainer .textBlue .applyRedIcon {
    top: 6px;
  }

  .multipleUserIcon {
    background-position: -266px -594px !important;
    margin-right: 0;
  }

  .questionIcon {
    background-position: -290px -595px !important;
  }

  .blueBgDiv {
    display: none !important;
  }

  .clgInfoHeroSection {
    margin-top: -11px !important;
    margin-left: -10px;
    margin-right: -10px;
    border-radius: 0;
  }

  .collegeInfo {
    margin-bottom: 0 !important;
    padding-bottom: 10px !important;
  }

  .collegeInfo .collegeIntro {
    position: relative;
  }

  .collegeInfo .collegeIntro h1 {
    font-size: 16px;
    font-weight: 700;
    line-height: 1.5;
    margin-left: 6px;
  }

  .collegeInfo .collegeIntro ul li:first-child {
    font-size: 12px;
    font-weight: 400;
  }

  .collegeInfo .collegeIntro .favriteIcon {
    position: absolute;
    bottom: 0;
    right: 0;
  }

  .collegeInfoNew {
    background-color: #fff;
    padding: 10px;
  }

  .collegeInfoNew ul {
    display: flex !important;
    flex-wrap: wrap;
    margin: 0;
    padding: 0;
    list-style-type: none;
  }

  .collegeInfoNew ul li {
    font-size: 12px;
    font-weight: 500;
    color: #282828 !important;
  }

  .collegeInfoNew ul .ctaMobileRed,
  .ctaMobileRed a {
    color: #ff4e53;
  }

  .collegeInfoNew ul .ctaMobileBlue {
    color: #3d8ff2;
  }

  .collegeInfoNew .btnDiv {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: 5px;
    max-height: 34px;
  }

  .collegeInfoNew .btnDiv .writeReview {
    flex-basis: 50%;
    border-radius: 3px;
    border: solid 1px #ff4e53;
    background-color: rgba(255, 78, 83, 0.1);
    font-size: 14px;
    font-weight: 500;
    color: #ff4e53;
    position: relative;
    text-align: center;
    padding: 8px 15px;
  }

  .collegeInfoNew .btnDiv .brochureBtn {
    border: none !important;
    margin: 0 !important;
    flex-basis: 50%;
    font-weight: 500;
  }

  .courseTypeMaster .allCoursesHeading {
    background-color: #fff;
    padding-left: 0;
    margin-top: 0;
    border-top: 1px solid #d8d8d8;
    border-bottom: 1px solid #d8d8d8;
    border-radius: 0;
  }

  .courseTypeMaster .courseTypeDiv {
    flex-direction: column;
    position: relative;
    padding: 10px 0 !important;
    margin-bottom: 0;
    min-height: 80px;
  }

  .courseTypeMaster .courseTypeDiv .courseNameAndEligibility p {
    font-size: 14px;
    font-weight: 500;
    color: #0d3d63;
    margin-bottom: 0;
  }

  .courseTypeMaster .courseTypeDiv .courseNameAndEligibility a {
    font-size: 14px;
    font-weight: 500;
    color: #0d3d63;
    margin-top: 0;
  }

  .courseTypeMaster .courseTypeDiv .detailedFee {
    margin-top: 8px;
  }

  .courseTypeMaster .courseTypeDiv .detailedFee .courseFee {
    font-size: 15px;
    font-weight: 500;
    color: #ff4e53;
  }

  .courseTypeMaster .courseTypeDiv .detailedFee .courseFee .courseDuration {
    font-size: 12px;
    font-weight: 400;
    color: #787878;
  }

  .courseTypeMaster .courseTypeDiv .detailedFee .viewDetailedFee {
    font-size: 12px;
    font-weight: 700;
    color: #3d8ff2;
  }

  .courseTypeMaster .courseTypeDiv .applyNowButtonContainer {
    position: absolute;
    bottom: 15px;
    right: 0;
  }

  .courseTypeMaster .courseTypeDiv .applyNowButtonContainer button {
    width: 140px;
  }

  .courseTypeMaster .courseTypeDiv .applyNowButtonContainer .writeIcon {
    margin-right: 0;
  }

  .courseTypeMaster .courseMain {
    margin-bottom: 10px;
    flex-wrap: wrap;
  }

  .courseTypeMaster .courseMain .courseDetails {
    flex-basis: 55%;
  }

  .courseTypeMaster .courseMain .courseDetails .courseHeading {
    font-size: 15px;
    font-weight: 700;
    color: #0d3d63;
    padding: 0;
    margin-bottom: 6px;
    max-width: 180px;
  }

  .courseTypeMaster .courseMain .courseDetails .courseRating {
    font-size: 12px;
  }

  .courseTypeMaster .courseMain .courseDetails .courseRating i {
    transform: scale(0.8);
  }

  .courseTypeMaster .courseMain .courseDetails .courseItems span {
    font-size: 12px;
  }

  .courseTypeMaster .courseMain .courseDetails .courseExamAccepted {
    font-size: 12px;
  }

  .courseTypeMaster .courseMain .courseData .courseFee {
    font-size: 15px;
    font-weight: 500;
    color: #ff4e53;
  }

  .courseTypeMaster .courseMain .courseData .courseDuration {
    font-size: 12px;
    font-weight: 400;
    color: #787878;
  }

  .courseTypeMaster .courseMain .courseData .avgFees {
    font-size: 12px;
    font-weight: 400;
    color: #282828;
    display: inline !important;
  }

  .bulletIcon {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #989898;
    display: inline-block;
    vertical-align: middle;
    margin: 0 2px;
  }

  .collegeListpageBody .brochureBtn.fixedbrochureBtn {
    top: 0;
  }

  .collegeCriteria ul {
    padding: 8px 0;
  }

  .foundClgs button {
    display: none;
  }

  .horizontalRectangle .appendAdDiv.xs-h100 {
    height: 100px;
  }

  .getPopup {
    color: var(--anchor-textclr);
  }

  .tooltipIconText {
    width: 200px;
    right: 20px;
    top: 32px;
  }

  .tooltipIconText:before,
  .tooltipIcon:hover .tooltipIconText:before {
    display: none;
  }

  .tooltipIcon:before {
    content: "";
    width: 15px;
    height: 15px;
    transform: rotate(42.5deg);
    left: 5px;
    background: var(--color-white);
    position: absolute;
    box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.16);
    z-index: 1;
    display: none;
    top: 24px;
    left: auto;
    border: var(--border-line);
    border-right: none;
    border-bottom: none;
  }

  .tooltipIcon:hover:before {
    display: block;
  }

  .mobileOnly.favriteIcon {
    display: inline-block !important;
  }

  .pageData,
  .reviewsSection {
    padding: 10px;
    margin-bottom: 10px;
  }

  .pageData .tooltipIcon,
  .reviewsSection .tooltipIcon {
    vertical-align: middle;
    margin-bottom: 4px;
  }
  h2, .pageData h2{
    padding: 8px 10px;
  }
  .pageData h2,
  .reviewsSection h2 {
    line-height: 28px;
    margin-bottom: 10px;
    display: block;
    background-color: #f5f5f5;
  }

  .pageData h2 a,
  .reviewsSection h2 a {
    display: none;
  }

  .pageData p,
  .reviewsSection p {
    padding-bottom: 10px;
  }

  .pageData ul,
  .reviewsSection ul {
    margin: 10px 0;
    padding-left: 30px;
  }

  .pageInfo,
  .redirectToLink {
    max-height: 350px;
  }

  .readMoreDiv,
  .showMoreCourseWrap,
  .redirectPage,
  .horizontalRectangle,
  .verticleRectangle,
  .squareDiv {
    margin-bottom: 10px;
  }

  .redirectPage:before {
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0), #fff);
  }

  .urlIcon {
    vertical-align: text-bottom;
    margin-bottom: 2px;
  }

  .clgIcon {
    background-position: 132px -274px;
  }

  .rankIcon {
    background-position: 132px -297px;
  }

  .whiteLabel {
    background-position: 132px -322px;
  }

  .assureIcon {
    background-position: 132px -348px;
  }

  .locationPinIcon {
    transform: scale(0.6);
    background-position: 265px -297px;
    width: 20px;
    height: 28px;
    margin-right: 0;
  }

  .qnaIcon {
    background-position: 132px -370px;
  }

  .clgInfoHeroSection {
    margin-top: -157px;
    border: none;
    margin-bottom: 10px;
    border-radius: 0;
    overflow: hidden;
  }

  .collegeInfo {
    padding: 10px;
    padding-bottom: 0;
    margin-bottom: 10px;
    background-position: top;
    overflow: hidden;
    max-height: 92px;
    border-radius: 0;
  }

  .collegeInfo h1 {
    font-size: 18px;
    line-height: 28px;
    font-weight: 400;
    padding-bottom: 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 2;
  }

  .collegeInfo ul li {
    padding-right: 0;
    font-size: 12px;
    line-height: 24px;
    display: block;
    color: var(--primary-font-color);
  }

  .collegeInfo ul li:after {
    display: none;
  }

  .collegeInfo ul li a {
    color: var(--primary-font-color);
  }

  .collegeInfo .collegeIntro {
    flex-basis: calc(100% - 66px);
  }

  .collegeInfo .collegeIntro ul li {
    color: var(--color-white);
  }

  .addtoWishList {
    position: initial;
  }

  .collegeLogo {
    max-width: 56px;
    max-height: 56px;
    margin-right: 3px !important;
  }

  .addtoWishList {
    display: none;
  }

  .heroSectionSubDiv {
    border: var(--border-line);
    border-radius: 4px;
    padding: 10px 20px;
  }

  .heroSectionSubDiv img {
    width: 36px;
    height: 36px;
  }

  .collegeRelataedLinks {
    border-radius: 0;
    border-right: 0;
    border-left: 0;
    margin: 0 -10px;
    margin-bottom: 10px;
    padding: 0px 5px;
    height: 43px;
  }

  .collegeRelataedLinks .btn_left,
  .collegeRelataedLinks .btn_right {
    width: 35px;
    height: 38px;
  }

  .collegeRelataedLinks .btn_right .right_angle {
    margin: 10px 0;
  }

  .collegeRelataedLinks .btn_left .left_angle {
    margin: 10px 0;
  }

  .collegeRelataedLinks ul li {
    padding: 0;
    margin: 0 5px;
  }

  .collegeRelataedLinks ul li a,
  .collegeRelataedLinks ul li .activeLink {
    padding: 7px 0;
  }

  table thead td {
    padding: 6px 11px;
  }

  .table-responsive tbody td p {
    padding-bottom: 0;
  }

  .courseFeesTable tbody tr td:nth-of-type(2) {
    font-weight: 500;
    line-height: 20px;
  }

  .filterSection {
    padding: 10px;
    margin-bottom: 10px;
  }

  .sideBarSection {
    margin-bottom: 10px;
    width: 100%;
  }

  .sideBarSection .sidebarHeading {
    margin: 10px;
    margin-bottom: 0;
  }

  .sideBarSection .listCard {
    padding: 10px;
  }

  .sideBarSection .sidebarImgDiv {
    flex-basis: 56px;
    margin-right: 10px;
  }

  .sideBarSection .sidebarImgDiv img {
    width: 56px;
    height: 56px;
  }

  .sideBarSection .sidebarTextLink {
    flex-basis: calc(100% - 66px);
  }

  .sideBarSection .sidebarTextLink p {
    font-size: 12px;
    line-height: 16px;
  }

  .sideBarSection .sidebarTextLink .applyText {
    padding-top: 5px;
  }

  .customSlider .scrollLeft,
  .customSlider .scrollRight {
    display: none !important;
  }

  .customSlider .sliderCardInfo p {
    padding-bottom: 0;
  }

  .customSlider .sliderCardInfo p:first-child {
    font-size: 14px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
  }

  .two-cardDisplay .sliderCardInfo {
    padding: 10px;
    width: 270px;
    margin-right: 5px;
    white-space: normal;
    display: inline-block !important;
    vertical-align: middle;
  }

  .two-cardDisplay .sliderCardInfo .viewAllDiv {
    min-height: 162px;
  }

  .two-cardDisplay .sliderCardInfo .clgLogo {
    margin-bottom: 10px;
  }

  .facilities .spriteIcon {
    margin-right: 5px;
  }

  .facilities ul {
    margin: 0;
    padding: 0;
  }

  .facilities ul li {
    flex-basis: 48.4%;
    margin-right: 10px;
    margin-bottom: 15px;
    font-size: 12px;
  }

  .facilities ul li:nth-of-type(2n) {
    margin-right: 0;
  }

  .facilities ul li:before {
    display: none;
  }

  .faq_section {
    margin-bottom: 0;
  }

  .fixedExamRelatedDiv {
    border-top: 0;
  }

  .allReviews {
    padding: 1px;
    border: none;
  }

  .allReviews .ratingHeading {
    font-size: 15px;
  }

  .allReviews .col-md-3,
  .allReviews .col-md-4,
  .allReviews .col-md-5,
  .allReviews .col-7,
  .allReviews .col-5 {
    padding: 0;
    text-align: left;
  }

  .allReviews .col-md-4 {
    margin: 20px 0;
  }

  .allReviews .avgRating {
    display: inline-block;
    padding: 0;
  }

  .allReviews .d-inlineblock {
    display: inline-block;
  }

  .allReviews .ratings {
    padding-bottom: 0;
  }

  .allReviews .subText {
    padding: 0;
  }

  .reviewRation {
    justify-content: left;
  }

  .reviewRation p {
    padding-bottom: 0;
  }

  .reviewRation p:first-child {
    flex-basis: 61px;
    text-align: right;
  }

  .componentRatio .row {
    margin: 0;
    margin-bottom: 0;
  }

  .componentRatio p {
    font-size: 13px;
  }

  .componentRatio span {
    margin-left: 0;
  }

  .componentRatio .spriteIcon {
    margin: 0 1px;
  }

  .photoGallery h2.row {
    margin-bottom: 10px;
  }

  .photoGallery .row {
    display: block;
    overflow: auto;
    white-space: nowrap;
  }

  .photoGallery .picture {
    display: inline-block;
    width: 224px;
    margin-right: 5px;
  }

  .photoGallery .picture.mobileOnly {
    display: inline-block !important;
    margin-left: 5px;
  }

  .photoGallery .picture .viewAllDiv {
    min-height: 170px;
  }

  .photoGallery .picture img {
    height: 168px;
    width: 100%;
  }

  .contsctUs iframe {
    display: block;
    max-height: 200px;
    width: 100%;
    margin-bottom: 16px;
  }

  .contsctUs ul {
    margin: 0;
    padding: 0;
  }

  .contsctUs ul li {
    padding-bottom: 7px;
    font-size: 13px;
  }

  .askUsQuestion h2 {
    margin-bottom: 10px;
    background-color: #f5f5f5;
  }

  .four-cardDisplay .sliderCardInfo {
    width: 224px;
    display: inline-block !important;
    margin-right: 5px;
    vertical-align: middle;
  }

  .four-cardDisplay .sliderCardInfo:nth-of-type(4n + 1) {
    margin-left: 5px;
  }

  .four-cardDisplay .sliderCardInfo:first-child {
    margin-left: 0;
  }

  .four-cardDisplay .sliderCardInfo+.mobileOnly .viewAllDiv {
    min-height: 266px;
  }

  .four-cardDisplay .sliderCardInfo figure {
    height: 168px;
  }

  .four-cardDisplay .sliderCardInfo .textDiv {
    padding: 10px;
  }

  .four-cardDisplay .sliderCardInfo .subText {
    padding: 10px;
  }

  .four-cardDisplay .sliderCardInfo h3,
  .four-cardDisplay .sliderCardInfo .widgetCardHeading {
    font-weight: 400;
  }

  /*.getSupport {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 5px;
    border-radius: 0;
    z-index: 1;
    display: none;
  }

  .getSupport .row {
    display: none;
  }

  .getSupport button {
    width: 49%;
    border-radius: 2px;
    font-size: 14px;
    padding: 6px 4px;
  }

  .getSupport button.applyNow {
    margin-left: 0;
  }*/

  .brochureBtn {
    display: block;
    text-align: center;
    margin: 0 -10px;
    margin-bottom: 10px;
    border: 10px solid var(--color-white) !important;
    transition: none !important;
    padding: 0;
  }

  .brochureBtn button {
    width: 100%;
  }

  .reviewsFilterOPtions ul {
    padding: 0;
    margin: 0;
    display: none;
  }

  .reviewsFilterOPtions div {
    margin-right: 10px;
  }

  .reviewsFilterOPtions div:last-child {
    margin-right: 0;
  }

  .reviewsFilterOPtions .sortbyDiv {
    padding: 0;
    flex-basis: 194px;
    border: var(--border-line);
    padding-left: 5px;
  }

  .reviewsFilterOPtions p {
    padding: 0;
  }

  .reviewsFilterOPtions .filterByText {
    border: var(--border-line);
    margin: 0;
    font-weight: 400;
    position: relative;
    padding: 4px 20px;
    min-width: 113px;
    text-align: center;
  }

  .reviewsFilterOPtions .filterByText:after {
    content: "";
    position: absolute;
    width: 14px;
    height: 10px;
    right: 10px;
    top: 50%;
    transform: translate(0px, -50%);
    background-image: url(../../images/selectAngle.png);
  }

  .reviewsFilterOPtions .filterByText:before {
    content: "";
    position: absolute;
    background: url(../../images/master_sprite.webp);
    width: 24px;
    height: 24px;
    top: 10px;
    left: 5px;
    top: 50%;
    transform: translate(0px, -50%);
    background-position: 95px -341px;
  }

  .reviewsFilterOPtions select {
    flex-basis: auto;
  }

  .reviewsFilterOPtions {
    margin-bottom: 10px;
  }

  .reviewsFilterOPtions .sortIcon {
    margin-top: 3px;
  }

  .collegeReviewPage .four-cardDisplay .sliderCardInfo,
  .courseAndFeePage .four-cardDisplay .sliderCardInfo,
  .collegeListpageBody .four-cardDisplay .sliderCardInfo {
    width: 224px;
  }

  .collegeReviewPage .allReviews .subText,
  .courseAndFeePage .allReviews .subText,
  .collegeListpageBody .allReviews .subText {
    margin-top: 0;
  }

  .collegeListpageBody .reviewCard {
    max-height: 535px;
  }

  .collegeReviewPage .customSlider .sliderCardInfo,
  .collegeListpageBody .customSlider .sliderCardInfo {
    margin-right: 5px;
  }

  .collegeReviewPage .customSlider .sliderCardInfo:nth-of-type(4n + 1),
  .collegeListpageBody .customSlider .sliderCardInfo:nth-of-type(4n + 1) {
    margin-left: 5px;
  }

  .collegeReviewPage .customSlider .sliderCardInfo:first-child,
  .courseAndFeePage .customSlider .sliderCardInfo:first-child,
  .collegeListpageBody .customSlider .sliderCardInfo:first-child {
    margin-left: 0;
  }

  .collegeReviewPage .customSlider .sliderCardInfo figure,
  .courseAndFeePage .customSlider .sliderCardInfo figure,
  .collegeListpageBody .customSlider .sliderCardInfo figure {
    height: 168px;
  }

  .collegeReviewPage .customSlider .sliderCardInfo img,
  .courseAndFeePage .customSlider .sliderCardInfo img,
  .collegeListpageBody .customSlider .sliderCardInfo img {
    height: auto;
  }

  .collegeReviewPage .sidebarAds .appendAdDiv,
  .courseAndFeePage .sidebarAds .appendAdDiv,
  .collegeListpageBody .sidebarAds .appendAdDiv {
    max-width: 300px;
    width: 300px;
  }

  .collegeListpageBody .getSupport {
    display: block;
  }

  .selectedFiltersDisplay {
    margin-bottom: 5px;
    display: block;
  }

  .selectedFilters {
    display: inline-block;
  }

  .selectedFiltersDisplay .summary {
    font-size: 15px;
    line-height: 26px;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .selectedFiltersDisplay .selectedFilters button {
    font-size: 13px;
    line-height: 20px;
    margin-right: 5px;
    font-weight: 400;
  }

  .courseTypeDiv {
    margin-bottom: 10px;
    padding-bottom: 0;
  }

  .courseTypeDiv .row:first-child {
    margin: 0 -20px;
  }

  .courseTypeDiv .primaryBtn.brochureBtn {
    display: none;
  }

  .courseTypeDiv .courseTypeTitle {
    flex-basis: 100%;
  }

  .courseTypeDiv .courseName {
    font-size: 15px;
    line-height: 24px;
    padding-top: 0;
  }

  .courseTypeDiv .courseInfo {
    flex-basis: 100%;
    padding-top: 10px;
  }

  .courseTypeDiv .courseInfo .row {
    margin: 0 -10px;
  }

  .courseTypeDiv .courseInfo .col-md-2,
  .courseTypeDiv .courseInfo .col-md-4 {
    padding: 0 10px;
  }

  .courseTypeDiv .courseInfo .col-6 {
    padding: 0 10px;
    flex: 0 0 50%;
    max-width: 50%;
    padding-bottom: 10px;
  }

  .courseTypeDiv .courseInfo .full-star,
  .courseTypeDiv .courseInfo .half-star,
  .courseTypeDiv .courseInfo .emorty-star {
    transform: scale(0.75);
    margin: 0 -3px;
    vertical-align: text-bottom;
  }

  .courseTypeDiv p {
    font-size: 14px;
  }

  .courseTypeDiv .admissionGuid,
  .courseTypeDiv .primaryBtn {
    width: 50%;
    border: none !important;
    border-radius: 0 !important;
    display: block;
  }

  .courseTypeMaterial {
    margin: 0 -20px;
    border-top: var(--border-line);
    display: flex;
  }

  .admissionGuid {
    background: var(--color-white);
    font-size: 14px;
    line-height: 20px;
    color: var(--anchor-textclr);
    padding: 8px 5px;
    font-weight: var(--font-semibold);
  }

  .mobileSortandFilter {
    display: block;
    position: fixed;
    width: 100%;
    bottom: 0;
    background: #fff;
    left: 0;
    z-index: 2;
    border-top: var(--border-line);
  }

  .mobileSortandFilter .sortIcon {
    background-position: 534px -194px;
  }

  .mobileSortandFilter .optionDiv {
    display: flex;
  }

  .mobileSortandFilter button {
    width: 50%;
    border-radius: 0;
    font-size: 14px;
    line-height: 24px;
    color: #787878;
    background-color: var(--color-white);
    padding: 9px 6px;
    text-transform: uppercase;
    position: relative;
    font-weight: 500;
    border-radius: 3px;
    border: none;
  }

  .mobileSortandFilter button:first-child {
    border-right: var(--border-line);
  }

  .mobileFilterSection {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 4;
    width: 100%;
    height: 100%;
    background: var(--color-white);
  }

  .mobileFilterSection h2{
    background-color: #f5f5f5;
  }
  
  .mobileFilterSection h2,
  .mobileFilterSection .mobileFilterHeading {
    background: var(--color-white);
    padding: 12px 16px;
    color: var(--primary-font-color);
    line-height: 20px;
    font-weight: 500;
    text-transform: uppercase;
    display: flex;
    justify-content: space-between;
    border-bottom: var(--border-line);
  }

  .mobileFilterSection h2 span,
  .mobileFilterSection .mobileFilterHeading span {
    color: var(--color-red);
  }


  .tabs {
    flex-basis: 140px;
    border-right: var(--border-line);
    height: calc(100% - 89px);
    overflow: hidden auto;
  }

  .tabs li {
    padding: 10px 16px;
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    position: relative;
  }

  .tabs li.tab-link.current {
    font-weight: 500;
    background: #fafbfc;
  }

  .tabs li.tab-link.current:after {
    content: "";
    position: absolute;
    width: 3px;
    height: 15px;
    border-radius: 3px;
    right: -2px;
    top: 50%;
    transform: translate(0, -50%);
    background: #c4c4c4;
  }

  .tabs li.appliedFilter:before {
    content: "";
    position: absolute;
    width: 5px;
    height: 5px;
    background: #ff4e53;
    top: 50%;
    transform: translate(0, -50%);
    right: 5px;
    border-radius: 50%;
  }

  .filterContentDiv {
    flex-basis: calc(100% - 140px);
    background: #fafbfc;
    padding-left: 0;
  }

  .filterContentDiv a:hover {
    text-decoration: none;
  }

  .filterContentDiv input[type="checkbox"] {
    width: auto;
    display: inline-block;
    margin: 0;
    height: auto;
    width: 16px;
    height: 16px;
    padding: 0;
    vertical-align: middle;
  }

  .filterContentDiv label,
  .filterContentDiv li {
    color: #787878;
    line-height: 32px;
    font-size: 14px;
    padding-left: 3px;
    list-style-type: none;
  }

  .filterContentDiv label {
    line-height: 32px;
  }

  .filterOptionDiv {
    display: flex;
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 3;
  }

  .filterOptionDiv button {
    flex-basis: 50%;
    font-size: 14px;
    line-height: 20px;
    border: 1px solid var(--color-red);
    background-color: #fafbfc;
    padding: 11px;
    font-weight: var(--font-semibold);
    color: var(--color-red);
    text-align: center;
    background: var(--color-white);
  }

  .filterOptionDiv button.applyFilter {
    background: var(--color-red);
    color: var(--color-white);
  }

  .filterSearch input[type="text"] {
    margin-bottom: 6px;
  }

  .filterSidebarSection {
    display: none;
  }

  .mobileSortSection {
    background: rgba(0, 0, 0, 0.639215);
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 4;
    display: none;
  }

  .mobileSortDiv {
    position: fixed;
    height: auto;
    bottom: 0;
    left: 0;
    overflow: auto;
    border-radius: 4px 4px 0 0;
    width: 100%;
    background: var(--color-white);
    z-index: 3;
  }

  .mobileSortDiv h2{background-color: #f5f5f5;}
  .mobileSortDiv h2,
  .mobileSortDiv .mobileSortHeading {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    border-bottom: var(--border-line);
    line-height: 24px;
    font-weight: 500;
  }

  .mobileSortDiv ul {
    margin: 0;
    padding: 0 20px;
  }

  .mobileSortDiv ul li {
    font-size: 14px;
    line-height: 24px;
    border-bottom: var(--border-line);
    display: flex;
    color: #787878;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }

  .mobileSortDiv input[type="checkbox"] {
    border: none;
    margin: 0;
  }

  .mobileSortDiv input[type="checkbox"]:checked {
    background: var(--color-white);
    border: none;
  }

  .mobileSortDiv input[type="checkbox"].inputChecked:checked:after,
  .mobileSortDiv input[type="checkbox"]:checked:after {
    background: var(--color-white);
    border-color: #20d086;
    width: 7px;
    height: 16px;
  }

  .courseTypeList .loadMoreList,
  .collegeListpageBody .loadMoreList {
    width: 100%;
    margin-bottom: 10px;
    padding: 5px;
  }

  .courseAndFeePage .viewAllReviewsBtn {
    width: 100%;
  }

  .galleryImageList .galleryImage {
    flex-basis: 48.4%;
  }

  .galleryImageList .galleryImage:nth-of-type(2n) {
    margin-right: 0;
  }

  .galleryImageList .galleryImage img {
    height: 108px;
  }

  .cutoffPageMobileFilter {
    background: var(--color-white);
    text-align: center;
    padding: 7px;
    margin-bottom: 10px;
    border: var(--border-line);
  }

  .cutoffPageMobileFilter .spriteIcon {
    vertical-align: middle;
  }

  .cutoffPageMobileFilter .filterIcon {
    background-position: 95px -341px;
  }

  .cutOffFilter,
  .cutoffFilterOption,
  .selectedCutoffFilters {
    display: none;
  }

  .collegeVideos {
    display: block;
    white-space: nowrap;
    overflow: auto;
  }

  .collegeVideos .collegeVideoCard {
    margin-bottom: 0;
    display: inline-block;
    width: 260px;
    margin-right: 5px;
  }

  .collegeVideos .collegeVideoCard img {
    height: 260px;
  }

  .collegeInfoCard {
    flex-basis: 100%;
    max-width: 100%;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .collegeInfoCard .collegeLogo {
    margin-bottom: -28px;
  }

  .collegeListpageBody .sideBarSection .sidebarTextLink .applyText {
    margin-top: 0;
  }

  .aboutCollege {
    padding-top: 34px;
  }

  .aboutCollege .collegeLocation,
  .aboutCollege .collegeRating {
    display: inline-block;
    padding-right: 3px;
  }

  .aboutCollege .collegeName {
    font-size: 14px;
    line-height: 26px;
    padding-bottom: 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    font-weight: 500;
  }

  .aboutCollege .collegeName a {
    color: var(--primary-font-color);
  }

  .collegeCriteria ul li,
  .clgInfoCardfooter .addToCompare {
    font-size: 14px;
  }

  .clgListHeroSection {
    margin: -1px -10px 10px;
    background: var(--topheader-bg);
    padding: 10px;
  }

  .topContent .pageData {
    padding: 20px;
  }

  .topContent .readMoreDiv {
    margin-bottom: 0;
  }

  .clgListHeroSection h1 {
    font-size: 18px;
    line-height: 28px;
    padding-bottom: 10px;
    color: #282828;
  }

  .resultCount {
    padding-bottom: 15px;
  }

  .collegeListpageBody .pageInfo {
    max-height: 280px;
  }

  .selectedResultsMobile .foundesults {
    font-size: 15px;
    line-height: 25px;
    padding-bottom: 10px;
    font-weight: 500;
  }

  .selectedResultsMobile .filterDiv {
    padding: 0;
  }

  .selectedResultsMobile .filterDiv button {
    padding: 7px 8px;
    margin-bottom: 10px;
  }

  .selectedResultsMobile .filterDiv span {
    font-size: 13px;
    line-height: 20px;
  }

  .sortBySection {
    display: none;
  }

  .collegeKeyinfo {
    display: block;
    -moz-column-count: 2;
    column-count: 2;
  }

  .collegeKeyinfo .keyInfo {
    padding: 0;
  }

  .filterSearch ul {
    height: calc(100vh - 145px);
    overflow: auto;
  }

  .discussionForum .moreAnswer {
    margin: 0;
    margin-left: 2%;
  }

  .courseOffered {
    padding: 0 20px;
  }

  .contactUs .applyNow {
    padding: 0;
    background: 0 0;
    border: none;
  }

  .suggestion-list {
    float: none;
    margin: 0;
    margin-bottom: 10px;
    padding: 10px;
  }

  .suggestion-list .list-title {
    margin: 0 -10px;
    padding: 0 10px 10px;
  }

  .popupBody {
    width: calc(100% - 20px);
  }

  .popupBody .popUpContent {
    max-height: 230px;
  }

  .pagination,
  ul.pagination {
    padding: 0;
  }

  .courseAndFeePage .mobileSortandFilter {
    position: relative;
    border-top: none;
    background: 0 0;
    margin-bottom: 10px;
  }

  .courseAndFeePage .mobileSortandFilter .optionDiv {
    justify-content: space-between;
    background: 0 0;
  }

  .courseAndFeePage .mobileSortandFilter .mobileSort {
    position: relative;
    min-width: 146px;
    background-image: url(https://www.getmyuni.com/yas/images/select-angle.png);
    background-repeat: no-repeat;
    background-position: 90% 14px;
  }

  .courseAndFeePage .mobileSortandFilter button {
    background: #fafbfc;
    box-shadow: none;
    border: var(--border-line);
    width: auto;
    text-transform: initial;
    padding: 5px 12px;
    text-align: initial;
  }

  .clgCourseAndFeeSection {
    max-height: unset;
  }

  .courseTypeMaster {
    padding: 10px;
    margin-bottom: 10px;
  }

  .courseTypeMaster.pageInfo,
  .courseTypeMaster.limitCard {
    max-height: 1400px;
  }

  .courseTypeMaster h2 {
    margin-bottom: 10px;
    background-color: #f5f5f5;
  }

  .courseTypeMaster .admissionGuid {
    text-align: center;
    color: var(--color-red);
    text-decoration: none;
    font-weight: 500;
  }

  .courseTypeMaster .courseTypeDiv {
    padding: 10px;
    padding-bottom: 0;
    overflow: hidden;
  }

  .courseTypeMaster .courseTypeDiv .row:first-child {
    margin: 0 -10px;
  }

  .courseTypeMaster .courseTypeDiv .courseTypeMaterial {
    margin: 0 -10px;
  }

  .courseTypeMaster .courseTypeTitle {
    padding: 0 10px;
  }

  .courseTypeMaster .courseTypeTitle h3 {
    padding: 0;
  }

  .clgKeynotes .downloadBrochure {
    width: 100%;
    margin-top: 10px;
  }

  .siq_bR {
    bottom: 60px !important;
    margin-bottom: 60px !important;
  }

  .parentCollegeSection {
    margin-bottom: 20px;
  }

  .filterSectionForm {
    flex-wrap: wrap;
  }

  .filterSectionForm .tabs {
    flex-basis: revert;
    border: none;
  }

  .filterSectionForm .filterContentDiv {
    flex-basis: revert;
    background-color: transparent;
  }

  .filterSectionForm .tabs .tab-link::before {
    content: none;
  }

  .filterSectionForm .tabs .tab-link::after {
    content: none;
  }

  .filterSection .filterSectionForm .tabs .tab-link.current::after {
    content: none;
  }

  .filterSection .filterCheckButtons .filterCheckContainer input {
    vertical-align: middle;
  }

  .filterSection .filterCheckButtons .filterCheckContainer label {
    padding-left: 0;
    margin: 0;
  }

  .pageFooter.courseAndFeeFooter {
    padding-bottom: 100px;
  }

  .courseTypeDiv .textBlue {
    font-size: 14px;
    font-weight: 500;
  }

  .updated-info.row {
    align-items: flex-start;
    flex-wrap: nowrap;
    display: flex !important;
    margin-left: 0;
  }

  .updated-info.row .updatedBy img {
    float: left;
    width: 36px;
    height: 36px;
  }

  .updated-info.row .authorAndDate {
    display: flex;
    flex-wrap: wrap;
  }

  .updated-info.row .authorAndDate p {
    padding-left: 0;
    flex-basis: 100%;
    font-size: 12px;
    padding-bottom: 8px;
    margin-top: -5px;
  }

  .updated-info.row .authorAndDate .verifiedBlueTickIcon {
    background: url(../../images/master_sprite.webp);
    width: 16px;
    height: 16px;
    background-position: -673px -557px;
    vertical-align: text-top;
    transform: scale(0.65);
  }

  .updated-info.row .authorAndDate .authorName {
    display: inline-block !important;
    vertical-align: middle;
    padding-left: 0 !important;
    font-size: 12px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 18px;
    letter-spacing: 0.3px;
    text-align: left;
    color: #787878 !important;
    padding:0 20px 10px 0;
  }

  .collegeInfoNew .ctaMobileContainer {
    display: flex;
    flex-basis: 100%;
    gap: 12px;
    justify-content: space-between;
    margin-top: -5px;
  }

  .collegeInfoNew .ctaMobileContainer .ctaMobileRed {
    flex-basis: 50%;
  }

  .courseTypeMaster .courseMain .btnDiv.mobileOnly {
    flex-basis: 100%;
    display: flex !important;
    gap: 10px;
    margin-top: 10px;
  }

  .courseTypeMaster .courseMain .btnDiv.mobileOnly .primaryBtn.interestedButton {
    border-radius: 3px;
    border: solid 1.3px #ff4e53;
    background-color: rgba(255, 78, 83, 0.1);
    font-size: 14px;
    font-weight: 700;
    color: #ff4e53;
    flex: 1;
    max-height: 34px;
  }

  .courseTypeMaster .courseMain .btnDiv.mobileOnly .primaryBtn.brochureBtn {
    border: none !important;
    margin: 0;
    flex: 1;
    max-height: 34px;
  }

  .courseTypeMaster .courseMain .courseData {
    flex-basis: 45%;
  }

  .qnaIcon {
    margin-right: 0;
  }

  .courseTypeDiv .textBlue {
    font-size: 12px !important;
    font-weight: 500;
  }

  .courseTypeDiv .viewDetailedFee .textBlue {
    font-weight: 500;
  }

  .collegeInfoNew ul li.ctaMobileBlue::before {
    content: "\2022";
    color: #282828;
    font-size: 14px;
  }

  .collegeInfoNew .btnDiv .writeReview .applyRedIcon {
    top: 7px;
    vertical-align: middle;
    margin-left: 4px;
    position: static;
    background-position: -165px -595px;
  }

  .filterSectionForm {
    margin-bottom: 0 !important;
  }

  .tabs {
    margin-bottom: 5px !important;
  }

  .filterCheckButtons {
    padding-bottom: 0 !important;
  }

  .collegeInfoNew ul li.ctaMobileBlue::before {
    content: "\2022";
    color: #282828;
    font-size: 14px;
    margin-left: 5px;
  }

  .courseTypeMaster .courseMain .courseData div:first-child {
    padding-bottom: 0;
  }

  .collegeHeighlights .table-responsive td {
    padding: 0;
    border: 0;
  }

  .brochureBtn.fixedbrochureBtn {
    position: fixed;
    width: 100%;
    left: 0;
    top: 38px;
    z-index: 2;
    margin: 0;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    border-radius: 0;
    transition: none;
  }

  .collegeInfoNew.mobileOnly .brochureBtnNotFixed {
    border: none !important;
    margin: 0 !important;
    flex-basis: 50%;
  }

  .scholarshipTable.mobileOnly+.readMoreDiv {
    display: block !important;
  }

  .scholarshipTable .table-responsive {
    margin-bottom: 0;
  }

  .scholarshipTable table td {
    min-width: 0;
  }

  .scholarshipTable table td:last-child a {
    display: inline-block;
  }

  .questionIcon {
    margin-right: 0;
  }

  .courseFeesTable td {
    min-width: 0;
  }

  .courseTypeMaster .courseTypeDiv:last-child .applyNowButtonContainer {
    bottom: 5px;
  }

  .courseTypeMaster .courseMain .courseDetails .courseExamAccepted {
    max-width: unset;
  }

  .collegeInfo:before {
    border-radius: 0;
  }

  .courseListContainer .courseListCard .courseDetailsRow .rightDetails {
    flex-basis: 45%;
  }

  .courseListContainer .courseListCard .courseDetailsRow .leftDetails h3 a,
  .courseListContainer .courseListCard .courseDetailsRow .leftDetails h3,
  .courseListContainer .courseListCard .courseDetailsRow .rightDetails p {
    font-size: 15px;
  }

  .courseListContainer .courseListCard .courseDetailsRow .rightDetails a,
  .courseListContainer .courseListCard .courseDetailsRow .rightDetails button,
  .courseListContainer .courseListCard .courseDetailsRow .leftDetails .courseItems,
  .courseListContainer .courseListCard .specialisationRow .courseExamAccepted,
  .courseListContainer .courseListCard .specialisationRow .courseExamAccepted span,
  .courseListContainer .courseListCard .specialisationRow ul li:first-child,
  .courseListContainer .courseListCard .courseDetailsRow .rightDetails p span {
    font-size: 12px;
  }

  .courseListContainer .courseListCard .specialisationRow ul {
    gap: 0;
  }

  .courseListContainer .courseListCard .specialisationRow ul li {
    line-height: 1.5;
    font-size: 12px;
    overflow-wrap: break-word;
  }

  .courseListContainer .courseListCard .courseDetailsRow .leftDetails {
    flex-basis: 55%;
  }

  .courseListCard .specialisationRow .courseExamAccepted {
    font-size: 15px;
    font-weight: 400;
    color: #282828;
    max-width: 350px;
    margin-bottom: 8px;
    line-height: 1.5;
  }

  .courseListCard .specialisationRow .courseExamAccepted span {
    font-weight: 500;
    color: #3d8ff2;
    line-height: 22px;
  }

  .courseListContainer {
    padding: 10px;
  }

  .courseListContainer .courseListCard .specialisationRow ul li:first-child {
    margin-right: 5px;
    font-weight: 400;
  }

  .courseListCard .specialisationRow li:not(:last-child)::after {
    margin-right: 5px;
  }

  .infoBroucherButton,
  .admissionButton {
    flex-basis: 50%;
    max-height: 34px;
  }

  .courseListContainer .courseListCard .buttonRow {
    gap: 10px;
  }

  .pageData h2 {
    background: 0 0;
    text-transform: none;
    font-weight: 700;
    background-color: #f5f5f5;
  }

  .admissionPageData table td:first-child,
  .facilitiesTable table td:first-child {
    min-width: unset;
  }

  a.moreCoursesLink {
    width: 100%;
    max-width: unset;
  }

  .lastDateToApply.mobileOnly {
    height: auto;
    padding: 10px 0;
  }

  .collegeInfo {
    max-height: 146px;
    height: max-content;
  }

  .collegeInfo h1 {
    -webkit-line-clamp: unset;
  }

  .courseListContainer .courseListCard .buttonRow .admissionButton {
    min-width: 157px;
  }

  .collegeHeighlights table td:first-child {
    min-width: 0;
  }

  .collegeHeighlights .table-responsive table td {
    padding-left: 10px;
  }

  .moreCoursesLinkContainer {
    margin-top: 10px;
  }

  .collegeHeighlights .table-responsive table tbody tr td {
    padding-top: 5px;
  }

  .courseExams .courseExamAccepted {
    font-size: 12px;
    line-height: 22px;
  }

  .filterSection .filterCheckButtons .filterCheckContainer input {
    margin: 0 7px 0 0;
  }

  .courseTypeDiv .courseFee {
    margin-bottom: 0;
    line-height: 1.6;
  }

  .courseTypeDiv .applyNowButtonContainer button {
    height: 30px;
    padding: 3px 23px 5px 5px;
  }

  .applyWhiteIcon {
    top: 6px;
    right: 24px;
  }

  .tooltipIcon.tooltipAngle {
    width: 0;
    height: 0;
    border-top: 10px dashed;
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
    vertical-align: middle;
  }

  .tooltipIconText {
    top: 70px;
  }

  .courseListContainer .courseListCard .buttonRow .admissionButton,
  .courseListContainer .courseListCard .buttonRow .infoBroucherButton {
    padding: 7px 15px;
    font-weight: 500;
  }

  .courseListContainer .courseListCard {
    padding-bottom: 10px;
    margin-bottom: 10px;
  }

  .topHeader {
    height: 50px;
    padding: 4px 20px;
  }

  .hambergerIcon {
    width: 27px;
    height: 25px;
    background-position: 480px -71px;
    margin-left: 0;
    vertical-align: middle;
  }

  .headerLogo {
    transform: scale(0.7);
  }

  .searchIcon {
    height: 24px;
    background-position: 534px -74p;
  }

  .collegeInfoNew.mobileOnly ul li i.spriteIcon:first-child {
    margin-left: 5px;
  }

  .collegeInfo.mobileOnly ul li i.spriteIcon {
    transform: scaleX(0.98) scaleY(0.93);
  }

 

  .faq_section p {
    padding-left: 10px;
    padding-right: 10px;
  }

  .collegeHeighlights table {
    border-bottom: 0.2px solid #eaeaea;
  }

  .courseListContainer h2 {
    background: 0 0;
    border-bottom: solid 1px #d8d8d8;
    margin-bottom: 10px;
    background-color: #f5f5f5;
  }

  .courseListContainer .courseListCard .courseDetailsRow .rightDetails p {
    font-weight: 500;
  }

  .courseListContainer .courseListCard .specialisationRow {
    margin-top: 6px;
  }

  .courseListContainer .courseListCard .courseDetailsRow .leftDetails h3 {
    margin-bottom: 6px;
  }

  .courseListContainer .courseListCard .buttonRow {
    margin-top: 10px;
  }

  .filterSection .filterSectionForm .tabs .tab-link {
    padding: 1px 6px 4px;
  }

  .collegeInfoNew ul li:first-child::before {
    content: none;
  }

  .verifiedIcon {
    background-position: -671px -557px;
  }

  .check-your-chance {
    color: #ff4e53;
    font-size: 12px;
    font-weight: 500;
    padding-right: 0;
  }

  .stickyAds {
    position: static;
  }

  .brochureBtn.scholarshipBtn {
    border: 5px solid var(--color-white) !important;
  }

  .brochureBtn.scholarshipBtn button {
    height: 36px;
    padding: 4px 15px;
  }

  .filterHeading {
    font-size: 15px;
    padding: 0;
    padding-left: 5px;
    background: 0 0;
    text-transform: none;
    font-weight: 700;
    margin-bottom: 10px;
  }

  .pageData.placementHighlight table td {
    min-width: 0;
  }

  .cutOffDiv table td {
    min-width: unset !important;
  }

  .cutOffDetailSection .cutOffMainHeading {
    background-color: #f5f5f5;
    padding: 8px 10px;
    font-size: 15px;
    font-weight: 500;
    color: #282828;
  }

  .cutOffDetailSection .cutOffDetailSectionPara {
    margin-bottom: 10px;
  }

  .cutOffCard .cutOffCardHeading {
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
  }

  .cutOffCard .cutOffCardHeading h3 {
    font-size: 15px;
    font-weight: 500;
    color: #0d3d63;
  }

  .cutOffCard .cutOffCardHeading .selectOptionsDiv {
    display: flex;
    justify-content: space-between;
    flex-grow: 1;
    gap: 10px;
  }

  .cutOffCard .cutOffCardHeading .selectOptionsDiv select {
    flex: 1;
    margin: 0;
  }

  .cutOffCard .table-responsive table td {
    min-width: 1px;
  }

  .cutOffCard .primaryBtn {
    width: 100%;
    margin-top: 10px;
  }

  .cutOffDiv .updated-info.row img {
    border-radius: 50%;
  }

  .cutOffDiv .updated-info.row .verifiedIcon {
    background-position: -671px -557px;
    transform: scale(0.7);
  }

  .cutOffDiv .updated-info.row .authorAndDate p {
    margin-top: -5px;
  }

  .cutOffDiv .updated-info.row .authorAndDate .authorName {
    font-weight: 600;
    line-height: 18px;
    color: #787878 !important;
  }

  .ciPageTable td a {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.71;
  }

  .ciPageTable td:nth-child(1) {
    min-width: unset;
  }

  .ciPageTable td:nth-child(2) {
    min-width: unset;
  }

  .ciPageTable td:nth-child(3) {
    min-width: unset;
  }

  .ciPageList .allCoursesHeading {
    padding-top: 0;
    border-top: none;
    font-size: 15px;
  }

  .ciPageList .courseTypeDiv .courseNameAndEligibility .courseItems {
    font-size: 12px;
    line-height: 2;
    margin-top: 4px;
  }

  .ciPageList .courseTypeDiv .applyNowButtonContainer .applyWhiteIcon {
    right: 25px;
  }

  .collegeHeighlights .table-responsive .courseFeesTable tr:first-child td {
    padding-top: 10px;
  }

  .collegeHeighlights .table-responsive .courseFeesTable tr:last-child td {
    padding-bottom: 10px;
  }

  .collegeHeighlights .table-responsive .courseFeesTable td {
    padding: 5px 0 0;
    min-width: unset;
    padding-left: 10px;
  }

  .collegeHeighlights .table-responsive .courseFeesTable td:first-child {
    max-width: unset;
    line-height: 24px;
  }

  .collegeHeighlights .table-responsive .courseFeesTable td:last-child {
    padding-right: 10px;
  }

  .collegeHeighlights .table-responsive .courseFeesTable .heighlightsIcons1,
  .collegeHeighlights .table-responsive .courseFeesTable .heighlightsIcons2,
  .collegeHeighlights .table-responsive .courseFeesTable .heighlightsIcons3,
  .collegeHeighlights .table-responsive .courseFeesTable .heighlightsIcons4,
  .collegeHeighlights .table-responsive .courseFeesTable .heighlightsIcons5,
  .collegeHeighlights .table-responsive .courseFeesTable .heighlightsIcons6,
  .collegeHeighlights .table-responsive .courseFeesTable .heighlightsIcons7,
  .collegeHeighlights .table-responsive .courseFeesTable .heighlightsIcons8 {
    margin-right: 10px;
  }

  .courseHighlightIcon {
    width: 16px;
    height: 16px;
    vertical-align: text-bottom;
    margin-right: 10px;
  }

  .courseHighlight1 {
    background-position: -492px -882px;
  }

  .courseHighlight2 {
    background-position: -518px -882px;
  }

  .courseHighlight3 {
    background-position: -544px -882px;
  }

  .courseHighlight4 {
    background-position: -570px -884px;
  }

  .courseHighlight5 {
    background-position: -596px -882px;
  }

  .three-cardDisplay .sliderCardInfo {
    width: 224px;
    display: inline-block !important;
    margin-right: 5px;
    vertical-align: middle;
  }

  .three-cardDisplay .sliderCardInfo:first-child {
    margin-left: 0;
  }

  .three-cardDisplay .sliderCardInfo:nth-of-type(4n + 1) {
    margin-left: 5px;
  }

  .three-cardDisplay .sliderCardInfo figure {
    height: 168px;
    overflow: hidden;
  }

  .three-cardDisplay .sliderCardInfo .textDiv {
    padding: 10px;
  }

  .three-cardDisplay .sliderCardInfo .widgetCardHeading {
    font-weight: 400;
  }

  .three-cardDisplay .sliderCardInfo .subText {
    padding: 10px;
  }

  .pageData.pageInfo.courseAndFeeDiv.ciPage table td {
    min-width: unset;
  }

  .sideBarSection .sidebarTextLink p.cardText {
    font-size: 14px;
  }

  .moreReviews .reviewCard {
    padding: 10px;
  }

  .reviewCard .reviewerDetailCardHeader {
    position: relative;
  }

  .reviewCard .reviewerDetailCardHeader .reviewerIconCardHeader {
    position: absolute;
    left: 0;
  }

  .reviewCard .reviewerDetailCardHeader .reviewerHeaderContent {
    margin-left: 50px;
    position: relative;
    padding-bottom: 20px;
  }

  .reviewCard .reviewerDetailCardHeader .reviewLandingHeader {
    padding-bottom: 0 !important;
  }

  .reviewCard .reviewerDetailCardHeader .reviewerHeaderContent .reviewerNameCardHeader {
    display: block;
  }

  .moreReviews .reviewCard .reviewerDetailCardHeader .reviewerNameCardHeader {
    font-size: 14px;
  }

  .reviewerHeaderContent.reviewLandingHeader span:after {
    content: "\2022";
    margin-left: 5px;
    margin-right: 5px;
  }

  .reviewerHeaderContent.reviewLandingHeader span:after {
    content: "\2022";
    margin-left: 5px;
    margin-right: 5px;
  }

  .reviewerHeaderContent.reviewLandingHeader span:after {
    content: "\2022";
    margin-left: 5px;
    margin-right: 5px;
  }

  .moreReviews .reviewCard .ratingList {
    grid-row-gap: 5px;
  }

  .ratingList {
    grid-template-columns: repeat(3, max-content) !important;
    grid-auto-rows: 1fr;
  }

  .reviewCard .ratingList div:nth-child(3n + 1) ::before {
    content: none !important;
  }

  .reviewCard .ratingList .full-star,
  .reviewCard .ratingList .empty-star,
  .reviewCard .ratingList .half-star {
    transform: scale(0.6);
    margin: 0 -5px;
  }

  .full-star,
  .half-star,
  .empty-star {
    vertical-align: middle;
  }

  .moreReviews .reviewCard .redirectreviewCard {
    margin-left: -10px;
    margin-bottom: -10px;
    margin-right: -10px;
    background-color: #fafbfc;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 0;
    border-radius: 4px;
  }

  .moreReviews .reviewCard .redirectreviewCard a {
    padding-left: 7px;
  }

  .reviewerHeaderContent.reviewLandingHeader span:not(.reviewerNameCardHeader):before {
    content: none;
  }

  .reviewerHeaderContent.reviewLandingHeader span:last-child::after,
  .reviewerHeaderContent.reviewLandingHeader span:first-child::after {
    content: none;
  }

  .reviewsSection ul {
    margin: 5px 0;
    padding-left: 0;
  }

  .collegeReviewPage .allReviews .col-md-3 {
    flex: unset;
    max-width: unset;
  }

  .collegeReviewPage .allReviews .col-md-5 {
    max-width: initial;
    padding-right: 0;
  }

  .moreReviews .reviewCard .ratingList .full-star {
    margin: 0 -5px !important;
  }

  .redirectreviewCard.redirectionUrl.readMoreIcon p {
    padding-left: 10px;
    padding-bottom: 0;
  }

  .discussionForumSection .qnaAnswer {
    background: none;
    border: none;
  }

  .discussionForumSection .gmuLable {
    float: left;
  }

  .discussionForumSection .answerBy span.year {
    margin-left: 44px;
    margin-top: 5px;
  }

  .discussionForumSection .writeAnswer {
    background-color: #0966c2;
  }

  .discussionForumSection .writeAnswer,
  .discussionForumSection .moreAnswer {
    width: 50%;
    max-width: 145px;
    white-space: nowrap;
  }

  .discussionForumSection {
    padding: 10px;
    margin-bottom: 10px;
  }

  .qnaWidget h2 {
    background: none;
    text-transform: none;
    margin-bottom: 10px;
    background: #f5f5f5;
  }

  .cutOffCard .cutoffMatserType {
    padding-bottom: 10px;
    margin-bottom: 10px;
  }

  .programInfoDiv .feesIcons {
    background-position: -596px -882px;
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }

  .programInfoDiv .courseDuratioIcon {
    background-position: -518px -882px;
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }

  .programInfoDiv .eligibilityIcon {
    background-position: -544px -882px;
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }

  .programInfoDiv .seatsIcon,
  .programInfoDiv .approvalIcon,
  .programInfoDiv .modeIcon {
    margin-right: 10px;
  }

  .programInfoDiv table td {
    padding-bottom: 10px;
    padding-top: 10px;
    vertical-align: text-top;
  }

  .programInfoDiv table td:first-child {
    padding-left: 10px;
  }

  .programInfoDiv .updated-info.row {
    margin-bottom: 10px;
  }

  .programInfoDiv .updated-info.row .authorAndDate p {
    color: #787878;
  }

  .ciPageTable td:nth-child(1) {
    min-width: unset;
  }

  .ciPageTable td:nth-child(2) {
    min-width: unset;
  }

  .ciPageTable td:nth-child(3) {
    min-width: unset;
  }

  .pageData.programInfoDiv table td {
    border-right: none;
    border-bottom: none;
  }

  .pageData.programInfoDiv table tr:last-child td {
    border-bottom: 1px solid #d8d8d8;
  }

  .pageData.programInfoDiv .latestUpdates {
    margin-bottom: 10px;
  }

  .pageData.programInfoDiv .latestUpdates .cardHeading {
    font-size: 14px;
  }

  .pageData.programInfoDiv p {
    font-size: 14px;
    line-height: 1.71;
  }

  .programInfoTable table thead td {
    font-size: 14px;
  }

  .centerAlignButton .downloadButton {
    width: 100%;
    margin-top: 10px;
  }

  .pageData.programInfoDiv table td {
    padding: 5px 0 5px 10px;
    border-right: 0.2px solid #eaeaea;
    border-bottom: 0.2px solid #eaeaea;
  }

  .programInfoTable table tbody td p:first-child {
    font-size: 14px;
  }

  .programInfoTable table tbody td {
    font-size: 14px;
    padding: 5px 0 5px 10px;
  }

  .centerAlignLastCell .centerAlignButton .downloadButton {
    margin-top: 0;
  }

  .placementDiv p {
    font-size: 14px;
    color: #282828;
  }

  .placementDiv .listHeading {
    margin-bottom: 10px;
    font-size: 14px;
  }

  .placementDiv .placementList li {
    font-size: 14px;
  }

  .placementDiv .viewAllCompanies {
    font-size: 14px;
  }

  .placementDiv .centerAlignButton {
    margin-top: 10px;
  }

  .programInfoTable table thead td {
    padding-left: 10px;
  }

  .centerAlignLastCell table thead td:last-child {
    padding-left: 10px;
  }

  .centerAlignLastCell table tbody td:last-child {
    text-align: left;
  }

  table.importantEventsAndDatesTable tbody td:last-child {
    padding: 10px;
    text-align: center;
  }

  .programInfoDiv .modeIcon {
    width: 30px;
  }

  .programPage .feesIcons,
  .programPage .examTypeIcon,
  .programPage .seatsIcon,
  .programPage .blueLabel,
  .programPage .courseDuratioIcon,
  .programPage .modeIcon {
    width: 30px;
    margin-right: 10px;
  }

  .programInfoDiv .courseDuratioIcon {
    background-position: -514px -882px;
    width: 30px;
    margin-right: 13px;
  }

  .blueLabel {
    background-position: 137px -322px;
  }

  .tableGridItem {
    grid-template-columns: 30px 1fr;
    display: grid;
  }

  .courseHighlight3 {
    align-self: center;
  }

  .pageData.programInfoDiv table tr td:last-child {
    padding-left: 15px;
  }

  .newCapIcon {
    background-position: -27px -1108px;
    width: 16px;
    height: 11px;
    margin-right: 10px;
  }

  .newSocialIcon {
    background-position: -58px -1108px;
    width: 16px;
    height: 12px;
    margin-right: 10px;
  }

  .newClockIcon {
    background-position: -89px -1106px;
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }

  .newFlagIcon {
    background-position: -148px -1106px;
    width: 16px;
    height: 15px;
    margin-right: 10px;
  }

  .newInstituteIcon {
    background-position: -176px -1107px;
    width: 16px;
    height: 15px;
    margin-right: 10px;
  }

  .newEmblemIcon {
    background-position: -207px -1105px;
    width: 16px;
    height: 18px;
    margin-right: 10px;
  }

  .newLinkIcon {
    background-position: -120px -1109px;
    width: 16px;
    height: 10px;
    margin-right: 10px;
  }

  .newRupeeIcon {
    background-position: -238px -1106px;
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }

  .newWriteIcon {
    background-position: -269px -1106px;
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }

  .newAcademicIcon {
    background-position: -300px -1106px;
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }

  .newRankIcon {
    margin-right: 10px;
  }

  .mobileSubNavDropDownMenu {
    background: rgba(0, 0, 0, 0.119215);
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
    display: none;
  }

  .mobileSubNavDropDownDiv {
    position: fixed;
    height: auto;
    bottom: 0;
    left: 0;
    overflow: auto;
    border-radius: 4px 4px 0 0;
    width: 100%;
    background: #fff;
    z-index: 3;
  }

  .mobileSubNavDropDownDiv ul {
    margin: 0;
    padding: 0 20px;
  }

  .mobileSubNavDropDownDiv ul li {
    font-size: 14px;
    line-height: 24px;
    border-bottom: 1px solid #d8d8d8;
    display: flex;
    color: #787878;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }

  .mobileSubNavDropDownDiv ul li a {
    background: 0 0;
    border: none;
    color: #787878;
  }

  .mobileSubNavDropDownMenu.current {
    display: block;
  }

  .examRelataedLinks .mobileSubNavDropDownDiv ul {
    height: unset;
  }

  .mobileSubNavDropDownDiv ul li a {
    padding: 0;
  }

  .subNavDropDown:hover .caret {
    background-position: -438px -1089px;
    -webkit-transform: unset;
    transform: unset;
  }

  .mobileSubNavDropDown .caret {
    background-position: -382px -1057px;
    width: 25px;
    height: 37px;
    margin-left: 2px;
    margin-right: -10px;
    position: relative;
    bottom: 3px;
    transform: none !important;
  }

  .examRelataedLinks ul {
    overflow-y: hidden;
  }

  .subNavDropDown:has(.activeLink:hover):hover .caret {
    background-position: -382px -1057px;
  }

  .lead-cta-cls-button {
    /*display: unset;*/
  }

  .filterHeading {
    font-size: 15px;
    padding: 0;
    padding-left: 5px;
    background: none;
    text-transform: none;
    font-weight: 700;
    margin-bottom: 10px;
  }

  .scholarshipBtnCls {
    height: 46px;
  }

  .aside-college {
    min-height: 0px;
  }

  .lead-cta-cls {
    display: contents;
  }

  .getSupport .getSupport__subheading {
    display: none;
  }

  .getSupport .button__row__container {
    width: 100%;
  }

  .getSupport button {
    flex-grow: 1;
  }

  .collegeHeighlights .table-responsive .courseFeesTable td:first-child {
    display: flex;
    align-items: center;
  }

  .heighlightsIcons1,
  .heighlightsIcons2,
  .heighlightsIcons3,
  .heighlightsIcons4,
  .heighlightsIcons5,
  .heighlightsIcons6,
  .heighlightsIcons7,
  .heighlightsIcons8 {
    flex-shrink: 0;
  }

  .collegeHeighlights .courseFeesTable .newLinkIcon {
    background-position: -113px -1100px;
    width: 27px;
    height: 27px;
  }

  /* college-compare css */

  /* college-comapre css starts*/
  .collegeInfo__flexContent {
    flex-wrap: nowrap;
  }

  .collegeInfo {
    padding: 10px;
    padding-top: 20px;
  }

  .compareText {
    font-size: 12px;
  }

  .heroSection__leftSide {
    /*flex-direction: column;*/
  }

  .heroSection__leftSide .collegeIntro ul {
    display: none;
  }

  .collegeCompare__container {
    padding: 10px 0;
  }

  .collegeCompare__headingContainer {
    margin-bottom: 10px;
  }

  .collegeCompare__headingContainer .compareCollege__heading {
    font-size: 16px;
  }

  .selectCollegeScreen__panels .selectionPanel {
    padding: 12px 0;
    flex-direction: column;
    gap: 6px;
    width: 100%;
  }

  .selectCollegeScreen__panels .selectionPanel.selectCollegeScreen__leftPanel {
    padding-right: 12px;
  }

  .selectCollegeScreen__panels .selectionPanel.selectCollegeScreen__rightPanel {
    padding-left: 12px;
  }

  .selectCollegeScreen__panels .selectionPanel.unselectedPanel {
    align-items: center;
  }

  /* .selectCollegeScreen__panels .selectionPanel .selectionLogo__div {
    width: 40px;
    height: 40px;
  } */

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs {
    gap: 10px;
    width: 100%;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege,
  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedProgram {
    gap: 0px;
    flex-wrap: wrap;
  }

  .selectCollegeScreen__panels .selectionPanel .collegeCompare__drawer__mobile .selection__Inputs .selectedCollege,
  .selectCollegeScreen__panels .selectionPanel .collegeCompare__drawer__mobile .selection__Inputs .selectedProgram {
    flex-wrap: unset;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege .selectedCollege__heading,
  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege .selectedProgram__heading,
  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedProgram .selectedCollege__heading,
  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedProgram .selectedProgram__heading {
    flex-basis: 80%;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege.value__selected {
    margin-bottom: 5px;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege .selectedCollege__heading {
    font-size: 14px;
    line-height: 22px;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege .editIcon {
    align-self: flex-end;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedProgram .selectedProgram__heading {
    font-size: 14px;
    line-height: 22px;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs {
    width: 100%;
    display: block;
    position: relative;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .clearSelection {
    top: -70px;
    right: -10px;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box {
    padding: 12px 15px;
    border: 1px solid var(--border-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--light-gray-color);
    background-color: var(--white-color);
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box p,
  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box img {
    pointer-events: none;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box .editIcon {
    flex-basis: 20%;
    align-self: flex-end;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box:first-child {
    margin-bottom: 12px;
  }

  .collegeCompare__compareButton__container {
    padding: 10px;
  }

  .collegeCompare__compareButton__container .collegeCompare__compareButton {
    width: 100%;
  }

  .collegeCompare__drawer__mobile {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: var(--white-color);
    z-index: 20;
    top: 0;
    left: 0;
    animation: slideIn 1000ms ease-in-out;
    display: none;
  }

  .collegeCompare__drawer__mobile .selection__Input__Box .select2 {
    width: 100% !important;
  }

  .collegeCompare__drawer__mobile .drawercloseIcon {
    background-position: -244px -1142px;
    width: 13px;
    height: 13px;
  }

  .collegeCompare__drawer__mobile .drawer__heading {
    padding: 18px 20px;
    border-bottom: 1px solid var(--border-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .collegeCompare__drawer__mobile .drawer__heading h2 {
    color: var(--primary-black-color);
    font-size: 16px;
    font-weight: 600;
    line-height: normal;
    background-color: #f5f5f5;
    padding-left: 5px;
  }

  .collegeCompare__drawer__mobile .drawer__subheading {
    color: var(--light-gray-color);
    font-size: 14px;
    font-weight: 400;
  }

  .collegeCompare__drawer__mobile .drawer__container {
    padding: 24px 20px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    height: calc(100% - 56px);
  }

  .collegeCompare__drawer__mobile .drawer__content .selectedCollege.value__selected {
    border-bottom: 1px solid var(--border-gray);
    padding-bottom: 15px;
  }

  .collegeCompare__drawer__mobile .selectedProgram__heading {
    font-weight: 700;
  }

  .collegeCompare__drawer__mobile .selection__Inputs .selectedCollege,
  .collegeCompare__drawer__mobile .selection__Inputs .selectedProgram {
    justify-content: space-between;
  }

  .collegeCompare__drawer__mobile .drawer__close__submit {
    width: 100%;
    background-color: var(--primary-red-color);
    color: var(--white-color);
    border: none;
    padding: 9px 18px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 700;
  }

  .selectCollegeScreen__panels .selectionPanel.selectCollegeScreen__leftPanel,
  .selectCollegeScreen__panels .selectionPanel.selectCollegeScreen__rightPanel {
    padding: 18px;
  }

  /* college-compare css ends */
  .timelineModal {
    padding: 20px;
  }

  .timelineModal .timelineModal--closeIcon {
    top: 20px;
    right: 20px;
  }

  .timelineModal .timelineModal--heading {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.5;
    color: #282828;
    margin-bottom: 10px;
    max-width: calc(100% - 16px);
  }

  .timelineList li {
    padding-top: 0;
    padding-bottom: 10px;
  }

  .timelineList li h3 {
    font-size: 15px;
    line-height: 1.71;
  }

  .timelineList li p {
    font-size: 12px;
    line-height: 1.67;
  }

  .timelineList li.activeDate h3 {
    font-size: 15px;
    line-height: 1.71;
  }

  .timelineList li.presentDate {
    padding-top: 10px;
  }

  .timelineList li.presentDate p {
    font-size: 12px;
    line-height: 1.67;
  }

  .timelineList li.presentDate+.activeDate {
    padding-top: 10px;
  }

  .timelineList li.presentDate+.activeDate .circleSpot {
    top: 15px;
  }
}