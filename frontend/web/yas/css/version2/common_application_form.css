body {
    background: #F1F1F1;
    overflow-x: hidden;
    font-family: helvetica !important
}

#gmu_search_bar {
    display: none;
}

.write-review {
    display: none !important;
}

.twoDbackground {
    width: 100%;
    height: 600px;
    background: repeating-linear-gradient(80deg, #4374b9, #4374b9, #ee424f, #ee424f), linear-gradient(45deg, transparent 50%, transparent 30%);
    position: absolute;
    -webkit-transform: skew(0deg, -10deg);
    transform: skew(0deg, -10deg);
    padding: 200px 0;
    margin-top: -250px;
}

.form_heading h2 {
    color: white;
    font-size: 20px;
    font-weight: bold;
    line-height: 30px;
    letter-spacing: 0px;
    margin-top: 0px;
}

.form_heading p {
    color: white;
    font-size: 14px;
}

.form-banner {
    padding-top: 50px;
    padding-left: 15px;
}

.input-field {
    position: relative;
    width: 250px;
    height: 44px;
    line-height: 44px;
    margin-top: 50px;
}

#common_application_lead_form label {
    font-size: 12px;
    position: absolute;
    top: 10px;
    left: 15px;
    color: #333;
    transition: 0.2s all;
    display: inline;
}

#common_application_lead_form {
    background: white;
    margin-top: 25px;
    margin-bottom: 50px;
    padding: 5px 30px 20px 30px;
    text-align: left;
}

#common_application_lead_form input {
    width: 100%;
    border: 0;
    outline: 0;
    padding: 5px 0px;
    border-bottom: 1px solid #d3d3d3;
}

#common_application_lead_form input:focus~label,
#common_application_lead_form input:valid~label {
    top: -13px;
    color: #d3d3d3;
}

.clg_suggestion {
    min-height: 280px;
    width: 100%;
    padding: 30px 10px 0px;
    background: white;
    display: inline-block;
    text-align: center;
    cursor: pointer;
}

.clg_suggestion h1 {
    font-size: 14px;
    font-weight: bold;
    min-height: 45px;
    line-height: 21px;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.clg_suggestion_heading {
    background: white;
    padding: 5px 30px;
    margin: 15px;
}

.clg_suggestion_heading h1 {
    font-size: 28px;
    font-weight: bold;
    color: #4374b9;
}

.clg_suggestion_heading p {
    font-size: 15px;
    color: #333;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.select2-container--default .select2-selection--single {
    border: none;
    border-bottom: 1px solid #d3d3d3;
    padding-left: 0px;
    border-radius: 0px;
}

#course,
#qualification,
#current_location {
    border: none;
    border-bottom: 1px solid #d3d3d3;
    padding-left: 0px;
    box-shadow: none;
    color: #333;
    font-weight: bold;
    font-size: 12px;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #333;
    font-size: 12px;
    font-weight: bold;
}

a:focus,
a:hover {
    text-decoration: none;
}

.request_call_back_div {
    font-size: 24px;
    color: black;
    font-weight: bold;
    margin-top: 20px;
}

.request_call_back_div label {
    color: #444444;
    left: 0px;
    top: 0px;
    text-align: left;
    font-size: 14px;
    font-weight: normal;
}

#select2-current_location-container,
#select2-course-container,
#select2-qualification-container {
    padding-left: 0px;
}

#want-to-get-in {
    border-radius: 0px;
    box-sizing: 0px;
    border: 0px;
    padding: 8px 20px;
    background: #337ab7;
    color: white;
    font-weight: bold;
    font-size: 13px;
}

.star {
    position: absolute;
    width: 0;
    height: 0;
    border-top: 50px solid #00c853;
    border-left: 50px solid transparent;
    right: 15px;
    top: 0px;
}

.testing input[type=checkbox] {
    display: none;
}

.testing label div {
    width: 16px;
    height: 16px;
    border: 1px solid #999999;
    text-align: center;
    line-height: 16px;
    margin-right: 6px;
    cursor: pointer;
    border-radius: 3px;
}

label span {
    line-height: 16px;
}

.testing label img {
    width: 11px;
    opacity: 0;
    margin-top: -6px;
}

.testing input:checked+label img {
    opacity: 1;
}

.container.mobile-text-center {
    padding: 30px 0px;
}

label img {
    margin-top: 0px;
}

.pageFooter,
.page-header,
.pageLoader,
.scrollToTop {
    display: none;
}

#common_application_lead_form .select2-container--default .select2-selection--single {
    border: none !important;
    border-bottom: 1px solid #d3d3d3 !important;
    border-radius: 0px !important;
}

#common_application_lead_form .select2-container--default .select2-selection--single .select2-selection__placeholder {
    font-size: 12px;
    position: absolute;
    color: #333;
    transition: 0.2s all;
    display: inline;
}

.errorMsgEmail {
    color: red;
    font-size: 11px;
}

#request_submit_application {
    border: 0px;
    padding: 15px 30px;
    border-radius: 0px;
    background: #00c853;
    color: white;
    font-weight: bold;
    cursor: pointer;
    white-space: normal;
}

#clg_no_list_suggested {
    background: white;
    padding: 5px 30px;
    margin: 15px;
    height: 100px;
    text-align: center;
    font-size: 24px;
    font-size: 28px;
    font-weight: bold;
    color: #4374b9;
    padding: 40px;
    display: none;
}

@media(max-width: 998px) {
    .request_call_back_div label {
        right: 0px;
    }

    #select2-current_location-container {
        text-align: left;
    }
}

@media (max-width: 768px) {
    .request_call_back_div label {
        right: 0px;
        text-align: center;
    }

    .testing label {
        left: 0px;
        text-align: left;
    }

    #select2-current_location-container {
        text-align: left;
    }
}