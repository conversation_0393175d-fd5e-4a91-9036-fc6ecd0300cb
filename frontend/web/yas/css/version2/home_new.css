.text-center {
  text-align: center;
}

.p-lg-075 {
  padding: 0 75px;
}

.trendingBoardCardList .dataCard,
.trendingExamCardList .dataCard,
.trendingCourseCardList .dataCard {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  flex-shrink: 0;
}

.trendingBoardCardList .dataCard a:hover,
.trendingExamCardList .dataCard a:hover,
.trendingCourseCardList .dataCard a:hover {
  text-decoration-color: black;
}

.trendingCourseCardList .dataCard {
  border: 1px solid #d8d8d8;
}

.selected {
  text-decoration: none;
  background: var(--color-red);
  color: var(--color-white);
}

.articleRelataedLinks {
  position: relative;
  border-radius: 4px;
  border: 1px solid #C8C8C8;
  padding: 25px 60px 20px 60px;
}

.articleRelataedLinks ul {
  margin: 0;
  padding: 0;
  white-space: nowrap;
  overflow: auto;
}

.articleRelataedLinks ul::-webkit-scrollbar {
  appearance: none;
}

.articleRelataedLinks ul li:first-child {
  margin-left: 0;
}

.articleRelataedLinks ul li {
  display: inline-block;
  margin: 0 3px;
}

.articleRelataedLinks ul li a.activeLink {
  border-bottom: 2px solid #ff4e53;
  color: #0966C2;
  font-weight: 600;
  border-radius: 3px 3px 0 0;
}

.articleRelataedLinks ul li a {
  background: #fff;
  border-bottom: none;
  display: block;
  padding: 10px 16px;
  color: #333333;
  line-height: 20px;
  font-size: 16px;
  text-decoration: none;
  /* text-transform: uppercase; */
  text-align: center;
  box-shadow: none;
}

.articleRelataedLinks .btn_right,
.articleRelataedLinks .btn_left {
  position: absolute;
  width: 40px;
  height: 40px;
  background-color: #fff;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  top: 22px;
  cursor: pointer;
}

.articleRelataedLinks .btn_left {
  left: 20px;
}

.articleRelataedLinks .btn_right {
  border-left: none;
  right: 14px;
}

.articleRelataedLinks .btn_right,
.articleRelataedLinks .btn_left {
  height: 42px;
}


.articleRelatedCtn {
  display: flex;
  align-items: start;
  gap: 60px;
  padding: 30px 35px;
  border: 1px solid #C8C8C8;
  border-top: none;
  border-radius: 0 0 4px 4px;
}

.articleRelatedCtn .quickLinks {
  flex-basis: 50%;
  top: 0px;
  z-index: 0;
  border-radius: 4px;
  background-color: #fff;
  padding: 20px;
  padding: 0;
  position: relative;
}

.articleRelatedCtn .quickLinks h2 {
  font-size: 18px !important;
  font-weight: 600 !important;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px !important;
  color: #333333;
  text-align: start !important;
}

.articleRelatedCtn .quickLinks ul {
  padding: 10px 0px;
  margin: 0;
  max-height: 440px;
  overflow: auto;
}

.articleRelatedCtn .quickLinks ul li {
  font-size: 15px;
  line-height: 28px;
  list-style-type: none;
}

.articleRelatedCtn .quickLinks ul li a {
  font-size: 14px;
  line-height: 24px;
  border-bottom: 1px solid #d8d8d8;
  text-decoration: none;
  color: #333333;
  padding: 8px 0;
  display: flex;
  margin: 0 10px 0 0;
  gap: 20px;
}

.articleRelatedCtn .quickLinks ul li:last-child a {
  border-bottom: none !important;
}

.articleRelatedCtn .quickLinks ul li a img,
.articleRelatedCtn .quickLinks ul li a div:first-child {
  width: 130px;
  height: 72px;
  flex-shrink: 0;
  border-radius: 8px;
}

.articleRelatedCtn .quickLinks ul li a div:last-child {
  width: 100%;
  height: 16px;
  border-radius: 8px;
}

.animate {
  animation: shimmer 3s;
  animation-iteration-count: infinite;
  background: linear-gradient(to right, #e6e6e6 5%, #cccccc 25%, #e6e6e6 35%);
  background-size: 1000px 100%;
}

@keyframes shimmer {
  from {
    background-position: -1000px 0;
  }

  to {
    background-position: 1000px 0;
  }
}


.articleRelatedCtn .quickLinks ul li a h3 {
  font-size: 16px;
  font-weight: 400;
  line-height: 27px;
  text-align: left;
}

.articleRelatedCtn .quickLinks ul::-webkit-scrollbar {
  width: 6px;
}

.articleRelatedCtn .quickLinks ul::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 80px;
}

.articleRelatedCtn .quickLinks ul::-webkit-scrollbar-thumb {
  background: #C1C1C1;
  border-radius: 80px;
  cursor: pointer;
}

.articleRelatedCtn .quickLinks ul::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.articleRelatedCtn .verticalLine {
  display: none;
}

.errorData {
  position: absolute;
  top: 50%;
  left: 80%;
}

.articleRelataedLinks .homePageArticle {
  cursor: pointer;
}

@media (max-width: 1023px) {

  .articleRelataedLinks {
    padding: 20px 0px 10px 0;
  }

  .articleRelataedLinks ul li:first-child {
    margin-left: 0;
  }

  .articleRelataedLinks ul li a {
    color: #333333;
    line-height: 20px;
    font-size: 14px;
    box-shadow: none;
  }

  .articleRelataedLinks ul li a.activeLink {
    font-weight: 700;
  }

  .articleRelataedLinks .btn_right,
  .articleRelataedLinks .btn_left {
    top: 17px;
  }

  .articleRelataedLinks .btn_right {
    border-left: none;
    right: 0px;
  }

  .articleRelataedLinks .btn_right,
  .articleRelataedLinks .btn_left {
    height: 42px;
  }

  .articleRelatedCtn {
    display: flex;
    align-items: start;
    gap: 30px;
    padding: 27px 12px;
    flex-direction: column;
  }

  .articleRelatedCtn .quickLinks {
    flex-basis: 100%;
    top: 58px;
  }

  .articleRelatedCtn .quickLinks {
    top: 0px !important;
    position: relative !important;
  }

  .articleRelatedCtn .quickLinks h2 {
    font-size: 18px !important;
    font-weight: 600 !important;
    line-height: 28px;
    background: #f5f5f5;
    padding: 8px 20px !important;
    color: #333333;
    text-align: start !important;
  }

  .articleRelatedCtn .quickLinks ul {
    max-height: 250px;
  }

  .articleRelatedCtn .quickLinks ul li a {
    font-size: 14px;
    line-height: 24px;
    border-bottom: 1px solid #d8d8d8;
    text-decoration: none;
    color: #333333;
    padding: 8px 0;
    display: flex;
    margin: 0 10px 0 0;
    gap: 20px;
  }

  .articleRelatedCtn .quickLinks ul li a img {
    width: 80px;
    height: 80px;

  }

  .articleRelatedCtn .quickLinks ul li a h3 {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
  }

  .articleRelatedCtn .quickLinks ul::-webkit-scrollbar {
    appearance: none;
  }

  .articleIndexBox {
    padding: 0 0 40px 0 !important;
  }

  .articleRelatedCtn .verticalLine {
    display: block;
    width: 100%;
    border-top: 1px solid #d8d8d8;
  }

  .articleRelataedLinks .btn_left {
    left: 0;
  }

  .trending {
    width: calc(100% - 36px);
  }
}

.indexSprite {
  background-image: url(../../images/course_sprite.webp);
  display: inline-block;
  text-align: left;
  overflow: hidden;
  width: 68px;
  height: 67px;
  vertical-align: middle;
  cursor: pointer;
  margin: 0;
  transform: scale(0.6);
}

.engineering {
  background-position: -123px -28px;
}

.management {
  background-position: -394px -117px;
}

.science {
  background-position: -305px -291px;
}

.pharmacy {
  background-position: -215px -28px;
}

.law {
  background-position: -305px -117px;
}

.education {
  background-position: -305px -200px;
}

.dental {
  background-position: -123px -292px;
}

.medical {
  background-position: -305px -27px;
}

.agriculture {
  background-position: -28px -292px;
}

.design {
  background-position: -27px -28px;
}

.commerce {
  background-position: -214px -202px;
}

.architecture {
  background-position: -215px -292px;
}

.arts {
  background-position: -393px -27px;
}

.paramedical {
  background-position: -28px -203px;
}

.computer {
  background-position: -122px -203px;
}

.hotelManagement {
  background-position: -28px -114px;
}

.businessManagment {
  background-position: -394px -117px;
}

.veterinary {
  background-position: -123px -117px;
}

.massMedia {
  background-position: -215px -117px;
}

.clgIcon {
  background-position: 738px -5px;
}

.examIcon {
  background-position: 687px -7px;
}

.courseIcon {
  background-position: 639px -5px;
}

.admissionIcon {
  background-position: 590px -5px;
}

.boardIcon {
  background-position: 541px -5px;
}

.studyabroadIcon {
  background-position: 488px -6px;
}

.stateScholarships {
  background-position: 61px -441px;
}

.disciplineScholarships {
  background-position: 324px -394px;
}

.animation {
  background-position: -400px -291px;
}

.aviation {
  background-position: -395px -202px;
}

.mass-communication {
  background-position: -215px -116px;
}

.hotel-management {
  background-position: -27px -115px;
}

.vocational-courses {
  background-position: -27px -374px;
}

.classScholarships {
  background-position: 347px -441px;
}

.courseScholarships {
  background-position: 149px -441px;
}

.categoryScholarships {
  background-position: 237px -394px;
}

.typeScholarships {
  background-position: 118px -394px;
  width: 55px !important;
}

.genderScholarships {
  background-position: 246px -441px;
  width: 48px !important;
}

.countryScholarships {
  background-position: 163px -397px;
}

.reviewIcon {
  background-position: 192px -445px;
}

.ratingIcon {
  background-position: 101px -441px;
}

.collegeIcon {
  background-position: 60px -396px;
}

.usersIcon {
  background-position: 302px -440px;
  width: 51px !important;
}

.questionsIcon {
  background-position: 280px -394px;
}

.competitionsIcon {
  background-position: 198px -397px;
}

.tab-content.activeTab,
.tab-content.activeLink {
  display: block;
}

.basicCta {
  padding: 6px 20px;
  border-radius: 30px;
  margin-right: 20px;
  color: var(--priamry-color);
  margin-bottom: 15px;
  line-height: 24px;
  font-size: 15px;
  display: inline-block;
  border: 1px solid var(--color-red);
  text-decoration: none;
  transition: 0.2s ease;
  font-size: 500;
}

@media (max-width: 1023px) {
  .basicCta {
    padding: 4px 12px;
    margin-right: 5px;
    margin-bottom: 10px;
  }
}

.basicCta:hover {
  color: var(--priamry-color);
  text-decoration: none;
  background: var(--color-red);
  color: var(--color-white);
}

.bg-lightgray {
  background: #f3f2ef !important;
}

.quickLinkCard .spriteIcon {
  width: 46px;
  height: 48px;
  vertical-align: middle;
  margin-bottom: 15px;
}

.indexSection {
  padding: 40px 0;
  background: var(--color-white);
  position: relative;
}

.indexSection h1 {
  font-size: 24px;
  line-height: 44px;
  padding-bottom: 31px;
  color: var(--color-white);
  height: 88px;
}

@media (max-width: 1023px) {
  .indexSection h1 {
    font-size: 15px;
    line-height: 24px;
    padding-bottom: 20px;
    margin-top: 40px;
  }
}

.indexSection h2 {
  font-size: 24px;
  line-height: 38px;
  font-weight: normal;
  padding-bottom: 20px;
  text-align: center;
}

@media (max-width: 1023px) {
  .indexSection h2 {
    font-size: 18px;
    line-height: 28px;
  }
}

.indexSection .sectionSubheading {
  font-size: 15px;
  line-height: 24px;
  text-align: center;
  padding-bottom: 40px;
}

@media (max-width: 1023px) {
  .indexSection .sectionSubheading {
    padding-bottom: 20px;
    margin-top: -10px;
  }
}

.trendingTopicsList {
  padding-bottom: 50px;
  border-bottom: var(--border-line);
  position: relative;
}

@media (max-width: 1023px) {
  .trendingTopicsList {
    padding-bottom: 0;
    border: none;
  }
}

.trendingTopicsList .scrollRight {
  top: -8px;
  right: -5px;
}

.trendingTopicsList .scrollLeft {
  top: 9px;
  left: -4px;
}

.trendingTopicsList ul {
  padding: 0px;
  margin: 0;
  white-space: nowrap;
  display: block;
  overflow: auto;
}

.trendingTopicsList ul::-webkit-scrollbar {
  display: none;
}

.sub-header-drop a {
  color: var(--primary-font-color) !important;
}

.sub-header-drop {
  top: 41px !important;
}

.sub-header-drop li {
  line-height: 2px !important;
}

.sa_dropdown .sub-header-drop {
  top: 40px !important;
  z-index: 2 !important;
}

.sa_dropdown .sub-header-drop .col-md-12 ul li a {
  font-size: 14px;
  font-weight: normal;
  color: #333333;
  padding: 5px;
}

.trendingTopicsList ul li {
  display: inline-block;
  margin: 0 15px;
}

.trendingTopicsList ul li:first-child {
  margin-left: 0px;
}

.trendingTopicsList ul li:last-child {
  margin-right: 0px;
}

.trendingTopicsList ul li a {
  font-size: 18px;
  line-height: 24px;
  display: block;
}

@media (max-width: 1023px) {
  .trendingTopicsList ul li a {
    font-size: 15px;
  }
}

.quickLinkCard {
  padding: 20px;
  display: block;
  flex-basis: 30.7%;
  margin-bottom: 40px;
  margin-right: 40px;
  background: var(--color-white);
  border-radius: 10px;
  text-decoration: none;
  color: var(--priamry-color);
  text-align: center;
  transition: 0.2s ease;
}

@media (max-width: 1023px) {
  .quickLinkCard {
    width: 224px;
    display: inline-block;
    margin-right: 10px;
    border: var(--border-line);
    margin-bottom: 40px;
    white-space: normal;
    padding: 16px 8px;
    vertical-align: middle;
  }
}

.quickLinkCard:nth-of-type(3n) {
  margin-right: 0;
}

@media (max-width: 1023px) {
  .quickLinkCard:nth-of-type(3n) {
    margin-right: 10px;
  }
}

.quickLinkCard:hover {
  text-decoration: none;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
  cursor: pointer;
  color: var(--priamry-color);
}

.quickLinkCard h3 {
  font-size: 18px;
  line-height: 24px;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  min-height: 24px;
}

.quickLinkCard p {
  font-size: 15px;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 3;
  min-height: 72px;
}

@media (max-width: 1023px) {
  .quickLinkCard p {
    -webkit-line-clamp: 4;
    min-height: 96px;
  }
}

.customSlider {
  position: relative;
}

.sliderCard {
  position: relative;
  border: var(--border-line);
  transition: 0.2s ease;
}

.sliderCard:hover {
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
}

.sliderCard:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
}

.sliderCard .cityName {
  position: absolute;
  top: 50%;
  left: 50%;
  color: var(--color-white);
  transform: translate(-50%, -50%);
  z-index: 2;
  font-weight: var(--font-semibold);
  text-decoration: none;
}

.sliderCard img {
  display: block;
  width: 100%;
  height: 207px;
}

@media (max-width: 1023px) {
  .sliderCard img {
    height: 168px;
  }
}

.customSliderList,
.customSliderCards {
  white-space: nowrap;
  overflow: auto;
}

@media (max-width: 1023px) {

  .customSliderList,
  .customSliderCards {
    padding: 5px;
  }
}

.customSliderList::-webkit-scrollbar,
.customSliderCards::-webkit-scrollbar {
  display: none;
}

.customSlider .scrollRight {
  top: 50%;
  transform: translate(0%, -50%);
}

.customSlider .scrollLeft {
  top: 50%;
  transform: translate(0%, -50%) rotate(180deg);
}

.four-cardDisplay {
  padding: 0 20px;
}

@media (max-width: 1023px) {
  .four-cardDisplay {
    padding: 0;
  }
}

.four-cardDisplay .sliderCard {
  width: 23.7%;
  display: inline-block;
  padding: 0;
  white-space: initial;
  margin-right: 15px;
  border-radius: 4px;
  overflow: hidden;
  vertical-align: middle;
}

@media (max-width: 1023px) {
  .four-cardDisplay .sliderCard {
    width: 224px;
    margin-right: 6px;
  }
}

.four-cardDisplay .sliderCard:last-child {
  margin-right: 0;
}

.trendingBtnSection {
  text-align: center;
}

.collegesWithCategory ul {
  text-align: center;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  border-radius: 4px;
  background: var(--color-white);
  padding: 3px;
  margin: 0 auto;
  margin-bottom: 40px;
}

@media (max-width: 1023px) {
  .collegesWithCategory ul {
    margin-bottom: 18px;
  }
}

.collegesWithCategory ul li {
  display: inline-block;
  font-size: 16px;
  line-height: 24px;
  padding: 9px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.collegesWithCategory ul li.tabLink {
  background: #dbebff;
}

@media (max-width: 1023px) {
  .collegesWithCategory ul li {
    padding: 9px 20px;
  }
}

ul.dropdown-menu li.hoverbg a {
  color: #fff;
}

.page-header .searchIcon {
  display: none !important;
}

.limitCards {
  max-height: auto !important;
  overflow: hidden;
}

.exam-tab.selected {
  text-decoration: none;
  background: var(--color-red);
  color: var(--color-white);
}

.collegesWithCategoryData .row {
  margin: 0;
}

.collegesWithCategoryData .primaryBtn.viewMoreCards {
  padding: 10px 40px;
  line-height: 24px;
  margin-top: 20px;
}

@media (max-width: 1023px) {
  .collegesWithCategoryData .primaryBtn.viewMoreCards {
    margin-top: 20px;
  }
}

.collegesWithCategoryData .dataCard {
  flex-basis: 15.2%;
  max-width: 15.2%;
  padding: 20px 5px;
  border-radius: 6px;
  background: var(--color-white);
  margin-right: 20px;
  margin-bottom: 20px;
  text-align: center;
  transition: 0.2s ease;
}

@media (max-width: 1023px) {
  .collegesWithCategoryData .dataCard {
    flex-basis: 100%;
    max-width: 100%;
    margin-right: 0;
    padding: 0px 6px;
    padding-left: 0;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
  }

  .collegesWithCategoryData .dataCard:after {
    content: "";
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translate(0, -50%);
    width: 12px;
    height: 21px;
    background: url(https://www.getmyuni.com/yas/images/master_sprite.webp);
    background-position: 591px -71px;
  }
}

.collegesWithCategoryData .dataCard:nth-of-type(6n) {
  margin-right: 0;
}

.collegesWithCategoryData .dataCard:hover {
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  text-decoration: none;
}

@media (max-width: 1023px) {
  .collegesWithCategoryData .dataCard:hover {
    box-shadow: none;
  }
}

.collegesWithCategoryData .dataCard p {
  font-size: 16px;
  line-height: 24px;
  font-weight: var(--font-semibold);
  color: var(--primary-font-color);
}

@media (max-width: 1023px) {
  .collegesWithCategoryData .dataCard p {
    font-size: 15px;
  }
}

.collegesWithCategoryData .dataCard .count {
  color: #989898;
  font-weight: 500;
}

@media (max-width: 1023px) {
  .collegesWithCategoryData .dataCard .count {
    font-weight: normal;
  }
}

.displayCard {
  border-radius: 4px;
  border: var(--border-line);
  width: 23.7%;
  display: inline-block;
  margin-right: 15px;
  padding: 0;
  white-space: initial;
  text-decoration: none;
  vertical-align: middle;
}

.viewAllDiv {
  min-height: 302px;
  max-width: 276px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  color: #ff4e3a;
}

.viewAllIcon {
  width: 77px;
  height: 76px;
  background-position: 358px -73px;
  margin-bottom: 10px;
  display: block !important;
  margin-bottom: 10px;
}

.viewAllDiv p {
  color: var(--color-red);
  text-align: center;
  font-weight: 600;
}

.viewAllDiv a {
  color: #ff4e3a;
  text-align: center;
}

@media (max-width: 1023px) {
  .displayCard {
    width: 224px;
    margin-right: 5px;
    box-shadow: 0px 0px 4px 0 rgba(0, 0, 0, 0.25);
  }

  .viewAllDiv {
    min-height: 243px;
  }
}

.displayCard:hover .widgetCardHeading {
  color: var(--anchor-textclr);
  text-decoration: none;
}

.displayCard:hover {
  text-decoration: none;
}

.displayCard:last-child {
  margin-right: 0;
}

.displayCard figure {
  display: grid;
  height: 207px;
  border-bottom: var(--border-line);
}

@media (max-width: 1023px) {
  .displayCard figure {
    height: 168px;
  }
}

.displayCard figure img {
  display: block;
  width: 100%;
  align-self: center;
  height: 100%;
  max-height: 206px;
  -o-object-fit: cover;
  object-fit: cover;
}

@media (max-width: 1023px) {
  .displayCard figure img {
    max-height: 167px;
  }
}

.displayCard .textDiv {
  padding-left: 20px;
  padding-top: 20px;
  padding-bottom: 2px;
  background: var(--color-white);
}

@media (max-width: 1023px) {
  .displayCard .textDiv {
    padding: 10px;
  }
}

.displayCard .collegeLogo {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  margin-top: -38px;
  box-shadow: 0 0px 10px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 5px;
}

.displayCard p {
  font-size: 14px;
  line-height: 24px;
}

.displayCard .widgetCardHeading {
  padding-bottom: 0;
  min-height: 24px;
  margin-bottom: 5px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  color: var(--primary-font-color);
}

.displayCard .subText {
  padding-bottom: 20px;
  background-color: white;
  color: #989898;
  font-weight: normal;
  min-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.displayCard .spriteIcon {
  position: initial;
  vertical-align: middle;
  margin-right: 5px;
}

.exploreScholorshipDiv {
  justify-content: center;
}

@media (max-width: 1023px) {
  .exploreScholorshipDiv {
    justify-content: initial;
    padding: 0 16px;
  }
}

.scholorshipCard {
  text-align: center;
  padding: 20px;
  flex-basis: 16.3%;
  margin-right: 30px;
  margin-bottom: 30px;
  border-radius: 10px;
  background: var(--color-white);
  transition: 0.2s ease;
}

@media (max-width: 1023px) {
  .scholorshipCard {
    flex-basis: 48.4%;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 16px;
  }

  .scholorshipCard:nth-of-type(2n) {
    margin-right: 0;
  }
}

.scholorshipCard:hover {
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
  text-decoration: none;
}

.scholorshipCard:hover p {
  color: var(--anchor-textclr);
}

.scholorshipCard p {
  color: var(--primary-font-color);
  font-size: 15px;
  line-height: 24px;
}

.scholorshipCard .spriteIcon {
  width: 44px;
  height: 38px;
  margin-bottom: 16px;
}

.featuredBrandList .featuredCard {
  border-radius: 6px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.24);
  background: var(--color-white);
  padding: 36px 3px;
  align-items: center;
  text-align: center;
  margin: 5px 9px;
  width: 175px;
  max-height: 136px;
}

@media (max-width: 1023px) {
  .featuredBrandList .featuredCard {
    padding: 28px 3px;
  }
}

.student-testimonial {
  max-width: 1055px;
  margin: 0 auto;
}

.student-testimonial .customSliderCards {
  padding: 10px;
}

.student-testimonial .scrollRight {
  right: -20px;
}

.student-testimonial .scrollLeft {
  left: -20px;
}

.studentReviewCard {
  padding: 20px;
  text-align: center;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  white-space: normal;
  width: 31.4%;
  display: inline-block;
  margin-right: 27px;
  vertical-align: middle;
}

@media (max-width: 1023px) {
  .studentReviewCard {
    width: 253px;
    margin-right: 5px;
    padding: 20px 10px;
  }
}

.studentReviewCard:last-child {
  margin-right: 0;
}

.studentReviewCard img {
  width: 45px;
  height: 45px;
  margin-bottom: 20px;
  border-radius: 50%;
}

@media (max-width: 1023px) {
  .studentReviewCard img {
    margin-bottom: 16px;
  }
}

.studentReviewCard p,
.studentReviewCard a {
  line-height: 24px;
  color: var(--primary-font-color);
  text-decoration: none;
}

.studentReviewCard .studentName {
  font-size: 18px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  min-height: 24px;
  font-weight: var(--font-semibold);
  margin-bottom: 5px;
}

.studentReviewCard .subText {
  color: #787878;
  font-size: 14px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  min-height: 24px;
}

.studentReviewCard .studentReview {
  font-size: 15px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 6;
  min-height: 144px;
  margin-top: 20px;
  margin-bottom: 18px;
  position: relative;
  z-index: 1;
}

@media (max-width: 1023px) {
  .studentReviewCard .studentReview {
    -webkit-line-clamp: 9;
    min-height: 216px;
    margin-top: 6px;
    margin-bottom: 0;
  }
}

.studentReviewCard a.studentName:hover {
  color: var(--anchor-textclr);
}

.studentReviewDiv {
  position: relative;
}

.studentReviewDiv:before {
  content: "";
  position: absolute;
  top: -10px;
  left: 0;
  background: url(../../images/home_new/quote2.png);
  width: 43px;
  height: 35px;
}

.studentReviewDiv:after {
  content: "";
  position: absolute;
  bottom: -15px;
  right: 0;
  background: url(../../images/home_new/quote1.png);
  width: 43px;
  height: 35px;
}

.bannerContent {
  width: 866px;
  margin: 0 auto;
  text-align: center;
  padding: 40px 0;
  position: absolute;
  top: 50%;
  left: 50%;
  min-height: 279px;
  transform: translate(-50%, -50%);
  z-index: 1;
}

@media (max-width: 1023px) {
  .bannerContent {
    width: 100%;
    padding: 16px;
    padding-top: 0;
    margin-top: -60px;
  }
}

.bannerContent .searchSection {
  max-width: 700px;
  margin: 0 auto;
}

.bannerContent .searchSection input {
  border-radius: 0px 0 0 4px;
  border: var(--border-line);
  width: calc(100% - 114px);
  border-right: 0px;
  height: 44px;
}

@media (max-width: 1023px) {
  .bannerContent .searchSection input {
    padding: 6px 16px;
    font-size: 12px;
    line-height: 24px;
    height: auto;
    width: calc(100% - 41px);
  }
}

.bannerContent .searchSection .primaryBtn {
  border-radius: 0px 4px 4px 0px;
  padding: 8px 19px;
}

@media (max-width: 1023px) {
  .bannerContent .searchSection .primaryBtn {
    padding: 6px 8px;
  }
}

.bannerContent .searchSection .searchIcon {
  margin-right: 5px;
  transform: scale(0.8);
  margin-bottom: 4px;
}

@media (max-width: 1023px) {
  .bannerContent .searchSection .searchIcon {
    margin: 0;
  }
}

.bannerContent .searchSection .tab-content.current {
  display: flex;
}

.bannerContent .searchSection .bannerTabButtons {
  justify-content: initial;
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 0;
}

.bannerContent .searchSection .bannerTabButtons .tab-nav-link {
  flex-basis: auto;
  list-style-type: none;
  line-height: 24px;
  padding: 6px 16px;
  border-radius: 3px 3px 0px 0;
  margin-right: 5px;
  background: #0966c2;
  transition: 0.2s ease;
  color: var(--color-white);
  cursor: pointer;
}

@media (max-width: 1023px) {
  .bannerContent .searchSection .bannerTabButtons .tab-nav-link {
    padding: 6px 8px;
    font-weight: var(--font-semibold);
  }
}

.bannerContent .searchSection .bannerTabButtons .tab-nav-link.tabLink {
  background: #3d8ff2;
}

.bannerContent .searchSection .bannerTabButtons .tab-nav-link:hover {
  background: #3d8ff2;
}

.carouselSection {
  margin-top: -40px;
  height: 460px;
}

@media (max-width: 1023px) {
  .carouselSection {
    margin-top: 0;
    height: 431px;
  }
}

.carouselSection img {
  width: 100%;
  max-height: 460px;
  display: block;
}

@media (max-width: 1023px) {
  .carouselSection img {
    height: 431px;
    -o-object-fit: cover;
    object-fit: cover;
  }
}

.carouselSection .slick-dots {
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  z-index: 1;
}

@media (max-width: 1023px) {
  .carouselSection .slick-dots {
    bottom: 16px;
  }
}

.carouselSection .slick-dots li {
  display: inline-block;
  margin: 0 5px;
  line-height: initial;
  font-size: 0px;
}

.carouselSection .slick-dots li button {
  padding: 0;
  background: #ffffff;
  width: 10px;
  height: 10px;
  outline: none;
  font-size: 0px;
  border: none;
  border-radius: 50%;
}

.carouselSection .slick-dots li.slick-active button {
  background: var(--color-red);
}

.carsouselDiv {
  position: relative;
}

.carsouselDiv:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.55);
}

.carsouselDiv .sliderNmae {
  position: absolute;
  right: 10%;
  bottom: 20px;
  color: var(--color-white);
  font-weight: var(--font-semibold);
  font-size: 15px;
  line-height: 24px;
  z-index: 2;
}

@media (max-width: 1023px) {
  .carsouselDiv .sliderNmae {
    bottom: 40px;
    padding: 0 16px;
    text-align: center;
    font-weight: normal;
    left: 50%;
    transform: translate(-50%, 0);
    width: 100%;
  }
}

.indexPage .headerMegaMenu {
  background: rgba(255, 255, 255, 0.06);
  box-shadow: none;
}

.indexPage .mainMenuText ul li a {
  color: var(--color-white);
  font-weight: 500;
}

.indexPage .menu-li-tabs .caret {
  background-position: 708px -71px;
}

.indexPage ul.dropdown-menu li a {
  color: var(--primary-font-color);
  font-weight: normal;
}

.aboutUsSection {
  background: url(../../images/home_new/aboutus-banner.webp) no-repeat center;
  background-size: 100% 100%;
  position: relative;
  padding-top: 130px;
}

@media (max-width: 1023px) {
  .aboutUsSection {
    padding: 40px 0;
    background-position: center;
    background-size: cover;
  }
}

.aboutUsSection .websiteImg {
  position: absolute;
  width: 660px;
  height: 410px;
}

.aboutUsSection .row {
  position: relative;
  z-index: 1;
}

.aboutUsSection .aboutUsCard {
  flex-basis: 31%;
  margin: 0 20px 20px 0;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 30px;
  text-align: center;
}

@media (max-width: 1023px) {
  .aboutUsSection .aboutUsCard {
    padding: 20px;
    flex-basis: 46.8%;
  }

  .aboutUsSection .aboutUsCard:nth-of-type(2n) {
    margin-right: 0px;
  }
}

.aboutUsSection .aboutUsCard:nth-of-type(3n) {
  margin-right: 0;
}

@media (max-width: 1023px) {
  .aboutUsSection .aboutUsCard:nth-of-type(3n) {
    margin-right: auto;
  }
}

.aboutUsSection .aboutUsCard .spriteIcon {
  width: 39px;
  height: 39px;
  margin-bottom: 10px;
}

.aboutUsSection .aboutUsCard .cardHeading {
  font-size: 16px;
  line-height: 24px;
  color: #787878;
  margin-bottom: 6px;
}

@media (max-width: 1023px) {
  .aboutUsSection .aboutUsCard .cardHeading {
    font-size: 15px;
  }
}

.aboutUsSection .aboutUsCard .totalCount {
  font-size: 24px;
  line-height: 24px;
  font-weight: var(--font-500);
}

@media (max-width: 1023px) {
  .aboutUsSection .aboutUsCard .totalCount {
    font-size: 18px;
  }
}

.studyAbroad .studyAbroadCard {
  flex-basis: 29.9%;
  margin: 0 30px 30px 0;
  text-align: center;
  padding: 20px;
  transition: 0.2s ease;
  cursor: pointer;
  text-decoration: none;
}

@media (max-width: 1023px) {
  .studyAbroad .studyAbroadCard {
    flex-basis: 48%;
    margin: 0 10px 10px 0;
  }

  .studyAbroad .studyAbroadCard:nth-of-type(2n) {
    margin-right: 0px;
  }
}

.studyAbroad .studyAbroadCard:hover {
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
}

.studyAbroad .studyAbroadCard:hover p {
  color: var(--anchor-textclr);
}

.studyAbroad .studyAbroadCard:nth-of-type(3n) {
  margin-right: 0px;
}

@media (max-width: 1023px) {
  .studyAbroad .studyAbroadCard:nth-of-type(3n) {
    margin-right: auto;
  }
}

.studyAbroad .studyAbroadCard p {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
}

.studyAbroad .studyAbroadCard img {
  width: 75px;
  height: 75px;
  display: inline-block;
  margin-bottom: 15px;
}

.studyAbroad .studyAbroadCard p {
  font-size: 16px;
  line-height: 24px;
}

.h1_tooltip {
  position: relative;
  display: inline-block;
}

.h1_tooltip .tooltiptext {
  font-size: 14px;
  visibility: hidden;
  width: 320px;
  background-color: #000;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 7px 9px;
  position: absolute;
  z-index: 1032;
  top: 100%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
  /* font-weight: 500; */
}

.h1_tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

.student-testimonial-section {
  padding-top: 80px;
}

.featuredCard img {
  height: 64px;
  width: auto;
  margin: 0 auto;
}

.position-initial {
  position: initial;

}

.trending {
  height: 265px;
  display: none;
  padding: 0px !important;
  position: absolute;
  width: 586px;
  overflow: auto;
  z-index: 9999;
}

.trending a {
  display: block;
  background: white;
  color: #333333;
  text-align: left;
  padding: 6px 0px 6px 6px;
  font-size: 14px;
}

@media (max-width: 1023px) {
  .trending {
    width: calc(100% - 36px);
    height: 189px;
  }

  .xs-d-block {
    display: block;
  }

  .p-lg-075 {
    padding: 0;
  }

  .scroll {
    white-space: nowrap;
    overflow: auto;
  }

  .scrollRight,
  .scrollLeft {
    display: none !important;
  }

  .collegesWithCategoryData .dataCard .dataCardText {
    flex-basis: calc(100% - 58px);
    text-align: left;
  }

  .aboutUsSection h2,
  .aboutUsSection .sectionSubheading {
    text-align: center;
    color: var(--color-white);
    line-height: 24px;
    padding-bottom: 15px;
  }

  .aboutUsSection .sectionSubheading {
    font-size: 14px;
  }

  .aboutUsSection h2 {
    font-size: 18px;
  }

  .blueBgDiv.mobileOnly,
  .mobileOnly .searchIcon {
    display: none !important;
  }

  .student-testimonial-section {
    padding-top: 40px;
  }

  .pageFooter {
    padding-bottom: 0;
  }
}