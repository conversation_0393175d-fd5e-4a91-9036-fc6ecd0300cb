@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

h1,
h2,
h3,
h4,
h5 {
    padding: 0;
    margin: 0;
    font-weight: 600;
    line-height: normal;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

body,
p {
    font-family: "Roboto", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    font-variation-settings: "wdth"100;
    color: #000000;
    line-height: 1.5;
}

h1 {
    font-size: 50px;
    font-weight: 700;
}

h2 {
    font-size: 36px;
    font-weight: 600;
}

.button-style {
    background: #EE424F;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.section-space {
    margin-top: 70px;
}

/* header */
.container {
    max-width: 1280px;
    width: 100%;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
}

header {
    text-align: center;
    padding: 20px 15px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* banner */
.hero-banner {
    height: auto;
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: url(/../yas/images/clp/generic_based_three/banner-img.png);
    padding: 40px 0;
    background-repeat: no-repeat;
    background-size: cover;
}

.hero-banner img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.hero-banner .banner-overlay {
    background: linear-gradient(90deg, rgba(13, 63, 100, 0.9) 11%, rgba(26, 127, 202, 0.4) 71%);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.hero-banner .banner-right-content {
    max-width: 368px;
    width: 100%;
    background-color: white;
    padding: 30px 24px;
    border-radius: 4px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.hero-banner .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hero-banner .banner-left-content {
    max-width: 490px;
    width: 100%;
    color: white;
}

.hero-banner .banner-left-content h1 {
    font-size: 50px;
    line-height: 56px;
    font-weight: 700;
}

.hero-banner .banner-left-content h1>span {
    color: #EE424F;
}

.hero-banner .banner-left-content .top-college {
    margin-top: 10px;
}

.hero-banner .banner-left-content .top-college>span {
    background: url(/../yas/images/clp/generic_based_three/top-college-bg.png) top left no-repeat;
    width: 417px;
    height: 56px;
    color: #0D3F64;
    font-weight: 700;
    font-size: 28px;
    line-height: 50px;
    display: inline-block;
    padding: 0 10px;
    position: relative;
    background-size: cover;
}

.hero-banner .banner-left-content .jobready-block {
    margin-top: 20px;
}

.hero-banner .banner-left-content .jobready-block .jobready-head {
    font-size: 20px;
    line-height: 30px;
    font-weight: 600;
}

.hero-banner .banner-left-content .jobready-block>ul {
    list-style: none;
    padding: 0;
    margin: 8px 0 0;
}

.hero-banner .banner-left-content .jobready-block>ul>li {
    position: relative;
    padding-left: 40px;
    margin-bottom: 10px;
    font-weight: 400;
    font-size: 16px;
}

.hero-banner .banner-left-content .jobready-block>ul>li span {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    background-color: #F5A623;
    border-radius: 2px;
}

.hero-banner .banner-left-content .jobready-block>ul>li span::after {
    content: '';
    position: absolute;
    left: 3px;
    top: 5px;
    width: 10px;
    height: 4px;
    border-left: 2px solid white;
    border-bottom: 2px solid white;
    transform: rotate(-45deg);
}

.hero-banner .banner-right-content {
    max-width: 368px;
    width: 100%;
    background-color: white;
    padding: 30px 24px;
    border-radius: 4px;
}

.hero-banner .banner-right-content .form-head {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
}

.hero-banner .form-body-block {
    margin-top: 20px;
}

.hero-banner .form-body-block .banner-form-inner {
    position: relative;
    margin-bottom: 16px;
}

.hero-banner .form-body-block .banner-form-inner>span {
    position: absolute;
    display: block;
    width: 100%;
    bottom: -17px;
    left: 0;
    font-size: 11px;
    color: #F8382A;
}

.hero-banner .form-body-block .banner-form-inner span.select2 {
    position: static;
}

.hero-banner .form-body-block input {
    border: 1px solid #ADB5BD;
    border-radius: 8px;
    width: 100%;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    padding: 13px 24px;
}

.hero-banner .form-body-block input:focus {
    border-color: #F5A623;
}

.hero-banner .form-body-block input:focus-visible {
    outline: none;
}

.hero-banner .submit-btn {
    background-color: #EE424F;
    border-radius: 8px;
    width: 100%;
    margin-top: 10px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    border: 0;
    padding: 10px;
    color: white;
    cursor: pointer;
}

.input-error {
    border-color: #F8382A !important;
}

/* Why GetMyUni? */
.why-getmyuni {
    position: relative;
}

.why-getmyuni h2 {
    text-align: center;
}

.why-getmyuni-inner {
    display: flex;
    justify-content: space-between;
    column-gap: 40px;
    margin-top: 40px;
}

.why-getmyuni-inner .detail-block {
    max-width: 33.33%;
    width: 100%;
    display: flex !important;
    align-items: center;
    justify-content: flex-start;
    column-gap: 15px;
    border: 1px solid #D8D8D8;
    border-radius: 8px;
    padding: 15px;
    position: relative;
    background: radial-gradient(at left bottom, rgba(14, 64, 101, 0.1), rgba(255, 255, 255, 1.0));
    margin:  0 10px;
  
}

.why-getmyuni-inner .detail-block::before {
    content: '';
    position: absolute;
    width: 102px;
    height: 5px;
    background-color: #0D3F64;
    inset: 0;
    margin: -3px auto;
    border-radius: 50px;
}

.why-getmyuni-inner .detail-block .img-block {
    margin-bottom: 15px;
}

.why-getmyuni-inner .detail-block .text-block {
    font-size: 24px;
    line-height: 30px;
    font-weight: 500;
}

.why-getmyuni .explore-now-btn {
    text-align: center;
    margin-top: 50px;
}

/* Programs Offered */
.programs-offered {
    /*background:url(/../yas/images/clp/generic_based_three/bg-programs.jpg) no-repeat center;*/
    padding-top: 70px;
    padding-bottom: 70px;
    background-color: #F5F5F5;
}

.programs-offered h2 {
    text-align: center;
}

.programs-offered h2>span {
    line-height: 24px;
    font-size: 18px;
    font-weight: 400;
    display: block;
    margin-top: 10px;
}

.programs-offered-slider {
    margin-top: 30px;
    display: none;
}

.programs-offered-slider.slick-slider {
    margin-bottom: 0;
}

.programs-offered-slider.slick-slider .slick-list.draggable {
    padding-bottom: 70px;
}

.programs-offered-slider.slick-slider .slick-dots {
    bottom: 20px;
}
.why-getmyuni-inner.slick-slider .slick-dots{
    bottom: -30px;
}
.programs-offered-slider.slick-slider .slick-dots li, .why-getmyuni-inner.slick-slider .slick-dots li{
    width: 10px;
    height: 10px;
    list-style: none;
}

.programs-offered-slider.slick-slider .slick-dots li>button, .why-getmyuni-inner.slick-slider .slick-dots li>button  {
    width: 10px;
    height: 10px;
    background-color: #999fa3;
    border-radius: 100%;
}

.programs-offered-slider.slick-slider .slick-dots li.slick-active>button, 
.why-getmyuni-inner.slick-slider .slick-dots li.slick-active>button  {
    background-color: #0c65a6;
}

.programs-offered-slider.slick-slider .slick-dots li>button::before, .why-getmyuni-inner.slick-slider .slick-dots li>button::before {
    display: none;
}

.programs-card {
    width: 100%;
    position: relative;
    margin: 0 10px;
    border-radius: 12px;
    border: 2px solid transparent;

}

.slick-slider .slick-list,
.slick-slider .slick-track {
    transition: 0.5s;
}


.fit-content-height {
    border: 2px solid transparent;
    border-radius: 12px;
    display: flex;
    height: 100%;
    flex-wrap: wrap;
    box-shadow: -4px 14px 5px -10px rgba(0, 0, 0, 0.59);
    -webkit-box-shadow: -4px 14px 5px -10px rgba(0, 0, 0, 0.59);
    -moz-box-shadow: -4px 14px 5px -10px rgba(0, 0, 0, 0.59);
    position: relative;
    overflow: hidden;
}

.fit-content-height:hover .program-card-text {
    background-color: #EE424F;
    color: #fff;
}

.program-img img {
    box-shadow: inset 0 14px 20px -20px rgba(0, 0, 0, 0.8);
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    width: 100%;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
    display: flex;
}

.programs-card .program-img {
    width: 100%;
    height: auto;
    overflow: hidden;
    display: flex;
    order: 2;
}

.programs-card .program-card-text {
    font-weight: 500;
    font-size: 20px;
    line-height: 30px;
    /* text-align: left; */
    text-align: center;
    padding: 5px 10px;
    color: #212529;
    position: relative;
    width: 100%;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    border: 2px solid transparent;
    /* display: flex; */
    order: 1;
    border: 1px solid #0D3F64;
    height: 100px;
}

.programs-offered .fee-detail-button {
    text-align: center;
    margin-top: 10px;
}

/* our partner */
.our-partner-section h2 {
    text-align: center;
}

.our-partner-slider {
    margin-top: 30px;
    display: none;
}

.our-partner-slider.slick-slider .slick-track {
    display: flex;
    align-items: center;
}

.our-partner-slider .college-logo-block {
    margin: 0 20px;
}

.our-partner-slider .college-logo-block img {
    width: 100%;
    height: 100px;
    object-fit: scale-down;
}

.our-partner-section .talk-expert-button {
    text-align: center;
    margin-top: 20px;
}

/* Submit Your Application */
.submit-application-section {
    background: radial-gradient(at left bottom, #0D3F64, #4475BA);
    padding: 50px 0;
}

.submit-application-inner {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.submit-application-inner .submit-app-left {
    width: 100%;
    color: white;
    font-size: 28px;
    font-weight: 600;
    line-height: 40px;
    text-align: center;
}

.submit-application-inner .submit-app-left>span {
    color: white;
    display: block;
}

.submit-application-inner .submit-app-btn {
    width: 100%;
    text-align: center;
    margin-top: 30px;
}

.submit-application-inner .submit-app-btn>button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

/*--herocky button--*/
.herock-button {
    padding: 15px 0;
}

.herock-button p {
    display: flex;
    align-items: center;
    juherofy-content: center;
    gap: 10px;
}

.button-blue {
    background-color: #0D3F64;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

/* footer */
footer {
    background-color: #212529;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    line-height: 54px;
}

/* for modal */
.overflow-hide {
    overflow: hidden;
}

.modal-overlay-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1;
    display: none;
    align-items: center;
    overflow-y: auto;
}

.modal-body {
    padding: 0;
    margin: auto;
    height: auto;
    max-width: 370px;
    width: 100%;
    background: white;
    border-radius: 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, .4);
    position: relative;
    z-index: 300;
    overflow-x: auto;
    padding: 25px;
    border-radius: 5px;
}

.modal-body .modal-head {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    color: #1E1E1E;
}

.modal-body .modal-head>span {
    font-size: 16px;
    font-weight: 400;
    display: block;
}

.modal-body .form-content {
    margin-top: 20px;
}

.modal-body .form-content .form-field-block {
    position: relative;
}

.modal-body .form-content .form-field-block .form-error {
    position: absolute;
    display: block;
    width: 100%;
    bottom: 0;
    left: 0;
    font-size: 11px;
    color: #F8382A;
}

.modal-body .form-content .field-style {
    border: 1px solid #ADB5BD;
    border-radius: 8px;
    width: 100%;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    padding: 12px 24px;
}

.modal-body .form-content .field-style:focus {
    border-color: #F5A623;
}

.modal-body .form-content .field-style:focus-visible {
    outline: none;
}

.modal-body .form-content .field-style.select-arrow {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url(/../yas/images/clp/generic_based_three/select-arrow.png) no-repeat right 10px top 21px;
}

.modal-button {
    font-size: 18px;
    font-weight: 600;
    background-color: #EE424F;
    width: 100%;
    border-radius: 8px;
    padding: 10px 0;
    border: none;
    cursor: pointer;
    color: white;
    margin-top: 10px;
}

#modal-close {
    font-size: 28px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    line-height: 10px;
    font-weight: 300;
}

.modal-overlay-box.show-modal {
    display: flex;
}

.page-header,
.pageFooter,
.scrollToTop {
    display: none !important;
}

.select2-container {
    margin-bottom: 16px !important;
}

.select2-container .select2-selection--single {
    height: 48px !important
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding: 8px 20px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 12px !important;
    right: 12px !important;
}

#modalFormClp .banner-form-inner .select2-container {
    width: 100% !important;
}

.select2-container--default .select2-selection--single {
    border-radius: 8px !important;
}

.custom-select-box {
    position: relative;
}

.clpCourse {
    background-color: #fff;
    ;
    position: relative;
    appearance: none;
}

.custom-select-box::after {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #888;
    right: 14px;
    top: 20px;
}

#genericScreenSubmit:disabled,
#modalScreenSubmit:disabled {
    background-color: rgba(255, 78, 83, 0.4) !important;
}

.banner-form-inner .select2 {
    margin-bottom: 15px;
    width: 100% !important;
}

.spriteIcon {
    display: inline-block !important;
    background-image: url(../../images/master_sprite.webp);
    text-align: left;
    overflow: hidden;
}

.whiteDownloadIcon,
.redDownloadIcon {
    width: 19px;
    height: 18px;
    background-position: 233px -354px !important;
    vertical-align: text-bottom;
    margin-right: 4px;
}

.phoneIcon {
    width: 24px;
    height: 24px;
    background-position: 536px -246px;
    vertical-align: bottom;
    margin-right: 2px;
}

#sendCallerLeadToCld {
    display: none !important;
}

/*--sticky button--*/
.stick-button {
    padding: 15px 0;
}

.stick-button p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.stick-button .button-style {
    background-color: #EE424F
}

.button-blue {
    background-color: #0D3F64;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    font-size: 14px !important;
}

.form-subheading-generic {
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    display: block;
}

/* media start here */
@media only screen and (max-width:1200px) {
    .why-getmyuni-inner .detail-block .text-block {
        font-size: 17px;
        line-height: 30px;
    }
    .why-getmyuni-inner .detail-block .img-block img {
        width: 68px;
    }
    .programs-card .program-card-text {
        font-size: 18px;
        line-height: 24px;
    }
}

@media only screen and (max-width:992px) {

    /*--sticky button--*/
    .stick-button span {
        display: none;
    }

    .stick-button {
        padding: 0;
    }

    .stick-button p {
        gap: 0;
    }

    .stick-button .button-style,
    .stick-button .button-blue {
        border-radius: 0;
        height: 45px;
        font-size: 14px;
    }

    .stick-button .button-style {
        width: 60%;
    }

    .stick-button .button-blue {
        width: calc(100% - 60%);
    }

    .hero-banner .container {
        column-gap: 40px;
    }

    .hero-banner .banner-left-content {
        max-width: 100%;
        width: calc(100% - 360px);
    }

    .hero-banner .banner-left-content h1 {
        font-size: 40px;
    }

    .hero-banner .banner-left-content .top-college>span {
        font-size: 24px;
        width: 100%;
        background-size: contain;
    }

    .hero-banner .banner-right-content {
        max-width: 100%;
        width: 333px;
    }

    .submit-application-inner {
        gap: 10px;
    }

    .submit-application-inner .submit-app-left {
        width: 90%;
        text-align: center;
    }

    .submit-application-inner .submit-app-left>br {
        display: block;
    }
}

@media only screen and (max-width:767px) {

    .button-style,
    .button-blue {
        padding: 10px;
        font-size: 16px;
    }

    .stick-button p span {
        display: none;
    }

    h2 {
        font-size: 30px;
    }

    .section-space {
        margin-top: 40px;
    }

    .why-getmyuni .explore-now-btn {
        margin-bottom: 40px;
    }

    .hero-banner {
        align-items: flex-start;
        height: auto;
        padding: 10px 0;
    }

    .hero-banner .container {
        flex-direction: column;
    }

    .hero-banner .banner-left-content {
        width: 100%;
        padding: 40px 10px 0;
        margin-bottom: 30px;
    }

    .hero-banner .banner-left-content h1 {
        text-align: center;
        font-size: 36px;
        line-height: 40px;
    }

    .hero-banner .banner-left-content .top-college {
        width: 325px;
        margin: 5px auto 0;
    }

    .hero-banner .banner-left-content .top-college>span {
        font-size: 20px;
        height: 45px;
        line-height: 40px;
        text-align: center;
    }

    .hero-banner .banner-left-content .jobready-block {
        width: 85%;
        margin: 20px auto;
    }

    .hero-banner .banner-left-content .jobready-block .jobready-head {
        font-size: 20px;
    }

    .hero-banner .banner-left-content .jobready-block>ul>li {
        line-height: 22px;
        margin-bottom: 7px;
    }

    .hero-banner .banner-right-content {
        width: 100%;
        margin-bottom: 30px;
    }

    .why-getmyuni-inner {
        flex-direction: column;
        row-gap: 40px;
    }

    .why-getmyuni-inner .detail-block {
        max-width: 100%;
        display: inline-block;
    }

    .why-getmyuni-inner .detail-block .img-block {
        margin-bottom: 10px;
    }

    .programs-offered.section-space {
        margin-top: 0;
        padding-top: 40px;
        padding-bottom: 40px;
    }

    .programs-offered-slider .slick-list {
        padding: 0 10% 0 0;
    }

    .programs-card .program-img {
        order: 1;
    }

    .programs-card .program-card-text {
        order: 2;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        border-bottom-left-radius: 12px;
        border-bottom-right-radius: 12px;
        padding: 10px;
        height: 80px;
    }

    .program-img img {
        width: 100%;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .fit-content-height {
        box-shadow: none;
    }

    .submit-application-section {
        padding: 40px 0;
    }

    .submit-application-inner .submit-app-left {
        font-size: 24px;
    }
}