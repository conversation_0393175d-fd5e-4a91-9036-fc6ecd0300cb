.fancybox-slide--video .fancybox-content {
  max-width: 750px;
  max-height: 400px;
  margin: 0 auto;
}

.fancybox-button {
  background: none;
}

.fancybox-toolbar,
.fancybox-infobar,
.fancybox-navigation,
.fancybox-button--arrow_left,
.fancybox-button--arrow_right {
  background: #ccc;
}

* {
  color: #282828;
}

.breadcrumb {
  margin-bottom: 20px !important;
}

.reviewPageForm {
  max-width: 794px;
  margin: 40px auto;
  color: #282828;
}

.page-header {
  height: auto;
}

.reviewFormDiv p {
  padding-bottom: 20px;
  font-size: 16px;
  line-height: 24px;
}

.select2-selection.select2-selection--single {
  padding: 0;
  border: none;
  line-height: 24px;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding: 0;
  line-height: 24px;
}

span.select2-selection__arrow {
  display: none;
}

.select2-search--dropdown .select2-search__field {
  padding: 7px 12px;
  font-size: 13px;
  line-height: 24px;
}

.select2-container .select2-selection--single {
  background-image: url(https://www.getmyuni.com/yas/images/select-angle.png) !important;
  background-repeat: no-repeat !important;
  background-position: 100% 9px !important;
  height: 24px;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #989898 !important;
  font-size: 14px;
}

select:required:invalid {
  /* color: #989898; */
  font-size: 14px;
}

option {
  color: var(--primary-font-color);
}

select,
.select2-container {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-sizing: border-box;
  width: 100%;
  background-image: url("https://www.getmyuni.com/yas/images/select-angle.png") !important;
  background-repeat: no-repeat !important;
  background-position: 95% 19px !important;
  color: var(--primary-font-color);
}

select {
  width: 100%;
  cursor: pointer;
}

input[type=radio] {
  accent-color: #ff4e53;
  width: 18px;
  height: 18px;
  vertical-align: middle;
  margin: 0;
  margin-right: 5px;
}

input[type=radio]+label {
  margin-right: 10px;
  margin-bottom: 12px;
  color: #989898;
  font-size: 14px;
  line-height: 24px;
  cursor: pointer;
  display: inline-block;
}

.form-group {
  margin-bottom: 20px;
}

.form-group p {
  font-size: 16px;
  line-height: 24px;
  padding-bottom: 12px;
  color: #282828;
}

.formField {
  position: relative;
  max-width: 350px;
}

.formField input,
.formField select,
.formField .select2-container {
  padding: 11px 16px;
  padding-left: 41px;
  border-radius: 4px;
  border: var(--border-line);
  outline: none;
  width: 100%;
  font-size: 14px;
  line-height: 24px;
  background: var(--color-white);
  color: var(--primary-font-color);
}

.leadFormDiv .select2-container--default .select2-selection--single .select2-selection__rendered {
  border: none !important;
}

.leadFormDiv .select2-container {
  padding: 0px !important;
}

.leadFormDiv .mobileNumber.row,
.writeAnswerDiv .mobileNumber.row {
  max-width: 350px;
  margin-left: 0px;
}

.leadFormDiv .locationIcon {
  top: 15px;
}

.formField input::-moz-placeholder,
.formField select::-moz-placeholder,
.formField .select2-container::-moz-placeholder {
  color: #989898;
}

.formField input:-ms-input-placeholder,
.formField select:-ms-input-placeholder,
.formField .select2-container:-ms-input-placeholder {
  color: #989898;
}

.formField input::placeholder,
.formField select::placeholder,
.formField .select2-container::placeholder {
  color: #989898;
}

.formField.autoPadding select,
.formField.autoPadding input {
  padding-left: 16px;
}

.stepsBarDiv {
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: #fff;
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
}

.progressBar {
  width: 100%;
  background: #d8d8d8;
  border-radius: 4px;
  height: 10px;
  margin-top: 6px;
  position: relative;
  overflow: hidden;
}

.progressBarLine {
  background: #3AB54A;
  width: 0%;
  height: 10px;
  display: block;
}

.reviewFormDiv {
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: #fff;
  margin-bottom: 20px;
  padding: 40px;
}

.reviewFormDiv textarea {
  padding: 10px;
  border-radius: 4px;
  border: var(--border-line);
  width: none;
  width: 100%;
  resize: none;
}

.reviewFormDiv .charLimit {
  font-size: 14px;
  line-height: 24px;
  color: #989898;
  display: inline-block;
  margin-top: 6px;
}

.reviewFormDiv .form-heading {
  font-size: 24px;
  line-height: 24px;
  padding-bottom: 20px;
  font-weight: 500;
}

.reviewFormDiv .row {
  margin: 0 -10px;
}

.reviewFormDiv .col-md-6,
.reviewFormDiv .col-md-12 {
  padding: 0 10px;
}

.dialCodeDiv {
  flex-basis: 100px;
  position: relative;
  border-radius: 4px 0 0 4px;
  border: var(--border-line);
  border-right: 0px;
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.numberInput {
  flex-basis: calc(100% - 101px);
}

.numberInput input {
  padding-left: 12px;
  width: 100%;
  border-radius: 0 4px 4px 0;
  width: 100%;
  height: 48px;
}

.referral input {
  border-radius: 4px 0 4px;
  flex-basis: calc(100% - 106px);
}

.referral .primaryBtn {
  border-radius: 0 4px 4px 0;
  flex-basis: 106px;
}

.instructionDiv {
  background: #fff5d9;
  padding: 20px;
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 20px;
  border: var(--border-line);
  border-radius: 4px;
}

.instructionDiv .bulblightIcon {
  flex-basis: 33px;
  margin-right: 16px;
  width: 33px;
  height: 38px;
  background-position: -35px -460px;
}

.instructionDiv .instructionText {
  flex-basis: calc(100% - 40px - 16px);
}

.instructionDiv ul {
  margin: 0;
  padding: 0px 16px;
}

.instructionDiv p {
  padding-bottom: 10px;
}

.instructionDiv p,
.instructionDiv li {
  font-size: 14px;
  line-height: 24px;
}

/* .ratingSection ul {
  padding: 0;
  margin: 0;
  margin-left: -5px;
  margin-right: -5px;
}

.ratingSection ul li {
  display: inline-block;
  margin: 0 7px;
  font-size: 14px;
  line-height: 24px;
  text-align: center;
  position: relative;
}

.ratingSection ul li span {
  width: 32px;
  height: 32px;
  vertical-align: middle;
  display: inline-block;
  color: #989898;
  border-radius: 50%;
  background: #d8d8d8;
  border: 2px solid #989898;
  line-height: 29px;
  cursor: pointer;
}

.ratingSection button {
  padding: 5px;
  font-size: 14px;
  vertical-align: middle;
  font-weight: 500;
  border-radius: 4px;
  color: #ff4e53;
  border: none;
  outline: none;
}

.ratingSection .veryPoor,
.ratingSection .exellent {
  background: #fff;
}

.ratingSection .exellent {
  color: #5cb85c;
} */

.poor:before,
.average:before,
.good:before {
  position: absolute;
  bottom: -30px;
  line-height: 24px;
  left: 0;
}

.poor:before {
  content: "Poor";
  color: #ff4e53;
}

.average:before {
  content: "Average";
  color: #ffc000;
  left: -7px;
}

.good:before {
  content: "Good";
  color: #5cb85c;
}

.heading {
  text-align: left;
  font-size: 20px;
  line-height: 24px;
  font-weight: 600;
  padding-bottom: 30px;
}

/* .ratingSection {
  margin-bottom: 60px;
} */

button.primaryBtn {
  border: 1px solid #ff4e53;
}

button.previousBtn {
  background: #fff;
  color: #ff4e53;
  margin-right: 10px;
}

.myReview {
  padding: 20px 0;
  border-top: var(--border-line);
}

.myReview .formField {
  max-width: 100%;
}

.uploadImage .uploadBlock {
  flex-basis: 47.3%;
  margin-right: 38px;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
  border: 1px dashed #d8d8d8;
}

.uploadImage .uploadBlock:nth-of-type(2n) {
  margin-right: 0px;
}

.uploadImage .uploadBlock p {
  font-size: 14px;
  line-height: 24px;
}

.uploadImage button.primaryBtn {
  background: transparent;
  color: var(--color-red);
  padding: 8px 10px;
  line-height: 24px;
  min-width: 104px;
  margin-right: 10px;
}

.uploadImage .dragAndDroptext {
  margin-top: 10px;
  margin-bottom: 6px;
}

.blueUser,
.addImg {
  width: 44px;
  height: 42px;
  margin-bottom: 16px;
}

.blueUser {
  background-position: -87px -459px;
}

.addImg {
  background-position: -141px -455px;
}

.uploadCaution {
  padding: 10px;
  border: var(--border-line);
  border-radius: 4px;
  margin-bottom: 20px !important;
}

.uploadCaution .text {
  flex-basis: calc(100% - 40px);
}

.uploadCaution .text p {
  font-size: 14px;
  line-height: 24px;
  color: #989898;
}

.uploadCaution .text p:first-child {
  color: #282828;
  font-weight: 500;
}

.imgPlaceholderRed {
  flex-basis: 30px;
  height: 27px;
  max-width: 30px;
  margin-right: 10px;
  background-position: -112px -422px;
}

.instructions {
  padding: 10px;
  border-radius: 4px;
  border: var(--border-line);
  margin-bottom: 20px;
}

.instructions ul {
  margin: 0;
  padding-left: 20px;
}

.instructions ul li {
  font-size: 14px;
  line-height: 24px;
}

.reviewFormDiv .submitReviewBtn,
.reviewFormDiv button.submitReviewBtn {
  background: var(--color-red);
  color: var(--color-white);
}

.reviewFormDiv .agreeterms {
  font-size: 14px;
  padding-top: 16px;
  border-top: var(--border-line);
  font-weight: 500;
}

.reviewFormDiv .agreeterms input {
  accent-color: var(--color-red);
  width: 16px;
  height: 16px;
  margin: 0;
  margin-right: 16px;
  vertical-align: middle;
  border-radius: 2px;
}

.thankyouDiv {
  padding: 30px;
  border-radius: 4px;
  background: #fff5d9;
  border: 1px dotted #d8d8d8;
  text-align: center;
  margin-bottom: 20px;
}

.thankyouSection .thankyouDiv p.p-0 {
  font-size: 14px;
}

.thankyouDiv .thankyouIcon {
  width: 50px;
  height: 50px;
  margin-bottom: 10px;
  background-position: -334px -447px;
}

.thankyouDiv p {
  font-size: 14px;
  padding-bottom: 10px;
}

.thankyouDiv .thankyouText {
  font-weight: 500;
  font-size: 18px;
  padding-bottom: 6px;
}

.thankyouSection .hint {
  font-size: 12px;
  padding: 10px;
  border-radius: 4px;
  border: var(--border-line);
  margin-bottom: 20px;
}

.thankyouSection .mailIcon {
  position: initial;
  margin-right: 12px;
  height: 24px;
  vertical-align: middle;
}

.thankyouSection .refertext {
  color: #0966c2;
}

.referDiv {
  background: #f5f5f5;
  border: var(--border-line);
  padding: 20px;
  border-radius: 4px;
}

.referDiv p {
  padding-bottom: 10px;
  font-size: 14px;
}

.referDiv ul {
  margin: 0;
  padding: 0;
}

.referDiv ul li {
  display: inline-block;
  vertical-align: middle;
}

.referDiv ul .spriteIcon {
  margin: 0 5px;
  width: 28px;
  height: 27px;
}

.referDiv ul .spriteIcon.shareIcon {
  background-position: -153px -425px;
  width: 20px;
  height: 21px;
}

.fbshareIcon {
  background-position: -203px -467px;
}

.twitterShareIcon {
  background-position: -237px -467px;
}

.linkedInShareIcon {
  background-position: -270px -467px;
}

.copyreferalLink {
  margin-top: 10px !important;
}

.copyreferalLink input {
  padding: 13px 16px;
  font-size: 14px;
  line-height: 24px;
  color: #030303;
  border: var(--border-line);
  border-radius: 4px 0 0 4px;
  border-right: none;
  flex-basis: calc(100% - 62px);
  background: #fff;
}

.copyreferalLink button {
  border-radius: 0 4px 4px 0;
  background: #d8d8d8;
  border: var(--border-line);
  border-left: none;
  padding: 14px 13px;
  font-size: 14px;
  line-height: 24px;
}

.popupOtpSection {
  background: rgba(51, 51, 51, 0.6);
  left: 0;
  top: 0;
  position: fixed;
  width: 100%;
  height: 100vh;
  z-index: 12;
}

.popupOtpSection .otpDiv {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 13;
  background: #fff;
  padding: 40px;
  border-radius: 4px;
  width: 794px;
}

.optSectionHeader {
  display: flex;
  margin-bottom: 40px;
}

.optSectionHeader img {
  margin-right: 20px;
  display: inline-block;
}

.optSectionHeader .headingText {
  font-size: 24px;
  line-height: 24px;
  padding-bottom: 10px;
  font-weight: 500;
}

.optSectionHeader .sentOtp {
  padding: 0;
  color: #787878;
  font-size: 16px;
  line-height: 28px;
}

.ReviewOtpInputs {
  margin-bottom: 20px;
}

.ReviewOtpInputs input {
  max-width: 64px;
  margin-right: 23px;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  padding: 11px;
  color: #989898;
  border-radius: 4px;
  border: var(--border-line);
  outline: none;
}

.reviewClosePopupForm {
  position: absolute;
  right: 40px;
  top: 40px;
  width: 17px;
  height: 17px;
  background-position: 653px -334px;
  cursor: pointer;
}

.referral .form-group {
  margin: 0;
}

.referral .help-block {
  padding: 0;
}

/* review landing page */
.reviewsheroSection {
  border-radius: 4px;
  /* box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12); */
  box-shadow: none;
  /* background-color: #fff; */
  margin-bottom: 20px;
  background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
  padding: 28px;
  margin-top: -4px;
}

.reviewsheroSection h1 {
  font-size: 24px;
  line-height: 38px;
  font-weight: normal;
  padding-bottom: 20px;
}

.reviewsheroSection button.primaryBtn {
  padding: 5px 12px;
}

.reviewsheroSection input#autoComplete {
  max-width: 480px;
  padding: 10px;
  padding-left: 40px;
  font-size: 14px;
  line-height: 24px;
  width: 100%;
  border-radius: 3px;
  border: var(--border-line);
  background: url(../../../yas/images/search-icon.png) no-repeat;
  background-position: 15px 48%;
  color: var(--primary-font-color);
}

.reviewsheroSection input#autoComplete::-moz-placeholder {
  color: #989898;
  opacity: 1;
  font-weight: 500;
}

.reviewsheroSection input#autoComplete:-ms-input-placeholder {
  color: #989898;
  opacity: 1;
  font-weight: 500;
}

.reviewsheroSection input#autoComplete::placeholder {
  color: #989898;
  opacity: 1;
  font-weight: 500;
}

.reviewsheroSection input#autoComplete:focus {
  background-size: auto;
  outline: none;
}

.reviewsheroSection input#autoComplete:focus::-webkit-input-placeholder {
  padding-left: 0px;
  font-size: 14px;
}

.reviewsheroSection #autoComplete_list {
  max-width: 480px;
  border-radius: 0;
  margin: 0;
  position: absolute;
  max-height: 205px;
  overflow: auto;
  width: 100%;
  background: #fff;
  z-index: 3;
  left: 0;
  top: 46px;
}

.reviewsheroSection #autoComplete_list li.no_result {
  list-style-type: none;
}

.reviewsheroSection .searchBar {
  position: relative;
}

.reviewsheroSection .searchBar input {
  margin-bottom: 0;
}

.reviewsheroSection .autoComplete_list {
  padding: 0;
  margin: 0;
  max-width: 480px;
  border: var(--border-line);
}

.reviewsheroSection .autoComplete_list li {
  list-style-type: none;
  padding: 3px 5px;
  cursor: pointer;
}

.reviewsheroSection .autoComplete_list li:hover {
  background: rgba(0, 0, 0, 0.15);
}

.reviewsheroSection .autoComplete_list a {
  text-decoration: none;
}

.reviewsheroSection .col-md-5 {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.filterSidebarSection {
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: #fff;
  margin-bottom: 20px;
  padding-bottom: 0px;
}

.filterSidebarSection .row {
  margin: 0;
}

.filterSidebarSection p,
.filterSidebarSection li,
.filterSidebarSection a,
.filterSidebarSection button {
  font-size: 14px;
  line-height: 24px;
}

.filterSidebarSection a {
  text-decoration: none;
}

.filterSidebarSection .clearAll {
  color: var(--color-red);
  cursor: pointer;
}

.filterSidebarSection .foundesults {
  justify-content: space-between;
  padding-bottom: 20px;
}

.filterCategoryReviewName {
  margin: 0 -20px;
  padding: 8px 20px;
  font-weight: 500;
  background: #f5f5f5;
  position: relative;
  cursor: pointer;
}

.filterCategoryReviewName:after {
  content: " ";
  background: url(/yas/images/master_sprite.webp);
  width: 12px;
  height: 16px;
  position: absolute;
  right: 17px;
  top: 12px;
  background-position: 652px -94px;
  transition: 0.2s ease;
  transform: rotate(-90deg);
}

.filterCategoryReviewName.down_angle:after {
  transform: rotate(90deg);
}

.filterReviewDiv {
  padding-top: 12px;
}

.filterReviewDiv button {
  color: #787878;
  padding: 5px 8px;
  border-radius: 24px;
  border: var(--border-line);
  background: var(--color-white);
  margin-right: 5px;
  margin-bottom: 16px;
  font-weight: 500;
  cursor: initial;
}

.filterReviewDiv .closeIcon {
  cursor: pointer;
}

.filterReviewDiv input[type=checkbox] {
  margin: 0;
  margin-top: 3px;
  vertical-align: middle;
  flex-basis: 16px;
}

.filterReviewDiv ul {
  padding: 0;
  margin: 0;
  max-height: 120px;
  overflow: auto;
}

.filterReviewDiv ul li {
  list-style-type: none;
  padding-bottom: 8px;
  display: flex;
}

.filterReviewDiv ul::-webkit-scrollbar {
  width: 5px;
}

.filterReviewDiv ul::-webkit-scrollbar-thumb {
  background: #ccc;
}

.filterReviewDiv ul::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.filterReviewDiv label {
  color: #787878;
  padding-left: 10px;
  cursor: pointer;
  vertical-align: middle;
  flex-basis: calc(100% - 16px);
}

.filterReviewSearch input[type=text] {
  border: var(--border-line);
  padding: 7px 16px;
  margin-bottom: 16px;
  line-height: 24px;
  font-size: 14px;
  width: 100%;
  border-radius: 2px;
}

.sortBySection.row {
  margin: 0;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.sortBySection .sortByList select {
  padding: 5px 12px;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  background: var(--color-white);
  border: var(--border-line);
  color: #787878;
  border-radius: 3px;
  min-width: 162px;
  background-position: 92% 14px !important;
  cursor: pointer;
  outline: none;
}

.pagination {
  margin-top: 10px;
}

.pagination a {
  background-color: white;
}

.pagination .active a {
  background-color: #ff4e53;
  color: white;
}

.mobileSortandFilter,
.mobileFilterSectionReview,
.mobileSortSection {
  display: none;
}

/* review-detail-page */
.reviewerHeader {
  padding: 20px;
  background-image: linear-gradient(to right, #fff 0%, #fffbec 100%);
  position: relative;
}

.reviewerHeader .reviewerImage {
  max-width: 125px;
  min-width: 56px;
  display: block;
  height: auto;
  border-radius: 50%;
  float: left;
  margin-right: 10px;
  aspect-ratio: 1/1;
}

.reviewerHeader .reviewerNameCardHeader {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.3;
}

.reviewerHeader .reviewerHeaderText,
.reviewerHeader .reviewerHeaderText .reviewCourseName,
.reviewerHeader .reviewerHeaderText .reviewBatchName {
  margin-top: 10px;
  font-size: 14px;
  font-weight: normal;
  color: #787878;
  line-height: 1.65;
}

.reviewerHeader .reviewerHeaderText div {
  display: inline;
  margin-bottom: 10px;
}

/* .reviewerHeader .reviewerHeaderText span:last-child::before {
  content: "\2022   ";
  font-weight: bold;
} */

.starRating span {
  margin-right: 2px;
}

.reviewerHeader .starRating {
  margin-top: 10px;
  display: inline-flex;
  margin-right: 4px;
}

.reviewerHeader .starRating .reviewHeaderOverall {
  margin-right: 8px;
}

.reviewerHeader .starRating span {
  font-size: 14px;
  font-weight: normal;
  color: #787878;
}

.reviewerHeader .verifiedSpan {
  font-size: 12px;
  color: #388e3c;
}

.reviewerHeader .reviewedDate {
  margin-top: 15px;
  font-size: 14px;
  color: #787878;
}

.reviewerSection {
  display: flex;
  flex-direction: column;
}

.reviewerSection .reviewerName h3 {
  display: inline;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.33;
  letter-spacing: 0.3px;
  text-align: left;
}

.reviewerSection p {
  color: #787878;
  font-size: 14px;
  margin-top: 5px;
  margin-bottom: 10px;
}

.reviewerSection .reviewerDetails>* {
  display: inline-block;
}

.reviewerSection .reviewDate {
  margin: 0px;
}

.starRating {
  display: flex;
  align-items: center;
}

.stars {
  margin: 0px;
  padding: 0px;
}

.stars li {
  background-position: -613px -244px;
  width: 20px;
  height: 20px;
}

.reviewerRating {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
}

.reviewerRating p {
  font-size: 15px;
  line-height: 1.6;
  letter-spacing: 0.3px;
  text-align: left;
  color: #787878;
}

.reviewerRating p span {
  color: #282828;
  font-weight: 500;
}

.reviewerRating span {
  font-size: 12px;
  line-height: 2;
  letter-spacing: 0.3px;
  text-align: left;
  color: #388e3c;
}

.writeReviewButton {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 161px;
  height: 36px;
  background-color: #ff4e53;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  border: 1px solid #ff4e53;
  border-radius: 3px;
}

.write-review-otp-section .writeReviewVerifyOtp {
  border: 1px solid #e9acac;
  background: #e9acac;
}

.review-details {
  padding: 20px;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  position: relative;
}

.review-details h3 {
  font-size: 18px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.33;
  letter-spacing: 0.3px;
  text-align: left;
}

.review-details .reviewFeedback {
  position: absolute;
  bottom: 5px;
  right: 0;
  font-size: 12px;
  color: #282828;
}

.review-details section:nth-child(7) {
  border-bottom: none !important;
}

.reviewTitle {
  padding: 10px 10px;
  background-color: #f5f5f5;
  margin-bottom: 23px;
}

.reviewSubSection {
  margin: 20px 0px;
  border-bottom: 1px solid #d8d8d8;
  margin-top: 0px;
}

.reviewSubSection .subSectionHeader {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.reviewSubSection .subSectionHeader h3 {
  padding-left: 48px;
}

.reviewSubSection .subSectionHeader .starRating {
  padding-left: 48px;
}

.reviewSubSection .subSectionParagraph {
  margin: 20px 0px;
  font-size: 15px;
  line-height: 1.6;
  letter-spacing: 0.3px;
  text-align: left;
  color: #282828;
}

.reviewSubSection .subSectionList {
  /* display: flex; */
  list-style: none;
  padding: 0;
  flex-wrap: wrap;
}

.reviewSubSection li {
  flex-basis: 50%;
}

.reviewSubSection li span {
  height: 20px;
  width: 20px;
  background-position: -86px -70px;
  vertical-align: text-top;
  margin-left: -5px;
  margin-right: 10px;
}

.notepadIcon {
  height: 40px;
  width: 40px;
  background-position: -684px -398px;
  position: absolute;
}

.sideWidgets {
  margin-top: 20px;
  padding: 0px 0px 0px 20px;
}

.getSupport {
  padding: 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  background-color: #fff;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  margin-bottom: 20px;
}

.getSupport .supportSection {
  display: flex;
  align-items: center;
  gap: 16px;
}

.getSupport p {
  display: inline;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.44;
  letter-spacing: 0.3px;
  text-align: left;
  color: #282828;
}

button {
  font-size: 14px;
  font-weight: bold;
  line-height: 1.71;
  color: #fff;
  min-height: 36px;
  /* flex-basis: calc(50% - 16px); */
  border: none;
  border-radius: 3px;
}

.buttonOne {
  background-color: #0966c2;
  flex-basis: calc(50% - 16px);
}

.buttonTwo {
  background-color: #ff4e53;
  flex-basis: calc(50% - 16px);
}

.sideBarSection {
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 4px;
  margin-bottom: 20px;
}

.sideBarSection .sidebarHeading {
  background: #d8d8d8;
  padding: 10px 20px;
  font-size: 15px;
  line-height: 24px;
  margin: 20px;
  margin-bottom: 6px;
  font-weight: 500;
}

.sideBarSection .sidebarCollegeContainer {
  width: 90%;
}

.sideBarSection .sidebarCollegeContainer .row {
  justify-content: center;
  flex-wrap: nowrap;
  border-bottom: 1px solid #d8d8d8;
  margin: 0px;
  margin-bottom: 10px;
  padding-bottom: 10px;
  gap: 10px;
}

.sideBarSection .sidebarCollegeContainer .row img {
  width: 72px;
  height: 72px;
}

.sideBarSection .sidebarCollegeContainer .row p {
  font-size: 14px;
  font-weight: normal;
  line-height: 1.71;
}

.badgeIcon {
  width: 24px;
  height: 28px;
  background-position: 167px -262px;
  margin-right: 15px;
  margin-top: -10px;
}

.sidebarLinks.overflow-scroll {
  max-height: 479px;
}

.overflow-scroll {
  overflow: auto;
}

.sideBarSection .listCard:last-child {
  border-bottom: none;
}

.sideBarSection .listCard {
  display: block;
  padding: 10px 20px;
  border-bottom: var(--border-line);
}

.listCard:last-child>div {
  margin-bottom: 0;
  padding-bottom: 0;
  border: none;
}

.sideBarSection .row {
  margin: 0;
}

.sideBarSection .sidebarImgDiv {
  flex-basis: 72px;
  margin-right: 20px;
}

.sidebarImgDiv {
  flex-basis: 96px;
  margin-right: 16px;
  display: grid;
  min-height: 72px;
}

.sideBarSection .sidebarImgDiv img {
  display: block;
  margin: 0 auto;
  width: 100%;
}

.sideBarSection .sidebarTextLink {
  flex-basis: calc(100% - 92px);
}

.sidebarTextLink {
  word-break: break-all;
}

.sideBarSection .sidebarTextLink .cardText {
  color: var(--primary-font-color);
}

.sideBarSection .sidebarTextLink .subText {
  padding-top: 3px;
  color: #989898;
}

.sideBarSection .sidebarTextLink p {
  font-size: 15px;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.parentCollegeSection .spriteIcon {
  position: initial;
  vertical-align: middle;
}

.sliderCardInfo .locationIcon {
  background-position: 594px -311px;
  margin-left: 15px;
  margin-right: 0px !important;
}

.allReviews {
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: #fff;
  margin-bottom: 20px;
  box-shadow: none;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  padding: 20px 0px;
}

.allReviews .row {
  margin: 0;
}

.allReviews .col-md-3,
.allReviews .col-md-4 {
  text-align: center;
  padding-left: 0px;
  margin-right: 70px;
}

.allReviews .ratingHeading {
  font-size: 18px;
  font-weight: bold;
  line-height: 28px;
  font-weight: 500;
  padding-bottom: 10px;
}

.allReviews .ratingHeading span {
  font-size: 14px;
  color: #787878;
}

.allReviews .avgRating {
  font-size: 50px;
  line-height: 38px;
  /* padding: 20px 0;
  padding-top: 10px; */
  font-weight: var(--font-bold);
  /* display: inline; */
}

/* .allReviews .col-md-8 {
  padding: 0px;
  margin-left: -10px;
} */

.allReviews .subText {
  color: #787878;
  font-size: 14px;
  line-height: 28px;
  margin-top: 8px;
  font-weight: 500;
}

.allReviews .ratings {
  line-height: 34px;
  display: inline-block;
  vertical-align: top;
}

.allReviews .ratings .full-star,
.allReviews .ratings .half-star,
.allReviews .ratings .empty-star {
  transform: scale(1.5);
  margin: 0 6px;
}

.starRating .full-star,
.starRating .half-star,
.starRating .empty-star {
  vertical-align: text-top !important;
  margin-left: 1px;
  margin-right: 2px;
}

.allReviews .componentRatio .row {
  margin-bottom: 10px;
  flex-wrap: nowrap;
  justify-content: space-between;
}

.allReviews .componentRatio .row .full-star,
.allReviews .componentRatio .row .half-star,
.allReviews .componentRatio .row .empty-star {
  transform: scale(1);
  margin: 0 4px;
}

.allReviews .componentRatio .row .spriteIcon {
  vertical-align: top;
}

.componentRatio .spriteIcon {
  margin: 0 2px;
}

.full-star {
  background-position: 129px -246px;
}

.empty-star {
  background-position: 72px -246px;
}

.half-star {
  background-position: 99px -246px;
}

.full-star,
.half-star,
.empty-star {
  width: 17px;
  height: 17px;
  vertical-align: text-bottom;
}

.reviewerDetailCardHeader .full-star,
.reviewerDetailCardHeader .half-star,
.reviewerDetailCardHeader .empty-star {
  margin-right: 2px;
  margin-left: 1px;
}

.ratingList .full-star,
.ratingList .half-star,
.ratingList .empty-star {
  margin-right: 2px;
  margin-left: 1px;
}

.moreReviews {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  padding: 0px;
  align-items: stretch;
  gap: 20px;
}

.moreReviews .headerMoreReviews {
  width: inherit;
  background-color: #fff;
  height: 84px;
  padding: 20px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
}

.moreReviews .headerMoreReviews h3 {
  font-size: 18px;
  font-weight: 500;
  color: #282828;
  background-color: #f5f5f5;
  padding: 10px 20px;
}

.moreReviews .reviewCard {
  display: flex;
  flex-direction: column;
  width: inherit;
  padding: 20px 20px 20px 20px;
  background-color: #fff;
  gap: 10px;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
}

.moreReviews .reviewCard .reviewerDetailCardHeader {
  line-height: 1.5;
}

.moreReviews .reviewCard .reviewerDetailCardHeader img {
  height: 40px;
  width: 40px;
  background-color: #d8d8d8;
  border-radius: 50%;
  vertical-align: text-top;
  float: left;
  margin-right: 10px;
}

.moreReviews .reviewCard .reviewerDetailCardHeader .starRating {
  display: inline-flex !important;
}

.moreReviews .reviewCard .reviewerDetailCardHeader .starRating .stars .starIcon {
  vertical-align: sub;
}

.moreReviews .reviewCard .reviewerDetailCardHeader .starRating span {
  color: #282828;
}

.moreReviews .reviewCard .reviewerDetailCardHeader .verifiedSpan {
  float: right;
  color: #388e3c;
}

.moreReviews .reviewCard .reviewerDetailCardHeader span {
  font-size: 14px;
  /* color: #787878; */
}

.moreReviews .reviewCard .reviewerDetailCardHeader .reviewerNameCardHeader {
  font-size: 15px;
  font-weight: 500;
  color: #282828;
}

.moreReviews .reviewCard h4 {
  margin-top: 10px;
}

.moreReviews .reviewCard .ratingList {
  display: grid;
  /* flex-wrap: wrap; */
  grid-template-columns: repeat(6, max-content);
  justify-items: start;
}

.moreReviews .reviewCard .ratingList div {
  font-size: 12px;
  color: #787878;
  margin: 0px 5px 0px 0px;
}

.moreReviews .reviewCard .ratingList div b {
  color: #282828;
}

.moreReviews .reviewCard .ratingList .ratingRow {
  display: flex;
}

.moreReviews .reviewCard .ratingList .ratingRow div:not(:first-child) ::before {
  content: "\2022    ";
  font-size: 16px;
}

.moreReviews .reviewCard p {
  font-size: 14px;
  font-weight: normal;
  line-height: 1.71;
  color: #282828;
}

.moreReviews .reviewCard .imageList {
  display: flex;
  gap: 10px;
}

.moreReviews .reviewCard .imageList img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
}

.moreReviews .reviewCard .imageList .moreImages {
  position: relative;
}

.moreReviews .reviewCard .imageList .moreImages img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  background-color: #282828;
  opacity: 0.5;
}

.moreReviews .reviewCard .imageList .moreImages p {
  position: absolute;
  top: 30%;
  left: 40%;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.6;
  color: #fff;
}

.moreReviews .reviewCard .redirectreviewCard p {
  font-size: 12px;
  line-height: 24px;
}

.moreReviews .reviewCard .redirectreviewCard a {
  font-size: 14px;
  font-weight: bold;
  color: #ff4e53;
}

.moreReviews .reviewCard .redirectreviewCard a .urlIcon {
  width: 13px;
  height: 14px;
  background-position: 302px -375px;
  vertical-align: text-bottom;
}

.moreReviews .buttonTwo {
  padding: 5px 36px;
}

.lowerWidgets {
  margin-top: 20px;
  padding: 0px;
}

.lowerWidgets .pageData {
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: #fff;
  margin-bottom: 20px;
}

.lowerWidgets .pageData h2 {
  font-size: 18px;
  line-height: 28px;
  padding: 8px 20px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  text-transform: uppercase;
  position: relative;
}

.lowerWidgets .pageData h2 a {
  font-size: 14px;
  line-height: 24px;
  color: var(--color-red);
  font-weight: 500;
  text-transform: capitalize;
}

.lowerWidgets .pageData p,
.lowerWidgets .pageData li,
.lowerWidgets .pageData a {
  font-size: 15px;
  line-height: 26px;
}

.lowerWidgets .pageData p {
  color: var(--primary-font-color);
  padding-bottom: 15px;
}

.lowerWidgets .pageData h3 {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}

.lowerWidgets .pageData h4 {
  padding-bottom: 10px;
  line-height: 24px;
  font-weight: 500;
}

.lowerWidgets .pageData button {
  font-size: 14px;
  line-height: 20px;
  color: var(--color-white);
  padding: 8px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  transition: 0.2s ease;
  outline: none;
  margin-bottom: 10px;
}

.lowerWidgets .pageData .greyBtn {
  color: #787878;
  padding: 5px 20px;
  border-radius: 24px;
  border: var(--border-line);
  background: var(--color-white);
  margin-right: 5px;
  margin-bottom: 8px;
  font-weight: 500;
  display: inline-block;
  text-decoration: none;
}

.lowerWidgets .pageData .greyBtn:hover {
  background: #f2f2f2;
}

.lowerWidgets .pageData ul li {
  position: relative;
  list-style-type: none;
}

.lowerWidgets .pageData ul li:before {
  content: "";
  background: url(/yas/images/master_sprite.webp);
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

.customSlider {
  position: relative;
}

.customSlider .scrollRight {
  right: -20px;
}

.customSlider .scrollLeft {
  top: 50%;
  left: -20px;
}

.customSlider .row {
  margin: 0;
}

.customSlider .customSliderCards {
  display: flex;
  white-space: nowrap;
  overflow: auto;
}

.reviewSubSection .subSectionList .customSliderCards {
  display: block;
}

.customSlider .customSliderCards::-webkit-scrollbar {
  display: none;
}

.customSlider .sliderCardInfo {
  padding: 20px;
  border-radius: 4px;
  border: var(--border-line);
  margin-right: 20px;
}

.customSlider .sliderCardInfo:last-child {
  margin-right: 0;
}

.customSlider .sliderCardInfo .clgLogo {
  max-width: 72px;
  height: 72px;
  display: block;
  margin-right: 20px;
}

.customSlider .sliderCardInfo p {
  padding-bottom: 0px;
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-semibold);
}

.customSlider .sliderCardInfo p span {
  color: #989898;
  font-weight: normal;
  font-size: 13px;
}

.customSlider .sliderCardInfo p:first-child {
  font-size: 16px;
  line-height: 24px;
  padding-bottom: 2px;
}

.customSlider .sliderCardInfo p:first-child a {
  color: var(--primary-font-color);
}

.customSlider .sliderCardInfo p:first-child a:hover {
  color: var(--anchor-textclr);
}

.four-cardDisplay .sliderCardInfo {
  width: 35%;
  display: inline-block;
  padding: 0;
  white-space: initial;
  margin-right: 14px;
  min-width: 33%;
}

.four-cardDisplay .sliderCardInfo:nth-of-type(4n) {
  margin-right: 0;
}

.four-cardDisplay .sliderCardInfo:nth-of-type(4n + 1) {
  margin-left: 20px;
}

.four-cardDisplay .sliderCardInfo:first-child {
  margin-left: 0px;
}

.four-cardDisplay .sliderCardInfo img {
  display: block;
  width: 100%;
}

.four-cardDisplay .sliderCardInfo figure {
  /* display: grid; */
  height: 207px;
  border-bottom: var(--border-line);
}

.four-cardDisplay .sliderCardInfo figure img {
  align-self: center;
  height: inherit;
}

.four-cardDisplay .sliderCardInfo p {
  font-size: 14px;
  line-height: 24px;
}

.four-cardDisplay .sliderCardInfo h3,
.four-cardDisplay .sliderCardInfo .widgetCardHeading {
  font-size: 14px;
  line-height: 24px;
  padding-bottom: 0;
  min-height: 48px;
  margin-bottom: 5px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
}

.four-cardDisplay .sliderCardInfo .subText {
  color: #989898;
  font-weight: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.four-cardDisplay .sliderCardInfo .spriteIcon {
  position: initial;
  vertical-align: middle;
  margin-right: 5px;
}

.four-cardDisplay .sliderCardInfo .textDiv {
  padding: 20px;
}

.four-cardDisplay .sliderCardInfo .collegeLogo {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  margin-top: -60px;
  box-shadow: 0 0px 10px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 5px;
}

.four-cardDisplay a {
  text-decoration: none;
}

.four-cardDisplay .displayCard:hover h3,
.four-cardDisplay .displayCard:hover .widgetCardHeading {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.two-cardDisplay .sliderCardInfo {
  min-width: 50%;
  display: inline-block;
}

.sidebarAds .appendAdDiv {
  width: 300px;
  height: 250px;
  display: block;
  margin: 0 auto;
  margin-bottom: 20px;
}

.review-details .pageData {
  margin-bottom: 20px;
}

.subSectionIcons {
  background: url(/yas/images/master_sprite.webp);
  height: 40px;
  width: 40px;
  position: absolute;
}

.notepadIcon {
  background-position: -503px -504px;
}

.feesIcon {
  background-position: -554px -505px;
}

.academicIcon {
  background-position: -606px -505px;
}

.collegeIcon {
  background-position: -503px -561px;
}

.placementIcon {
  background-position: -556px -559px;
}

.clubIcon {
  background-position: -602px -564px;
}

.verifiedShieldIcon {
  background: url("/yas/images/master_sprite.webp");
  height: 16px;
  width: 16px;
  background-position: -705px -554px;
  vertical-align: text-bottom;
}

.verifiedBlueTickIcon {
  background: url("/yas/images/master_sprite.webp");
  width: 16px;
  height: 16px;
  background-position: -673px -557px;
  vertical-align: text-top;
}

.likeBtn {
  background: url(/yas/images/master_sprite.webp);
  width: 20px;
  height: 20px;
  background-position: -673px -515px;
  vertical-align: text-bottom;
}

.unlikeBtn {
  background: url(/yas/images/master_sprite.webp);
  width: 20px;
  height: 20px;
  background-position: -703px -516px;
  vertical-align: text-bottom;
}

.allReviews .col-md-3 .verifiedSpan {
  display: block;
  color: #388e3c;
  font-size: 12px;
  margin-top: 10px;
}

.row.mainReviewContainer {
  position: relative;
}

aside.col-md-4.sideWidgets {
  position: absolute;
  right: 0px;
}

a.primaryBtn.viewAllReviewsBtn {
  align-self: center;
}

.moreReviews .reviewCard .ratingList div:not(:first-child) b::before {
  color: #787878;
  font-size: 16px;
  margin-left: 4px;
  margin-right: 6px;
  content: "\2022       ";
}

.writeReviewRedirect {
  flex-basis: calc(50% - 16px);
}

/* .brochureBtn.fixedbrochureBtn {
  position: fixed !important;
  width: 100% !important;
  left: 0;
  top: 45px;
  z-index: 2;
  margin: 0px !important;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  border-radius: 0px;
  transition: none;
} */

span#resendTimer {
  color: var(--color-red);
}

.reviewLandingCollegeInfo {
  max-width: 726px;
}

.reviewCard .reviewerDetailCardHeader .reviewLandingHeader {
  margin-left: 50px !important;
}

/* rating section */
.ratingSection .ratingSectionList {
  display: flex;
  gap: 15px;
}

.ratingSection ul {
  padding: 0;
  margin: 0;
}

.ratingSection ul li {
  cursor: pointer;
  display: inline-block;
  margin: 0 7px;
  font-size: 14px;
  line-height: 24px;
  text-align: center;
  position: relative;
}

.ratingSection ul .empty-star {
  transform: scale(1.6);
  width: 20px;
  height: 20px;
  background-position: -670px -244px;
}

.ratingSection ul .full-star {
  transform: scale(1.6);
  width: 20px;
  height: 20px;
  background-position: -614px -244px;
}

.ratingSection button {
  padding: 5px;
  font-size: 14px;
  vertical-align: middle;
  font-weight: 500;
  border-radius: 4px;
  color: #fff;
  border: none;
  outline: none;
}

.ratingSection .veryPoor {
  background: #ff4e53;
}

.ratingSection .exellent {
  background: #5cb85c;
}

.componentRatio .row p:first-child {
  max-width: 200px;
}

.displayCard .subText {
  margin-bottom: 15px !important;
}

.reviewListBody .loadMoreListReview {
  font-size: 14px;
  line-height: 24px;
  padding: 5px;
  border-radius: 3px;
  border: 1px solid var(--color-red);
  background: var(--color-white);
  max-width: 460px;
  margin: 0 auto;
  margin-bottom: 20px;
  color: var(--color-red);
  text-align: center;
  font-weight: 500;
  cursor: pointer;
}

.redCaret {
  width: 12px;
  height: 10px;
  background-position: 651px -154px;
  transform: rotate(180deg);
  margin-left: 5px;
}

.loadMoreReviews {
  margin-top: 10px;
}

.overrallRating {
  color: #282828 !important;
  font-weight: 500 !important;
  margin-right: 8px !important;
}

.reviewsheroSection .col-md-5 a.primaryBtn.writeReviewRedirect {
  max-width: 161px;
}

.review .col-md-3 .filterSidebarSection {
  box-shadow: none;
  border: 1px solid #d8d8d8;
}

.moreReviews .reviewCard .redirectreviewCard {
  margin-left: -20px;
  margin-right: -20px;
  margin-bottom: -20px;
  padding: 20px;
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: #fafbfc;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px;
}

.moreReviews .reviewCard .redirectreviewCard a .urlIcon {
  margin-left: 5px;
}

.reviewRatingSection .allReviews .col-md-4 {
  margin-right: 0px;
  flex-basis: 30%;
}

.reviewRatingSection .allReviews .col-md-6 {
  max-width: 70%;
  flex-basis: 70%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.reviewRatingSection .allReviews .col-md-6 .componentRatio .row p:first-child {
  max-width: 300px;
}

.moreReviews a.primaryBtn.viewAllReviewsBtn {
  min-width: 180px;
}

.reviewRatingSection .allReviews .col-md-4 .ratingHeading {
  padding-bottom: 20px;
}

.reviewRatingSection .allReviews .col-md-4 .ratings {
  margin-top: 20px;
  margin-bottom: 10px;
}

.reviewerHeader .reviewerHeaderText div span:after {
  color: #787878;
  font-size: 18px;
  margin-left: 8px;
  margin-right: 6px;
  content: "\2022 ";
}

.reviewListBody .reviewsheroSection .row .col-md-7 .searchBar {
  padding-bottom: 8px;
}

.reviewerHeaderContent.reviewLandingHeader span:not(.reviewerNameCardHeader):before {
  content: "\2022";
  margin-left: 5px;
  margin-right: 5px;
}

.moreReviews .reviewCard .ratingList div:not(:first-child) b::before {
  vertical-align: middle;
}

.reviewsheroSection input#autoComplete {
  border: 1px solid #eaeaea;
}

.reviewsheroSection .autoComplete_list {
  position: absolute;
  z-index: 5;
  background-color: #fff;
  width: 100%;
}

.reviewerHeaderContent.reviewLandingHeader .reviewLandingCollegeInfo span:first-child:before {
  content: none;
}

.reviewerHeaderContent.reviewLandingHeader .starRating span:not(.reviewLandingStarRating):before {
  content: "\2022";
  margin-left: 5px;
  margin-right: 5px;
}

.reviewSubSection .subSectionList .reviewImagecustomSliderCards {
  display: flex;
}

.reviewSubSection .subSectionList .reviewImagecustomSliderCards .reviewGallery .displayCard .reviewImagesFancyBox {
  height: inherit;
}

.leadFormDiv .userInputs .row .col-md-6 {
  height: 73px !important;
}

.profileImage {
  margin-right: 0px !important;
}

.collegeImage {
  margin-left: 36px !important;
  margin-right: 0px !important;
}

.fees__div {
  display: flex;
  width: 100%;
}

.fees__div .fees__dropdown__container {
  flex-basis: 128px;
}

.fees__div .fees__dropdown__container select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
}

.fees__div .form-group {
  flex-grow: 1;
}

.fees__div .form-group input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.review__Error__Popup {
  outline: none;
  border: none;
  padding: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(51, 51, 51, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  display: none;
}

.review__Error__Popup:focus {
  outline: none;
}

.review__Error__Popup .error__text {
  font-size: 24px;
  line-height: 36px;
  color: var(--primary-font-color);
  padding: 0;
}

.review_center_align {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
  min-width: 300px;
  outline: none;
  border: none;
  text-align: center;
  background: #fff;
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 3px;
}

.review_center_align svg {
  max-width: 100px;
}

.close__review__error__popup {
  position: absolute;
  top: 20px;
  right: 20px;
  background-position: -91px -335px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}


@media (max-width: 1023px) {

  .collegeImage {
    margin-left: 0px !important;
  }

  .leadFormDiv .userInputs .row .col-md-6 {
    height: 55px !important;
  }

  .newsSidebarSection {
    margin-top: 10px;
  }

  .moreReviews .reviewCard .ratingList {
    grid-row-gap: 5px;
  }

  .scrollToTop {
    z-index: 2;
  }

  .reviewerHeaderContent.reviewLandingHeader span:not(.reviewerNameCardHeader):before {
    content: none;
  }

  .reviewerHeaderContent.reviewLandingHeader span:after {
    content: '\2022';
    margin-left: 5px;
    margin-right: 5px;
  }

  .full-star,
  .half-star,
  .empty-star {
    vertical-align: middle;
  }

  .reviewerHeaderContent.reviewLandingHeader span:nth-child(2):before {
    content: none;
  }

  .reviewerHeaderContent.reviewLandingHeader .reviewLandingStarRating span:not(.reviewLandingStarRating):before {
    content: none;
  }

  .reviewerHeaderContent.reviewLandingHeader .reviewLandingStarRating span:not(.reviewLandingStarRating):after {
    content: none;
  }

  .reviewerHeaderContent.reviewLandingHeader span.reviewerNameCardHeader:after {
    content: none;
  }

  .filterReviewDiv button {
    font-size: 13px;
  }

  .filterReviewDiv .foundReviews {
    margin-left: 11px;
  }

  .selectedResultsMobile .filterReviewDiv button {
    padding: 0px 8px;
    margin-bottom: 10px
  }

  .selectedResultsMobile .filterReviewDiv span {
    font-size: 13px;
    line-height: 20px;
    font-weight: 500;
  }

  .foundReviews button {
    display: none;
  }

  .moreReviews .reviewCard .reviewerDetailCardHeader .reviewerNameCardHeader {
    font-size: 14px;
  }

  .thankyouSection .thankyouDiv p.p-0 {
    font-size: 14px;
  }

  .writeCollege {
    font-size: 16px !important;
    text-align: left;
  }

  .reviewerHeader .reviewerHeaderText div span:last-child::after {
    content: none;
  }

  .reviewTitle {
    margin-bottom: 10px;
  }

  .allReviews .componentRatio .row .reviewValue {
    font-size: 14px;
  }

  .review-details {
    margin-top: 10px;
  }

  .moreReviews .reviewCard .ratingList div:not(:first-child) b::before {
    margin-left: 0px;
    margin-right: 0px;
    line-height: 1;
  }

  .reviewContainer .headerReviewContainer,
  .reviewContainer .mainReviewContainer {
    margin-left: 0px;
    margin-right: 0px;
  }

  .reviewRatingSection .allReviews .col-md-4 .ratings {
    margin-top: 0px;
  }

  .reviewRatingSection .allReviews .col-md-4 .ratingHeading {
    padding-bottom: 8px;
  }

  .sideWidgets {
    display: block;
    padding: 0px;
    margin: 0px;
  }

  .sideWidgets .sidebarAds {
    display: none;
  }

  .sideWidgets .getSupport {
    margin-bottom: 0px;
    gap: 5px;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1;
    padding: 10px;
  }

  .sideWidgets .getSupport .supportSection {
    display: none;
  }

  .sideWidgets .getSupport button {
    flex-basis: 49%;
  }

  .review-details {
    padding: 10px;
  }

  .reviewSubSection {
    margin: 16px 0px;
  }

  .reviewSubSection .subSectionParagraph {
    margin: 10px 0px;
  }

  .allReviews {
    margin-bottom: 10px;
  }

  .subSectionIcons {
    top: -10px;
  }

  .reviewerHeader {
    background-image: linear-gradient(to right, #fff 0%, #fff 100%);
    padding: 10px;
    border-radius: 4px;
  }

  .moreReviews a.primaryBtn.viewAllReviewsBtn {
    width: 100%;
  }

  .reviewRatingSection .allReviews .col-md-4 {
    margin-right: 0px;
    flex-basis: 100%;
  }

  .reviewRatingSection .allReviews .col-md-6 {
    max-width: 100%;
    flex-basis: 100%;
  }

  .reviewRatingSection .allReviews .col-md-6 .componentRatio .row p:first-child {
    max-width: 200px;
  }

  .moreReviews .reviewCard .redirectreviewCard {
    margin-left: -10px;
    margin-bottom: -10px;
    margin-right: -10px;
    background-color: #fafbfc;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 0px;
    border-radius: 4px;
  }

  .moreReviews .reviewCard .redirectreviewCard a {
    padding-left: 7px;
  }

  .reviewsheroSection .col-md-5 a.primaryBtn.writeReviewRedirect {
    max-width: 100%;
  }

  .breadcrumb {
    margin-bottom: 10px !important;
  }

  .reviewListBody .loadMoreListReview {
    width: 100%;
    margin-bottom: 10px;
    padding: 5px;
  }

  .allReviews .col-md-4 {
    margin-right: 0px;
    text-align: start;
    margin-bottom: 10px;
  }

  .allReviews .col-md-4 .verifiedSpan {
    position: absolute;
    top: 5px;
    right: 0px;
  }

  .allReviews .col-md-6 {
    padding: 0px;
  }

  .justify-content-between {
    display: flex !important;
  }

  .reviewsheroSection input#autoComplete {
    height: 40px;
  }

  .reviewsheroSection .col-md-5 {
    height: 43px;
  }

  /* .brochureBtn.fixedbrochureBtn {
    top: 0px;
  } */

  .selectedResultsMobile .foundesults {
    font-size: 15px;
    line-height: 25px;
    padding-bottom: 10px;
    font-weight: 500;
    margin-left: 12px;
  }

  .reviewCard .ratingList div:nth-child(3n+1) ::before {
    content: "" !important;
  }

  .reviewFormDiv .dialCodeDiv {
    position: absolute !important;
    left: 10px !important;
    border: 0px !important;
    padding-right: 10px !important;
    border-right: 1px solid #d8d8d8 !important;
  }

  .reviewFormDiv .numberInput input {
    padding-left: 106px !important;
  }

  .field-leadform-mobile .help-block {
    margin-left: 0px !important;
  }

  .imgPlaceholderRed {
    flex-basis: 30px;
    height: 27px;
    width: 30px;
    margin-right: 10px;
    margin-bottom: 46px;
    float: left;
    background-position: -112px -422px;
  }

  /* .dialCodeDiv {
    position: absolute !important;
    left: 10px;
    border: 0px;
    padding-right: 10px;
    border-right: 1px solid #d8d8d8 !important;
  }

  .numberInput input {
    padding-left: 100px !important;
  } */

  .referral .primaryBtn {
    position: absolute !important;
    z-index: 2;
    top: 1px !important;
    right: 0;
    width: 100px !important;
  }

  .referral .form-group {
    flex-basis: calc(100% - 106px);
  }

  .formField.referral {
    margin-bottom: 10px;
  }

  input[type=radio]+label {
    margin-bottom: 10px;
  }

  .formsStepsDiv {
    background: #0966c2;
    padding: 10px;
    margin: 0 -10px;
    margin-top: -80px
  }

  .stepsBarDiv {
    padding: 10px;
    margin: 0;
  }

  /* .dialCodeDiv img {
    margin: inherit;
  } */

  .reviewPageForm {
    margin-top: 0px;
  }

  .reviewFormDiv {
    padding: 10px;
    margin: 10px 0;
  }

  .reviewFormDiv p {
    font-size: 16px;
    padding-bottom: 10px;
  }

  .reviewFormDiv .primaryBtn {
    display: block;
    width: 100%;
  }

  .reviewFormDiv .heading {
    font-size: 16px;
    padding-bottom: 20px;
  }

  /* .numberInput input,
  .dialCodeDiv {
    height: 40px;
  } */

  .form-group {
    margin-bottom: 10px;
  }

  .form-group p {
    padding-bottom: 10px;
    font-size: 15px;
  }

  .formField input,
  .formField select,
  .formField .select2-container {
    font-size: 13px;
    padding: 7px 16px;
    padding-left: 40px;
  }

  /* .dialCodeDiv {
    flex-basis: 90px;
  } */

  /* .numberInput {
    flex-basis: calc(100% - 91px);
  } */

  .instructionDiv {
    padding: 10px;
    margin-bottom: 10px;
  }

  .instructionDiv ul {
    padding-left: 15px;
  }

  .instructionDiv .instructionText {
    flex-basis: calc(100% - 36px - 10px);
    margin-left: 39px;
    margin-top: -40px;
  }

  .instructionDiv .bulblightIcon {
    margin-right: 10px;
    flex-basis: 34px;
  }

  .instructionDiv p {
    font-size: 14px;
  }

  /* .ratingSection button {
    margin-bottom: 16px;
  }

  .ratingSection ul li {
    margin: 0 3px;
  }

  .ratingSection ul li span {
    width: 24px;
    height: 24px;
    line-height: 22px;
  }

  .ratingSection {
    margin-bottom: 40px;
  } */

  .btnDiv.row {
    justify-content: space-between;
  }

  .btnDiv.row button.primaryBtn {
    display: inline-block;
    width: auto;
  }

  .myReview {
    padding: 10px 0;
  }

  .myReview .heading {
    padding-bottom: 10px;
  }

  .pageFooter {
    margin: 0;
  }

  .uploadImage .uploadBlock {
    flex-basis: 100%;
    margin-right: 0px;
    margin-bottom: 10px;
    padding: 10px;
    text-align: left;
  }

  .uploadImage .blueUser,
  .uploadImage .addImg {
    float: left;
    margin-bottom: 50px;
    margin-right: 10px;
  }

  .uploadImage .addImg {
    margin-bottom: 70px;
  }

  .uploadCaution {
    margin-bottom: 10px !important;
  }

  .reviewFormDiv .agreeterms {
    display: flex;
  }

  .reviewFormDiv .agreeterms input {
    margin: 4px 15px 0 0;
    min-width: 18px;
  }

  .instructions {
    max-height: 140px;
    overflow: auto;
  }

  .instructions::-webkit-scrollbar {
    width: 5px;
    display: block;
  }

  .instructions::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .instructions::-webkit-scrollbar-thumb {
    background: #ccc;
  }

  .thankyouDiv {
    padding: 20px;
    margin-bottom: 10px;
  }

  .thankyouDiv .thankyouText {
    font-size: 15px;
    line-height: 24px;
  }

  .thankyouSection .hint {
    display: flex;
    margin-bottom: 10px;
    font-size: 13px;
  }

  .thankyouSection .hint .mailIcon {
    min-width: 20px;
  }

  .referDiv {
    padding: 10px;
    text-align: center;
  }

  .reviewFormDiv .row {
    display: block;
  }

  .reviewFormDiv .row ul li {
    margin-bottom: 10px;
  }

  .thankyouSection .refertext {
    font-weight: 500;
  }

  .copyreferalLink.row {
    display: flex;
  }

  .copyreferalLink.row input {
    padding: 10px;
  }

  .popupOtpSection .otpDiv {
    width: 95%;
    padding: 20px 10px;
  }

  .popupOtpSection .row p {
    text-align: center;
  }

  .optSectionHeader {
    margin-bottom: 20px;
  }

  .optSectionHeader img {
    width: 56px;
    height: 56px;
    margin-right: 10px;
  }

  .optSectionHeader .headingText {
    font-size: 16px;
    padding-bottom: 5px;
  }

  .optSectionHeader .sentOtp {
    font-size: 12px;
    line-height: 20px;
  }

  .ReviewOtpInputs input {
    margin-right: 16px;
  }

  .ReviewOtpInputs input:last-child {
    margin-right: 0px;
  }

  .reviewClosePopupForm {
    right: 10px;
    top: 10px;
  }

  /* review-landing-page */
  .moreReviews .reviewCard {
    padding: 10px;
  }

  .reviewsheroSection {
    padding: 10px;
    border-radius: 4px;
    /* box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12); */
    box-shadow: none;
    background: #fff;
    margin-top: -166px;
    margin-bottom: 20px
  }

  .reviewsheroSection h1 {
    font-size: 18px;
    padding-bottom: 10px;
    line-height: 25px;
    font-weight: normal;
  }

  .reviewsheroSection button.primaryBtn {
    flex-basis: 100%;
    margin-top: 10px;
  }

  .reviewsheroSection a.primaryBtn {
    flex-basis: 100%;
    margin-top: 10px;
  }

  .moreReviewsLanding {
    border: solid 1px #d8d8d8;
    background-color: #fff;
    padding: 5px;
    gap: 10px;
  }

  .moreReviewsLanding .reviewTitle {
    min-width: 100%;
  }

  .moreReviewsLanding .reviewTitle h3 {
    font-size: 15px;
    font-weight: 500;
    color: #282828;
  }

  .moreReviews.moreReviewsLanding {
    margin-top: 10px;
    border-radius: 4px;
  }

  .mobileSortandFilter {
    display: block;
    position: fixed;
    width: 100%;
    bottom: 0;
    background: #ffffff;
    left: 0;
    z-index: 3;
    border-top: var(--border-line);
  }

  .mobileSortandFilter .sortIcon {
    background-position: 534px -194px;
  }

  .mobileSortandFilter .optionDiv {
    display: flex;
  }

  .mobileSortandFilter button {
    width: 50%;
    border-radius: 0px;
    font-size: 14px;
    line-height: 24px;
    color: #787878;
    background-color: var(--color-white);
    padding: 9px 6px;
    text-transform: uppercase;
    position: relative;
    font-weight: 500;
    border-radius: 3px;
    border: none;
  }

  .mobileSortandFilter button:first-child {
    border-right: var(--border-line);
  }

  .mobileFilterSectionReview {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 5;
    width: 100%;
    height: 100%;
    background: var(--color-white);
  }

  .mobileFilterSectionReview h2,
  .mobileFilterSectionReview .mobileFilterHeading {
    background: var(--color-white);
    padding: 12px 16px;
    color: var(--primary-font-color);
    line-height: 20px;
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    display: flex;
    justify-content: space-between;
    border-bottom: var(--border-line);
  }

  .mobileFilterSectionReview h2 span,
  .mobileFilterSectionReview .mobileFilterHeading span {
    color: var(--color-red);
  }

  .filterTabReview {
    display: flex;
    height: 100%;
  }

  .filterTabReview ul {
    padding: 0;
    margin: 0;
    height: calc(100vh - 145px);
    overflow: auto;
  }

  .filterTabReview .tabsReview {
    flex-basis: 140px;
    border-right: var(--border-line);
    height: calc(100% - 89px);
    overflow: hidden auto;
  }

  .filterTabReview .tabsReview li {
    padding: 10px 16px;
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    position: relative;
  }

  .filterTabReview .tabsReview li.tab-link.current {
    font-weight: 500;
    background: #fafbfc;
  }

  .filterTabReview .tabsReview li.tab-link.current:after {
    content: "";
    position: absolute;
    width: 3px;
    height: 15px;
    border-radius: 3px;
    right: -2px;
    top: 50%;
    transform: translate(0, -50%);
    background: #c4c4c4;
  }

  .filterTabReview .tabsReview li.appliedFilter:before {
    content: "";
    position: absolute;
    width: 5px;
    height: 5px;
    background: #ff4e53;
    top: 50%;
    transform: translate(0, -50%);
    right: 5px;
    border-radius: 50%;
  }

  .filterContentDiv {
    flex-basis: calc(100% - 140px);
    background: #fafbfc;
    padding: 10px;
  }

  .filterContentDiv input[type=checkbox] {
    width: auto;
    display: inline-block;
    margin: 0;
    height: auto;
    width: 16px;
    height: 16px;
    padding: 0;
    vertical-align: middle;
  }

  .filterContentDiv label,
  .filterContentDiv li {
    color: #787878;
    line-height: 32px;
    font-size: 14px;
    padding-left: 6px;
    list-style-type: none;
  }

  .filterContentDiv label {
    line-height: 32px;
  }

  .filterOptionDiv {
    display: flex;
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 3;
  }

  .filterOptionDiv button {
    flex-basis: 50%;
    font-size: 14px;
    line-height: 20px;
    border: 1px solid var(--color-red);
    background-color: #fafbfc;
    padding: 11px;
    font-weight: var(--font-semibold);
    color: var(--color-red);
    text-align: center;
    background: var(--color-white);
  }

  .filterOptionDiv button.applyFilter {
    background: var(--color-red);
    color: var(--color-white);
  }

  .filterReviewSearch input[type=text] {
    margin-bottom: 6px;
  }

  .filterSidebarSection {
    display: none;
  }

  .mobileSortSection {
    background: rgba(0, 0, 0, 0.639215);
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
    display: none;
  }

  .mobileReviewSortDiv {
    position: fixed;
    height: auto;
    bottom: 0;
    left: 0;
    overflow: auto;
    border-radius: 4px 4px 0 0;
    width: 100%;
    background: var(--color-white);
    z-index: 3;
  }

  .mobileReviewSortDiv h2,
  .mobileReviewSortDiv .mobileSortHeading {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    border-bottom: var(--border-line);
    font-size: 15px;
    line-height: 24px;
    font-weight: 500;
  }

  .mobileReviewSortDiv ul {
    margin: 0;
    padding: 0 20px;
  }

  .mobileReviewSortDiv ul li {
    font-size: 14px;
    line-height: 24px;
    border-bottom: var(--border-line);
    display: flex;
    color: #787878;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }

  .mobileReviewSortDiv input[type=checkbox] {
    border: none;
    margin: 0;
  }

  .mobileReviewSortDiv input[type=checkbox]:checked {
    background: var(--color-white);
    border: none;
  }

  .mobileReviewSortDiv input[type=checkbox].inputChecked:checked:after,
  .mobileReviewSortDiv input[type=checkbox]:checked:after {
    background: var(--color-white);
    border-color: #20d086;
    width: 7px;
    height: 16px;
  }

  /* review-detail */

  .allReviews .col-md-3 {
    text-align: start;
    min-width: 100%;
    position: relative;
  }

  .allReviews .col-md-3 .verifiedSpan {
    position: absolute;
    top: 5px;
    right: 20px;
    margin-top: 0px;
  }

  .reviewCard .reviewerDetailCardHeader {
    /* padding-bottom: 20px; */
    position: relative;
  }

  .reviewCard .reviewerDetailCardHeader .reviewerIconCardHeader {
    position: absolute;
    left: 0px;
  }

  .reviewCard .reviewerDetailCardHeader .reviewerHeaderContent {
    margin-left: 50px;
    position: relative;
    padding-bottom: 20px;
  }

  .reviewCard .reviewerDetailCardHeader .reviewLandingHeader {
    padding-bottom: 0px !important;
  }

  .reviewCard .reviewerDetailCardHeader .starRating {
    position: absolute;
    display: flex !important;
    bottom: 0px;
    /* left: 48px; */
  }

  .reviewCard .reviewerDetailCardHeader .reviewLandingStarRating {
    position: static !important;
    /* display: flex !important; */
    font-size: 14px;
    color: #787878;
  }

  .reviewCard .reviewerDetailCardHeader .reviewerHeaderContent .reviewerNameCardHeader {
    display: block;
  }

  .moreReviews .headerMoreReviews h3 {
    font-size: 15px;
  }

  .allReviews .avgRating {
    font-size: 35px;
    display: inline;
  }

  .allReviews {
    padding: 0px;
    border: none;
  }

  .row.headerReviewContainer {
    margin-top: -157px;
  }

  .ratingList span {
    margin: 0px;
  }

  .reviewCard .ratingList .full-star,
  .reviewCard .ratingList .empty-star,
  .reviewCard .ratingList .half-star {
    transform: scale(0.6);
    margin: 0px -5px;
  }

  .ratingList {
    grid-template-columns: repeat(3, max-content) !important;
    grid-auto-rows: 1fr;
  }

  .reviewCard .ratingList div:nth-child(3n-1) {
    flex-basis: 36%;
  }

  .parentCollegeSection {
    margin-bottom: 10px;
    margin-top: 10px;
  }

  .sideBarSection {
    width: 100%;
  }

  .two-cardDisplay .sliderCardInfo .row div {
    flex-basis: 100%;
  }

  .allReviews .col-md-3 {
    text-align: start;
    min-width: 100%;
  }

  .allReviews .col-md-8 {
    margin-top: 10px;
    padding: 0px;
  }

  .allReviews .componentRatio .row {
    font-size: 15px;
    letter-spacing: 0.3px;
    color: #282828;
  }

  .allReviews .componentRatio .row p:first-child {
    display: inline;
    flex-shrink: 2;
    line-height: 24px;
  }

  .allReviews .componentRatio .row p:last-child {
    flex-basis: 60%;
    flex-grow: 1;
    flex-shrink: 0;
    text-align: end;
  }

  .allReviews .componentRatio .row .full-star,
  .allReviews .componentRatio .row .half-star,
  .allReviews .componentRatio .row .empty-star {
    height: 15px;
    width: 15px;
  }

  .allReviews .componentRatio .row .full-star {
    background-position: -616px -246px;
  }

  .allReviews .componentRatio .row .half-star {
    background-position: 99px -246px;
  }

  .reviewerHeaderText div {
    display: block !important;
  }

  .reviewerHeaderText>span::before {
    content: "" !important;
  }

  .sliderCardInfo.reviewGallery {
    min-width: 30% !important;
  }

  .reviewGallery img {
    align-self: auto !important;
    height: 108px !important;
  }

  .reviewGallery figure {
    height: 108px !important;
  }

  .four-cardDisplay .sliderCardInfo {
    min-width: 80%;
  }

  .reviewSubSection .subSectionHeader h3 {
    margin-bottom: 5px;
  }

  .review-details h3 {
    font-size: 15px;
  }

  .reviewerHeader .reviewerImage {
    max-width: 56px;
    aspect-ratio: 1/1;
  }

  .popupOtpSection .otpDiv {
    width: 95%;
    padding: 20px 10px;
  }

  .popupOtpSection .row p {
    text-align: center;
  }

  .optSectionHeader {
    margin-bottom: 20px;
  }

  .optSectionHeader img {
    width: 56px;
    height: 56px;
    margin-right: 10px;
  }

  .optSectionHeader .headingText {
    font-size: 16px;
    padding-bottom: 5px;
  }

  .optSectionHeader .sentOtp {
    font-size: 12px;
    line-height: 20px;
  }

  .ReviewOtpInputs input {
    margin-right: 16px;
  }

  .ReviewOtpInputs input:last-child {
    margin-right: 0px;
  }

  .two-cardDisplay .sliderCardInfo {
    padding: 10px;
    width: 270px;
    margin-right: 5px;
    white-space: normal;
    display: inline-block !important;
    vertical-align: middle;
    min-width: 80%;
  }

  .two-cardDisplay .sliderCardInfo .viewAllDiv {
    min-height: 162px;
  }

  .two-cardDisplay .sliderCardInfo .clgLogo {
    margin-bottom: 10px;
  }

  .customSlider .scrollLeft,
  .customSlider .scrollRight {
    display: none !important;
  }

  .pageFooter {
    padding-bottom: 0;
  }
}

/* review-detail */
@media (max-width: 768px) {
  /* .sideWidgets {
    display: none;
  } */

  .subSectionList li {
    flex-basis: 100%;
  }

  .buttonTwo {
    width: inherit;
  }

  .reviewerHeader .writeReviewButton {
    position: relative;
    width: 100%;
    bottom: 0px;
    right: 0px;
  }

  .reviewerImage {
    max-width: 56px !important;
  }

  .reviewerHeader .writeReviewButton {
    position: relative;
    width: 100%;
    bottom: 0px;
    right: 0px;
    margin-top: 10px;
  }

  .headerMoreReviews {
    padding: 10px !important;
    height: auto !important;
  }

  .headerMoreReviews h3 {
    padding: 10px !important;
  }
}