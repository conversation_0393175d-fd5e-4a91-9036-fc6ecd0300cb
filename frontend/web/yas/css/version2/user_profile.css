@charset "UTF-8";

.questionListIcon {
  background-position: -628px -931px;
  width: 11px;
  height: 16px;
  vertical-align: top;
}

.userProfilePage {
  max-width: 800px;
  margin: 0 auto;
}

.select2-container--default .select2-selection--single {
  border: 1px solid #d8d8d8 !important;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
  font-size: 14px !important;
  font-weight: normal !important;
  line-height: 1.71 !important;
  color: #787878 !important;
}

.select2-container .selection .select2-selection .select2-selection__arrow b {
  background-image: url("../../images/select-angle.png");
  background-color: transparent !important;
  background-size: contain !important;
  border: none !important;
  width: 9px !important;
  height: 6px !important;
  top: 14px !important;
  right: 16px !important;
  margin: 0 !important;
  left: unset !important;
  transform: scale(1.5) !important;
}

.select2-container .select2-selection--single {
  height: 36px !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 36px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 100% !important;
  right: -3px !important;
}

.select2-container .selection .select2-selection .select2-selection__arrow b {
  top: 50% !important;
  transform: translateY(-50%) scale(1.5) !important;
}

.myProfileHead {
  border-radius: 4px;
  background-color: #fff;
  padding: 20px;
  color: #282828;
  margin-bottom: 30px;
}

.myProfileHead h2 {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.33;
  color: #282828;
  margin-bottom: 20px;
}

.myProfileHead h3 {
  font-size: 15px;
  font-weight: 500;
  line-height: 1.6;
  margin-bottom: 10px;
}

.myProfileHead h2 {
  margin-bottom: 5px;
}

.progressDiv {
  display: flex;
  gap: 20px;
  align-items: baseline;
}

.progressDiv .progressBar {
  width: 100%;
  background: #d8d8d8;
  border-radius: 4px;
  height: 6px;
  position: relative;
  overflow: hidden;
}

.progressDiv .progressBarLine {
  background: #3ab54a;
  border-radius: 20px;
  width: 0%;
  height: 6px;
  display: block;
}

.progressDiv .progressPercent {
  font-size: 21px;
  font-weight: 500;
  line-height: 1.2;
}

.submitDetailButtonRow {
  margin-top: 20px;
  text-align: right;
}

.submitDetailButtonRow button {
  width: 125px;
  height: 40px;
  border-radius: 3px;
  background-color: #ff4e53;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.71;
  color: #fff;
  border: none;
}

.basicDetailSection {
  border-radius: 4px;
  background-color: #fff;
  padding: 20px;
  color: #282828;
  margin-bottom: 30px;
}

.basicDetailSection h2 {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.33;
  color: #282828;
  margin-bottom: 20px;
}

.basicDetailSection h3 {
  font-size: 15px;
  font-weight: 500;
  line-height: 1.6;
  margin-bottom: 10px;
}

.basicDetailForm {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.basicDetailForm .basicDetailField {
  max-width: 367px;
  flex-basis: 50%;
  position: relative;
}

.basicDetailForm .basicDetailField input,
.basicDetailForm .basicDetailField select {
  width: 100%;
  height: 36px;
  padding-left: 10px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  background-position-y: 15px !important;
  color: #282828;
  font-size: 14px;
}

.basicDetailForm .basicDetailField input::placeholder,
.basicDetailForm .basicDetailField select::placeholder {
  font-size: 14px;
  font-weight: normal;
  line-height: 1.71;
  color: #787878;
}

.contactDetail {
  display: flex;
}

.contactDetail .mobileContainerCodeDiv {
  position: relative;
  max-width: 105px;
  display: inline-block;
  height: 100%;
}

.contactDetail .mobileContainerCodeDiv .flagIcon {
  position: absolute;
  z-index: 1;
  top: 7px;
  left: 15px;
}

.contactDetail .mobileContainerCodeDiv .select2-selection {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.contactDetail .mobileContainerCodeDiv .select2-selection__rendered {
  padding-left: 45px !important;
  padding-right: 30px !important;
}

.contactDetail .mobileContainerCodeDiv .select2-selection__arrow b {
  right: 10px !important;
}

.contactDetail .inputContainerField {
  border-left: 0 !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  padding-left: 10px !important;
}

.contactDetail .select2-container {
  width: 100% !important;
}

.examDateField {
  flex-basis: 23%;
}

.examDateField .examScoreBox {
  width: 100%;
}

.dobDetail,
.examDateField {
  position: relative;
}

/* .dobDetail input[type=date]::-webkit-calendar-picker-indicator,
.examDateField input[type=date]::-webkit-calendar-picker-indicator {
  display: none;
} */

.dobDetail .calendarIcon,
.examDateField .calendarIcon {
  background: url("../../images/master_sprite.webp") no-repeat;
  background-position: -652px -958px;
  background-color: white;
  width: 30px;
  height: calc(100% - 4px);
  position: absolute;
  border-radius: 2px;
  top: 2px;
  right: 2px;
  z-index: 10;
  font-size: 20px;
  color: #333;
  cursor: pointer;
}

.examDateField .calendarIcon {
  right: 5px !important;
}

.courseDetailSection {
  border-radius: 4px;
  background-color: #fff;
  padding: 20px;
  color: #282828;
  margin-bottom: 30px;
}

.courseDetailSection h2 {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.33;
  color: #282828;
  margin-bottom: 20px;
}

.courseDetailSection h3 {
  font-size: 15px;
  font-weight: 500;
  line-height: 1.6;
  margin-bottom: 10px;
}

.selectCourseDiv,
.selectLocationDiv {
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  padding: 10px;
  margin-bottom: 20px;
}

.selectCourseDiv .selections,
.selectCourseDiv .noCourseSelections,
.selectLocationDiv .noCitySelections,
.selectLocationDiv .selections {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  gap: 2px;
  margin-bottom: 5px;
  flex-wrap: wrap;
}

.selectCourseDiv .selections p,
.selectLocationDiv .selections p,
.selectCourseDiv .noCourseSelections p,
.selectLocationDiv .noCitySelections p {
  font-size: 12px;
  font-weight: normal;
  font-style: italic;
  line-height: 2;
  color: #787878;
}

.selectCourseDiv .selections li,
.selectLocationDiv .selections li {
  padding: 3px 12px;
  border-radius: 24px;
  background-color: #ff4e53;
  height: 30px;
  font-size: 14px;
  font-weight: normal;
  color: #fff;
  display: flex;
  align-items: center;
}

.selectCourseDiv .selections li::after,
.selectLocationDiv .selections li::after {
  content: "";
  display: inline-block;
  background-image: url("../../images/master_sprite.webp");
  background-position: -124px -238px;
  width: 12px;
  height: 12px;
  margin-left: 8px;
  cursor: pointer;
}

.examDetailGrid {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr 1fr;
  margin-bottom: 20px;
}

.examDetailGrid .fullWidthContainer {
  grid-column-start: 1;
  grid-column-end: -1;
  max-width: unset !important;
  height: unset !important;
}

.examDetailGrid .scheduledExamContainer {
  display: flex;
  align-items: center;
}

.examDetailGrid .scheduledExamContainer .scheduledExamRow {
  display: flex;
}

.examDetailGrid .scheduledExamContainer .scheduledExamRow .scheduledExamOptionsLabel {
  font-size: 14px;
  font-weight: normal;
  line-height: 1.71;
  color: #282828;
  margin-left: 10px;
  margin-right: 16px;
  cursor: pointer;
  display: inline-block;
  position: relative;
  left: -4px;
}

.examDetailGrid .scheduledExamContainer .scheduledExamRow .scheduledExamRadio {
  margin: 0;
  accent-color: #ff4e53;
  width: 18px;
  height: 18px;
  vertical-align: middle;
}

.examDetailGrid .examScoreContainer .enterScorePrompt {
  font-size: 15px;
  font-weight: normal;
  line-height: 20px;
  color: #787878;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.examDetailGrid .examScoreContainer .enterScorePrompt::before {
  content: "•";
  color: #d8d8d8;
  display: inline-block;
  margin-right: 8px;
  font-size: 20px;
}

.examDetailGrid .examScoreContainer .examScoreBoxList {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.examDetailGrid .examScoreContainer .examScoreBoxList .examScoreBox {
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  max-width: 170px;
  height: 36px;
  padding: 12px 0px;
  padding-left: 16px;
}

.examDetailGrid .examScoreContainer .examScoreBoxList .examScoreBox::-webkit-outer-spin-button,
.examDetailGrid .examScoreContainer .examScoreBoxList .examScoreBox::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.examDetailGrid .examScoreContainer .examScoreBoxList .examScoreBox::placeholder {
  font-size: 13px;
  font-weight: normal;
  line-height: 1.85;
  color: #989898;
}

.examScoreContainer .container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.examScoreContainer .material-textfield {
  position: relative;
}

.examScoreContainer label {
  position: absolute;
  font-size: 14px;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: white;
  color: #989898;
  padding: 0 0.3rem;
  margin: 0 0.5rem;
  transition: 0.1s ease-out;
  transform-origin: left top;
  pointer-events: none;
}

.examScoreContainer input {
  font-size: 14px;
  outline: none;
  border: 1px solid #d8d8d8;
  border-radius: 5px;
  padding: 1rem 0.7rem;
  color: #282828;
  transition: 0.1s ease-out;
}

.examScoreContainer input:focus {
  border-color: #d8d8d8;
}

.examScoreContainer input:focus+label {
  color: #989898;
  top: 0;
  transform: translateY(-50%) scale(0.9) !important;
}

.examScoreContainer input:not(:placeholder-shown)+label {
  top: 0;
  transform: translateY(-50%) scale(0.9) !important;
}

.examScoreContainer input:not(:focus)::placeholder {
  opacity: 0;
}

.examScoreContainer input:focus::placeholder {
  opacity: 0;
}

.datepickerContainer {
  display: none;
}

.datepickerContainer .examScoreBoxList .examScoreBox {
  /* color: #989898; */
  padding-right: 8px;
}

.modalInputLabel {
  font-size: 15px;
  font-weight: normal;
  line-height: 1.33;
  color: #787878;
  display: inline-block;
  max-width: 300px;
}

.selectBudgetDiv {
  /* max-width: 367px; */
}

.educationDetailSection {
  border-radius: 4px;
  background-color: #fff;
  padding: 20px;
  color: #282828;
  margin-bottom: 30px;
  /* margin-bottom: 80px; */
}

.educationDetailSection h2 {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.33;
  color: #282828;
  margin-bottom: 20px;
}

.educationDetailSection h3 {
  font-size: 15px;
  font-weight: 500;
  line-height: 1.6;
  margin-bottom: 10px;
}

.educationDetailSection h3 {
  padding-bottom: 10px;
  margin-bottom: 0;
  border-bottom: 1px solid #d8d8d8;
}

.educationDetailList {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.educationDetailList li .detailItem {
  font-size: 14px;
  font-weight: normal;
  line-height: 1.71;
  color: #0966c2;
  padding: 10px 0;
  border-bottom: 1px solid #d8d8d8;
  cursor: pointer;
}

.educationDetailList li .detailItem::before {
  content: "+";
  display: inline-block;
  transform: scale(1.5);
  max-width: 12px;
  margin-right: 8px;
}

.detailContent {
  margin-top: 20px;
  position: relative;
  display: none;
  border-bottom: 1px solid #d8d8d8;
  padding-bottom: 20px;
}

.detailContent p {
  font-size: 15px;
  font-weight: 500;
  line-height: 1.6;
  color: #282828;
  margin-bottom: 10px;
}

.detailContent .detailContentGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.detailContent .detailContentGrid .contentBox {
  max-width: 367px;
  flex-basis: 50%;
}

.detailContent .detailContentGrid .contentBox input,
.detailContent .detailContentGrid .contentBox select {
  width: 100%;
  height: 36px;
  padding-left: 10px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  background-position-y: 15px !important;
  color: #787878;
  font-size: 14px;
}

.detailContent .detailContentGrid .contentBox input::placeholder,
.detailContent .detailContentGrid .contentBox select::placeholder {
  font-size: 14px;
  font-weight: normal;
  line-height: 1.71;
  color: #787878;
}

.detailContent .removeContent {
  color: #ff4e53;
  cursor: pointer;
  width: 14px;
  height: 20px;
  background-position: 591px -71px;
  transform: rotate(269deg);
}

/* .detailContent .removeContent::before {
  content: "-";
  display: inline-block;
  transform: scale(1.5);
  max-width: 12px;
  margin-right: 8px;
} */

.page-header {
  height: auto;
  margin-bottom: 30px;
}

/* .dobDetail input {
  padding-right: 15px;
} */

.examScoreContainer .examScoreBoxList .examScoreBox::-webkit-outer-spin-button,
.signupModal .examScoreContainer .examScoreBoxList .examScoreBox::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.examScoreContainer .examScoreBoxList .examScoreBox {
  -moz-appearance: textfield;
}

.select2-container--default .select2-search--inline .select2-search__field {
  font-size: 14px !important;
  font-weight: normal;
  line-height: 1.71;
  color: #787878;
  font-family: roboto !important;
  cursor: pointer;
}

.validationError,
.error {
  font-size: 12px !important;
  font-weight: normal !important;
  color: #ff4e53 !important;
  bottom: -17px;
  position: absolute;
}

.academicClasstenth .select2-container--default,
.academicClassDiploma .select2-container--default,
.academicClasstwelve .select2-container--default,
.graduateClass .select2-container--default,
.inputCourseGraduation .select2-container--default,
.postGraduate .select2-container--default,
.inputCoursePostGraduation .select2-container--default,
.yearClass .select2-container--default,
.specializationClass .select2-container--default {
  width: 100% !important;
}

/* .flatpickr-current-month .flatpickr-monthDropdown-months {
  background: transparent !important;
} */

.examScoreBox {
  background-color: #fff;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  font-size: 14px !important;
}

.selectedCourseValues .selections li {
  cursor: pointer;
}

.courseSelectedByUser,
.citySelectedByUser,
.removeClass {
  background-color: #e4e4e4;
  /* border: 1px solid #aaa; */
  border-radius: 4px;
  box-sizing: border-box;
  display: inline-block;
  /* margin-left: 5px; */
  margin-top: 5px;
  /* padding: 0px; */
  /* padding-left: 20px; */
  position: relative;
  max-width: 100%;
  /* overflow: hidden; */
  text-overflow: ellipsis;
  vertical-align: bottom;
  white-space: nowrap;
}

.selectCourseDiv .select2-container .select2-selection--multiple .select2-selection__rendered,
.selectLocationDiv .select2-container .select2-selection--multiple .select2-selection__rendered {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: inline-flex;
  /* gap: 10px; */
  /* margin-bottom: 10px; */
  flex-wrap: wrap;
}

.selectCourseDiv .select2-container--default .select2-selection--multiple .select2-selection__choice,
.selectLocationDiv .select2-container--default .select2-selection--multiple .select2-selection__choice {
  padding: 3px 12px;
  border-radius: 24px;
  background-color: #ff4e53;
  height: 30px;
  font-size: 14px;
  font-weight: normal;
  color: #fff;
  display: flex;
  align-items: center;
  position: relative;
  padding-right: 30px;
}

.selectCourseDiv .select2-container--default .select2-selection--multiple .select2-selection__choice__remove,
.selectLocationDiv .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  display: inline-block;
  background-image: url("../../images/master_sprite.webp");
  background-position: -124px -238px;
  width: 12px;
  height: 12px;
  margin-left: 8px;
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: unset;
  left: unset;
  border: none;
}

.selectCourseDiv .select2-container--default .select2-selection--multiple .select2-selection__choice__remove span,
.selectLocationDiv .select2-container--default .select2-selection--multiple .select2-selection__choice__remove span {
  visibility: hidden;
}

.selectCourseDiv .select2-container--default .select2-selection--multiple .select2-search--inline,
.selectLocationDiv .select2-container--default .select2-selection--multiple .select2-search--inline {
  display: inline-block;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  background-color: unset !important;
}

.pageMask {
  background: rgba(0, 0, 0, .5);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  overflow-y: hidden;
  z-index: 5
}

.userProfilePage .optSectionUser {
  display: block;
  background: var(--color-white);
  max-width: 560px;
  margin: 40px auto;
  position: fixed;
  border-radius: 4px;
  background: var(--color-white);
  padding: 25px;
  /* margin-top: 125px; */
  left: 50%;
  top: 50%;
  width: 100%;
  z-index: 7;
  overflow: auto;
  transform: translate(-50%, -50%);
}

.userProfilePage .optSectionUser .headingTextUser {
  font-size: 24px;
  line-height: 24px;
  padding-bottom: 10px;
  font-weight: var(--font-500);
  color: var(--primary-font-color);
  text-transform: uppercase;
}

.userProfilePage .optSectionUser .subHeadingUser {
  color: #787878;
  font-size: 16px;
  line-height: 28px;
  padding-bottom: 20px;
}

.userProfilePage .optSectionUser .otp-box-user {
  border: var(--border-line);
  outline: none;
  width: 100%;
  background: var(--color-white);
  min-height: 48px;
}

.optSectionUser .row .pb-0 {
  color: #787878;
  font-size: 16px;
  line-height: 28px;
  margin-left: 10px;
}

.numberInputsUser {
  padding-bottom: 30px
}

.numberInputsUser input {
  max-width: 80px;
  margin-right: 18px;
  border-radius: var(--border-line);
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  padding: 12px;
  color: #989898;
  padding-left: 12px
}

.numberInputsUser input:last-child {
  margin-right: 0
}

.optSection .row {
  margin: 0;
  -webkit-box-align: center;
  align-items: center
}

.dobDetail::after,
.examDateField::after {
  content: '';
  display: inline-block;
  background: url(../../images/master_sprite.webp) no-repeat;
  background-position: -652px -951px;
  width: 30px;
  height: calc(100% - 4px);
  position: absolute;
  right: 0px;
  pointer-events: none;
  cursor: pointer;
  top: -6px;
}

.closeLeadFormUser {
  position: absolute;
  right: 40px;
  top: 40px;
  width: 17px;
  height: 17px;
  background-position: 653px -334px;
  cursor: pointer;
}

#leadMobileOtpUser {
  color: #ff4e53;
}

p.accordianHeader {
  display: flex;
  justify-content: space-between;
}

.flatpickr-wrapper {
  display: block;
}

.dobDetail select.flatpickr-monthDropdown-months,
.examDateField select.flatpickr-monthDropdown-months,
.numInputWrapper select,
.examDateField .flatpickr-wrapper .numInputWrapper select {
  max-width: 120px;
  border: none !important;
  font-size: inherit !important;
  /* width: unset !important; */
  padding-left: 3px !important;
  background-position: 102% 16px !important;
  background: none;
  appearance: none !important;
}

.numInputWrapper select:focus-visible,
.examDateField .flatpickr-wrapper .numInputWrapper select:focus-visible {
  outline: none;
}

.examDateField .flatpickr-wrapper .numInputWrapper select {
  background-position: 101% 10px !important;
}

.examDateField select.flatpickr-monthDropdown-months {
  min-width: 113px;
  background-position: 97% 7px !important;
}

.dobDetail select.flatpickr-monthDropdown-months {
  background-position: 92% 16px !important;
}

.flatpickr-current-month {
  padding: 0px !important;
}

.dobDetail .dobUser,
.examDateField .examBooking {
  cursor: pointer;
}

.examScoreContainer .inputFocus:focus~label {
  color: #989898;
  top: 0;
  transform: translateY(-50%) scale(0.9) !important;
}

.examScoreContainer .inputFocus:not(:placeholder-shown)~label {
  top: 0;
  transform: translateY(-50%) scale(0.9) !important;
}

.contactDetail .countryCode.otherCountryCode {
  flex-basis: 101px;
  position: relative;
}

.contactDetail .dialCodeDiv {
  background: #eaeaea;
  border-radius: 4px 0 0 4px;
  border-right: var(--border-line);
  text-align: center;
  padding: 11px 12px;
  border: var(--border-line);
  outline: none;
  width: 101px;
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  max-width: 105px;
  background-color: #fff;
}

.domainExtention {
  height: 100%;
  /* border-left: 1px solid #d8d8d8; */
  padding-left: 7px;
  display: flex;
  align-items: center;
  /* z-index: 40; */
  /* background-color: white; */
  width: 90px;
  border: 1px solid #d8d8d8;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-left: 0px;
}

#formEmail {
  /* width: calc(100% - 90px); */
}

input:focus,
select:focus {
  outline: none;
}

.select2-container .select2-search--inline .select2-search__field {
  height: 20px !important;
}
@media (max-width: 1023px) {

  .validationError,
  .error {
    font-size: 11px !important;
    bottom: -15px;
  }

  .dateExam .examDateField:nth-child(even) .flatpickr-calendar.static {
    right: 26px;
  }

  .userProfilePage .optSectionUser {
    display: block;
    /* margin-top: 1px;
    padding-bottom: 12px; */
    max-width: 97%;
    margin: 0 auto;
    padding: 20px;
    /* position: relative; */
    border-radius: 4px;
    background: var(--color-white);
    width: 396px;
    /* height: 257px; */
    /* left: 6px; */
  }

  .userProfilePage .optSectionUser .headingTextUser {
    font-size: 16px;
    line-height: 24px;
    font-weight: var(--font-semibold);
    padding-bottom: 5px;
  }

  .selectCourseDiv .select2-container .select2-selection--multiple .select2-selection__rendered,
  .selectLocationDiv .select2-container .select2-selection--multiple .select2-selection__rendered {
    flex-wrap: wrap;
    gap: 0px;
  }

  .userProfilePage .optSectionUser .subHeadingUser {
    font-size: 12px;
    line-height: 20px;
  }

  .numberInputsUser input {
    max-width: 19.6%;
    padding-left: 8px;
    margin-right: 12px;
    border-radius: 4px;
  }

  .numberInputsUser {
    padding-bottom: 30px;
  }

  .verifyOtpUser {
    width: 100%;
    display: block;
    padding: 6px;
    margin-bottom: 20px;
  }

  .optSectionUser .row .pb-0 {
    font-size: 12px;
    line-height: 20px;
    color: #787878;
    text-align: center;
    margin-left: 70px;
  }

  .closeLeadFormUser {
    right: 21px;
    top: 19px
  }

  .selectCourseDiv .selections,
  .selectCourseDiv .noCourseSelections,
  .selectLocationDiv .noCitySelections,
  .selectLocationDiv .selections {
    gap: 0px;
  }

  .select2-container--default .select2-selection--single .select2-selection__rendered {
    font-size: 13px !important;
  }

  .blueBgDiv {
    display: none !important;
  }

  .pageFooter {
    padding-bottom: 0;
  }

  .select2-container--default .select2-selection--single .select2-selection__placeholder {
    font-size: 13px;
  }

  .submitDetailButtonRow {
    margin-top: 10px;
  }

  .myProfileHead {
    margin: 10px 0;
    padding: 10px;
  }

  .myProfileHead h2 {
    font-size: 18px;
    font-weight: 500;
    line-height: 1.33;
    margin-bottom: 0px;
  }

  .progressDiv {
    gap: 15px;
  }

  .progressDiv .progressPercent {
    font-size: 18px;
  }

  .basicDetailSection,
  .courseDetailSection,
  .educationDetailSection {
    padding: 10px;
    margin-bottom: 10px;
  }

  .basicDetailSection h3,
  .courseDetailSection h3,
  .educationDetailSection h3 {
    font-size: 14px;
  }

  .basicDetailForm {
    gap: 15px;
  }

  .basicDetailForm .basicDetailField {
    max-width: unset;
    flex-basis: 100%;
  }

  .basicDetailForm .basicDetailField input,
  .basicDetailForm .basicDetailField select {
    font-size: 13px;
  }

  .basicDetailForm .basicDetailField input::placeholder,
  .basicDetailForm .basicDetailField select::placeholder {
    font-size: 13px;
  }

  .selectCourseDiv,
  .selectLocationDiv {
    margin-bottom: 10px;
  }

  .examDetailGrid {
    margin-bottom: 10px;
    gap: 10px;
  }

  .examDetailGrid .scheduledExamText {
    grid-column-start: 1;
    grid-column-end: -1;
  }

  .examDetailGrid .scheduledExamText .modalInputLabel {
    max-width: unset;
    display: inline;
  }

  .examDetailGrid .examItem {
    display: flex;
  }

  .examDetailGrid .examScoreContainer .examScoreBoxList {
    gap: 10px;
  }

  .examDetailGrid .examScoreContainer .examScoreBoxList .examScoreBox {
    max-width: 100%;
  }

  .examDetailGrid .examScoreContainer .material-textfield {
    max-width: 48%;
  }

  .detailContent {
    margin: 10px 0;
  }

  .detailContent .detailContentGrid {
    gap: 10px;
  }

  .detailContent .detailContentGrid .contentBox {
    flex-basis: 100%;
    max-width: unset;
  }

  .detailContent p {
    font-size: 14px;
  }

  .page-header {
    margin-bottom: 10px;
  }

  .educationDetailSection {
    margin-bottom: 40px;
  }

  .contactDetail .mobileContainerCodeDiv .flagIcon {
    left: 10px;
  }

  .examDateField {
    flex-basis: 50%;
  }

  .examDateField input[type=date]::-webkit-datetime-edit {
    font-size: 10px;
  }
}