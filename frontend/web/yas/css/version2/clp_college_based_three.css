@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

h1,
h2,
h3,
h4,
h5 {
    padding: 0;
    margin: 0;
    font-weight: 600;
    line-height: normal;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

body,
p {
    font-family: "Roboto", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    font-variation-settings: "wdth"100;
    color: #000000;
    line-height: 1.5;
}

h1 {
    font-size: 50px;
    font-weight: 700;
}

h2 {
    font-size: 36px;
    font-weight: 600;
}

h2>span {
    font-size: 18px;
    color: #212529;
    font-weight: 400;
    line-height: 24px;
    margin-top: 10px;
    display: block;
}

.button-style {
    background: #F5A623;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.section-space {
    padding: 70px 0;
}

/* header */
.container {
    max-width: 1280px;
    width: 100%;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
}

header {
    padding: 20px 15px;
}

header.container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* banner */
.hero-banner {
    height: auto;
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-size: cover;
    padding: 40px 0;
    background-repeat: no-repeat;
    background-size: cover;
}

.hero-banner .banner-overlay {
    background: linear-gradient(90deg, rgba(5, 48, 97, 0.9) 0%, rgba(7, 70, 143, 0.8) 34%, rgba(26, 127, 202, 0) 66%);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.hero-banner .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hero-banner .banner-left-content {
    max-width: 390px;
    width: 100%;
    color: white;
}

.custom-description-template-liner p {
    color: white;
}

.hero-banner .banner-left-content h1 {
    font-size: 50px;
    line-height: 56px;
    font-weight: 700;
}

.hero-banner .banner-left-content .banner-liner {
    font-size: 20px;
    line-height: 24px;
    font-weight: 400;
    margin-top: 20px;
}

.banner-left-content ul {
    padding: 0;
    list-style: none;
    margin: 30px 0 0 0;
}

.banner-left-content ul .stream {
    width: 33px;
    height: 33px;
    background-color: #fff;
    border-radius: 5px;
    padding: 6px;
    display: flex;
    align-items: center;
    justify-content: center
}

.banner-left-content ul .stream img {
    width: 100%;
}

.banner-left-content ul li {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
}

.hero-banner .banner-right-content {
    max-width: 368px;
    width: 100%;
    background-color: white;
    padding: 20px 24px;
    border-radius: 4px;
    margin: 0;
    z-index: 1;
}

/* .hero-banner .banner-right-content .form-head {font-size: 20px; font-weight: 600; text-align: center;}
.hero-banner .form-body-block {margin-top: 20px;}
.hero-banner .form-body-block .banner-form-inner {position: relative; margin-bottom: 16px;}
.hero-banner .form-body-block .banner-form-inner > span {position: absolute; display: block; width: 100%; bottom: -17px; left: 0; font-size: 11px; color: #F8382A;}
.hero-banner .form-body-block input {border: 1px solid #ADB5BD; border-radius: 8px; width: 100%; font-size: 14px; font-weight: 400; line-height: 22px; padding: 13px 24px;}
.hero-banner .form-body-block input:focus{border-color: #F5A623;}
.hero-banner .form-body-block input:focus-visible {outline: none;}
.hero-banner .submit-btn {background-color: #EE424F; border-radius: 8px; width: 100%; margin-top: 10px; font-size: 18px; font-weight: 600; text-align: center; border: 0; padding: 10px; color: white; cursor: pointer;} */
.input-error {
    border-color: #F8382A !important;
}

/* Streams Offered */
.streams-offered {
    position: relative;
}

.streams-offered::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: url(/../yas/images/clp/college-based-two/bg-stream.png);
}

.streams-offered h2 {
    text-align: center;
}

.streams-offered-inner {
    display: flex;
    flex-wrap: wrap;
    column-gap: 15px;
    margin-top: 40px;
    height: 168px;
    overflow: hidden;
    transition: height 0.5s ease;
    justify-content: center;
}

.streams-offered-inner .detail-block {
    max-width: 405px;
    height: 160px;
    width: 100%;
    position: relative;
    border-radius: 20px;
    border: 2px solid transparent;
    background-image: linear-gradient(to right, #fffbf3, #e6f2ff);
    display: flex !important;
    align-items: center;
    justify-content: flex-start;
    border: 1px solid #D8D8D8;
    gap: 20px;
    padding: 40px;
    margin: 0 0 20px;
}

.streams-offered-inner.expanded {
    height: auto;
}

.text-center {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.view-toggle {
    cursor: pointer;
    user-select: none;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    color: #333;
}

.arrow {
    border: solid #fff;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    margin-left: 15px;
}

.down {
    transform: rotate(-135deg) translateY(-2px);
    -webkit-transform: rotate(-135deg) translateY(-2px);
}

.up {
    transform: rotate(45deg) translateY(-3px);
    -webkit-transform: rotate(45deg) translateY(-3px);
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box, linear-gradient(to bottom, #0A62C7, #F5A623) border-box;
}


.streams-offered-inner .detail-block::before {
    content: '';
    position: absolute;
    width: 50%;
    height: 95%;
    border-left: 6px solid #F5A623;
    border-top: 6px solid #F5A623;
    border-bottom: 6px solid #F5A623;
    left: 0;
    top: -2px;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
}

.detail-block .img-block {
    width: 66px;
    height: 66px;
}

.streams-offered-inner .detail-block .text-block {
    font-size: 24px;
    line-height: 24px;
    font-weight: 700;
    text-transform: uppercase;
    color: #212529;
}

.streams-offered .explore-now-btn {
    text-align: center;
    margin-top: 50px;
}

.streams-offered .explore-now-btn>a {
    display: inline-block;
}


/* our partner */
.slick-active:nth-child(3n) {
    border-bottom: 2px solid #F5A623 !important;
}

.top-recruiters-section h2 {
    text-align: center;
}

.top-recruiters-slider {
    margin-top: 40px;
    display: none;
}

.top-recruiters-slider.slick-slider .slick-track {
    display: flex;
    align-items: center;
}

.top-recruiters-slider .recruiters-logo-block {
    margin: 0 20px;
    border-bottom: 2px solid #D8D8D8;
    padding: 10px 20px;
}

.top-recruiters-slider .recruiters-logo-block img {
    width: 100%;
    height: 68px;
    object-fit: scale-down;
}

.top-recruiters-section .enquiry-now-button {
    text-align: center;
    margin-top: 70px;
}

.top-recruiters-slider.slick-slider .slick-dots {
    bottom: -40px;
}

.top-recruiters-slider.slick-slider .slick-dots li {
    width: 10px;
    height: 10px;
    list-style: none;
}

.top-recruiters-slider.slick-slider .slick-dots li>button {
    width: 10px;
    height: 10px;
    background-color: #D8D8D8;
    border-radius: 100%;
}

.top-recruiters-slider.slick-slider .slick-dots li.slick-active>button {
    background-color: #F5A623;
}

.top-recruiters-slider.slick-slider .slick-dots li>button::before {
    display: none;
}

.section-image .slick-slide img {
    display: block;
    width: 265px;
    height: 173px;
    position: absolute;
    border-radius: 12px;
    z-index: 1;
    inset: 0;
    margin: 70px auto 0
}

.our-campus-section {
    background: url(/../yas/images/clp/college-based-two/bg-campus.png) no-repeat center;
    padding-top: 70px;
    padding-bottom: 70px;
    background-size: cover;
    position: relative;
}

.campus-overlay {
    background-color: #094FA0E5;
    opacity: 0.9;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.our-campus-section h2 {
    text-align: center;
    color: #fff;
}

.our-campus-section h2>span {
    line-height: 24px;
    font-size: 18px;
    font-weight: 400;
    display: block;
    margin-top: 10px;
}

.our-campus-slider {
    margin-top: 50px;
    display: none;
}

.our-campus-slider.slick-slider {
    margin-bottom: 0;
}

.our-campus-slider.slick-slider .slick-list.draggable {
    padding-bottom: 70px;
}

.our-campus-slider.slick-slider .slick-dots {
    bottom: 20px;
}

.streams-offered-inner.slick-slider .slick-dots {
    bottom: -35px;
}

.our-campus-slider.slick-slider .slick-dots li,
.streams-offered-inner.slick-slider .slick-dots li {
    width: 10px;
    height: 10px;
    list-style: none;
}

.sstreams-offered-inner.slick-slider .slick-dots li>button,
.streams-offered-inner.slick-slider .slick-dots li.slick-active>button {
    border-radius: 50%;
}

.our-campus-slider.slick-slider .slick-dots li>button,
.streams-offered-inner.slick-slider .slick-dots li>button {
    width: 10px;
    height: 10px;
    background-color: #D8D8D8;
    border-radius: 100%;
}

.our-campus-slider.slick-slider .slick-dots li.slick-active>button,
.streams-offered-inner.slick-slider .slick-dots li.slick-active>button {
    background-color: #F5A623;
}

.our-campus-slider.slick-slider .slick-dots li>button::before,
.streams-offered-inner.slick-slider .slick-dots li>button::before {
    display: none;
}

.campus-card {
    width: 100%;
    position: relative;
    margin: 0 10px;
    border-radius: 12px;
    overflow: hidden;
    background-image: linear-gradient(#FFEED3, #BFD7F2);
    border: 1px solid #D8D8D8;
}

.campus-card:hover {
    background-image: linear-gradient(#F5A623, #0A62C7);
}

.campus-card:hover .campus-card-text {
    color: #fff;
}

.campus-card .campus-img {
    border-radius: 12px;
    overflow: hidden;
    width: 293px;
    height: 215px;
    border: 1px solid #FAD493;
}

.campus-card .campus-card-text {
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    padding: 5px 15px;
    color: #212529;
    border-radius: 100px;
    position: absolute;
    margin: 0 auto;
    max-width: 285px;
    width: 100%;
    top: 10px;
    left: 0;
    right: 0;
}

.location-img {
    width: 16px;
    height: 16px;
    position: relative;
    margin-right: 5px;
    display: inline-block;
}

.campus-card .campus-card-text img {
    width: 16px;
    height: 16px;
    position: relative;
    margin: 0;
    display: inline-block;
    vertical-align: middle;
    position: absolute;
    left: 0;
    top: 2px;
    filter: none
}

.our-campus-section .explore-life-button {
    text-align: center;
    margin-top: 10px;
}

.campus-card .whiteLocation {
    visibility: hidden;
}

.campus-card:hover .whiteLocation {
    visibility: visible;
}

.campus-card:hover .colorLocation {
    visibility: hidden;
}

/* About Jaipuria Institute of Management */
.about-insti-section {
    position: relative;
}

.about-insti-inner {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.about-insti-inner h2 {
    width: 100%;
    margin-bottom: 30px;
    text-align: center;
}

.about-insti-inner .about-insti-left {
    width: 53%;
    padding: 0 0 30px 0;
    color: #212529;
    display: flex;
    order: 2;
    flex-wrap: wrap;

}

.about-insti-inner .about-insti-left::-webkit-scrollbar {
    display: none;
}

.about-insti-inner .about-insti-left h2 {
    margin-bottom: 25px;
}

.about-insti-inner .about-insti-left>p {
    padding: 0;
    margin: 0 0 20px 0;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: #212529;
}

.about-insti-inner .about-insti-right {
    width: 43%;
    display: flex;
    order: 1;
    height: 400px;
}

.about-insti-inner .about-insti-right img {
    display: block;
    border-left: 0;
    width: 100%;
}

.arrow {
    border: solid #333;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    margin-left: 15px;
}

/* footer */
footer {
    background-color: #212529;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    line-height: 54px;
}

/* for modal */
.overflow-hide {
    overflow: hidden;
}

.modal-overlay-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1;
    display: none;
    align-items: center;
    overflow-y: auto;
}

.modal-body {
    padding: 0;
    margin: auto;
    height: auto;
    max-width: 370px;
    width: 100%;
    background: white;
    border-radius: 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, .4);
    position: relative;
    z-index: 300;
    overflow-x: auto;
    padding: 25px;
    border-radius: 5px;
}

.modal-body .modal-head {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    color: #1E1E1E;
}

.modal-body .modal-head>span {
    font-size: 16px;
    font-weight: 400;
    display: block;
}

.modal-body .form-content {
    margin-top: 20px;
}

.modal-body .form-content .form-field-block {
    position: relative;
}

.modal-body .form-content .form-field-block .select2-container {
    margin-bottom: 16px;
}

.modal-body .form-content .form-field-block .form-error {
    position: absolute;
    display: block;
    width: 100%;
    bottom: 0;
    left: 0;
    font-size: 11px;
    color: #F8382A;
}

.modal-body .form-content .field-style {
    border: 1px solid #ADB5BD;
    border-radius: 8px;
    width: 100%;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    padding: 10px 15px;
}

.modal-body .form-content .field-style:focus {
    border-color: #F5A623;
}

.modal-body .form-content .field-style:focus-visible {
    outline: none;
}

.modal-body .form-content .field-style.select-arrow {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url(/../yas/images/clp/college-based-two/select-arrow.png) no-repeat right 10px top 21px;
}

.modal-button {
    font-size: 18px;
    font-weight: 600;
    background-color: #F5A623;
    width: 100%;
    border-radius: 8px;
    padding: 10px 0;
    border: none;
    cursor: pointer;
    color: white;
    margin-top: 5px;
}

#modal-close {
    font-size: 28px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    line-height: 10px;
    font-weight: 300;
}

.modal-overlay-box.show-modal {
    display: flex;
}

.know-more-btn {
    width: 100%;
    text-align: center;
    margin-top: 50px;
}

.page-header,
.pageFooter,
.scrollToTop {
    display: none !important;
}

#genericScreenSubmit:disabled,
#modalScreenSubmit:disabled {
    background-color: rgba(202, 170, 25, 0.4);
}




.select2-container .select2-selection--single {
    height: 44px !important
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding: 8px 20px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 7px !important;
    right: 8px !important;
}

.select2-container--default .select2-selection--single {
    border-radius: 8px !important;
}

.custom-select-box {
    position: relative;
}

.clpCourse {
    background-color: #fff;
    ;
    position: relative;
    appearance: none;
}

.custom-select-box::after {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #888;
    right: 14px;
    top: 20px;
}

.form-field-block .select2 {
    margin-bottom: 15px;
    width: 100% !important;
}

.stick-button {
    padding: 15px 0;
}

.stick-button p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.button-blue {
    background-color: #0D3F64;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.whiteDownloadIcon,
.redDownloadIcon {
    width: 19px;
    height: 18px;
    background-position: 233px -354px !important;
    vertical-align: text-bottom;
    margin-right: 4px;
}

.spriteIcon {
    display: inline-block !important;
    background-image: url(../../images/master_sprite.webp);
    text-align: left;
    overflow: hidden;
}

.phoneIcon {
    width: 24px;
    height: 24px;
    background-position: 536px -246px;
    vertical-align: bottom;
    margin-right: 2px;
}

#sendCallerLeadToCld {
    display: none !important;
}

/*about show more*/
.show-more-content {
    height: 285px;
    overflow: hidden;
    transition: height 0.4s ease;
}

.show-more-content.expanded {
    height: 315px;
    overflow-y: scroll;
}

.show-more-content.expanded::-webkit-scrollbar {
    width: 5px;
}

/* Track */
.show-more-content.expanded::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
.show-more-content.expanded::-webkit-scrollbar-thumb {
    background: #888;
}

/* Handle on hover */
.show-more-content.expanded::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.toggle-btn {
    display: inline-block;
    margin-top: 10px;
    color: #333;
    cursor: pointer;
    font-weight: 500;
}

/* media start here */
@media only screen and (max-width:992px) {

    /*--sticky button--*/
    .stick-button span {
        display: none;
    }

    .stick-button {
        padding: 0;
    }

    .stick-button p {
        gap: 0;
    }

    .stick-button .button-style,
    .stick-button .button-blue {
        border-radius: 0;
        height: 45px;
        font-size: 14px;
    }

    .stick-button .button-style {
        width: 60%;
    }

    .stick-button .button-blue {
        width: calc(100% - 60%);
    }

    .hero-banner .container {
        column-gap: 40px;
    }

    .hero-banner .banner-left-content {
        max-width: 100%;
        width: calc(100% - 360px);
    }

    .hero-banner .banner-left-content h1 {
        font-size: 40px;
    }

    .hero-banner .banner-left-content .top-college>span {
        font-size: 24px;
        width: 100%;
        background-size: contain;
    }

    .hero-banner .banner-right-content {
        max-width: 100%;
        width: 333px;
    }

    .about-insti-inner {
        flex-direction: column;
    }

    .about-insti-inner .about-insti-left,
    .about-insti-inner .about-insti-right {
        width: 100%;
    }

    .about-insti-inner .about-insti-left {
        order: 2;
        border-top-left-radius: 0;
        border-bottom-right-radius: 30px;
    }

    .about-insti-inner .about-insti-right {
        order: 1;
        height: auto;
    }

    .college-usps-inner {
        flex-wrap: wrap;
        gap: 15px;
    }

    .college-usps-inner .usps-card {
        width: calc(50% - 9px)
    }
}

@media only screen and (max-width:767px) {
    header {
        padding: 10px 15px;
    }

    h2 {
        font-size: 30px;
    }

    .streams-offered-inner .detail-block {
        max-width: 100%;
    }

    .streams-offered-inner .detail-block .img-block img {
        width: 100%;
    }

    header>a>img:first-child {
        width: 148px;
        height: auto;
        display: block;
    }

    header>a>img:last-child {
        width: 133px;
        height: auto;
        display: block;
    }

    .hero-banner {
        align-items: flex-start;
        height: auto;
        padding: 10px 0;
    }

    .hero-banner .container {
        flex-direction: column;
    }

    .hero-banner .banner-left-content {
        width: 100%;
        padding: 40px 10px 0;
    }

    .hero-banner .banner-left-content h1 {
        text-align: center;
        font-size: 36px;
        line-height: 40px;
    }

    .hero-banner .banner-left-content .banner-liner {
        font-size: 18px;
        text-align: center;
        text-decoration: none;
    }

    .hero-banner .banner-right-content {
        width: 100%;
        margin: 40px 0;
    }

    .streams-offered-inner {
        flex-direction: column;
        gap: 20px;
    }

    .streams-offered-inner .detail-block .text-block {
        font-size: 20px;
    }

    .college-usps-section {
        padding: 40px 0;
    }

    .college-usps-inner .usps-card {
        width: 100%;
        height: 180px;
    }

    .our-campus-section {
        padding: 40px 0;
    }

    .our-campus-slider.slick-slider {
        margin-top: 20px;
    }

    .our-campus-slider .slick-list {
        padding: 0 10% 0 0;
    }

    .about-insti-inner .about-insti-left h2 {
        text-align: center;
        margin-bottom: 15px;
    }

    .about-insti-inner .about-insti-left {
        padding: 15px 0 0;
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px
    }

    .slick-active:nth-child(3n) {
        border-bottom: 2px solid #D8D8D8 !important;
    }

    .know-more-btn {
        margin-top: 30px;
    }

    .about-insti-inner .about-insti-left {
        height: auto;
        overflow-y: auto;
        padding-bottom: 30px;
    }

    .button-style {
        font-size: 16px;
    }

    .campus-card .campus-img {
        width: 310px;
    }

}