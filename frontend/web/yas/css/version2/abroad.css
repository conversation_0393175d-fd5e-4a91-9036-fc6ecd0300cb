#hnn-mainclass .nav>li.dropdown.open .dropdown-menu {
    display: table;
    border-radius: 0px;
    width: 100%;
    text-align: center;
    left: 0;
    right: 0;
    z-index: 9999;
}


.page-header {
    border: none;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    color: #333;
    line-height: 1.5em
}

.loadLeadModelNew {
    cursor: pointer !important
}

.needguide {
    background: transparent;
    border: 0px;
}

.carousel-wrapper .carousel-detail-wrapper {
    padding: 15px;
    position: relative
}

.carousel-wrapper hr {
    margin: 0
}

.carousel-wrapper .carousel-detail-wrapper .gmu-btn-green {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 15px
}

.font-bold {
    font-weight: 700
}

.padding-right-zero {
    padding-right: 0
}

.border_bottom {
    border-bottom: 1px solid #eee
}

.pointer {
    cursor: pointer
}

.text-left {
    text-align: left
}

.table>tbody>tr>td,
.table>tbody>tr>th,
.table>tfoot>tr>td,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>thead>tr>th {
    border-top: 1px solid #eee
}

table caption {
    padding: 5px;
    text-align: center;
    caption-side: bottom
}

.modal-dialog {
    z-index: 1050
}

.courses-list hr {
    margin-top: 5px;
    margin-bottom: 15px;
    border-top: 1px solid #f9f9f9
}

.hide_desktop_view {
    display: none
}

.info h4 {
    font-size: 14px
}

.review_more_link {
    padding-left: 70px
}

a {
    color: #4374b9
}

a:hover {
    color: #4374b9
}

.gmu_slide_menu_logo {
    border-radius: 50%;
    margin-bottom: 14px
}

.search_heading {
    position: absolute;
    top: 15px;
    font-size: 16px;
    font-weight: 700
}

.info-course-detail sup {
    top: 0 !important
}

.bottom_nav {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    font-size: 14px;
    font-weight: 700;
    color: #fff;
    z-index: 99
}

.bottom_nav div:first-child {
    padding: 7px 0;
    background: #4374b9
}

.bottom_nav div a {
    color: #fff
}

.bottom_nav div:last-child {
    padding: 7px 0;
    background: #ee424f
}

.bottom_nav .ivr_call {
    position: fixed;
    bottom: 10px;
    left: 0;
    right: 0;
    margin: auto;
    height: 50px;
    width: 50px;
    z-index: 10;
    border-radius: 50%
}

.mobile_view {
    display: none
}

.gmu-loader {
    position: absolute;
    height: calc(100% - 30px);
    width: calc(100% - 30px);
    display: none;
    background-color: #fff;
    z-index: 9999
}

.cssload-container {
    position: relative
}

.cssload-container {
    top: calc(50% - 20px)
}

#lead-modal-right-div .cssload-container {
    top: -50px
}

.cssload-whirlpool,
.cssload-whirlpool::after,
.cssload-whirlpool::before {
    position: absolute;
    top: 50%;
    left: 50%;
    border: 1px solid #0d3c63;
    border-left-color: #fc3d4d;
    border-radius: 824px;
    -o-border-radius: 824px;
    -ms-border-radius: 824px;
    -webkit-border-radius: 824px;
    -moz-border-radius: 824px
}

.cssload-whirlpool {
    margin: -21px 0 0 -21px;
    height: 41px;
    width: 41px;
    animation: cssload-rotate 1.75s linear infinite;
    -o-animation: cssload-rotate 1.75s linear infinite;
    -ms-animation: cssload-rotate 1.75s linear infinite;
    -webkit-animation: cssload-rotate 1.75s linear infinite;
    -moz-animation: cssload-rotate 1.75s linear infinite
}

.cssload-whirlpool::before {
    content: "";
    margin: -19px 0 0 -19px;
    height: 36px;
    width: 36px;
    animation: cssload-rotate 1.75s linear infinite;
    -o-animation: cssload-rotate 1.75s linear infinite;
    -ms-animation: cssload-rotate 1.75s linear infinite;
    -webkit-animation: cssload-rotate 1.75s linear infinite;
    -moz-animation: cssload-rotate 1.75s linear infinite
}

.cssload-whirlpool::after {
    content: "";
    margin: -24px 0 0 -24px;
    height: 46px;
    width: 46px;
    animation: cssload-rotate 3.5s linear infinite;
    -o-animation: cssload-rotate 3.5s linear infinite;
    -ms-animation: cssload-rotate 3.5s linear infinite;
    -webkit-animation: cssload-rotate 3.5s linear infinite;
    -moz-animation: cssload-rotate 3.5s linear infinite
}

@keyframes cssload-rotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-webkit-keyframes cssload-rotate {
    100% {
        -webkit-transform: rotate(360deg)
    }
}

.lead-model-content-wrapper {
    z-index: 9999 !important;
    margin: -1px -15px
}

.grey-background {
    background: #f4f4f4
}

.white-background {
    background: #f6f6f6
}

.no-margin {
    margin: 0 !important
}

.no-margin-bottom {
    margin-bottom: 0 !important
}

.materialbox {
    -webkit-box-shadow: 0 1px 4px 1px rgba(0, 0, 0, .3);
    box-shadow: 0 1px 4px 1px rgba(0, 0, 0, .3);
    border-radius: 6px;
    background: #fff;
    padding: 10px;
    margin: 20px 0
}

.search-page-logo {
    width: 60px;
    height: 60px
}

.search-page-sponsored {
    font-size: 10px;
    color: #fff;
    background: orange;
    padding: 2px 6px;
    margin: 0 5px
}

.slider-menu,
.slider-submenu {
    top: 0;
    display: none;
    height: 101vh;
    width: 80%;
    background: #f9f9f9;
    position: fixed;
    z-index: 9999;
    border-right: #fff
}

.slider-submenu {
    top: 0;
    z-index: 99999;
    overflow: auto
}

.close-submenu {
    float: left;
    margin: 2px 10px 0 0;
    font-size: 18px;
    color: #444
}

.form-control:focus {
    border-color: #ccc;
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.form-control {
    border-radius: 0
}

.no-padding {
    padding: 0 !important
}

.heading-design {
    background-image: -webkit-linear-gradient(45deg, #4374b9, #ee424f, #ee424f, #ee424f, #ee424f);
    background-image: -o-linear-gradient(45deg, #4374b9, #ee424f, #ee424f, #ee424f, #ee424f);
    background-image: linear-gradient(45deg, #4374b9, #ee424f, #ee424f, #ee424f, #ee424f);
    color: transparent !important;
    -webkit-background-clip: text;
    background-clip: text;
    font-weight: 700
}

.pre_next_btn {
    background: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background: linear-gradient(45deg, #4374b9, #ee424f);
    border-radius: 16px;
    color: #fff;
    border: 0;
    padding: 6px 20px;
    float: none !important
}

.pre_next_btn:hover {
    color: #fff !important
}

.pre_next_text {
    background-image: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background-image: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background-image: linear-gradient(45deg, #4374b9, #ee424f);
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    font-weight: 700;
    border-radius: 50%;
    border: 1px solid #b087b796;
    padding-top: 7px
}

#pagination-div .active {
    color: #fff;
    background: -webkit-linear-gradient(45deg, #4374b9, #ee424f) !important;
    background: -o-linear-gradient(45deg, #4374b9, #ee424f) !important;
    background: linear-gradient(45deg, #4374b9, #ee424f) !important
}

.pagination_btn_all {
    background-image: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background-image: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background-image: linear-gradient(45deg, #4374b9, #ee424f);
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    font-weight: 700;
    border: 1px solid #b087b796
}

.pagination_btn_digit>a,
a.pagination_btn_digit {
    border-radius: 50%
}

.pagination_btn_other>a {
    border-radius: 16px;
    padding: 6px 20px
}

.pagination_btn_first>a {
    float: left
}

.pagination_btn_last>a {
    float: right
}

.pagination_ul {
    padding: 0 !important
}

.pagination_ul>li {
    list-style-type: none;
    list-style-image: none;
    display: unset
}

.navbar-toggle {
    border: none !important
}

.navbar-logo {
    margin-top: -5px;
    width: 140px
}

.navbar-searchbox {
    min-width: 300px;
    border-radius: 0;
    border: 1px solid #ddd;

}

.navbar-default {
    background-repeat: repeat-x;
    background: 0 0;
    border: #fff;
    -webkit-box-shadow: none;
    box-shadow: none
}

#whiteheader-mob-logo {
    display: none
}

#whiteheader .navbar-toggle .icon-bar,
.navbar-default .navbar-toggle .icon-bar {
    display: block;
    background-color: #fff;
    width: 30px;
    height: 2px;
    margin: 7px 0;
    border-radius: 0;
    -webkit-transform: rotate(0);
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
    -webkit-transition: .25s ease-in-out;
    -moz-transition: .25s ease-in-out;
    -o-transition: .25s ease-in-out;
    transition: .25s ease-in-out
}

.navbar-default .navbar-toggle,
.navbar-default .navbar-toggle:focus,
.navbar-default .navbar-toggle:hover {
    border-color: #fff;
    background: 0 0;
    float: left;
    margin: 0;
    z-index: 1040
}

#whiteheader .navbar-toggle span:nth-child(3),
.navbar-default .navbar-toggle span:nth-child(3) {
    width: 20px
}

.navbar-default .navbar-toggle {
    -webkit-transform: rotate(0);
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
    -webkit-transition: .5s ease-in-out;
    -moz-transition: .5s ease-in-out;
    -o-transition: .5s ease-in-out;
    transition: .5s ease-in-out;
    cursor: pointer
}

.nav-search-logo {
    content: "\f002";
    width: 45px;
    float: right;
    padding: 9px;
    color: #fff;
    font-size: 16px;
    background: #4374b9;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center
}

#whiteheader li a,
.navbar-fixed-top li a {
    font-size: 13px;
    color: #fff !important;
    font-size: 15px;
    font-weight: 700
}

.navbar-custom .navbar-nav>li>a:focus,
.navbar-custom .navbar-nav>li>a:hover {
    background: 0 0;
    color: #4374b9
}

.nav>li>a:focus,
.nav>li>a:hover {
    background: 0 0
}

.navbar-custom .navbar-nav>li>a:hover {
    background-color: none;
    color: #4374b9
}

.navbar-right li:hover {
    background: 0 0;
    color: #777
}

.interlink-navbar {
    min-height: 34px;
    margin-bottom: 0 !important;
    background: #fff
}

.interlink-navbar li a {
    font-size: 14px;
    color: #4374b9 !important
}

.main-navbar-tab-li {
    cursor: pointer
}

.main-navbar-tab-li>a {
    padding: 7px !important
}

.top-menu .mega_menu {
    margin-bottom: 20px
}

.top-menu .main-dropdown {
    border: none;
    padding: 0;
    position: absolute;
    top: 80%;
    left: 0;
    z-index: 100;
    background: #fff;
    border-top: none;
    -webkit-box-shadow: 0 -5px 20px 1px rgba(0, 0, 0, .08);
    box-shadow: 0 -5px 20px 1px rgba(0, 0, 0, .08)
}

.top-menu .main-dropdown .disc-list-wrapper .discipline-list-div ul.nav-tabs {
    border-bottom: none;
    width: 180px;
    background: #f3f3f3;
    float: left;
    display: inline-block;
    margin-top: 0
}

.top-menu .main-dropdown .disc-list-wrapper .discipline-list-div ul.nav-tabs li {
    font-size: 12px;
    line-height: 16px;
    font-weight: 900;
    text-transform: uppercase;
    display: block;
    float: none;
    text-align: left !important;
    margin: 0;
    max-height: 52px
}

.top-menu .main-dropdown .disc-list-wrapper .discipline-list-div ul.nav-tabs li.active a {
    background: #3d8ff2 !important;
    color: #fff !important;
    -webkit-box-shadow: -10px 0 10px 0 rgba(0, 0, 0, .1);
    box-shadow: -10px 0 10px 0 rgba(0, 0, 0, .1)
}

.top-menu .main-dropdown .disc-list-wrapper .discipline-list-div ul.nav-tabs li a {
    text-decoration: none;
    color: #fff !important;
    padding: 8px 5px 8px 8px !important;
    border-bottom: 1px solid #fff;
    border-radius: 0;
    margin: 0;
    text-align: left !important;
    font-size: 12px !important;
    letter-spacing: .1px !important;
    position: relative;
    cursor: pointer;
    background: #2c415e
}

.top-menu .main-dropdown .disc-list-wrapper .discipline-list-div ul.nav-tabs li a:hover {
    background: #3d8ff2 !important;
    color: #fff !important;
    -webkit-box-shadow: -10px 0 10px 0 rgba(0, 0, 0, .1);
    box-shadow: -10px 0 10px 0 rgba(0, 0, 0, .1)
}

.menu_heading {
    font-size: 18px;
    color: #fc3d4c;
    font-weight: 900;
    display: inline-block;
    text-transform: uppercase;
    overflow: hidden;
    line-height: 42px;
    margin-top: 0;
    margin-bottom: -5px
}

.sub_heading {
    font-size: 12px;
    letter-spacing: .3px;
    color: #a7a6a6;
    font-weight: 700;
    margin-bottom: 10px;
    margin-top: 15px;
    display: block;
    text-transform: uppercase
}

.top-menu .main-dropdown .disc-list-wrapper .tab-content .menu_list .list_wrap .all_list ul {
    padding: 0 !important
}

.top-menu .main-dropdown .disc-list-wrapper .tab-content .menu_list .list_wrap .all_list ul li {
    display: block;
    margin-bottom: 7px
}

.top-menu .main-dropdown .disc-list-wrapper .tab-content .menu_list .list_wrap .all_list ul li a {
    font-size: 12px !important;
    line-height: 16px !important;
    letter-spacing: .3px;
    color: #3a2f2f !important;
    padding: 0 !important;
    cursor: pointer
}

.top-menu .main-dropdown .disc-list-wrapper .tab-content .menu_list .list_wrap .all_list ul li:hover {
    background-color: #fff !important
}

.top-menu .main-dropdown .disc-list-wrapper .tab-content .tab-pane {
    position: relative
}

.div-border-right {
    border-right: 1px solid #eee
}

.all-a {
    text-decoration: underline;
    font-style: italic
}

.bottom_nav.mobile_view .col-xs-6:first-child {
    padding: 7px 0;
    background: #4374b9;
}

.bottom_nav.mobile_view .col-xs-6:last-child {
    padding: 7px 0;
    background: #ee424f;
}

@media (max-width: 991px) {
    .pageFooter {
        padding-bottom: 38px !important;
    }

    .closeMenu {
        display: none;
        background: url("https://www.getmyuni.com/yas/images/master_sprite.webp");
        text-align: left;
        overflow: hidden;
        position: fixed;
        right: 6%;
        top: 7px;
        width: 40px;
        height: 40px;
        background-position: 423px -118px;

    }

    .slider-menu,
    .slider-submenu {
        overflow: auto;
        display: block;
        width: 0%;
        left: 0;
        transition: 0.15s ease;
    }

    #hnn-mainclass .fix_navbar_style {
        background: transparent;
        z-index: 20;
        -webkit-box-shadow: none;
        box-shadow: none;
        text-align: center;
    }

    #hnn-mainclass #search-button {
        background: transparent;
        font-size: 24px;
        padding: 14px 10px;
        float: right;
        margin: 0px;
        border: 0px;
        color: white;
        display: block;
        z-index: 999;
    }
}

@media (min-width: 768px) {
.blueBgDiv .mobileOnly {
        display: none;
    }

    .new-navbar-right {
        float: right !important;
        margin-right: -15px
    }

    .new-navbar-right~.new-navbar-right {
        margin-right: 0
    }

    .new-navbar-right .dropdown-menu {
        right: 0;
        left: auto
    }

    .new-navbar-right .dropdown-menu-left {
        right: auto;
        left: 0
    }
}

.write-review {
    color: #fff !important;
    background: #4374b9;
    border-radius: 3px;
    padding: 7px 20px 7px 20px !important;
    margin: 8px 10px 8px 10px
}

.write-review:hover {
    background: #fff !important;
    color: #4374b9 !important
}

.close,
.close:focus,
.close:hover {
    opacity: unset
}

.slider-menu .close {
    font-weight: 700;
    font-size: 24px;
    float: left;
    padding: 20px;
    color: #ccc
}

.slider-menu .nav_close {
    font-weight: 700;
    font-size: 24px;
    float: left;
    padding: 20px;
    color: #ccc
}

.slider-menu>img {
    padding: 20px;
    float: left;
    width: 60px
}

.slider-menu li .fa-angle-right {
    float: right;
    font-size: 25px;
    color: #444
}

.angle-right {
    float: right;
    width: 15px;
    height: 15px;
    margin-top: 3px
}

.top-clearfix {
    padding-top: 100px
}

.slide-top-clearfix {
    padding-top: 40px
}

.slider-submenu .sub-menu-div p {
    padding: 12px 20px;
    text-align: left;
    font-weight: 700;
    color: #fff;
    margin: 0
}

.slider-submenu .sub-menu-div {
    background: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background: linear-gradient(45deg, #4374b9, #ee424f)
}

.slider-menu-option ul {
    list-style-type: none;
    padding: 10px 20px;
    text-align: left;
    list-style-image: none
}

.slider-menu-option ul li {
    border-bottom: 1px solid #ddd
}

.slider-menu-option ul li a {
    font-weight: 700;
    color: #444;
    font-size: 16px;
    display: block;
    padding: 20px 0;
    text-decoration: none
}

#login-signup {
    border-bottom: 0
}

#login-signup a {
    background-image: -webkit-linear-gradient(45deg, #4374b9, #ee424f, #ee424f);
    background-image: -o-linear-gradient(45deg, #4374b9, #ee424f, #ee424f);
    background-image: linear-gradient(45deg, #4374b9, #ee424f, #ee424f);
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    font-weight: 700;
    font-size: 20px
}

.user-info ul li {
    border: 0 !important;
    padding: 0
}

.user-info ul li:nth-child(2) a {
    font-weight: unset !important;
    padding: 5px 0
}

.blur-background {
    top: 0;
    display: none;
    position: fixed;
    height: 100%;
    width: 100%;
    background: #000000ad;
    z-index: 9990
}

.navbar-default .navbar-nav>.active>a,
.navbar-default .navbar-nav>.open>a {
    background: 0 0 !important
}

.user_menu {
    background-color: #fff;
    color: #4374b9;
    padding: 0
}

.user_menu li a {
    color: #4374b9 !important;
    padding: 9px 20px;
    font-size: 14px
}

.user_menu .divider {
    margin: 0 !important
}

.user_menu>li>a:focus,
.user_menu>li>a:hover {
    background: #4374b9 !important;
    color: #fff !important
}

.form-control-feedback {
    background: #4374b9;
    padding: 10px
}

.modal-backdrop.in {
    z-index: 1040 !important
}

#cap_logo {
    height: 330px;
    position: absolute;
    left: 25%;
    top: 115px
}

.banner_with_background {
    background: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background: linear-gradient(45deg, #4374b9, #ee424f);
    width: 100%;
    min-height: 410px
}

.banner_with_background h1 {
    font-size: 44px;
    margin: 5px 0
}

.course-name-text-box {
    width: 100%;
    border: medium none;
    line-height: 34px;
    outline: 0;
    height: 40px;
    text-align: left;
    margin-top: 20px;
    font-size: 14px;
    text-indent: 10px;
    z-index: 1
}

.banner-search {
    position: absolute;
    top: calc(50% + 150px)
}

.banner-search>h1 {
    display: inline;
    color: #fff;
    font-weight: 700;
    font-size: 30px
}

.banner-search>h2 {
    color: #fff;
    margin-top: 10px;
    font-size: 16px;
    line-height: 24px
}

.main-body {
    margin-top: 30px
}

.banner {
    min-height: auto
}

.banner h1 {
    color: #fff;
    width: 100%;
    font-size: 21px;
    margin-top: 25px;
    padding-top: 80px;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: 28px
}

.banner p {
    color: #fff;
    font-size: 14px;
    margin-bottom: 46px
}

.banner p span {
    font-weight: 700
}

.panel {
    margin-bottom: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 0;
    border: 0
}

#accordion1 {
    background: #fff;
    font-weight: 700
}

#accordion1 .fa-angle-down,
#accordion1 .fa-angle-right {
    color: #1075b9;
    font-size: 25px;
    position: absolute;
    right: 20px;
    top: 15px
}

#accordion1 ul {
    list-style-type: none;
    padding: 0 15px;
    text-decoration: none;
    font-weight: 400;
    color: #29abe2
}

#accordion1 ul li {
    padding: 10px 0
}

#accordion1>li>a {
    border-left: 2.5px solid #fff
}

#accordion1>ul li a:hover {
    text-decoration: none;
    list-style-type: none;
    color: #4374b9;
    font-weight: 400
}

#accordion1 ul li:hover {
    background: #fff !important
}

#about_sub_category li a {
    color: #555
}

#accordion1>li>a:hover {
    text-decoration: none;
    background-color: #e9f3f7
}

#accordion1>li>a:focus {
    text-decoration: none
}

#accordion1>li+li {
    margin-top: 0
}

#accordion1>li>a {
    padding: 15px 15px
}

.lg-button {
    padding: 10px 15px;
    background: #4374b9;
    color: #fff;
    text-align: center;
    cursor: pointer;
    font-weight: 700;
    text-decoration: none
}

.lg-button a {
    color: #fff;
    font-weight: 700;
    text-decoration: none
}

.read-more-div {
    max-height: 300px !important;
    overflow: hidden
}

.read-more-div.double-height {
    max-height: 200vh !important
}

.read-more-div {
    position: relative
}

.read-more-div:after {
    background: -webkit-gradient(linear, left top, left bottom, color-stop(5%, hsla(0, 0%, 100%, 0)), to(#fff));
    background: -webkit-linear-gradient(hsla(0, 0%, 100%, 0) 5%, #fff);
    background: -o-linear-gradient(hsla(0, 0%, 100%, 0) 5%, #fff);
    background: linear-gradient(hsla(0, 0%, 100%, 0) 5%, #fff);
    position: absolute;
    bottom: 0;
    display: block;
    height: 80px;
    width: 100%
}

.read-more-btn,
.read-more-button {
    cursor: pointer;
    color: #fff;
    display: inline-block;
    border-radius: 40px;
    font-weight: 700 !important;
    color: #ee424f;
    margin-top: 15px
}

.read-more-btn:focus,
.read-more-btn:hover,
.read-more-button:focus,
.read-more-button:hover {
    color: #ee424f
}

.read-more-btn i,
.read-more-button i {
    padding-right: 5px;
    font-size: 12px
}

.arrow_icon {
    width: 10px;
    margin-left: 4px;
    margin-top: -2px
}

#left-fix .navbar {
    margin-bottom: 15px
}

.info-card {
    padding: 15px;
    background: #fff;
    margin-bottom: 15px;
    overflow: hidden;
    border-radius: 0
}

.info-card p.updated_wrapper {
    margin: 0
}

.breadcrumb-info-card {
    padding: 10px 15px
}

.breadcrumb-info-card p {
    margin: 0
}

.info-card a {
    text-decoration: none
}

.info-card h2,
.info-card h3,
.info-heading-label,
.info-heading-link,
.mce-content-body h2,
.mce-content-body h3,
.review-h2 {
    color: #4374b9;
    font-weight: 700 !important;
    font-size: 16px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 15px
}

.info-card h3,
.mce-content-body h3 {
    font-size: 15px
}

.info-heading-label {
    font-size: 16px
}

.info-heading-link {
    font-size: 14px
}

.info-heading-link:hover {
    font-size: 14px;
    color: #4374b9
}

.info-card h4,
.mce-content-body h4 {
    background: #f8f8f8;
    padding: 8px 15px;
    border-left: 5px solid #4374b9;
    font-size: 14px;
    font-weight: 700;
    text-align: left !important;
    color: #4374b9;
    font-weight: 700;
    margin: 10px 0
}

.card-title h2,
.card-title h3 {
    color: #4374b9;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 8px
}

.info-card .card-btn-link {
    margin: 0 -3px
}

.card-btn-link a,
.info-card .card-btn-link a {
    color: #333;
    border: 1px solid #ddd;
    padding: 5px 10px;
    border-radius: 0;
    font-size: 13px;
    margin: 3px
}

.card-btn-link a:hover,
.info-card .card-btn-link a:hover {
    color: #fff;
    background: #4374b9
}

.info-card .card-title-link {
    background: #4374b9;
    color: #fff !important;
    font-weight: 700;
    border: 1px solid #fff !important;
    display: inline-block;
    padding: 5px 10px;
    border-radius: 0;
    margin: 3px;
    font-size: 13px
}

.card-table td:not(:first-child) {
    padding-left: 10px
}

.info-card table,
.mce-content-body table {
    border: 1px solid #eee !important;
    width: 100% !important;
    margin: 15px 0
}

.info-card table td,
.mce-content-body table td {
    border: 1px solid #eee;
    font-size: 14px;
    padding: 8px 20px
}

.info-card table th,
.mce-content-body table th {
    padding: 8px 20px;
    font-size: 14px;
    color: #fff;
    background: #4374b9
}

.info-card table thead tr th {
    vertical-align: middle !important
}

.info-card table thead tr,
.mce-content-body table thead tr {
    color: #fff;
    background: #4374b9;
    font-size: 14px;
    padding: 0;
    font-weight: 700
}

.info-card table thead tr td .mce-content-body table thead tr td {
    padding: 8px
}

.info-card table thead tr td p,
.mce-content-body table thead tr td p {
    margin-bottom: 0
}

.panel-body {
    padding: 0
}

.right-card {
    padding: 10px 5px;
    background: #fff;
    text-align: center;
    font-size: 16px;
    color: #333;
    font-weight: 700
}

.right-card button {
    background: #00c853;
    color: #fff;
    padding: 9px 14px;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    border-radius: 30px;
    font-size: 12px;
    margin: 20px 0 0 5px;
    border: none;
    line-height: normal
}

.right-card button:last-child {
    background: #ffc000;
    padding: 9px 24px;
    margin: 20px 5px 10px 5px
}

.right-card a:hover {
    text-decoration: none;
    cursor: pointer
}

.right-card>svg {
    border-radius: 50%;
    height: 75px;
    width: 75px;
    margin: 10px
}

.suggestion-list {
    background: #fff;
    margin-top: 20px;
    height: auto
}

.suggestion-list .list-title {
    color: #fff;
    background: #4374b9;
    font-weight: 700;
    padding: 10px 15px;
    text-align: center;
    font-size: 16px
}

.suggestion-list .interested-list a {
    text-decoration: none;
    color: #333;
    font-size: 13px;
    display: block;
    padding: 10px 0;
    border-bottom: 1px solid #efefef
}

.interested-list {
    margin: 0 20px
}

.clg-card {
    text-align: center;
    border: 1px solid #efefef;
    padding: 20px 7px 0;
    cursor: pointer;
    border-radius: 6px;
    width: calc(25% - 8px);
    margin: 0 4px;
    float: left;
    height: 150px
}

.clg-card img {
    width: 70px !important;
    height: 70px !important;
    margin: 0 auto
}

.clg-card a {
    color: #333;
    font-size: 13px
}

.info-card .view-all {
    padding-top: 15px
}

.info-card .view-all a {
    color: #ee424f
}

.info-card-bootstrap {
    display: table
}

.inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex
}

.info-card-bootstrap .inline-flex div:first-child {
    margin-right: 15px
}

.info-card-bootstrap .inline-flex div:last-child a {
    color: #333;
    font-size: 12px
}

.info-card-bootstrap .inline-flex {
    padding: 5px;
    border-bottom: 1px solid #efefef
}

.india_map {
    margin: 0 auto 20px;
    width: 100%;
    height: 600px
}

.fee-structure-card>div:first-child {
    width: 20%;
    text-align: center
}

.fee-structure-card>div:first-child div {
    margin-bottom: 15px
}

.fee-structure-card>div:last-child {
    width: 80%;
    float: left
}

.fee-structure-card>div:last-child a {
    float: right;
    margin-right: 60px
}

.progress {
    width: 60%;
    height: 10px;
    background: #3b68a13b;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex
}

.fee-structure-card .progress:nth-child(2n) {
    background: #f9d2ac
}

.fee-structure-card .progress:nth-child(2n) .progress-bar {
    background: #f7931e
}

.progress {
    border-radius: 0 !important
}

.cart-fee-structure {
    padding: 0;
    margin-bottom: 15px;
    font-size: 13px
}

.average-fees-title {
    font-size: 13px;
    font-weight: 700;
    margin-bottom: 5px
}

.cart-fee-structure .progress {
    width: 100%;
    margin: 10px 0
}

.cart-fee-structure div:last-child:not(:nth-child(2)) .progress {
    background: #f9d2ac
}

.cart-fee-structure div:last-child:not(:nth-child(2)) .progress .progress-bar {
    background: #f7931e
}

.entrance_exam_div {
    margin-left: -4px;
    margin-right: -4px
}

.form-control-feedback {
    line-height: normal !important
}

#whiteheader li a {
    color: #333 !important
}

#whiteheader {
    background: #fff;
    border: none;
    margin-bottom: 0;
    -webkit-box-shadow: 0 1px 10px -1px grey;
    box-shadow: 0 1px 10px -1px grey;
    z-index: 1031
}

#whiteheader .write-review {
    color: #fff !important;
    padding: 5px 18px 5px 18px !important
}

#whiteheader .write-review:hover {
    -webkit-box-shadow: inset 0 0 0 1.5px #4374b9 !important;
    box-shadow: inset 0 0 0 1.5px #4374b9 !important;
    color: #4374b9 !important
}

.table_resize_class_wrapper {
    overflow-x: auto
}

.table_resize_class_wrapper .table_resize_class {
    margin: 0 auto
}

#whiteheader .user_menu>li>a:focus,
#whiteheader .user_menu>li>a:hover {
    background: #4374b9 !important;
    color: #fff !important
}

.bg_color {
    background-color: #ffffd0 !important
}

.review_img_style {
    width: 11px;
    margin-top: -3px
}

.search-page-sponsored img {
    width: 11px;
    margin-top: -3px
}

.interested_btn_style {
    font-weight: 700 !important;
    width: 100%
}

.filter_checkbox_label {
    padding-left: 26px
}

.filter_checkbox_label .custom_checkbox {
    margin-left: -26px
}

.close-button-wrapper button.close {
    padding: 0;
    float: right
}

.select2-results {
    text-align: left
}

.info-card .int-btn:last-child {
    width: 80px
}

.info-card .int-btn:last-child a {
    font-weight: 700;
    margin-left: 10px
}

#uni_filter_div {
    overflow-y: auto;
    max-height: 200px
}

#uni_filter_div .filter-label div:first-child {
    margin-left: -25px
}

select.select_icon {
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url(https://getmyuni.azureedge.net/assets/images/form_dropdown.svg);
    background-repeat: no-repeat;
    background-position: center right 5px;
    background-size: 8px
}

.info-card ul,
.mce-content-body ul {
    list-style-image: url(https://getmyuni.azureedge.net/assets/images/BULLET_ICONS.png);
    padding-left: 30px
}

.empty-star,
.full-star,
.half-star {
    display: inline-block;
    background: url(https://getmyuni.azureedge.net/assets/images/review_star_sprite.png) no-repeat;
    overflow: hidden;
    text-indent: -9999px;
    text-align: left
}

.empty-star {
    background-position: 0 0;
    width: 13px;
    height: 12px
}

.half-star {
    background-position: -14px 0;
    width: 13px;
    height: 12px
}

.full-star {
    background-position: -42px 0;
    width: 13px;
    height: 12px
}

.full-star-new,
.half-star-new,
.stroke-star-new {
    display: inline-block;
    background: url(https://getmyuni.azureedge.net/assets/images/review_star_sprite_new.png) no-repeat;
    overflow: hidden;
    text-indent: -9999px;
    text-align: left
}

.full-star-new {
    background-position: 0 0;
    width: 24px;
    height: 24px
}

.half-star-new {
    background-position: 0 -24px;
    width: 24px;
    height: 24px
}

.stroke-star-new {
    background-position: 0 -48px;
    width: 24px;
    height: 24px
}

.search-box {
    font-size: 12px;
    width: 100%;
    margin-bottom: 10px
}

#approval_filter_div,
#fees_filter_div,
#location_filter_div {
    overflow-y: auto;
    max-height: 100px
}

.state-cities label {
    margin-left: 20px
}

#filter-loader {
    background: rgba(0, 0, 0, .5);
    position: fixed;
    height: calc(100% - 50px);
    right: 0;
    left: 0;
    top: 50px;
    width: 100%
}

.card-cell {
    display: table;
    width: 100%
}

.card-cell div:first-child {
    display: table-cell;
    width: 60px
}

.card-cell .table-cell p:first-child a {
    color: #333;
    font-size: 15px;
    text-decoration: none
}

.card-cell .table-cell {
    padding-right: 0 !important
}

hr {
    margin: 10px 0
}

#filters-div .form-control {
    -webkit-box-shadow: none;
    box-shadow: none
}

#filters-div .info-card {
    padding: 0 20px 20px 20px;
    border-radius: 4px
}

#filters-div select {
    max-width: 100%;
    width: 100%;
    font-size: 12px
}

#back-to-top {
    background: #4374b9 !important;
    border: none !important;
    width: 40px !important;
    height: 40px !important;
    z-index: 1000 !important;
    padding-top: 3px
}

#back-to-top i {
    line-height: 38px !important
}

#filters-div input[type=checkbox],
.clg_suggestion input[type=checkbox] {
    display: none
}

#filters-div label div {
    width: 16px;
    height: 16px;
    display: inline-block;
    border: 1px solid grey;
    text-align: center;
    line-height: 16px;
    margin-right: 6px;
    cursor: pointer
}

.custom_checkbox {
    width: 16px;
    height: 16px;
    display: inline-block;
    border: 1px solid #999;
    text-align: center;
    line-height: 16px;
    margin-right: 6px;
    cursor: pointer
}

label i {
    font-size: 14px !important;
    opacity: 0 !important
}

#filters-div input:checked+label i {
    opacity: 1 !important
}

#filters-div label img {
    margin-top: -4px;
    width: 12px;
    opacity: 0 !important
}

#filters-div input:checked+label img {
    opacity: 1 !important
}

#filters-div label svg {
    margin-top: -4px;
    width: 12px;
    opacity: 0 !important
}

#filters-div input:checked+label svg {
    opacity: 1 !important
}

.page-logo {
    width: 30px;
    height: 30px
}

.spo {
    font-size: 12px;
    padding: 3px
}

.spo:hover {
    cursor: pointer;
    -webkit-transition: background-color 1s ease !important;
    -o-transition: background-color 1s ease !important;
    transition: background-color 1s ease !important;
    background-color: #efefef
}

.ui-autocomplete {
    max-height: 300px;
    overflow-x: hidden;
    overflow-y: auto;
    z-index: 999 !important;
    text-align: left;
    width: 40%
}

.pnm {
    white-space: nowrap;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis
}

.filter-checkbox {
    position: relative;
    margin: 0;
    vertical-align: middle;
    top: -3px;
    left: 0;
    background-color: #5bc0de;
    border-color: #5bc0de;
    -moz-transform: scale(.9);
    -ms-transform: scale(.9);
    -webkit-transform: scale(.9);
    -o-transform: scale(.9)
}

.filter-label {
    display: block;
    font-weight: 400
}

.filter-label span {
    font-size: 12px
}

.filter-title {
    font-size: 14px;
    color: #333;
    font-weight: 700 !important;
    margin: 0 0 10px 0 !important;
    display: block
}

.filter-title:hover {
    color: #333
}

.filter-label span {
    font-weight: 400
}

#filters-div hr {
    margin-top: 15px;
    margin-bottom: 10px
}

.filter-title .fa-angle-down,
.filter-title .fa-angle-up {
    font-size: 20px;
    color: #444
}

.card-icon-div {
    line-height: 20px
}

.card-icon-div a {
    min-width: 50px !important;
    font-size: 11px;
    color: #333
}

.heading-banner {
    padding: 20px 20px;
    background: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background: linear-gradient(45deg, #4374b9, #ee424f);
    width: 100%;
    color: #fff;
    overflow: hidden;
    border-radius: 6px;
    margin-bottom: 15px
}

.heading-banner h1 {
    margin: 0 0;
    font-size: 1.5em;
    font-weight: 700;
    line-height: 28px
}

.heading-banner h2 {
    font-weight: 700;
    font-size: 1em;
    margin: 10px 0;
    line-height: 21px
}

.heading-banner #all-exams-link a {
    color: #fff;
    font-weight: 700;
    font-size: 1.125em;
    margin: 10px 0;
    text-decoration: underline
}

#heading-banner-wrapper {
    margin-top: 30px
}

#sub-heading {
    font-weight: 400
}

.filter-btn {
    background: #fff;
    padding: 2px 20px;
    border-radius: 20px;
    color: #ee424f;
    font-weight: 700;
    font-size: 1em
}

.filter-btn:hover {
    color: #ee424f
}

#filters-div .info-card>div:first-child {
    text-align: center;
    padding: 10px;
    font-weight: 700;
    background-color: #4374b9;
    color: #fff;
    margin-left: -20px;
    margin-right: -20px;
    margin-bottom: 10px;
    line-height: 1.5em
}

.gmu-btn-blue,
.gmu-btn-green,
.gmu-btn-red,
.gmu-btn-yellow {
    font-weight: 700;
    font-size: 14px;
    border: 0;
    color: #fff;

}

.gmu-btn-green {
    background: #00c853;
    padding: 3px 20px
}

.gmu-btn-green:hover {
    color: #fff
}

.gmu-btn-yellow {
    background: #ffc000;
    padding: 3px 18px
}

.gmu-btn-yellow:hover {
    color: #fff
}

.gmu-btn-red {
    background: #ee424f;
    padding: 3px 10px
}

.gmu-btn-blue {
    background: #4374b9;
    padding: 3px 18px
}

.gmu-label {
    color: #333 !important;
    display: inline !important;
    font-weight: 700 !important;
    font-size: 14px !important
}

.explore-stream-div {
    margin: 50px auto
}

.explore-stream-div h2 {
    font-size: 24px
}

.explore-stream-div p {
    font-size: 15px;
    line-height: 24px
}

.explore-stream-div div:nth-child(2) {
    padding: 50px 0;
    text-align: center
}

.explore-stream-div button,
.gradient-button {
    background: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background: linear-gradient(45deg, #4374b9, #ee424f);
    border: 0;
    padding: 10px;
    color: #fff;
    font-weight: 700;
    border-radius: 20px
}

.explore-stream-div button {
    padding: 10px 30px
}

.explore-stream-div button:active,
.explore-stream-div button:focus,
.explore-stream-div button:hover {
    background: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background: linear-gradient(45deg, #4374b9, #ee424f);
    color: #fff;
    outline: 0 !important;
    border: 0;
    outline-style: none;
    outline-width: 0
}

.courses-list {
    width: 100%;
    background: #f9f9f9;
    padding: 50px 0
}

.courses-list .container .row i {
    margin-right: 10px
}

.gmu-btn {
    background: -webkit-linear-gradient(45deg, #0c66e4, #ee424f);
    background: -o-linear-gradient(45deg, #0c66e4, #ee424f);
    background: linear-gradient(45deg, #0c66e4, #ee424f);
    border: 0;
    font-weight: 700;
    color: #fff;
    margin-right: 20px;
    cursor: none
}

.gmu-btn:active {
    background: -webkit-linear-gradient(45deg, #0c66e4, #ee424f);
    background: -o-linear-gradient(45deg, #0c66e4, #ee424f);
    background: linear-gradient(45deg, #0c66e4, #ee424f);
    border: 0;
    cursor: none
}

.gmu-btn:focus,
.gmu-btn:hover {
    color: #fff !important;
    cursor: pointer
}

.courses-list .container .row a {
    border: 0;
    color: #333;
    background: #fff;
    margin: 0 10px 10px 0;
    font-weight: 700;
    border: .5px solid #ddd
}

.courses-list .row {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    width: 100%
}

.sub-course-list {
    text-align: left
}

#accordion1>li.active>a,
#accordion1>li>a:hover,
.nav-pill>li.active>a,
.nav-pill>li.active>a:focus,
.nav-pill>li.active>a:hover,
.nav-pill>li>a:hover {
    border-left: 2.5px solid #4374b9;
    background: #e9f3f7;
    color: #4374b9;
    border-radius: 0
}

.nav-pill li.active a i,
.nav-pill li:hover a i {
    display: block
}

#about_sub_category li a:hover,
#about_sub_category li.active a,
#about_sub_category li.active a:hover {
    color: #4374b9;
    border-left: 0;
    background: #fff;
    padding-left: 0 !important
}

.updated_wrapper {
    text-transform: capitalize
}

.updated_wrapper span {
    font-weight: 700
}

.slick-prev:before {
    content: '<'
}

.slick-next:before {
    content: '>'
}

.slick-next {
    right: 0;
    height: 43px;
    width: 30px;
    color: #000;
    z-index: 1000;
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.slick-prev {
    left: 0;
    background: #fff;
    height: 43px;
    width: 30px;
    color: #000;
    z-index: 1000;
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.slick-next::before,
.slick-prev::before {
    color: #000;
    font-weight: 700;
    display: inline-block;
    position: absolute;
    top: 9px;
    right: 8px;
    left: 8px
}

.slick-next:active:focus,
.slick-prev:active:focus {
    outline: 0
}

#slick-slider-menu {
    background: #fff;
    z-index: 900;
    padding: 0 30px
}

.menu-main-div {
    background: #fff none repeat scroll 0 0;
    margin-bottom: 10px;
    -webkit-box-shadow: 0 1px 10px 0 #696969;
    box-shadow: 0 1px 10px 0 #696969
}

.menu-menu-item {
    display: inline-block;
    font-size: 15px;
    padding: 3px 10px 0 10px;
    line-height: 40px;
    border-bottom: 3px solid transparent
}

.menu-menu-item:focus {
    outline: 0
}

.menu-menu-items-a {
    text-decoration: none !important;
    cursor: pointer;
    color: #666;
    font-weight: 700;
    padding: 0 5px !important;
    outline: 0 !important
}

.menu-menu-last-item {
    display: inline-block;
    font-size: 15px;
    line-height: 40px;
    padding: 3px 10px 0 10px !important
}

#college-menu {
    padding-left: 0;
    padding-right: 0;
    z-index: 1001 !important
}

.active-border {
    border-bottom: 3px solid #1492dd
}

.active-border .menu-menu-items-a {
    color: #4374b9 !important
}

.active-menu {
    border-bottom: 3px solid #4374b9 !important
}

.active-menu .menu-menu-items-a {
    color: #4374b9 !important;
    border: none
}

.card-table {
    overflow-y: auto
}

#prev_next_nav {
    padding: 0 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    background-color: #fff;
    height: 36px;
    min-height: 36px;
    line-height: 36px;
    font-weight: 700;
    font-size: 16px
}

.prev_next_button_bottom_two {
    text-align: right;
    background-color: #00c853 !important;
    color: #fff !important;
    font-size: 14px;
    font-size: 14px;
    line-height: 15px;
    padding: 6px
}

.prev_next_button_bottom {
    background-color: #12133c !important;
    color: #fff !important;
    text-align: left;
    font-size: 14px;
    min-height: 35px;
    font-size: 14px;
    line-height: 15px;
    padding: 6px
}

.span-title {
    display: block
}

.image_resize_class_wrapper {
    overflow-x: auto
}

.image_resize_class {
    max-width: 100% !important;
    height: auto !important
}

.iframe_resize_class_wrapper .iframe_resize_class {
    overflow-x: auto
}

.iframe_resize_class {
    max-width: 100% !important
}

.explore-stream-div p {
    margin-bottom: 0
}

.footer-wrapper {
    text-align: left;
    background-color: #0d283f;
    font-size: 14px;
    margin-top: 40px
}

.footer-wrapper ul {
    list-style-type: none;
    list-style-image: none;
    padding-left: 0
}

.footer-col {
    color: #fff;
    font-size: 14px;
    line-height: 24px
}

.footer-col h3 {
    font-size: 16px;
    font-weight: 700
}

.footer-col a {
    color: #fff
}

.footer-col img {
    width: 30px;
    height: 30px;
    line-height: 30px;
    margin: 3px
}

.footer-col [class*="fa fa-"] {
    background-color: #fff;
    border-radius: 30px;
    color: #0d283f;
    height: 30px;
    line-height: 30px;
    margin: 3px;
    width: 30px;
    font-size: 15px;
    text-align: center
}

.footer-col:nth-last-child(2) ul li:last-child,
.footer-col:nth-last-child(3) ul li:last-child,
.footer-col:nth-last-child(4) ul li:last-child {
    font-weight: 700;
    font-size: 14px
}

#uppper_footer {
    padding: 40px 0
}

.footer-last {
    color: #fff;
    padding: 20px 0;
    border-top: 1px solid #fff
}

.footer-last h3 {
    font-size: 16px;
    font-weight: 700
}

.follow_us {
    display: none
}

.info-course-detail img {
    width: 12px;
    margin-top: -4px
}

.slider-search {
    display: none;
    top: 0;
    height: 101vh;
    width: 100%;
    background: #f9f9f9;
    z-index: 9999;
    position: fixed;
    text-align: left
}

.slider-search a {
    color: #333;
    background: #f1f1f1;
    margin: 0;
    font-weight: 700;
    border-radius: 0;
    border-radius: 0;
    text-decoration: none
}

.tab-pane .input-group input {
    font-size: 16px;
    height: 40px
}

.nav-pills>li.active>a {
    background: -webkit-linear-gradient(45deg, #0c66e4, #ee424f);
    background: -o-linear-gradient(45deg, #0c66e4, #ee424f);
    background: linear-gradient(45deg, #0c66e4, #ee424f);
    border-radius: 0
}

.form-tabs.nav-pills>li+li {
    margin-left: 1px
}

.slider-search .nav-pills>li {
    float: left;
    width: 33.2%
}

.tab-content .input-group input {
    font-size: 15px
}

.search-toggle .fa-close {
    font-size: 24px;
    color: #ccc;
    float: right;
    padding: 18px 0;
    cursor: pointer
}

.search-toggle {
    padding: 10px 0;
    width: 100%;
    display: inline-block;
    background: 0 0 !important
}

.search-toggle>svg {
    float: right;
    margin: 7px 0;
    cursor: pointer
}

.tab-content .input-group {
    width: 100%
}

.trending {
    overflow-x: auto;
    height: calc(100vh - 220px)
}

.trending-college-section .heading-design {
    margin: 20px 0 5px 0;
    font-size: 18px
}

.trending-college-section a {
    background: 0 0;
    display: block;
    padding: 6px 0
}

.trending-college-section a:hover {
    background: #f2f2f2
}

.trending-college-detail {
    border-bottom: 1px solid #ccc
}

.trending-college-detail h2 {
    margin: 15px 0 8px 0;
    font-size: 16px;
    font-weight: 700;
    color: #666
}

.trending-college-detail p {
    margin-bottom: 13px;
    color: #666;
    font-size: 14px;
    font-weight: 400
}

.anchor {
    display: block;
    position: relative;
    top: -90px;
    visibility: hidden
}

.college_counseling_modal_new .modal-body {
    padding-top: 0;
    padding-bottom: 0
}

.college_counseling_modal_new #lead-modal-left-div .lead_modal_heading {
    margin-bottom: 40px
}

.college_counseling_modal_new #lead-modal-left-div .lead_modal_heading+div {
    border-bottom: 1px solid #fff;
    width: 250px;
    margin: 0 auto
}

.college_counseling_modal_new #lead-modal-left-div .lead_modal_heading+div+div {
    margin-top: -15px
}

.college_counseling_modal_new #lead-modal-left-div .lead_modal_heading+div+div+p {
    margin: 25px 0
}

.college_counseling_modal_new #lead-modal-left-div img#lead-modal-left-div {
    width: 100%
}

.college_counseling_modal_new #counseling-loader {
    height: 100%
}

.college_counseling_modal_new #counseling-loader .cssload-container {
    margin-top: calc(50% + 50px)
}

.college_counseling_modal_new #lead-modal-right-div #lead-form-header .lead_form_title_heading_one {
    font-weight: 700;
    font-size: 20px
}

.college_counseling_modal_new #lead-modal-right-div #lead-form-header .lead_form_title_heading_two {
    font-weight: 700
}

.college_counseling_modal_new #lead-modal-right-div #lead-form-header .lead_form_sub_heading_one {
    text-align: center;
    margin: .5em 0;
    font-weight: 700
}

.college_counseling_modal_new #lead-modal-right-div #lead-form-header .lead_form_sub_heading_two {
    text-align: center;
    margin: 0
}

.college_counseling_modal_new #lead-modal-right-div #leadForm .form-group #email {
    margin-bottom: 15px
}

.college_counseling_modal_new #lead-modal-right-div #leadForm .select2-container,
.college_counseling_modal_new #lead-modal-right-div #leadForm select#current_location {
    width: 100% !important
}

.college_counseling_modal_new #lead-modal-right-div #leadForm .form-group #qualification option {
    color: #afafaf !important
}

.college_counseling_modal_new #lead-modal-right-div #leadForm .form-group:last-child {
    margin-bottom: 5px
}

.college_counseling_modal_new #lead-modal-right-div #leadForm .form-group:last-child label.pull-left {
    max-width: calc(100% - 90px);
    font-size: 11px;
    line-height: 13px
}

.college_counseling_modal_new #lead-modal-right-div #leadForm .form-group:last-child label.pull-left span {
    display: block;
    padding-left: 20px
}

.college_counseling_modal_new #lead-modal-right-div #leadForm .form-group:last-child input {
    float: left;
    margin-top: 6px
}

.college_counseling_modal_new #lead-modal-right-div #leadForm .form-group:last-child button:not(.pull-right) {
    display: block;
    margin: 0 auto;
    font-weight: 700
}

.college_counseling_modal_new #lead-modal-right-div #leadForm .form-group:last-child button.pull-right {
    font-weight: 700
}

.college-counseling-modal .modal-title {
    font-size: 1.5em
}

.college-counseling-modal .modal-header {
    background-color: #1492dd;
    color: #fff;
    padding: 5px
}

.college-counseling-modal .close {
    opacity: 1;
    background: #2a90ce;
    color: #fff;
    border-radius: 50%;
    height: 24px;
    width: 24px;
    margin: -13px -26px 0 0;
    line-height: 24px
}

.gmu-loader {
    position: absolute;
    height: calc(100% - 30px);
    width: calc(55% - 30px);
    display: none;
    background-color: #fff;
    z-index: 9999
}

.counseling-model-body-heading {
    font-size: 24px;
    color: orange;
    font-weight: 700
}

.college-counseling-modal .select2-container--default .select2-selection--single {
    border: none;
    border-bottom: solid 1px #7b7b7b;
    border-radius: 0
}

.college-counseling-modal .select2-container:focus {
    outline: 0
}

.college-counseling-modal .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #7b7b7b !important;
    font-size: 14px !important
}

.college-counseling-modal .select2-selection-single-revised {
    background-color: #fff !important;
    border: none !important;
    border-radius: 0 !important;
    border-bottom: solid 1px #7b7b7b !important
}

.college-counseling-modal .select2-selection__rendered {
    padding-left: 0 !important
}

.modal-form-input {
    width: 100%;
    border: none;
    border-bottom: solid 1px #7b7b7b;
    font-size: 14px;
    line-height: 1.42857143;
    background-color: #fff;
    margin-top: 15px
}

.modal-form-textarea {
    width: 100%;
    border: none;
    border-bottom: solid 1px #7b7b7b;
    font-size: 14px;
    line-height: 1.42857143;
    background-color: #fff;
    box-shadow: none;
    border-radius: 0;
    padding: 0
}

.modal-form-input:focus,
.modal-form-textarea:focus {
    outline: 0;
    box-shadow: none;
    border-bottom: solid 1px #7b7b7b
}

.modal-select-input {
    color: #7b7b7b
}

.modal-select-input option:not(first-child) {
    color: #000
}

.circle-div {
    background-color: #fff;
    border-radius: 15px;
    color: #000;
    font-weight: 700;
    height: 30px;
    line-height: 30px;
    width: 30px;
    text-align: center;
    margin: 0 auto
}

.circle-div-wrapper-div {
    display: inline-block;
    width: 80px;
    padding: 0 5px
}

.lead-model-content-wrapper {
    display: table;
    margin: 0 -15px
}

#lead-modal-left-div {
    background-color: #2a90ce;
    color: #fff;
    display: table-cell;
    padding: 0 15px;
    width: 45%
}

#lead-modal-right-div {
    display: table-cell;
    width: 55%;
    padding: 0 15px;
    min-height: 400px
}

.close-button-wrapper {
    position: absolute;
    width: calc(55% - 30px)
}

.checkbox-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px
}

.checkbox-switch input {
    display: none
}

.checkbox-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s
}

.checkbox-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: #fff;
    -webkit-transition: .4s;
    transition: .4s
}

input:checked+.checkbox-slider {
    background-color: #00c853
}

input:focus+.checkbox-slider {
    box-shadow: 0 0 1px #00c853
}

input:checked+.checkbox-slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px)
}

.checkbox-slider.round {
    border-radius: 34px
}

.checkbox-slider.round:before {
    border-radius: 50%
}

@media (max-width: 768px) {
.writeReview {
        margin-left: 34%;
    }

    .blueBgDiv .mobileOnly {
        display: none !important;
    }

    .mobileOnly {
        display: none !important;
    }

    .college_counseling_modal_new .modal-content {
        margin-top: 10%
    }

    .lead-model-content-wrapper {
        display: block
    }

    .college-counseling-modal .modal-title {
        font-size: 20px;
        padding: 0 15px
    }

    .college-counseling-modal .modal-header {
        padding-bottom: 10px
    }

    .college-counseling-modal .modal-header p {
        margin: 5px
    }

    .college-counseling-modal #lead-form-header {
        background: #2a90ce;
        color: #fff;
        margin-left: -15px;
        margin-right: -15px;
        padding: 5px
    }

    .college-counseling-modal .close {
        height: 24px;
        width: 24px;
        margin: 0 -15px 0 0;
        line-height: 24px
    }

    .counseling-model-body-heading {
        font-size: 20px
    }

    #lead-modal-left-div {
        display: none
    }

    #lead-modal-right-div {
        width: 100%;
        display: block;
        min-height: 350px
    }

    .gmu-loader {
        width: calc(100% - 30px)
    }

    .close-button-wrapper {
        position: absolute;
        width: calc(100% - 30px)
    }

    .close_icon {
        font-size: 28px !important
    }
}

.modal_success_msg {
    margin: 20% 5%;
    font-size: 1.5em;
    font-weight: 700
}

#lead-modal-right-bottom-div {
    padding: 32px 0
}

#lead-modal-right-bottom-div #get-brochure {
    font-weight: 700
}

#lead-modal-right-bottom-div span {
    max-width: calc(100% - 70px);
    font-size: 11px
}

.brochure_modal_new #br_mobile,
.brochure_modal_new #br_name {
    margin-bottom: 15px
}

.brochure_modal_new .modal-body {
    padding-bottom: 0;
    padding-top: 0
}

.brochure_modal_new #lead-modal-left-div {
    background-position: center;
    background-size: 55%;
    background-repeat: no-repeat
}

.brochure_modal_new #lead-modal-right-div {
    padding-left: 0;
    padding-right: 0
}

.brochure_modal_new #lead-modal-right-div #brochure_form_wrapper .modal-title {
    font-size: 1.5em;
    margin: 1.5em 0
}

.brochure_modal_new #lead-modal-right-div #brochure_form_wrapper .modal-dis {
    margin: .5em 0
}

.brochure_modal_new #lead-modal-right-div .modal-dis {
    margin: .5em 0
}

.main_page_nav>li {
    width: 100%
}

.main_page_nav>ul {
    padding-left: 0
}

.main_page_nav .nav-pills>li,
.menu_new .nav-pills>li {
    width: 100%
}

#facilities_info span {
    min-width: 98px
}

#courses .courses_heading {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-bottom: 0;
    border-bottom: 1px solid #ddd;
    width: 100%;
    align-items: center
}

#facilities_card .college-facility-list-span {
    cursor: pointer;
    padding: 10px
}

#facilities_card .facilities_card_span {
    display: block
}

#exam_table .exam_table_name {
    cursor: pointer
}

.menu_new .nav-pills>li.active>a,
.menu_new .nav-pills>li.active>a:focus,
.menu_new .nav-pills>li.active>a:hover {
    color: #272727;
    border-left: 4px solid #1075b9;
    background: #e9f3f7 !important;
    border-radius: 0
}

.menu_new .nav-pills>li {
    max-height: 100px
}

.menu_new .nav-pills>li>a>img {
    position: absolute;
    right: 8px;
    top: calc(50% - 8px);
    width: 16px;
    display: none
}

.menu_new .nav-pills>li.active>a>img,
.menu_new .nav-pills>li:hover>a>img {
    display: block
}

.slick_bottom_up_margin {
    margin-top: 0;
    margin-bottom: 15px
}

#facilities_div {
    text-align: center
}

#facilities_div h2 {
    text-align: left
}

.main_middle_course_fee .info-heading-label {
    margin: 0
}

.review-card .category-wrapper .category-name {
    font-weight: 700;
    width: calc(100% - 180px);
    text-transform: none !important;
    float: left
}

.fee_review_card {
    background: #fff
}

.review-card .category-wrapper {
    margin-left: 67px;
    margin-top: 0 !important
}

.filter-grid .category-wrapper .category-name,
.mixitup-course .category-wrapper .category-name,
.review-card .category-wrapper .category-name {
    color: #4374b9
}

.review-card .reviewer-info {
    margin-bottom: 10px
}

#ssr-info .info-card>h2 {
    display: inline-block
}

#college-images-wrapper {
    padding-right: 7px;
    padding-left: 7px
}

.college-image-wrapper {
    padding: 0 8px
}

#college-images-wrapper h2 {
    padding-left: 8px
}

#manga-nav .nav-pills img {
    width: 24px;
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

#manga-nav>.nav-pills>li.active>a img,
#manga-nav>.nav-pills>li:hover img {
    -webkit-filter: grayscale(0);
    filter: grayscale(0)
}

#manga-nav .nav-pills svg {
    width: 24px;
    height: 24px;
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

#manga-nav>.nav-pills>li.active>a svg,
#manga-nav>.nav-pills>li:hover svg {
    -webkit-filter: grayscale(0);
    filter: grayscale(0)
}

#manga-nav>.nav-pills>li h3 {
    padding: 5px 0
}

.branch_detail .fees-column-3 {
    line-height: 42px;
    text-align: right
}

.make_inline {
    display: inline
}

.heading_icon_wrapper {
    padding: 12px 0 12px 15px
}

.heading_icon_1 {
    display: inline-block;
    vertical-align: middle;
    height: 30px
}

.heading_icon_2 {
    width: calc(100% - 40px);
    display: inline-block;
    vertical-align: middle
}

.heading_icon {
    width: 30px;
    height: 30px;
    margin-right: 5px
}

.check_details .download_icon {
    height: 12px;
    width: 12px;
    margin-top: -2px
}

#course-filter-div .read-more-div>ul {
    display: inline
}

#fee_main_div {
    padding: 0;
    margin-top: 0
}

.review-card hr {
    margin: 15px 0 !important
}

.mix_title {
    padding-left: 0
}

.program-title {
    border: none;
    color: #13a89e;
    font-size: 15px
}

.program-title .fees-column-2 {
    text-align: left
}

.fees_filter_streams .read-more-div:after {
    display: none
}

#table-fees {
    margin-bottom: 15px
}

.rating_left_div {
    padding-left: 0;
    padding-right: 5px;
    margin-top: 10px
}

.rating_right_div {
    padding-right: 0;
    padding-left: 5px;
    margin-top: 10px
}

#ssi-div {
    background-color: #fff;
    padding: 15px 0
}

#ssr-info {
    padding-bottom: 15px
}

.fee_college_card {
    padding: 0
}

.fee_college_card #container-html {
    padding: 15px 15px
}

.fee_college_card #container-html div:nth-child(2) {
    margin-top: 10px;
    color: #4374b9
}

.fee_college_card #container-html div:nth-child(2) span:nth-child(2) {
    color: #4374b9
}

.college-page-card {
    margin-top: 0
}

.filter_gride_card {
    margin-bottom: 10px
}

.filter_gride_card .course-fees-ads-div {
    margin: 20px 0
}

.fee_review_card .review_more_link .label-nl {
    display: none
}

.branches_div .card-btn-link {
    margin-top: 10px
}

.clg_info_div .clg_course_info,
.mixitup-course .panel-heading .course-collapse-anchor {
    border-bottom: 0 solid #d3d3d3;
    background: #f4f4f4
}

.category_hr {
    margin-left: 70px
}

.filter-grid .category-wrapper,
.mixitup-course .category-wrapper {
    margin-left: 67px
}

.filter-grid .category-wrapper .category-name,
.mixitup-course .category-wrapper .category-name {
    color: #4374b9;
    width: calc(100% - 190px);
    line-height: 20.5px
}

.fee_review_card .reviewer-info .reviewer-grad {
    font-size: 14px;
    font-weight: 400
}

.fee_review_card .reviewer-info .star_div>div {
    margin: 5px 5px 0 5px
}

.fee_review_card .reviewer-info .star_div>div>span {
    padding-top: 20px;
    line-height: 1
}

.mixitup-course .fees-column-2 .duration-wrapper .info-heading-label {
    margin: 5px 0
}

.clg_info_div .fees-column-2 .duration-wrapper .info-cost,
.mixitup-course .fees-column-2 .duration-wrapper .info-cost {
    margin: 0;
    font-size: 14px;
    font-weight: 700
}

.clg_info_div .fees-column-1 .custom-pointer-wrapper,
.mixitup-course .fees-column-1 .custom-pointer-wrapper {
    margin-bottom: 10px
}

.clg_info_div .fees-column-1 .custom-pointer-wrapper span,
.mixitup-course .fees-column-1 .custom-pointer-wrapper span {
    color: #0d3d63
}

.mixitup-course .fees-column-1 .duration-wrapper p {
    display: inline;
    color: #858585
}

.mixitup-course .fees-column-2 .duration-wrapper {
    display: inline
}

.course_fee_details {
    border-bottom: 1px solid #eee
}

.clg_info_div .fees-column-2 .info-heading-label,
.course_fee_details .fees-column-2 .info-heading-label {
    font-size: 14px;
    margin: 0 0 10px 0
}

.carousel-wrapper .carousel-detail-wrapper .banner-college-name>div {
    display: block;
    font-weight: 700;
    font-size: 14px
}

.carousel-wrapper .carousel-detail-wrapper .banner-college-name>div span {
    font-weight: 700;
    color: #4374b9
}

.carousel-wrapper {
    margin-bottom: 15px
}

.carousel-wrapper .banner-college-name {
    width: calc(100% - 180px)
}

.fees_mix_show {
    margin-top: 0;
    line-height: 44px;
    cursor: pointer;
    font-weight: 700;
    color: #4374b9
}

.fees_mix_show:hover {
    color: #4374b9
}

.branch_detail .block_weight:hover {
    color: #4374b9;
    text-decoration: underline !important
}

.fees_page_info_card {
    margin-top: 0
}

#fees-loading-loader {
    height: 100%;
    width: 100%;
    background: transparent none repeat scroll 0 0;
    position: fixed
}

#fees-loading-loader .cssload-container {
    top: calc(50% - 20px)
}

#table-fees .table>thead>tr>th {
    border-bottom: 0 !important
}

.branches_div {
    margin-bottom: 10px
}

.fees_table_div .read-more-btn-table {
    cursor: pointer;
    font-weight: 700;
    color: #4374b9
}

.fees_table_div .table {
    margin-bottom: 20px
}

#forum-wrapper reply_by_section {
    line-height: 1.8em
}

#forum-wrapper reply_by_section p {
    margin: 0
}

.full_answer_div thank_count {
    color: #000;
    font-size: 20px
}

.reply_by_section .timeago {
    font-size: 120%
}

.reply_by_section .answer-subwrapper {
    padding-top: 0
}

.reply_by_heading span {
    font-weight: 700;
    font-size: 14px
}

.reply_by_section {
    overflow-y: hidden
}

.reply_by_heading {
    color: #0e325e;
    font-size: 80%
}

.question_by_section span:first-child {
    font-size: 85%
}

.forum_question_answer_wrapper .link_wrapper {
    float: right;
    line-height: 1.8em
}

.link_wrapper div {
    display: inline
}

.clg_course_info .fees-column-3,
.course_fee_details .fees-column-3 {
    text-align: right
}

.course_fee_details .fees-column-1 h3 {
    margin-bottom: 0
}

.star_div {
    position: absolute;
    right: 0;
    top: 0
}

.make-inline {
    display: inline !important
}

#forum-wrapper .forum-tags-one {
    display: inline-block;
    color: #ccc;
    background: 0 0;
    border: 1px solid #ccc;
    border-radius: 20px;
    text-transform: capitalize;
    margin: 0 5px
}

#forum-wrapper .forum-tags-two {
    display: inline-block;
    color: #ccc;
    background: 0 0;
    border: 1px solid #ccc;
    border-radius: 20px;
    margin: 0 5px 0 0
}

.question-header {
    color: #4374b9 !important;
    font-weight: 700 !important;
    font-size: 14px !important
}

.subwrapper-logo-one {
    left: 0;
    top: 5px;
    position: absolute;
    color: #4374b9;
    background: #f5f5f5;
    border: none;
    border-radius: 50%;
    padding: 10px 11px;
    font-weight: 700;
    font-size: 24px !important
}

.subwrapper-logo-two {
    left: 0;
    top: 8px;
    background: #f5f5f5;
    position: absolute;
    color: #4374b9;
    border: none;
    border-radius: 50%;
    padding: 10px 13px;
    line-height: 20px;
    font-weight: 700;
    font-size: 24px !important
}

.question-wrapper {
    padding-top: 10px
}

#fee-exam-detail {
    margin-top: 0
}

#fee-exam-detail .exam-name {
    margin: 0;
    padding: 0
}

#fee-exam-detail .fee-app-date {
    color: #858585;
    font-size: 14px;
    padding: 0;
    margin-right: 10px
}

#fee-exam-detail .fee-exam-date {
    color: #858585;
    font-size: 14px;
    margin-right: 10px
}

.fee-heading-right {
    padding-left: 0;
    padding-right: 0
}

.fee-heading-right p {
    float: right;
    font-size: 13px
}

#rank_section {
    display: table;
    width: 100%
}

.table-row {
    display: table-row
}

.table-cell {
    display: table-cell;
    vertical-align: middle
}

.rank-section-cell {
    color: #4374b9;
    padding: 20px 0;
    width: 150px
}

#rank_section .table-row .table-cell .key-stats-sub-heading {
    font-weight: 700;
    font-size: 16px
}

.info_full_hr,
.respo_info_full_hr {
    margin-top: 15px;
    margin-bottom: 15px;
    margin-left: -16px !important;
    margin-right: -16px !important
}

.arrow-link-font,
.review-card .info-heading-label {
    font-size: 14px !important
}

.navbar-brand {
    padding: 10px 15px 0 15px
}

#top-links .top-links-button {
    margin-bottom: 5px;
    white-space: normal
}

@media (max-width: 991px) {
.blueBgDiv .mobileOnly {
        display: none;
    }

    .pnm {
        white-space: unset;
        -o-text-overflow: unset;
        text-overflow: unset
    }

    #whiteheader .navbar-toggle {
        display: block
    }

    #whiteheader {
        background: 0 0;
        z-index: 20;
        -webkit-box-shadow: none;
        box-shadow: none
    }

    .bottom_nav {
        display: inline;
        text-align: center
    }

    #filter_icon {
        padding: 12px 10px 12px 10px;
        border: none;
        float: right !important
    }

    #whiteheader #search-button {
        float: right;
        padding: 12px 10px 12px 10px
    }

    #whiteheader .navbar-toggle,
    #whiteheader .navbar-toggle:focus,
    #whiteheader .navbar-toggle:hover {
        border-color: #fff;
        background: 0 0;
        float: left;
        margin: 0;
        z-index: 999
    }

    #whiteheader-mob-logo {
        display: block
    }

    #whiteheader-logo {
        display: none
    }

    #cap_logo {
        width: 150;
        top: 237px;
        left: calc(50% - 90px)
    }

    .course-name-text-box {
        text-align: center
    }

    .navbar-searchbox {
        min-width: 190px
    }

    .navbar-form {
        margin-right: 0;
        padding-right: 0
    }

    .navbar-default .navbar-nav>li>a {
        margin-left: 0;
        padding-right: 15px;
        padding-left: 15px
    }

    .india_map {
        margin: 0 auto;
        width: 275px;
        height: 335px
    }

    .info-card {
        padding: 10px !important
    }

    .anchor {
        top: -45px
    }

    .footer-wrapper {
        margin-bottom: 30px
    }

    .mobile-text-center {
        text-align: center !important
    }

    #container-html h4 {
        line-height: 20px
    }
}

@media (max-width: 767px) {
.blueBgDiv .mobileOnly {
        display: none;
    }

    .carousel-wrapper .banner-college-name {
        width: calc(100% - 60px) !important
    }

    .carousel-wrapper .carousel-detail-wrapper .gmu-btn-green {
        position: relative;
        display: block;
        -webkit-transform: none;
        -ms-transform: none;
        transform: none;
        right: 0
    }

    .carousel-wrapper .carousel-detail-wrapper {
        padding: 10px !important
    }

    #facilities_info span {
        min-width: 65px
    }

    #courses .courses_heading {
        display: block;
        margin-bottom: 0
    }

    #facilities_info {
        -webkit-box-pack: space-evenly;
        -ms-flex-pack: space-evenly;
        justify-content: space-evenly
    }

    .mob-no-padding {
        padding: 0
    }

    .mob-no-margin {
        margin: 0
    }

    .review-card .category-name {
        width: 100%
    }

    .review-card .reviewer-info+div {
        width: 100%;
        margin-top: 10px
    }

    .review-card .category-wrapper {
        margin-left: 0;
        margin-right: 0;
        width: 100%
    }

    .review-card .category-wrapper .reviewer-info .category-name {
        margin-top: 26px
    }

    .main_middle_course_fee {
        margin: 0
    }

    .clg_course_info .fees-column-3 {
        width: 100%;
        text-align: center
    }

    .clg_course_info .fees-column-1,
    .course_fee_details .fees-column-1 {
        line-height: 24px
    }

    .filter_gride_card .collapsestream {
        padding: 10px
    }

    .review-card hr {
        margin: 10px 0 !important
    }

    .fees_filter_streams h3 {
        margin-bottom: 10px
    }

    .fees_filter_streams .new-card-sub-title {
        margin-bottom: 0
    }

    .respo_info_full_hr {
        margin: 10px -16px !important
    }

    .branch_detail .fees-column-3 {
        line-height: normal;
        text-align: right
    }

    .courses-list hr {
        border-top: 1px solid #f9f9f9
    }

    .courses_container .row {
        text-align: center
    }

    .info-card .view-all {
        padding-top: 10px
    }

    .fee-structure-card>div:first-child div {
        margin-bottom: 8px
    }

    .clg-card {
        width: calc(50% - 8px);
        margin: 4px
    }

    .hide_mobile_view {
        display: none
    }

    .hide_desktop_view {
        display: block
    }

    .rating_left_div {
        padding-right: 0
    }

    .rating_right_div {
        padding-left: 0
    }

    .filter-grid .category-wrapper,
    .mixitup-course .category-wrapper {
        margin-left: 0
    }

    #info-card {
        margin: 0
    }

    #ssi-div .rating-circle-wrapper:first-child {
        display: block;
        margin: 0 auto 10px auto
    }

    #ssi-div .rating-circle-wrapper {
        width: 31%;
        margin-bottom: 10px
    }

    .review_more_link {
        padding-left: 0
    }

    .reply_div {
        padding-left: 50px !important
    }

    .link_wrapper .show_answer {
        margin-top: 10px;
        float: right
    }

    .forum_question_answer_wrapper .pull-left {
        padding: 0
    }

    .link_wrapper {
        padding-top: 0
    }

    .forum_question_answer_wrapper {
        padding-left: 50px !important
    }

    #ssr-info .info-card>span {
        float: left !important
    }

    .star_div {
        position: relative;
        margin-top: -32px
    }

    .reviewer-info-detail {
        padding-left: 72px
    }

    .fee_review_card .review-text-wrapper {
        padding: 0;
        margin-top: 63px
    }

    .branch_detail .fees-column-1 {
        display: inline-block;
        width: 100%;
        padding-bottom: 5px
    }

    .branch_detail .fees-column-2 {
        padding-left: 0;
        text-align: left
    }

    .branch_detail .fees-column-2 .custom-pointer a {
        font-weight: 100;
        cursor: pointer
    }

    .branch_detail {
        padding: 8px
    }

    .branch_detail .fees-column-3 {
        float: right;
        text-align: right
    }

    .desktop_view {
        display: block
    }

    .course_fee_details .fees-column-2 .visible-xs {
        display: inline-block !important
    }

    .clg_course_info .fees-column-1 .btn_exam,
    .course_fee_details .fees-column-2 .btn_exam {
        color: #ccc;
        background: 0 0;
        border: 1px solid #ccc;
        border-radius: 3px;
        margin: 0 5px 9px 0;
        padding: 0 5px;
        font-size: 12px;
        display: inline-block !important
    }

    .course_fee_details .fees-column-2 .btn_exam {
        margin-bottom: 5px
    }

    .course_fee_details .check_details {
        text-align: center
    }

    .course_fee_details .fees-column-2 {
        width: 100%;
        text-align: left;
        padding-bottom: 5px
    }

    .course_fee_details .gmu-btn-red {
        width: 100%;
        font-weight: 700
    }

    .course_fee_details .fees-column-3 {
        width: 100%;
        text-align: center;
        margin-top: 0
    }

    .course_fee_details .fees-column-3 .gmu-btn-green {
        margin-bottom: 0;
        padding: 3px 32px;
        width: 100%;
        margin-top: 10px
    }

    .course_fee_details .fees-column-3 .gmu-btn-yellow {
        display: none;
        padding: 3px 32px
    }

    .course_fee_details .fees-column-3 button {
        width: 48%
    }

    .course_fee_details .fees-column-2 p {
        display: inline;
        margin: 5px 0;
        color: #858585
    }

    .course_fee_details .fees-column-2 .info-heading-label {
        color: #333;
        font-size: 15px;
        margin: 0;
        font-weight: 400
    }

    .clg_course_info .fees-column-1,
    .course_fee_details .fees-column-1 {
        width: 100%;
        float: left
    }

    .clg_course_info .duration-wrapper,
    .clg_course_info .info-cost {
        display: inline !important
    }

    #rank_section .table-row .table-cell {
        display: inline-block;
        width: 100%;
        padding: 0
    }

    #rank_section .table-row .table-cell .key-stats-sub-heading {
        padding-bottom: 15px
    }

    #rank_section .table-row .table-cell span .college-facility-list {
        margin-right: 13px
    }

    .info-card .card-btn-link a {
        white-space: normal;
        text-align: left
    }

    #main_div {
        padding-right: 0;
        padding-left: 0
    }

    #whiteheader .navbar-toggle,
    #whiteheader .navbar-toggle:focus,
    #whiteheader .navbar-toggle:hover {
        border-color: #fff;
        background: 0 0;
        float: left;
        margin: 0;
        z-index: 999
    }

    #whiteheader-mob-logo {
        display: block
    }

    #whiteheader-logo {
        display: none
    }

    .banner-search>h2 {
        line-height: 22px
    }

    .mobi {
        padding: 0 !important
    }

    .navbar-default .navbar-toggle {
        display: block;
        padding: 9px 16px
    }

    .banner-search>h1 {
        display: block
    }

    .banner-search {
        position: absolute;
        top: calc(50% + 74px);
        text-align: center;
        width: 90%;
        left: 5%;
        right: 5%
    }

    .searchtype {
        border-radius: 0
    }

    .navbar-toggle {
        margin: 8px 15px
    }

    .navbar-default .navbar-brand {
        padding-left: 8px
    }

    #search-button {
        background: 0 0;
        font-size: 24px;
        padding: 9px 16px;
        float: right;
        margin: 0;
        border: 0;
        color: #fff;
        display: block;
        z-index: 999
    }

    #whiteheader #search-button {
        float: right;
        padding: 12px 10px
    }

    #search-button img {
        width: 24px
    }

    #filter_icon img {
        width: 24px
    }

    #filter_icon {
        padding: 12px 10px 12px 10px;
        border: none;
        float: right !important
    }

    #cap_logo {
        width: 180px;
        top: 230px
    }

    .explore-stream-div div:nth-child(2) {
        left: 0
    }

    .banner-search {
        position: absolute;
        top: calc(50% + 74px);
        text-align: center
    }

    .banner-search h1 {
        font-size: 20px;
        line-height: 24px
    }

    .course-name-text-box {
        width: 100%;
        text-align: center
    }

    .courses-list .container .row {
        margin: 0
    }

    .explore-stream-div {
        margin-bottom: 0
    }

    .footer-col {
        padding-right: 5px
    }

    .input-group {
        width: 100%
    }

    .slider-search .nav-pills>li {
        width: 33.1%
    }

    .slider-menu-option ul li a {
        font-size: 14px;
        padding: 14px 0
    }

    #login-signup a {
        font-size: 14px;
        padding: 14px 0
    }

    .banner h1 {
        font-size: 20px;
        margin-top: 0;
        padding-top: 70px;
        line-height: 27px
    }

    #left-fix {
        display: none
    }

    .main-body table {
        width: 100% !important;
        display: block;
        overflow-x: auto
    }

    #mid_section {
        padding-left: 0;
        padding-right: 0
    }

    .opt {
        position: absolute;
        display: none;
        border-radius: 5px;
        -webkit-box-shadow: 0 8px 16px 0 rgba(0, 0, 0, .2);
        box-shadow: 0 8px 16px 0 rgba(0, 0, 0, .2);
        z-index: 50;
        left: 30px;
        margin-top: -10px;
        background-color: #fff
    }

    .opt {
        text-decoration: none;
        list-style: none;
        padding: 0;
        margin-top: 1px
    }

    .opt a {
        color: #000;
        padding: 12px 16px;
        text-decoration: none;
        display: block
    }

    .opt a:hover {
        background-color: #f1f1f1
    }

    .table_resize_class_wrapper .table_resize_class {
        width: 100% !important
    }

    .fee-structure-card .progress:nth-child(n) {
        float: left
    }

    .fee-structure-card>div:last-child a {
        margin-right: 0
    }

    .banner p {
        margin-bottom: 16px
    }
}

@media (max-width: 600px) {
.blueBgDiv .mobileOnly {
        display: none;
    }

    .courses-list .gmu-btn {
        margin-bottom: 20px;
        margin-right: 0
    }

    .courses-list hr {
        border-top: 1px solid #e6e4e4
    }

    #main_div {
        padding-right: 0;
        padding-left: 0
    }

    #whiteheader .navbar-toggle,
    #whiteheader .navbar-toggle:focus,
    #whiteheader .navbar-toggle:hover {
        border-color: #fff;
        background: 0 0;
        float: left;
        margin: 0;
        z-index: 999
    }

    #whiteheader-mob-logo {
        display: block
    }

    #whiteheader-logo {
        display: none
    }

    .courses-list .row {
        display: block
    }

    .sub-course-list {
        text-align: center;
        width: 100%
    }

    #uppper_footer {
        display: none
    }

    .footer-last .follow_us {
        display: block;
        margin-bottom: 10px
    }

    .footer-last {
        text-align: center;
        border-top: none
    }

    .courses-list {
        padding: 50px 0 0 0
    }
}

@media (max-width: 1200px) and (min-width:992px) {
    .navbar-searchbox {
        min-width: 240px !important
    }
}

@media (max-width: 991px) and (min-width:768px) {
.blueBgDiv .mobileOnly {
        display: none !important;
    }

    #filter_icon {
        display: block !important
    }

    #whiteheader-mob-logo {
        display: block
    }

    #whiteheader-logo {
        display: none
    }

    .navbar-default .container {
        width: 100vw !important
    }

    .navbar-default .navbar-toggle {
        display: block;
        padding: 9px 24px
    }

    #nav-collapse {
        display: none !important
    }

    .navbar-header {
        width: 100%
    }

    #search-button {
        background: 0 0;
        font-size: 24px;
        padding: 9px 16px;
        float: right;
        margin: 0;
        border: 0;
        color: #fff;
        z-index: 999
    }

    #search-button>img {
        width: 24px
    }

    .navbar>.container .navbar-brand {
        margin-left: 0
    }

    .banner-search {
        position: absolute;
        top: calc(50% + 100px);
        text-align: center;
        width: 80%;
        left: 10%;
        right: 10%
    }

    .explore-stream-div {
        margin-bottom: 0
    }

    .explore-stream-div p {
        margin-bottom: 0
    }

    .slider-menu {
        width: 40%
    }

    .slider-submenu {
        width: 40% !important
    }
}

.padding-right-zero {
    padding-right: 0
}

.padding-left-zero {
    padding-left: 0
}

::-webkit-scrollbar-track {
    background: #fff;
    width: 0;
    display: table-cell;
    vertical-align: middle;
    border: 1px solid rgba(0, 0, 0, .1)
}

::-webkit-scrollbar {
    width: 10px;
    background: 0 0
}

::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 0
}

.review-text-wrapper {
    margin: 28px 0 10px 0
}

.ajax-page-title {
    color: #fff;
    font-size: 18px;
    line-height: 24px;
    font-weight: 700
}

.sp-black-right,
.sp-blue-right,
.sp-blue-right-arrow,
.sp-close-button,
.sp-default-logo,
.sp-empty-star,
.sp-facebook,
.sp-filter-icon,
.sp-full-star,
.sp-gmu-logo,
.sp-gmu-white-logo,
.sp-green-tick,
.sp-half-star,
.sp-linkedin,
.sp-pdf-icon,
.sp-pop-tick,
.sp-red-right-arrow,
.sp-search-icon,
.sp-search-icon-small,
.sp-twitter,
.sp-white-left,
.sp-white-star,
.sp-youtube {
    display: inline-block;
    background: url(https://getmyuni.azureedge.net/assets/images/mega_sprite.png) no-repeat;
    overflow: hidden;
    text-indent: -9999px;
    text-align: left
}

.sp-gmu-logo {
    background-position: -2px 0;
    width: 138px;
    height: 33px
}

.sp-gmu-white-logo {
    background-position: -2px -34px;
    width: 138px;
    height: 33px
}

.sp-default-logo {
    background-position: -2px -68px;
    width: 60px;
    height: 60px
}

.sp-pdf-icon {
    background-position: -64px -68px;
    width: 50px;
    height: 50px
}

.sp-facebook {
    background-position: -64px -120px;
    width: 31px;
    height: 31px
}

.sp-linkedin {
    background-position: -96px -120px;
    width: 31px;
    height: 31px
}

.sp-twitter {
    background-position: -2px -130px;
    width: 31px;
    height: 31px
}

.sp-youtube {
    background-position: -34px -152px;
    width: 31px;
    height: 31px
}

.sp-empty-star {
    background-position: -66px -152px;
    width: 24px;
    height: 24px
}

.sp-filter-icon {
    background-position: -92px -152px;
    width: 24px;
    height: 24px
}

.sp-full-star {
    background-position: -2px -162px;
    width: 24px;
    height: 24px
}

.sp-half-star {
    background-position: -66px -178px;
    width: 24px;
    height: 24px
}

.sp-search-icon {
    background-position: -92px -178px;
    width: 24px;
    height: 24px
}

.sp-black-right {
    background-position: -116px -68px;
    width: 15px;
    height: 15px
}

.sp-blue-right {
    background-position: -116px -85px;
    width: 15px;
    height: 15px
}

.sp-close-button {
    background-position: -116px -102px;
    width: 15px;
    height: 15px
}

.sp-pop-tick {
    background-position: -34px -130px;
    width: 15px;
    height: 15px
}

.sp-white-left {
    background-position: -118px -152px;
    width: 15px;
    height: 15px
}

.sp-white-star {
    background-position: -118px -169px;
    width: 15px;
    height: 15px
}

.sp-search-icon-small {
    background-position: -28px -184px;
    width: 14px;
    height: 14px
}

.sp-green-tick {
    background-position: -44px -184px;
    width: 12px;
    height: 12px
}

.sp-blue-right-arrow {
    background-position: -51px -130px;
    width: 10px;
    height: 10px
}

.sp-red-right-arrow {
    background-position: -118px -186px;
    width: 10px;
    height: 10px
}

.sp-rotate-90 {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}

#modal_left_image {
    width: 99%
}

.facilities-auditorium,
.facilities-cafeteria,
.facilities-campus,
.facilities-canteen,
.facilities-classrooms,
.facilities-computer-labs,
.facilities-e-classroom,
.facilities-gym,
.facilities-hostel,
.facilities-laboratory,
.facilities-library,
.facilities-medical,
.facilities-sports,
.facilities-swimming-pool,
.facilities-transport,
.facilities-wifi {
    display: inline-block;
    background: url(https://getmyuni.azureedge.net/assets/images/facilities_sprite.png) no-repeat;
    overflow: hidden;
    text-indent: -9999px;
    text-align: left
}

.facilities-auditorium {
    background-position: 0 0;
    width: 50px;
    height: 50px
}

.facilities-cafeteria {
    background-position: -50px 0;
    width: 50px;
    height: 50px
}

.facilities-campus {
    background-position: -100px 0;
    width: 50px;
    height: 50px
}

.facilities-canteen {
    background-position: -150px 0;
    width: 50px;
    height: 50px
}

.facilities-classrooms {
    background-position: 0 -50px;
    width: 50px;
    height: 50px
}

.facilities-computer-labs {
    background-position: -50px -50px;
    width: 50px;
    height: 50px
}

.facilities-e-classroom {
    background-position: -100px -50px;
    width: 50px;
    height: 50px
}

.facilities-gym {
    background-position: -150px -50px;
    width: 50px;
    height: 50px
}

.facilities-hostel {
    background-position: 0 -100px;
    width: 50px;
    height: 50px
}

.facilities-laboratory {
    background-position: -50px -100px;
    width: 50px;
    height: 50px
}

.facilities-library {
    background-position: -100px -100px;
    width: 50px;
    height: 50px
}

.facilities-medical {
    background-position: -150px -100px;
    width: 50px;
    height: 50px
}

.facilities-sports {
    background-position: 0 -150px;
    width: 50px;
    height: 50px
}

.facilities-swimming-pool {
    background-position: -50px -150px;
    width: 50px;
    height: 50px
}

.facilities-transport {
    background-position: -100px -150px;
    width: 50px;
    height: 50px
}

.facilities-wifi {
    background-position: -150px -150px;
    width: 50px;
    height: 50px
}

.h4_table th[colspan] {
    margin-top: 0;
    padding: 0 0 15px 0 !important
}

.review-card-5 .reviewer-info {
    margin-bottom: 15px !important
}

.review-card-5 .review-text-wrapper {
    margin-top: 15px;
    margin-bottom: 15px
}

.review-card-5 .category-wrapper .category-name {
    margin-bottom: 0 !important
}

.review-card-5 .review-text-wrapper ul {
    margin: 0
}

.button_to_link {
    background: 0 0 !important;
    color: #4374b9;
    border: none;
    padding: 0 !important;
    font: inherit;
    cursor: pointer;
    text-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none
}

.see-all-branches-button {
    background: #4374b9;
    border-radius: 18px;
    padding: 3px 12px;
    font-weight: 700;
    color: #fff;
    margin-top: -7px
}

.see-all-branches-button:hover {
    color: #fff
}

.see-all-branches-button:focus {
    outline: 0 !important
}

.svg-gmu-logo {
    width: 138px;
    height: 33px
}

.svg-gmu-white-logo {
    width: 138px;
    height: 33px
}

.svg-default-logo {
    width: 80px;
    height: 80px
}

.svg-pdf-icon {
    width: 50px;
    height: 50px
}

.svg-facebook {
    width: 30px;
    height: 30px
}

.svg-linkedin {
    width: 30px;
    height: 30px
}

.svg-twitter {
    width: 30px;
    height: 30px
}

.svg-youtube {
    width: 30px;
    height: 30px
}

.svg-empty-star {
    width: 24px;
    height: 24px
}

.svg-filter-icon {
    width: 24px;
    height: 24px
}

.svg-full-star {
    width: 24px;
    height: 24px
}

.svg-half-star {
    width: 24px;
    height: 24px
}

.svg-search-icon {
    width: 24px;
    height: 24px
}

.svg-black-right {
    width: 15px;
    height: 15px
}

.svg-blue-right {
    width: 15px;
    height: 15px
}

.svg-close-button {
    width: 15px;
    height: 15px
}

.svg-pop-tick {
    width: 15px;
    height: 15px
}

.svg-white-left {
    width: 15px;
    height: 15px
}

.svg-white-star {
    width: 15px;
    height: 15px
}

.svg-search-icon-small {
    width: 14px;
    height: 14px
}

.svg-green-tick {
    width: 12px;
    height: 12px
}

.svg-blue-right-arrow {
    width: 10px;
    height: 10px
}

.svg-red-right-arrow {
    width: 10px;
    height: 10px
}

.svg-white-down-arrow {
    width: 11.5px;
    height: 9px
}

.review_button {
    background: #ee424f;
    font-size: 14px;
    padding: 6px 34px;
    color: #fff;
    font-weight: 700;
    margin: 5px 0;
    display: inline-block;
    text-align: center
}

.review_button:hover {
    text-decoration: none;
    color: #fff;
    cursor: pointer
}

.compare_button {
    background: #4374b9;
    font-size: 14px;
    padding: 6px 25px;
    color: #fff;
    font-weight: 700;
    margin: 5px 0;
    display: inline-block;
    text-align: center
}

.compare_button:hover {
    text-decoration: none;
    color: #fff;
    cursor: pointer
}

.fee_heading {
    margin-bottom: 15px
}

#breadcrumb-wrapper-div li {
    display: inline
}

.faqsection {
    margin-top: 30px;
}

.faqsection .card,
.faqsection .card:hover {
    background-color: white;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    transition: 0.3s;
}

.faqsection .card-body .answer-data {
    padding: 0 !important;
    padding-left: 30px !important;
}

.answer-data p {
    margin: 0 !important;
    font-size: 14px;
}

.carousel_section h2 {
    color: #000 !important;
}

.read-more-div {
    max-height: 100px !important;
    overflow: hidden;
    position: relative;
    padding-bottom: 0px;
}

.read-more-div:after {
    background: linear-gradient(hsla(0, 0%, 100%, 0) 5%, #fff);
    position: absolute;
    bottom: 0;
    display: block;
    height: 80px;
    width: 100%;
}

@media (min-width: 991px) {
.blueBgDiv .mobileOnly {
        display: none !important;
    }

    .fee_heading_detail {
        background: #fff;
        color: #444;
        padding: 45px 40px;
        font-size: 14px
    }

    .fee_heading_detail img {
        display: block;
        margin: 0 auto
    }

    .fee_heading_detail>.fee_clg_detail>h1 {
        font-weight: 700;
        line-height: 32px;
        margin: 0;
        font-size: 21px;

    }

    .fee_clg_detail {
        padding-right: 30px;
        padding-left: 30px
    }

    .fee_heading {
        background: linear-gradient(45deg, rgba(67, 116, 185, .9), rgba(238, 66, 79, .9));
        padding: 80px 0 0 0;
        color: #444
    }

    .address_full {
        font-size: 15px;
        padding: 10px 0 9px 0;
        border-bottom: 1px solid #efefef;
        margin-bottom: 10px
    }

    .col-left {
        float: left;
        width: 91px
    }

    .col-center {
        float: left;
        width: calc(100% - 182px - 91px)
    }

    .col-right {
        float: left;
        width: 182px
    }
}

@media (max-width: 990px) {
.blueBgDiv .mobileOnly {
        display: none !important;
    }

    .compare_button,
    .review_button {
        font-size: 14px;
        padding: 5px 15px;
        margin: 10px 5px
    }

    .fee_heading_detail>.fee_clg_detail>h1 {
        font-size: 1.2em;
        font-weight: 700;
        margin: 0;
        line-height: 22px
    }

    .container-mobile,
    .fee_heading_detail {
        padding: 0 !important
    }

    .fee_clg_detail {
        padding: 0 !important
    }

    .fee_heading {
        padding-top: 85px !important;
        background: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
        background: -o-linear-gradient(45deg, #4374b9, #ee424f);
        background: linear-gradient(45deg, #4374b9, #ee424f);
        color: #fff;
        margin-top: -72px !important;
        padding-bottom: 15px;
        margin-bottom: 0;
        text-align: center
    }

    .fee_heading_img {
        margin: auto;
        margin-bottom: 10px;
        width: 80px
    }

    .pro-pica-mobile {
        border: 2px solid #e6e6e6;
        border-radius: 50px;
        width: 80px;
        height: 80px;
        text-align: center
    }

    .rating-p {
        display: inline-block;
        padding: 5px 0 0 0
    }

    .rating-based {
        font-size: .9em
    }
}

@media (max-width: 767px) {

    .container-mobile,
    .fee_heading_detail {
        padding: 0 !important;
        margin: 0 !important
    }
}


a,
p,
h1,
h2,
h3,
h4,
h5,
h6,
b {
        font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;

}

.card h2 {
    color: #4374b9;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
}

.college-counseling-modal .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #7B7B7B !important;
    font-size: 14px !important;
}

.college_counseling_modal_new #lead-modal-right-div #leadForm select#current_location,
.college_counseling_modal_new #lead-modal-right-div #leadForm .select2-container {
    width: 100% !important;
    max-height: 16px !important;
}

#leadForm span>span.selection>span {
    outline: none;
    border: none;
}

.college-counseling-modal .select2-container--default .select2-selection--single {
    border: none;
    border-bottom: solid 1px #7B7B7B;
    border-radius: 0px;
}

/* Abroad College page - Style Sheet */
p {
    margin: 0;
}

.card {
    margin: initial;
    overflow: initial;
    padding: initial;
}

main * {
    outline: 0;
}

.top_menu>.nav_top {
    position: initial;
}

.top_section p {
    font-size: 16px;
}

.top_section .search {
    margin-top: 26px;
    position: relative;
}

.top_section .search>input::placeholder {
    color: #919191;
    opacity: 1;
}

.top_section .search>input:-ms-input-placeholder {
    color: #919191;
}

.top_section .search>input::-ms-input-placeholder {
    color: #919191;
}

.ratechanceBtn {
    background-color: #00c853;
    border: 0;
    color: white;
    font-size: 15px;
    font-weight: bold;
    padding: 10px 10px;
    border-radius: 3px;
    display: flex;
    margin: auto;
}

.btn_help {
    background-color: #EE424F;
    border: 0;
    color: white;
    font-size: 15px;
    padding: 10px 10px;
    border-radius: 3px;
}

.top_section .btn_help {
    margin-top: 50px;
}

.top_section .girl {
    float: right;
    position: relative;
    top: -200px;
    height: 400px;
    width: 400px;
}

.ui-autocomplete {
    max-height: 300px;
    overflow-x: hidden;
    overflow-y: auto;
    z-index: 999 !important;
    text-align: left;
    width: 40%;
}

.content-haeding {
    padding: 10px 26px 0px;
}

.gmu-ad.container {
    text-align: center;
    margin-top: 12px;
}

@media only screen and (min-width : 900px) {
.blueBgDiv .mobileOnly {
        display: none !important;
    }

    .top_section>* {
        max-width: 50%;
    }

    .middle_section>img {
        margin-right: 144px;
    }

    .background_top {
        top: 4% !important;
    }

    .top_section {
        color: white;
        padding-left: 44px;
    }

    .top_section .search>input {
        padding: 12px 0 13px 18px;
        border: 0;
        border-radius: 30px;
        color: black;
        width: 82%;
    }

    .top_section h1 {
        font-size: 36px;
        font-weight: bold;
        margin-top: 70px;
    }

    .top_section .search>button {
        background-color: #4374B9;
        border: 0;
        border-radius: 30px;
        padding: 10px 20px;
        position: absolute;
        right: 18%;
        top: 50%;
        -moz-transform: translateX(-3%) translateY(-51%);
        -webkit-transform: translateX(-3%) translateY(-51%);
        transform: translateX(-3%) translateY(-51%);
    }

    .country_description_text {
        padding: 75px;
    }
}

.background_top {
    position: absolute;
    top: 0%;
    left: 0;
    width: 100%;
    z-index: -1;
}

.middle_section {
    display: flex;
}

.middle_section>* {
    align-self: center;
    flex: 1;
}

.middle_section>div {}

.middle_section .btn_help {
    margin-top: 53px;
}


.carousel_section {
    margin-top: 30px;
    position: relative;
    overflow: hidden;
}

.carousel_section h2 {
    font-size: 30px;
    font-weight: bold;
    text-align: center;
}

.carousel_section .carousel_container {
    margin-top: 35px;
    position: relative;
}

.carousel_section .slide_container {
    display: block;
    white-space: nowrap;
    overflow-x: scroll;
    scrollbar-width: none;
}

.carousel_section .slide_container::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
}

.slide_container .box_container {
    background-color: white;
    display: inline-block;
    width: 25.85%;
    text-align: center;
    margin: 0 7px;
}

.slide_container .box_container.more {
    background: transparent !important;
    border: 0 !important;
    box-shadow: none !important;
    position: relative;
}

.slide_container .box_container.more>* {
    background: transparent;
    visibility: hidden;

}

.slide_container .box_container.more>.btn_show_more {
    background-color: #EE424F;
    border: 0;
    color: #FFFFFF;
    padding: 14px 66px;
    position: absolute;
    top: 50%;
    left: 50%;
    -moz-transform: translateX(-50%) translateY(-50%);
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    visibility: initial;
}

.slide_container .box_container:first-child {
    margin-left: 122px;
}

.slide_container .box_container:last-child {
    margin-right: 122px;
}

.slide_container .box_container .img_container {
    position: relative;
    background: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background: linear-gradient(45deg, #4374b9, #ee424f);
    height: 100%;
}

.slide_container .box_container .img_container>.background {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    max-width: 100%;
    filter: brightness(50%);
    display: block;
    height: 175px;
    margin-bottom: 5px;
}

.slide_container .box_container .img_container>.logo {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    bottom: 0;
    border: 1px solid black;
    height: 120px;
    padding: 12px 24px 13px 15px;
    position: absolute;
    left: 50%;
    -moz-transform: translateX(-50%) translateY(50%);
    -webkit-transform: translateX(-50%) translateY(50%);
    transform: translateX(-50%) translateY(50%);
    width: 126px;
}

.slide_container .box_container>.title {
    font-size: 20px;
    font-weight: bold;
    margin-top: 80px;
    white-space: normal !important;
    height: 60px;
}

.slide_container .box_container>.location {
    font-size: 17px;
    font-weight: bold;
    margin-top: 7px;
}

.slide_container .box_container .course_container {
    display: flex;
    margin-top: 36px;
}

.slide_container .box_container .course_container>.course {
    flex: 1;
}

.slide_container .box_container .course_container>.course>.title {
    color: #4374B9;
    font-size: 14px;
    font-weight: bold;
}

.slide_container .box_container .course_container>.course>.count {
    font-size: 12px;
    font-weight: bold;
}

.slide_container .box_container .btn_apply {
    margin-top: 20px;
}

.btn_apply {
    background-color: #4374B9;
    border: 0;
    color: white;
    font-size: 18px;
    font-weight: bold;
    padding: 13px 0 14px 0;
    text-align: center;
    width: 100%;
}

.btn_carousel_left,
.btn_carousel_right {
    display: flex;
    background-color: #707070;
    color: white;
    cursor: pointer;
    font-size: 50px;
    font-weight: bold;
    height: 100%;
    opacity: 0.53;
    position: absolute;
    text-align: center;
    top: 0;
    user-select: none;
    width: 122px;
    z-index: 1;
}

.btn_carousel_left>.icon,
.btn_carousel_right>.icon {
    align-self: center;
    margin: 0 auto;
    width: 35px;
}

.btn_carousel_left {
    left: 0;
}

.btn_carousel_right {
    right: 0;
}

.slide_container .box_container .course_container>.course>.count {
    font-size: 12px;
    font-weight: bold;
}


.column_section {
    background-color: white;
    display: flex;
    flex-wrap: wrap;
    margin-top: 80px;
}

.column_section>p {
    padding: 54px 15px;
    text-align: center;
    width: 50%;
}


.article_section>h2 {
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    margin-top: 80px;
}

.article_container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 40px;
}

.article_container>.box_container {
    background-color: white;
    flex: 1 1 30%;
    position: relative;
    padding: 6px 6px 32px;
    margin: 6px 6px;
    justify-content: space-between;
}

.article_container>.box_container>.background {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    display: block;
    height: 160px;
    margin-bottom: 5px;
    width: 100%;
}

.article_container>.box_container>.content {
    font-size: 12px;
    margin: 32px 26px 0 26px;
}

.article_container>.box_container>.read_more {
    color: #4374B9;
    font-size: 14px;
    position: absolute;
    bottom: 0;
    right: 0;
    padding-right: 17px;
    text-decoration: none;
    cursor: pointer;
}

.article_section .view_all {
    text-align: center;
    margin-top: 91px;
}

.btn_view_all {
    background-color: #4374B9;
    border: none;
    color: white;
    font-size: 18px;
    padding: 14px 40px 14px 41px;
}


.bottom_section {
    margin-top: 80px;
}

.bottom_section>.resource {
    font-size: 30px;
    font-weight: bold;
}

.bottom_section>.right_container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 50px;
    margin-bottom: 23px;
}

.bottom_section>.right_container>.icon_text {
    display: flex;
    align-items: center;
}

.bottom_section>.right_container>.icon_text>.icon {
    height: 26px;
    width: 26px;
    margin-right: 7px;
}

.bottom_section>.right_container>.icon_text>.content {
    color: #4374B9;
    font-size: 18px;
    font-weight: bold;
}


.card {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: .25rem;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    transition: 0.3s;
}

.card:hover {
    box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 0.2);
}

.slide_container .box_container.card:hover {
    box-shadow: 0 90px 90px 0 rgba(0, 0, 0, 0.2);
}


@media only screen and (max-width : 1280px) {
    .slide_container .box_container {
        width: 40%;
    }
}

@media only screen and (max-width : 992px) {
    .blueBgDiv .mobileOnly {
        display: none !important;
    }

    #mobile-girl {
        display: inline-block !important;
    }

    #desktop-girl {
        display: none !important;
    }

    .background_top {
        /*top:8%!important;*/
    }

    .top_section .girl {
        top: initial !important;
        height: auto !important;
        width: auto !important;
        right: 5px;
        padding-top: 10px;

    }

    .top_section>h1,
    .top_section>p {
        text-align: center;
    }

    .top_section h1 {
        font-size: 20px;
        font-weight: bold;
    }

    .top_section p {
        font-size: 10px;
    }

    .top_section {
        color: white;
        padding: 0px 15px;
    }

    .top_section .search>input {
        padding: 7px 0 10px 11px;
    }

    .btn_help {
        font-size: 12px;
        padding: 8px 10px;
    }

    .top_section img {
        position: initial;
        top: initial;
        height: 126px;
        width: 141px;
        margin-top: 32px;
    }

    .top_section .search>input {
        border: 0;
        border-radius: 30px;
        color: black;
        width: 100%;
    }

    .middle_section {
        display: table;
        text-align: center;
    }

    .middle_section>.img_container {
        margin: 0 auto;
        width: 90%;
    }

    .middle_section>div {
        margin-top: 31px;
    }

    .middle_section .btn_help {
        margin-top: 22px;
    }

    .carousel_section {
        margin-top: 60px;
    }

    .carousel_section>h2 {
        font-size: 16px;
    }

    .carousel_section>.carousel_container {
        margin-top: 20px;
    }

    .slide_container .box_container {
        width: 65%;
    }

    .slide_container .box_container:first-child {
        margin-left: 56px;
    }

    .slide_container .box_container .img_container>.background {
        height: 138px;
        margin-bottom: 2px;
    }

    .slide_container .box_container .img_container>.logo {
        width: 72px;
        height: 68px;
    }

    .slide_container .box_container>.title {
        font-size: 18px;
        margin-top: 40px;
    }

    .slide_container .box_container>.location {
        font-size: 15px;
        margin-top: 4px;
    }

    .slide_container .box_container .course_container {
        margin-top: 23px;
    }

    .slide_container .box_container .course_container>.course>.title {
        font-size: 10px;
    }

    .slide_container .box_container .course_container>.course>.count {
        font-size: 9px;
    }

    .slide_container .box_container .btn_apply {
        margin-top: 29px;
    }

    .btn_carousel_left,
    .btn_carousel_right {
        font-size: 25px;
        padding: 0 12px;
        width: inherit;
    }

    .btn_carousel_left>.icon,
    .btn_carousel_right>.icon {
        width: 13px;
    }

    .btn_apply {
        font-size: 9px;
        padding: 8px 0;
    }

    .column_section {
        margin: 35px 15px 0 15px;
        max-width: 90%;
    }

    .column_section>p {
        padding: 53px 9px;
        width: inherit;
    }

    .column_section>p:last-child {
        padding-bottom: 23px;
    }

    .article_section {
        max-width: 90%;
        padding: initial;
    }

    .article_section>h2 {
        font-size: 16px;
        margin-top: 45px;
    }

    .article_section>.article_container {
        margin-top: 17px;
    }

    .article_container>.box_container {
        flex: 1 1 47.5%;
    }

    .article_container>.box_container>.content {
        margin: 15px 23px 0 23px;
    }

    .article_container>.box_container>.read_more {
        bottom: 10px;
        font-size: 10px;
    }

    .article_section .view_all {
        margin-top: 34px;
    }

    .btn_view_all {
        font-size: 10px;
        padding: 8px 23px 7px 24px;
    }

    .bottom_section {
        margin-top: 64px;
    }

    .bottom_section>.resource>p {
        font-size: 16px;
    }

    .bottom_section>.right_container>.icon_text>.icon {
        height: 11px;
        width: 11px;
        margin-right: 10px;
    }

    .bottom_section>.right_container>.icon_text>.content {
        font-size: 9px;
    }

    .top_section h1 {
        font-size: 20px;
        font-weight: bold;
        margin-top: 30px;
    }

    .top_section .search>button {
        background-color: #4374B9;
        border: 0;
        border-radius: 30px;
        padding: 5px 12px 8px 13px;
        position: absolute;
        right: 0%;
        top: 50%;
        -moz-transform: translateX(-3%) translateY(-51%);
        -webkit-transform: translateX(-3%) translateY(-51%);
        transform: translateX(-3%) translateY(-51%);
    }

    .gmu-ad.container {
        margin-top: 12px;
    }
}