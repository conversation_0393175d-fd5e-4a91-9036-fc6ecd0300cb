* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

h1,
h2,
h3,
h4,
h5 {
    padding: 0;
    margin: 0;
    font-weight: 600;
    line-height: normal;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

body,
p {
    font-family: "Roboto", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    font-variation-settings: "wdth"100;
    color: #000000;
    line-height: 1.5;
}

h1 {
    font-size: 50px;
    font-weight: 700;
}

h2 {
    font-size: 36px;
    font-weight: 600;
}

.button-style {
    background: #EE424F;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.section-space {
    margin-top: 70px;
}

/* header */
.container {
    max-width: 1280px;
    width: 100%;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
}


header {
    text-align: center;
    padding: 20px 15px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* banner */
.hero-banner {
    height: auto;
    background: url(/../yas/images/clp/generic/banner-img.jpg);
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40px 0;
    background-repeat: no-repeat;
    background-size: cover;
}

.hero-banner .banner-overlay {
    background: linear-gradient(90deg, rgba(13, 63, 100, 0.9) 11%, rgba(26, 127, 202, 0.4) 71%);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.hero-banner .container {
    display: flex;
    justify-content: space-between;
}

.hero-banner .banner-left-content {
    max-width: 490px;
    width: 100%;
    color: white;
}

.hero-banner .banner-left-content h1 {
    font-size: 50px;
    line-height: 56px;
    font-weight: 700;
}

.hero-banner .banner-left-content h1>span {
    color: #EE424F;
}

.hero-banner .banner-left-content .top-college {
    margin-top: 10px;
}

.hero-banner .banner-left-content .top-college>span {
    background: url(/../yas/images/clp/generic/top-college-bg.png) top left no-repeat;
    width: 417px;
    height: 56px;
    color: #0D3F64;
    font-weight: 700;
    font-size: 28px;
    line-height: 50px;
    display: inline-block;
    padding: 0 10px;
    position: relative;
    background-size: cover;
}

.hero-banner .banner-left-content .jobready-block {
    margin-top: 20px;
}

.hero-banner .banner-left-content .jobready-block .jobready-head {
    font-size: 20px;
    line-height: 30px;
    font-weight: 600;
}

.hero-banner .banner-left-content .jobready-block>ul {
    list-style: none;
    padding: 0;
    margin: 8px 0 0;
}

.hero-banner .banner-left-content .jobready-block>ul>li {
    position: relative;
    padding-left: 40px;
    margin-bottom: 10px;
    font-weight: 400;
    font-size: 16px;
}

.hero-banner .banner-left-content .jobready-block>ul>li>span {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    background-color: #EE424F;
    border-radius: 2px;
}

.hero-banner .banner-left-content .jobready-block>ul>li>span::after {
    content: '';
    position: absolute;
    left: 3px;
    top: 5px;
    width: 10px;
    height: 4px;
    border-left: 2px solid white;
    border-bottom: 2px solid white;
    transform: rotate(-45deg);
}

.hero-banner .banner-right-content {
    max-width: 368px;
    width: 100%;
    background-color: white;
    padding: 30px 24px;
    border-radius: 4px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.hero-banner .banner-right-content .form-head {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
}

.hero-banner .form-body-block {
    margin-top: 20px;
}

.hero-banner .form-body-block .banner-form-inner {
    position: relative;
    margin-bottom: 16px;
}

.hero-banner .form-body-block .banner-form-inner .form-error {
    position: absolute;
    display: block;
    width: 100%;
    bottom: -17px;
    left: 0;
    font-size: 11px;
    color: #F8382A;
}

.select2-container {
    width: 100% !important;
}

.select2-container .select2-selection--single {
    height: 44px !important
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding: 8px 20px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 7px !important;
    right: 8px !important;
}

.select2-container--default .select2-selection--single {
    border-radius: 8px !important;
}

.custom-select-box {
    position: relative;
}

.clpCourse {
    background-color: #fff;
    ;
    position: relative;
    appearance: none;
}

.custom-select-box::after {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #888;
    right: 14px;
    top: 20px;
}

.hero-banner .form-body-block input,
.clpCourse {
    border: 1px solid #ADB5BD;
    border-radius: 8px;
    width: 100%;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    padding: 10px 20px;
}

.hero-banner .form-body-block input:focus {
    border-color: #F5A623;
}

.hero-banner .form-body-block input:focus-visible {
    outline: none;
}

.hero-banner .submit-btn {
    background-color: #EE424F;
    border-radius: 8px;
    width: 100%;
    margin-top: 10px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    border: 0;
    padding: 10px;
    color: white;
    cursor: pointer;
}

.input-error {
    border-color: #F8382A !important;
}

/* Why GetMyUni? */
.why-getmyuni {
    position: relative;
}

.why-getmyuni h2 {
    text-align: center;
}

.why-getmyuni-inner {
    display: flex;
    justify-content: space-between;
    column-gap: 90px;
    margin-top: 40px;
}

.why-getmyuni-inner .detail-block {
    max-width: 33.33%;
    width: 100%;
    position: relative;
    flex: 1;
    padding: 0px 17px;
    margin:0 20px;
    display: flex !important;
    justify-content: center;
    flex-wrap: wrap;
}

.why-getmyuni-inner .detail-block .img-block {
    text-align: center;
    margin-bottom: 30px;
}

.why-getmyuni-inner .detail-block .text-block {
    text-align: center;
    font-size: 24px;
    line-height: 30px;
    font-weight: 500;
}

.why-getmyuni .explore-now-btn {
    text-align: center;
}

/* Programs Offered */
.programs-offered {
    background: url(/../yas/images/clp/generic/bg-programs.jpg) no-repeat center;
    padding-top: 70px;
    padding-bottom: 70px;
    background-size: cover;
}

.programs-offered h2 {
    text-align: center;
}

.programs-offered h2>span {
    line-height: 24px;
    font-size: 18px;
    font-weight: 400;
    display: block;
    margin-top: 10px;
}

.programs-offered-slider {
    margin-top: 30px;
    display: none;
}

.programs-offered-slider.slick-slider {
    margin-bottom: 0;
}

.programs-offered-slider.slick-slider .slick-list.draggable, .why-getmyuni-inner.slick-slider .slick-list.draggable {
    padding-bottom: 70px;
}

.programs-offered-slider.slick-slider .slick-dots, .why-getmyuni-inner.slick-slider .slick-dots {
    bottom: 20px;
}

.programs-offered-slider.slick-slider .slick-dots li, .why-getmyuni-inner.slick-slider .slick-dots li {
    width: 10px;
    height: 10px;
    list-style: none;
}

.programs-offered-slider.slick-slider .slick-dots li>button, .why-getmyuni-inner.slick-slider .slick-dots li>button {
    width: 10px;
    height: 10px;
    background-color: #999fa3;
    border-radius: 100%;
}

.programs-offered-slider.slick-slider .slick-dots li.slick-active>button, 
.why-getmyuni-inner.slick-slider .slick-dots li.slick-active>button {
    background-color: #0c65a6;
}

.programs-offered-slider.slick-slider .slick-dots li>button::before, .why-getmyuni-inner.slick-slider .slick-dots li>button::before {
    display: none;
}

.programs-card {
    width: 100%;
    position: relative;
    margin: 0 10px;
}

.programs-card .program-img {
    border-radius: 12px;
    overflow: hidden;
    width: 100%;
    height: 268px;
    /* box-shadow: 1px 103px 57px -54px rgba(0, 0, 0, 0.6); */
}

.programs-card .program-card-text {
    font-weight: 400;
    font-size: 22px;
    line-height: 30px;
    text-align: center;
    padding: 10px 0;
    color: white;
    border-radius: 12px;
    position: relative;
    /* margin: -40px auto 0; */
    /* max-width: 90%; */
    width: 100%;
}

.fit-content-height {
    display: flex !important;
    height: 100%;
    flex-wrap: wrap;
    background-color: #0D3F64;
    border-radius: 8px;
    padding: 10px;


}

.slick-slider .slick-track,
.slick-slider .slick-list {
    display: flex;
}

.programs-offered .fee-detail-button {
    text-align: center;
    margin-top: 10px;
}

/* our partner */
.our-partner-section h2 {
    text-align: center;
}

.our-partner-slider {
    margin-top: 30px;
    display: none;
}

.our-partner-slider.slick-slider .slick-track {
    display: flex;
    align-items: center;
}

.our-partner-slider .college-logo-block {
    margin: 0 20px;
}

.our-partner-slider .college-logo-block img {
    width: 100%;
    height: 100px;
    object-fit: scale-down;
}

.our-partner-section .talk-expert-button {
    text-align: center;
    margin-top: 20px;
}

.our-partner-section h2 {
    margin-bottom: 30px;
}

.our-partner-slider .slick-slide img {
    width: 100px !important;
    height: 100px !important;
}

/* Submit Your Application */
.submit-application-section {
    background: linear-gradient(90deg, rgba(13, 63, 100, 1) 60%, rgba(26, 127, 202, 1) 100%);
    padding: 50px 0;
}

.submit-application-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.submit-application-inner .submit-app-left {
    width: calc(100% - 395px);
    color: white;
    font-size: 28px;
    font-weight: 600;
    line-height: normal;
}

.submit-application-inner .submit-app-left>span {
    background-color: white;
    color: #0D3F64;
}

.submit-application-inner .submit-app-left>br {
    display: none;
}

.submit-application-inner .submit-app-btn {
    width: 305px;
}

.submit-application-inner .submit-app-btn>button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

/* footer */
footer {
    background-color: #212529;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    line-height: 54px;
}

/* for modal */
.overflow-hide {
    overflow: hidden;
}

.modal-overlay-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1;
    display: none;
    align-items: center;
    overflow-y: auto;
}

.modal-body {
    padding: 0;
    margin: auto;
    height: auto;
    max-width: 370px;
    width: 100%;
    background: white;
    border-radius: 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, .4);
    position: relative;
    z-index: 300;
    overflow-x: auto;
    padding: 25px;
    border-radius: 5px;
}

.modal-body .modal-head {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    color: #1E1E1E;
}

.modal-body .modal-head>span {
    font-size: 16px;
    font-weight: 400;
    display: block;
}

.modal-body .form-content {
    margin-top: 20px;
}

.modal-body .form-content .form-field-block {
    position: relative;
}

.modal-body .form-content .form-field-block .form-error {
    position: absolute;
    display: block;
    width: 100%;
    bottom: 0;
    left: 0;
    font-size: 11px;
    color: #F8382A;
}

.modal-body .form-content .banner-form-inner {
    margin-bottom: 16px;
}

.modal-body .form-content .field-style {
    border: 1px solid #ADB5BD;
    border-radius: 8px;
    width: 100%;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    padding: 12px 24px;
}

.modal-body .form-content .field-style:focus {
    border-color: #F5A623;
}

.modal-body .form-content .field-style:focus-visible {
    outline: none;
}

.modal-body .form-content .field-style.select-arrow {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url(/../yas/images/clp/generic/select-arrow.png) no-repeat right 10px top 21px;
}

.modal-button {
    font-size: 18px;
    font-weight: 600;
    background-color: #EE424F;
    width: 100%;
    border-radius: 8px;
    padding: 10px 0;
    border: none;
    cursor: pointer;
    color: white;
    margin-top: 10px;
}

#modal-close {
    font-size: 28px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    line-height: 10px;
    font-weight: 300;
}

.modal-overlay-box.show-modal {
    display: flex;
}

.page-header,
.pageFooter,
.scrollToTop {
    display: none !important;
}

#genericScreenSubmit:disabled,
#modalScreenSubmit:disabled {
    background-color: rgba(255, 78, 83, 0.4);
}

#sendCallerLeadToCld {
    display: none !important;
}

/*--sticky button--*/
.stick-button {
    padding: 15px 0;
}

.stick-button p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.button-blue {
    background-color: #0D3F64;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.whiteDownloadIcon,
.redDownloadIcon {
    width: 19px;
    height: 18px;
    background-position: 233px -354px !important;
    vertical-align: text-bottom;
    margin-right: 4px;
}

.spriteIcon {
    display: inline-block !important;
    background-image: url(../../images/master_sprite.webp);
    text-align: left;
    overflow: hidden;
}

.phoneIcon {
    width: 24px;
    height: 24px;
    background-position: 536px -246px;
    vertical-align: bottom;
    margin-right: 2px;
}

/* footer */
.footer {
    background-color: #212529;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    line-height: 54px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    font-size: 14px !important;
}

.form-subheading-generic {
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    display: block;
}

.why-getmyuni-inner>.detail-block:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 10%;
    right: -30px;
    width: 1px;
    height: 100%;
    background-image: linear-gradient(to bottom, #D8D8D8 36%, transparent 15%);
    background-size: 1px 17px;
    background-repeat: repeat-y;
}

/* media start here */
@media only screen and (max-width:1200px) {
    .why-getmyuni-inner .detail-block .text-block {
        font-size: 17px;
        line-height: 30px;
    }
    
    .why-getmyuni-inner .detail-block .img-block img {
        width: 68px;
    }

    .programs-card .program-card-text {
        font-size: 18px;
        line-height: 24px;
    }
}

@media only screen and (max-width:992px) {

    .stick-button span {
        display: none;
    }

    .stick-button {
        padding: 0;
    }

    .stick-button p {
        gap: 0;
    }

    .stick-button .button-style,
    .stick-button .button-blue {
        border-radius: 0;
        height: 45px;
        font-size: 14px;
    }

    .stick-button .button-style {
        width: 60%;
    }

    .stick-button .button-blue {
        width: calc(100% - 60%);
    }

    .hero-banner .container {
        column-gap: 40px;
    }

    .hero-banner .banner-left-content {
        max-width: 100%;
        width: calc(100% - 360px);
    }

    .hero-banner .banner-left-content h1 {
        font-size: 40px;
    }

    .hero-banner .banner-left-content .top-college>span {
        font-size: 24px;
        width: 100%;
        background-size: contain;
    }

    .hero-banner .banner-right-content {
        max-width: 100%;
        width: 333px;
    }

    .submit-application-inner {
        flex-direction: column;
        gap: 30px;
    }

    .submit-application-inner .submit-app-left {
        width: 90%;
        text-align: center;
    }

    .submit-application-inner .submit-app-left>br {
        display: block;
    }
}

@media only screen and (max-width:767px) {

    h2 {
        font-size: 30px;
    }

    .section-space {
        margin-top: 40px;
    }

    .hero-banner {
        align-items: flex-start;
        height: auto;
        padding: 10px 0;
    }

    .hero-banner .container {
        flex-direction: column;
    }

    .hero-banner .banner-left-content {
        width: 100%;
        padding: 40px 10px 0;
    }

    .hero-banner .banner-left-content h1 {
        text-align: center;
        font-size: 36px;
        line-height: 40px;
    }

    .hero-banner .banner-left-content .top-college {
        width: 325px;
        margin: 5px auto 0;
    }

    .hero-banner .banner-left-content .top-college>span {
        font-size: 20px;
        height: 45px;
        line-height: 40px;
        text-align: center;
    }

    .hero-banner .banner-left-content .jobready-block {
        width: 85%;
        margin: 20px auto;
    }

    .hero-banner .banner-left-content .jobready-block .jobready-head {
        font-size: 20px;
    }

    .hero-banner .banner-left-content .jobready-block>ul>li {
        line-height: 22px;
        margin-bottom: 7px;
    }

    .hero-banner .banner-right-content {
        width: 100%;
    }

    section.why-getmyuni.section-space {
        margin-top: 65px;
    }

    .slick-slide img {
        width: 100%;
    }

    .why-getmyuni-inner {
        flex-direction: column;
        row-gap: 40px;
    }

    .why-getmyuni-inner .detail-block {
        max-width: 100%;
        padding: 0px;
    }

    .why-getmyuni-inner>.detail-block:not(:last-child)::after {
        content: '';
        position: absolute;
        bottom: 10%;
        top: 110%;
        left: 10%;
        width: 80%;
        height: 1px;
        background-image: linear-gradient(to right, #D8D8D8 36%, transparent 15%);
        background-size: 17px 1px;
        background-repeat: repeat-x;
    }

    .programs-offered.section-space {
        margin-top: 0;
        padding-top: 40px;
        padding-bottom: 40px;
    }

    .programs-offered-slider .slick-list {
        padding: 0 10% 0 0;
    }

    .submit-application-section {
        padding: 40px 0;
    }

    .submit-application-inner .submit-app-left {
        font-size: 24px;
    }

    .programs-card .program-img {
        height: auto;
    }
}