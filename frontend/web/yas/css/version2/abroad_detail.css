body {
  background: #f4f4f4 !important;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}

a,
p,
h1,
h2,
h3,
h4,
h5,
h6,
b,
td,
th,
span,
button {
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;

}

.avg_tutn_fee_mob .cleantr th {
  background: transparent linear-gradient(360deg, #974774 0%, #4672B6 100%);
}

.college-counseling-modal .select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #7b7b7b !important;
  font-size: 14px !important;
}

.college_counseling_modal_new #lead-modal-right-div #leadForm select#current_location,
.college_counseling_modal_new #lead-modal-right-div #leadForm .select2-container {
  width: 100% !important;
  margin-bottom: -20px !important;
}

#leadForm span>span.selection>span {
  outline: none;
  border: none;
}

.college-counseling-modal .select2-container--default .select2-selection--single {
  border: none;
  border-bottom: solid 1px #7b7b7b;
  border-radius: 0px;
}

#menu-center {
  cursor: pointer;
}

#university-table-desktop td:first-child,
#admission-mobile-table td:last-child,
#top-recommendation-div td:first-child {
  border-right: unset;
}

#university-table-desktop p {
  color: #fff;
}

#admission-mobile-table,
#top-recommendation-div {
  overflow: hidden;
  border: none;
}

table td {
  min-width: unset;
}

.program-offered {
  flex-direction: column;
}

.admission-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.admission-label {
  flex: 1;
  max-width: 70%;
}

.admission-value {
  text-align: right;
  flex-shrink: 0;
  width: 5em;
}

#university table {
  border-collapse: collapse;
  /* Ensures no gaps between cells */
  border-spacing: 0;
  /* Removes extra spacing */
  width: 100%;
  /* Ensures full-width */
  border: none;
}

#university .table-responsive,
#admission .table-responsive {
  border: none;
}

#university tr.cleantr {
  border: none;
  /* Removes any borders from table rows */
}

#university td.university-td-data {
  border: none !important;
  /* Ensures no borders around table cells */
  padding: 20px 0px 13px 21px;
  /* Keeps the padding clean */
}

#university .table-white-img svg {
  box-shadow: none;
  /* Removes shadow from images */
}

#university-table-desktop td {
  border: none;
}

@media (max-width: 991px) {
  .tabs nav {
    width: 100% !important;
    flex-wrap: nowrap !important;
  }

  .combine-button {
    display: block;
    margin-bottom: 0px !important;
    margin-top: 0px !important;
  }

  .fall-table td {
    padding: 7px 9px 8px 8px !important;
  }

  .winnter-table td {
    padding: 7px 9px 8px 8px !important;
  }

  .no-inline {
    display: block !important;
  }

  .float-right {
    float: none !important;
  }

  .sa_tab {
    display: block !important;
  }

  .tabs .content {
    position: relative !important;
    width: 100% !important;
    left: 0px !important;
    transition: opacity 0.1s linear 0s;
    margin-left: 0px !important;
    margin-top: 15px;
  }

  .content-list-button {
    padding: 6px 20px 7px !important;
  }

  .spec-content-list-button {
    padding: 6px 12px 7px !important;
    margin: 5px;
  }

  .fall-table {
    width: 100% !important;
  }

  .winnter-table {
    width: 100% !important;
  }

  .col-md-10 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .avg_tutn_fee_mob {
    background: #ffffff !important;
  }

  .row {
    margin-right: 0px !important;
    margin-left: 0px !important;
  }
}

@media screen and (max-width: 992px) {

  /* top header */
  /*Mobile view*/
  #mobile {
    display: block !important;
  }

  #desktop {
    display: none !important;
  }

  /*.fix_navbar_style{
        background: transparent !important;
    }*/
  .fee_clg_detail {
    display: inline !important;
  }

  .pro-pica-mobile {
    border: 2px solid #e6e6e6;
    border-radius: 50px;
    width: 56px;
    height: 56px;
    text-align: center;
    margin: 3px;
  }

  .header_wrapper {
    padding-top: 70px !important;
    position: relative;
    background: -webkit-linear-gradient(45deg, #4374b9, #ee424f);
    background: -o-linear-gradient(45deg, #4374b9, #ee424f);
    background: linear-gradient(45deg, #4374b9, #ee424f);
    color: #fff;
    margin-top: -72px !important;
    padding-bottom: 15px;
    margin-bottom: 0;
    text-align: center;
    height: 285px !important;
  }

  .top_card_heading_detail {
    font-size: 22px !important;
    font-weight: bold;
    margin-bottom: 19px !important;
  }

  .mobile-inter-get {
    margin-top: 10px;
  }

  address {
    margin-top: 1px !important;
  }

  .mobile-card-img {
    display: inline-grid !important;
    left: -40px;
    position: relative;
    background-color: #fff;
    border-radius: 10px;
  }

  .intrested_button {
    background: #00c853;
    color: #ffffff;
    display: inline-block;
    box-shadow: 1px 1px 1px 1px #eeeeee73 !important;
    padding: 5px 12px !important;
    margin-right: 10px !important;
    font-size: 13px !important;
  }

  .get_more_info_button {
    background: #4374b9;
    color: #ffffff;
    display: inline-block !important;
    box-shadow: 1px 1px 1px 1px #eeeeee73 !important;
    padding: 5px 7px !important;
    font-size: 13px !important;
  }

  .clg-address {
    display: inline-grid !important;
    position: absolute !important;
    color: #fff !important;
    /* left:312px; */
    left: 47%;
    top: 168px;
  }

  .clg-loc {
    color: #fff !important;
    top: -20px;
    font-size: 12px;
  }

  .clog-card-date {
    position: relative;
    /* left: -94px; */
    right: 10%;
    left: -30px;
    top: 18px;
  }

  #clg-address-svg {
    bottom: 12px;
    position: absolute;
    left: -43px;
  }

  .clg-loc {
    position: relative;
    bottom: 19px;
    width: 266px;
    color: #707070;
  }

  #clg-date-svg {
    padding: 8px 1px 1px 8px !important;
    margin-right: 6px !important;
  }

  #header-div-id {
    background: url("banner image") center center / 500% no-repeat;
    height: 237px;
  }

  .top_card_heading {
    background: linear-gradient(45deg,
        rgba(67, 116, 185, 0.7),
        rgba(238, 66, 79, 0.7));
    padding: 0px !important;
    height: 237px;
  }

  /* top header ends */

  .display-webkit {
    display: block !important;
    padding: 10px 30px !important;
  }

  .model-table-width-fourty {
    width: 100% !important;
    margin: 0px auto !important;
  }

  .model-table-width-fourty.model-table-margin {
    margin-bottom: 20px !important;
  }

  .browse-by-mobile-inter {
    display: block !important;
  }

  .application-title {
    top: 0px !important;
  }

  .nav .sa_sub_menu>li>a {
    padding: 10px 15px;
  }

  .university-td-data {
    width: 50% !important;
    padding: 5px !important;
  }

  #admission-mobile-table {
    width: 100% !important;
  }

  #desktop-top-recommand {
    display: none !important;
  }

  #mobile-table-content {
    border: 1px solid #fff;
    border-radius: 15px;
    padding: 5px 20px;
  }

  .table .app-table-svg {
    bottom: -4px !important;
  }

  .tabs {
    display: block !important;
  }

  .sa_tab {
    width: 100% !important;
  }

  .sa_tab_menu {
    width: 100% !important;
    margin-top: 20px;
  }

  .carousel_section {
    margin-top: 60px;
  }

  .carousel_section>.carousel_container {
    margin-top: 20px;
  }

  .btn_carousel_left,
  .btn_carousel_right {
    font-size: 25px;
    padding: 0 12px;
    width: inherit;
  }

  .slide_container .box_container {
    width: 65% !important;
  }

  .slide_container .box_container:first-child {
    margin-left: 56px;
  }

  #mobile-table-menu {
    display: block !important;
  }

  td .fee-data {
    font-size: 13px !important;
  }

  td {
    font-size: 13px !important;
  }

  p {
    font-size: 13px !important;
  }

  #admission-mobile {
    display: block !important;
  }

  #admission-desktop {
    display: none !important;
  }

  .admission-margin-bottom {
    margin-top: 15px;
  }

  #university-table-desktop {
    display: none !important;
  }

  #university-table-mobile {
    display: block !important;
  }

  .fix_navbar_style {
    background: linear-gradient(45deg,
        rgb(67, 116, 185),
        rgb(238, 66, 79)) !important;
  }

  .menu-menu-item {
    padding: 3px 5px 0 0px !important;
  }

  #slick-slider-menu {
    padding: 0 5px !important;
  }

  .gmu-ad.container {
    margin-top: 12px;
  }
}

.tabs {
  display: inline-flex;
  position: relative;
}

.sa_tab_menu {
  width: 80%;
  max-height: 220px !important;
  overflow-y: auto;
}

.tabs nav {
  color: #fff;
  background: transparent linear-gradient(360deg, #974774 0%, #4672b6 100%);
  border-radius: 5px;
  box-shadow: 1px 2px 2px 2px #eee;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 200px;
}

.tabs nav a {
  padding: 20px 0px;
  text-align: center;
  width: 100%;
  cursor: pointer;
  color: #fff;
}

.tabs nav a:hover,
.tabs nav a.selected {
  background: #eee;
  color: #fff;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}

.tabs nav button {
  padding: 20px 0px;
  text-align: center;
  width: 100%;
  cursor: pointer;
  color: #fff;
  background-color: transparent;
  border: 0px;
}

.tabs nav button:hover,
.tabs nav button.selected {
  background: #eeeeee94;
  color: #fff;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}

.bws_by_pgrm_active {
  background: #eeeeee94 !important;
  color: #fff;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}

.float-right {
  float: right;
}

.tabs .content {
  padding: 20px 0px;
  position: absolute;
  box-shadow: 0px 3px 3px #d5d0d0;
  border-radius: 5px;
  top: 0px;
  left: 200px;
  color: #6c5d5d;
  height: 100%;
  overflow: hidden;
  /*opacity: 0;*/
  width: 57%;
  transition: opacity 0.1s linear 0s;
  margin-left: 14px;
}

.tabs .content.active {
  padding: 20px;
  opacity: 1;
}

.show-more {
  border: 2px solid #a09c9c;
  border-radius: 20px;
  padding: 6px 12px 4px;
  text-align: center;
}

.spec-content-list-button {
  /*border:0px solid #e2dbdb;
      background: transparent linear-gradient(360deg, #974774 0%, #4672B6 100%);
      border-radius: 20px;
      text-align: center;
      color:#fff;
      margin: 5px;*/
  padding: 6px 18px 6px;
  margin: 3px;
  font-size: 12px;
  letter-spacing: 0.8px;
  font-weight: 500;
  color: #000;
  background-color: #fff;
  border: none;
  font-size: 12px;
  border-radius: 45px;
  box-shadow: 0px 8px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease 0s;
  cursor: pointer;
  outline: none;
  margin-bottom: 11px;
  line-height: 15px;
}

button.spec-content-list-button:hover {
  box-shadow: 0px 15px 20px #e0cfd9;
  background: transparent linear-gradient(270deg, #974774 0%, #4672b6 100%) 0% 0% no-repeat padding-box;
  color: #fff;
  transform: translateX(-7px);
}

.spec-content-list-button_white {
  margin: 5px !important;
  border: 2px solid #e2dbdb !important;
  background: #ffffff !important;
  border-radius: 20px !important;
  padding: 6px 18px 6px !important;
  text-align: center !important;
  color: #000000 !important;
}

.view_all_courses {
  cursor: pointer;
  position: relative;
  padding: 9px 3px;
  border-radius: 30px;
  line-height: 20px;
  font-size: 13px;
  font-weight: 600;
  /*text-decoration: none;*/
  /* border: 1px solid #012880; */
  /* background-image: linear-gradient(-180deg, #FF89D6 0%, #C01F9E 100%); */
  background: transparent linear-gradient(360deg, #974774 0%, #4672b6 100%);
  box-shadow: 0 1rem 1.25rem 0 rgba(22, 75, 195, 0.5),
    0 -0.25rem 1.5rem rgba(110, 15, 155, 1) inset,
    0 0.75rem 0.5rem rgba(255, 255, 255, 0.4) inset,
    0 0.25rem 0.5rem 0 rgba(180, 70, 207, 1) inset;
  width: 136px;
  margin: 0px auto;
}

.view_all_courses_url:hover {
  text-decoration: none;
}

.view_all_courses a {
  color: transparent;
  background-image: linear-gradient(0deg, #ee82da 0%, #fefafd 100%);
  -webkit-background-clip: text;
  background-clip: text;
  filter: drop-shadow(0 2px 2px hsla(290, 100%, 20%, 1));
}

.view_all_courses::before {
  content: "";
  display: block;
  height: 0.25rem;
  position: absolute;
  top: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 7.5rem);
  background: #fff;
  border-radius: 100%;

  opacity: 0.7;
  background-image: linear-gradient(-270deg,
      rgba(255, 255, 255, 0) 0%,
      #ffffff 20%,
      #ffffff 80%,
      rgba(255, 255, 255, 0) 100%);
}

/* .DegreeButton {
  font-family: Open Sans !important;
} */

.content-list-button {
  border: 2px solid #a09c9c;
  border-radius: 20px;
  padding: 6px 36px 45px;
  text-align: center;
}

.content-button {
  margin-top: 20px;
  margin-bottom: 24px;
}

.combine-button {
  display: inline-block;
}

.winnter-table {
  width: 42%;
  display: inline-block;
  padding: 7px 9px 12px 13px;
}

.fall-table {
  width: 43%;
  display: inline-block;
  padding: 7px 9px 12px 13px;
}

.fall-table td {
  padding: 7px 9px 12px 21px;
  font-size: 1em;
  font-weight: 200;
}

.fall-table-svg {
  width: 50px;
  height: 50px;
}

.winnter-table td {
  padding: 6px 12px 13px 21px;
}

.tabs .content p {
  padding-bottom: 2px;
}

.tabs .content p:last-of-type {
  padding-bottom: 0px;
}

.h4_title_hrader {
  padding: 5px 0px;
  color: #a33b4d;
  font-weight: bold;
  margin-top: 10px;
  margin-bottom: 20px;
  font-size: 18px;
}

.col-3 {
  width: 230px;
  position: relative;
  min-height: 1px;
  padding-right: 0px;
  padding-left: 15px;
  float: left;
}

.col-9 {
  max-width: 100%;
  width: calc(100% - 230px);
  position: relative;
  min-height: 1px;
  padding-right: 0px;
  padding: 0px 15px;
  float: left;
  display: inline-flex;
  border-radius: 10px;
}

.menu-menu-items-a {
  text-decoration: none !important;
  cursor: pointer;
  color: #666;
  font-weight: 700;
  padding: 0 5px !important;
  outline: 0 !important;
}

.StreamButtons {
  border-radius: 20px;
  border: 1px solid #ffffff;
  padding: 5px 15px;
  color: #ffffff;
  background: transparent;
  margin: 5px;
  font-size: 13px;
}

.showButton {
  border-radius: 20px;
  border: 0px;
  padding: 5px 15px;
  color: #000;
  /*background: transparent linear-gradient(270deg, #974774 0%, #4672B6 100%) 0% 0% no-repeat padding-box;*/
  box-shadow: 0px 8px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease 0s;
}

button.showButton:hover {
  /* background-color: #4ee1e8; */
  box-shadow: 0px 15px 20px #e0cfd9;
  color: #000;
  background-color: #fff;
  transform: translateX(-7px);
}

.HeadtwoButton {
  /*margin: 5px;
      border-radius: 20px;
      border: 0px;
      padding: 5px 15px;
      color: #FFFFFF;
      background: transparent linear-gradient(270deg, #974774 0%, #4672B6 100%) 0% 0% no-repeat padding-box;*/
  padding: 10px;
  margin: 3px;
  font-size: 13px;
  letter-spacing: 0.8px;
  font-weight: 500;
  color: #000;
  background-color: #fff;
  border: none;
  border-radius: 45px;
  box-shadow: 0px 8px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease 0s;
  cursor: pointer;
  outline: none;
  margin-bottom: 11px;
  line-height: 15px;
}

button.HeadtwoButton:hover {
  /* background-color: #4ee1e8; */
  box-shadow: 0px 15px 20px #e0cfd9;
  background: transparent linear-gradient(270deg, #974774 0%, #4672b6 100%) 0% 0% no-repeat padding-box;
  color: #fff;
  transform: translateX(-7px);
}

.StreamButtons {
  border-radius: 20px;
  border: 1px solid #ffffff;
  padding: 5px 15px;
  color: #ffffff;
  background: transparent;
  margin: 5px;
}

.gmu-filter-button {
  background: none;
  padding: 0px 7px;
  margin-bottom: 17px;
  font-size: 13px;
  border: 2px solid #eeeeee;
  border-radius: 15px;
  padding: 2px 10px;
}

.filter-btn-active,
.filter-btn-active:hover,
.gmu-filter-button:active,
.gmu-filter-button:active:focus,
.gmu-filter-button:focus {
  border-color: #4374b9;
  color: #ffffff !important;
  background: #4374b9;
}

.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.box-2-border {
  background: -webkit-linear-gradient(90deg, #974774 0%, #4672b6 100%);
  border-radius: 10px;
  padding: 5px;
  margin-top: 10px;
}

.box-2-border-outside {
  background: -webkit-linear-gradient(90deg, #974774 0%, #4672b6 100%);
  border-radius: 10px;
  padding: 10px;
}

.box-2-border-inside {
  background-color: #ffffff;
  border-radius: 7px;
  padding: 20px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.4);
}

.other_fee_svg {
  padding: 12px;
  display: inline-block;
}

.other_fees_row {
  display: flex;
  /* font-family: Open Sans !important; */
}

.other_fees_vbox1 {
  width: 10%;
  padding: 8px 34px;
}

.other_fees_vbox2 {
  width: 70%;
  padding: 15px 15px;
}

.other_fees_vbox3 {
  width: 30%;
  font-weight: bold;
  padding: 15px 0px;
}

.box-2-wrapper {
  background: #ffffff;
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.4);
  margin-top: 15px;
  min-height: 130px;
}

.grad-box-1_bkup {
  background: transparent linear-gradient(270deg, #974774 0%, #4672b6 100%);
  margin-top: 10px;
  padding-bottom: 15px;
  border-radius: 10px;
}

.grad-box-1 {
  background: transparent linear-gradient(270deg, #974774 0%, #4672b6 100%);
  margin-top: 10px;
  border-radius: 10px;
  padding: 10px;
}

.app_details_events {
  font-size: 13px;
  font-weight: 200;
  border: none !important;
  padding: 12px;
}

.course-heading {
  color: #ffffff;
  padding-left: 5px;
  font-weight: bold;
  font-size: 16px;
}

.course-heading_bkup {
  color: #ffffff;
  padding: 10px;
  margin-top: 15px;
}

.headOne-head {
  padding: 5px 15px;
  color: #4d4d4d;
  font-weight: bold;
  font-size: 15px;
}

.model-headOne-fees {
  color: #868181;
  padding-left: 15px;
  margin-top: 15px;
  font-size: 13px;
  margin-bottom: 0px;
}

.headOne-fees {
  color: #868181;
  padding-left: 15px;
  margin-top: 15px;
  font-weight: bold;
  font-size: 13px;
}

.sessonTitle {
  text-align: center;
  color: #ffffff;
  background: transparent linear-gradient(270deg, #974774 0%, #4672b6 100%);
  padding: 10px 0px;
  border-radius: 4px;
  font-size: 15px;
  margin-bottom: 20px;
  font-weight: bold;
}

.sessonDate {
  /*box-shadow: 0px 3px 6px #00000029;*/
  color: #4d4d4d;
  padding: 5px 10px;
  border: 1px solid #ccc;
  font-weight: bold;
  /* border-radius: 8px 10px 0px 0px;*/
  margin: 0px;
}

.sessonDate.program {
  /*box-shadow: 0px 3px 6px #00000029;*/
  color: #4d4d4d;
  padding: 5px 10px;
  border: 1px solid #ccc;
  font-weight: bold;
  /* border-radius: 8px 10px 0px 0px;*/
  margin: 0px;
}

.interestedBtn {
  border-radius: 4px;
  font-weight: bold;
  color: #ffffff;
  background-color: #00c853;
  float: right;
  padding-right: 40px;
  border: 0px;
  padding: 5px 15px;
  margin-right: 40px;
}

.ratechanceBtn {
  background-color: #00c853;
  border: 0;
  color: white;
  font-size: 15px;
  font-weight: bold;
  padding: 5px 15px;
  border-radius: 3px;
  display: flex;
  margin: 10px auto;
}

.DegreeShowMore {
  border-radius: 20px;
  border: 1px solid #ffffff;
  padding: 5px 15px;
  color: #4772b5;
  background: #ffffff;
  margin: 5px;
}

.navbar ul li::not(.mainmenu) {
  list-style-type: none;
  margin-bottom: 10px;
}

.app-table {
  width: 87%;
}

.table .app-table-svg {
  width: 50px;
  height: 50px;
  position: relative;
  bottom: -16px;
}

.contact-circle {
  border-radius: 50px;
  border: 1px solid #7a6ea7;
  box-shadow: 2px 2px 2px #eee;
  padding: 2px 21px;
  margin-bottom: 16px;
  display: flex;
}

.contatc-icon-circle {
  border-radius: 50%;
  position: relative;
  top: 13px;
  display: inline-block;
  width: 143%;
  height: 12%;
  text-align: center;
}

.contact-info-container {
  display: inline-block;
}

.contact-info-container-l {
  display: inline-block;
  width: 20%;
}

.contact-info-container-r {
  display: inline-block;
  width: 80%;
  padding: 3px 0px;
  margin-top: 5px;
}

.contact-info-head-one {
  display: inline-block;
  font-size: 13px;
  color: #000;
  margin: 0px;
  padding-left: 20px;
  text-align: left;
  font-weight: 100;
}

.contact-info {
  font-size: 13px;
  color: #000;
  text-align: left;
  padding-left: 20px;
  font-weight: bold;
}

/*.border-table ,td ,p{
      border:none;
  }
  table ,td{
      border:none;
      background: none;
      padding: 12px 0px 13px 21px;
  }*/

.overview_text>h2 {
  font-size: 18px !important;
}

.overview_text>h3 {
  font-size: 16px !important;
}

.overview_text>h4 {
  font-size: 15px !important;
}

.nobordernobg {
  border: none;
  background: none;
  padding: 12px 0px 13px 21px;
}

.table-white-img {
  display: inline-block;
  padding-left: 25px;
  margin-right: 10px;
}

.margin-no {
  margin: 0px;
}

.display-inline {
  display: inline-block;
}

.p-font-size {
  font-weight: 200;
  font-size: 13px;
}

.s-font-size {
  font-size: 13px;
  font-weight: bold;
}

.background-white {
  background: #fff;
}

h4 {
  font-size: 24px;
}

.p-font {
  color: #000;
  font-weight: 100;
  font-size: 15px;
}

.p-font-s {
  font-weight: 500;
  color: #000;
  font-size: 17px;
  padding: 0px;
}

.p-font-s.text-left {
  text-align: left;
}

.padding-no {
  padding: 0px !important;
}

/*Dynamic Circle*/
.container_circle {
  width: 110px;
  height: 110px;
  /*margin: 100px auto;*/
}

.prec {
  top: 30px;
  position: relative;
  font-size: 30px;
}

.circle {
  position: relative;
  top: 5px;
  left: 5px;
  text-align: center;
  width: 100px;
  height: 100px;
  border-radius: 100%;
  background-color: #e6f4f7;
}

.active-border {
  position: relative;
  text-align: center;
  width: 110px;
  height: 110px;
  border-radius: 100%;

  background-color: #39b4cc;
  background-image: linear-gradient(91deg, transparent 50%, #a2ecfb 50%),
    linear-gradient(90deg, #a2ecfb 50%, transparent 50%);
}

/*End of dynamic Circle*/
.background-header {
  background: transparent linear-gradient(270deg, #974774 0%, #4672b6 100%);
}

.table-header,
th {
  padding: 10px !important;
  border: none;
}

.td-odd {
  background-color: #eee;
}

.card-table {
  border-bottom: 1px solid #9a949429;
}

.table {
  border-collapse: collapse;
  border-radius: 12px;
  border-style: hidden;
  /* hide standard table (collapsed) border */
  box-shadow: 2px 2px 2px 2px #eee;
  width: 100%;
  border: 1px solid #d2cbcb;
}

.font-color-white {
  color: #000;
}

.other-td {
  padding: 0px 0px 0px 23px;
}

.contact-info-container-loc {
  width: 51%;
  float: left;
  border-radius: 25px;
  padding: 20px 0px;
  text-align: center;
}

.contact-inform {
  width: 45%;
  float: left;
  margin-top: 16px;
  border-radius: 25px;
  /*padding: 3px 30px 1px 0px; */
  padding: 3px 0px 1px 45px;
}

.padding-left {
  padding-left: 8em;
}

.sixty-width-con {
  width: 70%;
  border-radius: 25px;
  padding: 5px 5px 5px 5px;
}

.dispaly-inline {
  display: inline-block;
}

.inline-display {
  display: inline-block;
}

.app-bottom-data {
  position: relative;
  bottom: 20px;
}

.right-lead-card {
  padding: 10px 5px;
  background: #fff;
  text-align: center;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  border-radius: 10px;
  box-shadow: 3px 3px 6px #00000029;
}

.right-lead-card>svg {
  border-radius: 50%;
  height: 75px;
  width: 75px;
  /* margin: 10px; */
}

.right-lead-card button {
  background: #ee424f;
  color: #fff;
  padding: 9px 14px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: 5px;
  font-size: 12px;
  margin: 20px 0 0 5px;
  border: none;
  line-height: normal;
}

.aside-p-tag {
  padding: 10px 10px;
}

.aside-svg-img {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  vertical-align: middle;
}

.aside-title {
  position: relative;
  bottom: 39px;
  font-size: 15px;
  font-weight: 500;
}

.gmu-ad.container {
  text-align: center;
  margin-top: 12px;
}

@media (min-width: 992px) {
  .col-md-2 {
    /* width: 16.66666667%; */
    width: 20% !important;
  }

  .col-md-10 {
    width: 80%;
  }

  .scroll-to-fixed-left .nav>li>a {
    padding: 15px 15px;
    width: 190px;
    font-weight: bold;
    /* for align set */
    display: flex;
  }

  /* FAQ Align Set */
  li a.faq {
    margin-left: 6px;
  }

  .sa_sidemenu_active {
    width: 190px;
    color: #353434 !important;
    border-radius: 0px 5px 5px 0px;
    border: 0px;
    padding: 5px 15px;
    color: #000;
    box-shadow: 0px 8px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease 0s;
  }

  .mb20 {
    margin: 40px 20px;
  }

  .top_recommedations_header {
    color: #ffffff;
    background: transparent linear-gradient(360deg, #974774 0%, #4672b6 100%);
    margin-top: -10px;
    width: 215px;
    margin-left: -5px;
    border-radius: 3px;
    margin-bottom: 10px;
    padding: 10px;
  }

  .margin_on_desktop {
    margin: 0px 15px !important;
  }

  .col-9 {
    background-color: #ffffff;
  }
}

@media (max-width: 991px) {
  .col-9 {
    width: 100%;
    position: relative;
    min-height: 1px;
    padding-right: 0px;
    padding-left: 0px;
    float: left;
  }

  .contact-info-container-loc {
    width: 100% !important;
    float: none !important;
    border-radius: 0px !important;
    padding: 20px;
    text-align: center;
  }

  .contact-inform .contact-circle {
    border-radius: 5px !important;
  }

  .p-font-s {
    padding: 0px !important;
  }

  .display-inline {
    display: block !important;
    text-align: center;
  }

  .sixty-width-con {
    width: 100% !important;
    margin-top: 0px !important;
  }

  .fee-data {
    padding: 12px 0px 13px 2px !important;
  }

  .app-table {
    width: 100% !important;
  }

  .table .app-table-svg {
    width: 30px !important;
    height: 30px !important;
  }

  .app-bottom-data {
    position: relative;
    bottom: 12px !important;
  }

  .location_iframe {
    width: inherit;
  }

  .contact-inform {
    width: 100%;
    margin-top: 10px;
    border-radius: 25px;
    padding: 0px 10px;
  }

  .col-md-10 {
    width: 100%;
  }

  .other_fees_vbox1 {
    width: 10%;
    padding: 8px 0px;
  }

  .other_fees_vbox2 {
    width: 70%;
    padding: 0px 20px;
    margin-top: 16px;
    font-size: 13px;
  }

  .other_fees_vbox3 {
    width: 20%;
    font-weight: bold;
    padding: 15px 0px;
    font-size: 13px;
  }

  .mb20 {
    margin: 40px 40px;
  }

  .card_white_background {
    padding: 15px !important;
    background: #ffffff;
    border-radius: 4px;
    /*box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.4)*/
  }

  .mob_menu_font {
    color: #fff;
    font-size: 13px;
  }

  .sa_nav_style {
    padding: 10px 20px;
    background: transparent linear-gradient(360deg, #974774 0%, #4672b6 100%) !important;
  }

  .cdev {
    top: -8px !important;
    left: 94px !important;
  }

  .enquiryBtn {
    background: #00c853;
    color: #fff;
    padding: 3px;
    font-weight: 600;
    font-size: 14px;
    width: 100%;
    border: 1px solid transparent;
    border-radius: 4px;
    margin-top: 5px;
  }

  #acceptance-rate {
    bottom: -54px !important;
    text-align: left;
  }
}

#acceptance-rate {
  color: #143658;
  padding-left: 0px;
  font-size: 14px;
  font-weight: bold;
  position: relative;
  left: 3%;
  bottom: -108px;
}

.card_background-white {
  padding: 15px !important;
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.4);
}

.cdev {
  position: relative;
  height: 100px;
  width: 100px;
  margin: 0 auto;
  top: -27px;
}

.cdev div {
  position: absolute;
  height: 100px;
  width: 100px;
  border-radius: 50%;
}

.cdev div span {
  position: absolute;
  /* font-family: Arial; */
  font-size: 25px;
  line-height: 75px;
  height: 75px;
  width: 75px;
  left: 12.5px;
  top: 12.5px;
  text-align: center;
  border-radius: 50%;
  background-color: white;
}

.cdev .background {
  background-color: #b3cef6;
}

.cdev .rotate {
  clip: rect(0 50px 100px 0);
  background-color: #4b86db;
}

.cdev .left {
  clip: rect(0 50px 100px 0);
  opacity: 1;
  background-color: #b3cef6;
}

.cdev .right {
  clip: rect(0 50px 100px 0);
  transform: rotate(180deg);
  opacity: 0;
  background-color: #4b86db;
}

.cdev {
  position: relative;
  height: 100px;
  width: 100px;
  margin: 0 auto;
}

.cdev div {
  position: absolute;
  height: 100px;
  width: 100px;
  border-radius: 50%;
}

.cdev div span {
  position: absolute;
  /* font-family: Arial; */
  font-size: 25px;
  line-height: 75px;
  height: 75px;
  width: 75px;
  left: 12.5px;
  top: 12.5px;
  text-align: center;
  border-radius: 50%;
  background-color: white;
}

.cdev .background {
  background-color: #b3cef6;
}

.cdev .rotate {
  clip: rect(0 50px 100px 0);
  background-color: #4b86db;
}

.cdev .left {
  clip: rect(0 50px 100px 0);
  opacity: 1;
  background-color: #b3cef6;
}

.cdev .right {
  clip: rect(0 50px 100px 0);
  transform: rotate(180deg);
  opacity: 0;
  background-color: #4b86db;
}

@keyframes toggle {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

#college-menu {
  padding-left: 0;
  padding-right: 0;
  z-index: 1001 !important;
}

#slick-slider-menu {
  background: #fff;
  z-index: 900;
  white-space: nowrap;
  width: 100%;
  margin-bottom: 10px;
  margin-top: 140px;
  box-shadow: 0px 0px 5px 0 rgba(0, 0, 0, 0.2);
  padding: 0px 5px;
}

#mobile-slick-slider-menu {
  background: #fff;
  z-index: 900;
  white-space: unset !important;
  width: 100%;
  margin-bottom: 10px;
  margin-top: 5px;
  box-shadow: 0px 0px 5px 0 rgba(0, 0, 0, 0.2);
}

.menu-menu-item,
.menu-menu-items-a {
  user-select: none;
  border-right: 0px;
}

.active-menu {
  border-bottom: 3px solid #4374b9 !important;
}

#clg-date-svg {
  display: inline-block;
  margin-right: 10px;
  width: 47px;
  height: 47px;
  padding: 6px 1px 1px 8px;
}

.clg-loc {
  position: relative;
  bottom: 18px;
  color: #707070;
}

.clg-address {
  display: block;
  color: #000;
}

.menu-menu-item {
  border-right: 0px;
}

.menu-menu-item {
  display: inline-block;
  font-size: 13px;
  padding: 3px 10px 0 10px;
  line-height: 40px;
  border-bottom: 3px solid transparent;
}

.menu-menu-items-a {
  text-decoration: none !important;
  cursor: pointer;
  color: #666;
  font-weight: 700;
  padding: 0 5px !important;
  outline: 0 !important;
}

.sa_nav {
  background: #ffffff;
}

.green_intrested_button {
  outline: none;
  border: none;
  background: #00c853;
  color: #ffffff;
  padding: 5px 25px;
  font-size: 15px;
  font-weight: 600;
  text-align: center;
  box-shadow: 1px 1px 2px 2px #eee;
  border-radius: 5px;
  margin-top: 3px;
}

.program-header {
  margin-bottom: 30px;
}

@media (min-width: 991px) {

  /* top header */
  /*Desktop view*/
  .fix_navbar_style {
    background: #31708f !important;
  }

  .top_card_heading {
    background: linear-gradient(45deg,
        rgba(67, 116, 185, 0.7),
        rgba(238, 66, 79, 0.7));
    padding: 80px 0 0 0;
    color: #444;
    height: 200px;
    margin-top: -50px;
  }

  .college-info-card {
    padding: 15px;
    background: #fff;
    overflow: hidden;
    margin: 0px 15px;
    margin-top: 30px;
    border-radius: 5px;
    box-shadow: 2px 3px #eee;
    margin-bottom: 9px;
    color: #707070;
    font-weight: bold;
  }

  .college-navbar-card {
    padding: 15px;
    background: #fff;
    overflow: hidden;
    margin: 0px 20px;
  }

  #header-div-id {
    /*background: url("<?php //echo $banner;
                            ?>") center center / 100% no-repeat;*/
    height: 200px;
  }

  .top_card_heading_detail {
    /*font-family: helvetica!important;*/
    font-size: 14px;
    text-align: center;
    font-size: 33px;
    color: #ffffff;
    font-weight: bold;
    margin-bottom: 12px;
  }

  .col-left {
    float: left;
    width: 195px;
  }

  .col-center {
    float: left;
    width: calc(100% - 390px);
  }

  .col-right {
    float: right;
    width: 145px;
    margin-right: 25px;
  }

  .fee_clg_detail {
    padding-right: 30px;
    padding-left: 30px;
  }

  .clg-address {
    margin: 5px 0px;
    font-size: 20px;
    left: 0px !important;
  }

  .col-left {
    box-shadow: 1px 1px 2px 2px #eee;
    border-radius: 20px;
    text-align: center;
    padding: 20px !important;
    background: #ffffff;
    margin-left: 25px;
  }

  .clg-loc {
    position: relative;
    bottom: 19px;
    left: 1px;
    width: 0px;
    color: #707070;
  }

  .intrested_button {
    background: #00c853;
    color: #ffffff;
    padding: 10px 10px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 15px;
    text-align: center;
    box-shadow: 1px 1px 2px 2px #eee;
    border-radius: 5px;
    width: 100%;
    border: 0px;
  }

  .get_more_info_button {
    background: #4374b9;
    color: #ffffff;
    padding: 10px 10px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 15px;
    text-align: center;
    box-shadow: 1px 1px 2px 2px #eee;
    border-radius: 5px;
    width: 100%;
    border: 0px;
  }

  /* top header ends */

  .popopup {
    position: fixed;
    top: 10% !important;
    bottom: 0 !important;
    left: 20% !important;
    z-index: 1050;
    display: none;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    outline: 0;
    width: 60% !important;
  }
}

/* top header */
@media screen and (max-width: 600px) {
  .clg-address {
    color: #fff !important;
    /* left:140px!important; */
  }

  #clg-address-svg {
    bottom: 16px !important;
    position: absolute;
    left: -37px;
  }

  .clg-loc {
    color: #fff !important;
    width: 167px !important;
    /* top:0px!important; */
  }

  .mobile-card-img {
    left: -40px !important;
  }

  .clog-card-date {
    left: -22px !important;
  }
}

/* top header ends */

@media (max-width: 990px) {
  .popopup {
    position: fixed;
    top: 10% !important;
    right: 24% !important;
    bottom: 0 !important;
    left: 0% !important;
    z-index: 1050;
    display: none;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    outline: 0;
    width: 100% !important;
  }

  #slick-slider-menu {
    margin-top: 0px !important;
  }
}

.top-recommend-svg-mobile {
  height: 65px;
  width: 45px;
}

.top-recommend-svg {
  height: 71px;
  width: 45px;
}

.top-recommend-p {
  font-size: 14px;
  border-bottom: 2px solid #e6e5e5;
}

.top-recom-span {
  font-size: 13px;
}

.top-text-left {
  text-align: left;
}

.btn_carousel_left,
.btn_carousel_right {
  display: flex;
  background-color: #707070;
  color: white;
  cursor: pointer;
  font-size: 50px;
  font-weight: bold;
  height: 100%;
  opacity: 0.53;
  position: absolute;
  text-align: center;
  top: 0;
  user-select: none;
  width: 45px;
  z-index: 1;
}

.btn_carousel_left>.icon,
.btn_carousel_right>.icon {
  align-self: center;
  margin: 0 auto;
  width: 35px;
}

.btn_carousel_left {
  left: 0;
}

.btn_carousel_right {
  right: 0;
}

.carousel_section .carousel_container {
  margin-top: 35px;
  position: relative;
}

.carousel_section .slide_container {
  display: block;
  white-space: nowrap;
  overflow-x: scroll;
  scrollbar-width: none;
}

.carousel_section .slide_container::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
}

.slide_container .box_container {
  background-color: white;
  display: inline-block;
  width: 25.85%;
  text-align: center;
  margin: 0 7px;
}

#admission-desktop {
  width: 20%;
  display: inline-block;
  margin-right: 50px;
}

.application-title {
  position: relative;
}

.display-webkit {
  display: -webkit-box;
}

.model-table-width-fourty {
  width: 40%;
}

.vertical-container ul>li>a {
  padding: 5px 10px;
}

.top-desktop-td-data-svg {
  padding: 5px 0px 0px 7px;
}

.top-desktop-td-data {
  float: left;
  text-align: left;
  padding: 21px 0px 0px 7px;
  position: relative;
  top: 2px;
}

/* Blure on open modal */
.modal-open .container-fluid,
.modal-open .container {
  -webkit-filter: blur(3px);
  -moz-filter: blur(3px);
  -o-filter: blur(3px);
  -ms-filter: blur(3px);
  filter: blur(3px);
}

.browse-by-mobile-inter {
  display: none;
}

@media screen and (max-width: 992px) {
  .browse-by-mobile-inter {
    display: block !important;
  }
}

/* Blure on open modal end */