body {
    font-family: -apple-system, BlinkMacSystemFont, “Segoe UI”, “Helvetica Neue”, sans-serif !important;
}

header .navbar-form.navbar-left {
    display: none;
}

.carousel-filter-div {
    background: rgba(238, 66, 79, 0.7);
    background: linear-gradient(45deg, rgba(67, 116, 185, 0.7), rgba(238, 66, 79, 0.7));
    height: 100vh;
}

.carousel-item {
    width: 100%;
    height: 100vh;
    background-size: cover;
    background-position: center;
}

#search-div {
    margin-top: -100vh;
}

#search-div section {
    /* background: rgba(0,0,0,0.47); */
    height: 100vh;
    padding-top: 20vh;
}

#search-div section h1 {
    font-weight: 700;
    font-size: 24px;
    color: #FFF;
    line-height: 30px;
    margin-bottom: 0px;
}

#search-div .nav-pills>li>a {
    background: #0071bc;
    color: #FFF;
    padding: 10px 30px;
    display: inline-flex;
    font-weight: bold;
    border-radius: 0px;
}

#search-div .nav-pills>li.active>a {
    background: #438ee1;
}

.divider {
    height: 50px;
}

div .a {
    background-color: #fff;
    padding: 20px;
    border-radius: 5px
}

div .a svg {
    margin-bottom: 10px;
}

div .a a {
    color: #333333;
    font-weight: bold;
    font-size: 16px;
}

div .a p {
    font-size: 13px;
    margin-bottom: 15px;
}

.footer-wrapper {
    margin: 0px !important;
}

h2 {
    font-size: 18px !important;
    color: #fff !important;
    line-height: 26px;
}

#mousescroll span {
    position: absolute;
    bottom: 8vh;
    left: 50%;
    width: 24px;
    height: 24px;
    margin-left: -12px;
    border-left: 1px solid #fff;
    border-bottom: 1px solid #fff;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-animation: sdb07 2s infinite;
    animation: sdb07 2s infinite;
    opacity: 0;
    box-sizing: border-box;
}

#mousescroll span:nth-of-type(1) {
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
}

#mousescroll span:nth-of-type(2) {
    bottom: 8vh;
    margin-bottom: 10px;
    -webkit-animation-delay: .15s;
    animation-delay: .15s;
}

#mousescroll span:nth-of-type(3) {
    bottom: 8vh;
    margin-bottom: 20px;
    -webkit-animation-delay: .3s;
    animation-delay: .3s;
}

@-webkit-keyframes sdb07 {
    0% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

@keyframes sdb07 {
    0% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

#speaker-img {
    max-width: 100%;
}

@media(min-width:992px) {
    .carousel-filter-div {
        height: 400px;
    }

    .carousel-item {
        height: 400px;
    }

    #search-div {
        margin-top: -400px;
    }

    #search-div section {
        height: 500px;
    }

    #invest {
        padding-top: 200px;
    }

    #times-logo {
        padding-top: 120px;
        margin-left: 130px;
    }

    .abt-us-btn {
        min-width: 25%;
        margin-right: 10px;
    }

    #or-span {
        margin: 10px;
    }

    .speaker-row {
        padding: 0px 75px;
    }

    .small-cn {
        width: 890px;
        padding-bottom: 50px;
    }

    .mt-30 {
        margin-top: 30px;
    }
}

@media(max-width:991px) {
    .divider {
        height: 15px;
    }

    .main-body {
        margin-top: 15px;
    }

    #course-section {
        margin: 0px;
    }

    #search-div .nav-pills>li>a {
        padding: 10px 26px;
        display: block;
    }

    #search-div section h1 {
        font-size: 18px;
        line-height: 24px;
        margin-bottom: 0px;
    }

    .abt-us-btn {
        width: 100%;
    }

    #or-span {
        margin: 10px;
    }

    .speaker-row {
        padding: 0px 75px;
    }
}

@media (max-width: 768px) {
    .heading-gradient {
        width: 100%;
    }

    #or-span {
        margin: 280px;
    }

    .speaker-row {
        padding: 0px 20px;
    }
}

@media (max-width: 320px) {
    body * {
        font-size: 95%;
    }

    .abt-us-btn {
        width: 100%;
    }

    #or-span {
        margin: 100px;
    }

    .mt-100 {
        margin-top: -100px;
    }

    .times-logo {
        margin-top: 50px;
    }

    .speaker-row {
        padding: 0px 20px;
    }
}

.main-body {
    text-align: left !important;
    background: #fff;
}

.heading-gradient {
    background-image: -webkit-linear-gradient(90deg, #4374b9, #ee424f);
    background-image: -moz-linear-gradient(90deg, #4374b9, #ee424f);
    background-image: -ms-linear-gradient(90deg, #4374b9, #ee424f);
    background-image: -o-linear-gradient(90deg, #4374b9, #ee424f);
    background-image: linear-gradient(90deg, #4374b9, #ee424f);
    color: transparent !important;
    -webkit-background-clip: text;
    background-clip: text;
    font-weight: bold;
    padding-top: 50px;
    width: fit-content;
}

.abt-us-btn {
    background: transparent;
    padding: 6px 21px;
    border-radius: 20px;
    color: #f3f3f3;
    font-weight: bold;
    font-size: 1em;
    border: 2px solid white;
    margin-top: 10px;
    margin-bottom: 10px;
}

.abt-us-btn:focus,
.abt-us-btn:hover {
    background: #fff;
    color: #4380b8;
}

#or-span {
    font-weight: bold;
    font-size: 1em;
}

.achievements {
    color: #4374b9;
    font-weight: 700 !important;
    font-size: 16px;
    line-height: 24px;
    margin-top: 5;
}

.times-logo {
    height: 450px;
    background-repeat: no-repeat;
    background-position: right;
}

.speaker-row {
    color: #fff;
}

@media (max-width: 991px) {
    #times-logo {
        float: right;
        padding-top: 85px;
        margin-right: 5%;
    }

    #myCarousel {
        margin-top: -40px;
    }
}