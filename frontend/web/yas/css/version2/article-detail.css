:root {
  --border-line: 1px solid #d8d8d8;
}

h3,
h4 {
  padding-bottom: 10px;
  line-height: 24px;
}

.badgeIcon {
  width: 24px;
  height: 28px;
  background-position: 167px -262px;
  margin-right: 15px;
  margin-top: -10px;
}

.articelNote {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
}

.articelNote p {
  font-size: 15px;
  line-height: 26px;
}

.bannerImg {
  border-radius: 4px;
  margin-top: 20px;
  overflow: hidden;
  max-height: 400px;
}

.bannerImg img {
  max-height: 400px;
  width: 100%;
  display: block;
}

.dataContainer,
.articleInfo {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin: 20px 0;
}

.dataContainer p,
.articleInfo p,
.articleInfo div {
  font-size: 15px;
  color: var(--primary-font-color);
  line-height: 26px;
  padding-bottom: 10px;
}

.dataContainer h2,
.articleInfo h2 {
  padding: 8px 20px;
  line-height: 28px;
  font-size: 18px;
  font-weight: var(--font-bold);
  color: var(--primary-font-color);
  background: #f5f5f5;
  margin-bottom: 20px;
}

.dataContainer h3,
.articleInfo h3 {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}

.dataContainer h4,
.articleInfo h4 {
  font-size: 16px;
  padding-bottom: 10px;
  line-height: 24px;
  color: var(--primary-font-color);
}

.dataContainer ul li,
.articleInfo ul li {
  font-size: 15px;
  line-height: 26px;
  color: var(--primary-font-color);
}

.articleInfo img {
  display: block;
  text-align: center;
  margin: 0 auto;
}

.articleInfo table thead tr {
  background: #f1f3f4;
  padding: 10px;
  font-size: 15px;
  line-height: 26px;
  text-align: left;
  border-right: 0.2px solid #eaeaea;
  border-bottom: 0.2px solid #eaeaea;
  text-align: center;
}

.articleInfo table thead tr th {
  background: 0 0;
  text-align: center;
}

.articleInfo table thead tr td {
  font-weight: 600;
}

.articleInfo table tbody tr td {
  text-align: center;
}

.articleInfo table tbody tr td ul li {
  list-style: none;
}

.articleInfo caption {
  padding-top: 5px;
  text-align: center;
  font-size: 14px;
  margin-bottom: 0 !important;
  caption-side: bottom;
  color: var(--primary-font-color);
}

.articleInfo .table-responsive {
  margin-bottom: 0 !important;
}

.articleInfo button {
  display: block;
  background: var(--color-red);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-white);
  padding: 8px 15px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  -webkit-transition: 0.2s ease;
  transition: 0.2s ease;
  outline: none;
  margin: 0 auto;
}

.table-responsive::-webkit-scrollbar {
  -webkit-appearance: none;
}

.table-responsive::-webkit-scrollbar:horizontal {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background-color: #d8dbdd;
  border-radius: 10px;
  border: 2px solid #fff;
}

.table-responsive::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #fff;
}

.faq_section h2 {
  padding: 10px 20px;
  font-size: 18px;
  line-height: 24px;
  color: var(--primary-font-color);
  background: #f5f5f5;
  margin-bottom: 20px;
}

.faq_section .faqDiv {
  border-radius: 4px;
  border: var(--border-line);
}

.faq_section p {
  padding: 11px 24px;
  font-size: 16px;
  line-height: 26px;
  border-bottom: var(--border-line);
}

.faq_section .faq_question {
  position: relative;
  cursor: pointer;
  -webkit-transition: 0.4s ease;
  transition: 0.4s ease;
}

.faq_section .faq_answer {
  border-left: 5px solid var(--color-red);
  font-size: 15px;
  background: #fafbfc;
}

.getSupport {
  padding: 19px;
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 3px;
  margin-bottom: 20px;
}

.getSupport .row {
  margin: 0;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}

.getSupport button {
  flex: 1;
}

.getSupport img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}

.getSupport p {
  font-size: 18px;
  line-height: 26px;
}

.getSupport button {
  width: 161px;
  border-radius: 3px;
  font-size: 14px;
  line-height: 24px;
  padding: 6px;
  text-align: center;
  color: var(--color-white);
  font-weight: var(--font-bold);
  border: none;
}

.getSupport button.talkToExpert {
  background: var(--topheader-bg);
}

.getSupport button.applyNow {
  background: var(--color-red);
  margin-left: 15px;
  width: 160px;
}

.trendingArtilce p,
.recentArticles p,
.recentnews p,
.trendingNews p {
  padding: 10px 20px;
  font-size: 16px;
  line-height: 24px;
  border-bottom: var(--border-line);
  font-weight: var(--font-semibold);
}

.trendingArtilerDiv.row,
.recentArticlesDiv.row,
.recentnewsDiv.row {
  margin: 0;
  flex-wrap: nowrap;
  margin-bottom: 10px;
  border-bottom: var(--border-line);
  padding-bottom: 10px;
  -webkit-box-align: center;
  align-items: center;
  cursor: pointer;
}

.trendingArtilerDiv.row:hover .sidebarTextLink,
.recentArticlesDiv.row:hover .sidebarTextLink,
.recentnewsDiv.row:hover .sidebarTextLink,
.trendingArtilerDiv.row:hover a,
.recentArticlesDiv.row:hover a,
.recentnewsDiv.row:hover a {
  text-decoration: underline;
  color: var(--anchor-textclr);
}

.listCard:last-child .trendingArtilerDiv.row,
.listCard:last-child .recentArticlesDiv.row,
.listCard:last-child .recentnewsDiv.row {
  margin-bottom: 0;
  border: none;
  padding: 0;
}

.trendingArtilerDiv img,
.recentArticlesDiv img {
  width: 96px;
  max-height: 72px;
  display: block;
  align-self: center;
}

.sidebarImgDiv {
  flex-basis: 96px;
  margin-right: 16px;
  display: grid;
  min-height: 72px;
}

.trendingArtileText,
.recentArticlesDivText {
  flex-basis: calc(100% - 96px - 16px);
}

.trendingArtilerDiv .trendingArtileText a,
.trendingArtilerDiv .recentArticlesDivText a,
.trendingArtilerDiv .recentnewsDivText a,
.recentArticlesDiv .trendingArtileText a,
.recentArticlesDiv .recentArticlesDivText a,
.recentArticlesDiv .recentnewsDivText a,
.recentnewsDiv .trendingArtileText a,
.recentnewsDiv .recentArticlesDivText a,
.recentnewsDiv .recentnewsDivText a,
.sidebarTextLink {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: 500;
  text-decoration: none;
  overflow: hidden;
}

.trendingArtilerDiv .trendingArtileText .sidebarTextLink,
.trendingArtilerDiv .recentArticlesDivText .sidebarTextLink,
.trendingArtilerDiv.recentnewsDivText .sidebarTextLink,
.recentArticlesDiv .recentnewsDivText .sidebarTextLink,
.recentArticlesDiv .trendingArtileText .sidebarTextLink,
.recentArticlesDiv .recentArticlesDivText .sidebarTextLink,
.recentnewsDiv .trendingArtileText .sidebarTextLink,
.recentnewsDiv .recentArticlesDivText .sidebarTextLink,
.recentnewsDiv .recentnewsDivText .sidebarTextLink {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: 500;
  text-decoration: none;
  font-weight: 400;
  padding: 0;
  border: none;
}

.trendingArtilerDiv .trendingArtileText a:hover,
.trendingArtilerDiv .recentArticlesDivText a:hover,
.trendingArtilerDiv .recentnewsDivText a:hover,
.recentArticlesDiv .trendingArtileText a:hover,
.recentArticlesDiv .recentArticlesDivText a:hover,
.recentArticlesDiv .recentnewsDivText a:hover,
.recentnewsDiv .trendingArtileText a:hover,
.recentnewsDiv .recentArticlesDivText a:hover,
.recentnewsDiv .recentnewsDivText a:hover {
  color: #337ab7;
  text-decoration: underline;
}

.articleSidebarSection,
.newsSidebarSection {
  border-radius: 4px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.12);
  margin-bottom: 20px;
}

.articleSidebarSection .tab-content.activeLink,
.newsSidebarSection .tab-content.activeLink {
  display: block;
}

.articleSidebarSection ul,
.newsSidebarSection ul {
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: flex;
}

.articleSidebarSection ul li,
.newsSidebarSection ul li {
  list-style-type: none;
  flex-basis: 50%;
  text-align: center;
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  cursor: pointer;
  padding: 12px 5px;
  padding-bottom: 9px;
  border-bottom: var(--border-line);
  font-weight: var(--font-semibold);
}

.articleSidebarSection ul li.recentHeading {
  width: 100%;
  flex: 1;
  text-align: left;
  padding: 12px 20px;
}

.articleSidebarSection ul li.activeLink,
.newsSidebarSection ul li.activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
}

.trendingArtilerList,
.recentArticlesList,
.recentnewsList {
  max-height: 490px;
  overflow: auto;
  padding: 20px;
}

.trendingArtilerList::-webkit-scrollbar,
.recentArticlesList::-webkit-scrollbar,
.recentnewsList::-webkit-scrollbar {
  width: 5px;
}

.trendingArtilerList::-webkit-scrollbar-thumb,
.recentArticlesList::-webkit-scrollbar-thumb,
.recentnewsList::-webkit-scrollbar-thumb {
  background: #ccc;
}

.faqDiv .faq_answer p,
.faqDiv .faq_answer li,
.faqDiv .faq_answer a {
  font-size: 15px;
  line-height: 26px;
  font-weight: 400;
  border: none;
}

.faqDiv .faq_answer {
  padding: 11px 24px;
}

.faqDiv .faq_answer p {
  padding: 0;
  padding-bottom: 5px;
}

.bannerDiv,
.registerLatestArticle {
  margin-top: 10px;
}

.bannerDiv,
.registerLatestArticle,
.articleSidebarSection,
.newsSidebarSection,
.articelNote,
.articleInfo,
.faq_section,
.contentProvider,
.commentSection,
.relatedArticles {
  box-shadow: none;
  border: var(--border-line);
  background: var(--color-white);
}

.recentnewsDivText,
.recentArticlesDivText {
  flex-basis: calc(100% - 96px - 16px);
}

.pageDescription {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin: 10px 0;
  color: var(--primary-font-color);
  padding: 30px;
}

.pageDescription .liveIcon {
  background-position: -27px -378px;
  margin-right: 10px;
  width: 70px;
  height: 35px;
  float: left;
}

.pageDescription h1 {
  font-size: 24px;
  line-height: 38px;
  padding-bottom: 20px;
  font-weight: 400;
}

.updatedBy {
  padding-right: 5px;
}

.updated-info.row {
  margin: 0;
  align-items: center;
  line-height: 20px;
  font-size: 14px;
}

.updated-info.row img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 10px;
  vertical-align: middle;
}

.updated-info.row .updatedBy p {
  display: inline-block;
  font-weight: var(--font-semibold);
}

.updated-info.row .updatedDetails {
  display: flex;
  align-items: center;
  flex-grow: 2;
}

.updated-info.row a {
  text-decoration: none;
  color: var(--anchor-textclr);
}

.updated-info.row ul {
  margin: 0;
  padding: 0;
  margin-left: 10px;
  right: -43px;
  z-index: 3;
}

.updated-info.row ul p {
  display: inline-block;
}

.updated-info.row ul li {
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
}

.examRelataedLinks {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  padding: 0;
  margin-bottom: 20px;
  position: relative;
}

.examRelataedLinks .btn_left,
.examRelataedLinks .btn_right {
  position: absolute;
  width: 48px;
  height: 48px;
  background-color: #fff;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  top: 0;
  cursor: pointer;
}

.examRelataedLinks .btn_left {
  left: 0;
}

.examRelataedLinks .btn_right {
  right: 0;
}

.examRelataedLinks ul {
  margin: 0;
  padding: 0 30px;
  white-space: nowrap;
  overflow: auto;
}

.examRelataedLinks ul::-webkit-scrollbar {
  display: none;
}

.examRelataedLinks ul li {
  display: inline-block;
  padding: 0 10px;
}

.examRelataedLinks ul li a {
  display: block;
  padding: 12px 0;
  padding-bottom: 9px;
  border-bottom: 3px solid var(--color-white);
  color: #787878;
  line-height: 24px;
  font-size: 14px;
  text-decoration: none;
}

.examRelataedLinks ul li a.activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
  font-weight: var(--font-semibold);
}

.articelNote {
  font-size: 15px;
  font-weight: 400;
  font-stretch: normal;
  font-style: italic;
  line-height: 1.73;
  letter-spacing: 0.3px;
  text-align: left;
  color: #282828;
  border-left: solid #ff4e53 6px;
}

.getSupport {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px;
}

.getSupport button.freeScholarship {
  background: var(--topheader-bg);
}

.getSupport button.applyScholarship {
  background: var(--color-red);
  margin-left: 14px;
}

.trendingArtilce {
  border-radius: 4px;
  box-shadow: none;
  background: #fff;
  margin-bottom: 20px;
}

.trendingArtilce p {
  padding: 10px 20px;
  font-size: 16px;
  line-height: 24px;
  border-bottom: var(--border-line);
  font-weight: var(--font-semibold);
}

.trendingArtilerList {
  padding: 20px;
}

.trendingArtilerDiv.row {
  margin: 0;
  flex-wrap: nowrap;
  margin-bottom: 16px;
  align-items: center;
}

.trendingArtilerDiv.row:last-child {
  margin-bottom: 0;
}

.trendingArtilerDiv.row .dateLabel {
  background: #fff;
  width: 56px;
  height: 56px;
  color: #fb7739;
  font-weight: var(--font-semibold);
  font-size: 16px;
  line-height: 20px;
  text-align: center;
  margin-right: 16px;
  border-radius: 4px;
  padding: 7px;
  border: 1px solid #fb7739;
}

.trendingArtilerDiv.row .dateLabel span {
  font-size: 24px;
  color: inherit;
}

.trendingArtilerDiv img {
  max-width: 74px;
  max-height: 56px;
}

.trendingArtilerDiv .trendingArtileText a {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: 500;
  text-decoration: none;
}

.trendingArtilerDiv .trendingArtileText a:hover {
  color: #3d8ff2;
  text-decoration: underline;
}

.trendingArtilerDiv .trendingArtileText p {
  font-size: 14px;
  line-height: 24px;
  color: #989898;
  padding: 0;
  font-weight: 400;
  border: none;
}

.quickLinks {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  padding: 0;
}

.quickLinks h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  text-transform: uppercase;
}

.quickLinks ul {
  padding: 10px 20px;
  margin: 0;
  max-height: 440px;
  overflow: auto;
}

.quickLinks ul::-webkit-scrollbar {
  width: 5px;
}

.quickLinks ul::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.quickLinks ul::-webkit-scrollbar-thumb {
  background: #ccc;
}

.quickLinks ul li {
  list-style-type: none;
}

.quickLinks ul li:last-child a {
  border-bottom: none;
}

.quickLinks ul li a {
  display: block;
  font-size: 14px;
  line-height: 24px;
  border-bottom: var(--border-line);
  text-decoration: none;
  color: var(--primary-font-color);
  padding: 8px 0;
}

.quickLinks ul li a:hover {
  color: #3d8ff2;
  text-decoration: underline;
}

.clgWithCourse {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px;
}

.clgWithCourse h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  text-transform: uppercase;
  margin-bottom: 20px;
}

.clgWithCourse h2.row {
  justify-content: space-between;
  margin: 0;
  margin-bottom: 20px;
}

.clgWithCourse h2 a {
  font-size: 14px;
  color: var(--color-red);
  text-decoration: none;
}

.liveApllicationForms {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px;
}

.liveApllicationForms h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  text-transform: uppercase;
  margin-bottom: 20px;
  margin-left: 0;
  margin-right: 0;
}

.liveApllicationFormsInner .row,
.clgWithCourseInner .row {
  margin: 0;
}

.liveApllicationFormsInner .row .applicationDiv,
.liveApllicationFormsInner .row .clgWithCourseDiv,
.clgWithCourseInner .row .applicationDiv,
.clgWithCourseInner .row .clgWithCourseDiv {
  margin-right: 20px;
  flex-basis: 18.6%;
}

.liveApllicationFormsInner .row .applicationDiv:last-child,
.liveApllicationFormsInner .row .clgWithCourseDiv:last-child,
.clgWithCourseInner .row .applicationDiv:last-child,
.clgWithCourseInner .row .clgWithCourseDiv:last-child {
  margin-right: 0;
}

.liveApllicationFormsInner {
  position: relative;
}

.liveApllicationFormsInner .scrollRight,
.liveApllicationFormsInner .scrollLeft {
  top: 50%;
}

.liveApllicationFormsInner .scrollRight {
  right: -20px;
}

.liveApllicationFormsInner .scrollLeft {
  left: -20px;
  transform: translate(0px, -50%) rotate(180deg);
}

.liveApllicationFormsInner .row {
  flex-wrap: nowrap;
  overflow: auto;
  white-space: nowrap;
}

.liveApllicationFormsInner .row::-webkit-scrollbar {
  display: none;
}

.liveApllicationFormsInner .row .applicationDiv {
  width: 18.6%;
}

.liveApllicationFormsInner .row .applicationDiv:last-child {
  margin: 0;
}

.liveApllicationFormsInner .row .applicationDiv {
  border-radius: 4px;
  border: var(--border-line);
  padding: 20px 11px;
  text-align: center;
}

.liveApllicationFormsInner .row .applicationDiv a {
  text-decoration: none;
}

.liveApllicationFormsInner .row .applicationDiv figure {
  margin-bottom: 20px;
}

.liveApllicationFormsInner .row .applicationDiv img {
  max-width: 80px;
  min-height: 80px;
  margin: 0 auto;
  display: block;
}

.liveApllicationFormsInner .row .applicationDiv p,
.liveApllicationFormsInner .row .applicationDiv a {
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.liveApllicationFormsInner .row .applicationDiv p:first-child,
.liveApllicationFormsInner .row .applicationDiv a:first-child {
  font-weight: var(--font-semibold);
}

.liveApllicationFormsInner .row .applicationDiv p a,
.liveApllicationFormsInner .row .applicationDiv a a {
  margin-bottom: 0;
}

.liveApllicationFormsInner .row .applicationDiv p.clgName,
.liveApllicationFormsInner .row .applicationDiv a.clgName {
  min-height: 50px;
}

.applicationDiv a.clgName,
a.clgName {
  color: #333;
}

.applicationDiv a.clgName:hover {
  text-decoration: underline;
  cursor: pointer;
  color: #3d8ff2;
}

.trendingArtilerDiv img,
.recentArticlesDiv img {
  max-height: 72px;
  align-self: center;
}

.applicationDiv p {
  -webkit-line-clamp: 1;
  min-height: 24px;
}

.applicationDiv p.course-fees {
  margin: 0;
  color: #989898;
}

.liveApllicationFormsInner .row .applicationDiv p span,
.liveApllicationFormsInner .row .applicationDiv a span {
  color: #989898;
  display: block;
}

.liveApllicationFormsInner .row .applicationDiv .applicaticationFormBtn,
.liveApllicationFormsInner .row .applicationDiv a.applicaticationFormBtn {
  display: block;
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
  border: none;
  color: var(--color-white);
}

.liveApllicationFormsInner .row .applicationDiv .applicaticationFormBtn a,
.liveApllicationFormsInner .row .applicationDiv a.applicaticationFormBtn a {
  color: var(--color-white);
  margin: 0;
}

.clgWithCourseList {
  margin: 0;
}

.clgWithCourseDiv {
  border-radius: 4px;
  border: var(--border-line);
  overflow: hidden;
  padding: 20px;
  text-align: center;
}

.clgWithCourseDiv img {
  max-width: 80px;
  min-height: 80px;
  margin: 0 auto;
  display: block;
}

.clgWithCourseDiv figure {
  text-align: center;
  margin-bottom: 20px;
}

.clgWithCourseDiv p,
.clgWithCourseDiv a {
  font-size: 14px;
  line-height: 24px;
  padding-bottom: 10px;
  text-decoration: none;
}

.clgWithCourseDiv p.clgName,
.clgWithCourseDiv a.clgName {
  font-weight: var(--font-semibold);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 50px;
  padding: 0;
  margin-bottom: 14px;
}

.clgWithCourseDiv .avgFee {
  font-weight: var(--font-semibold);
}

.clgWithCourseDiv .avgFee span {
  color: #989898;
  display: block;
  font-weight: 400;
}

.clgWithCourseDiv button {
  border-radius: 3px;
  border: solid 1px var(--anchor-textclr);
  display: block;
  padding: 5px;
  text-align: center;
  background: var(--color-white);
  color: var(--anchor-textclr);
  font-size: 14px;
  line-height: 24px;
  font-weight: var(--font-500);
  width: 100%;
  margin-bottom: 10px;
}

.clgWithCourseDiv button:last-child {
  margin-bottom: 0;
}

.latestInfoListContainer {
  position: relative;
}

.latestInfoListContainer .latestInfoList.row {
  flex-wrap: nowrap;
}

.latestInfoListContainer .latestInfoList.row .latestInfoDiv {
  min-width: 23.7%;
}

iframe {
  border: 0;
}

.fancybox-slide--video .fancybox-content {
  max-width: 750px;
  max-height: 400px;
  margin: 0 auto;
}

* {
  letter-spacing: normal;
  color: var(--primary-font-color);
}

table td span {
  color: var(--primary-font-color) !important;
}

.table-responsive {
  margin-bottom: 10px;
}

table caption {
  caption-side: bottom;
  margin-top: 20px;
  margin-bottom: 0;
  font-size: 15px;
  line-height: 24px;
  color: var(--primary-font-color);
}

.tab-content.activeLink {
  display: block;
}

.breadcrumbDiv ul li a {
  font-weight: 500;
}

.pageInfo {
  max-height: 200px;
}

.faq_section.pageInfo {
  max-height: 400px;
}

.modeIcon,
.courseDuratioIcon,
.feesIcons {
  width: 20px;
  height: 20px;
  vertical-align: middle;
}

.feesIcons {
  background-position: 52px -346px;
}

.modeIcon {
  background-position: 52px -274px;
}

.courseDuratioIcon {
  background-position: 52px -299px;
}

.customSlider {
  position: relative;
}

.customSlider .scrollRight {
  right: -20px;
}

.customSlider .scrollLeft {
  top: 50%;
  left: -20px;
}

.customSlider .row {
  margin: 0;
}

.customSlider .customSliderCards {
  display: block;
  white-space: nowrap;
  overflow: auto;
}

.customSlider .customSliderCards::-webkit-scrollbar {
  display: none;
}

.customSlider .sliderCardInfo {
  border-radius: 4px;
  border: var(--border-line);
  margin-right: 14px;
  vertical-align: middle;
  min-height: 303px;
}

.customSlider .sliderCardInfo:last-child {
  margin-right: 0;
}

.customSlider .sliderCardInfo img {
  display: block;
  height: 207px;
  width: 100%;
  border-bottom: var(--border-line);
}

.customSlider .sliderCardInfo .clgLogo {
  max-width: 72px;
  height: 72px;
  display: block;
  margin-right: 20px;
}

.customSlider .sliderCardInfo .textDiv {
  padding: 20px;
}

.customSlider .sliderCardInfo .textDiv .collegeLogo {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  margin-top: -60px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 5px;
  display: block;
}

.customSlider .sliderCardInfo p {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-semibold);
  padding-bottom: 0;
  white-space: initial;
}

.customSlider .sliderCardInfo p span {
  color: #989898;
  font-weight: 400;
  font-size: 13px;
}

.customSlider .sliderCardInfo .widgetCardHeading {
  font-size: 14px;
  padding-bottom: 0;
  min-height: 24px;
  margin-bottom: 5px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.customSlider .sliderCardInfo .subText {
  color: #989898;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  padding: 0;
  padding-bottom: 5px;
  position: relative;
}

.customSlider .sliderCardInfo .subText:last-child {
  padding-bottom: 20px;
}

.customSlider .sliderCardInfo .subText .spriteIcon {
  position: initial;
  vertical-align: middle;
}

.customSlider .sliderCardInfo a {
  text-decoration: none;
}

.sideBarSection {
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 4px;
  margin-bottom: 20px;
}

.sideBarSection .row {
  margin: 0;
  align-items: center;
}

.sideBarSection .sidebarHeading {
  background: #d8d8d8;
  padding: 10px 20px;
  font-size: 16px;
  line-height: 24px;
  margin: 20px;
  margin-bottom: 6px;
  font-weight: 500;
}

.sideBarSection .sidebarTextLink {
  flex-basis: calc(100% - 92px);
}

.sideBarSection .sidebarTextLink p {
  font-size: 14px;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.sideBarSection .sidebarTextLink .cardText {
  color: var(--primary-font-color);
}

.sideBarSection .sidebarTextLink .subText {
  color: #989898;
}

.sideBarSection p.listCard {
  font-weight: 500;
  font-size: 15px;
  line-height: 24px;
}

.sideBarSection .listCard {
  display: flex;
  padding: 10px 20px;
  border-bottom: var(--border-line);
}

.sideBarSection .listCard:hover .cardText,
.sideBarSection .listCard:hover .subText {
  color: var(--anchor-textclr);
}

.sideBarSection .listCard:last-child {
  border-bottom: none;
}

.sideBarSection .sidebarImgDiv {
  flex-basis: 72px;
  margin-right: 20px;
}

.sideBarSection .sidebarImgDiv img {
  display: block;
  margin: 0 auto;
  width: 100%;
}

.sideBarSection .applyText {
  color: var(--anchor-textclr);
}

.sidebarImgDiv img {
  width: 96px;
  max-height: 72px;
  display: block;
  align-self: center;
}

.getSupport button.articleScholarship {
  /* width: auto; */
  margin-left: 0;
}

.four-cardDisplay .sliderCardInfo {
  margin-right: 14px;
}

.four-cardDisplay .sliderCardInfo {
  width: 23.8%;
  display: inline-block;
  padding: 0;
  white-space: initial;
}

.four-cardDisplay .sliderCardInfo:nth-of-type(4n) {
  margin-right: 0;
}

.four-cardDisplay .sliderCardInfo:nth-of-type(4n + 1) {
  margin-left: 20px;
}

.four-cardDisplay .sliderCardInfo:first-child {
  margin-left: 0;
}

.four-cardDisplay .sliderCardInfo img {
  display: block;
  width: 100%;
}

.four-cardDisplay .sliderCardInfo figure {
  display: grid;
  height: 207px;
  border-bottom: var(--border-line);
}

.four-cardDisplay .sliderCardInfo figure img {
  align-self: center;
  height: auto;
  max-height: 207px;
}

.four-cardDisplay .sliderCardInfo p {
  font-size: 14px;
  line-height: 24px;
}

.four-cardDisplay .sliderCardInfo h3,
.four-cardDisplay .sliderCardInfo .widgetCardHeading {
  font-size: 14px;
  line-height: 24px;
  padding-bottom: 0;
  min-height: 48px;
  margin-bottom: 5px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
}

.four-cardDisplay .sliderCardInfo .subText {
  color: #989898;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  padding: 20px;
}

.four-cardDisplay .sliderCardInfo .spriteIcon {
  position: initial;
  vertical-align: middle;
  margin-right: 5px;
}

.four-cardDisplay .sliderCardInfo .textDiv {
  padding: 20px;
}

.four-cardDisplay .sliderCardInfo .collegeLogo {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  margin-top: -60px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 5px;
}

.four-cardDisplay a {
  text-decoration: none;
}

.four-cardDisplay .displayCard:hover h3,
.four-cardDisplay a:hover .widgetCardHeading {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.audio-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: left;
  align-items: center;
}

.audio-container audio {
  width: 100%;
  border-radius: 10px;
}

.audio-text {
  flex-basis: 100%;
  margin-bottom: 10px;
  font-size: 20px;
  font-weight: 600;
}

.webpSpriteIcon {
  display: inline-block !important;
  background: url(/yas/images/master_sprite.webp);
  text-align: left;
  overflow: hidden;
}

.authorInfoAndTranslateBtn {
  display: flex;
  justify-content: space-between;
}

.translateIcon1 {
  background-position: -635px -876px;
  width: 27px;
  height: 24px;
  vertical-align: middle;
  margin-right: 10px;
  display: inline-block;
}

.translateIcon2 {
  background-position: -676px -876px;
  width: 27px;
  height: 24px;
  vertical-align: middle;
  margin-right: 10px;
  display: inline-block;
}

.authorInfoAndTranslateBtn .updated-info.row {
  margin-top: 0;
}

.authorInfoAndTranslateBtn .translateBtn {
  padding: 5px 10px;
  font-size: 15px;
  font-weight: 400;
  line-height: 1.73;
  color: #3d8ff2;
  border-radius: 3px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  text-transform: none;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
}

/* Missed CSS Fix */
.custom-cardDisplay .sliderCardInfo {
  width: 275px;
  display: inline-block;
  padding: 0;
  white-space: initial;
}

.pageData p,
.pageData li,
.pageData a {
  font-size: 15px;
  line-height: 26px;
}

.two-cardDisplay .sliderCardInfo {
  width: 48.4%;
  display: inline-block;
  padding: 20px;
  margin-right: 18px;
}

.two-cardDisplay .sliderCardInfo:last-child {
  margin-right: 0;
}

.two-cardDisplay a {
  color: var(--primary-font-color);
  font-weight: 600;
}

.two-cardDisplay a:hover {
  color: var(--anchor-textclr);
}

.two-cardDisplay .customSliderCards .sliderCardInfo {
  min-height: 180px;
}

.two-cardDisplay .viewAllDiv {
  min-height: 160px;
}

.custom-cardDisplay .viewAllDiv {
  min-height: inherit;
}

.custom-cardDisplay .viewAllDiv {
  min-height: 303px;
}

.two-cardDisplay .viewAllIcon {
  background-position: 424px -73px;
  margin: 0 auto;
  margin-bottom: 5px;
  width: 44px;
  height: 40px;
}

.viewAllDiv a {
  color: var(--color-red);
}

.otherCategorySection {
  position: relative;
  padding-bottom: 10px;
}

.ohterCategoryArticles .row {
  margin: 0;
  flex-wrap: nowrap;
  overflow: auto;
}

.ohterCategoryArticles .row::-webkit-scrollbar {
  display: none;
}

.ohterCategoryArticles .categoryArticles {
  flex-basis: 8%;
  margin-right: 20px;
  margin-bottom: 10px;
}

.ohterCategoryArticles .categoryArticles:hover p {
  color: var(--anchor-textclr);
}

.ohterCategoryArticles .categoryArticles:last-child {
  margin-right: 0;
}

.ohterCategoryArticles .categoryArticlesImg {
  text-align: center;
  padding: 10px;
  border: var(--border-line);
  margin-bottom: 7px;
}

.ohterCategoryArticles .categoryArticlesImg .courseSprite {
  margin: 0;
}

.ohterCategoryArticles p {
  padding: 0;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
}

.ohterCategoryArticles a {
  text-decoration: none;
}

.ohterCategoryArticles .row {
  flex-wrap: nowrap;
  overflow: auto;
}

.ohterCategoryArticles .categoryArticles p {
  font-size: 14px;
}

.otherCategorySection .scrollRight {
  right: -20px;
}

.otherCategorySection .scrollLeft {
  top: 50%;
  left: -20px;
}

.pageData {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.pageData h2 {
  font-size: 18px;
  line-height: 28px;
  padding: 8px 20px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  text-transform: uppercase;
  position: relative;
}

.pageData h2 a {
  font-size: 18px;
  line-height: 24px;
  color: var(--color-red);
  font-weight: 500;
  text-transform: capitalize;
  display: block;
}

.pageData h2.row {
  display: flex;
}

.pageData p,
.pageData li,
.pageData a {
  font-size: 15px;
  line-height: 26px;
}

.pageData p {
  color: var(--primary-font-color);
  padding-bottom: 15px;
}

.pageData h3 {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}

.pageData h4 {
  padding-bottom: 10px;
  line-height: 24px;
  font-weight: 500;
}

.courseSprite {
  display: inline-block;
  background: url(../../images/course_sprite.webp);
  text-align: left;
  overflow: hidden;
  margin-right: 16px;
  width: 68px;
  height: 67px;
  vertical-align: middle;
  cursor: pointer;
}

.engineering {
  background-position: -123px -28px;
}

.management {
  background-position: -394px -117px;
}

.science {
  background-position: -305px -291px;
}

.pharmacy {
  background-position: -215px -28px;
}

.law {
  background-position: -305px -117px;
}

.education {
  background-position: -305px -200px;
}

.dental {
  background-position: -123px -292px;
}

.medical {
  background-position: -305px -27px;
}

.agriculture {
  background-position: -28px -292px;
}

.design {
  background-position: -27px -28px;
}

.commerce {
  background-position: -214px -202px;
}

.architecture {
  background-position: -215px -292px;
}

.arts {
  background-position: -393px -27px;
}

.paramedical {
  background-position: -28px -203px;
}

.computer {
  background-position: -122px -203px;
}

.mass-communication {
  background-position: -215px -116px;
}

.hotel-management {
  background-position: -27px -115px;
}

.aviation {
  background-position: -395px -202px;
}

.veterinary {
  background-position: -122px -115px;
}

.animation {
  background-position: -400px -291px;
}

.vocational-courses {
  background-position: -27px -374px;
}

.photoGallery .row {
  margin: 0;
}

.photoGallery h2.row {
  margin-bottom: 20px;
}

.photoGallery .picture {
  flex-basis: 23.7%;
  border-radius: 50%;
  margin-right: 20px;
  border-radius: 4px;
  border: var(--border-line);
  overflow: hidden;
}

.photoGallery .picture img {
  height: 206px;
  display: block;
  margin: 0 auto;
}

.photoGallery .picture:last-child,
.photoGallery .picture:nth-of-type(4n) {
  margin-right: 0;
}

/*GMU-471*/
.getSupport {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 0px;
  z-index: 5;
  /*display: flex;*/
  gap: 18px;
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
  color: #282828;
  align-items: center;
  justify-content: center;
  min-height: 58px;
  display: none;
}

.getSupport .getSupport__subheading {
  display: inline-block;
}

.getSupport .button__row__container {
  display: flex;
  gap: 13px;
  align-items: center;
}

.getSupport .row {
  display: none;
}

.getSupport button {
  border-radius: 2px;
  font-size: 13px;
  padding: 6px 4px;
  width: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.getSupport button:last-child {
  margin-left: 0;
}

.button__row__container {
  min-width: 300px;
}

.container nav {
  width: 100%;
  padding: 0px 15px;
}

/**/
.article-aside {
  height: 100%;
  padding-bottom: 20px;
}

.articleInfo p:has(img)+p {
  text-align: center;
}

.table-content-ctn {
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  margin-bottom: 40px;
}

.table-content-heading-article {
  background-color: #f5f5f5;
  padding: 10px 16px;
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
  color: #282828;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.downArrowIcon,
.upArrowIcon {
  background-position: -151px -125px;
  width: 18px;
  height: 11px;
}

.table-content-article {
  padding: 0px 0px 10px 0px;
  margin: 0 0 10px 0;
  max-height: 200px;
  overflow-y: auto;
}

.table-content-article li {
  list-style-type: none;
  position: relative;
  padding: 0 30px;
  margin-top: 10px;
}

.rotate {
  transform: rotate(180deg);
  top: 0px !important;
}

a {
  color: #3d8ff2;
  text-decoration: none;
  cursor: pointer;
}

.table-content-ctn ::-webkit-scrollbar {
  width: 8px;
}

.table-content-ctn ::-webkit-scrollbar-thumb {
  background: #d8d8d8;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 5px #d8d8d8;
}

.table-content-article {
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s ease-out, padding 0.4s ease-out;
}

.table-content-article.open {
  max-height: 200px;
  padding: 10px 0;
  overflow: auto;
}

ul.table-content-article.open li:before {
  left: 5px;
}

.faq_answer p {
  line-height: 26px !important;
}

.faq_answer span {
  font-size: 15px !important;
  background-color: unset !important;
}

.faq_answer li {
  white-space: unset !important;
  font-size: 15px !important;
}

/* Scroll Top CSS */
.scrollToh2CSS {
  scroll-margin-top: 53px;
}

.authorAndDate {
  margin-bottom: 10px;
}

.col-md-6.cta-btn-row-fix {
  justify-content: end;
  display: flex;
}

.authorInfoAndTranslateBtn button.freeScholarship {
  background-color: #0966c2;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton {
  max-width: 165px;
  font-weight: 500;
  font-size: 14px;
}

.pageDescription .row .ctaRow .primaryBtn,
.pageDescription .row .ctaRow button.primaryBtn {
  min-width: 200px;
  max-width: 200px;
  width: 200px;
}

.pageDescription .row .ctaRow button.primaryBtn {
  min-width: 222px;
}

.pageDescription .lead-cta button:nth-child(2) {
  margin-left: 6px;
}

.lead-cta {
  width: 100%;
  display: flex;
  gap: 10px;
  justify-content: center;
}

@media (max-width: 1023px) {

  .four-cardDisplay .sliderCardInfo,
  .custom-cardDisplay .sliderCardInfo {
    margin-right: 6px;
    width: 224px;
    display: inline-block !important;
    min-height: 274px;
  }

  .four-cardDisplay .sliderCardInfo:nth-of-type(4n),
  .custom-cardDisplay .sliderCardInfo:nth-of-type(4n) {
    margin-right: 6px;
  }

  .four-cardDisplay .sliderCardInfo:last-child,
  .custom-cardDisplay .sliderCardInfo:last-child {
    margin-right: 0;
  }

  .four-cardDisplay .sliderCardInfo img,
  .custom-cardDisplay .sliderCardInfo img {
    height: 168px;
  }

  .four-cardDisplay .sliderCardInfo .textDiv,
  .custom-cardDisplay .sliderCardInfo .textDiv {
    padding: 10px;
  }

  .four-cardDisplay .sliderCardInfo .widgetCardHeading,
  .custom-cardDisplay .sliderCardInfo .widgetCardHeading {
    font-weight: 400;
  }

  .four-cardDisplay .sliderCardInfo.mobileOnly,
  .custom-cardDisplay .sliderCardInfo.mobileOnly {
    vertical-align: bottom;
  }

  .four-cardDisplay .sliderCardInfo+.mobileOnly .viewAllDiv {
    min-height: 266px;
  }

  .sideBarSection {
    margin-bottom: 10px;
  }

  .sideBarSection .sidebarHeading {
    margin: 10px;
    margin-bottom: 0;
  }

  .sideBarSection .listCard {
    padding: 10px;
  }

  .sideBarSection .sidebarImgDiv {
    margin-right: 10px;
    flex-basis: 56px;
  }

  .sideBarSection .sidebarTextLink {
    flex-basis: calc(100% - 66px);
  }

  .sideBarSection .sidebarTextLink .cardText {
    padding-bottom: 5px;
  }

  .viewAllDiv {
    min-height: 274px;
  }
/* 
  .viewAllDiv {
    min-height: 274px;
  } */

  .sidebarImgDiv {
    flex-basis: 56px;
    margin-right: 10px;
    min-height: 56px;
  }

  .sidebarImgDiv img {
    width: 56px;
    height: 56px;
  }

  .listCard:last-child .recentnewsDiv.row {
    margin: 0;
  }

  /* .fixedExamRelatedDiv {
    border: none;
    box-shadow: 0 2px 4px 0 rgb(0 0 0/12%);
  } */

  .fixedExamRelatedDiv {
    position: fixed;
    top: 0;
    z-index: 2;
    width: 100%;
    max-width: 1206px;
    margin: 0 auto;
    height: 50px;
    margin-left: -10px;
    border: none;
    box-shadow: 0 2px 4px 0 rgb(0 0 0/12%);
  }

  .containerMargin {
    margin-top: -155px;
  }

  /*.getSupport {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 10px;
    border-radius: 0;
    z-index: 1;
  }

  .getSupport .row {
    display: none;
  }

  .getSupport button {
    width: 49%;
    border-radius: 2px;
    font-size: 13px;
    padding: 6px 4px;
  }

  .getSupport button.applyScholarship {
    margin-left: 0;
  }*/

  .examInfo {
    padding: 10px;
  }

  .examInfo h2 {
    padding: 8px 10px;
    font-size: 15px;
    margin-bottom: 10px;
    text-transform: uppercase;
  }

  .examInfo h3 {
    font-size: 15px;
  }

  .liveApllicationFormsInner .row,
  .clgWithCourseInner .row {
    overflow: auto;
    white-space: nowrap;
    display: block;
  }

  .liveApllicationFormsInner .row .applicationDiv,
  .liveApllicationFormsInner .row .clgWithCourseDiv,
  .clgWithCourseInner .row .applicationDiv,
  .clgWithCourseInner .row .clgWithCourseDiv {
    margin-right: 10px;
    margin-bottom: 0;
    display: inline-block !important;
    width: 86%;
    max-width: 86%;
    white-space: normal;
    vertical-align: middle;
    overflow: auto;
  }

  .liveApllicationFormsInner .clgWithCourseDiv .viewAllDiv,
  .clgWithCourseInner .clgWithCourseDiv .viewAllDiv {
    min-height: 350px;
  }

  .clgWithCourseInner .row .clgWithCourseDiv:nth-of-type(5n + 0) {
    margin-right: 10px;
  }

  .pageDescription {
    padding: 20px;
  }

  .pageDescription .row .col-md-12,
  .pageDescription .row .col-md-6 {
    padding: 0;
  }

  .pageDescription .row .ctaColumn {
    width: 100%;
  }

  .pageDescription .row .ctaRow {
    width: 100%;
    display: flex;
    gap: 10px;
  }

  .pageDescription .row .ctaRow .primaryBtn,
  .pageDescription .row .ctaRow button.primaryBtn {
    min-width: unset;
    max-width: unset;
    width: 50%;
    flex-basis: 50%;
    font-size: 13px !important;
  }

  .pageDescription .lead-cta button:nth-child(2) {
    margin-left: 0;
  }

  .pageDescription .liveIcon {
    width: 40px;
    height: 17px;
    background-position: -26px -329px;
    vertical-align: text-top;
    float: none;
  }

  .pageDescription h1 {
    font-size: 18px;
    font-weight: 400;
    line-height: 1.56;
    color: #282828;
    padding-bottom: 10px;
    display: inline;
  }

  .pageDescription .updated-info.row {
    align-items: flex-start;
    flex-wrap: nowrap;
  }

  .pageDescription .updated-info.row .updatedDetails {
    flex-wrap: wrap;
  }

  .pageDescription .updated-info.row ul {
    position: unset;
    margin-left: 0;
  }

  .sideBarSection {
    margin-bottom: 10px;
    width: 100%;
  }

  .sideBarSection .sidebarHeading {
    margin: 10px;
    margin-bottom: 0;
  }

  .sideBarSection .listCard {
    padding: 10px;
  }

  .sideBarSection .sidebarImgDiv {
    flex-basis: 56px;
    margin-right: 10px;
  }

  .sideBarSection .sidebarImgDiv img {
    width: 56px;
    height: 56px;
  }

  .sideBarSection .sidebarTextLink {
    flex-basis: calc(100% - 66px);
  }

  .sideBarSection .sidebarTextLink p {
    font-size: 12px;
    line-height: 16px;
  }

  .sideBarSection .sidebarTextLink .applyText {
    padding-top: 5px;
  }

  .customSlider .scrollLeft,
  .customSlider .scrollRight {
    display: none !important;
  }

  .blueBgDiv {
    background: var(--topheader-bg);
    width: 100%;
    height: 167px;
  }

  .bannerImg img {
    max-height: 400px;
  }


  .dataContainer,
  .articleInfo {
    padding: 10px;
  }

  .dataContainer h2,
  .articleInfo h2 {
    font-size: 15px;
    padding: 8px 10px;
    margin-bottom: 10px;
  }

  .faq_section {
    padding: 10px;
  }

  .faq_section h2 {
    margin-bottom: 10px;
  }

  .faq_section p {
    font-size: 15px;
    padding: 10px;
    padding-left: 5px;
  }

  .faq_section .faq_answer {
    color: #787878;
    padding: 10px;
  }

  .trendingArtilce p,
  .recentArticles p,
  .recentnews p,
  .trendingNews p {
    padding: 10px;
  }

  .trendingArtilerList,
  .recentArticlesList,
  .recentnewsList {
    padding: 10px;
  }

  .setAlarmDiv .primaryBtn {
    border-radius: 0;
  }

  .pageDescription .updated-info.row ul {
    z-index: 0;
  }

  .articleInfo .latestUpdates {
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .articleInfo .latestUpdates .cardHeading {
    font-size: 15px;
    font-weight: 500;
    line-height: 26px;
    padding-bottom: 10px;
  }

  .articleInfo .latestUpdates ul {
    margin: 0;
    padding-left: 20px;
  }

  .articleInfo .latestUpdates ul li {
    position: relative;
  }

  .articleInfo .latestUpdates ul li:before {
    content: "";
    position: absolute;
    width: 15px;
    height: 15px;
    left: -20px;
    top: 5px;
    background: url(../../images/master_sprite.webp);
    background-position: 339px -374px !important;
  }

  .articleInfo .latestUpdates ul li span {
    font-size: 14px;
    color: #ff4e53;
  }

  .articleInfo .latestUpdates ul li a {
    cursor: pointer;
    color: #3d8ff2;
    text-decoration: none;
  }

  .articleInfo .latestUpdates {
    padding: 10px;
  }

  .audio-text {
    font-size: 16px;
  }

  .authorInfoAndTranslateBtn {
    flex-wrap: wrap;
    gap: 10px;
  }

  .translateBtn {
    flex-basis: 100%;
  }

  /* Fixes */
  .otherCategorySection .scrollLeft,
  .otherCategorySection .scrollRight {
    display: none !important;
  }

  .pageData,
  .reviewsSection {
    padding: 10px;
    margin-bottom: 10px;
    word-break: break-word;
  }

  .pageData h2,
  .reviewsSection h2 {
    font-size: 15px;
    line-height: 24px;
    padding: 8px 10px;
    margin-bottom: 10px;
  }

  .pageData h2,
  .pageData h3 {
    background: 0 0;
    padding-left: 0;
    font-size: 15px;
  }

  .pageData p,
  .reviewsSection p {
    padding-bottom: 10px;
  }

  .two-cardDisplay .sliderCardInfo {
    padding: 10px;
    width: 271px;
    margin-right: 6px;
  }

  .two-cardDisplay .sliderCardInfo .row {
    display: block;
  }

  .two-cardDisplay .sliderCardInfo .clgLogo {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .two-cardDisplay .sliderCardInfo p:first-child {
    font-size: 14px;
  }

  .getSupport button.applyNow {
    margin-left: 0;
    width: 49%;
  }

  .photoGallery h2.row {
    margin-bottom: 10px;
  }

  .photoGallery .row {
    display: block;
    overflow: auto;
    white-space: nowrap;
  }

  .photoGallery .picture {
    display: inline-block;
    width: 224px;
    margin-right: 5px;
  }

  .photoGallery .picture.mobileOnly {
    display: inline-block !important;
    margin-left: 5px;
  }

  .photoGallery .picture .viewAllDiv {
    min-height: 170px;
  }

  .photoGallery .picture img {
    height: 168px;
    width: 100%;
  }

  .getSupport .getSupport__subheading {
    display: none;
  }

  .getSupport .button__row__container {
    width: 100%;
  }

  .getSupport button {
    flex-grow: 1;
  }

  .container nav {
    padding: 0px;
  }

  .getSupport .button__row__container {
    gap: 5px;
  }

  .getSupport {
    padding: 3px;
  }

  .getSupport button {
    padding: 6px 8px;
    font-size: 12px;
  }
}

@media (min-width: 1023px) and (max-width: 1236px) {
  .col-md-4 .getSupport button {
    width: 125px;
  }
}

/** Article Practice Set***/
.downloadIcon {
  background-position: -121px -258px;
  width: 16px;
  height: 16px;
}

.rightArw {
  background-position: -314px -594px;
  width: 18px;
  height: 18px;
}

.gmu-mock-panel {
  width: 100%;
  height: auto;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #D9D9E5;
  background-color: #fff;
  margin: 30px 0;
}

.gmu-mock-heading {
  background: #F5F5F5;
  font-size: 20px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.10000000149011612px;
  text-align: left;
  padding: 13px 14px;
}

.gmu-mock-subHeading {
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
  text-align: left;
  margin-bottom: 10px;
}

.gmu-mock-content {
  margin: 16px 0 5px 0;
  overflow: hidden;
  max-height: 90px;
  transition: max-height 0.3s ease;
}

.gmu-mock-content.expanded {
  max-height: 1000px;
}

.gmu-readMore-btn {
  outline: none;
  padding: 0;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  font-size: 14px;
  font-weight: 700;
  line-height: 24px;
  text-align: center;
  color: #F05156;
}

.gmu-testList {
  margin: 16px 0 0 0;
  padding: 0;
  list-style-type: none;
}

.gmu-testListItem {
  padding: 16px 24px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #E4E4E4;
  border-radius: 8px;
  overflow: hidden;
}

.gmu-testListItem:not(:last-child) {
  margin-bottom: 16px;
}


.gmu-testList-left {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

.gmu-testList-left h2 {
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  text-align: center;
  color: #282828;
}

.gmu-testList-applyBtn {
  width: auto;
  height: 35px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: 2px;
  border-radius: 4px;
  color: #F05156 !important;
  border: none;
  outline: none;
  background-color: transparent;
}



/**************** MOCK TABLE ****************/

#gmu-mock-table {
  margin-top: 16px;
}

#gmu-mock-table>thead>tr>th {
  background: #F2F2F2;
  font-size: 18px;
  font-weight: 600;
  line-height: 18px;
}

table#gmu-mock-table td:first-child {
  background: #F2F2F2;
  border-right: 5px solid #fff;
}

table#gmu-mock-table td {
  background: #F2F2F2;
  border-right: 5px solid #fff;
  border-bottom: 5px solid #fff;
}

table#gmu-mock-table tr td:last-child {
  border-right: none;
}

table#gmu-mock-table th {
  background: #F2F2F2;
  border-bottom: 5px solid #fff;
}

#gmu-mock-table>tbody>tr>td {
  font-size: 16px;
  font-weight: 400;
  line-height: 23.9px;
  text-align: left;
  color: #626262;
}

.gmu-mock-downloadBtn {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
  width: 172px;
  height: 44px;
  border-radius: 4px;
  background: #FF4E53;
  font-size: 14px;
  font-weight: 700;
  line-height: 24px;
  text-align: center;
  color: #fff;
  outline: none;
  border: none;
}

/************ QNA Panel Tabs and Tabs Content **************/
.gmu-mock-listDiv {
  position: relative;
}

.gmu-mock-list {
  position: relative;
  margin: 0 20px 40px 0;
  padding: 0;
  display: flex;
  align-items: center;
  list-style-type: none;
  width: 100%;
  overflow-x: auto;
}

.gmu-mock-listDiv::after {
  position: absolute;
  content: '';
  width: 105.3%;
  height: 5px;
  top: 45px;
  left: -20px;
  border-bottom: 5px solid #f3f2ef;
}

.gmu-mock-list::-webkit-scrollbar {
  appearance: none;
  height: 0;
  width: 0;
}

.gmu-mock-listItem {
  padding: 0 16px 10px 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 14px;
  text-align: left;
  color: #8E8E8E;
  white-space: nowrap;
  transition: all 0.2s linear;
  border-bottom: 1px solid #CFCFCF;
  cursor: pointer;
}

.gmu-mock-listItem.active {
  color: #FF4E53;
  font-weight: 500;
  border-bottom: 2px solid #FF4E53;
}

.gmu-mock-questionContent {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 1px solid #D8D8D8;
  border-radius: 4px;
}

.gmu-mock-qno {
  width: 100%;
  padding: 16px;
  font-size: 18px;
  font-weight: 500;
  line-height: 22px;
  text-align: left;
  border-bottom: 1px solid #d8d8d8;
}

.gmu-mock-desc {
  padding: 15px 24px 0 15px;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  text-align: left;
  color: #717171;
}

.gmu-mock-optionList {
  padding: 0;
  margin: 24px 16px;
  counter-reset: item;
  list-style-type: none;
}

.gmu-mock-optionListItem {
  width: 100%;
  height: auto;
  border-radius: 4px;
  padding: 10px 80px;
  font-size: 18px;
  font-weight: 400;
  line-height: 22px;
  text-align: left;
  background: #F5F5F5B2;
  border: 1px solid #F5F5F5B2;
  cursor: pointer;
  transition: all 0.2s linear;
  position: relative;
  border-radius: 30px 4px 4px 30px;
}

.gmu-mock-optionListItem::before {
  content: counter(item, upper-alpha);
  counter-increment: item;
  position: absolute;
  width: 44px;
  height: 45px;
  font-weight: 500;
  left: -1px;
  top: 51%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transform: translateY(-50%);
  border: 1px solid #d8d8d8;
  background: #FFFBFB;
}

.gmu-mock-optionListItem:hover {
  border: 1px solid #d8d8d8;
}

.gmu-mock-optionListItem:not(:last-child) {
  margin-bottom: 8px;
}

.gmu-mock-actions {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: end;
  padding: 0 16px 24px;
}

.gmu-mock-actions button {
  height: 48px;
  width: 200px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  text-align: center;
  outline: none;
  border-radius: 4px;
}

.gmu-mock-actionCorrect {
  border: 1px solid #FF4E53;
  color: #FF4E53;
  background-color: #fff;
}

.gmu-mock-actionExplain {
  border: none;
  background: #FF4E53;
  color: #fff;
  /* box-shadow: ; */
}

.gmu-mock-explanationCtn {
  display: none;
  margin: 0 24px 24px;
  padding: 20px;
  border-top: 4px solid #168158 !important;
  border: 1px solid #D8D8D8;
  background: #F7F9FB;
}

.gmu-mock-explanationCtn h1 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 500;
  line-height: 36.19px;
  text-align: left;
  color: #168158;
  margin-bottom: 6px;
}

.gmu-mock-explanationCtn p {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  text-align: left;
  color: #717171;
}

.checkIcon {
  background-position: -707px -554px;
  width: 14px;
  height: 16px;
}


/********** True Condition *********/
.gmu-mock-optionListItem.isTrue {
  background: #AFFFE03D;
  border: 1px solid #168158;
  color: #168158;
}

.gmu-mock-optionListItem.isTrue::before,
.gmu-mock-optionListItem.isTrue:hover {
  border: 1px solid #168158;
  color: #168158;
}

.gmu-mock-optionListItem.selected {
  background: rgba(9, 101, 194, 0.1);
  border: 1px solid #0966c2;
  color: #0966c2;
}

.gmu-mock-optionListItem.selected::before,
.gmu-mock-optionListItem.selected:hover {
  border: 1px solid #0966c2;
  color: #0966c2;
}

/********** False Condition *********/

.gmu-mock-optionListItem.isFalse {
  background: #FF4E531A;
  border: 1px solid #DC2026;
  color: #DC2026;
}

.gmu-mock-optionListItem.isFalse::before,
.gmu-mock-optionListItem.isFalse:hover {
  border: 1px solid #DC2026;
  color: #DC2026;
}

/********** End False Condition *********/
.gmu-mock-tabContent {
  display: none;
}

.gmu-mock-tabContent.active {
  display: block;
}

.gmu-mock-atf {
  padding: 24px;
  width: 100%;
  height: 100px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #B8B8B8;
  background: linear-gradient(90deg, #FFFFFF 76.5%, #D7D6E6 100%);
}

.gmu-mock-atfLeft h1 {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  color: #5A5695;
}

.gmu-mock-atfLeft p {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  color: #282828;
}

.gmu-mock-atfBtn {
  width: 225px;
  height: 42px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #FFFFFF;
  text-align: center;
  border-radius: 4px;
  border: none;
  outline: none;
  background: #5A5695;
}

.horizontal-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
  display: flex;
}

.horizontal-list li {
  margin-right: 10px;
}

@media (max-width:1023px) {
  .downloadIcon {
      background-position: -121px -258px;
      width: 16px;
      height: 16px;
  }

  .gmu-mock-panel {
      padding: 16px 10px;
      margin: 24px 0;
  }

  .gmu-mock-heading {
      font-size: 16px;
      line-height: 24px;
      padding: 10px;
  }

  .gmu-mock-subHeading {
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 10px;
  }

  .gmu-mock-content {
      margin: 10px 0 5px 0;
  }

  .gmu-mock-content.expanded {
      max-height: 100%;
  }

  .gmu-testListItem {
      padding: 16px;
      height: auto;
      border-radius: 4px;
      flex-direction: column;
  }

  .gmu-testList-left {
      flex-direction: column;
      gap: 8px;
  }

  .gmu-testList-applyBtn {
      width: auto;
      height: 20px;
  }

  .gmu-testList-viewBtnCtn {
      display: flex;
      justify-content: center;
      align-items: center;
  }

  /**************** MOCK TABLE ****************/

  #gmu-mock-table {
      margin-top: 10px;
  }

  #gmu-mock-table>thead>tr>th {
      font-size: 16px;
      line-height: 16px;
  }

  #gmu-mock-table>tbody>tr>td {
      min-width: unset;
      font-size: 14px;
      line-height: 16px;
  }

  .gmu-mock-downloadBtn {
      width: 100%;
  }

  /************ QNA Panel Tabs and Tabs Content **************/

  .gmu-mock-listDiv::after {
      width: 109.3%;
  }

  .gmu-mock-questionContent {
      margin-top: 16px;
      margin-bottom: 16px;
  }

  .gmu-mock-qno {
      padding: 10px 16px;
      font-size: 14px;
  }

  .gmu-mock-desc {
      padding: 10px 16px;
  }

  .gmu-mock-optionList {
      margin: 10px 16px 16px;
  }

  .gmu-mock-optionListItem {
      height: auto;
      padding: 8px 8px 8px 60px;
      font-size: 14px;
      border-radius: 30px 4px 4px 30px;
  }

  .gmu-mock-optionListItem::before {
      width: 36px;
      height: 36px;
  }

  .gmu-mock-optionListItem:not(:last-child) {
      margin-bottom: 8px;
  }

  .gmu-mock-actions {
      gap: 5px;
      padding: 0 16px 16px;
  }

  .gmu-mock-actions button {
      height: 48px;
      width: 50%;
      flex-basis: 50%;
      padding: 0;
  }

  .gmu-mock-explanationCtn {
      margin: 0 16px 16px;
      padding: 10px 16px 16px;
  }

  .gmu-mock-explanationCtn h1 {
      font-size: 14px;
  }

  .gmu-mock-explanationCtn p {
      font-size: 14px;
      line-height: 22px;
  }

  .gmu-mock-atf {
      padding: 16px;
      height: unset;
      flex-direction: column;
  }

  .gmu-mock-atfBtn {
      width: 100%;
      font-size: 14px;
      margin-top: 16px;
  }
}


.horizontal-list li {
  color: white;
  padding: 10px 20px;
  margin: 5px;
  border-radius: 30px;
  text-align: center;
  cursor: pointer;
  transition: background 0.3s;
  border-bottom: none;
  color: #ff4e53;
  font-weight: 500;
}
.horizontal-list li.active-li {
  background-color: #fff;
  border: 1px solid #ff4e53;
  color: #ff4e53;
}
.horizontal-list li.active-li  a{
  color: #ff4e53;
  font-weight: 600;
}
.horizontal-list li.active-li  button{
  color: #ff4e53;
  font-weight: 600;
  background-color: #fff;
}
.horizontal-list li a{ color: #ff4e53 !important;}
.horizontal-list li a{ color: #ff4e53 !important;}

.horizontal-list {
  list-style: none;
  display: flex;
  padding: 0;
  border-radius: 30px;
  padding: 3px;
  background-color: #ff000017;
  margin-top:20px;
  margin-bottom:20px;
  justify-content: space-between;
  /* margin-left: 100px; */
}
.button-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.custom-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  border: 1px solid #FF4E53;
  color: #282828;
  background-color: transparent;
  font-size: 16px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.grow img{
 width: 200px;
}
.table-responsive table{width:100% !important}

/*****************/
.article-links{
  position: static !important;
  margin-bottom: 30px;
}

