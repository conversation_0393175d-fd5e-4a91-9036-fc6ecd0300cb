:root {
  --primary-font-color: #282828;
}

h3,
h4 {
  padding-bottom: 10px;
  line-height: 24px;
}

.updatedBy {
  padding-right: 5px;
}

.updated-info.row {
  margin: 0;
  align-items: center;
  margin-top: 20px;
  line-height: 20px;
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 20px;
}

.updated-info.row img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  vertical-align: middle;
}

.updated-info.row .updatedBy p {
  display: inline-block;
  font-weight: var(--font-semibold);
}

.updated-info.row a {
  text-decoration: none;
  color: var(--anchor-textclr);
}

.updated-info.row ul {
  margin: 0;
  padding: 0;
}

.updated-info.row ul p {
  display: inline-block;
}

.updated-info.row ul li {
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
}

.examRelataedLinks {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  padding: 0;
  margin-bottom: 20px;
  position: relative;
  border: none !important;
}

.examRelataedLinks .btn_left,
.examRelataedLinks .btn_right {
  position: absolute;
  width: 48px;
  height: 48px;
  background-color: #fff;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  top: 0;
  cursor: pointer;
}

.examRelataedLinks .btn_left {
  left: 0;
}

.examRelataedLinks .btn_right {
  right: 0;
}

.examRelataedLinks ul {
  margin: 0;
  white-space: nowrap;
  overflow: auto;
  padding: 0 10px;
}

.examRelataedLinks ul::-webkit-scrollbar {
  display: none;
}

.examRelataedLinks ul li {
  display: inline-block;
  padding: 0 5px;
  font-size: 14px;
  line-height: 44px;
  list-style-type: none;
}

.examRelataedLinks ul li a {
  display: block;
  color: #787878;
  line-height: 24px;
  font-size: 14px;
  text-decoration: none;
  border-bottom: none;
  padding: 8.5px 0;
}

.examRelataedLinks ul li span.activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
  font-weight: var(--font-semibold);
}

.fixedExamRelatedDiv {
  position: fixed;
  top: 0;
  z-index: 2;
  width: 100%;
  max-width: 1206px;
  margin: 0 auto;
}

.examInfo,
.examDownload,
.examPdfonEmail {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  overflow: hidden;
  height: auto;
  transition: all 1s;
}

.examInfo.pageInfo {
  height: 410px;
  margin: 0;
  transition: all 1s;
}

.examInfo p {
  font-size: 15px;
  line-height: 24px;
  color: var(--primary-font-color);
  padding-bottom: 15px;
}

.examInfo h2,
.examDownload h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  margin-bottom: 20px;
}

.examInfo h3 {
  line-height: 24px;
  font-size: 17px;
  font-weight: var(--font-semibold);
  padding-bottom: 10px;
}

.liveApllicationFormsInner .row,
.clgWithCourseInner .row {
  margin: 0;
}

.liveApllicationFormsInner .row .applicationDiv,
.liveApllicationFormsInner .row .clgWithCourseDiv,
.clgWithCourseInner .row .applicationDiv,
.clgWithCourseInner .row .clgWithCourseDiv {
  margin-right: 20px;
  flex-basis: 18.6%;
}

.liveApllicationFormsInner .row .applicationDiv:last-child,
.liveApllicationFormsInner .row .clgWithCourseDiv:last-child,
.clgWithCourseInner .row .applicationDiv:last-child,
.clgWithCourseInner .row .clgWithCourseDiv:last-child {
  margin-right: 0;
}

.clgWithCourse {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px;
}

.clgWithCourse h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  text-transform: uppercase;
  margin-bottom: 20px;
}

.clgWithCourse h2.row {
  justify-content: space-between;
  margin: 0;
  margin-bottom: 20px;
}

.clgWithCourse h2 a {
  font-size: 14px;
  color: var(--color-red);
  text-decoration: none;
}

.clgWithCourseList {
  margin: 0;
}

.clgWithCourseDiv {
  border-radius: 4px;
  border: var(--border-line);
  overflow: hidden;
  padding: 20px;
  text-align: center;
}

.clgWithCourseDiv img {
  max-width: 80px;
  min-height: 80px;
  margin: 0 auto;
  display: block;
}

.clgWithCourseDiv figure {
  text-align: center;
  margin-bottom: 20px;
}

.clgWithCourseDiv p,
.clgWithCourseDiv a {
  font-size: 14px;
  line-height: 24px;
  padding-bottom: 10px;
  text-decoration: none;
}

.clgWithCourseDiv p.clgName,
.clgWithCourseDiv a.clgName {
  font-weight: var(--font-semibold);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 50px;
  padding: 0;
  margin-bottom: 14px;
}

.clgWithCourseDiv .avgFee {
  font-weight: var(--font-semibold);
}

.clgWithCourseDiv .avgFee span {
  color: #989898;
  display: block;
  font-weight: 400;
}

.clgWithCourseDiv button {
  border-radius: 3px;
  border: solid 1px var(--anchor-textclr);
  display: block;
  padding: 5px;
  text-align: center;
  background: var(--color-white);
  color: var(--anchor-textclr);
  font-size: 14px;
  line-height: 24px;
  font-weight: var(--font-500);
  width: 100%;
  margin-bottom: 10px;
}

.clgWithCourseDiv button:last-child {
  margin-bottom: 0;
}

.trendingArtilce {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  margin-bottom: 20px;
}

.trendingArtilce p {
  padding: 10px 20px;
  font-size: 16px;
  line-height: 24px;
  border-bottom: var(--border-line);
  font-weight: var(--font-semibold);
}

.trendingArtilerList {
  padding: 20px;
}

.dateLabel {
  background: #fff;
  width: 56px;
  height: 56px;
  color: #fb7739;
  font-weight: var(--font-semibold);
  font-size: 16px;
  line-height: 20px;
  text-align: center;
  margin-right: 16px;
  border-radius: 4px;
  padding: 7px;
  border: 1px solid #fb7739;
}

.dateLabel span {
  font-size: 24px;
}

.trendingArtilerDiv.row {
  margin: 0;
  flex-wrap: nowrap;
  margin-bottom: 16px;
  align-items: center;
}

.trendingArtilerDiv.row:last-child {
  margin-bottom: 0;
}

.trendingArtilerDiv img {
  max-width: 74px;
  max-height: 56px;
}

.trendingArtilerDiv .trendingArtileText a {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: 500;
  text-decoration: none;
}

.trendingArtilerDiv .trendingArtileText a:hover {
  color: #3d8ff2;
  text-decoration: underline;
}

.trendingArtilerDiv .trendingArtileText p {
  font-size: 14px;
  line-height: 24px;
  color: #989898;
  padding: 0;
  font-weight: 400;
  border: none;
}

.getSupport {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px;
}

.getSupport .row {
  margin: 0;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}

.getSupport img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}

.getSupport p {
  font-size: 18px;
  line-height: 26px;
}

.getSupport button {
  width: 161px;
  border-radius: 3px;
  font-size: 14px;
  line-height: 24px;
  padding: 6px;
  text-align: center;
  color: var(--color-white);
  font-weight: var(--font-bold);
  border: none;
}

.getSupport button.freeScholarship,
.getSupport button.getLeadForm {
  width: 218px;
  white-space: nowrap;
}

.getSupport button.freeScholarship {
  background: var(--topheader-bg);
}

.getSupport button.applyScholarship {
  background: var(--color-red);
  margin-left: 15px;
}

.quickLinks {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  padding: 0;
}

.quickLinks h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  text-transform: uppercase;
}

.quickLinks ul {
  padding: 10px 20px;
  margin: 0;
  max-height: 440px;
  overflow: auto;
}

.quickLinks ul::-webkit-scrollbar {
  width: 5px;
}

.quickLinks ul::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.quickLinks ul::-webkit-scrollbar-thumb {
  background: #ccc;
}

.quickLinks ul li {
  list-style-type: none;
}

.quickLinks ul li:last-child a {
  border-bottom: none;
}

.quickLinks ul li a {
  display: block;
  font-size: 14px;
  line-height: 24px;
  border-bottom: var(--border-line);
  text-decoration: none;
  color: var(--primary-font-color);
  padding: 8px 0;
}

.quickLinks ul li a:hover {
  color: #3d8ff2;
  text-decoration: underline;
}

.pageDescription,
.trendingArtilce,
.examInfo,
.aboutExam,
.downloadPdfSection,
.faq_section,
.liveApllicationForms,
.clgWithCourse,
.clgWithCourse,
.discussionForumSection,
.askUsQuestion,
.commentSection,
.examInfoSlider,
.latestInfoSection,
.examRelataedLinks,
.quickLinks,
.otherOptions,
.getSupport {
  box-shadow: none;
  border: var(--border-line);
}

.trendingArtilce,
.updated-info {
  background: var(--color-white);
}

.examHeroSection {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
  padding: 28px;
  margin-bottom: 20px;
  margin-top: 10px;
  box-shadow: none;
  border: 1px solid #d8d8d8;
}

.examHeroSection button.primaryBtn {
  padding: 5px 12px;
  /* max-width: 165px; */
  flex-grow: 1;
}

.examHeroSection .col-md-5 {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.commonHeroSection {
  padding: 20px;
  margin-top: 20px;
}

.commonHeroSection .heroHeader {
  align-items: center;
  margin: 0;
  flex-wrap: nowrap;
}

.commonHeroSection .heroHeader .imgContainer {
  max-width: 72px;
  max-height: 72px;
}

.commonHeroSection .heroHeader .headingContainer {
  margin-left: 10px;
}

.commonHeroSection .heroHeader .headingContainer h1 {
  font-size: 24px;
  font-weight: 400;
  line-height: 1.58;
  color: #282828;
  padding-bottom: 0;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.commonHeroSection .helpfulInfo {
  padding: 5px 10px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  background-color: rgba(9, 102, 194, 0.1);
  max-width: fit-content;
  margin: 0;
  margin-top: 6px;
}

.commonHeroSection .helpfulInfo .helpfulItem:nth-child(2) .spriteIcon {
  margin-left: 20px;
}

.commonHeroSection .helpfulInfo .helpfulItem span {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.71;
  color: #282828;
}

.commonHeroSection .helpfulInfo .helpfulItem .calenderIcon {
  background-position: -395px -931px;
  width: 15px;
  height: 16px;
  vertical-align: middle;
  margin-right: 5px;
}

.commonHeroSection .helpfulInfo .helpfulItem .listIcon {
  background-position: -373px -931px;
  width: 13px;
  height: 17px;
  vertical-align: middle;
  margin-right: 5px;
}

.commonHeroSection .ctaColumn {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
}

.commonHeroSection .ctaColumn .ctaRow {
  display: flex;
  gap: 20px;
  justify-content: flex-end;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton {
  /* max-width: 184px; */
  height: 36px;
  border-radius: 4px;
  border: solid 1px #ff4e53;
  background-color: rgba(255, 78, 83, 0.1);
  font-size: 16px;
  font-weight: 700;
  line-height: 1.5;
  color: #ff4e53;
  flex-grow: 1;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton .applyNowIcon {
  background-position: -543px -330px;
  width: 20px;
  height: 20px;
  vertical-align: middle;
  margin-left: 12px;
}

.commonHeroSection .ctaColumn .ctaRow .brochureButton {
  max-width: 126px;
  height: 36px;
  border-radius: 4px;
  background-color: #ff4e53;
  flex-grow: 1;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.71;
  color: #fff;
}

.commonHeroSection .ctaColumn .ctaRow .brochureButton .brochureButtonIcon {
  background-position: -513px -355px;
  width: 16px;
  height: 16px;
  margin-left: 10px;
  vertical-align: middle;
}

.updated-info.row .authorAndDate .authorName {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.43;
  color: #282828;
  text-transform: none;
}

.updated-info.row .authorAndDate p {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.29;
  color: #787878;
  padding-bottom: 0;
  margin-top: 2px;
}

.updated-info.row .authorAndDate .verifiedBlueTickIcon {
  background-position: -673px -557px;
  width: 17px;
  height: 16px;
  vertical-align: top;
  transform: scale(0.85);
  margin-left: 8px;
}

.examInfo .latestUpdates ul li span {
  font-weight: 500;
  font-size: 15px;
}

.examInfo .latestUpdates ul li a {
  font-size: 15px;
  font-weight: 500;
}

.examInfo .latestUpdates {
  margin-top: 10px;
  margin-bottom: 10px;
}

.examInfo .latestUpdates .cardHeading {
  color: #282828;
}

.lead-cta-exam-cls {
  /* height: 176px;
  /*cls issue
  display: inline-block;
  margin-bottom: 20px;*/
}

.getSupport {
  padding: 19px;
}

.pageData .latestUpdates,
.examInfo .latestUpdates {
  padding-left: 0;
}

.trendingArtilce p {
  font-weight: 500;
}

.trendingArtilerDiv .trendingArtileText a {
  font-weight: 400;
}

.articleSidebarSection .trendingArtilce {
  margin-bottom: 0;
}

.applicationDiv a.clgName,
a.clgName,
.examCategoryInfo p a {
  color: #282828;
  white-space: normal;
}

.applicationDiv a.clgName:hover,
a.clgName:hover,
.examCategoryInfo p a:hover {
  color: #3d8ff2;
  text-decoration: underline;
}

.topHeader .writeReview,
.topHeader .register {
  border-radius: 3px;
  font-weight: 500;
}

.clgWithCourseDiv a {
  padding: 5px;
}

.examInfoSlider h2,
.clgWithCourse h2,
.liveApllicationForms h2 {
  text-transform: none;
}

.applyWhiteIconCta {
  width: 20px;
  height: 20px;
  background-position: -191px -594px;
  vertical-align: middle;
  margin-left: 12px;
  position: static;
}

h2 {
  font-weight: 500;
}

table thead th {
  font-weight: 500;
}

a.ctaBtn,
button.ctaBtn,
.ctaBtn {
  text-transform: none;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton {
  /* max-width: 165px; */
  font-weight: 500;
  font-size: 14px;
}

.commonHeroSection .ctaColumn .ctaRow .brochureButton {
  max-width: 165px;
  font-weight: 500;
  font-size: 14px;
}

.removeFixedQuickLink {
  height: 0;
}

.examRelataedLinks .btn_left,
.examRelataedLinks .btn_right {
  height: 44px;
}

.left_angle,
.right_angle {
  margin: 12px 0;
}

.updatedBy {
  padding: 0;
}

.examRelataedLinks ul li span {
  padding: 8.5px 0;
}

.pageData .latestUpdates {
  margin-bottom: 0;
}

.commonHeroSection .helpfulInfo .helpfulItem .calenderIcon:last-of-type {
  margin-left: 10px;
}

table thead tr th,
table thead tr td,
table th,
.examInfo table thead p {
  background-color: #0d3d63;
  color: #fff;
}

table th,
table td {
  text-align: center;
}

table thead strong {
  color: inherit;
}

.table-responsive table thead tr td p {
  color: #fff;
}

.table-responsive table thead p strong {
  color: #fff;
}

.pageData h2 {
  margin-top: 20px;
}

.pageData h2:first-of-type {
  margin-top: 0;
}

.table-responsive {
  margin-bottom: 20px;
}

.quickLinks h2 {
  text-transform: none;
}

.examInfo table p {
  padding: 0;
}

.faq_section {
  margin-top: 20px;
}

.second-row-content {
  display: flex;
  justify-content: space-between;
  min-height: 42px;
}

.subNavDropDown a {
  display: inline-block !important;
}

.subNavDropDown:hover .caret {
  background-position: 651px -154px;
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
}

.subNavDropDown .subNavDropDownMenu {
  display: none;
  margin: 0;
  position: absolute;
  z-index: 2;
  padding: 5px 20px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.24);
  background-color: #fff;
}

.subNavDropDown .subNavDropDownMenu li {
  display: block;
  color: #787878;
  cursor: pointer;
}

.subNavDropDown .subNavDropDownMenu li:not(:last-child) {
  margin-bottom: 5px;
}

.subNavDropDown .subNavDropDownMenu li:hover {
  color: #ff4e53;
}

.subNavDropDown:hover .subNavDropDownMenu {
  display: block;
}

.btn_left,
.btn_right {
  z-index: 1;
}

.mobileSubNavDropDownMenu {
  display: none;
}

.subNavDropDown .subNavDropDownMenu li a {
  padding: 0;
  color: #787878;
}

.subNavDropDown .subNavDropDownMenu li a:hover {
  color: #ff4e53;
}

.examRelataedLinks ul:not(.subNavDropDownMenu) {
  height: 48px;
}

.examRelataedLinks .caret {
  padding: 0;
  border: 0;
  line-height: unset;
}

.examRelataedLinks ul li a.activeLink,
.examRelataedLinks ul li span.activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
  font-weight: var(--font-semibold);
  cursor: pointer;
}

.subNavActive {
  color: #ff4e53 !important;
}

.formHeadingDiv .formImg img {
  height: 100px;
  width: 100px;
}

.liveApllicationForms {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px;
}

.liveApllicationForms h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  text-transform: uppercase;
  margin-bottom: 20px;
}

.liveApllicationFormsInner .row,
.clgWithCourseInner .row {
  margin: 0;
}

.liveApllicationFormsInner .row .applicationDiv,
.liveApllicationFormsInner .row .clgWithCourseDiv,
.clgWithCourseInner .row .applicationDiv,
.clgWithCourseInner .row .clgWithCourseDiv {
  margin-right: 20px;
  flex-basis: 18.6%;
}

.liveApllicationFormsInner .row .applicationDiv:last-child,
.liveApllicationFormsInner .row .clgWithCourseDiv:last-child,
.clgWithCourseInner .row .applicationDiv:last-child,
.clgWithCourseInner .row .clgWithCourseDiv:last-child {
  margin-right: 0;
}

.liveApllicationFormsInner {
  position: relative;
}

.liveApllicationFormsInner .scrollRight,
.liveApllicationFormsInner .scrollLeft {
  top: 50%;
}

.liveApllicationFormsInner .scrollRight {
  right: -20px;
}

.liveApllicationFormsInner .scrollLeft {
  left: -20px;
  transform: translate(0px, -50%) rotate(180deg);
}

.liveApllicationFormsInner .row {
  flex-wrap: nowrap;
  overflow: auto;
  white-space: nowrap;
}

.liveApllicationFormsInner .row::-webkit-scrollbar {
  display: none;
}

.liveApllicationFormsInner .row .applicationDiv {
  width: 18.6%;
}

.liveApllicationFormsInner .row .applicationDiv:last-child {
  margin: 0;
}

.applicationDiv {
  border-radius: 4px;
  border: var(--border-line);
  padding: 20px 11px;
  text-align: center;
}

.applicationDiv a {
  text-decoration: none;
}

.applicationDiv figure {
  margin-bottom: 20px;
}

.applicationDiv img {
  max-width: 80px;
  min-height: 80px;
  margin: 0 auto;
  display: block;
}

.applicationDiv p,
.applicationDiv a {
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.applicationDiv p:first-child,
.applicationDiv a:first-child {
  font-weight: var(--font-semibold);
}

.applicationDiv p a,
.applicationDiv a a {
  margin-bottom: 0;
}

.applicationDiv p.clgName,
.applicationDiv a.clgName {
  min-height: 50px;
}

.applicationDiv p span,
.applicationDiv a span {
  color: #989898;
  display: block;
}

.applicationDiv .applicaticationFormBtn,
.applicationDiv a.applicaticationFormBtn {
  display: block;
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
  border: none;
  color: var(--color-white);
}

.applicationDiv .applicaticationFormBtn a,
.applicationDiv a.applicaticationFormBtn a {
  color: var(--color-white);
  margin: 0;
}

.applicationDiv a.clgName,
a.clgName,
.examCategoryInfo p a {
  color: #282828;
  white-space: normal;
}

.applicationDiv a.clgName:hover,
a.clgName:hover,
.examCategoryInfo p a:hover {
  color: #3d8ff2;
  text-decoration: underline;
}

.liveApllicationForms .applicationDiv .applicaticationFormBtn,
.liveApllicationForms .applicationDiv a.applicaticationFormBtn {
  margin-bottom: 0;
}

.liveApllicationForms .applicationDiv p.course-fees {
  margin: 0;
  color: #989898;
}

.liveApllicationForms .applicationDiv p {
  -webkit-line-clamp: 1;
  min-height: 24px;
}

.applicationDiv {
  padding: 20px;
}

/*
CLS CSS
*/
.quickLinks-cls {
  min-height: 487px;
}

.trendingArtilce-cls {
  min-height: 152px;
}

.newsSidebarSection {
  height: 540px;
}

.exam-aside {
  min-height: 100%;
}

.stateCityWidget {
  padding-bottom: 20px;
}

.stateCityWidget .trendingArtileText {
  display: block;
  font-size: 14px;
  line-height: 24px;
  border-bottom: var(--border-line);
  text-decoration: none;
  color: var(--primary-font-color);
  padding: 8px 0;
}

.stateCityWidget .trendingArtileText:hover {
  color: #3d8ff2;
  text-decoration: underline;
}

.stateCityWidget .trendingArtilerDiv.row {
  margin-left: 18px;
  border-bottom: 0;
  padding-bottom: 0;
}

.stateCityWidget .listCard:last-child .trendingArtileText {
  padding-bottom: 0;
  border-bottom: 0;
}

.stateCityWidget .row {
  display: flex;
}

.stateCityWidget .sidebarLinks {
  margin-bottom: 15px;
}

.trendingArtilerLastDiv {
  width: 100%;
}

.trendingArtilerLastDiv .trendingArtileText {
  width: 100%;
}

.trendingArtileViewAll {
  display: flex;
  justify-content: center;
  width: 100%;
  font-weight: 600 !important;
  color: #ff4e53 !important;
}

/*End CSS */
/* Get Support CLS*/

.lead-cta-exam-cls .getSupport {
  /*margin-bottom: 0;*/
}

.cta-btn-row-fix .primaryBtn {
  flex-shrink: 0;
}

/** End CSS/
/* Red icon */
.applyRedIcon {
  background-position: -166px -594px;
  width: 17px;
  height: 18px;
  vertical-align: middle;
  margin-left: 12px;
}

/*Row Margin*/
.row .row-margin-zero {
  margin-left: 0px;
  margin-right: 0px;
}

/**/
.cta-btn-row-fix {
  padding: 0;
}

/*GMU-471*/
.getSupport {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 0px;
  z-index: 5;
  /*display: flex;*/
  gap: 18px;
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
  color: #282828;
  align-items: center;
  justify-content: center;
  display: none;
}

.getSupport .getSupport__subheading {
  display: inline-block;
}

.getSupport .button__row__container {
  display: flex;
  gap: 13px;
  align-items: center;
}

.getSupport .row {
  display: none;
}

.getSupport button {
  width: 49%;
  border-radius: 2px;
  font-size: 13px;
  padding: 6px 4px;
  width: 149px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.getSupport button:last-child {
  margin-left: 0;
}

/**/
.examCollegeAcceptingDiv,
.examCourseOffereDiv {
  border: none;
}

.examCourseOffereDiv .trendingArtilerDiv.row,
.examCollegeAcceptingDiv .trendingArtilerDiv.row {
  min-height: 35px;
  margin-bottom: 10px;
  border-bottom: 1px solid #d8d8d8;
}

.tableIcon {
  vertical-align: middle;
  margin-right: 20px;
}

.degreeIcon {
  background-position: -414px -882px;
  width: 24px;
  height: 17px;
}

.clockIcon {
  background-position: -346px -878px;
  width: 24px;
  height: 24px;
  transform: scale(0.8);
}

.ageIcon {
  background-position: -690px -325px;
  width: 24px;
  height: 12px;
}

.percentIcon {
  background-position: -648px -373px;
  width: 24px;
  height: 16px;
}

.feesIcon {
  background-position: -448px -878px;
  width: 24px;
  height: 24px;
  transform: scale(0.8);
}

.employmentIcon {
  background-position: -610px -324px;
  width: 20px;
  height: 16px;
}

.opportunityIcon {
  background-position: -610px -350px;
  width: 20px;
  height: 14px;
}

.examDetailsTable table td:first-child {
  min-width: 270px;
  padding-left: 20px;
  text-align: left;
}

.examDetailsTable .table-responsive {
  margin-bottom: 0;
}

.examDetailsTable tr td:last-child {
  font-weight: 500;
}

.examDetailsTable table td:last-child {
  padding-left: 40px;
  text-align: left;
}

.examInfo.featureDetail {
  margin-bottom: 20px;
}

aside.exam-aside {
  padding-bottom: 20px;
}

.examInfoSlider.stateCityWidget .row {
  display: flex;
}

.examInfoSlider {
  position: relative;
}

.table-content-ctn {
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  margin-bottom: 40px;
}

.table-content-heading-article {
  background-color: #f5f5f5;
  padding: 10px 16px;
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
  color: #282828;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.downArrowIcon,
.upArrowIcon {
  background-position: -151px -125px;
  width: 18px;
  height: 11px;
}

.table-content-article {
  padding: 0px 0px 10px 0px;
  margin: 0 0 10px 0;
  max-height: 200px;
  overflow-y: auto;
}

.table-content-article li {
  list-style-type: none;
  position: relative;
  padding: 0 30px;
  margin-top: 10px;
}

.rotate {
  transform: rotate(180deg);
  top: 0px !important;
}

a {
  color: #3d8ff2;
  text-decoration: none;
  cursor: pointer;
}

.table-content-ctn ::-webkit-scrollbar {
  width: 8px;
}

.table-content-ctn ::-webkit-scrollbar-thumb {
  background: #d8d8d8;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 5px #d8d8d8;
}

.table-content-article {
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s ease-out, padding 0.4s ease-out;
}

.table-content-article.open {
  max-height: 200px;
  padding: 10px 0;
  overflow: auto;
}

ul.table-content-article.open li:before {
  left: 5px;
}

/* Scroll Top CSS */
.scrollToh2CSS {
  scroll-margin-top: 53px;
}

.examRelataedLinks ul {
  scrollbar-width: none;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton span.spriteIcon.whiteDownloadIcon.redDownloadIcon {
  background-position: -952px -365px !important;
}

@media (max-width: 1023px) {

  /* exam glance */
  .exam-glance-top .exam-glance-top-left .glance-days {
    background-color: var(--color-red);
    padding: 6px 35px 6px 14px;
    font-weight: 500;
    position: relative;
    color: #fff;
    font-size: 14px;
    white-space: nowrap;
  }

  .glance-name {
    position: relative;
    font-size: 14px;
  }

  .primaryBtn,
  .examSecondray {
    font-size: 12px !important;
    padding: 7px 0 !important
  }

  .phoneIcon {
    margin-right: 2px !important
  }

  .exam-glance-steps {
    overflow: visible !important;
    padding: 20px 42px 65px !important;
  }

  .exam-glance-steps>ul>li .step-name {
    font-size: 12px;
  }

  .exam-glance-wrap {
    padding: 5px !important;
  }

  .exam-glance-steps>ul>li:nth-child(1) .step-name {
    bottom: 10px;
    left: 5px;
  }

  .exam-glance-steps>ul>li:nth-child(3) .step-name,
  .exam-glance-steps>ul>li:nth-child(5) .step-name {
    bottom: 10px;
  }

  .exam-glance-steps>ul>li:after {
    width: 85% !important;
    left: 58% !important;
  }

  .coming-soon {
    font-size: 7px;
    height: 14px;
    width: 55px;
  }

  .exam-glance-steps>ul>li>.step-circle {
    font-size: 9px;
    width: 17px;
    height: 17px;
  }

  .coming-step .step-circle .tickicon {
    height: 8px;
    width: 4px;
  }

  /* exam glance ends */

  .examHeroSection button.primaryBtn {
    max-width: 100%;
    flex: 1;
  }

  .examHeroSection .ctaColumn .ctaRow button {
    white-space: nowrap;
    padding: 5px 7px;
  }

  /*  CLS CSS */
  .exam-aside {
    min-height: 1090px;
  }

  /* mobile cta*/
  .ctaRow .primaryBtn:only-child {
    width: 100%;
  }

  .quickLinks-cls {
    min-height: 968px;
  }

  .trendingArtilce-cls {
    min-height: 132px;
  }

  .newsSidebarSection {
    height: 540px;
  }

  /*End CSS */

  .second-row-content:has(.helpfulItem) {
    height: 105px;
  }

  .second-row-content {
    display: block;
  }

  .cta-mobile {
    margin-top: 10px;
    padding-left: 0;
    padding-right: 0;
  }

  .examRelataedLinks ul,
  .pageRedirectionLinks ul {
    overflow-y: hidden;
  }

  iframe {
    width: 300px;
    height: 150px;
  }

  .commonHeroSection .helpfulInfo .helpfulItem:nth-child(2) .spriteIcon {
    margin-left: 1px;
  }

  .commonHeroSection .heroHeader .headingContainer h1 {
    -webkit-line-clamp: 3;
  }

  .fixedExamRelatedDiv {
    border: none;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  }

  .quickLinks {
    margin-bottom: 20px;
  }

  .quickLinks ul {
    max-height: 100%;
    overflow: initial;
  }

  .row {
    margin: 0 -10px;
  }

  .container {
    padding: 0 10px;
  }

  .aboutExam,
  .examUsefulLinks,
  .liveApllicationForms,
  .clgWithCourse,
  .discussionForumSection,
  .examInfo {
    padding: 10px;
  }

  .aboutExam h2,
  .examUsefulLinks h2,
  .liveApllicationForms h2,
  .clgWithCourse h2,
  .discussionForumSection h2,
  .examInfo h2 {
    padding: 8px 10px;
    font-size: 15px;
    margin-bottom: 10px;
    text-transform: uppercase;
  }

  .aboutExam h3,
  .examUsefulLinks h3,
  .liveApllicationForms h3,
  .clgWithCourse h3,
  .discussionForumSection h3,
  .examInfo h3 {
    font-size: 15px;
  }

  .liveApllicationFormsInner .row,
  .clgWithCourseInner .row {
    overflow: auto;
    white-space: nowrap;
    display: block;
  }

  .liveApllicationFormsInner .row .applicationDiv,
  .liveApllicationFormsInner .row .clgWithCourseDiv,
  .clgWithCourseInner .row .applicationDiv,
  .clgWithCourseInner .row .clgWithCourseDiv {
    margin-right: 10px;
    margin-bottom: 0;
    display: inline-block !important;
    width: 86%;
    max-width: 86%;
    white-space: normal;
    vertical-align: middle;
    overflow: auto;
  }

  .liveApllicationFormsInner .clgWithCourseDiv .viewAllDiv,
  .clgWithCourseInner .clgWithCourseDiv .viewAllDiv {
    min-height: 350px;
  }

  .clgWithCourseInner .row .clgWithCourseDiv:nth-of-type(5n + 0) {
    margin-right: 10px;
  }

  .examRelataedLinks {
    margin: 0 -10px;
    margin-bottom: 10px;
    border-radius: 0;
    border-left: 0;
    border-right: 0;
  }

  .examRelataedLinks ul li {
    padding: 0 8px;
    line-height: 37px;
  }

  .examRelataedLinks ul li a.activeLink {
    font-weight: var(--font-semibold);
  }

  .clgWithCourse h2.row {
    margin-bottom: 10px;
  }

  .clgWithCourse h2 a {
    display: none;
  }

  .clgWithCourse .row {
    overflow: auto;
    white-space: nowrap;
    margin: 0;
    display: block;
  }

  .clgWithCourse .clgWithCourseDiv {
    margin-right: 10px;
    margin-bottom: 0;
    display: inline-block;
    white-space: normal;
    vertical-align: middle;
    padding: 10px;
    overflow: auto;
  }

  
  .updated-info.row {
    border-radius: 4px;
    background: var(--color-white);
    margin-bottom: 10px;
  }

  .updated-info.row img {
    float: left;
  }

  .updated-info.row .authorAndDate .authorName {
    font-size: 12px;
  }

  .updated-info.row .authorAndDate p {
    font-size: 12px;
  }

  .updated-info.row .authorAndDate .verifiedBlueTickIcon {
    transform: scale(0.6);
    margin-left: 0;
    vertical-align: middle;
  }

  .pageMask {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: none;
    overflow-y: hidden;
  }

  .leadFormContainer {
    top: 50px;
    height: auto;
    width: 95%;
    left: 10px;
    overflow: hidden;
    border-radius: 4px;
    display: none;
  }

  .leadFormContainer .closeLeadFormContainer {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 8;
  }

  .leadFormContainer .leadFormDiv {
    margin-top: 0;
  }

  .leadFormContainer {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .examHeroSection {
    margin-top: -157px;
    padding: 20px;
    margin-bottom: 10px;
  }

  .examHeroSection h1 {
    font-size: 18px;
    line-height: 28px;
    padding-bottom: 10px;
  }

  .examHeroSection .examDates .col-md-4 {
    padding-bottom: 10px;
  }

  .examHeroSection .examDates .col-md-4:last-child {
    padding-bottom: 0;
  }

  .commonHeroSection {
    padding: 10px;
    margin-top: 0;
    margin: 0 -10px 10px;
    background: #fff;
  }

  .commonHeroSection .heroHeader .imgContainer {
    max-width: 56px;
    max-height: 56px;
    flex-shrink: 0;
  }

  .commonHeroSection .heroHeader .headingContainer h1 {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.5;
  }

  .commonHeroSection .helpfulInfo {
    margin-top: 10px;
  }

  .commonHeroSection .helpfulInfo .examDateItem {
    flex-grow: 1;
    text-align: center;
  }

  .commonHeroSection .ctaColumn .ctaRow {
    gap: 10px;
  }

  .commonHeroSection .ctaColumn .ctaRow .brochureButton,
  .commonHeroSection .ctaColumn .ctaRow .applyNowButton {
    max-width: unset;
    flex: 1;
  }

  .headerLogo {
    transform: scale(0.8);
  }

  .page-header,
  .topHeader {
    height: 46px;
  }

  .topHeader {
    padding: 2px 20px;
  }

  .examRelataedLinks,
  .examRelataedLinks .btn_left,
  .examRelataedLinks .btn_right {
    height: 40px;
  }

  .examRelataedLinks ul li a {
    padding: 5px 0;
    font-weight: 400;
  }

  .right_angle,
  .left_angle {
    margin: 8px 0;
  }

  .examInfo .latestUpdates {
    margin: 0;
  }

  .commonHeroSection .ctaColumn .ctaRow .applyNowButton,
  .commonHeroSection .ctaColumn .ctaRow .brochureButton {
    font-weight: 500;
  }

  .breadcrumbDiv {
    background: unset;
  }

  .breadcrumbDiv ul li {
    color: #282828;
  }

  .breadcrumbDiv ul li a {
    color: #282828;
  }

  .breadcrumbDiv ul li a::after {
    background-position: 707px -150px !important;
  }

  .commonHeroSection .helpfulInfo .helpfulItem {
    width: 100%;
  }

  .commonHeroSection .ctaColumn .ctaRow .applyNowButton,
  .commonHeroSection .ctaColumn .ctaRow .brochureButton {
    font-size: 14px;
  }

  .pageData p,
  .pageData li,
  .pageData a,
  .examInfo p,
  .examInfo li,
  .examInfo a {
    font-size: 14px;
  }

  .examRelataedLinks .btn_left,
  .examRelataedLinks .btn_right {
    height: 38px;
  }

  .examInfoSlider h2.row,
  .clgWithCourse h2.row,
  .liveApllicationForms h2.row {
    text-transform: none;
    background: 0 0;
    padding: 0;
  }

  .latestInfoSection h2,
  .otherEntranceExams h2 {
    background: 0 0;
    padding: 0;
  }

  .liveApllicationFormsInner .row .applicationDiv,
  .liveApllicationFormsInner .row .clgWithCourseDiv,
  .clgWithCourseInner .row .applicationDiv,
  .clgWithCourseInner .row .clgWithCourseDiv {
    max-width: 68%;
    width: 68%;
  }

  .trendingArtilce {
    margin-bottom: 10px;
  }

  div.articleSidebarSection,
  div.newsSidebarSection {
    margin-bottom: 10px;
  }

  .subNavDropDown:has(.activeLink:hover):hover .caret {
    background-position: -382px -1057px;
  }

  .quickLinks h2 {
    font-size: 15px;
    text-transform: none;
  }

  .liveApllicationForms,
  .clgWithCourse,
  .examInfoSlider,
  .latestInfoSection,
  .latestInfoSection {
    margin-bottom: 10px;
  }

  table {
    border: 1px solid #eaeaea !important;
  }

  table td:first-child {
    border-right: 1px solid #eaeaea;
  }

  table tr td:last-child {
    border-right: 1px solid #eaeaea;
  }

  table td {
    border-right: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
  }

  .examRelataedLinks ul li span {
    padding: 6.5px 0;
  }

  .commonHeroSection .helpfulInfo .helpfulItem .calenderIcon:last-of-type {
    margin-left: 0;
  }

  .commonHeroSection .helpfulInfo .helpfulItem .calenderIcon:last-of-type::before {
    content: "\A";
    white-space: pre;
  }

  .pageData .latestUpdates {
    margin-bottom: 0;
  }

  .pageData h2,
  .examInfo h2 {
    background: 0 0;
    padding: 0;
    text-transform: none;
  }

  .pageData h4 {
    color: #333;
  }

  .table-responsive {
    margin-bottom: 10px;
  }

  .mobileSubNavDropDownMenu {
    background: rgba(0, 0, 0, 0.119215);
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
    display: none;
  }

  .mobileSubNavDropDownDiv {
    position: fixed;
    height: auto;
    bottom: 0;
    left: 0;
    overflow: auto;
    border-radius: 4px 4px 0 0;
    width: 100%;
    background: #fff;
    z-index: 3;
  }

  .mobileSubNavDropDownDiv ul {
    margin: 0;
    padding: 0 20px;
  }

  .mobileSubNavDropDownDiv ul li {
    font-size: 14px;
    line-height: 24px;
    border-bottom: 1px solid #d8d8d8;
    display: flex;
    color: #787878;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }

  .mobileSubNavDropDownDiv ul li a {
    background: 0 0;
    border: none;
    color: #787878;
  }

  .mobileSubNavDropDownMenu.current {
    display: block;
  }

  .examRelataedLinks .mobileSubNavDropDownDiv ul {
    height: unset;
  }

  .mobileSubNavDropDownDiv ul li a {
    padding: 0;
  }

  .subNavDropDown:hover .caret {
    background-position: -438px -1089px;
    -webkit-transform: unset;
    transform: unset;
  }

  .mobileSubNavDropDown .caret {
    background-position: -382px -1057px;
    width: 25px;
    height: 37px;
    margin-left: 2px;
    margin-right: -10px;
    position: relative;
    bottom: 3px;
    transform: none !important;
  }

  .subNavDropDown:has(.activeLink:hover):hover .caret {
    background-position: -382px -1057px;
  }

  .blueBgDiv {
    display: none !important;
  }

  .stateCityWidget {
    padding-bottom: 10px;
  }

  .stateCityWidget .trendingArtilerDiv.row {
    width: 100%;
    margin-left: 0px;
    border-bottom: 0;
    padding-bottom: 0;
  }

  .examInfoSlider.stateCityWidget .row {
    gap: 20px !important;
  }

  .examInfoSlider.stateCityWidget .row .col-md-6 {
    width: 100%;
    max-width: 100%;
  }

  .examInfoSlider.stateCityWidget .row .col-md-6 h2 {
    padding-left: 10px;
  }

  .examInfoSlider .trendingArtileText {
    width: 100%;
    flex-basis: 100% !important;
  }

  .getSupport .getSupport__subheading {
    display: none;
  }

  .getSupport .button__row__container {
    width: 100%;
  }

  .getSupport button {
    flex-grow: 1;
  }

  aside.exam-aside {
    padding-bottom: 0px;
  }
 
}

/*# sourceMappingURL=exam_detail.css.map */


/*------------exam_glance------------*/
.exam-glance-wrap {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px;
}

.exam_glance-inner {
  border-radius: 4px;
  border: var(--border-line);
  background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 1), rgba(233, 242, 255, 1));

}

.exam-glance-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: #f5f5f5;
  padding: 20px 0;
}

.exam-glance-top .exam-glance-top-left {
  display: flex;
  align-items: center;
  font-size: 18px;
  gap: 5px;
}

.exam-glance-top .exam-glance-top-left .glance-days {
  background-color: var(--color-red);
  padding: 6px 35px 6px 14px;
  font-weight: 500;
  position: relative;
  color: #fff;
}

.exam-glance-top .exam-glance-top-left .glance-days:after {
  content: '';
  position: absolute;
  right: -35px;
  top: 0;
  width: 50px;
  height: 100%;
  background: #f5f5f5;
  border-radius: 50%;
}

.glance-name {
  position: relative;
}

.exam-glance-steps {
  max-width: 700px;
  margin: 0 auto;
  padding: 20px 30px 65px;
  overflow: hidden;
}

.exam-glance-steps>ul {
  display: flex;
  justify-content: space-between;
  padding: 0;
  margin: 0 -40px;
  position: relative;
}

.exam-glance-steps>ul>li {
  position: relative;
  list-style: none;
  flex: 1;
  display: inline-flex;
  justify-content: center;
}

.exam-glance-steps>ul>li:after {
  content: '';
  width: 87%;
  position: absolute;
  height: 1px;
  border-bottom: 1px solid rgba(196, 211, 230, 1);
  top: 9px;
  left: 57%;
}

.exam-glance-steps>ul>li.coming-step::after {
  border-bottom: 1px solid rgba(38, 163, 115, 1);
}

.exam-glance-steps>ul>li.coming-step .step-name {
  color: rgba(28, 24, 24, 1);
}

.exam-glance-steps>ul>li:last-child::after {
  display: none;
}

.exam-glance-steps>ul>li>.step-circle {
  width: 20px;
  height: 20px;
  border: 1px solid rgba(211, 230, 255, 1);
  background-color: rgba(227, 239, 255, 1);
  display: flex;
  position: relative;
  z-index: 1;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  font-size: 12px;
  color: rgba(9, 102, 194, 1);
}

.exam-glance-steps>ul>li .step-name {
  position: absolute;
  bottom: -50px;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;
  z-index: 1;
  height: 40px;
  line-height: 1.3;
  color: rgba(177, 173, 173, 1);
  font-size: 14px;
  font-weight: 400;
}

.exam-glance-steps>ul>li .coming-soon {
  display: none;
}

.glance-options-container {
  border-top: 1px dashed rgba(219, 217, 217, 1);
  margin: 0 15px;
  padding: 20px 0;
}

.glance-options-inner {
  max-width: 325px;
  margin: 0 auto;
  padding: 0;
  height: 120px;
  overflow: hidden;
  overflow-y: scroll;
}

.glance-options-inner>ul {
  padding: 0;
  list-style: none;
  margin: 0;
}

.glance-options-inner ul>li {
  display: flex;
  justify-content: center;
  align-items: center;
  list-style: none;
  margin: 0;
  gap: 10px;
  height: 38px;
  color: rgba(184, 180, 180, 1);
  cursor: pointer;
  font-size: 12px;
  line-height: 18px;
  font-weight: 400;
}

.glance-options-inner::-webkit-scrollbar {
  width: 0;
}

.glance-options-inner::-webkit-scrollbar-track {
  display: none;
}

.glance-options-inner::-webkit-scrollbar-thumb {
  display: none;
}

.glance-options-inner ul>li.active {
  border-top: 1px dashed rgba(9, 102, 194, 1);
  border-bottom: 1px dashed rgba(9, 102, 194, 1);
  color: rgba(40, 40, 40, 1);
  cursor: default;
  padding: 10px 0;
  background: rgb(238, 174, 202);
  background: linear-gradient(90deg, rgba(241, 247, 255, 1) 0%, rgba(227, 239, 255, 1) 33%, rgba(227, 239, 255, 1) 62%, rgba(240, 246, 255, 1) 100%);
  font-weight: bold;
}

.examSecondray {
  display: inline-block;
  background: var(--topheader-bg);
  color: var(--color-white);
  padding: 8px 15px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  -webkit-transition: 0.2s ease;
  transition: 0.2s ease;
  outline: none;
  line-height: 22px;
  font-weight: 500;
}

.primaryBtn,
.examSecondray {
  width: 180px;
  line-height: 22px !important;
  font-weight: 500 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px
}

.examSecondray,
.examSecondrayPrimary {
  width: 260px;
  white-space: nowrap;
}

.glance-options-button {
  border-top: 1px dashed rgba(219, 217, 217, 1);
  display: flex;
  justify-content: center;
  gap: 10px;
  padding-top: 20px;
}

.tickicon {
  display: none;
  position: relative;
}

.coming-step .step-circle {
  border: 1px solid rgba(38, 163, 115, 1) !important;
  color: rgba(38, 163, 115, 1) !important;
  background-color: transparent !important;
}

.coming-step .step-circle .number {
  display: none;
}

.coming-step .step-circle .tickicon {
  display: inline-block;
  transform: rotate(45deg);
  height: 10px;
  width: 6px;
  border-bottom: 1px solid rgba(38, 163, 115, 1);
  border-right: 1px solid rgba(38, 163, 115, 1);
  margin-top: -3px;
}

.coming-soon {
  position: relative;
  background-color: rgba(255, 239, 240, 1);
  border: 1px solid rgba(207, 69, 77, 1);
  color: rgba(207, 69, 77, 1);
  font-size: 12px;
  width: 88px;
  border-radius: 4px;
  z-index: 1;
  font-weight: 500;
  height: 20px;
  line-height: 1.3;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.exam-glance-steps>ul>li.coming-soon-text .step-name {
  color: rgba(28, 24, 24, 1);
  font-weight: 600;
}

.spriteIconTemporary {
  display: inline-block !important;
  background: url("../../images/master_sprite.webp");
}

.phoneIcon {
  background-position: -685px -1144px;
  width: 22px;
  height: 17px;
  margin-right: -2px;
}

/******************/
.article-links {
  min-height: auto;
  margin-bottom: 25px;
  position: static !important;
}


@media (max-width: 480px) {
  .exam-glance-steps>ul>li:after {
    width: 77% !important;
    left: 61% !important;
  }
}