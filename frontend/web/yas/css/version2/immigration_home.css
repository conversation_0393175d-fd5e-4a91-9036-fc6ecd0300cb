.gis__immigrationContainer {
  background-color: #fff;
}

/*****************************************************************/
/********************* Banner Section ****************************/
/*****************************************************************/

.gisSpriteIcon {
  display: inline-block;
  background: url("../../images/master-sprite-gis.webp");
  text-align: left;
  overflow: hidden;
}

.canadaEligibilityIcon {
  background-position: -430px -607px;
  width: 72px;
  height: 72px;
}

.australiaEligibilityIcon {
  background-position: -336px -606px;
  width: 72px;
  height: 72px;
}

.canadaFlag {
  background-position: -580px -230px;
  width: 92px;
  height: 59px;
}

.australiaFlag {
  background-position: -457px -230px;
  width: 87px;
  height: 55px;
}

.austriaFlag {
  background-position: -339px -226px;
  width: 96px;
  height: 59px;
}

.germanyFlag {
  background-position: -220px -630px;
  width: 92px;
  height: 59px;
}

.swedenFlag {
  background-position: -220px -547px;
  width: 92px;
  height: 59px;
}

.uaeFlag {
  background-position: -223px -224px;
  width: 92px;
  height: 59px;
}

.container-fluid {
  padding: 0;
}

.gis__immigrationBanner {
  height: 600px;
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.6) 100%,
      rgba(196, 196, 196, 0.4) 0%,
      rgba(196, 196, 196, 0.4) 100%),
    url(../../images/gis_banner.webp);
  background-size: 100% 200%;
  background-repeat: no-repeat;
}

.gis__immigrationBanner .container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
}

.gis__immigrationBanner__title {
  max-width: 800px;
  font-size: 55.36px;
  font-weight: 300;
  line-height: 66.44px;
  text-align: left;
  color: #fff;
}

.gis__immigrationBanner__subTitle {
  margin: 20px 0;
  font-size: 25px;
  font-weight: 400;
  line-height: 33px;
  letter-spacing: -0.02em;
  text-align: left;
  color: #fff;
}

.gis__immigrationBanner__subTitleEnd {
  padding: 6px 20px;
  height: 40px;
  font-size: 20.09px;
  font-weight: 400;
  line-height: 25.83px;
  text-align: left;
  color: #fff;
  opacity: 0;
  transform: translateX(100%);
  transition: transform 0.5s ease, opacity 0.5s ease;
  z-index: 1;
}

.gis__immigrationBanner__subTitleEnd_class {
  overflow: hidden;
  background: #0966c2;
  width: 500px;
  text-align: left;
}

.gis__immigrationBanner__getBtn {
  margin-top: 40px;
  width: 136px;
  height: 41px;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 700;
  line-height: 27px;
  text-align: center;
  outline: none;
  border: none;
  color: #fff;
  background: #ff4e53;
}

.slide-in {
  opacity: 1;
  transform: translateX(0);
  animation: slide-in 0.5s forwards;
}

.slide-out {
  opacity: 0;
  transform: translateX(-100%);
  animation: slide-out 0.5s forwards;
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-out {
  from {
    opacity: 1;
    transform: translateX(0);
  }

  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

/*****************************************************************/
/********************* One Step Section **************************/
/*****************************************************************/

.gis__immigrationStep {
  height: 246px;
  background: url(../../images/gis_oneStepBg.webp),
    linear-gradient(to bottom, #0966c22b, #0966c22b);
}

.gis__immigrationStep .container {
  height: 100%;
}

.gis__immigrationStep__gridView {
  height: 100%;
  width: 100%;
  display: flex;
  gap: 30px;
  align-items: center;
  justify-content: space-between;
  color: #505050;
}

.gis__stepCard:first-child h1 {
  width: 237px;
  height: 144px;
  font-size: 30px;
  font-weight: 900;
  line-height: 36px;
  text-align: left;
}

.gis__stepCard:not(:first-child) {
  width: 195px;
  height: 177px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-repeat: no-repeat;
  background-image: url(../../images/gis_oneStepCardBg.webp);
  background-size: contain;
}

.gis__stepCard:not(:first-child) h1 {
  width: 125px;
  font-size: 20px;
  font-weight: 800;
  line-height: 25.42px;
  text-align: center;
  color: #505050;
}

/*****************************************************************/
/********************* Services Section **************************/
/*****************************************************************/

.rightArrowIcon {
  background-position: -537px -554px;
  width: 14px;
  height: 14px;
}

.gis__immigrationService {
  padding: 80px 0;
}

.gis__immigrationTitle {
  font-size: 30px;
  font-weight: 700;
  line-height: 38px;
  letter-spacing: 0.3px;
  text-align: center;
  color: #414141;
}

.gis__immigrationSubTitle {
  margin-top: 10px;
  font-size: 20px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
  color: #414141;
}

.gis__immigrationContentWrap {
  margin-top: 40px;
}

.gis__immigrationService__gridView {
  display: grid;
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  justify-content: center;
}

.gis__immigrationService__gridView a {
  text-decoration: none;
}

.gis__immigrationService__gridItems {
  flex-basis: 33%;
  max-width: 364px;
  height: 402px;
  border-radius: 28px;
  background-color: #fff;
  box-shadow: 0px 0px 9px 0px #0000001a;
  overflow: hidden;
}

.gis__immigrationService__img {
  width: 100%;
  height: 230px;
  background-color: grey;
  overflow: hidden;
}

.gis__immigrationService__img img {
  width: 100%;
  height: 100%;
}

.gis__immigrationService__body {
  padding: 20px;
  position: relative;
}

.gis__immigrationService__body h4 {
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  text-align: left;
  color: #414141;
}

.gis__immigrationService__body p {
  margin-top: 5px;
  position: relative;
  font-size: 15px;
  font-weight: 400;
  color: #989898;
  line-height: 24px;
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: normal;
}

.gis__immigrationService__body p::after {
  /* content: "\00a0..."; */
  position: absolute;
  bottom: 0;
  right: 0;
  width: 200px;
  background-color: white;
}

.gis__immigrationService__learnBtn {
  position: absolute;
  bottom: 18px;
  right: 38px;
  font-size: 15px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ff4e53;
  text-decoration: none;
}

.gis__immigrationService__learnBtn:hover {
  color: #ff4e53;
  text-decoration: none;
}

/*****************************************************************/
/********************* Fly Abroad Section ************************/
/*****************************************************************/

.gis__immigrationAbroad {
  padding: 80px 0;
  background-color: #0966c20a;
}

.gis__immigrationService__flyGridItem {
  flex-basis: 33%;
  max-width: 364px;
  height: 192px;
  padding: 30px;
  display: flex;
  border-radius: 28px;
  justify-content: center;
  align-items: start;
  flex-direction: column;
  overflow: hidden;
  background-size: 100% 100%;
}

.gis__immigrationService__flyGridBody__title {
  color: #fff;
  line-height: 38px;
  letter-spacing: 0.3px;
  text-align: left;
  margin-top: 10px;
}

.gis__immigrationService__flyGridBody__title b {
  font-size: 24px;
  font-weight: 700;
}

.gis__immigrationService__flyGridBody__title span {
  font-size: 16px;
  font-weight: 400;
}

/*****************************************************************/
/****************** Fly Abroad Country Section *******************/
/*****************************************************************/

.gis__immigrationAbroadCountry {
  background-image: linear-gradient(to left, #0966c2, #0966c2);
}

.gis__immigrationAbroadCountry .container {
  padding: 80px 0;
  background-image: url("../../images/gis_flyBg.png");
  filter: brightness(500%);
  background-size: 100% 100%;
}

.gis__immigrationCountry__flyView {
  gap: 27px;
  display: flex;
  align-items: end;
  margin-top: 40px;
  width: 100%;
  background-color: transparent;
  overflow-x: auto;
  overflow-y: hidden;
}

.gis__immigrationCountry__flyView .scrollLeft {
  top: calc(50% - -38px);
  left: -80px;
}

.gis__immigrationCountry__flyView .scrollRight {
  top: calc(50% - -13px);
  right: -80px;
}

.gis__immigrationCountry__flyView .scrollRight,
.gis__immigrationCountry__flyView .scrollLeft {
  background-position: -655px -537px;
  width: 50px;
  height: 50px;
}

.gis__immigrationCountry__flyView::-webkit-scrollbar {
  appearance: none;
}

.gis__immigrationCountry__flyViewItem {
  flex-shrink: 0;
}

.gis__immigrationCountry__flyViewItem img {
  flex-shrink: 0;
  border: 1.55px solid #efefef;
  border-radius: 14px;
}

.gis__immigrationAbroadCountry h2,
.gis__immigrationAbroadCountry p {
  color: #fff;
}

.gis__immigrationCountry__flyViewItem p {
  font-size: 19px;
  font-weight: 700;
  line-height: 24px;
  text-align: center;
}

/*****************************************************************/
/********************** Testimonial Section **********************/
/*****************************************************************/

.gis__testimonialsCtn .fullStar {
  background-position: -537px -637px;
  width: 15px;
  height: 15px;
}

.gis__testimonialsCtn .halfStar {
  background-position: -576px -638px;
  width: 15px;
  height: 15px;
}

.gis__testimonialsCtn .emptyStar {
  background-position: -618px -637px;
  width: 15px;
  height: 15px;
}

.gis__testimonialsCtn .closeIcon {
  position: absolute;
  display: none;
  top: 13px;
  right: 10px;
  width: 24px;
  height: 24px;
  background-position: -11px -10px;
  cursor: pointer;
  transform: scale(0.9);
}

.gis__testimonialsCtn .doubleQuoteIcon {
  background-position: -414px -547px;
  width: 26px;
  height: 20px;
}

.gis__testimonialsCtn .canadaFlag {
  background-position: -487px -783px;
  width: 28px;
  height: 14px;
}

.gis__testimonialsCtn .australiaFlag {
  background-position: -391px -783px;
  width: 28px;
  height: 14px;
}

.gis__testimonialsCtn .swedenFlag {
  background-position: -439px -783px;
  width: 28px;
  height: 14px;
}

.gis__testimonialsCtn .austriaFlag {
  background-position: -343px -783px;
  width: 28px;
  height: 14px;
}

.gis__testimonialsCtn .germanyFlag {
  background-position: -295px -783px;
  width: 28px;
  height: 14px;
}

.gis__testimonialsCtn .uaeFlag {
  background-position: -535px -783px;
  width: 29px;
  height: 15px;
}

.gis__testimonialTabBtn.selected .closeIcon {
  display: inline-block;
}

.gis__testimonialsCtn {
  padding: 80px 0;
}

.gis__testimonialTabList {
  margin-top: 44px;
  padding: 0;
  display: flex;
  gap: 14px;
  align-items: center;
  justify-content: center;
  list-style-type: none;
  width: 100%;
  flex-wrap: wrap;
}

.gis__testimonialTabBtn {
  position: relative;
  width: 230px;
  height: 48px;
  display: flex;
  outline: none;
  align-items: center;
  gap: 5px;
  font-size: 18px;
  font-weight: 500;
  line-height: 21.78px;
  text-align: center;
  border-radius: 100px;
  justify-content: center;
  background-color: #fff;
  border: 1px solid #d1cfcf;
}

.gis__testimonialTabBtn.selected {
  border: 2px solid #0c3da2;
}

.gis__testimonialContentBox {
  margin-top: 43px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(357px, 1fr));
  grid-gap: 37px;
}

.gis__testimonialCard {
  max-width: 357px;
  height: 331px;
  border-radius: 4px;
  border: 1px solid #e4e4e4;
}

.gis__testimonialCardTop {
  position: relative;
  padding: 16px 20px;
  height: 147px;
  border-radius: 4px;
  background: #f5f5f5;
  border-bottom: 1px solid #e4e4e4;
  z-index: 1;
}

.gis__testimonialCardBadge {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.gis__testimonialCardBadge::before {
  content: "";
  position: absolute;
  left: 0;
  top: 11px;
  background-image: url(../../images/gis_testimonialBadge.svg);
  width: 161px;
  height: 28px;
  background-repeat: no-repeat;
  z-index: -1;
}

.gis__testimonialCardBadge p:first-child {
  font-size: 16px;
  font-weight: 600;
  line-height: 21.89px;
  letter-spacing: 0.27px;
  text-align: left;
  color: #fff;
}

.gis__testimonialCardBadge p:last-child {
  display: flex;
  gap: 4px;
  align-items: center;
}

.gis__testimonialCardHeader {
  margin-top: 15px;
  display: flex;
  gap: 25px;
  align-items: center;
}

.gis__testimonialCardHeaderImg {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 1px solid #ff615d;
}

.gis__testimonialCardHeaderImg img {
  width: 80px;
  height: 78px;
  border-radius: 50%;
}

.gis__testimonialCardDetail h2 {
  font-size: 18px;
  font-weight: 600;
  line-height: 21.89px;
  letter-spacing: 0.27px;
  text-align: left;
}

.gis__testimonialCardDetail p {
  font-size: 14px;
  font-weight: 400;
  line-height: 21.89px;
  letter-spacing: 0.27px;
  text-align: left;
}

.gis__testimonialCardBottom {
  padding: 20px;
}

.gis__testimonialCardBottom p {
  padding-right: 5px;
  max-height: 143px;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.27px;
  text-align: left;
  overflow-y: auto;
}

.gis__testimonialCardBottom p::-webkit-scrollbar {
  width: 6px;
}

.gis__testimonialCardBottom p::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.gis__testimonialCardBottom p::-webkit-scrollbar-thumb {
  background: #c6c6c8;
  cursor: pointer;
}

.gis__testimonialCardBottom p::-webkit-scrollbar-thumb:hover {
  background: #555555a1;
}

.gis__testimonialBtnBox {
  margin-top: 40px;
  display: flex;
  justify-content: center;
}

.gis__testimonialBtnBox .button {
  width: 172px;
  height: 48px;
  outline: none;
  font-size: 16px;
  font-weight: 400;
  line-height: 44.4px;
  text-align: center;
  color: #ff615d;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #ff615d;
  text-decoration: none;
}

/*****************************************************************/
/************************* Journey Section ***********************/
/*****************************************************************/
.profileIcon {
  background-position: -441px -731px;
  width: 24px;
  height: 28px;
}

.languageIcon {
  background-position: -680px -728px;
  width: 25px;
  height: 25px;
}

.educationIcon {
  background-position: -539px -470px;
  width: 28px;
  height: 28px;
}

.prIcon {
  background-position: -308px -721px;
  width: 28px;
  height: 28px;
}

.invitationIcon {
  background-position: -372px -725px;
  width: 29px;
  height: 28px;
}

.gis__journeyCtn {
  padding: 80px 0;
  background: #0966c217;
}

.gis__immigrationService__gridJourneyItem {
  flex-basis: 33%;
  max-width: 364px;
}

.gis__immigrationService__gridJourneyItem:first-child {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: start;
}

.gis__immigrationService__gridJourneyItem:first-child h2 {
  font-size: 30px;
  font-weight: 700;
  line-height: 38px;
  letter-spacing: 0.30000001192092896px;
  text-align: left;
  color: #414141;
}

.gis__immigrationService__gridJourneyItem:first-child p {
  margin-top: 10px;
  font-size: 19px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  color: #414141;
}

.gis__immigrationService__gridJourneyItem:not(:first-child) {
  width: 100%;
  height: 234px;
  padding: 20px;
  display: flex;
  justify-content: start;
  flex-direction: column;
  align-items: start;
  border-radius: 16px;
  background-color: #fff;
  box-shadow: 0px 0px 5.28px 0px #0000000f;
}

.gis__immigrationService__gridJourneyItem__icon {
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #0966c217;
}

.gis__immigrationService__gridJourneyItem h2 {
  margin-top: 16px;
  font-size: 20px;
  font-weight: 700;
  line-height: 14.09px;
  letter-spacing: -0.02em;
  text-align: left;
  color: #000;
}

.gis__immigrationService__gridJourneyItem p {
  font-size: 15px;
  font-weight: 400;
  line-height: 19.1px;
  text-align: left;
  margin-top: 10px;
  color: #989898;
}

/*****************************************************************/
/******************** Job Assistance Section *********************/
/*****************************************************************/

.gis__jobCtn {
  padding: 80px 0;
}

.gis__jobCtn img {
  margin-top: 40px;
}

/*****************************************************************/
/*********************** Fly Abroad 2 Section ********************/
/*****************************************************************/

.customSliderList,
.customSliderCards {
  display: flex;
  gap: 30px;
  white-space: nowrap;
  overflow: auto;
}

.customSliderList::-webkit-scrollbar,
.customSliderCards::-webkit-scrollbar {
  appearance: none;
  width: 0;
  height: 0;
}

.gis__abroadFlexBox .scrollLeft {
  top: calc(50% - -10px);
  left: -50px;
}

.gis__abroadFlexBox .scrollRight {
  top: calc(50% - 22px);
  right: -50px;
}

.gis__abroadFlexBox .scrollRight,
.gis__abroadFlexBox .scrollLeft {
  background-position: -421px -473px;
  width: 22px;
  height: 38px;
}

.videoPlayIcon {
  background-position: -322px -393px;
  width: 60px;
  height: 60px;
}

.gis__abroadCtn {
  padding: 80px 0;
  background: #0966c20d;
}

.gis__abroadFlexBox {
  position: relative;
  margin-top: 40px;
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

.gis__abroadFlexItem {
  flex-shrink: 0;
  width: 276px;
  height: 271px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #eaeaea;
  overflow: hidden;
}

.gis__abroadFlexItem__img {
  position: relative;
  width: 100%;
  height: 207px;
}

.gis__abroadFlexItem__img button {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 60px;
  width: 60px;
  outline: none;
  border: none;
  border-radius: 50%;
  background-color: transparent;
  transform: translate(-50%, -50%);
}

.gis__abroadFlexItem__img img {
  width: 100%;
  height: 100%;
}

.gis__abroadFlexItem__body {
  padding: 20px;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.3px;
  text-align: left;
}

/*****************************************************************/
/******************* Immigration News Section ********************/
/*****************************************************************/

.gis__immigrationNewsCtn {
  padding: 80px 0;
  background-image: url(../../images/gis_newsBg.webp);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 0% 100%;
}

.gis__immigrationNewsCtn .gis__immigrationService__gridView {
  margin-top: 40px;
}

.gis__immigrationService__newsItem {
  flex-basis: 33%;
  max-width: 364px;
  height: 374px;
  border-radius: 28px;
  border: 1px solid #efefef;
  box-shadow: 0px 0px 9px 0px #0000001a;
  overflow: hidden;
}

.gis__newsItem__img {
  width: 100%;
  height: 188px;
  background-color: #989898;
  overflow: hidden;
}

.gis__newsItem__body {
  padding: 20px;
}

.gis__newsItem__body h3,
.gis__immigrationTipsItem__body h3 {
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  text-align: left;
  color: #414141;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 10px;
}

.gis__newsItem__body h4,
.gis__immigrationTipsItem__body h4,
.gis__newsItem__body p,
.gis__immigrationTipsItem__body p {
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  color: #989898;
}

.gis__newsItem__body p {
  font-weight: 400;
  margin-top: 5px;
}

.gis__immigrationViewBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;
}

.gis__immigrationViewBtn a {
  width: 130px;
  height: 44px;
  font-size: 14px;
  font-weight: 700;
  line-height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  background: #ff4e53;
  text-decoration: none;
}

/*****************************************************************/
/******************* Immigration Tips Section ********************/
/*****************************************************************/

.gis__immigrationTipsCtn {
  padding: 80px 0;
  background: #f5f9fd;
}

.gis__immigrationTips__flex {
  margin-top: 40px;
  display: flex;
  gap: 28px;
  align-items: center;
  justify-content: center;
}

.gis__immigrationTips__flexItem {
  width: 100%;
  height: 302px;
  border-radius: 13px;
  background-color: #fff;
  border: 1px solid #efefef;
  overflow: hidden;
}

.gis__immigrationTipsItem__img {
  width: 100%;
  height: 174px;
  overflow: hidden;
  background-color: #989898;
}

.gis__immigrationTipsItem__img img {
  height: 100%;
  width: 100%;
}

.gis__immigrationTipsItem__body {
  padding: 16px;
}

.gis__immigrationTipsItem__body h3 {
  margin-bottom: 2px;
}

.gis__immigrationTipsItem__body h4,
.gis__immigrationTipsItem__body p {
  font-size: 14.5px;
}

.gis__immigrationTipsItem__body p {
  font-weight: 400;
}


@media (max-width: 1023px) {
  /*****************************************************************/
  /********************* Banner Section ****************************/
  /*****************************************************************/

  .container-fluid {
    padding: 0;
  }

  .pageFooter {
    padding-bottom: 0px !important;
    margin-top: 0px !important;
  }

  .blueBgDiv {
    height: 0px !important;
  }

  .gis__immigrationBanner {
    height: 400px;
  }

  .gis__immigrationBanner__title {
    max-width: unset;
    font-size: 28px;
    line-height: 33px;
  }

  .gis__immigrationBanner__subTitle {
    margin: 12px 0;
    font-size: 18px;
    line-height: 24px;
  }

  .gis__immigrationBanner__subTitleEnd {
    padding: 4px;
    font-size: 16px;
    line-height: 25px;
  }

  .gis__immigrationBanner__subTitleEnd_class {
    width: 340px;
  }

  /*****************************************************************/
  /********************* One Step Section **************************/
  /*****************************************************************/

  .gis__immigrationStep {
    height: 184px;
  }

  .gis__immigrationStep .container {
    height: 100%;
  }

  .gis__immigrationStep__gridView {
    flex-wrap: wrap;
    gap: 15px;
  }

  .gis__stepCard:first-child h1 {
    width: 100%;
    height: unset;
    text-align: center;
    font-size: 14px;
    line-height: 16.8px;
  }

  .gis__stepCard:first-child {
    width: unset;
    height: unset;
    flex: 0 0 100%;
  }

  .gis__stepCard:not(:first-child) {
    width: 100%;
    height: 110px;
    flex: 0 0 calc((100% - 70px) / 3);
  }

  .gis__stepCard:not(:first-child) h1 {
    width: 80px;
    font-size: 10px;
    font-weight: 800;
    line-height: 14px;
    padding-bottom: 15px;
  }

  /*****************************************************************/
  /********************* Services Section **************************/
  /*****************************************************************/

  .gis__immigrationService {
    padding: 40px 0;
  }

  .gis__immigrationTitle {
    font-size: 24px;
  }

  .gis__immigrationSubTitle {
    font-size: 16px;
    line-height: 20px;
  }

  .gis__immigrationContentWrap {
    margin-top: 25px;
  }

  .gis__immigrationService__gridView {
    grid-template-columns: repeat(1, 1fr);
    gap: 30px;
  }

  .gis__immigrationService__gridItems {
    height: 350px;
    flex-basis: 100%;
  }

  .gis__immigrationService__img {
    width: 100%;
    height: 200px;
  }

  .gis__immigrationService__body {
    padding: 12px;
  }

  .gis__immigrationService__body h4 {
    font-size: 16px;
    line-height: 21px;
  }

  .gis__immigrationService__body p {
    font-size: 14px;
    line-height: 21px;
  }

  .gis__immigrationService__learnBtn {
    top: 100px;
  }

  /*****************************************************************/
  /********************* Fly Abroad Section ************************/
  /*****************************************************************/

  .gis__immigrationAbroad {
    padding: 40px 0;
  }

  .gis__immigrationAbroad .gis__immigrationService__gridView {
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
  }

  .gis__immigrationService__flyGridItem {
    flex-basis: 47%;
    max-width: 170px;
    height: 199px;
    padding: 12px;
    justify-content: center;
    align-items: start;
  }

  .gis__immigrationService__flyGridBody__title {
    margin-top: 6px;
    line-height: 24px;
  }

  .gis__immigrationService__flyGridBody__title b {
    font-size: 20px;
  }

  .gis__immigrationService__flyGridBody__title span {
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
  }

  /*****************************************************************/
  /****************** Fly Abroad Country Section *******************/
  /*****************************************************************/

  .gis__immigrationAbroadCountry .container {
    padding: 40px 16px;
  }

  .gis__immigrationCountry__flyView {
    margin-top: 20px;
  }

  /*****************************************************************/
  /********************** Testimonial Section **********************/
  /*****************************************************************/

  .gis__testimonialsCtn .closeIcon {
    right: 2px;
  }

  .gis__testimonialsCtn {
    padding: 40px 0;
  }

  .gis__testimonialTabList {
    margin-top: 20px;
    gap: 9px;
    justify-content: start;
    overflow-x: auto;
    flex-wrap: nowrap;
  }

  .gis__testimonialTabBtn {
    width: 180px;
    gap: 3px;
    font-size: 16px;
    line-height: 19px;
  }

  .gis__testimonialContentBox {
    margin-top: 33px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
    grid-gap: 20px;
  }

  .gis__testimonialCard {
    max-width: 100%;
    height: 331px;
  }

  .gis__testimonialCardHeader {
    gap: 20px;
  }

  /*****************************************************************/
  /************************* Journey Section ***********************/
  /*****************************************************************/

  .gis__journeyCtn {
    padding: 40px 0;
  }

  .gis__journeyCtn .gis__immigrationService__gridView {
    gap: 24px;
  }

  .gis__immigrationService__gridJourneyItem {
    flex-basis: 100%;
  }

  .gis__immigrationService__gridJourneyItem:first-child {
    align-items: center;
  }

  .gis__immigrationService__gridJourneyItem:first-child h2 {
    font-size: 24px;
    text-align: center;
  }

  .gis__immigrationService__gridJourneyItem:first-child p {
    margin-top: 10px;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    color: #414141;
  }

  .gis__immigrationService__gridJourneyItem:not(:first-child) {
    height: 196px;
    padding: 16px;
  }

  .gis__immigrationService__gridJourneyItem__icon {
    width: 38px;
    height: 38px;
  }

  .gis__immigrationService__gridJourneyItem h2 {
    margin-top: 14px;
    font-size: 16px;
    line-height: 11.09px;
  }

  .gis__immigrationService__gridJourneyItem p {
    font-size: 12px;
    line-height: 16.1px;
  }


  /*****************************************************************/
  /*********************** Fly Abroad 2 Section ********************/
  /*****************************************************************/

  .gis__abroadCtn {
    padding: 40px 0;
  }

  .gis__abroadFlexBox {
    margin-top: 40px;
    justify-content: start;
    width: 100%;
    overflow-x: auto;
  }

  .gis__abroadFlexItem {
    flex-shrink: 0;
  }

  /*****************************************************************/
  /******************* Immigration News Section ********************/
  /*****************************************************************/

  .gis__immigrationNewsCtn {
    padding: 40px 0;
  }

  .gis__immigrationNewsCtn .gis__immigrationService__gridView {
    display: flex;
    margin-top: 40px;
    width: 100%;
    overflow-x: auto;
    gap: 24px;
  }

  .gis__immigrationService__newsItem {
    flex-shrink: 0;
    flex-basis: 100%;
    width: 266px;
    height: 276px;
  }

  .gis__newsItem__img {
    width: 100%;
    /* height: 160px; */
  }

  .gis__newsItem__body {
    padding: 14px;
  }

  .gis__newsItem__body h3,
  .gis__immigrationTipsItem__body h3 {
    font-size: 15px;
    line-height: 17px;
  }

  .gis__newsItem__body h4,
  .gis__immigrationTipsItem__body h4,
  .gis__newsItem__body p,
  .gis__immigrationTipsItem__body p {
    font-size: 14px;
    line-height: 18px;
  }

  .gis__immigrationViewBtn a {
    width: 100%;
  }

  /*****************************************************************/
  /******************* Immigration Tips Section ********************/
  /*****************************************************************/

  .gis__immigrationTipsCtn {
    padding: 40px 0;
    background: #f5f9fd;
  }

  .gis__immigrationTips__flex {
    margin-top: 40px;
    justify-content: start;
    width: 100%;
    overflow-x: auto;
    gap: 24px;
  }

  .gis__immigrationTips__flexItem {
    flex-shrink: 0;
    width: 270px;
    height: 280px;
    border-radius: 13px;
    background-color: #fff;
    border: 1px solid #efefef;
    overflow: hidden;
  }

  .gis__immigrationTipsItem__body h3 {
    margin-bottom: 2px;
  }


}