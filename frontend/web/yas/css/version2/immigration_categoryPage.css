body {
  background-color: #fff;
}

/*****************************************************************/
/********************* Banner Section ****************************/
/*****************************************************************/

.gisSpriteIcon {
  display: inline-block;
  background: url("../../images/master-sprite-gis.webp");
  text-align: left;
  overflow: hidden;
}

.rightArrowIcon {
  background-position: -537px -554px;
  width: 14px;
  height: 14px;
}

.container-fluid {
  padding: 0;
}

.gis__immigrationBanner {
  height: 600px;
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.6) 100%,
      rgba(196, 196, 196, 0.4) 0%,
      rgba(196, 196, 196, 0.4) 100%),
    url(../../images/gis_banner2.webp);
  background-size: 100% 150%;
  background-repeat: no-repeat;
}

.gis__immigrationBanner .container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
}

.gis__immigrationBanner__title {
  max-width: 900px;
  font-size: 52.36px;
  font-weight: 300;
  line-height: 66.44px;
  text-align: left;
  color: #fff;
}

.gis__immigrationBanner__subTitle {
  max-width: 1030px;
  margin: 20px 0;
  font-size: 25px;
  font-weight: 400;
  line-height: 33px;
  letter-spacing: -0.02em;
  text-align: left;
  color: #fff;
}

.gis__immigrationBanner__subTitleEnd {
  padding: 6px;
  font-size: 20.09px;
  font-weight: 400;
  line-height: 25.83px;
  text-align: center;
  color: #fff;
  background: #0966c2;
}

.gis__immigrationBanner__getBtn {
  margin-top: 40px;
  width: 136px;
  height: 41px;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 700;
  line-height: 27px;
  text-align: center;
  outline: none;
  border: none;
  color: #fff;
  background: #ff4e53;
}

.spriteIcon__2 {
  display: inline-block !important;
  background: url(../../images/master-sprite-gis.webp);
  text-align: left;
  overflow: hidden;
}

.gis__teleIcon {
  background-position: -349px -555px;
  width: 15px;
  height: 15px;
  margin-right: 6px;
}

.gis__emailIcon {
  background-position: -686px -640px;
  width: 15px;
  height: 11px;
  margin-right: 6px;
}

.gis__canadaFlag__side,
.gis__usefulLinkSideWidget .canadaEligibilityIcon {
  background-position: -516px -401px;
  width: 54px;
  vertical-align: middle;
  height: 37px;
}

.gis__usefulLinkSideWidget .australiaEligibilityIcon {
  background-position: -217px -410px;
  width: 54px;
  vertical-align: middle;
  height: 37px;
}

.gis__canadaFlag {
  background-position: -580px -230px;
  width: 92px;
  height: 59px;
  display: block !important;
}

.gis__australiaFlag {
  background-position: -457px -230px;
  width: 87px;
  height: 55px;
  display: block !important;
}

.gis__germanyFlag {
  background-position: -220px -630px;
  width: 87px;
  height: 55px;
  display: block !important;
}

.gis__austriaFlag {
  background-position: -339px -226px;
  width: 87px;
  height: 55px;
  display: block !important;
}

.gis__dubaiFlag {
  background-position: -223px -224px;
  width: 87px;
  height: 55px;
  display: block !important;
}

.gis__swedenFlag {
  background-position: -220px -547px;
  width: 87px;
  height: 55px;
  display: block !important;
}

.gis__grid {
  display: grid;
  justify-content: center;
  grid-template-columns: repeat(2, minmax(0, auto));
  margin-top: 60px;
  margin-bottom: 80px;
  max-width: 1150px;
  column-gap: 50px;
}

.gis__countryOptions {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.gis__countryOptions .gis__countryOptions__heading {
  font-size: 32px;
  font-weight: 600;
  line-height: 38px;
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

.gis__countryOptions .gis__countryOptions__row {
  display: flex;
  justify-content: center;
  gap: 20px;
  width: 100%;
}

.gis__countryOptions__row a {
  display: contents;
}

.gis__infoList.row a {
  min-width: 48%;
  flex-basis: 50%;
}

.gis__infoList.row::-webkit-scrollbar {
  appearance: none;
  height: 0;
}

.gis__infoListContainer {
  position: relative;
  width: 100%;
  margin-top: 30px;
}

.over {
  pointer-events: none;
  opacity: 0;
}

.scrollRight,
.scrollLeft {
  position: absolute;
  border-radius: 50%;
  /* top: calc(50% - 20px); */
  outline: none;
  cursor: pointer;
  z-index: 2;
  border: none;
  width: 40px;
  height: 40px;
  background-position: 422px -72px;
  padding: 0;
}

.scrollLeft {
  -webkit-transform: translate(0, -50%) rotate(-180deg);
  transform: translate(0, -50%) rotate(-180deg);
}

.scrollRight {
  right: -18px;
}

.gis__infoListContainer .scrollLeft {
  top: 50%;
  left: -20px;
}

.gis__infoList.row {
  width: 100%;
  margin: 0;
  display: flex;
  overflow: auto;
  gap: 10px;
  flex-wrap: nowrap;
}

.gis__countryOptions__card {
  flex: 1;
  height: 187px;
  border-radius: 30px;
  background-repeat: no-repeat;
  background-position: center;
  padding: 30px 0px 30px 30px;
  position: relative;
  background-size: cover;
}

.gis__countryOptions__card.canadaBG {
  background-image: linear-gradient(180deg,
      rgba(9, 102, 194, 0.3) 10.87%,
      #0966c2 94.02%),
    url(../../images/canada.webp);
}

.gis__countryOptions__card.australiaBG {
  background-image: linear-gradient(180deg,
      rgba(9, 102, 194, 0.3) 10.87%,
      #0966c2 94.02%),
    url(../../images//australia.webp);
}

.gis__countryOptions__card.germanyBG {
  background-image: linear-gradient(180deg,
      rgba(9, 102, 194, 0.3) 10.87%,
      #0966c2 94.02%),
    url(../../images/banner-germany.png);
}

.gis__countryOptions__card.austriaBG {
  background-image: linear-gradient(180deg,
      rgba(9, 102, 194, 0.3) 10.87%,
      #0966c2 94.02%),
    url(../../images/banner-austria.png);
}

.gis__countryOptions__card.dubaiBG {
  background-image: linear-gradient(180deg,
      rgba(9, 102, 194, 0.3) 10.87%,
      #0966c2 94.02%),
    url(../../images/banner-uae.png);
}

.gis__countryOptions__card.swedenBG {
  background-image: linear-gradient(180deg,
      rgba(9, 102, 194, 0.3) 10.87%,
      #0966c2 94.02%),
    url(../../images/banner-sweeden.png);
}

.gis__countryOptions__card span {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #fff;
}

.gis__countryOptions__card .gis__countryOptions__name {
  font-size: 24px;
  font-weight: 700;
  line-height: 38px;
}

.gis__mainContent {
  padding: 20px 20px 30px 20px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  margin-top: 25px;
}

.gis__mainContent p {
  font-size: 16px;
  font-weight: normal;
  line-height: 1.5;
  color: #282828;
}

.gis__mainContent .gis__mainContent__firstHeading {
  font-size: 22px;
  font-weight: 500;
  line-height: 24px;
  color: #282828;
  margin-bottom: 14px;
  margin-top: 30px;
  background-color: #f0f8ff;
  margin-left: -20px;
  padding: 10px 0 10px 20px;
  margin-right: -20px;
}

.gis__mainContent__countryTabber {
  margin-top: 20px;
}

.gis__mainContent__countryTabber .gis__countryTabber__countryRow {
  margin-bottom: 25px;
}

.gis__mainContent__countryTabber .gis__countryTabber__countryRow ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
  display: flex;
  gap: 40px;
  position: relative;
}

.gis__mainContent__countryTabber .gis__countryTabber__countryRow ul::after {
  display: inline-block;
  height: 0.5px;
  width: 100%;
  border: 1px solid #d8d8d8;
  position: absolute;
  content: "";
  bottom: 0px;
  z-index: -1;
}

.gis__mainContent__countryTabber .gis__countryTabber__countryRow ul li {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
  color: #282828;
  padding-bottom: 4px;
  /* width: 50%; */
  width: 100%;
  cursor: pointer;
  display: flex;
  justify-content: center;
}

.gis__mainContent__countryTabber .gis__countryTabber__countryRow ul li.current {
  border-bottom: 2px solid #ff4e53;
  color: #ff4e53;
}

.gis__mainContent__countryTabber .gis__countryTabber__countryRow ul li.gis__countryRow__canada::before {
  content: " ";
  background-position: -473px -409px;
  width: 28px;
  height: 14px;
  background-image: url(../../images/master-sprite-gis.webp);
  margin-top: 5px;
  margin-right: 5px;
}

.gis__mainContent__countryTabber .gis__countryTabber__countryRow ul li.gis__countryRow__australia::before {
  content: " ";
  background-position: -415px -409px;
  width: 34px;
  height: 21px;
  background-image: url(../../images/master-sprite-gis.webp);
  margin-top: 5px;
  margin-right: 5px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__keyHighlightsSection {
  border-radius: 4px;
  background-color: #f0f8ff;
  border: 1px solid #d8d8d8;
  margin-bottom: 30px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__keyHighlightsSection h3 {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #282828;
  padding: 10px 0 10px 20px;
  border-bottom: 1px solid #d8d8d8;
}

.gis__countryTabber__countryContainer .gis__countryContainer__keyHighlightsSection .highlightsList {
  margin: 0;
  list-style-type: none;
  padding: 12px 0 12px 12px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__keyHighlightsSection .highlightsList li {
  font-size: 16px;
  font-weight: normal;
  line-height: 32px;
  color: #282828;
}

.gis__countryTabber__countryContainer .gis__countryContainer__keyHighlightsSection .highlightsList li a {
  color: #282828;
}

.gis__countryTabber__countryContainer .gis__countryContainer__keyHighlightsSection .highlightsList li::before {
  background-image: url(../../images/master-sprite-gis.webp);
  background-position: -686px -343px;
  width: 9px;
  height: 13px;
  content: " ";
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__firstHeading {
  font-size: 24px;
  font-weight: 700;
  line-height: 29.23px;
  padding: 0 0 10px 20px;
  border-bottom: 1px solid #d8d8d8;
  margin-bottom: 20px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__image {
  float: right;
  width: 364px;
  height: 298px;
  border-radius: 6px;
  margin-left: 25px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__commonHeading {
  margin: 30px 0 20px 0;
  padding: 10px 0 10px 24px;
  background-color: #f3f3f3;
  font-size: 22px;
  font-weight: 500;
  line-height: 24px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__list {
  list-style-type: none;
}

.gis__countryTabber__countryContainer .gis__countryContainer__list li {
  color: #282828;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  position: relative;
}

.gis__countryTabber__countryContainer .gis__countryContainer__list li::before {
  background-image: url(../../images/master-sprite-gis.webp);
  background-position: -509px -739px;
  width: 9px;
  height: 9px;
  content: " ";
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
  position: absolute;
  top: 10px;
  left: -20px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__commonImage {
  width: 100%;
  border-radius: 6px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__table {
  margin-top: 30px;
  border-radius: 4px;
  overflow: auto;
  margin-bottom: 20px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__table th {
  border: 2px solid #0966c2;
  background-color: #fff;
}

.gis__countryTabber__countryContainer .gis__countryContainer__table td {
  background: #f2f7fc;
  border-width: 2px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__accordion {
  margin-top: 28px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__accordion .gis__accordion__heading {
  border: 1px solid #d8d8d8;
  background: #f0f8ff;
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
  padding: 12px 12px 12px 25px;
  cursor: pointer;
}

.gis__countryTabber__countryContainer .gis__countryContainer__accordion .gis__accordion__heading::after {
  content: "";
  background-image: url(../../images/master-sprite-gis.webp);
  background-position: -598px -343px;
  width: 14px;
  height: 9px;
  float: right;
  margin-left: 5px;
  margin-top: 10px;
  transform: scale(1.1);
}

.gis__countryTabber__countryContainer .gis__countryContainer__accordion .active:after {
  transform: scale(1.1) rotate(180deg);
}

.gis__countryTabber__countryContainer .gis__countryContainer__accordion .gis__accordion__content {
  background: #fafbfc;
  padding: 14px;
  border-left: 1px solid #d8d8d8;
  border-right: 1px solid #d8d8d8;
  display: none;
}

.gis__countryTabber__countryContainer .gis__countryContainer__accordion .gis__accordion__content:last-child {
  border-bottom: 1px solid #d8d8d8;
}

.gis__countryTabber__countryContainer .gis__countryContainer__accordion .gis__accordion__content table th {
  background-color: #fff;
  font-size: 15px;
  font-weight: 600;
  text-align: left;
  line-height: 26px;
  padding: 12px 0 12px 20px;
}

.gis__countryTabber__countryContainer .gis__countryContainer__accordion .gis__accordion__content table td {
  padding: 9px 0 9px 20px;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  background-color: #fff;
  text-align: left;
}

.gis__grid__right .gis__usefulLinkSideWidget {
  border: 1px solid #d8d8d8;
  border-radius: 4px;
  background-color: #fff;
  padding: 20px 12px;
}

.gis__grid__right .gis__usefulLinkSideWidget h3 {
  font-size: 16px;
  color: #787878;
  font-weight: 600;
  line-height: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #d8d8d8;
}

.gis__grid__right .gis__usefulLinkSideWidget ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.gis__grid__right .gis__usefulLinkSideWidget ul li {
  padding: 16px 0;
  border-bottom: 1px solid #d8d8d8;
}

.gis__grid__right .gis__usefulLinkSideWidget ul li:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}

.gis__grid__right .gis__usefulLinkSideWidget ul li a {
  font-size: 16px;
  color: #282828;
  font-weight: 400;
  line-height: 24px;
}

.gis__grid__right .gis__usefulLinkSideWidget ul li a:hover {
  text-decoration: none;
  cursor: pointer;
  color: #ff4e53;
}

.gis__ourOffices {
  padding: 80px 0px;
}

.gis__ourOffices .locationTab {
  padding: 0;
  max-width: 552px;
  background-color: #fff;
  border-radius: 8px;
}

.gis__ourOffices .locationTab h2 {
  font-size: 24px;
  font-weight: 500;
  line-height: 20px;
  color: #282828;
  margin-bottom: 20px;
}

.gis__ourOffices .locationTab .locationTabCities {
  list-style-type: none;
  display: flex;
  row-gap: 12px;
  column-gap: 10px;
  padding: 0;
  margin: 0;
  flex-wrap: wrap;
  padding-bottom: 20px;
  border-bottom: 1px solid #d8d8d8;
  margin-bottom: 20px;
}

.gis__ourOffices .locationTab .locationTabCities li {
  flex-basis: 48%;
  flex-grow: 1;
  max-width: 235px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  padding: 6px 16px;
  position: relative;
}

.gis__ourOffices .locationTab .locationTabCities li label {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  color: #282828;
}

.gis__ourOffices .locationTab .locationTabCities li input {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
}

.gis__ourOffices .locationTab .locationTabCities li:has(input:checked) {
  border-left: 4px solid #ff615d;
}

.gis__ourOffices .locationTab .locationTabCities li input:checked+label {
  font-weight: 600;
  color: #ff615d;
}

.gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress {
  display: none;
}

.gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress h3 {
  margin-bottom: 10px;
}

.gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress .col-md-6 {
  font-size: 16px;
  line-height: 1.5;
}

.gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress .locationPhone {
  margin-bottom: 10px;
}

.gis__ourOffices .locationTab .selectedCityArea .selectedCity {
  display: block;
}

.gis__ourOffices .mapTab {
  padding: 0px;
}

.gis__ourOffices .mapTab .selectedCityMap {
  display: none;
  height: 100%;
}

.gis__ourOffices .mapTab .selectedCityMap iframe {
  height: 100% !important;
}

.gis__ourOffices .mapTab .selectedCityMap iframe .review-box {
  display: none;
}

.gis__ourOffices .mapTab .selectedCity {
  display: block;
}

/*****************************************************************/
/************************* Journey Section ***********************/
/*****************************************************************/

.profileIcon {
  background-position: -441px -731px;
  width: 24px;
  height: 28px;
}

.languageIcon {
  background-position: -680px -728px;
  width: 25px;
  height: 25px;
}

.educationIcon {
  background-position: -539px -470px;
  width: 28px;
  height: 28px;
}

.prIcon {
  background-position: -308px -721px;
  width: 28px;
  height: 28px;
}

.invitationIcon {
  background-position: -372px -725px;
  width: 29px;
  height: 28px;
}

.gis__journeyCtn {
  padding: 80px 0;
  background: #0966c217;
}

.gis__immigrationService__gridView {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.gis__immigrationService__gridJourneyItem:first-child {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: start;
}

.gis__immigrationService__gridJourneyItem:first-child h2 {
  font-size: 30px;
  font-weight: 700;
  line-height: 38px;
  letter-spacing: 0.30000001192092896px;
  text-align: left;
  color: #414141;
}

.gis__immigrationService__gridJourneyItem:first-child p {
  margin-top: 10px;
  font-size: 19px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  color: #414141;
}

.gis__immigrationService__gridJourneyItem:not(:first-child) {
  width: 100%;
  height: 234px;
  padding: 20px;
  display: flex;
  justify-content: start;
  flex-direction: column;
  align-items: start;
  border-radius: 16px;
  background-color: #fff;
  box-shadow: 0px 0px 5.28px 0px #0000000f;
}

.gis__immigrationService__gridJourneyItem__icon {
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #0966c217;
}

.gis__immigrationService__gridJourneyItem h2 {
  margin-top: 16px;
  font-size: 20px;
  font-weight: 700;
  line-height: 14.09px;
  letter-spacing: -0.02em;
  text-align: left;
  color: #000;
}

.gis__immigrationService__gridJourneyItem p {
  font-size: 15px;
  font-weight: 400;
  line-height: 19.1px;
  text-align: left;
  margin-top: 10px;
  color: #989898;
}

/*****************************************************************/
/*********************** Eligibilty Section **********************/
/*****************************************************************/

.gis__eligibilityCtn {
  padding: 80px;
  background: #201f1f05;
}

.gis__eligibilityCtn .gis__immigrationService__gridView {
  margin-top: 40px;
}

.gis__immigrationService__gridEligibilityItem {
  width: 386px;
  height: 393px;
  padding: 20px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 14px;
  align-items: center;
  background: #ffffff;
  border-radius: 16.44px;
  box-shadow: 0px 0px 5.28px 0px #00000017;
}

.gis__immigrationService__gridEligibilityImg {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  border: 1px solid #989898;
  overflow: hidden;
}

.gis__immigrationService__gridEligibilityImg img {
  object-fit: fill;
  width: 100%;
  height: 100%;
}

.gis__immigrationService__gridEligibilityItem h2 {
  font-size: 20px;
  font-weight: 700;
  line-height: 22px;
  letter-spacing: -0.02em;
  text-align: center;
  color: #414141;
}

.gis__immigrationService__gridEligibilityItem p {
  font-size: 18px;
  font-weight: 400;
  line-height: 21.6px;
  text-align: center;
  color: #414141;
}

.gis__immigrationService__gridEligibilityItem a {
  display: flex;
  gap: 5px;
  align-items: center;
  font-size: 15px;
  font-weight: 800;
  line-height: 24px;
  text-align: left;
  color: #ff4e53;
}

/*****************************************************************/
/*********************** Fly Abroad 2 Section ********************/
/*****************************************************************/

.videoPlayIcon {
  background-position: -322px -393px;
  width: 60px;
  height: 60px;
}

.gis__abroadCtn {
  padding: 80px 0;
  background: #0966c20d;
}

.gis__abroadFlexBox {
  margin-top: 40px;
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

.gis__abroadFlexItem {
  width: 276px;
  height: 271px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #eaeaea;
  overflow: hidden;
}

.gis__abroadFlexItem__img {
  position: relative;
  width: 100%;
  height: 207px;
}

.gis__abroadFlexItem__img button {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 60px;
  width: 60px;
  outline: none;
  border: none;
  border-radius: 50%;
  background-color: transparent;
  transform: translate(-50%, -50%);
}

.gis__abroadFlexItem__img img {
  width: 100%;
  height: 100%;
}

.gis__abroadFlexItem__body {
  padding: 20px;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.3px;
  text-align: left;
}

/*****************************************************************/
/******************* Immigration News Section ********************/
/*****************************************************************/

.gis__immigrationNewsCtn {
  padding: 80px 0;
  background-image: url(../../images/gis_newsBg.webp);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 0% 100%;
}

.gis__immigrationNewsCtn .gis__immigrationService__gridView {
  margin-top: 40px;
}

.gis__immigrationService__newsItem {
  width: 100%;
  height: 374px;
  border-radius: 28px;
  border: 1px solid #efefef;
  box-shadow: 0px 0px 9px 0px #0000001a;
  overflow: hidden;
}

.gis__newsItem__img {
  width: 100%;
  height: 220px;
  background-color: #989898;
  overflow: hidden;
}

.gis__newsItem__body {
  padding: 20px;
}

.gis__newsItem__body h3,
.gis__immigrationTipsItem__body h3 {
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  text-align: left;
  color: #414141;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 10px;
}

.gis__newsItem__body h4,
.gis__immigrationTipsItem__body h4,
.gis__newsItem__body p,
.gis__immigrationTipsItem__body p {
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  color: #989898;
}

.gis__newsItem__body p {
  font-weight: 400;
  margin-top: 5px;
}

.gis__immigrationViewBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;
}

.gis__immigrationViewBtn a {
  width: 130px;
  height: 44px;
  font-size: 14px;
  font-weight: 700;
  line-height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  background: #ff4e53;
}

/*****************************************************************/
/******************* Immigration Tips Section ********************/
/*****************************************************************/

.gis__immigrationTipsCtn {
  padding: 80px 0;
  background: #f5f9fd;
}

.gis__immigrationTips__flex {
  margin-top: 40px;
  display: flex;
  gap: 28px;
  align-items: center;
  justify-content: center;
}

.gis__immigrationTips__flexItem {
  width: 100%;
  height: 302px;
  border-radius: 13px;
  background-color: #fff;
  border: 1px solid #efefef;
  overflow: hidden;
}

.gis__immigrationTipsItem__img {
  width: 100%;
  height: 174px;
  overflow: hidden;
  background-color: #989898;
}

.gis__immigrationTipsItem__img img {
  height: 100%;
  width: 100%;
}

.gis__immigrationTipsItem__body {
  padding: 16px;
}

.gis__immigrationTipsItem__body h3 {
  margin-bottom: 2px;
}

.gis__immigrationTipsItem__body h4,
.gis__immigrationTipsItem__body p {
  font-size: 14.5px;
}

.gis__immigrationTipsItem__body p {
  font-weight: 400;
}

.gis__immigrationTitle {
  font-size: 30px;
  font-weight: 700;
  line-height: 38px;
  letter-spacing: 0.3px;
  text-align: center;
  color: #414141;
}

.gis__immigrationSubTitle {
  margin-top: 10px;
  font-size: 20px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
  color: #414141;
}

@media (max-width: 1023px) {
  .blueBgDiv {
    height: 0px !important;
  }

  .gis__grid {
    grid-template-columns: minmax(0, 1fr);
    margin-top: 10px;
  }

  .gis__grid__left {
    max-width: 100%;
  }

  .gis__countryOptions .gis__countryOptions__heading {
    font-size: 24px;
    font-weight: 600;
    line-height: 38px;
    text-align: center;
    margin-bottom: 0px;
  }

  .gis__countryOptions .gis__countryOptions__row {
    gap: 15px;
  }

  .gis__countryOptions .gis__countryOptions__card {
    padding: 12px;
    height: 200px;
  }

  .gis__mainContent p {
    font-size: 14px;
    line-height: 21px;
  }

  .gis__mainContent .gis__mainContent__firstHeading {
    margin-left: -10px;
    margin-right: -10px;
    border-radius: 2px;
  }

  .gis__mainContent__countryTabber .gis__countryTabber__countryRow {
    margin: 0 -20px 25px -20px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__keyHighlightsSection .highlightsList {
    padding-left: 30px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__keyHighlightsSection .highlightsList li {
    font-size: 14px;
    position: relative;
    line-height: 22px;
    margin-bottom: 8px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__keyHighlightsSection .highlightsList li::before {
    position: absolute;
    left: -20px;
    top: 5px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__firstHeading {
    font-size: 20px;
    font-weight: 700;
    line-height: 24.36px;
    margin-left: -20px;
    margin-right: -20px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__image {
    width: 100%;
    margin: 0;
    margin-bottom: 25px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__commonHeading {
    margin-left: -20px;
    margin-right: -20px;
    padding: 10px 0 10px 6px;
    font-size: 18px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__list {
    padding-left: 20px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__list li {
    font-size: 14px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__accordion .gis__accordion__heading {
    padding: 10px;
    padding-right: 20px;
  }

  .gis__countryTabber__countryContainer .gis__countryContainer__accordion .gis__accordion__heading::after {
    position: relative;
    left: 10px;
    /* top: -10px; */
  }

  .gis__countryOptions__card {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .gis__countryOptions__card .gis__countryOptions__name {
    font-size: 20px;
  }

  .gis__countryOptions__card span {
    font-size: 14px;
    white-space: nowrap;
    line-height: 10px;
  }

  .gis__countryOptions__card .gis__canadaFlag,
  .gis__countryOptions__card .gis__australiaFlag {
    transform: scale(0.8) translateX(-10px);
  }

  .gis__ourOffices {
    padding: 26px 16px 0 16px;
  }

  .gis__ourOffices .mapTab .selectedCityMap iframe {
    width: 100%;
    min-height: 255px;
  }

  .gis__ourOffices .locationTab {
    margin-top: 50px;
  }

  .gis__ourOffices .locationTab h2 {
    font-size: 15px;
    font-weight: 500;
  }

  .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress h3 {
    font-size: 14px;
    font-weight: 500;
  }

  .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress .gis__ourOffices__mobileDivision {
    flex: 0 0 50%;
    max-width: 50%;
    font-size: 10px;
  }

  .gis__ourOffices .locationTab .locationTabCities {
    padding-bottom: 14px;
    margin-bottom: 14px;
  }

  .gis__ourOffices .locationTab .locationTabCities li {
    max-width: 48%;
    height: 24px;
    padding: 4px 0 4px 10px;
    line-height: 15px;
    font-size: 10px;
  }

  .gis__ourOffices .locationTab .locationTabCities li label {
    line-height: 15px;
    font-size: 10px;
  }

  .gis__ourOffices {
    padding: 26px 16px 0 16px;
  }

  .gis__ourOffices .mapTab .selectedCityMap iframe {
    width: 100%;
    min-height: 255px;
  }

  .gis__ourOffices .locationTab {
    margin-top: 50px;
  }

  .gis__ourOffices .locationTab h2 {
    font-size: 15px;
    font-weight: 500;
  }

  .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress h3 {
    font-size: 14px;
    font-weight: 500;
  }

  .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress .gis__ourOffices__mobileDivision {
    flex: 0 0 50%;
    max-width: 50%;
    font-size: 10px;
  }

  .gis__ourOffices .locationTab .locationTabCities {
    padding-bottom: 14px;
    margin-bottom: 14px;
  }

  .gis__ourOffices .locationTab .locationTabCities li {
    max-width: 48%;
    height: 24px;
    padding: 4px 0 4px 10px;
    line-height: 15px;
    font-size: 10px;
  }

  .gis__ourOffices .locationTab .locationTabCities li label {
    line-height: 15px;
    font-size: 10px;
  }

  /*****************************************************************/
  /********************* Banner Section ****************************/
  /*****************************************************************/

  .container-fluid {
    padding: 0;
  }

  .gis__immigrationBanner {
    height: 400px;
  }

  .gis__immigrationBanner__title {
    max-width: unset;
    font-size: 28px;
    line-height: 33px;
  }

  .gis__immigrationBanner__subTitle {
    margin: 12px 0;
    font-size: 18px;
    line-height: 24px;
  }

  .gis__immigrationBanner__subTitleEnd {
    padding: 4px;
    font-size: 16px;
    line-height: 25px;
  }

  /*****************************************************************/
  /************************* Journey Section ***********************/
  /*****************************************************************/

  .gis__journeyCtn {
    padding: 40px 0;
  }

  .gis__immigrationService__gridView {
    grid-template-columns: repeat(1, 1fr);
    gap: 30px;
  }

  .gis__journeyCtn .gis__immigrationService__gridView {
    gap: 24px;
  }

  .gis__immigrationService__gridJourneyItem:first-child {
    align-items: center;
  }

  .gis__immigrationService__gridJourneyItem:first-child h2 {
    font-size: 24px;
    text-align: center;
  }

  .gis__immigrationService__gridJourneyItem:first-child p {
    margin-top: 10px;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    color: #414141;
  }

  .gis__immigrationService__gridJourneyItem:not(:first-child) {
    height: 196px;
    padding: 16px;
  }

  .gis__immigrationService__gridJourneyItem__icon {
    width: 38px;
    height: 38px;
  }

  .gis__immigrationService__gridJourneyItem h2 {
    margin-top: 14px;
    font-size: 16px;
    line-height: 11.09px;
  }

  .gis__immigrationService__gridJourneyItem p {
    font-size: 12px;
    line-height: 16.1px;
  }

  /*****************************************************************/
  /*********************** Eligibilty Section **********************/
  /*****************************************************************/

  .gis__eligibilityCtn {
    padding: 40px 0;
  }

  .gis__eligibilityCtn .gis__immigrationService__gridView {
    margin-top: 40px;
    gap: 26px;
  }

  .gis__immigrationService__gridEligibilityItem {
    width: 100%;
    height: 327px;
    padding: 16px;
  }

  .gis__immigrationService__gridEligibilityImg {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
  }

  .gis__immigrationService__gridEligibilityItem h2 {
    font-size: 16px;
    line-height: 18px;
  }

  .gis__immigrationService__gridEligibilityItem p {
    font-size: 15px;
    font-weight: 400;
  }

  .gis__immigrationService__gridEligibilityItem a {
    font-size: 15px;
  }

  /*****************************************************************/
  /*********************** Fly Abroad 2 Section ********************/
  /*****************************************************************/

  .gis__abroadCtn {
    padding: 40px 0;
  }

  .gis__abroadFlexBox {
    margin-top: 40px;
    justify-content: start;
    width: 100%;
    overflow-x: auto;
  }

  .gis__abroadFlexItem {
    flex-shrink: 0;
  }

  /*****************************************************************/
  /******************* Immigration News Section ********************/
  /*****************************************************************/

  .gis__immigrationNewsCtn {
    padding: 40px 0;
  }

  .gis__immigrationNewsCtn .gis__immigrationService__gridView {
    display: flex;
    margin-top: 40px;
    width: 100%;
    overflow-x: auto;
    gap: 24px;
  }

  .gis__immigrationService__newsItem {
    flex-shrink: 0;
    width: 266px;
    height: 276px;
  }

  .gis__newsItem__img {
    width: 100%;
    height: 160px;
  }

  .gis__newsItem__body {
    padding: 14px;
  }

  .gis__newsItem__body h3,
  .gis__immigrationTipsItem__body h3 {
    font-size: 15px;
    line-height: 17px;
  }

  .gis__newsItem__body h4,
  .gis__immigrationTipsItem__body h4,
  .gis__newsItem__body p,
  .gis__immigrationTipsItem__body p {
    font-size: 14px;
    line-height: 18px;
  }

  .gis__immigrationViewBtn a {
    width: 100%;
  }

  /*****************************************************************/
  /******************* Immigration Tips Section ********************/
  /*****************************************************************/

  .gis__immigrationTipsCtn {
    padding: 40px 0;
    background: #f5f9fd;
  }

  .gis__immigrationTips__flex {
    margin-top: 40px;
    justify-content: start;
    width: 100%;
    overflow-x: auto;
    gap: 24px;
  }

  .gis__immigrationTips__flexItem {
    flex-shrink: 0;
    width: 270px;
    height: 280px;
    border-radius: 13px;
    background-color: #fff;
    border: 1px solid #efefef;
    overflow: hidden;
  }

  .gis__immigrationTipsItem__body h3 {
    margin-bottom: 2px;
  }

  .gis__immigrationTitle {
    font-size: 24px;
  }

  .gis__immigrationSubTitle {
    font-size: 16px;
    line-height: 20px;
  }

  .gis__infoList.row a {
    min-width: 48%;
    flex-basis: 50%;
  }

  .gis__infoListContainer {
    max-width: calc(90vw);
    margin-top: 30px;
  }

  .scrollRight,
  .scrollLeft {
    display: none !important;
  }
}