:root {
    --white-color: #fff;
    --border-gray: #d8d8d8;
    --primary-black-color: #282828;
    --light-gray-color: #989898;
    --primary-red-color: #ff4e3a;
    --background-gray-color: #f5f5f5
}

.selectedCollege__heading_anchor:hover {
    text-decoration: none;
  }
  
  .collegeInfo {
    padding-right: 30px;
  }
  
  .collegeInfo__flexContent {
    justify-content: space-between;
  }
  
  .heroSection__leftSide {
    display: flex;
  }
  
  .heroSection__rightSide {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
  }
/* College compare utility panel css starts*/

  .compareText {
    font-size: 14px;
    font-weight: 700;
    line-height: 12px;
  }
  
  .compareCloseIcon {
    background-position: -11px -10px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  
  
  .collegeCompare__container {
    animation: slideIn 1000ms ease-in-out;
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: var(--white-color);
    box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.1019607843);
    padding: 14px 0;
    letter-spacing: 0.3px;
    z-index: 11;
    display: none;
  }
  
  .collegeCompare__headingContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 14px;
  }
  
  .collegeCompare__headingContainer .compareCollege__heading {
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
  }
  
  @keyframes slideIn {
    from {
      transform: translateY(100vh);
    }
  
    to {
      transform: translateY(0vh);
    }
  }
  
  .collegeCompare__selectCollegeScreen__container {
    border-top: 1px solid var(--border-gray);
    border-bottom: 1px solid var(--border-gray);
  }
  
  .collegeCompare__compareButton__container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
    padding-bottom: 0;
  }
  
  .collegeCompare__compareButton__container .collegeCompare__compareButton:disabled,
  button.drawer__close__submit:disabled {
    opacity: 0.4;
  }
  
  .collegeCompare__compareButton__container .collegeCompare__compareButton {
    border: none;
    border-radius: 4px;
    background: var(--primary-red-color);
    color: var(--white-color);
    display: flex;
    width: 280px;
    padding: 7px 18px;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: 700;
    line-height: 22px;
    letter-spacing: 0.3px;
    text-transform: capitalize;
  }
  
  .selectCollegeScreen__panels {
    display: flex;
    position: relative;
  }
  
  .selectCollegeScreen__panels:after {
    content: " ";
    display: inline-block;
    position: absolute;
    width: 1px;
    height: 100%;
    background-color: var(--border-gray);
    top: 0;
    left: 50%;
  }
  
  .selectCollegeScreen__panels .selectionPanel {
    flex-basis: 50%;
    padding: 22px 0;
    display: flex;
    gap: 24px;
  }
  
  .selectCollegeScreen__leftPanel {
    /* border-right: 1px solid var(--border-gray); */
    position: relative;
  }
  
  .selectCollegeScreen__rightPanel {
    justify-content: center;
  }
  
  .selectionPanel .selectionLogo__div {
    border-radius: 4px;
    border: 1px solid var(--border-gray);
    width: 56px;
    height: 56px;
    background: var(--white-color);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .selection__Inputs {
    flex-grow: 1;
    max-width: 360px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    position: relative;
  }
  
  .selection__Inputs .clearSelection {
    width: 20px;
    height: 20px;
    /* border-radius: 50%;
    border: 2px solid var(--primary-black-color); */
    position: absolute;
    top: -10px;
    right: -30px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    display: none;
  }
  
  .selection__Inputs .clearIcon {
    /* background-position: -244px -1142px;
    width: 13px;
    height: 13px; */
    transform: scale(0.7);
  }
  
  .selection__Input__Box {
    height: 44px;
    position: relative;
  }
  
  .selection__Input__Box .select2,
  .selection__Input__Box .select2-container .select2-selection--single,
  .selection__Input__Box .select2-selection__rendered,
  .selection__Input__Box.selection__Select__Program .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 100%;
    width: 100% !important;
  }
  
  .selection__Input__Box.selection__Search__College .select2-selection__arrow {
    display: none;
  }
  
  .selection__Input__Box .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    transform: scale(1.5) rotate(180deg);
  }
  
  .selection__Input__Box .select2-container--default .select2-selection--single {
    border: 1px solid var(--border-gray);
  }
  
  .selection__Input__Box .select2-container--default .select2-selection--single .select2-selection__arrow b {
    background-image: url("https://www.getmyuni.com/yas/images/select-angle.png");
    background-color: transparent;
    background-size: contain;
    border: none !important;
    width: 9px;
    height: 6px;
    top: 20px;
    right: 16px;
    margin: 0;
    left: unset;
    transform: scale(1.5);
  }
  
  .selection__Input__Box .select2-container--default .select2-selection--single .select2-selection__rendered {
    display: flex;
    align-items: center;
    padding-left: 42px;
  }
  
  .selection__Input__Box .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: var(--light-gray-color);
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.3px;
  }
  
  .selection__Input__Box .searchIcon {
    background-position: -152px -214px;
    width: 16px;
    height: 16px;
    top: 14px;
    left: 16px;
    position: absolute;
  }
  
  .selection__Input__Box .courseIcon {
    background-position: -306px -634px;
    width: 18px;
    height: 14px;
    position: absolute;
    top: 14px;
    left: 16px;
  }
  
  .selection__Inputs .selectedCollege {
    display: none;
    gap: 20px;
  }
  
  .selection__Inputs .selectedCollege.value__selected {
    display: flex;
  }
  
  .selectedCollege .selectedCollege__heading {
    color: var(--primary-black-color);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
  
  .selectedCollege .selectedCollege__subheading {
    font-size: 12px;
    font-weight: 400;
    line-height: 24px;
    color: var(--primary-black-color);
  }
  
  .selectedProgram .selectedProgram__heading {
    color: var(--primary-black-color);
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }
  
  .selectedProgram .editIcon,
  .selectedCollege .editIcon {
    /* background-position: -280px -1140px;
    width: 16px;
    height: 16px; */
    cursor: pointer;
    flex-shrink: 0;
  }
  
  .selection__Inputs .selectedProgram {
    display: none;
    gap: 26px;
    align-items: center;
  }
  
  .selection__Inputs .selectedProgram.value__selected {
    display: flex;
  }
  
  .versusText {
    color: var(--primary-black-color);
    font-size: 12px;
    font-weight: 500;
    line-height: normal;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(50%, -50%);
    border-radius: 50%;
    border: 1px solid var(--border-gray);
    padding: 5px;
    color: var(--primary-black-color);
    font-size: 12px;
    font-weight: 500;
    background-color: var(--white-color);
    z-index: 1;
  }
  
  .collegeCompare__drawer__mobile .drawercloseIcon svg,
  .collegeCompare__drawer__mobile .drawercloseIcon path {
    pointer-events: none;
  }
  
  .editIcon svg,
  .editIcon path {
    pointer-events: none;
  }
  
  .selectedProgram .editIcon,
  .selectedCollege .editIcon {
    background-position: -42px -43px;
    width: 16px;
    height: 16px;
  }
  
  .selection__Inputs .clearIcon {
    background-position: -9px -43px;
    width: 20px;
    height: 20px;
  }
  
  .selection__img {
    background-position: -89px -128px;
    width: 44px;
    height: 49px;
  }
  /* College compare utility panel css ends*/
@media(max-width:1023px){
      /* college-compare css */

  /* college-comapre css starts*/
  .collegeInfo__flexContent {
    flex-wrap: nowrap;
  }

  .collegeInfo {
    padding: 10px;
    padding-top: 20px;
  }

  .compareText {
    font-size: 12px;
  }

  .heroSection__leftSide {
    flex-direction: column;
  }

  .heroSection__leftSide .collegeIntro ul {
    display: none;
  }

  .collegeCompare__container {
    padding: 10px 0;
  }

  .collegeCompare__headingContainer {
    margin-bottom: 10px;
  }

  .collegeCompare__headingContainer .compareCollege__heading {
    font-size: 16px;
  }

  .selectCollegeScreen__panels .selectionPanel {
    padding: 12px 0;
    flex-direction: column;
    gap: 6px;
    width: 100%;
  }

  .selectCollegeScreen__panels .selectionPanel.selectCollegeScreen__leftPanel {
    padding-right: 12px;
  }

  .selectCollegeScreen__panels .selectionPanel.selectCollegeScreen__rightPanel {
    padding-left: 12px;
  }

  .selectCollegeScreen__panels .selectionPanel.unselectedPanel {
    align-items: center;
  }

  /* .selectCollegeScreen__panels .selectionPanel .selectionLogo__div {
    width: 40px;
    height: 40px;
  } */

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs {
    gap: 10px;
    width: 100%;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege,
  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedProgram {
    gap: 0px;
    flex-wrap: wrap;
  }

  .selectCollegeScreen__panels .selectionPanel .collegeCompare__drawer__mobile .selection__Inputs .selectedCollege,
  .selectCollegeScreen__panels .selectionPanel .collegeCompare__drawer__mobile .selection__Inputs .selectedProgram {
    flex-wrap: unset;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege .selectedCollege__heading,
  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege .selectedProgram__heading,
  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedProgram .selectedCollege__heading,
  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedProgram .selectedProgram__heading {
    flex-basis: 80%;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege.value__selected {
    margin-bottom: 5px;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege .selectedCollege__heading {
    font-size: 14px;
    line-height: 22px;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedCollege .editIcon {
    align-self: flex-end;
  }

  .selectCollegeScreen__panels .selectionPanel .selection__Inputs .selectedProgram .selectedProgram__heading {
    font-size: 14px;
    line-height: 22px;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs {
    width: 100%;
    display: block;
    position: relative;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .clearSelection {
    top: -70px;
    right: -10px;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box {
    padding: 12px 15px;
    border: 1px solid var(--border-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--light-gray-color);
    background-color: var(--white-color);
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box p,
  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box img {
    pointer-events: none;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box .editIcon {
    flex-basis: 20%;
    align-self: flex-end;
  }

  .selectCollegeScreen__panels .mobile__selection__inputs .mobile__input__box:first-child {
    margin-bottom: 12px;
  }

  .collegeCompare__compareButton__container {
    padding: 10px;
  }

  .collegeCompare__compareButton__container .collegeCompare__compareButton {
    width: 100%;
  }

  .collegeCompare__drawer__mobile {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: var(--white-color);
    z-index: 20;
    top: 0;
    left: 0;
    animation: slideIn 1000ms ease-in-out;
    display: none;
  }

  .collegeCompare__drawer__mobile .selection__Input__Box .select2 {
    width: 100% !important;
  }

  .collegeCompare__drawer__mobile .drawercloseIcon {
    background-position: -244px -1142px;
    width: 13px;
    height: 13px;
  }

  .collegeCompare__drawer__mobile .drawer__heading {
    padding: 18px 20px;
    border-bottom: 1px solid var(--border-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .collegeCompare__drawer__mobile .drawer__heading h2 {
    color: var(--primary-black-color);
    font-size: 16px;
    font-weight: 600;
    line-height: normal;
  }

  .collegeCompare__drawer__mobile .drawer__subheading {
    color: var(--light-gray-color);
    font-size: 14px;
    font-weight: 400;
  }

  .collegeCompare__drawer__mobile .drawer__container {
    padding: 24px 20px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    height: calc(100% - 56px);
  }

  .collegeCompare__drawer__mobile .drawer__content .selectedCollege.value__selected {
    border-bottom: 1px solid var(--border-gray);
    padding-bottom: 15px;
  }

  .collegeCompare__drawer__mobile .selectedProgram__heading {
    font-weight: 700;
  }

  .collegeCompare__drawer__mobile .selection__Inputs .selectedCollege,
  .collegeCompare__drawer__mobile .selection__Inputs .selectedProgram {
    justify-content: space-between;
  }

  .collegeCompare__drawer__mobile .drawer__close__submit {
    width: 100%;
    background-color: var(--primary-red-color);
    color: var(--white-color);
    border: none;
    padding: 9px 18px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 700;
  }

  .selectCollegeScreen__panels .selectionPanel.selectCollegeScreen__leftPanel,
  .selectCollegeScreen__panels .selectionPanel.selectCollegeScreen__rightPanel {
    padding: 18px;
  }

  /* college-compare css ends */
}