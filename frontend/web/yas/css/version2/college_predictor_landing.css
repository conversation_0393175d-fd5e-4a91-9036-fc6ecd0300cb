.predictor-header {
    height: 130px;
    background: #FFF0BE;
    background: radial-gradient(circle, rgb(255 240 190 / 45%) 18%, rgb(237 221 83 / 45%) 100%);
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center
}

.predictor-header h1 {
    font-size: 40px;
    line-height: 54px;
    font-weight: 600
}

.predict-college {
    position: relative;
    border-radius: 4px;
    border: 1px solid rgba(216, 216, 216, 1);
    background-color: var(--color-white);
    margin: 20px 0;
    overflow: hidden;
}

.field-complete {
    background-color: rgba(219, 235, 255, 1);
    color: rgba(9, 102, 194, 1);
    height: 34px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.predict-college ul {
    padding: 20px;
    margin: 0;
    list-style: none;
    display: flex;
    gap: 40px;
    justify-content: space-between;
    align-items: end;
}

.predict-college ul li label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 16px;
    line-height: 15px;
}

.predict-college ul li {
    width: 34%;
}

.predict-college ul li input,
.predict-college ul li select {
    border: 1px solid rgba(216, 216, 216, 0.8);
    background: var(--color-white);
    border-radius: 4px;
    padding: 7px 12px;
    font-size: 14px;
    color: rgba(140, 149, 166, 1);
    width: 100%;
}

.predict-college ul li span {
    color: var(--color-red);
    font-weight: bold;
}

.predict-college ul li:last-child {
    justify-content: end;
    display: flex;
    width: 20%;
}

.predict-college ul button {
    width: 100%;
    height: 36px;
    background-color: var(--color-red);
    color: var(--color-white);
    border-radius: 3px;
    border: 0;
}

/*-----tab------*/
.predict-college-tab {
    position: relative;
    border-radius: 4px;
    border: 1px solid rgba(216, 216, 216, 1);
    background-color: var(--color-white);
    margin: 20px 0;
    overflow: hidden;
}

ul.tabs {
    margin: 0;
    padding: 10px 20px 10px 20px;
    list-style: none;
    display: flex;
    border-bottom: 1px solid #D8D8D8;
    gap: 25px;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: inherit;
    cursor: pointer;
}

/* width */
ul.tabs::-webkit-scrollbar {
    height: 2px;
    display: none;
}

/* Track */
ul.tabs::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
ul.tabs::-webkit-scrollbar-thumb {
    background: #ccc;
}

ul.tabs li {
    position: relative;
    color: #787878;
    font-weight: 400;
    line-height: 24px;
    font-size: 14px;
}

ul.tabs li.current {
    color: var(--color-red);
}

ul.tabs li.current:before {
    position: absolute;
    content: '';
    width: 100%;
    height: 2px;
    background: var(--color-red);
    left: 0;
    bottom: -11px;
}

.tab-content-section {
    padding: 20px;
}

.tab-content-section h2 {
    font-size: 18px;
    line-height: 28px;
    margin-bottom: 20px;
}

.tab-content ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.logo-section {
    width: 275px;
    border-radius: 4px;
    border: 1px solid #D8D8D899;
    height: 53px;
    color: #333333;
    font-weight: 400;
    font-size: 15px;
    line-height: 24px;
    display: flex;
    padding: 8px;
    gap: 15px;
    align-items: center
}

.icon-section {
    width: 36px;
    height: 36px;
    border-radius: 20px;
    border: 1px solid #AAAAAA33
}

/*-----tab------*/

/*-----share on whatsup------*/
.share-whatsup {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
    padding: 20px;
    margin-bottom: 20px;
    overflow: hidden;
}

.share-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.share-flex h3 {
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
}

.share-btn {
    white-space: nowrap;
    display: flex;
    gap: 15px;
}

.whatsup {
    background-color: rgba(0, 153, 101, 1);
    width: 187px;
    height: 32px;
    border: 1px solid rgba(0, 153, 101, 1);
    color: #fff;
    border-radius: 8px;
}

.copy-link {
    border: 1px solid var(--color-red);
    color: var(--color-red);
    width: 120px;
    height: 32px;
    background-color: transparent;
    border-radius: 8px;
}

.whatsupicon,
.attachIcon {
    width: 19px;
    height: 18px;
    background-position: 718px -1200px !important;
    vertical-align: text-bottom;
    margin-right: 10px;
}

.attachIcon {
    background-position: 673px -1200px !important;
}

.pageFooter {
    margin-top: auto !important;
    padding-bottom: 0 !important;
}

.tab-content ul li a:hover {
    text-decoration: none
}

.tab-content {
    min-height: 300px
}

/*-----share on whatsup------*/
@media (max-width: 1023px) {
    .blueBgDiv {
        height: auto
    }

    .tab-content ul li {
        width: 48%;
    }

    .logo-section {
        width: 100%;
    }

}

@media (max-width: 640px) {

    .tab-content ul li,
    .logo-section {
        width: 100%;
    }

    .share-flex {
        justify-content: center;
        flex-wrap: wrap;
        gap: 20px
    }

    .share-flex h3 {
        text-align: center
    }

    .predictor-header h1 {
        font-size: 20px
    }
}