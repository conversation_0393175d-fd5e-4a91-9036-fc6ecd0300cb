@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

h1,
h2,
h3,
h4,
h5 {
    padding: 0;
    margin: 0;
    font-weight: 600;
    line-height: normal;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

body,
p {
    font-family: "Roboto", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    font-variation-settings: "wdth"100;
    color: #000000;
    line-height: 1.5;
}

h1 {
    font-size: 50px;
    font-weight: 700;
}

h2 {
    font-size: 36px;
    font-weight: 600;
}

h2>span {
    font-size: 18px;
    color: #212529;
    font-weight: 400;
    line-height: 24px;
    margin-top: 10px;
    display: block;
}

.button-style {
    background: #F5A623;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.section-space {
    padding: 70px 0;
}

/* header */
.container {
    max-width: 1280px;
    width: 100%;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
}

header {
    padding: 10px 15px;
}

header.container {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* banner */
.hero-banner {
    height: auto;
    background: url(/../yas/images/clp/generic_one/banner-img.jpg) no-repeat;
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-size: cover;
    padding: 40px 0;
    background-repeat: no-repeat;
    background-size: cover;
}

.hero-banner .banner-overlay {
    background: linear-gradient(90deg, rgba(10, 98, 199, 0.9) 25%, rgba(26, 127, 202, 0.4) 70%);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;

}

.hero-banner .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hero-banner .banner-left-content {
    max-width: 500px;
    width: 100%;
    color: white;
}



.hero-banner .banner-left-content h1 {
    font-size: 50px;
    line-height: 56px;
    font-weight: 700;
}

.hero-banner .banner-left-content .banner-liner {
    font-size: 28px;
    line-height: 28px;
    font-weight: 700;
    margin-top: 20px;
    color: #0D3F64;
    padding: 10px;
    width: 420px;
    position: relative;
    margin-bottom: 20px;
    background: url(/../yas/images/clp/generic_one/strip.png) no-repeat;
    height: 55px;
}

.banner-left-content h3 {
    font-size: 20px;
}

.banner-left-content ul {
    list-style: none;
    padding: 0;
    margin: 15px 0 0;
}

.banner-left-content ul li {
    position: relative;
    padding-left: 40px;
    margin-bottom: 10px;
    font-weight: 400;
    font-size: 16px;
}

.banner-left-content ul li .stream {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    background-color: #F5A623;
    border-radius: 2px;
}

.banner-left-content ul li .stream::after {
    content: '';
    position: absolute;
    left: 3px;
    top: 4px;
    width: 10px;
    height: 4px;
    border-left: 2px solid white;
    border-bottom: 2px solid white;
    transform: rotate(-45deg);
}

.hero-banner .banner-right-content {
    max-width: 368px;
    width: 100%;
    background-color: white;
    padding: 20px 24px;
    border-radius: 4px;
    margin: 0;
    z-index: 1;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.input-error {
    border-color: #F8382A !important;
}

/* Streams Offered */
.streams-offered {
    position: relative;
}

.streams-offered h2 {
    text-align: center;
}

.streams-offered-inner {
    display: flex;
    flex-wrap: wrap;
    column-gap: 20px;
    margin-top: 40px;
}

.streams-offered-inner .detail-block {
    max-width: 402px;
    height: 273px;
    width: 100%;
    position: relative;
    display: flex !important;
    align-items: center;
    justify-content: flex-start;
    margin: 0 10px;
}

.streams-offered-inner .detail-block .img-block {
    width: 100%;
}

.streams-offered-inner .detail-block .img-block img {
    width: 100%;
    vertical-align: bottom;
}

.streams-offered-inner .detail-block .text-block {
    font-size: 24px;
    line-height: 30px;
    font-weight: 500;
    color: #212529;
    position: absolute;
    background-color: #ffffffe3;
    width: 350px;
    height: 213px;
    border-radius: 20px;
    inset: auto 0 auto 0;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 20px;
    text-align: center;
    justify-content: center;
}

.streams-offered .explore-now-btn {
    text-align: center;
    margin-top: 30px;
}

.streams-offered .explore-now-btn>a {
    display: inline-block;
}

/* our partner */
.top-recruiters-section h2 {
    text-align: center;
}

.top-recruiters-slider {
    margin-top: 40px;
}

.top-recruiters-slider.slick-slider .slick-track {
    display: flex;
    align-items: center;
}

.top-recruiters-slider .recruiters-logo-block {
    margin: 0 20px;
    padding: 10px 20px;
}

.top-recruiters-slider .recruiters-logo-block img {
    width: 100%;
    height: 68px;
    object-fit: scale-down;
}

.top-recruiters-section .enquiry-now-button {
    text-align: center;
}

.top-recruiters-slider.slick-slider .slick-dots {
    bottom: -17px;
}

.top-recruiters-slider.slick-slider .slick-dots li {
    width: 10px;
    height: 10px;
    list-style: none;
}

.top-recruiters-slider.slick-slider .slick-dots li>button {
    width: 10px;
    height: 10px;
    background-color: #D8D8D8;
    border-radius: 100%;
}

.top-recruiters-slider.slick-slider .slick-dots li.slick-active>button {
    background-color: #F5A623;
}

.top-recruiters-slider.slick-slider .slick-dots li>button::before {
    display: none;
}

.section-image .slick-slide img {
    display: block;
    width: 90%;
    object-fit: cover;
    height: 173px;
    position: absolute;
    border-radius: 12px;
    z-index: 1;
    inset: auto 0 auto 0;
    margin: 10px auto;
}

.our-campus-section {
    padding-top: 70px;
    padding-bottom: 70px;
    background-size: cover;
    position: relative;
}

.campus-overlay {
    background-color: #F5F5F5;
    opacity: 0.9;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.our-campus-section h2 {
    text-align: center;
    color: #212529;
}

.our-campus-section h2>span {
    line-height: 24px;
    font-size: 18px;
    font-weight: 400;
    display: block;
    margin-top: 10px;
}

.our-campus-slider {
    margin-top: 50px;
    display: none;
}

.our-campus-slider.slick-slider, 
.streams-offered-inner.slick-slider {
    margin-bottom: 0;
}

.our-campus-slider.slick-slider .slick-dots {
    bottom: -35px;
}
.streams-offered-inner.slick-slider .slick-dots{
    bottom: -20px;
}

.our-campus-slider.slick-slider .slick-dots li, .streams-offered-inner.slick-slider .slick-dots li {
    width: 10px;
    height: 10px;
    list-style: none;
}

.our-campus-slider.slick-slider .slick-dots li>button, .streams-offered-inner.slick-slider .slick-dots li>button {
    width: 10px;
    height: 10px;
    background-color: #D8D8D8;
    border-radius: 100%;
}

.our-campus-slider.slick-slider .slick-dots li.slick-active>button, .streams-offered-inner.slick-slider .slick-dots li.slick-active>button {
    background-color: #F5A623;
}

.our-campus-slider.slick-slider .slick-dots li>button::before, .streams-offered-inner.slick-slider .slick-dots li>button::before {
    display: none;
}

.campus-card {
    width: 100%;
    position: relative;
    margin: 0 10px;
    transition: 0.5s;
    border-radius: 12px;
    background-color: #fff;
    ;
    border: 1px solid #F5A623;
    transition: 0.5s;
}

.campus-card:hover {
    background: #F5A623;
}

.campus-card .campus-img {
    border-radius: 12px;
    overflow: hidden;
    width: 100%;
    height: 190px;
}

.slick-slider .slick-list,
.slick-slider .slick-track {
    display: flex;
}

.campus-card .campus-card-text {
    font-weight: 500;
    font-size: 20px;
    line-height: 30px;
    text-align: center;
    padding: 15px 15px;
    color: #212529;
    border-radius: 100px;
    margin: 0 auto;
    max-width: 285px;
    width: 100%;
    /* display: flex; */
    height: 100%;
}

.location-img {
    width: 16px;
    height: 16px;
    position: relative;
    margin-right: 5px;
    display: inline-block;
}

.campus-card .campus-card-text img {
    width: 16px;
    height: 16px;
    position: relative;
    margin: 0;
    display: inline-block;
    vertical-align: middle;
    position: absolute;
    left: 0;
    top: 2px;
    filter: none
}

.our-campus-section .explore-life-button {
    text-align: center;
    margin-top: 10px;
}

.campus-card .whiteLocation {
    visibility: hidden;
}

.campus-card:hover .whiteLocation {
    visibility: visible;
}

.campus-card:hover .colorLocation {
    visibility: hidden;
}

/* About Jaipuria Institute of Management */
.about-insti-section {
    position: relative;
    background-image: linear-gradient(to right, #F5A62399, #FFFFFF);
}

.about-insti-inner {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
}

.about-insti-inner h2 {
    width: 100%;
    margin-bottom: 30px;
    text-align: center;
}

.about-insti-inner .about-insti-left {
    width: 57%;
    color: #212529;
}

.about-insti-inner .about-insti-left h2 {
    margin-bottom: 25px;
}

.about-insti-inner .about-insti-left>p {
    padding: 0;
    font-size: 24px;
    line-height: 36px;
    font-weight: 600;
    color: #212529;
}

.about-insti-inner .about-insti-left>p>span {
    color: #064388;
}

.about-insti-inner .about-insti-right {
    width: 33%
}

.about-insti-inner .about-insti-right button {
    display: flex;
    align-items: center;
    gap: 10px
}

/*--sticky button--*/
.stick-button {
    padding: 15px 0;
}

.stick-button p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.button-blue {
    background-color: #0D3F64;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.whiteDownloadIcon,
.redDownloadIcon {
    width: 19px;
    height: 18px;
    background-position: 233px -354px !important;
    vertical-align: text-bottom;
    margin-right: 4px;
}

.spriteIcon {
    display: inline-block !important;
    background-image: url(../../images/master_sprite.webp);
    text-align: left;
    overflow: hidden;
}

.phoneIcon {
    width: 24px;
    height: 24px;
    background-position: 536px -246px;
    vertical-align: bottom;
    margin-right: 2px;
}

.stick-button .button-style {
    background-color: #F5A623;
}


#sendCallerLeadToCld {
    display: none !important;
}

/* footer */
footer {
    background-color: #212529;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    line-height: 54px;
}

/* for modal */
.overflow-hide {
    overflow: hidden;
}

.modal-overlay-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1;
    display: none;
    align-items: center;
    overflow-y: auto;
}

.modal-body {
    padding: 0;
    margin: auto;
    height: auto;
    max-width: 370px;
    width: 100%;
    background: white;
    border-radius: 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, .4);
    position: relative;
    z-index: 300;
    overflow-x: auto;
    padding: 25px;
    border-radius: 5px;
}

.modal-body .modal-head {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    color: #1E1E1E;
}

.modal-body .modal-head>span {
    font-size: 16px;
    font-weight: 400;
    display: block;
}

.modal-body .form-content {
    margin-top: 20px;
}

.modal-body .form-content .form-field-block {
    position: relative;
}

.modal-body .form-content .form-field-block .form-error {
    position: absolute;
    display: block;
    width: 100%;
    bottom: 0;
    left: 0;
    font-size: 11px;
    color: #F8382A;
}

.modal-body .form-content .field-style {
    border: 1px solid #ADB5BD;
    border-radius: 8px;
    width: 100%;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    padding: 12px 15px;
}

.modal-body .form-content .field-style:focus {
    border-color: #F5A623;
}

.modal-body .form-content .field-style:focus-visible {
    outline: none;
}

.modal-body .form-content .field-style.select-arrow {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url(/../yas/images/clp/generic_one/select-arrow.png) no-repeat right 10px top 21px;
}

.modal-button {
    font-size: 18px;
    font-weight: 600;
    background-color: #F5A623;
    width: 100%;
    border-radius: 8px;
    padding: 10px 0;
    border: none;
    cursor: pointer;
    color: white;
    margin-top: 5px;
}

#modal-close {
    font-size: 28px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    line-height: 10px;
    font-weight: 300;
}

.modal-overlay-box.show-modal {
    display: flex;
}

.know-more-btn {
    width: 100%;
    text-align: center;
    margin-top: 50px;
}

.page-header,
.pageFooter,
.scrollToTop {
    display: none !important;
}

.select2-container {
    margin-bottom: 16px !important;
}

.select2-container .select2-selection--single {
    height: 48px !important
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding: 8px 20px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 12px !important;
    right: 12px !important;
}

#modalFormClp .banner-form-inner .select2-container {
    width: 100% !important;
}

.select2-container--default .select2-selection--single {
    border-radius: 8px !important;
}

.custom-select-box {
    position: relative;
}

.clpCourse {
    background-color: #fff;
    ;
    position: relative;
    appearance: none;
}

.custom-select-box::after {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #888;
    right: 14px;
    top: 20px;
}

#genericScreenSubmit:disabled,
#modalScreenSubmit:disabled {
    background-color: rgba(202, 170, 25, 0.4) !important;
}

.banner-form-inner .select2 {
    margin-bottom: 15px;
    width: 100% !important;
}


.our-partner-slider {
    margin-top: 30px;
    display: none;
}

.our-partner-section h2 {
    margin-bottom: 30px;
    text-align: center;
}

.our-partner-slider .slick-slide img {
    width: 100px !important;
    height: 100px !important;
}

.our-partner-section .talk-expert-button {
    text-align: center;
    margin-top: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    font-size: 14px !important;
}

.form-subheading-generic {
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    display: block;
}

.hero-banner .banner-left-content h1>span {
    color: #F5A623;
}

/* media start here */
@media only screen and (max-width:992px) {
    .hero-banner .container {
        column-gap: 40px;
    }

    .hero-banner .banner-left-content {
        max-width: 100%;
        width: calc(100% - 360px);
    }

    .hero-banner .banner-left-content h1 {
        font-size: 40px;
    }

    .hero-banner .banner-left-content .top-college>span {
        font-size: 24px;
        width: 100%;
        background-size: contain;
    }

    .hero-banner .banner-right-content {
        max-width: 100%;
        width: 333px;
    }

    .about-insti-inner {
        flex-direction: column;
    }

    .about-insti-inner .about-insti-left,
    .about-insti-inner .about-insti-right {
        width: 100%;
    }

    .about-insti-inner .about-insti-left {
        order: 1;
        border-top-left-radius: 0;
        border-bottom-right-radius: 30px;
        text-align: center;
    }

    .about-insti-inner .about-insti-right {
        order: 2;
        justify-content: center;
        display: flex;
        margin-top: 20px;
        height: 45px;
    }

    .college-usps-inner {
        flex-wrap: wrap;
        gap: 15px;
    }

    .college-usps-inner .usps-card {
        width: calc(50% - 9px)
    }

    .streams-offered-inner .detail-block .text-block {
        width: auto;
    }
}

@media only screen and (max-width:767px) {
    .campus-card .campus-card-text {
        padding: 10px;
        font-size: 18px;
    }

    header {
        padding: 10px 15px;
    }

    h2 {
        font-size: 30px;
    }

    .headingSpace {
        /* padding-top: 120px; */
    }

    .streams-offered-inner .detail-block {
        max-width: 100%;
        height: auto;
    }

    .streams-offered-inner .detail-block .img-block img {
        width: 100%;
    }

    header>a>img:first-child {
        width: 148px;
        height: auto;
        display: block;
    }

    header>a>img:last-child {
        width: 133px;
        height: auto;
        display: block;
    }

    .hero-banner {
        align-items: flex-start;
        height: auto;
        padding: 10px 0;
    }

    .hero-banner .container {
        flex-direction: column;
    }

    .hero-banner .banner-left-content {
        width: 100%;
        padding: 40px 10px 0;
    }

    .hero-banner .banner-left-content h1 {
        text-align: center;
        font-size: 36px;
        line-height: 40px;
    }

    .hero-banner .banner-left-content .banner-liner {
        font-size: 18px;
        text-align: left;
        text-decoration: none;
        background-size: 318px;
        line-height: 17px;
        width: 318px;
    }

    .hero-banner .banner-right-content {
        width: 100%;
        margin-top: 40px;
    }

    .streams-offered-inner {
        flex-direction: column;
        gap: 20px;
    }

    .streams-offered-inner .detail-block .text-block {
        font-size: 20px;
        width: 85%;
        height: 85%;
    }

    .college-usps-section {
        padding: 40px 0;
    }

    .college-usps-inner .usps-card {
        width: 100%;
        height: 180px;
    }

    .our-campus-section {
        padding: 40px 0;
    }

    .our-campus-slider.slick-slider {
        margin-top: 20px;
    }

    .our-campus-slider .slick-list {
        padding: 0 10% 0 0;
    }

    .about-insti-inner .about-insti-left h2 {
        text-align: center;
        margin-bottom: 15px;
    }

    .about-insti-inner .about-insti-left {
        padding: 15px 0 0;
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px;
        text-align: center;
    }

    .know-more-btn {
        margin-top: 30px;
    }

    .top-recruiters-slider .recruiters-logo-block {
        margin: 0 10px;
        padding: 10px;
    }

    .streams-offered {
        padding-bottom: 40px;
    }

    .section-space {
        padding: 40px 0;
    }

    .about-insti-section {
        padding: 20px 0 40px;
    }

    /*--sticky button--*/
    .stick-button span {
        display: none;
    }

    .stick-button {
        padding: 0;
    }

    .stick-button p {
        gap: 0;
    }

    .stick-button .button-style,
    .stick-button .button-blue {
        border-radius: 0;
        height: 45px;
        font-size: 14px;
    }

    .stick-button .button-style {
        width: 60%;
    }

    .stick-button .button-blue {
        width: calc(100% - 60%);
    }
}