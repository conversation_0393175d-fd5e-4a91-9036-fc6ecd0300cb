  /******************************************************************/
  /*********************** Location*****************************/
  /******************************************************************/

  .gis__ourOffices .mapTab {
      padding: 0px;
  }

  .gis__ourOffices .mapTab .selectedCityMap {
      display: none;
      height: 100%;
  }

  .gis__ourOffices .mapTab .selectedCityMap iframe {
      height: 100% !important;
  }

  .gis__ourOffices .mapTab .selectedCityMap iframe .review-box {
      display: none;
  }

  .gis__ourOffices .mapTab .selectedCity {
      display: block;
  }

  .gis__ourOffices .locationTab {
      padding: 0;
      max-width: 552px;
      background-color: #fff;
      border-radius: 8px;
  }

  .gis__ourOffices .locationTab h2 {
      font-size: 24px;
      font-weight: 500;
      line-height: 20px;
      color: #282828;
      margin-bottom: 20px;
  }

  .gis__ourOffices .locationTab .locationTabCities {
      list-style-type: none;
      display: flex;
      row-gap: 12px;
      column-gap: 10px;
      padding: 0;
      margin: 0;
      flex-wrap: wrap;
      padding-bottom: 20px;
      border-bottom: 1px solid #d8d8d8;
      margin-bottom: 20px;
  }

  .gis__ourOffices .locationTab .locationTabCities li {
      flex-basis: 48%;
      flex-grow: 1;
      max-width: 235px;
      border-radius: 4px;
      border: solid 1px #d8d8d8;
      background-color: #fff;
      padding: 6px 16px;
      position: relative;
  }

  .gis__ourOffices .locationTab .locationTabCities li label {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      text-align: left;
      color: #282828;
  }

  .gis__ourOffices .locationTab .locationTabCities li input {
      opacity: 0;
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      margin: 0;
  }

  .gis__ourOffices .locationTab .locationTabCities li:has(input:checked) {
      border-left: 4px solid #ff615d;
  }

  .gis__ourOffices .locationTab .locationTabCities li input:checked+label {
      font-weight: 600;
      color: #ff615d;
  }

  .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress {
      display: none;
  }

  .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress h3 {
      margin-bottom: 10px;
  }

  .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress .col-md-6 {
      font-size: 16px;
      line-height: 1.5;
  }

  .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress .locationPhone {
      /* margin-bottom: 10px; */
  }

  .gis__ourOffices .locationTab .selectedCityArea .selectedCity {
      display: block;
  }

  .gis__ourOffices {
      padding: 80px 0px;
      margin-bottom: -18px;
  }

  @media (max-width: 1023px) {
      .gis__ourOffices .mapTab .selectedCityMap iframe {
          width: 100%;
          min-height: 255px;
      }

      .gis__ourOffices .locationTab {
          margin: 20px 0px;
      }

      .gis__ourOffices .locationTab h2 {
          font-size: 15px;
          font-weight: 500;
      }

      .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress h3 {
          font-size: 14px;
          font-weight: 500;
      }

      .gis__ourOffices .locationTab .selectedCityArea .cityLocationAddress .gis__ourOffices__mobileDivision {
          flex: 0 0 50%;
          max-width: 50%;
          font-size: 10px;
      }

      .gis__ourOffices .locationTab .locationTabCities {
          padding-bottom: 14px;
          margin-bottom: 14px;
      }

      .gis__ourOffices .locationTab .locationTabCities li {
          max-width: 48%;
          height: 24px;
          padding: 4px 0 4px 10px;
          line-height: 15px;
          font-size: 10px;
      }

      .gis__ourOffices .locationTab .locationTabCities li label {
          line-height: 15px;
          font-size: 10px;
      }

      .gis__ourOffices {
          padding: 26px 16px 0 16px;
          margin-bottom: 0px;
      }
  }