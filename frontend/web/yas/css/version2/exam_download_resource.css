/*------------------examDownloaded----------------*/
.examDownload,
.examPdfonEmail {
    margin: 20px 0;
}

.view-more-btn {
    display: none;
}

.examDownload ul {
    margin: 0;
    list-style-type: none;
    padding: 0;
    position: relative;
    white-space: nowrap;
    border-bottom: 2px rgba(232, 232, 232, 1) solid;
    position: relative;
    overflow-x: auto;

}

.examDownload ul li.current::after {
    content: '';
    width: 83px;
    height: 3px;
    background-color: var(--color-red);
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.examDownload ul::-webkit-scrollbar {
    display: none;
}

.examDownload ul li {
    display: inline-block;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    color: rgba(120, 123, 126, 1);
    padding: 10px 27px;
    position: relative;

}

.view-less-icon {
    position: relative;
}

.view-less-icon:before,
.view-less-icon::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 8px;
    background-color: var(--anchor-textclr);
    right: -18px;
    transform: rotate(45deg);
    top: 4px;

}

.view-less-icon::after {
    transform: rotate(-45deg);
    right: -13px;
}

.examDownload ul li::before {
    content: "";
    position: absolute;
    height: 16px;
    width: 2px;
    background-color: rgba(219, 215, 215, 1);
    right: 0;
    top: 16px;
    left: auto !important;
}

.rotateIcon.view-less-icon::before,
.rotateIcon.view-less-icon::after {
    transform: rotate(135deg);
}

.rotateIcon.view-less-icon::after {
    transform: rotate(-135deg);
}

.examDownload ul li:last-child::before {
    display: none;
}

.examDownload ul li.current {
    color: var(--color-red);
    font-weight: 700;
}

.exam-res-inner-tab {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    white-space: nowrap;
    overflow-x: auto;
}

.exam-res-inner-tab button {
    border-radius: 3px;
    font-weight: 400;
    display: block;
    text-decoration: none;
    color: rgba(9, 102, 194, 1);
    font-size: 14px;
    line-height: 22px;
    padding: 4.55px 10px 4.55px 10px;
    background: rgba(238, 245, 254, 1);
    border: 0.5px solid rgba(68, 147, 242, 0.5);
    text-transform: capitalize;
    margin: 2px 4px;
}

.category-content {
    display: none;
    opacity: 0;
    transition: opacity .3s ease-in-out;
}

.category-content.activeCategory {
    display: block;
    opacity: 1;
}

.examDownload .customSlider {
    position: relative;
}

.examDownload .customSlider .customSliderCards {
    display: block;
    white-space: nowrap;
    overflow: auto;
    padding: 2px;
}

.examDownload .customSlider .customSliderCards::-webkit-scrollbar {
    display: none;
}

.examDownload .custom-cardDisplay .sliderCardInfo {
    display: inline-block;
    white-space: initial;
    border: none !important;
    margin-right: 16px;
    border-radius: 4px 0px 0px 0px;
    width: 232px;
    padding: 10px;
    border-radius: 9px;
    background-image: linear-gradient(rgba(255, 255, 255, 1), rgba(233, 242, 255, 1));
    box-shadow: 0 1px 10px rgba(0, 0, 0, .20);
}

.examDownload .custom-cardDisplay .sliderCardInfo button {
    width: 100%;
    background-color: var(--topheader-bg);
    border: 1px solid rgba(9, 102, 194, 1);
    border-radius: 4px;
    font-weight: 500;
    font-size: 18px;
    height: 44px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 15px 0;
}

.examDownload .custom-cardDisplay .sliderCardInfo p {
    font-size: 13px;
    font-weight: 400;
    text-align: center;
}

.examDownload .custom-cardDisplay .sliderCardInfo p span {
    font-weight: 600;
    font-size: 14px;

}

.examDownload .custom-cardDisplay .sliderCardInfo button span {
    margin-right: 5px;
}

.examDownload .cardInfo {
    border: 1px solid rgba(13, 61, 99, 1);
    border-radius: 9px;
    height: 211px;
    position: relative;
}

.examDownload .cardInfo img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-top-left-radius: 9px;
    border-top-right-radius: 9px;
}

.getUnivSolution {
    position: absolute;
    width: 100%;
    height: 60px;
    background-color: #fff;
    bottom: 0;
    left: 0;
    border-bottom-left-radius: 9px;
    border-bottom-right-radius: 9px;
    color: rgba(9, 102, 194, 1) !important;
    font-size: 11px !important;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    padding: 6px;
    line-height: 17px !important;
}


.getUnivSolution span {
    font-weight: 600;
    font-size: 13px;
    display: block;
    text-align: center;
    width: 100%;
    color: rgba(9, 102, 194, 1) !important;
}

.examPdfonEmail p {
    font-size: 16px;
    color: rgba(120, 123, 126, 1);
    line-height: 20px;
}

.examRow {
    display: flex;
    gap: 30px;
    align-items: center;
    justify-content: center;
}

.examPdfonEmail button {
    background: var(--color-red);
    width: 296px;
    height: 51px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0;
    border-radius: 4px;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    color: #fff;
}

.examPdfonEmail button svg {
    margin-right: 10px;
}

.updownArrow {
    background-position: 66px -2342px !important;
}

.getDownload {
    background-position: 37px -2339px !important;
    width: 22px !important;
    height: 26px !important;
    margin-left: 0;
    margin-right: 5px;
}

.category-btn {
    box-sizing: border-box;
}

.category-btn.active {
    outline: 1px solid #4493F2;
}

/*------------------examDownloaded----------------*/

@media (max-width: 1023px) {
    .examRow {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .examDownload .custom-cardDisplay .sliderCardInfo {
        width: 160px;
        margin-right: 5px;
        padding: 5px;
    }

    .examDownload .cardInfo {
        height: 236px;
    }

    .getUnivSolution {
        font-size: 11px;
        padding: 8px;
        height: 76px;
    }

    .examDownload .custom-cardDisplay .sliderCardInfo button {
        font-size: 14px;
        margin: 10px 0;
    }

    .examDownload .custom-cardDisplay .sliderCardInfo p span {
        font-weight: 600;
        font-size: 13px;
    }

    .examDownload .custom-cardDisplay .sliderCardInfo p {
        font-size: 10px;
    }

    .examDownload h2 {
        font-size: 14px;
    }

    .exam-res-inner-tab {
        display: flex;
        margin: 10px 0 0;
        white-space: normal;
        flex-wrap: wrap;
        gap: 7px;
        height: 71px;
        overflow: hidden;
    }

    .exam-res-inner-tab button {
        font-size: 12px;
        padding: 1.55px 10px 1.55px 10px;
        margin: 1px;
        height: 30px;
    }

    .expanded {
        height: auto;
    }

    .view-more-btn {
        display: block;
        font-size: 12px;
        font-weight: 400;
        line-height: 22px;
        color: var(--anchor-textclr);
        margin: 10px 0;
    }

    .examDownload ul::before {
        width: 110px;
    }

    .getUnivSolution span {
        font-size: 10px;
    }

    .examDownload .customSlider {
        margin-top: 10px;
    }
}