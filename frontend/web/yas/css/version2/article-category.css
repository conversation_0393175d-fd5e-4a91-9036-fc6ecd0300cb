.pageHeading {
  padding: 20px 0;
}

.pageHeading h1 {
  font-weight: normal;
  font-size: 24px;
  line-height: 38px;
}

.catgegoryArticle {
  border-radius: 4px;
  box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  margin-bottom: 20px;
}

.catgegoryArticle .row {
  margin: 0;
}

.catgegoryArticle .articleBanner {
  flex-basis: 309px;
  display: flex;
  align-items: center;
  border-right: var(--border-line);
}

.catgegoryArticle .articleBanner img {
  max-width: 309px;
  max-height: 232px;
  display: block;
  cursor: pointer;
}

.catgegoryArticle .articleText {
  flex-basis: calc(100% - 310px);
  padding: 20px;
}

.catgegoryArticle .articleText h2 {
  font-size: 16px;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  margin-bottom: 10px;
  font-weight: 500;
}

.catgegoryArticle .articleText h2 a {
  text-decoration: none;
  color: var(--primary-font-color);
}

.catgegoryArticle .articleText h2 a:hover {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.catgegoryArticle .articleText p {
  font-size: 15px;
  line-height: 26px;
  color: #787878;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 3;
  margin-bottom: 20px;
}

.catgegoryArticle .updated-info {
  font-size: 14px;
  line-height: 20px;
}

.catgegoryArticle .updated-info.row {
  -webkit-box-align: center;
          align-items: center;
}

.catgegoryArticle .updated-info .updatedBy {
  padding-right: 5px;
}

.catgegoryArticle .updated-info .updatedBy img {
  width: 36px;
  height: 36px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
  border-radius: 50%;
}

.catgegoryArticle .updated-info .updatedBy .authorName {
  font-weight: var(--font-semibold);
  /* cursor: pointer; */
  display: inline-block;
  vertical-align: middle;
}

.catgegoryArticle .updated-info p {
  margin-bottom: 0;
  line-height: 20px;
}

.registerLatestArticle {
  border-radius: 4px;
  box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  margin-bottom: 20px;
}

.registerLatestArticle .row {
  -webkit-box-align: center;
          align-items: center;
  flex-wrap: nowrap;
  margin: 0;
}

.registerLatestArticle img {
  max-width: 84px;
}

.registerLatestArticle p {
  font-size: 18px;
  line-height: 26px;
  margin-left: 20px;
}

.registerLatestArticle .registerNow {
  display: block;
  width: 100%;
  margin-top: 20px;
  font-weight: var(--font-semibold);
}

.getSupport {
  padding: 19px;
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 3px;
  margin-bottom: 20px;
}

.getSupport .row {
  margin: 0;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}

.getSupport img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}

.getSupport p {
  font-size: 18px;
  line-height: 26px;
}

.getSupport button {
  width: 161px;
  border-radius: 3px;
  font-size: 14px;
  line-height: 24px;
  padding: 6px;
  text-align: center;
  color: var(--color-white);
  font-weight: var(--font-bold);
  border: none;
}

.getSupport button.talkToExpert {
  background: var(--topheader-bg);
}

.getSupport button.applyNow {
  background: var(--color-red);
  margin-left: 0px;
  width: 172px;
}

.articleSidebarSection {
  border-radius: 4px;
  box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
  margin-bottom: 20px;
}

.articleSidebarSection .tab-content.activeLink {
  display: block;
}

.articleSidebarSection ul {
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: flex;
}

.articleSidebarSection ul li {
  list-style-type: none;
  flex-basis: 50%;
  text-align: center;
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  cursor: pointer;
  padding: 12px 5px;
  padding-bottom: 9px;
  border-bottom: var(--border-line);
  font-weight: var(--font-semibold);
}

.articleSidebarSection ul li.activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
}

.trendingArtilerList,
.recentArticlesList {
  max-height: 490px;
  overflow: auto;
  padding: 20px;
}

.trendingArtilerList::-webkit-scrollbar-thumb,
.recentArticlesList::-webkit-scrollbar-thumb {
  background: #ccc;
}

.trendingArtilerList::-webkit-scrollbar,
.recentArticlesList::-webkit-scrollbar {
  width: 5px;
}

.trendingArtilerDiv.row,
.recentArticlesDiv.row {
  margin: 0;
  flex-wrap: nowrap;
  margin-bottom: 10px;
  border-bottom: var(--border-line);
  padding-bottom: 10px;
  -webkit-box-align: center;
          align-items: center;
}
.recentArticlesDiv.row:hover h3, 
.trendingArtilerDiv.row:hover h3{
  color: var(--anchor-textclr);
}
.listCard:last-child .trendingArtilerDiv.row,
.listCard:last-child .recentArticlesDiv.row {
  margin-bottom: 0;
  border: none;
  padding: 0;
}

.trendingArtilerDiv img,
.recentArticlesDiv img {
  width: 96px;
  max-height: 72px;
  display: block;
  align-self: center;
}
.sidebarImgDiv{
  flex-basis: 96px;
  margin-right: 16px;
  display: grid;
  min-height: 72px;
}
.trendingArtileText, .recentArticlesDivText{
  flex-basis: calc(100% - 96px - 16px);
}

.trendingArtilerDiv .trendingArtileText .sidebarTextLink,
.trendingArtilerDiv .recentArticlesDivText .sidebarTextLink,
.recentArticlesDiv .trendingArtileText .sidebarTextLink,
.recentArticlesDiv .recentArticlesDivText .sidebarTextLink,
.trendingArtilerDiv .trendingArtileText h3,
.trendingArtilerDiv .recentArticlesDivText h3,
.recentArticlesDiv .trendingArtileText h3,
.recentArticlesDiv .recentArticlesDivText h3{
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: 500;
  text-decoration: none;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  padding: 0;
  font-weight: normal;
}

.trendingArtilerDiv .trendingArtileText a:hover,
.trendingArtilerDiv .recentArticlesDivText a:hover,
.recentArticlesDiv .trendingArtileText a:hover,
.recentArticlesDiv .recentArticlesDivText a:hover {
  text-decoration: underline;
  color: var(--anchor-textclr);
}
.trendingArtilerDiv.row:hover .sidebarTextLink, 
.recentArticlesDiv.row:hover .sidebarTextLink,
 .recentnewsDiv.row:hover .sidebarTextLink, 
 .trendingArtilerDiv.row:hover a, 
 .recentArticlesDiv.row:hover a,
 .recentnewsDiv.row:hover a{
  text-decoration: underline;
  color: var(--anchor-textclr);
}

.pageFooter {
  padding-bottom: 0;
}


/* theme css  */
.pageHeading {
  padding-top: 10px;
}
.catgegoryArticle,
.registerLatestArticle,
.registerLatestArticle,
.articleSidebarSection {
  box-shadow: none;
  border: var(--border-line);
  background: var(--color-white);
}
.examTypeDiv,
.pagination li,
ul.pagination li {
  background: var(--color-white);
}
.pagination li.active,
ul.pagination li.active a {
  background: var(--color-red);
}
.author-link{
  color:var(--primary-font-color) !important;
}
@media (max-width: 1023px) {
  .pageHeading {
    margin-top: -160px;
    border-radius: 4px;
    box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    margin-bottom: 10px;
  }

  .pageHeading h1 {
    font-size: 18px;
    line-height: 28px;
  }

  .categoryArticlesList {
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
  }

  .catgegoryArticle {
    box-shadow: none;
    border: var(--border-line);
    margin-bottom: 10px;
  }

  .catgegoryArticle .row {
    display: block;
  }

  .catgegoryArticle .articleBanner img {
    max-width: 100%;
    width: 100%;
    cursor: pointer;
  }

  .catgegoryArticle .articleText {
    padding: 10px;
  }

  .catgegoryArticle .articleText h2 {
    font-size: 15px;
    line-height: 25px;
    -webkit-line-clamp: 3;
  }

  .catgegoryArticle .articleText p {
    font-size: 14px;
    line-height: 24px;
    -webkit-line-clamp: 5;
    margin-bottom: 10px;
  }

  .catgegoryArticle .updated-info.row {
    display: -webkit-box;
    display: flex;
  }

  .catgegoryArticle .updated-info p {
    margin-bottom: 0px;
  }

  .registerLatestArticle {
    display: none;
  }

  .setAlarmDiv {
    position: fixed;
    bottom: 0;
    left: 0;
    margin: 0;
    z-index: 2;
    padding: 0;
    width: 100%;
  }

  /* thtme css  */
  .categoryArticlesList {
    box-shadow: none;
    border: var(--border-line);
    background: var(--color-white);
  }
  .catgegoryArticle .articleBanner{
    border-right: 0px;
    border-bottom: var(--border-line);
    justify-content: center;
  }
  
  .getSupport {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 10px;
    border-radius: 0px;
    z-index: 1;
  }

  .getSupport .row {
    display: none;
  }

  .getSupport button {
    width: 49%;
    border-radius: 2px;
    font-size: 13px;
    padding: 6px 4px;
  }

  .getSupport button.applyNow {
    margin-left: 0;
    width: 162px;
  }
}

