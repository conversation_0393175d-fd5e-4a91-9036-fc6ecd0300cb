body,
.breadcrumbDiv {
  background: #f3f2ef;
}

.arrowIcon {
  width: 19px;
  height: 21px;
  background-position: 412px -83px;
  vertical-align: middle;
}

.bannerDiv,
.articelNote,
.articleInfo,
.faq_section,
.contentProvider,
.commentSection,
.relatedArticles,
.articleSidebarSection,
.registerLatestArticle,
.newsSidebarSection {
  box-shadow: none;
  background: var(--color-white);
  border: var(--border-line);
  overflow-wrap: break-word;
}

.articelNote {
  border-left: 6px solid var(--color-red);
}

.articleCardInfo {
  background: #f0f8ff;
}

.tabelOfContent {
  background: #f0f8ff;
}

.tabelOfContent h2 {
  background: transparent;
}

.latestArticleSection .viewAll {
  -webkit-box-pack: center;
  justify-content: center;
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  min-height: 48px;
  font-size: 14px;
  line-height: 24px;
}

.latestArticleSection .viewAll.hoverbg {
  border-left: 0px;
}

.latestArticleSection .viewAll a {
  color: #ff4e53;
  text-decoration: none;
  font-weight: var(--font-semibold);
}

.latestArticleSection,
.latestInfoSection {
  box-shadow: none;
  border: var(--border-line);
}

.catgegoryArticle,
.registerLatestArticle,
.articleSidebarSection {
  box-shadow: none;
  background: var(--color-white);
  border: var(--border-line);
}

.catgegoryArticle .updated-info .updatedBy img {
  border-radius: 50%;
}

.newsSidebarSection h2 {
  font-size: 14px;
  line-height: 24px;
  margin: 0;
  background: #f5f5f5;
  text-transform: uppercase;
  padding: 12px 19px;
  border-bottom: var(--border-line);
}

.articleList .articleTxt h3 {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-500);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 58px;
  margin-bottom: 10px;
  padding: 0;
  text-decoration: none;
}

.articleAuthorName {
  padding: 16px;
  padding-top: 0;
  display: -webkit-box;
  font-size: 14px;
  line-height: 24px;
  color: #989898;
  margin: 0;
  font-weight: 400;
}

.articleDiv:hover a {
  text-decoration: none;
}

.articleDiv:hover h3 {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.articleTxt p:hover {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.articleTxt p:last-child:hover {
  text-decoration: none;
}

:root {
  --border-line: 1px solid #d8d8d8;
}

.topHeader {
  background: #0966c2;
}

.tagsDiv ul li a {
  border: var(--border-line);
}

.tagsDiv ul li a:hover {
  border: 1px solid var(--anchor-textclr);
}

.pagination li,
.ul.pagination li {
  vertical-align: middle;
}

.pagination li a,
.ul.pagination li a {
  background: var(--color-white);
  color: #989898;
}

.pagination li.active a,
.ul.pagination li.active a {
  background: var(--color-red);
}

.recentnewsDivText {
  flex-basis: calc(100% - 96px - 16px);
}

.pageHeading {
  padding-top: 10px;
}

.catgegoryArticle .articleBanner img,
.articleDisplay figure {
  border-right: var(--border-line);
}

.latestInfoSection .latestInfoDiv figure,
.articlesByCategory .latestInfoDiv img,
.articleList .articleDiv figure {
  border-bottom: var(--border-line);
}

.latestInfoSection .latestInfoDiv img,
.articlesByCategory .latestInfoDiv img {
  width: auto;
  margin: 0 auto;
}

.articleList .articleDiv img {
  height: 207px;
}

.trendingArtilerDiv .trendingArtileText .sidebarTextLink,
.recentnewsDiv .recentnewsDivText .sidebarTextLink,
.recentArticlesDiv .recentArticlesDivText .sidebarTextLink {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: 500;
  text-decoration: none;
  display: -webkit-box;
  font-weight: 400;
  padding: 0;
  /* -webkit-line-clamp: 3; */
  -webkit-box-orient: vertical;
  overflow: hidden;
  border: none;
}

.trendingArtilerList,
.recentArticlesList,
.recentnewsList {
  max-height: 100%;
  overflow: auto;
}

.categoryArticlesContainer {
  margin-top: 10px;
}

.applyForFreeScholarship {
  background: #273553;
  text-align: center;
  padding: 9px;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 2;
  width: 100%;
}

.applyForFreeScholarship p {
  font-size: 15px;
  line-height: 26px;
  color: var(--color-white);
}

.applyForFreeScholarship p span {
  font-weight: var(--font-semibold);
  color: var(--color-white);
}

.applyForFreeScholarship p .primaryBtn {
  padding: 5px 7px;
  margin-left: 20px;
}

.latestArticleSection .articleList ul li {
  min-height: 69px;
}

.latestArticleSection .aticleInfo img.gifLive,
.bannerDiv img.gifLive {
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
  max-width: 72px;
  height: 32px;
}

.articleList img.gifLive,
.sidebarTextLink img.gifLive {
  max-width: 37px;
  height: 15px;
  margin-right: 5px;
  display: inline-block;
  vertical-align: middle;
}

.liveUpdateSection .whiteDot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--color-white);
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
}

.liveUpdateSection .liveUpdateHeading {
  background: var(--color-red);
  color: var(--color-white);
  padding: 8px 20px;
  font-size: 18px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 20px;
}

.latestNewsList {
  position: relative;
  padding-left: 22px;
}

.latestNewsList:before {
  content: "";
  position: absolute;
  width: 1px;
  height: calc(100% - 15px);
  transform: translate(0px, -10px);
  left: 8px;
  bottom: 0;
  border-left: 1.2px dashed var(--color-red);
}

.lastestNewsDiv {
  padding-bottom: 10px;
  border-bottom: var(--border-line);
  margin-bottom: 20px;
  position: relative;
}

.lastestNewsDiv:last-child {
  margin-bottom: 0px;
  padding-bottom: 0px;
}

.lastestNewsDiv .newsUpdatedTime {
  position: relative;
}

.lastestNewsDiv .newsUpdatedTime:before {
  content: "";
  position: absolute;
  width: 18px;
  height: 18px;
  top: 3px;
  left: -22px;
  z-index: 1;
  background-image: url(/yas/images/master_sprite.webp?master_sprite.webp?b5965618277cb72aa2882736b150fec5);
  background-position: 593px -400px;
  background-color: var(--color-white);
}

.globalHelloBar {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 0);
  width: 1206px;
  max-width: 1026px;
  height: 90px;
  z-index: 10;
  background: #EAEAEA;
  display: none;
}

.globalHelloBar:before {
  content: "-------- Advertisement --------";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 11px;
  text-align: center;
  line-height: 10px;
  color: #b7b7b7;
}

.globalHelloBar .closeHelloBar {
  position: absolute;
  width: 17px;
  height: 17px;
  background-position: 653px -335px;
  right: 0;
  top: 0;
  cursor: pointer;
  z-index: 10;
}

.page-header {
  height: 60px;
}

.topSearcheSection {
  background-color: var(--color-white);
  padding: 8px 0;
  margin-bottom: 30px;
}

.topSearcheSection .row {
  align-items: center;
}

.topSearcheSection .topsearchtext {
  margin-right: 20px;
  font-size: 18px;
  font-size: 14px;
  font-weight: bold;
  line-height: 20px;
  color: #0966c2;

}

.topSearchDiv {
  flex-basis: calc(100% - 135px);
  max-width: calc(100% - 135px);
  position: relative;
  overflow: hidden;
}

.topSearchDiv ul.scrolling-list {
  margin: 0;
  padding: 0;
  display: block;
  white-space: nowrap;
  overflow-x: auto;
}

@keyframes scroll-horizontal-left-topSearchDiv {
  0% {
    left: 1%;
  }

  100% {
    left: -100%;
  }
}

@keyframes scroll-horizontal-transform-topSearchDiv {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-600px); /* Scroll to the left by the full width of the list */
  }
}

.scrolling-list {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrolling-list::-webkit-scrollbar {
  display: none;
}

.topSearchDiv ul::-webkit-scrollbar {
  display: none;
}

.topSearchDiv ul li {
  display: inline-block;
  white-space: initial;
  padding: 0 5px;
  animation: var(--animation-type) 15s linear infinite;
  position: relative;
}

.topSearchDiv ul li:first-child {
  padding-left: 0px;
}

.topSearchDiv ul li a {
  font-size: 14px;
  line-height: 20px;
  display: block;
  color: var(--primary-font-color);
  font-weight: normal;
}

.topSearchDiv ul li a:hover {
  color: var(--anchor-textclr);
}

.knowMoreSection {
  background: linear-gradient(90deg, #FFFFFF 57%, #FFFBEC 100%);
}

.knowMoreSection .cardTitle {
  font-weight: var(--font-500);
}

.knowMoreSection ul {
  margin: 0;
  padding-left: 20px;
}

.knowMoreSection ul li {
  display: inline-block;
  margin-right: 35px;
}

.knowMoreSection ul li a {
  display: inline-block;
}

.fweight-500 {
  font-weight: 500;
}

.nextStorySection {
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
  background: var(--color-white);
}

.nextStorySection .row {
  margin: 0px;
  align-items: center;
}

.nextStorySection .imgDiv {
  flex-basis: 120px;
  margin-right: 20px;
}

.nextStorySection .imgDiv img {
  border-radius: 4px;
  width: 100%;
  height: 95px;
  display: block;
}

.nextStorySection .textDiv {
  flex-basis: calc(100% - 140px);
}

.nextStorySection p {
  font-size: 15px;
  line-height: 28px;
}

.nextStorySection p a,
.nextStorySection .clickLink {
  color: var(--color-red);
  font-weight: 500;
  cursor: pointer;
}

.nextStorySection .clickLink:hover {
  text-decoration: underline;
}

.nextStorySection .nextStoryHeading {
  line-height: 38px;
  padding-bottom: 10px;
  font-weight: 500;
  color: var(--primary-font-color);
}

.nextStorySection .nextStoryHeading:hover {
  text-decoration: underline;
  color: var(--anchor-textclr);
  cursor: pointer;
}

/* banner section new design  */

.latestArticleSection .aticleInfo img.gifLive,
.bannerDiv img.gifLive {
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
  max-width: 72px;
  height: 32px;
}

.latestArticleSection .aticleInfo img.gifLive,
.bannerDiv img.gifLive {
  display: block;
  margin: 0;
  height: 30px;
}

.latestArticleSection.row {
  height: auto;
}

.latestArticleSection .article1-view {
  position: relative;
}

.latestArticleSection .articleDisplay {
  flex-basis: calc(100% - 735px);
  height: 270px;
  border-right: 0;
  position: relative;
}

.latestArticleSection .articleDisplay figure {
  border: none;
  max-height: 270px;
}

.latestArticleSection .articleDisplay:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, #282828 56%);
  border-radius: 4px;
}

.latestArticleSection .articleDisplay .aticleInfo {
  padding: 20px;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 1;
}

.latestArticleSection .articleDisplay .aticleInfo h2 {
  margin-bottom: 10px;
  -webkit-line-clamp: 3;
}

.latestArticleSection .articleDisplay .aticleInfo h2 a,
.latestArticleSection .articleDisplay .aticleInfo .updated-info p {
  color: var(--color-white);
}

.latestArticleSection img {
  height: 270px;
}

.latestArticleSection .articleList {
  flex-basis: 735px;
  padding: 21px 31px;
  position: relative;
}

.latestArticleSection .articleList ul li {
  height: 57px;
  padding: 0;
  min-height: 57px;
  border: none;
}

.latestArticleSection .articleList ul li img {
  width: 95px;
  height: 45px;
  display: inline-block;
  border-radius: 0px;
}

.latestArticleSection .articleList ul li img.gifLive {
  height: 15px;
  display: block;
}

.latestArticleSection .articleList ul li a {
  display: flex;
  width: 100%;
  align-items: center;
  max-height: 57px;
}

.latestArticleSection .articleList ul li a:hover .articleName {
  color: var(--anchor-textclr);
}

.latestArticleSection .articleList ul li .articleName {
  padding-left: 10px;
  flex-basis: calc(100% - 155px);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  align-items: center;
  line-height: 20px;
}

.latestArticleSection .articleList ul li .articleName img.gifLive {
  width: auto;
}

.latestArticleSection .viewAll {
  position: absolute;
  right: 16px;
  height: auto;
  display: block;
  bottom: 16px;
  min-height: auto;
}

.latestArticleSection .viewAll a {
  color: var(--color-red);
  font-weight: 600;
}

.latestInfoList.row {
  flex-wrap: nowrap;
  overflow: auto;
}

.latestInfoList.row::-webkit-scrollbar {
  display: none;
}

.latestInfoList .latestInfoDiv .viewAllDiv {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.latestInfoList .latestInfoDiv .viewAllDiv a {
  font-size: 14px;
  line-height: 24px;
  color: var(--color-red);
  text-align: center;
  font-weight: 600;
}

.latestInfoListContainer {
  position: relative;
}

.latestInfoListContainer .scrollLeft {
  top: 50%;
  left: -20px;
}

.latestInfoListContainer .scrollRight {
  right: -20px;
}

.latestInfoSection .latestInfoDiv {
  min-width: 23.7%;
}

.latestInfoSection .latestInfoDiv:nth-of-type(4n) {
  margin-right: 20px;
}

.latestInfoSection .latestInfoDiv:last-child {
  margin-right: 0;
}


/* banner section new design ends  */
.shareSection a {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
}

.shareSection a p {
  margin: 6px 10px 6px 0;
  font-size: 15px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.33;
  letter-spacing: 0.3px;
  text-align: left;
  color: #282828;
}

.shareSection a p span {
  color: #3d8ff2;
}

.latestInfoSection .latestInfoTxt p {
  color: black !important;
}

.articleInfo .readMoreNewsDetail {
  color: #e03e2d;
}

.articleInfo a span {
  color: #3d8ff2
}

.headerCTAPair {
  display: flex;
  /*padding: 10px*/;
  background-color: #fff;
 /* margin-bottom: 10px;*/
  position: sticky;
  top: 0px;
  z-index: 10;
}

.headerCTAPair button {
  flex: 1;
  border-radius: 3px;
  height: 36px;
  border: none;
  font-size: 13px;
  font-weight: bold;
  line-height: 1.71;
}

.headerCTAPair button:first-child {
  border: solid 1px #0966c2;
  background-color: #0966c2;
  margin-right: 5px;
  color: #fff;
}

.headerCTAPair button:last-child {
  background-color: #ff4e53;
  color: #fff;
}

.whiteDownloadIcon {
  width: 19px;
  height: 18px;
  background-position: 233px -353px;
  vertical-align: text-bottom;
  margin-left: 4px;
}


@media (max-width: 1023px) {
  .leadFormContainerNews {
    display: none;
  }

  .nextStorySection {
    padding: 10px;
  }

  .nextStorySection .nextStoryHeading {
    line-height: 24px;
    padding: 0;
  }

  .nextStorySection .row {
    padding-bottom: 10px;
  }

  .nextStorySection .fweight-500 {
    padding-bottom: 10px;
  }

  .nextStorySection .imgDiv {
    margin-right: 13px;
  }

  .nextStorySection .textDiv {
    flex-basis: calc(100% - 133px);
  }

  .globalHelloBar {
    width: 300px;
    max-width: 300px;
    height: 50px;
    bottom: 58px;
  }

  .breadcrumbDiv {
    background: #545ebd;
  }

  .latestArticleSection {
    border: none;
  }

  .latestArticleSection .articleDisplay {
    border-right: none;
  }

  .latestArticleSection .articleDisplay .mobileOnly {
    display: inline-block !important;
  }

  .categoryArticlesList {
    margin-bottom: 20px;
  }

  .articleCardInfo img {
    margin: 0 auto;
  }

  .blueBgDiv,
  .breadcrumbDiv {
    background: #0966c2;
  }

  .latestArticleSection .articleDisplay {
    height: 280px;
  }

  .pagination,
  ul.pagination {
    margin-bottom: 20px;
  }

  .viewAllDiv a {
    white-space: normal;
  }

  .trendingArtilerList,
  .recentArticlesList,
  .recentnewsList {
    overflow: initial;
  }

  .catgegoryArticle .articleBanner,
  .latestArticleSection .articleDisplay .row figure {
    border-bottom: var(--border-line);
    border-right: none;
  }

  .pageHeading {
    padding-top: 20px;
  }

  .latestInfoSection .latestInfoDiv {
    min-height: 274px;
  }

  .latestInfoSection .latestInfoDiv figure {
    min-height: 168px;
  }

  .latestInfoSection .latestInfoDiv img {
    height: 168px;
  }

  .catgegoryArticle:last-child {
    margin-bottom: 0;
  }

  .categoryArticlesContainer {
    margin-top: 0;
  }

  .pageFooter {
    margin-bottom: 0;
  }

  .applyForFreeScholarship {
    display: none;
  }

  .latestArticleSection .aticleInfo img.gifLive,
  .bannerDiv img.gifLive {
    max-width: 37px;
    height: 16px;
    margin-right: 5px;
  }

  .liveUpdateSection .liveUpdateHeading {
    padding: 8px 10px;
  }

  .articleInfo ul {
    padding-left: 30px;
  }

  .topSearcheSection {
    padding: 10px;
  }

  .topSearcheSection .row {
    margin: 0;
    display: block;
  }

  .topSearcheSection .topsearchtext {
    font-size: 14px;
    font-weight: bold;
    padding-bottom: 8px;
    margin: 0;
    margin-right: 10px;
  }

  .topSearchDiv {
    flex-basis: calc(100%);
    max-width: 100%;
  }

  .topSearchDiv .scrollLeft,
  .topSearchDiv .scrollRight {
    display: none !important;
  }

  .topSearchDiv ul li {
    margin-right: 5px;
    padding: 0;
  }

  .topSearchDiv ul li:last-child {
    margin-right: 0;
  }

  .topSearchDiv ul li a {
    padding: 8px 16px;
    background: var(--color-white);
    border-radius: 50px;
    white-space: nowrap;
  }


  .knowMoreSection .cardTitle {
    font-size: 14px;
  }

  .knowMoreSection ul {
    padding-left: 20px;
  }

  .knowMoreSection ul li {
    display: block;
    margin-right: 0;
  }

  /* banner section  */

  .latestArticleSection img {
    -o-object-fit: cover;
    object-fit: cover;
  }

  .latestArticleSection .articleDisplay {
    flex-basis: auto;
    height: 276px;
  }

  .latestArticleSection .articleDisplay .aticleInfo {
    padding: 10px;
  }

  .latestArticleSection .articleDisplay .aticleInfo h2 {
    line-height: 18px;
    min-height: auto;
    -webkit-line-clamp: 5;
    overflow-wrap: break-word;
  }

  .latestArticleSection .articleDisplay:after {
    display: none;
  }

  .latestArticleSection .articleDisplay>div {
    position: relative;
  }

  .latestArticleSection .articleDisplay>div:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, #282828 56%);
    border-radius: 4px;
  }

  .latestArticleSection .articleDisplay .mobileOnly {
    vertical-align: bottom;
    display: inline-block !important;
    margin-right: 0px;
  }

  .latestArticleSection .articleDisplay .mobileOnly:after {
    display: none;
  }

  .latestArticleSection .articleDisplay .aticleInfo p {
    line-height: 20px;
    font-weight: normal;
  }

  .latestArticleSection .aticleInfo img.gifLive {
    max-width: 70px;
    height: 30px;
    margin-bottom: 10px;
  }

  .latestArticleSection .viewAllDiv {
    min-height: 275px;
  }

  .latestArticleSection .articleDisplay .viewAllDiv {
    min-height: 274px;
  }

  .latestInfoSection .latestInfoDiv .viewAllDiv {
    min-height: 297px;
  }

  .latestInfoListContainer .scrollRight,
  .latestInfoListContainer .scrollLeft {
    display: none !important;
  }

  /* banner section ends  */
}

.newsSidebarSection {
  border-radius: 4px;
  box-shadow: 0 0px 4px 0 rgba(0, 0, 0, 0.12);
  margin-bottom: 20px;
}

.newsSidebarSection .tab-content.activeLink {
  display: block;
}

.newsSidebarSection ul {
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: flex;
}

.newsSidebarSection ul li {
  list-style-type: none;
  flex-basis: 50%;
  text-align: center;
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  cursor: pointer;
  padding: 12px 5px;
  padding-bottom: 9px;
  border-bottom: var(--border-line);
  font-weight: var(--font-semibold);
}

.newsSidebarSection ul li.activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
}

.recentnewsList {
  padding: 20px;
}

.recentnewsDiv.row {
  margin: 0;
  flex-wrap: nowrap;
  margin-bottom: 10px;
  border-bottom: var(--border-line);
  padding-bottom: 10px;
  -webkit-box-align: center;
  align-items: center;
}

.recentnewsDiv img {
  width: 96px;
  max-height: 72px;
  display: block;
  align-self: center;
}

.recentnewsDiv .trendingArtileText a,
.recentnewsDiv .recentArticlesDivText a,
.recentnewsDiv .recentnewsDivText a {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: 500;
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recentnewsDiv .trendingArtileText a:hover,
.recentnewsDiv .recentArticlesDivText a:hover,
.recentnewsDiv .recentnewsDivText a:hover {
  color: #337ab7;
  text-decoration: underline;
}

.listCard:last-child,
.listCard:last-child .recentnewsDiv.row {
  margin-bottom: 0;
  border: none;
  padding: 0;
}

.recentnewsDivText,
.recentArticlesDivText {
  flex-basis: calc(100% - 96px - 16px);
}

.verifiedBlueTickIcon {
  background: url(/yas/images/master_sprite.webp?b5965618277cb72aa2882736b150fec5);
  width: 16px;
  height: 16px;
  background-position: -673px -557px;
  vertical-align: text-top;
  transform: scale(0.65);
}

.bannerDiv .updated-info.row {
  align-items: flex-start;
}

.bannerDiv .updated-info.row .updatedBy img {
  float: left;
}

.bannerDiv .updated-info.row .authorAndDate {
  display: flex;
  flex-wrap: wrap;
}

.bannerDiv .updated-info.row .authorAndDate p {
  flex-basis: 100%;
  font-size: 12px;
}

.updated-info .authorAndDate .authorName {
  display: inline-block !important;
  vertical-align: middle;
  padding-left: 0px !important;
  font-size: 12px;
  font-weight: 700;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.5;
  letter-spacing: 0.3px;
  text-align: left;
  color: #282828 !important;

}

.articleText .authorName {
  color: var(--primary-font-color) !important;
}

.figureCaption {
  text-align: center;
  display: block;
  padding-top: 5px;
  font-size: 14px;
}

/* Share Icon */
.shareSection {
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
  background: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  border-radius: 4px;
}

.shareSection div {
  display: flex;
  align-items: center;
  gap: 10px;
}

.shareSection div p {
  margin: 6px 10px 6px 0;
  font-size: 18px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.33;
  letter-spacing: 0.3px;
  text-align: left;
  color: #282828;
}

.shareSection div p span {
  color: #3d8ff2;
}

.audio-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: left;
  align-items: center;
}

.audio-container audio {
  width: 100%;
  border-radius: 10px;
}

.audio-text {
  flex-basis: 100%;
  margin-bottom: 10px;
  font-size: 20px;
  font-weight: 600;
}

/*adding translation button style*/
.webpSpriteIcon {
  display: inline-block !important;
  background: url(/yas/images/master_sprite.webp);
  text-align: left;
  overflow: hidden;
}

.authorInfoAndTranslateBtn {
  display: flex;
  justify-content: space-between;
}

.translateIcon1 {
  background-position: -635px -876px;
  width: 27px;
  height: 24px;
  vertical-align: middle;
  margin-right: 10px;
  display: inline-block;
}

.translateIcon2 {
  background-position: -676px -876px;
  width: 27px;
  height: 24px;
  vertical-align: middle;
  margin-right: 10px;
  display: inline-block;

}

.authorInfoAndTranslateBtn .updated-info.row {
  margin-top: 0;
}

.authorInfoAndTranslateBtn .translateBtn {
  padding: 5px 10px;
  font-size: 15px;
  font-weight: normal;
  line-height: 1.73;
  color: #3d8ff2;
  border-radius: 3px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  text-transform: none;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
}

/*translation style end*/


@media (max-width: 1023px) {
  .blueBgDiv {
    background: #f3f2ef !important;
  }

  .breadcrumbDiv {
    background: none;
  }

  .topSearcheSection {
    margin-bottom: 5px !important;
  }

  .breadcrumbDiv ul li {
    color: #282828;
  }

  .breadcrumbDiv ul li a {
    color: #282828;
  }

  .breadcrumbDiv ul li a::after {
    background: url(/yas/images/master_sprite.webp);
    background-position: -37px -151px;
  }

  .pageFooter {
    margin-top: 0px;
  }

  .bannerDiv h1 {
    font-size: 18px;
  }

  .topSearcheSection {
    padding: 8px 10px;
  }

  .topSearcheSection .topSearchDiv ul li {
    margin-left: 0px
  }

  .topSearcheSection .topSearchDiv ul li a {
    font-size: 12px;
    color: #787878;
    padding: 0px;

  }

  .bannerDiv .updated-info.row {
    flex-wrap: nowrap;
  }

  .bannerDiv .updated-info.row .authorAndDate p {
    padding-left: 0px;
  }

  .followUs {
    border-radius: 4px;
    border: solid 1px #d8d8d8;
    background-color: #fff;
    flex-basis: 100%;
    justify-content: center;
  }

  .shareSection {
    gap: 10px;
    padding: 10px;
  }


  .shareSection div p {
    font-size: 15px;
    margin: 0px;
  }

  .topSearcheSection .container .row {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    overflow: hidden;
  }

  .topSearcheSection .container .row p {
    padding-bottom: 0px;
  }

  .shareIcon {
    transform: scale(0.85);
  }

  .audio-text {
    font-size: 16px;
  }

  .authorInfoAndTranslateBtn {
    flex-wrap: wrap;
    gap: 10px;
  }

  .translateBtn {
    flex-basis: 100%;
  }

}


/*  Banner Design for task 5438 */
.bannerDiv h1 {
  font-size: 24px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.58;
  text-align: left;
  color: #282828;
}

.bannerDiv h2 {
  font-size: 15px;
  font-weight: normal;
  font-stretch: normal;
  font-style: italic;
  line-height: 1.73;
  letter-spacing: 0.3px;
  text-align: left;
  color: #787878;
  margin: 20px 0px;
  padding-left: 10px;
  border-left: 2px solid #787878;

}

.shareIcon {
  width: 36px;
  height: 36px;
}

.whatsappIcon {
  background: url(/yas/images/WhatsApp.svg) no-repeat !important;
}

.gmailIcon {
  background: url(/yas/images/Gmail.svg) no-repeat !important;
  cursor: pointer;
}

.copyClipboardIcon {
  background: url(/yas/images/Copy-clipboard.svg) no-repeat !important;
  cursor: pointer;
}

.googleNewsIcon {
  background: url(/yas/images/Google-news.svg) no-repeat !important;
}

.bigTwitterIcon {
  background: url(/yas/images/Twitter.svg) no-repeat !important;
}

#custom-tooltip {
  padding: 5px 12px;
  background-color: #000000df;
  border-radius: 4px;
  color: #fff;
}

.pageFooter {
  padding-bottom: 0px;
}

.bannerDiv {
  border-radius: 4px;
  padding: 30px;
  margin: 20px 0;
}

@media (max-width:1023px) {
  .leadFormDiv .select2-container--default .select2-selection--single .select2-selection__rendered {
    padding: 0;
    padding-left: 8px;
    padding-right: 20px;
    border: none;
    min-height: unset;
    background-position: 95% 9px !important;
  }

  .bannerDiv {
    margin-top: -157px;
  }
}