bodY {
  background-color: #fff !important;
}

.select2-search.select2-search--dropdown {
  display: none;
}

.help-block {
  position: absolute;
  padding-top: 0px;
}

.gis-pr-main {
  padding: 40px 0 20px 0;
}

.gis-pr-heading {
  font-size: 36px;
  font-weight: bold;
  line-height: 1.44;
  text-align: left;
  color: #282828;
  margin-bottom: 20px;
}

.gis-pr-desc {
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.5;
  letter-spacing: normal;
  text-align: left;
  color: #282828;
}

.gis-pr-calculator {
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  margin-bottom: 40px;
}

.gis-pr-timeline {
  display: flex;
  width: 65%;
}

.gis-pr-step {
  flex-basis: 160px;
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.gis-pr-step-number {
  width: 30px;
  height: 30px;
  background-color: #fff;
  border: 1px solid grey;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  color: #686868;
}

.gis-pr-step-number.complete {
  border: 2px solid #282828;
  font-weight: 600;
  color: #282828;
}

.gis-pr-step-number.active {
  display: none;
}

.gis-pr-step-check {
  display: none;
  background-color: #fff;
}

.gis-pr-step-check.checked {
  display: block;
}

.wrapper {
  height: 30px;
  width: 30px;
  display: none;
  flex-grow: 0;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.checkmark {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #fff;
  stroke-miterlimit: 10;
  margin: 8% auto;
  box-shadow: inset 0px 0px 0px #6cc6aa;
  animation: fill 0.4s ease-in-out 0.4s forwards,
    scale 0.3s ease-in-out 0.9s both;
}

.checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}

.checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #6cc6aa;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

@keyframes fill {
  100% {
    box-shadow: inset 0px 0px 0px 30px #6cc6aa;
  }
}

@keyframes scale {

  0%,
  100% {
    transform: none;
  }

  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

.gis-pr-step-title {
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
  color: #686868;
}

.gis-pr-timeline-line {
  position: absolute;
  right: -80px;
  top: 25%;
  width: 160px;
  height: 2px;
  background-color: #d8d8d8;
  z-index: -1;
}

.gis-pr-timeline-line.active {
  background-color: #18c4aa;
}

.gis-pr-form-container {
  padding: 20px;
  margin-top: 20px;
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  background-color: rgba(219, 235, 255, 0.2);
}

.gis-pr-form {
  display: none;
}

.gis-pr-form.active {
  display: block;
}

.gis-pr-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gis-pr-form-heading {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  text-align: left;
  color: #282828;
}

.gis-pr-form-unlock-ctn {
  display: flex;
  justify-content: end;
}

.gis-pr-form-unlock,
.gis-pr-form-score {
  width: auto;
  height: 48px;
  border: none;
  flex-shrink: 0;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.5;
  text-align: center;
  color: #fff;
  justify-content: center;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.gis-pr-form-score.last-step {
  border-radius: 4px 0 0 4px;
}

.gis-pr-form-unlock {
  width: 162px;
  gap: 0px;
  display: flex;
  border-radius: 4px;
  background-color: #ff615d;
  animation-name: ripple;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  padding-right: 10px;
}

.gis-pr-form-container .gis-pr-form-unlock {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.gis-pr-form-score {
  display: block;
  border-radius: 4px;
  background-color: #0966c2;
  padding: 12px;
  cursor: default;
}

.myscore {
  color: white;
}

.gis-pr-form-questions:not(:last-child) {
  margin-bottom: 20px;
}

.gis-pr-form-question {
  margin-bottom: 10px;
  font-size: 18px;
  line-height: 1.33;
  text-align: left;
  color: #282828;
  position: relative;
}

.gis-pr-form-field {
  position: relative;
  display: flex;
  flex-direction: column;
}

.form-group {
  padding-bottom: 0px;
}

.gis-pr-form-radio-options {
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 30px;
}

.gis-pr-form-radio-options label {
  font-size: 16px;
  color: #282828;
  line-height: 1.1;
  display: grid;
  align-items: center;
  grid-template-columns: 1em auto;
  gap: 20px;
  margin-bottom: 0;
  font-weight: unset;
}

.gis-pr-form-radio-options input[type="radio"] {
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  margin: 0;
  font: inherit;
  color: currentColor;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #989898;
  transform: translateY(-0.075em);
  display: grid;
  place-content: center;
}

.gis-pr-form-radio-options input[type="radio"]:checked {
  border: 2px solid #ff615d;
}

.gis-pr-form-radio-options input[type="radio"]:checked::before {
  transform: scale(1);
}

.gis-pr-form-radio-options input[type="radio"]::before {
  content: "";
  width: 14px;
  height: 14px;
  border-radius: 50%;
  transform: scale(0);
  transition: 120ms transform ease-in-out;
  background-color: #ff615d;
}

.gis-pr-form-container select {
  width: 323px;
  height: 46px;
  border-radius: 4px;
  background: transparent;
}

.select2.select2-container {
  /* width: 40% !important;
    */
  width: 323px !important;
  height: 46px !important;
}

.select2-container .selection {
  display: initial;
}

.select2-container .select2-selection--single {
  position: relative;
  height: 46px !important;
  color: #686868 !important;
  border: solid 1px #d8d8d8;
  /* margin-bottom: 16px;
    */
}

.select2-container .selection .select2-selection .select2-selection__rendered {
  color: #686868 !important;
  font-size: 14px;
  padding: 8px 10px !important;
  max-width: 300px;
}

.select2-selection__placeholder {
  color: #686868 !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  position: absolute;
  /* top: 6px;
    */
  top: 2px;
  right: 16px !important;
}

.select2-container .selection .select2-selection .select2-selection__arrow b {
  background-image: url(../../images/master-sprite-gis.webp);
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: -598px -343px;
  border: none !important;
  height: 9px !important;
  width: 14px !important;
  margin: auto !important;
  top: 15px !important;
  left: 0px !important;
  position: absolute;
}

.gis-pr-form-btn {
  display: flex;
  justify-content: end;
  margin-top: 10px;
}

#prev-form-btn,
#next-form-btn,
#getmyscore,
.clb_submit,
#hideReport {
  width: 110px;
  height: 36px;
  padding: 0 20px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.5;
  text-align: center;
}

.clb_submit {
  border: none;
  color: #fff;
  background-color: #ff615d;
  padding-top: 0px;
  cursor: pointer;
  outline: none;
}

#next-form-btn,
#getmyscore,
#hideReport {
  border: none;
  color: #fff;
  background-color: #ff615d;
  padding-top: 5px;
  cursor: pointer;
  outline: none;
}

.workExperience,
.educationQualification,
.specialLang,
.submitForm,
.hideReport {
  display: none;
}

.submitForm {
  padding-top: 0px !important;
}

#hideReport,
#getmyscore,
.clb_submit {
  width: auto;
}

#prev-form-btn {
  display: none;
  border: 1px solid #ff615d;
  color: #ff615d;
  background-color: #fff;
  margin-right: 10px;
  padding-top: 5px;
  cursor: pointer;
  outline: none;
}

.gis-pr-form-score-reset {
  width: auto;
  height: 48px;
  border-radius: 0 4px 4px 0;
  flex-shrink: 0;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.5;
  text-align: center;
  color: #ff615d !important;
  background-color: transparent;
  border: 1px solid #ff615d;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 10px;
  cursor: pointer;
}

#gis-pr-form-score-reset {
  display: none;
}

@keyframes ripple {
  0% {
    box-shadow: 0 0 0 0 rgba(216, 80, 80, 0.267),
      0 0 0 0 rgba(245, 29, 29, 0.267);
  }

  80% {
    box-shadow: 0 0 0 15px #fff 0, 0 0 0 5px #fff 0;
  }

  100% {
    box-shadow: 0 0 0 0 #fff 0, 0 0 0 0 #fff 0;
  }
}

.gis-pr-eligibility-score {
  display: none;
  margin-top: 20px;
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  border-bottom: 0px;
}

.gis-pr-score-header {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.gis-pr-score-heading {
  margin-bottom: 16px;
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  text-align: center;
  color: #282828;
}

.gis-pr-score-detail {
  font-size: 16px;
  line-height: 1.5;
  text-align: center;
  color: #282828;
}

.gis-pr-score-detail span {
  font-weight: 600;
}

.gis-pr-eligibility-score table {
  margin-bottom: 0px;
  border: 0px;
}

.gis-pr-score-card {
  width: 100%;
}

.gis-pr-score-card th {
  width: 70%;
}

.gis-pr-score-card th,
.gis-pr-score-card td {
  text-align: center;
  border-collapse: collapse;
  border: 1px solid #d8d8d8;
  padding: 20px 0;
}

.gis-pr-score-card-head th {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.2;
  color: #282828;
}

.gis-pr-score-card th:first-child,
.gis-pr-score-card td:first-child {
  border-left: none;
}

.gis-pr-score-card th:last-child,
.gis-pr-score-card td:last-child {
  border-right: none;
}

.gis-pr-score-card td {
  width: 30%;
}

.gis-pr-score-card th,
.gis-pr-score-card td {
  text-align: center;
  border-collapse: collapse;
  border: 1px solid #d8d8d8;
  padding: 20px 0;
}

.gis-pr-score-card-body td {
  font-size: 18px;
  line-height: 1.33;
  color: #282828;
}

.container.sinp,
.container.express-cal {
  padding: 0px;
}

.gis-pr-blog {
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  margin-bottom: 80px;
}

.gis-pr-blog table {
  margin-bottom: 30px;
}

.gis-pr-blog-unlock {
  height: 680px;
  overflow-y: hidden;
  margin-bottom: 20px;
}

.gis-pr-blog-title {
  margin-bottom: 10px;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.5;
  text-align: left;
  color: #282828;
}

.gis-pr-blog-desc {
  margin-bottom: 16px;
  font-size: 16px;
  line-height: 1.5;
  text-align: left;
  color: #282828;
}

.gis-pr-blog-unlock table td,
.gis-pr-blog-unlock table th {
  text-align: center;
}

.gis-pr-blog-list {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  list-style-type: none;
}

.gis-pr-blog-list-item {
  position: relative;
  padding-left: 20px;
  font-size: 16px;
  line-height: 1.5;
  text-align: left;
  color: #282828;
  margin-bottom: 10px;
}

.gis-pr-blog-list-item::before {
  content: "";
  position: absolute;
  top: 12%;
  left: 0;
  background: url(../../images/master-sprite-gis.webp) no-repeat;
  background-position: -686px -343px;
  width: 9px;
  height: 13px;
}

.gis-pr-blog-prefix {
  font-weight: 600;
}

.cal-sub-heading {
  margin-left: 60px;
}

.gis-pr-continue-btn {
  width: 164px;
  height: 36px;
  border-radius: 4px;
  border: solid 1px #ff615d;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.5;
  background-color: #fff;
  text-align: center;
  color: #ff615d;
}

.customBullet::before {
  top: 10% !important;
}

.gis-pr-visa-approval {
  margin-bottom: 80px;
}

.gis-visaCards-container {
  display: flex;
  gap: 30px;
  overflow: hidden;
  overflow-x: auto;
}

.gis-visaCards-container::-webkit-scrollbar {
  display: none;
}

.gis-pr-visa-heading {
  font-size: 36px;
  font-weight: bold;
  line-height: 1.22;
  text-align: center;
  color: #282828;
  margin-bottom: 20px;
}

.gis-visaCards {
  overflow: hidden;
  flex-shrink: 0;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
  background-color: #fff;
  width: calc(23% + 1px);
  text-decoration: none;
  display: inline-block;
  color: #282828;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 3px 10px 0px rgba(0, 0, 0, 0.1);
}

.gis-visaCards:hover {
  text-decoration: none;
  color: #282828;
}

.gis-pr-visa-img {
  /* width: 256.7px;
    */
  height: 272px;
  text-align: center;
  object-fit: contain;
}

.gis-pr-visa-name {
  height: 54px;
  padding: 16px;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.33;
  text-align: left;
  text-decoration: none;
}

.customSlider {
  position: relative;
}

.clb-form {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
}

.detailsBox.cblConvertBox .clb-form .inputField {
  margin-right: 20px;
  width: 23%;
}

.clb-form .inputField label {
  font-size: 18px;
  line-height: 24px;
  padding-bottom: 10px;
  display: block;
}

.clb-form .inputField select {
  min-height: 46px;
  border: 1px solid #d8d8d8;
  cursor: pointer;
  background-color: #fff;
  border-radius: 4px;
  padding: 0 16px;
  font-size: 16px;
  line-height: 24px;
  color: #686868;
  width: 100%;
  max-width: 323px;
}

.cblConvertBox .gis-pr-form-btn {
  margin-top: 30px;
}

.gis-pr-form-score-reset.clbMobile {
  display: none;
}

.cblConvertBox #getmyscore,
.clb_submit {
  border: none;
  border-radius: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  cursor: pointer;
  height: auto;
}

.detailsBox.cblConvertBox form .formWrap {
  display: block;
}

.spouseData,
.primaryTestResult,
.secondaryScore,
.spouseLanguageTest,
.canadianEducation,
.noc,
.notEligible {
  display: none;
}

.gis-crs-calculator .gis-pr-form-header:not(:first-child),
.skipForm {
  margin-top: 30px;
}

.gis-crs-calculator .gis-pr-form-header {
  margin-bottom: 20px;
}

.gis-crs-calculator .gis-pr-form-score-reset {
  padding: 10px 15px 0px 16px;
}

.crsSubmit.submitForm {
  display: block;
}

.gis-pr-form-score-reset.crsMobile {
  display: none;
}

.gis-crs-calculator .notEligible {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  color: #0966c2;
  width: auto;
  border: none;
}

@media (max-width: 1023px) {
  

  .blueBgDiv {
    height: 0px !important;
  }

  .gis-pr-main {
    padding: 40px 0px 20px 0px;
  }

  .gis-pr-heading {
    font-size: 24px;
    margin-bottom: 16px;
    text-align: center;
  }

  .gis-pr-desc {
    font-size: 16px;
    line-height: 1.5;
    text-align: center;
  }

  .gis-pr-calculator {
    padding: 0px;
    border-radius: 0px;
    border: none;
  }

  .gis-pr-timeline,
  .gis-germany-timeline {
    display: flex;
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
  }

  .gis-pr-timeline::-webkit-scrollbar,
  .gis-germany-timeline::-webkit-scrollbar {
    display: none;
  }

  .gis-pr-form-container {
    padding: 16px;
    margin-top: 16px;
  }

  .gis-pr-form-header {
    gap: 10px;
    margin-bottom: 20px;
  }

  .gis-pr-form-heading {
    font-size: 20px;
    line-height: 1.4;
  }

  .gis-pr-form-score,
  .gis-pr-form-score.last-step {
    border-radius: 4px;
  }

  .gis-pr-form-questions:not(:last-child) {
    margin-bottom: 20px;
  }

  .gis-pr-form-btn {
    margin-top: 20px;
    margin-bottom: 10px;
  }

  .gis-pr-form-container select {
    width: 100%;
    height: 46px;
    border-radius: 4px;
    background: transparent;
  }

  .select2.select2-container {
    /* width: 40% !important;
        */
    width: 100% !important;
    height: 46px !important;
  }

  .select2-container .selection .select2-selection .select2-selection__rendered {
    display: block;
    padding-left: 8px;
    padding-right: 20px;
    overflow-x: auto !important;
    overflow-y: hidden !important;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  #prev-form-btn,
  #next-form-btn {
    width: 100%;
    height: 40px;
    /* padding: 0 20px;
        */
    border-radius: 4px;
    /* font-size: 16px;
        */
    font-size: 17.8px;
    font-weight: bold;
    line-height: 1.5;
    text-align: center;
  }

  #prev-form-btn {
    display: none;
    border: 1px solid #ff615d;
    color: #ff615d;
    background-color: #fff;
    margin-right: 10px;
  }

  #next-form-btn {
    border: none;
    color: #fff;
    background-color: #ff615d;
    padding-top: 7px;
  }

  #getmyscore {
    height: 39px;
  }

  #hideReport {
    width: 100%;
    font-size: 16.6px;
    height: 39px;
    padding-top: 7px;
  }

  .gis-pr-form-score-reset {
    display: none;
    border-radius: 4px;
  }

  .container.sinp,
  .container.express-cal {
    padding: 0px;
  }

  .gis-pr-blog {
    padding: 16px;
    /* margin: 0 16px 40px 16px;
        */
    margin: 0 0px 40px 0px;
  }

  .gis-pr-blog-unlock {
    height: 600px;
  }

  .gis-pr-blog-title {
    font-size: 24px;
  }

  .gis-pr-blog-desc {
    margin-bottom: 16px;
    font-size: 16px;
  }

  .gis-pr-blog-list-item {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .gis-pr-blog-list-item::before,
  .sinp .gis-pr-blog-list-item::before {
    top: 6px;
  }

  .cal-sub-heading {
    margin-left: 0px;
  }

  .gis-pr-blog-prefix {
    font-weight: 600;
  }

  .customBullet::before {
    top: 3% !important;
  }

  .gis-pr-continue-btn {
    width: 100%;
  }

  .gis-pr-eligibility-score {
    display: none;
    margin-top: 20px;
    border-radius: 4px;
    border: 1px solid #d8d8d8;
  }

  .gis-pr-score-header {
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .gis-pr-score-heading {
    margin-bottom: 16px;
    font-size: 24px;
    font-weight: bold;
    line-height: 1;
    text-align: center;
    color: #282828;
  }

  .gis-pr-score-detail {
    font-size: 16px;
    line-height: 1.5;
    text-align: center;
    color: #282828;
  }

  .gis-pr-score-card-head th {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.2;
    color: #282828;
  }

  .gis-pr-score-card th,
  .gis-pr-score-card td {
    padding: 8px 0;
  }

  .gis-pr-score-card-body td {
    font-size: 14px;
    line-height: 1.33;
    color: #282828;
  }

  .gis-visaCards {
    width: 63%;
  }

  .gis-pr-visa-name {
    height: 41px;
    padding: 9px 12px;
    font-size: 18px;
    display: flex;
    align-items: center;
  }

  .gis-pr-visa-img {
    object-fit: fit;
    height: 207px;
  }

  .gis-pr-visa-approval {
    padding-top: 16px;
    border: 1px solid #d8d8d8;
    border-radius: 4px;
    /* margin: 0 16px 40px 16px;
        */
    margin: 0 0px 40px 0px;
    padding-bottom: 16px;
  }

  .gis-pr-visa-approval .scrollRight,
  .gis-pr-visa-approval .scrollLeft {
    display: none !important;
  }

  .gis-pr-visa-heading {
    font-size: 24px;
    margin-bottom: 0px;
  }

  .customSlider {
    margin-top: 20px;
  }

  .pageFooter {
    padding-bottom: 0 !important;
  }

  .detailsBox.cblConvertBox .clb-form .inputField {
    width: 100%;
  }

  .gis-pr-form-score-reset.clbMobile {
    display: block;
    margin-right: 10px;
    width: 45%;
  }

  .gis-pr-form-score-reset.resetmobile {
    display: block;
  }

  .gis-pr-form-container .cta_button {
    border-radius: 4px;
    height: 48px;
  }

  .gis-pr-form-score-reset.crsMobile {
    padding: 10px;
    width: auto;
    display: block;
    margin-right: 10px;
  }

  .crsSubmit.submitForm::after {
    content: "Show Report";
  }

}