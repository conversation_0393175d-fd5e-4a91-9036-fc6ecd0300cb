body {
    background: #F1F1F1;
    overflow-x: hidden;
    font-family: helvetica !important;
}

.twoDbackground {
    width: 100%;
    height: 600px;
    background: repeating-linear-gradient(80deg, #4374b9, #4374b9, #ee424f, #ee424f), linear-gradient(45deg, transparent 50%, transparent 30%);
    position: absolute;
    -webkit-transform: skew(0deg, -10deg);
    transform: skew(0deg, -10deg);
    padding: 200px 0;
    margin-top: -250px;
}

.sa_dropdown.registerNew {
    padding: 0px 6px;
}

.sa_dropdown a.registerWelcomeMsg {
    padding-top: 1px !important;
    padding-bottom: 1px !important;
    margin-top: 6px !important;
}

.container.twoD {
    height: 745.88px;
}

#leadForm {
    background: white;
    margin-top: 25px;
    margin-bottom: 50px;
    padding: 5px 30px 20px 30px;
    text-align: left;
}

.form_heading h1 {
    color: white;
    font-weight: bold;
    font-size: 30px;
}

.footer-wrapper {
    margin-bottom: 0px;
}

.container .row a {
    white-space: normal;
}

footer>div {
    /*To hide white space above footer only on this page*/
    margin-top: 0 !important;
}

.twoDbackground {
    width: 100%;
    height: 600px;
    background: repeating-linear-gradient(80deg, #4374b9, #4374b9, #ee424f, #ee424f), linear-gradient(45deg, transparent 50%, transparent 30%);
    position: absolute;
    -webkit-transform: skew(0deg, -10deg);
    transform: skew(0deg, -10deg);
    padding: 200px 0;
    margin-top: -250px;
}

.form_heading h1 {
    color: white;
    font-weight: bold;
    font-size: 30px;
}

.form_heading h2 {
    color: white;
    font-size: 20px;
    font-weight: bold;
    line-height: 30px;
    letter-spacing: 0px;
    margin-top: 20px;
}

.form_heading p {
    color: white;
    font-size: 14px;
}

.form-banner {
    padding-top: 50px;
    padding-left: 15px;
}

#leadForm label {
    font-size: 12px;
    position: absolute;
    top: 10px;
    left: 15px;
    color: #333;
    transition: 0.2s all;
    display: inline;
}

#leadForm {
    background: white;
    margin-top: 25px;
    margin-bottom: 50px;
    padding: 5px 30px 20px 30px;
    text-align: left;
}

#leadForm input {
    width: 100%;
    border: 0;
    outline: 0;
    padding: 5px 0px;
    border-bottom: 1px solid #d3d3d3;
}

#leadForm input:focus~label,
#leadForm input:valid~label {
    top: -13px;
    color: #d3d3d3;
}

.form-group {
    margin-bottom: 20px;
}

#course {
    border: none;
    border-bottom: 1px solid #d3d3d3;
    padding-left: 0px;
    box-shadow: none;
    color: #333;
    font-weight: bold;
    font-size: 12px;
}

#qualification {
    border: none;
    border-bottom: 1px solid #d3d3d3;
    padding-left: 0px;
    box-shadow: none;
    color: #333;
    font-weight: bold;
    font-size: 12px;
}

a:focus,
a:hover {
    text-decoration: none;
}

#want-to-get-in {
    border-radius: 0px;
    box-sizing: border-box;
    border: 0px;
    padding: 8px 20px;
    background: #337ab7;
    color: white;
    font-weight: bold;
    font-size: 13px;
}

.star {
    position: absolute;
    width: 0;
    height: 0;
    border-top: 50px solid #00c853;
    border-left: 50px solid transparent;
    right: 15px;
    top: 0px;
}

label span {
    line-height: 16px;
}

.container.mobile-text-center {
    padding: 30px 0px;
}

label img {
    margin-top: 0px;
}

.input-default {
    width: inherit !important;
}

.label-default {
    font-size: 12px !important;
    position: inherit !important;
    top: inherit !important;
    left: inherit !important;
    color: #333 !important;
    background-color: inherit !important;
    transition: none !important;
    display: inherit !important;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #333;
    font-size: 12px;
    font-weight: bold;
}

.container .row a {
    white-space: normal;
}

.banner h2 {
    color: #fff;
    margin-top: 10px;
    font-size: 16px;
    line-height: 30px;
    margin-bottom: 46px;
}

.share-btn {
    border-radius: 0px;
    display: inline-flex;
    margin: .25rem;
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:focus,
.nav-tabs>li.active>a:hover {
    color: #4374b9;
    cursor: default;
    background-color: #fff;
    font-weight: 700;
    border: 0px;
    border-bottom: 3px solid #4989b7
}

.nav-tabs>li>a {
    color: #7d7d7d;
    font-weight: 700;
    text-align: center;
}

.nav-tabs>li {
    width: 50%;
}

.link-icon {
    transform: scale(0.6);
}

.footer-wrapper {
    margin-bottom: 0px;
}

/*City Select2 custom style*/
.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #000 !important;
}

.select2-container--default .select2-selection--single {
    border: none !important;
    border-bottom: solid 1px #d3d3d3 !important;
    border-radius: 0px !important;
}

.select2-container:focus {
    outline: none !important;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #333 !important;
    font-size: 12px !important;
}

.select2-selection-single-revised {
    background-color: #fff !important;
    border: none !important;
    border-radius: 0px !important;
    border-bottom: solid 1px #d3d3d3 !important;
}

.select2-selection__rendered {
    padding-left: 0px !important;
}

.content-design {
    background-image: -o-linear-gradient(45deg, #4374b9, #ee424f, #ee424f, #ee424f, #ee424f);
    background-image: linear-gradient(45deg, #4374b9, #ee424f, #4374b9, #ee424f, #ee424f);
    color: transparent !important;
    -webkit-background-clip: text;
    font-weight: 700;
    font-size: large;
    line-height: 2;
    word-spacing: 0.1em;
}

#stats {
    height: 375px;
}

@media (max-width: 720px) {

    #hnn-mainclass .container-fluid {
        display: none;
    }

    #stats {
        height: 630px;
    }

    .rules {
        margin-top: 50px;
    }

    .pageFooter .container,
    .pageFooter .row,
    .pageFooter ul,
    .pageFooter p,
    .pageFooter a {
        text-align: left !important;
        justify-content: flex-start !important;
    }

    .pageFooter .row {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
    }

    .pageFooter .socialMedia,
    .pageFooter .contactInfo {
        text-align: left !important;
        float: none !important;
    }

    .pageFooter ul {
        padding-left: 0;
        list-style: none;
    }

    .pageFooter ul li {
        display: inline-block;
        margin-right: 15px;
    }
}