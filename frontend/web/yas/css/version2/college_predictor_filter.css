.predictor-header {
    background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
    padding: 20px;
    margin: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
}

.predictor-header h1 {
    color: rgba(5, 0, 56, 1);
    font-weight: 500;
    font-size: 36px;
    line-height: 54px;
}

.predictor-header p {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin: 5px 0;
}

.img-section {
    position: relative;
}

.marks {
    text-align: center;
    position: absolute;
    inset: 6px 0 0 0;

}

.predictor-tool {
    display: flex;
    justify-content: space-between;

}

.predictor-tool p {
    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
}

.custom-select {
    position: relative;
    width: 170px;
    margin-left: 10px;
}

.custom-select select {
    width: 100%;
    padding: 6px 20px 6px 20px;
    border: 1px solid #ccc;
    border-radius: 20px;
    appearance: none;
    /* Remove default arrow in most browsers */
    -webkit-appearance: none;
    /* Safari */
    -moz-appearance: none;
    /* Firefox */
    background-color: #fff;
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 20 20'%3E%3Cpolygon fill='%23333' points='0,0 20,0 10,10'/%3E%3C/svg%3E") !important;
    background-repeat: no-repeat;
    background-position: 80% 14px !important;
    background-size: 9px;
    cursor: pointer;
    border: 1px solid rgba(216, 216, 216, 1);
    line-height: 20px;
    font-weight: 600;
}

.custom-select:nth-of-type(2) select {
    padding: 6px 45px 6px 30px;
}

.custom-select select:focus {
    outline: none;
    border-color: #007BFF;
    box-shadow: 0 0 3px rgba(0, 123, 255, 0.5);
}

.predictor-select-box {
    display: flex;
}

.predictor-select-box ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: inline-flex;
}

.predictor-tool h2 {
    color: rgba(5, 0, 56, 1);
    font-weight: 400;
    font-size: 18px;
    margin-bottom: 5px
}

#college-predictor-results-container {
    border: var(--border-line);
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    padding: 20px;
    overflow: hidden;
    height: auto;
    transition: all 1s;
}

.predictor-container {
    /* display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px; */
    /* justify-content: space-between; */
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    margin-top: 10px;
    /* justify-content: space-between; */
    margin-left: 70px;
}

.predictor-box {
    width: 290px;
    border: 1px solid rgba(216, 216, 216, 1);
    border-radius: 4px;
}

.predictor-image {
    position: relative;
    width: 100%;
    height: 180px
}

.predictor-image img {
    width: 100%;
    vertical-align: bottom;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    height: 100%;
}

.download {
    position: absolute;
    width: 38px;
    height: 38px;
    background: #fff;
    border-radius: 50%;
    top: 15px;
    right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.spriteIcon {
    display: inline-block !important;
    background-image: url(/yas/images/master_sprite.webp);
    overflow: hidden;
}

.downloadIcon,
.percentageIcon {
    width: 30px;
    height: 18px;
    background-position: 598px -1200px !important;
    vertical-align: text-bottom;
}

.downloadIcon {
    border: none;
}

.percentage {
    background: rgba(219, 235, 255, 1);
    padding: 5px 10px;
    margin: 10px 0;
    color: rgba(5, 0, 56, 1);
    font-weight: 400;
    line-height: 20px;
}

.percentageIcon {
    background-position: 631px -1200px !important;
    width: 24px;
}

.predictor_text_1 {
    margin-bottom: 15px;
}

.predictor_text_1:last-child {
    margin-bottom: 0;
}

.predictor_logo {
    width: 56px;
    height: 56px;
    margin-top: -50px;
    position: relative;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    background: #fff;
    margin-bottom: 15px;
    padding: 5px;
}

.predictor_logo img {
    width: 100%;
}

.predictor_text {
    padding: 20px;
    background: #fff;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.predictor_text button {
    width: 100%;
    height: 40px;
    color: #fff;
    background: rgba(255, 78, 83, 1);
    border: 0;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
}

.predictor_text p {
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.3px;
    color: rgba(51, 51, 51, 1);
    line-height: 21px;
}

.load-more {
    text-align: center;
    margin: 15px 0;
}

.load-more button {
    color: rgba(255, 78, 83, 1);
    background: transparent;
    border: 0;
    font-weight: 600;
    line-height: 24px;
    font-size: 14px;
}

.common-height {
    min-height: 250px;
}

/*-----share on whatsup------*/
.share-whatsup {
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
    padding: 20px;
    margin-bottom: 20px;
    overflow: hidden;
    margin-top: 15px;
}

.share-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.share-flex h3 {
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
}

.share-btn {
    white-space: nowrap;
    display: flex;
    gap: 15px;
}

.whatsup {
    background-color: rgba(0, 153, 101, 1);
    width: 187px;
    height: 32px;
    border: 1px solid rgba(0, 153, 101, 1);
    color: #fff;
    border-radius: 8px;
}

.copy-link {
    border: 1px solid var(--color-red);
    color: var(--color-red);
    width: 120px;
    height: 32px;
    background-color: transparent;
    border-radius: 8px;
}

.whatsupicon,
.attachIcon {
    width: 19px;
    height: 18px;
    background-position: 718px -1200px !important;
    vertical-align: text-bottom;
    margin-right: 10px;
}

.attachIcon {
    background-position: 673px -1200px !important;
}

.whatsup_text {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.whatsup_text p {
    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
}

.predictor-header-text {
    font-size: 18px;
    line-height: 28px;
    background: #f5f5f5;
    padding: 8px 20px;
    color: var(--primary-font-color);
    text-transform: uppercase;
    margin-bottom: 20px;
}

#college-predictor-filter-form {
    margin-left: 400px;
}

/*-----share on whatsup------*/

@media (max-width: 640px) {

    .predictor-container {
        margin-left: 0;
    }

    #college-predictor-filter-form {
        margin-left: 9% !important;
    }

    .predictor-header {
        margin: -150px 0 20px;
        flex-wrap: wrap;
        border-radius: 4px;
    }

    .predictor-header h1 {
        font-size: 24px;
        text-align: center;
        line-height: 36px;
        margin-bottom: 15px;
    }

    .predictor-header p {
        text-align: center
    }

    .img-section {
        margin: 25px auto 0;
    }

    .predictor-main {
        padding: 10px;
        margin-bottom: 15px;
    }

    .predictor-header p {
        font-weight: 600;
        line-height: 16px;
    }

    .predictor-tool h2 {
        line-height: 28px;
    }

    .custom-select:first-child {
        margin-left: 0;
    }

    .predictor-tool {
        display: block;
    }

    .predictor-box {
        width: 100%;
    }

    .share-flex {
        flex-wrap: wrap;
    }

    .share-whatsup {
        padding: 10px
    }

    .share-flex h3 {
        text-align: center;
        padding: 0 30px;
    }

    .share-whatsup {
        background: linear-gradient(180deg, #ffffff 57%, #fffae7 100%);
    }

    .whatsup_text p {
        text-align: center;
        margin-bottom: 20px
    }

    .share-btn {
        gap: 10px;
        width: 100%;
        justify-content: center;
    }

    .load-more {
        margin: 15px 0 0;
    }

    /* .predictor-select-box {
        overflow-x: scroll;
        width: auto;
    } */

    .predictor-select-box ul li {
        flex: 1
    }

    .predictor-select-box {
        display: block;
        white-space: nowrap;
        overflow-x: auto;
    }

    /* .predictor-header-text {
        margin-bottom: 15px;
        text-align: center;
        padding: 0 50px;
    } */

}