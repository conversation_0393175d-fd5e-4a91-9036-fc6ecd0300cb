<?php

namespace frontend\helpers;

use common\helpers\ContentHelper;
use common\helpers\ArticleDataHelper;
use Yii;

class Schema
{
    public static function breadcrumb($breadcrumbs)
    {
        if (empty($breadcrumbs)) {
            return '';
        }

        $items = [];
        foreach ($breadcrumbs as $key => $breadcrumb) {
            $url = !empty($breadcrumb['url']) ? Url::to($breadcrumb['url'], true) : Url::base(true) . Yii::$app->request->getUrl();

            $items[] = [
                '@type' => 'ListItem',
                // 'name' => $breadcrumb['label'] ?? $breadcrumbs[$key],
                'position' => $key + 1,
                'item' => [
                    '@type' => 'WebPage',
                    '@id' => trim(strtok($url, '?'), '/'),
                    'name' => $breadcrumb['label'] ?? $breadcrumbs[$key]
                ]
            ];
        }

        $schema = [
            '@context' => 'http://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $items
        ];

        return '<script type="application/ld+json">' . json_encode($schema) . '</script>';
    }

    /**
     * Get Live News Schema of BlogsPosting
     * @param  object $post
     * @param array $liveUpdate
     * @param array $meaData
     *
     * return array |[]
     */
    public static function liveNewsSchema($post, $liveUpdate, $ampUrl = null, $page = null)
    {

        if ($page == 'amp') {
            $currentUrl = $ampUrl;
        } else {
            $currentUrl = Url::base(true) . Yii::$app->request->getUrl();
        }

        $items = [];
        foreach ($liveUpdate as $value) {
            if (empty($value)) {
                return '';
            }
            $items[] = [
                '@type' => 'BlogPosting',
                'headline' => !empty($post->newsContent) ? $post->newsContent->meta_title : '',
                'url' => $currentUrl,
                'datePublished' => $value->published_at ?? '',
                'mainEntityOfPage' => $currentUrl,
                'dateModified' => date(DATE_ATOM, strtotime($value->updated_at)) ?? '',
                'articleBody' => ContentHelper::htmlDecode($value->content, true) ?? '',
                'image' => !empty($post) ? Url::toNewsImages($post->banner_image) : '',
                'author' => [
                    '@type' => 'Person',
                    'name' => !empty($post->newsContent) ? ($post->newsContent->author ? $post->newsContent->author->name : '') : '',
                ],
                'publisher' => [
                    '@type' => 'Organization',
                    'name' => 'Getmyuni',
                    'logo' => [
                        '@type' => 'ImageObject',
                        'url' => Yii::$app->params['gmuLogo'],
                        'height' => '320',
                        'width' => '320'
                    ],
                ],
            ];
        }

        $schema = [
            '@context' => 'http://schema.org',
            '@type' => 'LiveBlogPosting',
            'coverageEndTime' => !empty($value->news) && !empty($value->news->expired_at) ? date(DATE_ATOM, strtotime($value->news->expired_at)) : '',
            'coverageStartTime' => !empty($post) && !empty($post->published_at) ? date(DATE_ATOM, strtotime($post->published_at)) : date(DATE_ATOM, strtotime($post->created_at)),
            'mainEntityOfPage' => $currentUrl,
            'headline' => !empty($post->newsContent) ? $post->newsContent->meta_title : '',
            'articleBody' => !empty($post->newsContent) ? ContentHelper::htmlDecode($post->newsContent->content, true) : '',
            'description' => !empty($post->newsContent) ? $post->newsContent->meta_description : '',
            'inLanguage' => 'en',
            'articleSection' =>  'education',
            'url' => $currentUrl,
            'image ' => !empty($post) ? Url::toNewsImages($post->banner_image) : '',
            'datePublished' => !empty($post) && !empty($post->published_at) ? date(DATE_ATOM, strtotime($post->published_at)) : date(DATE_ATOM, strtotime($post->created_at)),
            'dateModified' => !empty($post) ? date(DATE_ATOM, strtotime($liveUpdate[0]['updated_at'])) : '',
            'author' => [
                '@type' => 'Person',
                'name' => !empty($post->newsContent) ? ($post->newsContent->author ? $post->newsContent->author->name : '') : '',
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'Getmyuni',
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => Yii::$app->params['gmuLogo'],
                    'height' => '320',
                    'width' => '320'
                ],
            ],
            'about' => [
                '@type' => 'Event',
                'name' => !empty($post) ? $post->name : '',
                'startDate' => !empty($post) && !empty($post->published_at) ? date(DATE_ATOM, strtotime($post->published_at)) : date(DATE_ATOM, strtotime($post->created_at)),
                'description' => !empty($post->newsContent) ? $post->newsContent->meta_description : '',
                'endDate' => !empty($value->news) && !empty($value->news->expired_at) ? date(DATE_ATOM, strtotime($value->news->expired_at)) : '',
                'eventAttendanceMode' => 'mixed',
                'eventStatus' => 'live',
                'image' => !empty($post) ? Url::toNewsImages($post->banner_image) : '',
                'location' => [
                    '@type' => 'Place',
                    'name' => 'India',
                    'address' => [
                        '@type' => 'PostalAddress',
                        'name' => 'India',
                    ],
                ]
            ],
            'liveBlogUpdate' => $items,
        ];
        if (!empty($post->newsContent) &&  $post->newsContent->meta_keywords != '') {
            $keyword['keywords'] = $post->newsContent->meta_keywords;
            $schema = array_merge(array_slice($schema, 0, 7), $keyword, array_slice($schema, 7));
        }
        return $schema;
    }

    /**
     * Get News Schema
     * @param object $post | Post Object of news details
     * @param string $liveUpdateDate | Latest update Date
     *
     * return array | []
     */
    public static function newsSchema($post, $liveUpdateDate = null, $ampUrl = null, $page = null)
    {
        if (empty($post)) {
            return '';
        }

        if ($page == 'amp') {
            $currentUrl = $ampUrl;
        } else {
            $currentUrl = Url::base(true) . Yii::$app->request->getUrl();
        }

        $schema = [
            '@context' => 'http://schema.org',
            '@type' => 'NewsArticle',
            'url' => $currentUrl,
            'articleBody' => !empty($post->newsContent) ? ContentHelper::htmlDecode($post->newsContent->content, true) : '',
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => $currentUrl,
            ],
            'headline' => !empty($post->newsContent) ? $post->newsContent->meta_title : '',
            'description' => !empty($post->newsContent) ? $post->newsContent->meta_description : '',
            'datePublished' => !empty($post) && !empty($post->published_at) ? date(DATE_ATOM, strtotime($post->published_at)) : date(DATE_ATOM, strtotime($post->created_at)),
            'dateModified' => !empty($liveUpdateDate) ? date(DATE_ATOM, strtotime($liveUpdateDate)) : date(DATE_ATOM, strtotime($post->updated_at)),
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'Getmyuni',
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => Yii::$app->params['gmuLogo'],
                    'height' => '320',
                    'width' => '320'
                ],
            ],
            'author' => [
                '@type' => 'Person',
                'name' => !empty($post->newsContent) ? ($post->newsContent->author ? $post->newsContent->author->name : '') : '',
            ],
            'image' => [
                '@type' => 'ImageObject',
                'url' => Url::toNewsImages($post->banner_image) ?? '',
                'width' => '1200',
                'height' => '667'
            ]
        ];
        if (!empty($post->newsContent) &&  $post->newsContent->meta_keywords != '') {
            $keyword['keywords'] = $post->newsContent->meta_keywords;
            $schema = array_merge(array_slice($schema, 0, 7), $keyword, array_slice($schema, 7));
        }

        return $schema;
    }

    /**
     * Get Faq Sceham
     * @param object | $faq FAQ array Object
     * @return object | JSON Obeject
     */
    public static function faqSchema($faqs)
    {
        $loadFaq = [];

        foreach ($faqs as $faq) {
            $loadFaq[] = [
                '@type' => 'Question',
                'name' => ContentHelper::htmlDecode($faq->question, true),
                'acceptedAnswer' => [
                    '@type' => 'Answer',
                    'text' => ContentHelper::htmlDecode($faq->answer, false)
                ]
            ];
        }

        return \yii\helpers\Json::encode([[
            '@context' => 'http://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => $loadFaq
        ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Get Ncert Sceham
     * @param object | $ncert Ncert array Object, $currentUrl for current page, $title
     * @return object | JSON Obeject
     */
    public static function ncertSchema($ncert, $currentUrl, $title)
    {
        return  \yii\helpers\Json::encode([[
            '@context' => 'http://schema.org',
            '@type' => 'Article',
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => $currentUrl,
            ],
            'headline' => $title,
            'image' => [ArticleDataHelper::getImage($ncert->cover_image)],
            'datePublished' => date(DATE_ATOM, strtotime($ncert->created_at)),
            'dateModified' => date(DATE_ATOM, strtotime($ncert->updated_at)),
            'author' => [
                '@type' => 'Person',
                'name' => $ncert->author ? $ncert->author->name : ''
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'Getmyuni',
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => Yii::$app->params['gmuLogo']
                ],
            ],
            'description' => $ncert->meta_description
        ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    public static function getPageSchema($slug, $pageData, $type)
    {
        $pageName =  ucfirst($slug);
        $imgBaseUrl = Url::base(true) . 'images/' . $type . '/' . $pageName . '/';
        $bannerImg = $imgBaseUrl . 'banner-' . $slug . '.webp';
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $pageData['seoTags']['title'],
            'description' => $pageData['seoTags']['description'],
            'url' => Url::current(['lg' => null], true),
            // "breadcrumb" => [
            //     "@type" => "BreadcrumbList",
            //     "itemListElement" => self::getBreadcrumb()
            // ],
            'mainEntity' => [
                '@type' => 'FAQPage',
                'mainEntity' => self::getFaqs($pageData['mainFaqs'])
            ],
            'publisher' => self::getPublisher($bannerImg),
            'sameAs' => self::getSocialMediaHandles(),
            'keywords' => self::getKeywords($slug)
        ];
        return self::cleanJsonString(json_encode($schema));
    }

    public static function getKeywords($slug)
    {
        $keywords = [
            'canada' => 'Canada PR, Canada Immigration, Canada permanent residency, Immigration Consultant, Canada PR Consultant',
            'australia' => 'Australia PR, Australia Immigration, Australia permanent residency, Immigration Consultant, Australia PR Consultant',
            'austria' => 'Austria RWR card, Austria RED White Red Card, Job Seeker Visa, Austria Job Seeker Visa, Job Search Visa, Immigration Consultant',
            'germany' => 'Job Seeker Visa, Germany Job Seeker Visa, Job Search Visa, Germany Visa, Immigration Consultant',
            'sweden' => 'Job Seeker Visa, Sweden Job Seeker Visa, Job Search Visa, Sweden Visa, Immigration Consultant',
            'immigration' => '',
            'pr-permanent-residence' =>'Canada PR, Canada Immigration, Canada permanent residency, Immigration Consultant, Canada PR Consultant',
            'job-seeker-visa' => 'Job Seeker Visa,Job Seeker Visa Assistance,Job Seeker Visa Countries',
            // 'sinp-points-calculator' => 'Canada SINP calculator, Saskatchewan PNP Score Calculator, Saskatchewan PNP Score Calculator, Canada PNP Calculator',
            // 'ielts-to-clb-converter' => 'IELTS to CLB Converter, IELTS to CLB Calculator, Canadian Language Benchmark Calculator, IELTS to CLB Score',
            // 'crs-calculator' => 'CRS Calculator, Express Entry Points Calculator, PR Points Calculator, CRS Score Calculator',
            // 'pr-eligibility-calculator' => '67 Points Calculator, Express Entry eligibility Calculator, PR Eligibility Calculator, FSW Points Calculator',
            // 'pr-points-calculator' => 'Australia PR Points Calculator, Australia PR Score Calculator, Australia PR Points',
            // "ielts-to-clb-converter" => "IELTS to CLB Converter, IELTS to CLB Calculator, Canadian Language Benchmark Calculator, IELTS to CLB Score",
            // "crs-calculator" => "CRS Calculator, Express Entry Points Calculator, PR Points Calculator, CRS Score Calculator",
            // "pr-eligibility-calculator" => "67 Points Calculator, Express Entry eligibility Calculator, PR Eligibility Calculator, FSW Points Calculator",
            // "pr-point-calculator" => "Australia PR Points Calculator, Australia PR Score Calculator, Australia PR Points",
            // 'germany-opportunity-card' => 'Germany Opportunity Card, Germany Job search visa, opportunity card requirements, Germany Visa, Immigration Consultant',
            // 'germany-healthcare-visa' => 'Germany healthcare visa, germany visa for healthcare professionals, germany job seeker visa healthcare, germany work visa requirements, germany healthcare jobs, germany visa processing time, work in germany hospitals, germany healthcare immigration.'
        ];
        return $keywords[$slug] ?? '';
    }

    public static function getFaqs($questions)
    {
        $faqItems = [];
        if (is_array($questions) && !empty($questions)) {
            foreach ($questions as $qa) {
                $faqItems[] = [
                    '@type' => 'Question',
                    'name' => $qa['question'],
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => $qa['answer']
                    ]
                ];
            }
        }
        return $faqItems;
    }

    public static function getSocialMediaHandles()
    {
        return [
            'https://www.facebook.com/GetmyuniInc',
            
            'https://www.instagram.com/getmyuni/',
            'https://www.linkedin.com/company/getmyuni/',
            'https://www.youtube.com/channel/UCvczFiMv9OZwYkFydoNdMCA'
        ];
    }

    public static function cleanJsonString($string)
    {
        return str_replace('\\', '', $string);
    }

    public static function getPublisher($bannerImg = '')
    {
        $publisherArr =  [
            '@type' => 'Organization',
            'name' => 'Getmyuni',
            'url' => 'https://www.getmyuni.com/',
            'logo' => 'https://www.getmyuni.com/azure/assets/images/logo_squre.png',
            'legalName' => 'GETMYUNI Education Services Private Limited',
            // 'alternateName' => 'Global Immigration Services',
            'telephone' => '+91-888-40-32-828',
            // "address" => [
            //     "@type" => "PostalAddress",
            //     "streetAddress" => "36/5, Hustlehub Tech Park-2nd Floor, Somasundarapalya Main Rd, adjacent 27th Main Road, Haralukunte Village, Sector 2, HSR Layout",
            //     "addressLocality" => "Bengaluru",
            //     "addressRegion" => "Karnataka",
            //     "postalCode" => "560102",
            //     "addressCountry" => "India"
            // ]
        ];
        if ($bannerImg != '') {
            $publisherArr['image'] = $bannerImg;
        }
        return $publisherArr;
    }
}
