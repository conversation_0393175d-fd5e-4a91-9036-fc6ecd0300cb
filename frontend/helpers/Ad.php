<?php

namespace frontend\helpers;

class Ad
{
    public static $counter = 0;

    public static $script = [];

    public static $targetString;

    public static $saMtfAdsIndex = [4 => 1, 8 => 2, 12 => 3, 16 => 4];

    public static function unit(string $slot, $size, $targeting = [])
    {
        list($width, $height) = Ad::getSlotSize($size);
        $slot = strtolower($slot);
        $counter = self::$counter;
        $randomId = '1614408539431';
        $containerId = 'div-gpt-ad-' . $randomId . '-' . $counter;
        $body = "<div class='lazy-ad' data-slot='$counter' id='$containerId' style='height:{$height}px'><script>googletag.cmd.push(function() { googletag.display('$containerId'); });</script></div>";
        $gptSlots = 'gptAdSlots[' . $counter . "] = googletag.defineSlot('/21840540389/{$slot}', {$size}, '{$containerId}').addService(googletag.pubads());\n";
        self::$script[$counter] = $gptSlots;
        self::$counter++;

        return $body;
    }

    public static function renderAdScript()
    {
        return implode('', self::$script) . self::$targetString;
    }

    private static function getSlotSize($size)
    {
        $sizes = ['250,200'=>[250,200],'390,90'=>[390,90],'728,90' => [728, 90], '300,250' => [300, 250], '250,250' => [250, 250], '300,50' => [300, 50], '300,600' => [300, 600], '300,100' => [300, 100], '1206,90' => [1206, 90]];
        foreach ($sizes as $key => $val) {
            if (strpos($size, $key) !== false) {
                return $val;
            }
        }
        return [728, 90];
    }

    public static function setTarget($targeting = [])
    {
        if (!empty($targeting)) {
            self::$targetString = 'googletag.pubads()';
            foreach ($targeting as $key => $value) {
                if ($value == '') {
                    continue;
                }
                if (is_array($value)) {
                    $value = '["' . implode('","', $value) . '"]';
                    self::$targetString .= '.setTargeting("' . $key . '",' . $value . ')';
                } else {
                    self::$targetString .= '.setTargeting("' . $key . '",' . '"' . $value . '")';
                }
            }
        }
    }

    public static function setTargetArticleNews($targeting = [])
    {
        if (!empty($targeting)) {
            self::$targetString = 'googletag.pubads()';
            foreach ($targeting as $target => $value) {
                if ($value == '') {
                    continue;
                }
                foreach ($value as $key => $keyValue) {
                    if ($keyValue == '') {
                        continue;
                    }
                    if (is_array($keyValue)) {
                        $keyValue = '["' . implode('","', $keyValue) . '"]';
                        self::$targetString .= '.setTargeting("' . $key . '",' . $keyValue . ')';
                    } else {
                        self::$targetString .= '.setTargeting("' . $key . '",' . '"' . $keyValue . '")';
                    }
                }
            }
        }
    }
    public static function setTargetAllColleges($targeting = [])
    {

        if (!empty($targeting)) {
            self::$targetString = 'googletag.pubads()';
            foreach ($targeting as $key => $keyValue) {
                if ($keyValue == '') {
                    continue;
                }
                if (is_array($keyValue)) {
                    $keyValue = '["' . implode('","', $keyValue) . '"]';
                    self::$targetString .= '.setTargeting("' . $key . '",' . $keyValue . ')';
                } else {
                    self::$targetString .= '.setTargeting("' . $key . '",' . '"' . $keyValue . '")';
                }
            }
        }
    }
}
