<?php

namespace frontend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\CollegeCourse;
use common\models\CollegeProgram;
use common\models\documents\CollegeProgram as DocumentsCollegeProgram;
use yii\data\ArrayDataProvider;
use yii\helpers\ArrayHelper;

/**
 * CollegeCourseSearch represents the model behind the search form of `common\models\CollegeCourse`.
 */
class CollegeCourseSearch extends CollegeProgram
{

    public $college;

    public $type;

    public $sort;

    public $stream = [];

    public $branch;

    public $degree;

    public $totalFee;

    public $course = [];

    public $location;

    public $courses = [];

    public $streams = [];

    public $branches = [];

    public $selectedFilters = [];

    public $stream_id;

    public $degrees = [];

    public $types = [];

    public $totalPrograms;

    public $typeList = [
        'full_time' => 'Full Time',
        'part_time' => 'Part Time',
        'distance_learning' => 'Distance',
    ];

    public $totalFees = [
        '0-100000' => 'Less than 1 Lakh',
        '100001-200000' => '1 to 2 Lakhs',
        '200001-300000' => '2 to 3 Lakhs',
        '300001-500000' => '3 to 5 Lakhs',
        '500001' => 'More than 5 Lakhs',
    ];

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['type', 'degree', 'course', 'totalFee', 'stream', 'branch', 'college', 'location', 'mode'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    public function attributeLabels()
    {
        return array_merge(parent::attributeLabels(), [
            'stream' => 'Stream',
            'course' => 'Course',
            'degree' => 'Course Level',
            'mode' => 'Study Mode',
            'branch' => 'Branch',
            'totalFee' => 'Total Fees'
        ]);
    }

    private function loadCourses($collegeId, $data = [])
    {

        $courses = [];
        $streams = [];
        $branches = [];
        $courseSlugs = [];
        $courseLevels = [];
        $types = [];


        $query  = DocumentsCollegeProgram::find()
            ->where(['college_id' => $collegeId])
            ->andWhere(['status' => CollegeCourse::STATUS_ACTIVE])
            ->orderBy(['course_position' => SORT_ASC]);

        $queryData = $query->all();

        foreach ($queryData as $d) {

            /** DegreeList */
            if (!empty($d->degree)) {
                if (!in_array($d->degree, $courseLevels)) {
                    $courseLevels[$d->degree] = $d->degree;
                }
            }

            /** TypeList Full/Part/distance */
            if (!empty($d->type)) {
                if (!in_array($d->type, $types)) {
                    $types[$d->type] = $d->type;
                }
            }

            /** CourseList */
            if (!empty($d->course_slug)) {
                if (!empty($data['stream'])) {
                    foreach ($data['stream'] as $stream) {
                        if ($d->stream_slug == $stream) {
                            if (!in_array($d->course_slug, $courseSlugs)) {
                                $courseSlugs[] = $d->course_slug;
                                $courses[] = ['slug' => $d->course_slug, 'name' => $d->course_short_name];
                            }
                        }
                    }
                } else if (!empty($data['branch'])) {
                    foreach ($data['branch'] as $branch) {
                        if ($d->specialization_slug == $branch) {
                            if (!in_array($d->course_slug, $courseSlugs)) {
                                $courseSlugs[] = $d->course_slug;
                                $courses[] = ['slug' => $d->course_slug, 'name' => $d->course_short_name];
                            }
                        }
                    }
                } else if (!empty($data['degree'])) {
                    foreach ($data['degree'] as $degree) {
                        if ($d->degree == $degree) {
                            if (!in_array($d->course_slug, $courseSlugs)) {
                                $courseSlugs[] = $d->course_slug;
                                $courses[] = ['slug' => $d->course_slug, 'name' => $d->course_short_name];
                            }
                        }
                    }
                } else {
                    if (!in_array($d->course_slug, $courseSlugs)) {
                        $courseSlugs[] = $d->course_slug;
                        $courses[] = ['slug' => $d->course_slug, 'name' => $d->course_short_name];
                    }
                }
            }

            /** StreamList */
            if (!empty($d->stream_slug)) {
                if (!empty($data['course'])) {
                    foreach ($data['course'] as $course) {
                        if ($d->course_slug == $course) {
                            if (!in_array($d->stream_slug, $streams)) {
                                $streams[] = ['slug' => $d->stream_slug, 'name' => $d->stream_name];
                            }
                        }
                    }
                } else if (!empty($data['branch'])) {
                    foreach ($data['branch'] as $branch) {
                        if ($d->specialization_slug == $branch) {
                            if (!in_array($d->stream_slug, $streams)) {
                                $streams[] = ['slug' => $d->stream_slug, 'name' => $d->stream_name];
                            }
                        }
                    }
                } else if (!empty($data['degree'])) {
                    foreach ($data['degree'] as $degree) {
                        if ($d->degree == $degree) {
                            if (!in_array($d->stream_slug, $streams)) {
                                $streams[] = ['slug' => $d->stream_slug, 'name' => $d->stream_name];
                            }
                        }
                    }
                } else {
                    if (!in_array($d->stream_slug, $streams)) {
                        $streams[] = ['slug' => $d->stream_slug, 'name' => $d->stream_name];
                    }
                }
            }

            /** BranchList */
            /*if (!empty($d->specialization_slug)) {
                if (!empty($data['course'])) {
                    foreach ($data['course'] as $course) {
                        if ($d->course_slug == $course) {
                            if (!in_array($d->specialization_slug, $branches)) {
                                $branches[] = ['slug' => $d->specialization_slug , 'name' => $d->specialization_name];
                            }
                        }
                    }
                } else if (!empty($data['stream'])) {
                    foreach ($data['stream'] as $stream) {
                        if ($d->stream_slug == $stream) {
                            if (!in_array($d->stream_slug, $branches)) {
                                $branches[] = ['slug' => $d->specialization_slug , 'name' => $d->specialization_name];
                            }
                        }
                    }
                } else if (!empty($data['degree'])) {
                    foreach ($data['degree'] as $degree) {
                        if ($d->degree == $degree) {
                            if (!in_array($d->specialization_slug, $branches)) {
                                $branches[] = ['slug' => $d->specialization_slug , 'name' => $d->specialization_name];
                            }
                        }
                    }
                } else {
                    if (!in_array($d->specialization_slug, $branches)) {
                        $branches[] = ['slug' => $d->specialization_slug , 'name' => $d->specialization_name];
                    }
                }
            }*/
        }

        $this->courses = ArrayHelper::map($courses, 'slug', 'name');
        // $this->branches = ArrayHelper::map($branches, 'slug', 'name');
        $this->streams = ArrayHelper::map($streams, 'slug', 'name');
        $this->degrees = $courseLevels;
        $this->types = array_intersect_key($this->typeList, $types);
    }

    private function loadParams($params)
    {
        if (isset($params['type'])) {
            $this->type = explode(' ', $params['type']);
        }
        if (isset($params['degree'])) {
            $this->degree = explode(' ', $params['degree']);
        }

        if (isset($params['totalFee'])) {
            $this->totalFee = explode(' ', $params['totalFee']);
        }
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($collegeId, $params, $bodyParams = [], $page = false)
    {

        $this->loadRequests($params, $bodyParams);

        if (!empty($params['CollegeCourseSearch'])) {
            $this->course = $params['CollegeCourseSearch']['course'];
            $this->stream = $params['CollegeCourseSearch']['stream'];
            // $this->branch = $params['CollegeCourseSearch']['branch'];
            $this->degree = $params['CollegeCourseSearch']['degree'];

            if (!empty($this->course)) {
                $data['course'] = $this->course;
                $this->loadCourses($collegeId, $data);
            } else if (!empty($this->stream)) {
                $data['stream'] = $this->stream;
                $this->loadCourses($collegeId, $data);
            } else if (!empty($this->degree)) {
                $data['degree'] = $this->degree;
                $this->loadCourses($collegeId, $data);
            } else {
                $this->loadCourses($collegeId);
            }
        } else {
            $this->loadCourses($collegeId);
        }

        $collection = Yii::$app->mongodb->getCollection(DocumentsCollegeProgram::COLLECTION_NAME);
        $match = [];
        $match['status'] = 1;
        $match['college_id'] = $collegeId;
        $sort = [];
        $aggregate = [];
        $aggregate = [['$addFields' => [
            'program_position_null' => [
                '$cond' => [
                    'if' => ['$eq' => ['$program_position', null]],
                    'then' => 1,
                    'else' => 0
                ]
            ],
            'course_position_null' => [
                '$cond' => [
                    'if' => ['$eq' => ['$course_position', null]],
                    'then' => 1,
                    'else' => 0
                ]
            ]
        ]]];
        $sort['program_position_null'] = 1;
        $sort['course_position_null'] = 1;
        $sort['program_position'] = 1;
        $sort['course_position'] = 1;

        if ($this->degree) {
            $this->selectedFilters['degree'] = $this->degree;
            $match['$and'][] = [
                'degree' => ['$in' => $this->degree]
            ];
        }

        if ($this->type) {
            $this->selectedFilters['type'] = $this->type;
            $match['$and'][] = [
                'type' => ['$in' => $this->type]
            ];
        }


        if (!empty($this->totalFee)) {
            $this->selectedFilters['totalFee'] = $this->totalFee;
            $feeArr = [];
            foreach ($this->totalFee as $fee) {
                $fees = array_map('intval', explode('-', $fee));
                if (isset($fees[0]) && isset($fees[1])) {
                    $feeArr[] = ['fees' => ['$gte' => $fees[0], '$lte' => $fees[1]]];
                } else if (isset($fees[0]) && $fees[0] > 500000) {
                    $feeArr[] = ['fees' => ['$gte' => $fees[0]]];
                }
            }

            $match['$and'][] = [
                '$or' => $feeArr
            ];
        }

        if (!empty($this->course)) {
            $this->selectedFilters['course'] = $this->course;
            $match['$and'][] = [
                'course_slug' => ['$in' => $this->course]
            ];
        };
        if (!empty($this->stream)) {
            $this->selectedFilters['stream'] = $this->stream;
            $match['$and'][] = [
                'stream_slug' => ['$in' => $this->stream]
            ];
        }

        /*if (!empty($this->branch)) {
            $this->selectedFilters['branch'] = $this->branch;
            $match['$and'][] = [
                'specialization_slug' => ['$in' => $this->branch]
            ];
        }*/

        $aggregate[] = [
            '$match' => $match,
        ];
        $aggregate[] = [
            '$sort' => $sort
        ];

        $aggregate[] =
            [
                '$project' => [
                    'college_id' => 1,
                    'program_id' => 1,
                    'college_program_id' => 1,
                    'program_position' => 1, 'program_slug' => 1, 'program' => 1, 'fees' => 1, 'stream_slug' => 1,
                    'stream_name' => 1, 'course_short_name' => 1, 'course_position' => 1,
                    'courseAvgFees' => 1, 'exams' => 1, 'degree' => 1,
                    'type' => 1, 'isCiPage' => 1, 'course_brochure' => 1,
                    'course' => 1, 'course_slug' => 1, 'course_id' => 1,
                    'specialization_slug' => 1,  'specialization_name' => 1,
                    'pageIndex' => 1, 'coursePageIndex' => 1
                ]
            ];

        $collegeData = $collection->aggregate($aggregate);

        $items = [];
        if (!empty($collegeData)) {
            foreach ($collegeData as $data) {
                $items[$data['program_slug']] = [
                    'name' => $data['program'],
                    'slug' => $data['program_slug'],
                    'pageIndex' => $data['pageIndex']
                ];
            }
        }

        $data = [];
        $data['programList'] = $items;
        $data['collegeData'] = $collegeData;

        $dataProvider = new ArrayDataProvider([
            'allModels' => $data,
            'pagination' => false,
        ]);


        if (!$this->validate()) {
            return $dataProvider;
        }

        return [
            'dataProvider' => $dataProvider,
            'totalProgrmsFetched' => count($collegeData)
        ];
    }


    public function parseValues($attribute, $data)
    {
        $values = [
            'degree' => $this->degrees,
            'type' => $this->types,
        ];

        if (!is_array($data) || empty($data) || !isset($values[$attribute])) {
            return null;
        }

        $result = [];
        foreach ($data as $v) {
            if (isset($values[$attribute], $values[$attribute][$v])) {
                $this->selectedFilters[$attribute][] = $v;

                $result[] = $v;
            }
        }

        if (empty($result)) {
            return null;
        }

        return $result;
    }

    public function loadRequests($params, $bodyParams)
    {
        $this->loadParams($params);

        $this->load($bodyParams);

        return $this;
    }
}
