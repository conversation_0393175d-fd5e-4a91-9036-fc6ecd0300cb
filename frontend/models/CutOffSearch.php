<?php

namespace frontend\models;

use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\College;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\CutOff;
use common\models\CutoffDetailH1Description;
use common\models\Specialization;
use common\services\CollegeService;
use yii\data\ArrayDataProvider;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * CutOffSearch represents the model behind the search form of `common\models\CutOff`.
 */
class CutOffSearch extends CutOff
{

    public $courses = [];
    public $exams = [];
    public $specializations = [];
    public $genders = [];
    public $categories = [];
    public $years = [];
    public $rounds = [];
    public $types = [];
    public $course;
    public $type;
    public $exam;
    public $specialization;
    public $gender;
    public $category;
    public $year;
    public $round;
    public $selectedFilters = [];

    public static $cutoffGender = [
        1 => 'Male',
        2 => 'Female',
        3 => 'Gender Neutral'
    ];

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['category', 'gender', 'round', 'course', 'exam', 'year'], 'safe'],
        ];
    }


    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    public function attributeLabels()
    {
        return array_merge(parent::attributeLabels(), [
            'exam' => 'Exam',
            'course' => 'Course',
            'category' => 'Category',
            'type' => 'Type',
            'gender' => 'Gender',
            'Round' => 'Round',
            'year' => 'Year'
        ]);
    }

    //cut off data load
    private function loadAllData($collegeId, $params = [])
    {
        $query = new Query();
        $query->select([
            'cd.college_id as collegeId',
            'cd.course_id as courseId',
            'cd.exam_id as examId',
            'cd.specialization_id as specializationId',
            'cd.category',
            'cd.gender',
            'cd.program_name as programName',
            'cd.type',
            'cd.round',
            'cd.opening_rank',
            'cd.closing_rank',
            'cd.percentile',
            'cd.closing_score',
            'cd.year',
            'e.display_name as examName',
            'sp.display_name as specializationName',
            'cr.short_name as courseName'
        ])
            ->from(CutOff::tableName() . ' cd')
            ->leftJoin('college c', 'cd.college_id = c.id')
            ->leftJoin('course cr', 'cd.course_id = cr.id')
            ->leftJoin('exam e', 'cd.exam_id = e.id')
            ->leftJoin(Specialization::tableName() . ' sp', 'cd.specialization_id = sp.id')
            ->where(['cd.college_id' => $collegeId])
            ->andWhere(['cd.status' => 1])
            ->andWhere(['not', ['cd.category' => null]])
            ->orderBy(['cd.year' => SORT_DESC, 'cd.exam_id' => SORT_ASC]);

        $result = $query->all();
        $courses = [];
        $rounds = [];
        $types = [];
        $categories = [];
        $exams = [];
        $specialization = [];
        $years = [];
        $genders = [];

        $roundArr = [];
        $categoriesArr = [];
        $typeArr = [];
        if (!empty($params['CutOffSearch']['round'])) {
            $roundArr  = $this->explodeData($params['CutOffSearch']['round']);
        }

        if (!empty($params['CutOffSearch']['category'])) {
            $categoriesArr  = $this->explodeData($params['CutOffSearch']['category']);
        }

        if (!empty($params['CutOffSearch']['type'])) {
            $typeArr  = $this->explodeData($params['CutOffSearch']['type']);
        }

        foreach ($result as $res) {

            /** genderList */
            if (!empty($res['gender'])) {
                if (!in_array($res['gender'], $genders)) {
                    $courseLevels[$res['gender']] = $res['gender'];
                }
            }

            //course List
            if (!empty($res['courseId'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['courseId'], $courses)) {
                            $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['courseId'], $courses)) {
                                $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['courseId'], $courses)) {
                            $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['courseId'], $courses)) {
                                $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['type'])) {
                    foreach ($typeArr as $type) {
                        if ($res['type'] == $type) {
                            if (!in_array($res['courseId'], $courses)) {
                                $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['courseId'], $courses)) {
                        $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                    }
                }
            }

            //examList
            if (!empty($res['examId'])) {
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['examId'], $exams)) {
                            $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['examId'], $exams)) {
                                $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['examId'], $exams)) {
                            $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['examId'], $exams)) {
                                $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['type'])) {
                    foreach ($typeArr as $type) {
                        if ($res['type'] == $type) {
                            if (!in_array($res['examId'], $exams)) {
                                $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['examId'], $exams)) {
                        $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                    }
                }
            }

            //categoryList
            if (!empty($res['category'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['category'], $categories)) {
                            $categories[$res['category']] = $res['category'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['category'], $categories)) {
                            $categories[$res['category']] = $res['category'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['category'], $categories)) {
                            $categories[$res['category']] = $res['category'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['category'], $categories)) {
                                $categories[$res['category']] = $res['category'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['type'])) {
                    foreach ($typeArr as $type) {
                        if ($res['type'] == $type) {
                            if (!in_array($res['category'], $categories)) {
                                $categories[$res['category']] = $res['category'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['category'], $categories)) {
                        $categories[$res['category']] = $res['category'];
                    }
                }
            }

            //year list
            if (!empty($res['year'])) {
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['year'], $years)) {
                            $years[$res['year']] = $res['year'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['year'], $years)) {
                            $years[$res['year']] = $res['year'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['year'], $years)) {
                                $years[$res['year']] =  $res['year'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['year'], $years)) {
                                $years[$res['year']] =  $res['year'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['type'])) {
                    foreach ($typeArr as $type) {
                        if ($res['type'] == $type) {
                            if (!in_array($res['year'], $years)) {
                                $years[$res['year']] =  $res['year'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['year'], $years)) {
                        $years[$res['year']] =  $res['year'];
                    }
                }
            }

            //round list
            if (!empty($res['round'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['round'], $rounds)) {
                            $rounds[$res['round']] = $res['round'];
                        }
                    }
                }
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['round'], $rounds)) {
                            $rounds[$res['round']] = $res['round'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['round'], $rounds)) {
                            $rounds[$res['round']] = $res['round'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['round'], $rounds)) {
                                $rounds[$res['round']] = $res['round'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['type'])) {
                    foreach ($typeArr as $type) {
                        if ($res['category'] == $type) {
                            if (!in_array($res['round'], $rounds)) {
                                $rounds[$res['round']] = $res['round'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['round'], $rounds)) {
                        $rounds[$res['round']] = $res['round'];
                    }
                }
            }

            if (!empty($res['type'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['type'], $types)) {
                            $types[$res['type']] = $res['type'];
                        }
                    }
                }
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['type'], $types)) {
                            $types[$res['type']] = $res['type'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['type'], $types)) {
                            $types[$res['type']] = $res['type'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['type'], $types)) {
                                $types[$res['type']] = $res['type'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['type'], $types)) {
                                $types[$res['type']] = $res['type'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['type'], $types)) {
                        $types[$res['type']] = $res['type'];
                    }
                }
            }
        }

        $this->courses = ArrayHelper::map($courses, 'id', 'name');
        $this->exams = ArrayHelper::map($exams, 'id', 'name');
        $this->categories = ArrayHelper::map($this->formateCategory($categories), 'id', 'name');
        $this->types = ArrayHelper::map($this->formateType($types), 'id', 'name');
        $this->genders = ArrayHelper::map($this->formateGender($result), 'id', 'name');
        $this->years = $years;
        $this->rounds = ArrayHelper::map($this->formateRound($rounds), 'id', 'name');
        // dd($this->types);
    }

    //program page cut off filter data
    private function loadCourseBasedData($collegeId, $params = [])
    {
        $query = new Query();
        $query->select([
            'cd.college_id as collegeId',
            'cd.course_id as courseId',
            'cd.exam_id as examId',
            'cd.specialization_id as specializationId',
            'cd.category',
            'cd.gender',
            'cd.program_name as programName',
            'cd.year',
            'cd.round',
            'cd.opening_rank',
            'cd.closing_rank',
            'cd.percentile',
            'cd.closing_score',
            'cd.year',
            'e.display_name as examName',
            'sp.display_name as specializationName',
            'cr.short_name as courseName'
        ])
            ->from(CutOff::tableName() . ' cd')
            ->leftJoin('college c', 'cd.college_id = c.id')
            ->leftJoin('course cr', 'cd.course_id = cr.id')
            ->leftJoin('exam e', 'cd.exam_id = e.id')
            ->leftJoin(Specialization::tableName() . ' sp', 'cd.specialization_id = sp.id')
            ->where(['cd.college_id' => $collegeId])
            ->andWhere(['cd.status' => 1])
            ->andWhere(['not', ['cd.category' => null]])
            ->orderBy(['cd.year' => SORT_DESC, 'cd.exam_id' => SORT_ASC]);

        $result = $query->all();
        $courses = [];
        $rounds = [];
        $categories = [];
        $exams = [];
        $specialization = [];
        $years = [];
        $genders = [];

        $roundArr = [];
        $categoriesArr = [];
        if (!empty($params['CutOffSearch']['round'])) {
            $roundArr  = $this->explodeData($params['CutOffSearch']['round']);
        }

        if (!empty($params['CutOffSearch']['category'])) {
            $categoriesArr  = $this->explodeData($params['CutOffSearch']['category']);
        }

        foreach ($result as $res) {

            /** genderList */
            if (!empty($res['gender'])) {
                if (!in_array($res['gender'], $genders)) {
                    $courseLevels[$res['gender']] = $res['gender'];
                }
            }

            //course List
            if (!empty($res['courseId'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['courseId'], $courses) && $params['CutOffSearch']['course'] == $res['courseId']) {
                            $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['courseId'], $courses) && $params['CutOffSearch']['course'] == $res['courseId']) {
                                $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['courseId'], $courses) && $params['CutOffSearch']['course'] == $res['courseId']) {
                            $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['courseId'], $courses) && $params['CutOffSearch']['course'] == $res['courseId']) {
                                $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                            }
                        }
                    }
                } else {
                    if (!empty($params['CutOffSearch']['course']) && $params['CutOffSearch']['course'] == $res['courseId']) {
                        $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                    }
                }
            }

            //examList
            if (!empty($res['examId'])) {
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['examId'], $exams)) {
                            $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['examId'], $exams)) {
                                $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['examId'], $exams)) {
                            $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['examId'], $exams)) {
                                $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['examId'], $exams)) {
                        $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                    }
                }
            }

            //categoryList
            if (!empty($res['category'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['category'], $categories)) {
                            $categories[$res['category']] = $res['category'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['category'], $categories)) {
                            $categories[$res['category']] = $res['category'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['category'], $categories)) {
                            $categories[$res['category']] = $res['category'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['category'], $categories)) {
                                $categories[$res['category']] = $res['category'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['category'], $categories)) {
                        $categories[$res['category']] = $res['category'];
                    }
                }
            }

            //year list
            if (!empty($res['year'])) {
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['year'], $years)) {
                            $years[$res['year']] = $res['year'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['year'], $years)) {
                            $years[$res['year']] = $res['year'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['year'], $years)) {
                                $years[$res['year']] =  $res['year'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['year'], $years)) {
                                $years[$res['year']] =  $res['year'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['year'], $years)) {
                        $years[$res['year']] =  $res['year'];
                    }
                }
            }

            //round list
            if (!empty($res['round'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['round'], $rounds)) {
                            $rounds[$res['round']] = $res['round'];
                        }
                    }
                }
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['round'], $rounds)) {
                            $rounds[$res['round']] = $res['round'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['round'], $rounds)) {
                            $rounds[$res['round']] = $res['round'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['round'], $rounds)) {
                                $rounds[$res['round']] = $res['round'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['round'], $rounds)) {
                        $rounds[$res['round']] = $res['round'];
                    }
                }
            }
        }
        // dd($courses);
        $this->courses = ArrayHelper::map($courses, 'id', 'name');
        $this->exams = ArrayHelper::map($exams, 'id', 'name');
        $this->categories = ArrayHelper::map($this->formateCategory($categories), 'id', 'name');
        $this->genders = ArrayHelper::map($this->formateGender($result), 'id', 'name');
        $this->years = $years;
        $this->rounds = ArrayHelper::map($this->formateRound($rounds), 'id', 'name');
    }

    private function loadExamCutoffData($collegeId, $examId, $params = [])
    {
        $query = new Query();
        $query->select([
            'cd.college_id as collegeId',
            'cd.course_id as courseId',
            'cd.exam_id as examId',
            'cd.specialization_id as specializationId',
            'cd.category',
            'cd.gender',
            'cd.program_name as programName',
            'cd.year',
            'cd.round',
            'cd.opening_rank',
            'cd.closing_rank',
            'cd.percentile',
            'cd.closing_score',
            'cd.type',
            'e.display_name as examName',
            'sp.display_name as specializationName',
            'cr.short_name as courseName'
        ])
            ->from(CutOff::tableName() . ' cd')
            ->leftJoin('college c', 'cd.college_id = c.id')
            ->leftJoin('course cr', 'cd.course_id = cr.id')
            ->leftJoin('exam e', 'cd.exam_id = e.id')
            ->leftJoin(Specialization::tableName() . ' sp', 'cd.specialization_id = sp.id')
            ->where(['cd.college_id' => $collegeId])
            ->andWhere(['cd.exam_id' => $examId])
            ->andWhere(['cd.status' => 1])
            ->andWhere(['not', ['cd.category' => null]])
            ->orderBy(['cd.year' => SORT_DESC, 'cd.exam_id' => SORT_ASC]);
        // dd($query->createCommand()->getRawSql());
        $result = $query->all();
        // dd($result);
        $courses = [];
        $rounds = [];
        $categories = [];
        $exams = [];
        $specialization = [];
        $types = [];
        $years = [];
        $genders = [];

        $roundArr = [];
        $categoriesArr = [];
        $typeArr = [];

        if (!empty($params['CutOffSearch']['round'])) {
            $roundArr  = $this->explodeData($params['CutOffSearch']['round']);
        }

        if (!empty($params['CutOffSearch']['category'])) {
            $categoriesArr  = $this->explodeData($params['CutOffSearch']['category']);
        }

        if (!empty($params['CutOffSearch']['type'])) {
            $typeArr  = $this->explodeData($params['CutOffSearch']['type']);
        }

        foreach ($result as $res) {

            /** genderList */
            if (!empty($res['gender'])) {
                if (!in_array($res['gender'], $genders)) {
                    $courseLevels[$res['gender']] = $res['gender'];
                }
            }

            //course List
            if (!empty($res['courseId'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['courseId'], $courses)) {
                            $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['courseId'], $courses)) {
                                $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['courseId'], $courses)) {
                            $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['courseId'], $courses)) {
                                $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['type'])) {
                    foreach ($typeArr as $type) {
                        if ($res['type'] == $type) {
                            if (!in_array($res['courseId'], $courses)) {
                                $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['courseId'], $courses)) {
                        $courses[$res['courseName']] = ['id' => $res['courseId'], 'name' => $res['courseName']];
                    }
                }
            }

            //examList
            if (!empty($res['examId'])) {
                // if (!empty($params['CutOffSearch']['course'])) {
                //     if ($res['courseId'] == $params['CutOffSearch']['course']) {
                //         if (!in_array($res['examId'], $exams)) {
                //             $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                //         }
                //     }
                // } else if (!empty($params['CutOffSearch']['category'])) {
                //     foreach ($categoriesArr as $category) {
                //         if ($res['category'] == $category) {
                //             if (!in_array($res['examId'], $exams)) {
                //                 $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                //             }
                //         }
                //     }
                // } else if (!empty($params['CutOffSearch']['year'])) {
                //     if ($res['year'] == $params['CutOffSearch']['year']) {
                //         if (!in_array($res['examId'], $exams)) {
                //             $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                //         }
                //     }
                // } else if (!empty($params['CutOffSearch']['round'])) {
                //     foreach ($roundArr as $round) {
                //         if ($res['round'] == $round) {
                //             if (!in_array($res['examId'], $exams)) {
                //                 $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                //             }
                //         }
                //     }
                // } else {
                if (!in_array($res['examId'], $exams)) {
                    $exams[$res['examName']] = ['id' => $res['examId'], 'name' => $res['examName']];
                }
                // }
            }

            //categoryList
            if (!empty($res['category'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['category'], $categories)) {
                            $categories[$res['category']] = $res['category'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['category'], $categories)) {
                            $categories[$res['category']] = $res['category'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['category'], $categories)) {
                            $categories[$res['category']] = $res['category'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['category'], $categories)) {
                                $categories[$res['category']] = $res['category'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['type'])) {
                    foreach ($typeArr as $type) {
                        if ($res['type'] == $type) {
                            if (!in_array($res['category'], $categories)) {
                                $categories[$res['category']] = $res['category'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['category'], $categories)) {
                        $categories[$res['category']] = $res['category'];
                    }
                }
            }

            //year list
            if (!empty($res['year'])) {
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['year'], $years)) {
                            $years[$res['year']] = $res['year'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['year'], $years)) {
                            $years[$res['year']] = $res['year'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['year'], $years)) {
                                $years[$res['year']] =  $res['year'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['year'], $years)) {
                                $years[$res['year']] =  $res['year'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['type'])) {
                    foreach ($typeArr as $type) {
                        if ($res['type'] == $type) {
                            if (!in_array($res['year'], $years)) {
                                $years[$res['year']] =  $res['year'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['year'], $years)) {
                        $years[$res['year']] =  $res['year'];
                    }
                }
            }

            //round list
            if (!empty($res['round'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['round'], $rounds)) {
                            $rounds[$res['round']] = $res['round'];
                        }
                    }
                }
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['round'], $rounds)) {
                            $rounds[$res['round']] = $res['round'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['round'], $rounds)) {
                            $rounds[$res['round']] = $res['round'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['round'], $rounds)) {
                                $rounds[$res['round']] = $res['round'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['type'])) {
                    foreach ($typeArr as $type) {
                        if ($res['type'] == $type) {
                            if (!in_array($res['round'], $rounds)) {
                                $rounds[$res['round']] = $res['round'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['round'], $rounds)) {
                        $rounds[$res['round']] = $res['round'];
                    }
                }
            }

            if (!empty($res['type'])) {
                if (!empty($params['CutOffSearch']['exam'])) {
                    if ($res['examId'] == $params['CutOffSearch']['exam']) {
                        if (!in_array($res['type'], $types)) {
                            $types[$res['type']] = $res['type'];
                        }
                    }
                }
                if (!empty($params['CutOffSearch']['course'])) {
                    if ($res['courseId'] == $params['CutOffSearch']['course']) {
                        if (!in_array($res['type'], $types)) {
                            $types[$res['type']] = $res['type'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['year'])) {
                    if ($res['year'] == $params['CutOffSearch']['year']) {
                        if (!in_array($res['type'], $types)) {
                            $types[$res['type']] = $res['type'];
                        }
                    }
                } else if (!empty($params['CutOffSearch']['category'])) {
                    foreach ($categoriesArr as $category) {
                        if ($res['category'] == $category) {
                            if (!in_array($res['type'], $types)) {
                                $types[$res['type']] = $res['type'];
                            }
                        }
                    }
                } else if (!empty($params['CutOffSearch']['round'])) {
                    foreach ($roundArr as $round) {
                        if ($res['round'] == $round) {
                            if (!in_array($res['type'], $types)) {
                                $types[$res['type']] = $res['type'];
                            }
                        }
                    }
                } else {
                    if (!in_array($res['type'], $types)) {
                        $types[$res['type']] = $res['type'];
                    }
                }
            }
        }

        $this->courses = ArrayHelper::map($courses, 'id', 'name');
        $this->exams = ArrayHelper::map($exams, 'id', 'name');
        $this->categories = ArrayHelper::map($this->formateCategory($categories), 'id', 'name');
        $this->genders = ArrayHelper::map($this->formateGender($result), 'id', 'name');
        $this->years = $years;
        $this->rounds = ArrayHelper::map($this->formateRound($rounds), 'id', 'name');
        $this->types = ArrayHelper::map($this->formateType($types), 'id', 'name');
    }

    private function loadParams($params)
    {
        if (isset($params['gender'])) {
            $this->gender = explode(' ', $params['gender']);
        }
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($collegeId, $params, $bodyParams = [], $examType = null, $courselatestName = null)
    {
        // dd($params);
        $this->loadRequests($params, $bodyParams);
        if (!empty($examType)) {
            $this->loadExamBasedData($collegeId, $params, $examType);
        } else {
            if (!empty($params['CutOffSearch'])) {
                $this->loadAllData($collegeId, $params);
            } else {
                $this->loadAllData($collegeId);
            }
        }

        $query = new Query();
        $query->select([
            'cd.college_id as collegeId',
            'cd.course_id as courseId',
            'cd.exam_id as examId',
            'cd.specialization_id as specializationId',
            'cd.category',
            'cd.program_id as programId',
            'cd.gender',
            'cd.program_name as programName',
            'cd.year',
            'cd.round',
            'cd.opening_rank',
            'cd.closing_rank',
            'cd.percentile',
            'cd.closing_score',
            'cd.type',
            'e.display_name as examName',
            'sp.slug as specializationSlug',
            'sp.display_name as specializationName',
            'cr.slug as courseSlug',
            'cr.name as courseName',
            'cr.short_name as courseShortName'
        ])
            ->from(CutOff::tableName() . ' cd')
            ->leftJoin('college c', 'cd.college_id = c.id')
            ->leftJoin('course cr', 'cd.course_id = cr.id')
            ->leftJoin('exam e', 'cd.exam_id = e.id')
            ->leftJoin(Specialization::tableName() . ' sp', 'cd.specialization_id = sp.id')
            ->where(['cd.college_id' => $collegeId])
            ->andWhere(['not', ['cd.category' => null]])
            ->andWhere(['cd.status' => 1])
            ->orderBy(['cd.year' => SORT_DESC, 'cd.exam_id' => SORT_ASC]);

        if (!empty($params['CutOffSearch']['course'])) {
            $this->selectedFilters['course'] = $params['CutOffSearch']['course'];
            $query->andWhere(['in', 'cd.course_id', $params['CutOffSearch']['course']]);
        }

        if (!empty($params['CutOffSearch']['exam'])) {
            $this->selectedFilters['exam'] = $params['CutOffSearch']['exam'];
            $query->andWhere(['in', 'cd.exam_id', $params['CutOffSearch']['exam']]);
        }

        if (!empty($params['CutOffSearch']['specialization'])) {
            $this->selectedFilters['specialization'] = $params['CutOffSearch']['specialization'];
            $query->andWhere(['in', 'cd.specialization_id', $params['CutOffSearch']['specialization']]);
        }

        if (!empty($params['CutOffSearch']['round'])) {
            $this->selectedFilters['round'] = $params['CutOffSearch']['round'];
            $roundExplodeArr = $this->explodeData($params['CutOffSearch']['round']);
            $query->andWhere(['in', 'cd.round', $roundExplodeArr]);
        }

        if (!empty($params['CutOffSearch']['gender'])) {
            $this->selectedFilters['gender'] = $params['CutOffSearch']['gender'];
            $genderExplodeArr = $this->explodeData($params['CutOffSearch']['gender']);
            $query->andWhere(['in', 'cd.gender', $genderExplodeArr]);
        }

        if (!empty($params['CutOffSearch']['category'])) {
            $this->selectedFilters['category'] = $params['CutOffSearch']['category'];
            $cateExplodeArr = $this->explodeData($params['CutOffSearch']['category']);
            $query->andWhere(['in', 'cd.category', $cateExplodeArr]);
        }

        if (!empty($params['CutOffSearch']['type'])) {
            $this->selectedFilters['type'] = $params['CutOffSearch']['type'];
            $typeExplodeArr = $this->explodeData($params['CutOffSearch']['type']);
            $query->andWhere(['in', 'cd.type', $typeExplodeArr]);
        }

        if (!empty($params['CutOffSearch']['year'])) {
            $this->selectedFilters['year'] = $params['CutOffSearch']['year'];
            $query->andWhere(['in', 'cd.year', $params['CutOffSearch']['year']]);
        }

        $collegeData = $query->all();

        $dataArr = [];
        if (!empty($collegeData)) {
            $dataArr = $this->formateData($collegeData, $params);
        }

        if (!empty($courselatestName) && isset($dataArr[$courselatestName])) {
            $dataArr = [$courselatestName => $dataArr[$courselatestName]];
        }

        $dataProvider = new ArrayDataProvider([
            'allModels' => $dataArr,
            'pagination' => false,
        ]);

        if (!$this->validate()) {
            return $dataProvider;
        }

        return [
            'dataProvider' => $dataProvider
        ];
    }

    /**
     * Creates data provider instance with search query applied based
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function searchExam($collegeId, $params, $bodyParams = [], $examType = null)
    {
        // dd($params);
        $this->loadRequests($params, $bodyParams);
        // if (!empty($examType)) {
        //     $this->loadExamBasedData($collegeId, $params, $examType);
        // } else {
        //     if (!empty($params['CutOffSearch'])) {
        //         $this->loadAllData($collegeId, $params);
        //     } else {
        //         $this->loadAllData($collegeId);
        //     }
        // }

        $this->loadExamCutoffData($collegeId, $examType, $params);

        $query = new Query();
        $query->select([
            'cd.college_id as collegeId',
            'cd.course_id as courseId',
            'cd.program_id as programId',
            'cd.exam_id as examId',
            'cd.specialization_id as specializationId',
            'cd.category',
            'cd.gender',
            'cd.program_name as programName',
            'cd.year',
            'cd.round',
            'cd.opening_rank',
            'cd.closing_rank',
            'cd.percentile',
            'cd.closing_score',
            'cd.year',
            'e.display_name as examName',
            'sp.slug as specializationSlug',
            'sp.display_name as specializationName',
            'cr.slug as courseSlug',
            'cr.name as courseName',
            'cr.short_name as courseShortName'
        ])
            ->from(CutOff::tableName() . ' cd')
            ->leftJoin('college c', 'cd.college_id = c.id')
            ->leftJoin('course cr', 'cd.course_id = cr.id')
            ->leftJoin('exam e', 'cd.exam_id = e.id')
            ->leftJoin(Specialization::tableName() . ' sp', 'cd.specialization_id = sp.id')
            ->where(['cd.college_id' => $collegeId])
            ->andWhere(['cd.exam_id' => $examType])
            ->andWhere(['not', ['cd.category' => null]])
            ->orderBy(['cd.year' => SORT_DESC, 'cd.exam_id' => SORT_ASC]);

        if (!empty($params['CutOffSearch']['course'])) {
            $this->selectedFilters['course'] = $params['CutOffSearch']['course'];
            $query->andWhere(['in', 'cd.course_id', $params['CutOffSearch']['course']]);
        }

        // if (!empty($params['CutOffSearch']['exam'])) {
        //     $this->selectedFilters['exam'] = $params['CutOffSearch']['exam'];
        //     $query->andWhere(['in', 'cd.exam_id', $params['CutOffSearch']['exam']]);
        // }

        // if (!empty($params['CutOffSearch']['specialization'])) {
        //     $this->selectedFilters['specialization'] = $params['CutOffSearch']['specialization'];
        //     $query->andWhere(['in', 'cd.specialization_id', $params['CutOffSearch']['specialization']]);
        // }

        if (!empty($params['CutOffSearch']['round'])) {
            $this->selectedFilters['round'] = $params['CutOffSearch']['round'];
            $roundExplodeArr = $this->explodeData($params['CutOffSearch']['round']);
            $query->andWhere(['in', 'cd.round', $roundExplodeArr]);
        }

        if (!empty($params['CutOffSearch']['gender'])) {
            $this->selectedFilters['gender'] = $params['CutOffSearch']['gender'];
            $genderExplodeArr = $this->explodeData($params['CutOffSearch']['gender']);
            $query->andWhere(['in', 'cd.gender', $genderExplodeArr]);
        }

        if (!empty($params['CutOffSearch']['category'])) {
            $this->selectedFilters['category'] = $params['CutOffSearch']['category'];
            $cateExplodeArr = $this->explodeData($params['CutOffSearch']['category']);
            $query->andWhere(['in', 'cd.category', $cateExplodeArr]);
        }

        if (!empty($params['CutOffSearch']['type'])) {
            $this->selectedFilters['type'] = $params['CutOffSearch']['type'];
            $typeExplodeArr = $this->explodeData($params['CutOffSearch']['type']);
            $query->andWhere(['in', 'cd.type', $typeExplodeArr]);
        }

        if (!empty($params['CutOffSearch']['year'])) {
            $this->selectedFilters['year'] = $params['CutOffSearch']['year'];
            $query->andWhere(['in', 'cd.year', $params['CutOffSearch']['year']]);
        }

        $collegeData = $query->all();

        $dataArr = [];
        if (!empty($collegeData)) {
            $dataArr = $this->formateData($collegeData, $params);
        }

        $dataProvider = new ArrayDataProvider([
            'allModels' => $dataArr,
            'pagination' => false,
        ]);

        if (!$this->validate()) {
            return $dataProvider;
        }

        return [
            'dataProvider' => $dataProvider
        ];
    }

    public function parseValues($attribute, $data)
    {
        $values = [
            'gender' => $this->genders,
        ];

        if (!is_array($data) || empty($data) || !isset($values[$attribute])) {
            return null;
        }

        $result = [];
        foreach ($data as $v) {
            if (isset($values[$attribute], $values[$attribute][$v])) {
                $this->selectedFilters[$attribute][] = $v;

                $result[] = $v;
            }
        }

        if (empty($result)) {
            return null;
        }

        return $result;
    }

    public function loadRequests($params, $bodyParams)
    {
        $this->loadParams($params);

        $this->load($bodyParams);

        return $this;
    }

    private function explodeData($data)
    {
        $items = [];
        $arr = explode('_', $data);
        $items[] = $arr[0];

        return $items;
    }

    private function formateGender(array $arr)
    {
        $gender = array_flip(CollegeHelper::$cutoffGender);
        if (empty($arr)) {
            return [];
        }

        $data  = array_unique(array_column($arr, 'gender'));
        $items = [];
        foreach ($data as $v) {
            if (isset($gender[$v])) {
                $items[] = [
                    'id' => $v . '_gender',
                    'name' => $gender[$v]
                ];
            }
        }
        sort($items);

        return $items;
    }

    private function formateCategory(array $arr)
    {
        $category = array_flip(CollegeService::getCategory());
        if (empty($arr)) {
            return [];
        }

        sort($arr);

        $items = [];
        foreach ($arr as $v) {
            if (isset($category[$v])) {
                $items[] = [
                    'id' => $v . '_cate',
                    'name' => $category[$v]
                ];
            }
        }

        return $items;
    }

    private function formateType(array $arr)
    {
        $type = array_flip(CollegeHelper::$cutOffType);
        if (empty($arr)) {
            return [];
        }

        sort($arr);

        $items = [];
        foreach ($arr as $v) {
            if (isset($type[$v])) {
                $items[] = [
                    'id' => $v . '_type',
                    'name' => $type[$v]
                ];
            }
        }

        return $items;
    }

    private function formateRound(array $arr)
    {
        if (empty($arr)) {
            return [];
        }

        $items = [];
        foreach ($arr as $d) {
            $items[] = ['id' => $d . '_round', 'name' => 'Round ' . $d];
        }

        sort($items);

        return $items;
    }

    private function formateData(array $collegeData, $params)
    {
        $items = [];

        $category = !empty($params['CutOffSearch']['category'])
            ? self::explodeData($params['CutOffSearch']['category'])
            : [1];

        $gender = !empty($params['CutOffSearch']['gender'])
            ? self::explodeData($params['CutOffSearch']['gender'])
            : [3];

        $availableCategories = array_unique(array_filter(array_column($collegeData, 'category')));
        $availableGenders = array_unique(array_filter(array_column($collegeData, 'gender')));

        $programRoundMap = [];
        $filteredItems = [];
        $allCourseNames = array_unique(array_column($collegeData, 'courseName'));

        foreach ($collegeData as $data) {
            if (!self::isValidCategoryGender($data, $category, $gender)) {
                continue;
            }
            self::mapData($filteredItems, $data, $params, $category, $programRoundMap);
        }

        $addedCourses = array_keys($filteredItems);
        $missingCourses = array_diff($allCourseNames, $addedCourses);

        if (!empty($missingCourses)) {
            foreach ($collegeData as $data) {
                if (!in_array($data['courseName'], $missingCourses)) {
                    continue;
                }
                if (!self::isValidCategoryGender($data, $availableCategories, $availableGenders)) {
                    continue;
                }
                self::mapData($filteredItems, $data, $params, $availableCategories, $programRoundMap);
            }
        }

        $items = $filteredItems;

        foreach ($items as $courseName => &$courseData) {
            foreach ($courseData['specArr'] as $examName => &$examData) {
                foreach (['opclArr', 'scoreArr', 'percentileArr'] as $type) {
                    $this->sortAndSetRoundName($examData, $type, $programRoundMap);
                }
            }
        }
        unset($courseData, $examData);

        return $items;
    }

    private static function mapData(&$items, $data, $params, $category, &$programRoundMap)
    {
        $courseName = $data['courseName'];
        $examName = $data['examName'];
        $hasSpec = !empty($data['specializationId']);

        $examItem = &$items[$courseName]['specArr'][$examName];
        $examItem['examId'] = $data['examId'];
        $examItem['examName'] = $examName;
        $examItem['courseSlug'] = $data['courseSlug'];
        $examItem['courseId'] = $data['courseId'];
        $examItem['courseName'] = $courseName;
        $examItem['courseShortName'] = $data['courseShortName'];
        $examItem['collegeId'] = $data['collegeId'];
        $examItem['category'] = !empty($params['CutOffSearch']['category']) ? self::explodeData($params['CutOffSearch']['category']) : '';

        $key = $hasSpec ? 'valueArr' : 'courseValueArr';
        $map = [
            'closing_rank'  => 'opclArr',
            'closing_score' => 'scoreArr',
            'percentile'    => 'percentileArr',
        ];

        foreach ($map as $field => $type) {
            if (!empty($data[$field]) && (int)$data[$field] > 0) {
                $target = &$examItem[$type]['data'];
                $value = $data[$field];
                $target[$key][$data['programName']][$data['year']][] = $value;
                $target[$key . '_programIdMap'][$data['programName']] = $data['programId'];

                if (!empty($data['round'])) {
                    $programKey = $data['programName'] . '-' . $data['year'] . '-' . $value;
                    $programRoundMap[$programKey] = $data['round'];
                }

                $target['yearArr'][] = $data['year'];
                $target['Cname'] = $courseName;
                $target['Csname'] = $data['courseShortName'];
                if ($hasSpec) {
                    $target['specName'] = $data['specializationName'];
                    $target['specId'] = $data['specializationId'];
                }

                unset($target);
                break;
            }
        }
    }


    private static function isValidCategoryGender($data, $categoryList, $genderList)
    {
        return !empty($data['category']) &&
            !empty($data['gender']) &&
            in_array($data['category'], $categoryList) &&
            in_array($data['gender'], $genderList);
    }

    private function sortAndSetRoundName(&$examData, $type, $programRoundMap)
    {
        if (!empty($examData[$type]['data']['courseValueArr'])) {
            $courseValueArr = $examData[$type]['data']['courseValueArr'];
            $targetKey = 'courseValueArr';
        } elseif (!empty($examData[$type]['data']['valueArr'])) {
            $courseValueArr = $examData[$type]['data']['valueArr'];
            $targetKey = 'valueArr';
        } else {
            return; // No data to process
        }

        $filteredCourseValueArr = [];
        $roundsFound = [];

        // Filter to only keep the latest year data
        if (!empty($examData[$type]['data']['yearArr'])) {
            $yearArr = $examData[$type]['data']['yearArr'];
            rsort($yearArr); // Sort years in descending order
            $latestYear = $yearArr[0]; // Latest year
            $examData[$type]['data']['yearArr'] = [$latestYear]; // Retain only the latest year

            // Filter courseValueArr to keep only latest year data
            foreach ($courseValueArr as $programName => $yearWiseRanks) {
                foreach ($yearWiseRanks as $year => $ranks) {
                    if ($year != $latestYear) {
                        unset($courseValueArr[$programName][$year]);
                    }
                }
                // Remove the program if it has no data for the latest year
                if (empty($courseValueArr[$programName])) {
                    unset($courseValueArr[$programName]);
                }
            }
        }

        // Step 1: Find all used rounds
        foreach ($courseValueArr as $programName => $yearWiseRanks) {
            foreach ($yearWiseRanks as $year => $ranks) {
                foreach ($ranks as $rank) {
                    $programKey = $programName . '-' . $year . '-' . $rank;
                    if (!empty($programRoundMap[$programKey])) {
                        $roundsFound[] = (int)$programRoundMap[$programKey];
                    }
                }
            }
        }

        if (empty($roundsFound)) {
            $examData[$type]['data'][$targetKey] = [];
            $examData['roundName'] = '-';
            return;
        }

        // Step 2: Get max round
        $maxRound = max($roundsFound);

        // Step 3: Filter by max round and track max rank for sorting
        $programMaxRanks = [];
        $min = $max = null;
        $minProg = $maxProg = null;

        foreach ($courseValueArr as $programName => $yearWiseRanks) {
            foreach ($yearWiseRanks as $year => $ranks) {
                foreach ($ranks as $rank) {
                    $programKey = $programName . '-' . $year . '-' . $rank;
                    $round = isset($programRoundMap[$programKey]) ? (int)$programRoundMap[$programKey] : 0;

                    if ($round === $maxRound) {
                        $current = $filteredCourseValueArr[$programName][$year] ?? null;
                        $rankFloat = floatval($rank);

                        if ($type === 'opclArr') {
                            if ($current === null || $rankFloat < floatval($current)) {
                                $filteredCourseValueArr[$programName][$year] = $rank;
                            }
                        } else {
                            if ($current === null || $rankFloat > floatval($current)) {
                                $filteredCourseValueArr[$programName][$year] = $rank;
                            }
                        }

                        if ($type === 'opclArr') {
                            if (!isset($programMaxRanks[$programName]) || $rankFloat < $programMaxRanks[$programName]) {
                                $programMaxRanks[$programName] = $rankFloat;
                            }
                        } else {
                            if (!isset($programMaxRanks[$programName]) || $rankFloat > $programMaxRanks[$programName]) {
                                $programMaxRanks[$programName] = $rankFloat;
                            }
                        }
                    }
                }
            }
        }

        // Step 4: Sort conditionally
        if ($type === 'opclArr') {
            asort($programMaxRanks); // Ascending: low to high
        } else {
            arsort($programMaxRanks); // Descending: high to low
        }

        $sortedFilteredArr = [];
        foreach (array_keys($programMaxRanks) as $programName) {
            if (isset($filteredCourseValueArr[$programName])) {
                $sortedFilteredArr[$programName] = $filteredCourseValueArr[$programName];
            }
        }

        if (!empty($sortedFilteredArr)) {
            $firstProgramName = array_key_first($sortedFilteredArr);
            $firstValue = reset($sortedFilteredArr[$firstProgramName]);

            $lastProgramName = array_key_last($sortedFilteredArr);
            $lastValue = reset($sortedFilteredArr[$lastProgramName]);

            $min = floatval($firstValue);
            $minProg = $firstProgramName;
            $max = floatval($lastValue);
            $maxProg = $lastProgramName;
        }

        $examData[$type]['data'][$targetKey] = $sortedFilteredArr;
        $otherKey = $targetKey === 'courseValueArr' ? 'valueArr' : 'courseValueArr';
        unset($examData[$type]['data'][$otherKey]);

        // Meta assignment
        $examData['type'] = $type;
        $examData['roundName'] = (string)$maxRound;
        $seoInfo = $this->cutOffSeoInfo($examData, $min, $max, $minProg, $maxProg, $latestYear);
        $examData['seoInfo'] = $seoInfo;
    }

    private function cutOffSeoInfo($examData, $min, $max, $minProg, $maxProg, $latestYear)
    {
        $data = [];

        // Detect if only one program
        $valueKey = $examData['type'] ?? 'opclArr';
        $courseValueArr = $examData[$valueKey]['data']['courseValueArr'] ?? [];
        $isSingleProgram = count($courseValueArr) === 1;

        $seoInfo = CutoffDetailH1Description::find()
            ->select(['h1', 'description'])
            ->where(['course_id' => $examData['courseId']])
            ->andWhere(['college_id' => $examData['collegeId']])
            ->andWhere(['status' => CutoffDetailH1Description::STATUS_ACTIVE])
            ->asArray()
            ->one();

        if (empty($seoInfo)) {
            $seoInfo = [
                'h1' => DataHelper::$cutOffH1DesDefaultValue['h1'],
                'description' => $isSingleProgram ? DataHelper::$cutOffH1DesDefaultValue['single_desc'] :
                    DataHelper::$cutOffH1DesDefaultValue['multi_desc']
            ];
        }

        $getCollege = College::find()->select(['name', 'display_name'])->where(['id' => $examData['collegeId']])->asArray()->one();
        $cutoffSources = ['opclArr' => 'Closing Rank', 'percentileArr' => 'Percentile', 'scoreArr' => 'Closing Score'];
        $type = isset($cutoffSources[$examData['type']]) ? $cutoffSources[$examData['type']] : '';
        $categoryMap = array_flip(CollegeService::getCategory());
        $selectedCategoryName = isset($examData['category']) && !empty($examData['category'][0]) && !empty($categoryMap[$examData['category'][0]]) ? $categoryMap[$examData['category'][0]] : '';

        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{College_Name}' => $getCollege['display_name'] ?? $getCollege['name'],
                '{Degree_Name}' => $examData['courseShortName'],
                '{Exam_Name}' => $examData['examName'],
                '{Cutoff_Round_Number}' => $examData['roundName'] ?? '',
                '{year}' => $latestYear,
                '{Rank/Score/Perecentile}' => $type,
                '{Category_Name}' => empty($selectedCategoryName) ? 'General' : $selectedCategoryName,
                '{Minimum_Closing_Rank/Score/Percentile}' => $type == 'Percentile' ? $min . '%' : $min,
                '{Maximum_Closing_Rank/Score/Percentile}' => $type == 'Percentile' ? $max . '%' : $max,
                '{Min_Program_Name}' => $minProg,
                '{Max_Program_Name}' => $maxProg,
            ]);
        }

        return $data;
    }

    protected function getGender($array)
    {
        if (empty($array)) {
            return 1;
        }

        $genders = array_flip(CollegeHelper::$cutoffGender);

        foreach ($array as $arr) {
            if ($genders[$arr] == 'All') {
                return $arr;
            } elseif ($genders[$arr] == 'Gender Neutral') {
                return $arr;
            } elseif ($genders[$arr] == 'Male') {
                return $arr;
            } else {
                return $arr;
            }
        }
    }
}
