<?php

namespace frontend\controllers;

use common\event\ReviewFilterEvent;
use common\helpers\ReviewHelper;
use common\models\ReferralCode;
use common\models\Review;
use common\models\ReviewAnswer;
use common\models\ReviewContent;
use common\models\ReviewImage;
use common\models\Student;
use common\models\StudentOtp;
use common\services\ReviewService;
use common\services\SmsService;
use Yii;
use yii\web\Response;
use yii\widgets\ActiveForm;
use common\models\Course;

use common\services\S3Service;
use common\helpers\DataHelper;

class WritereviewController extends Controller
{
    protected $reviewService;

    public function __construct(
        $id,
        $module,
        ReviewService $reviewService,
        $config = []
    ) {

        $this->reviewService = $reviewService;
        parent::__construct($id, $module, $config);
    }

    public function actionIndex()
    {
        $user = \Yii::$app->user->identity;
        $formData = $this->reviewService->getQuestion();
        $category = $this->reviewService->getCategory();
        $referralCode = Yii::$app->request->get('referralcode');
        $studentReferralCode = $this->reviewService->getStudentReferralCode($user);

        return $this->render('/review/write-review', [
            'form_questions' => $formData ?? '',
            'categoryQuestions' => $category ?? '',
            'referralCode' => $referralCode ?? '',
            'user' => $user ?? '',
            'studentReferralCode' => $studentReferralCode ?? ''
        ]);
    }

    public function actionCreateForm()
    {
        $request = Yii::$app->request->post();
        $otpResponse = false;
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (isset($request['Student']) && !empty($request['Student']['name']) && !empty($request['Student']['phone']) && !empty(preg_match('/^[6-9][0-9]{9}$/', $request['Student']['phone']))) {
            $user = Student::find()->where(['phone' => $request['Student']['phone']])->one();
            if (!$user) {
                $collegeCityState = (new ReviewService)->getCityStateByCollegeId($request['Review']['college_id']);

                $model = new Student();

                $model->scenario = Student::SCENARIO_IMPORTER;
                $model->name = $request['Student']['name'];
                $model->email = $request['Student']['email'];
                $model->phone = $request['Student']['phone'];
                $model->user_type = Student::USER_TYPE_REVIEW;
                $model->source = Student::SOURCE_ORGANIC;
                $model->current_city = isset($collegeCityState['cityId']) ? (int) $collegeCityState['cityId'] : '';
                $model->current_state = isset($collegeCityState['state_id']) ? (int) $collegeCityState['state_id'] : '';
                if ($model->save()) {
                    $studentOtp = StudentOtp::find()->where(['student_id' => $model->id])->one();

                    if (!$studentOtp) {
                        $studentOtp = new StudentOtp();

                        $studentOtp->student_id = $model->id;
                        $studentOtp->otp = rand(1000, 9999);

                        if ($studentOtp->save()) {
                            $otpResponse = SmsService::sendOtp($request['Student']['phone'], $studentOtp->otp);
                            $this->createReview($request, $model);
                        } else {
                            print_r($studentOtp->getErrors());
                            exit;
                        }
                    } else {
                        if ($studentOtp->otp_status !== StudentOtp::STATUS_OTP_USED) {
                            $studentOtp->otp = rand(1000, 9999);
                            if ($studentOtp->save()) {
                                $otpResponse = SmsService::sendOtp($request['Student']['phone'], $studentOtp->otp);
                            }
                        }
                    }
                } else {
                    $data['errors'] = ActiveForm::validate($model);
                    echo json_encode($data);
                    exit;
                }
                if (isset($request) && !empty($request) && !empty($request['Review']['college_id']) && !empty($request['Review']['course_id'])) {
                    $reviewId = $this->reviewService->getReviewId($model->id, $request['Review']['college_id'], $request['Review']['course_id']);
                }
                return ['success' => true, 'otp' => $otpResponse ?? '', 'student_id' => $model->id, 'review_id' => isset($reviewId) && !empty($reviewId) ? $reviewId->id : '', 'currentStep' => 'step1', 'nextStep' => 'step2'];
            } else {
                $otpStatus = StudentOtp::find()->where(['student_id' => $user->id])->one();

                if (!$otpStatus) {
                    $otpStatus = new StudentOtp();

                    $otpStatus->student_id = $user->id;
                    $otpStatus->otp = rand(1000, 9999);

                    if ($otpStatus->save()) {
                        $otpResponse = SmsService::sendOtp($request['Student']['phone'], $otpStatus->otp);
                        $this->createReview($request, $user);
                    } else {
                        print_r($otpStatus->getErrors());
                        exit;
                    }
                } else {
                    if ($otpStatus->otp_status !== StudentOtp::STATUS_OTP_USED) {
                        $otpStatus->otp = rand(1000, 9999);
                        if ($otpStatus->save()) {
                            $otpResponse = SmsService::sendOtp($request['Student']['phone'], $otpStatus->otp);
                        }
                    }
                }
                $this->createReview($request, $user);
                $reviewId = $this->reviewService->getReviewId($user->id, (!empty($request['Review']['college_id']) ? $request['Review']['college_id'] : ''), (!empty($request['Review']['course_id']) ? $request['Review']['course_id'] : ''));
                return ['success' => true, 'otp' => $otpResponse ?? '', 'student_id' => $user->id, 'review_id' => $reviewId->id ?? '', 'currentStep' => 'step1', 'nextStep' => 'step2'];
            }
        } else {
            return ['success' => false];
        }
    }

    public function createReview($request, $model)
    {
        if (empty($request) && empty($request['Review']['college_id']) || empty($request['Review']['course_id'])) {
            return '';
        }

        $review = Review::find()->where(['student_id' => $model->id])
            ->andWhere(['course_id' => $request['Review']['course_id']])
            ->andWhere(['college_id' => $request['Review']['college_id']])
            ->one();

        if (!$review) {
            $review = new Review();
        }

        $review->business_unit_id = ReviewHelper::$business_unit_id['getmyuni'];
        $review->student_id = $model->id;
        $review->course_id = $request['Review']['course_id'] ?? '';
        $review->college_id = $request['Review']['college_id'] ?? '';
        $review->admission_year = $request['Review']['admission_year'] ?? '';
        $review->referred_by = $request['Review']['referred_by'] ?? '';
        $review->college_fees = $request['Review']['college_fees'] ?? '';
        $review->college_fees_type = $request['Review']['college_fees_type'] ?? '';
        $review->utm_source = $request['Review']['utm_source'] ?? '';
        $review->utm_campaign = $request['Review']['utm_campaign'] ?? '';
        $review->utm_medium = $request['Review']['utm_medium'] ?? '';
        $review->title = !empty($request['Review']['title']) ? $request['Review']['title'] : null;
        $review->status = Review::STATUS_PENDING;

        if (!empty(ActiveForm::validate($review))) {
            $data['errors'] = ActiveForm::validate($review);
            echo json_encode($data);
            exit;
        } else {
            $reviewFormValidation = $this->checkReviewFormValidation($request);
            if (!empty($reviewFormValidation)) {
                return $reviewFormValidation;
            } else {
                $userReviewValidation = $this->checkUserReviewValidation($request, $model);
                if (!empty($userReviewValidation)) {
                    echo json_encode($userReviewValidation);
                    exit;
                }
            }
        }
        if ($review->save()) {
            $this->saveReviewAnswer($request, $review);
            return ['success' => true];
        } else {
            $data['errors'] = ActiveForm::validate($review);
            echo json_encode($data);
            exit;
        }
    }

    protected function checkReviewFormValidation($request)
    {
        $questionIdCanBeBlank = 2;
        $emptyKeys = array_keys(array_filter($request['ReviewAnswer']['answer'], function ($value) {
            if (empty($value)) {
                return empty($value); // Check for empty values
            }
        }));
        $emptyQuestions = array_filter($emptyKeys, function ($value) use ($questionIdCanBeBlank) {
            return $value !== $questionIdCanBeBlank;
        });
        if (!empty($emptyQuestions)) {
            foreach ($emptyQuestions as $value) {
                $quesId = $value;
                $question = 'question' . ++$quesId;
                if ($value != 3 && (empty($request[$question]) || $request[$question] == '')) {
                    return ['success' => true];
                } elseif ($value == 3 && $request['ReviewAnswer']['answer'][$value] == '') {
                    return ['success' => true];
                }
            }
        } else {
            return null;
        }
    }

    private function checkUserReviewValidation($request, $model)
    {
        $reviewCount = Review::find()->where(['student_id' => $model->id])->count();
        if (($reviewCount >= 2)) {
            $data['error_limit']['status'] = true;
            $data['error_limit']['message'] = 'You can review only 2 times';
            return $data;
        }
        $newReviewcourse = Course::find()->select(['degree'])->where(['id' => $request['Review']['course_id']])->one();
        $studentAllReviews = Review::find()->where(['student_id' => $model->id])
            ->asArray()->all();
        foreach ($studentAllReviews as $value) {
            $course = Course::find()->select(['degree'])->where(['id' => $value['course_id']])->one();
            if ($course->degree == $newReviewcourse->degree) {
                $data['error_limit']['status'] = true;
                $data['error_limit']['message'] = 'You should not review ' . $course->degree . ' degree again';
                return $data;
            }
        }
        return [];
    }


    public function actionStoreContent()
    {
        $request = Yii::$app->request->post();
        $previousStep = $request['steps'] - 1;
        $currentStep = $request['steps'];
        $nextStep = $request['steps'] + 1;
        Yii::$app->response->format = Response::FORMAT_JSON;
        if (!empty($request) && !empty($request['content'])) {
            $model = ReviewContent::find()->where(['review_id' => $request['reviewId']])->andWhere(['review_category_id' => $request['reviewCatId']])->one();

            if (!empty($model)) {
                $model->content = $request['content'];
                $model->rating = $request['ratingValue'] ?? '';
                if ($model->save()) {
                    $this->saveReviewTitle($request, $model);
                    return ['success' => true, 'previousStep' => 'step' . $previousStep, 'currentStep' => 'step' . $currentStep, 'nextStep' => 'step' . $nextStep];
                }
            }

            $model = new ReviewContent();

            $model->review_category_id = $request['reviewCatId'];
            $model->review_id = $request['reviewId'];
            $model->content = $request['content'];
            $model->rating = $request['ratingValue'] ?? '';
            $model->status = ReviewContent::STATUS_PENDING;

            if ($model->save()) {
                $this->saveReviewTitle($request, $model);
                return ['success' => true, 'previousStep' => 'step' . $previousStep, 'currentStep' => 'step' . $currentStep, 'nextStep' => 'step' . $nextStep];
            } else {
                print_r($model->getErrors());
                exit;
            }
        } else {
            exit;
        }
    }

    public function saveReviewAnswer($request, $review)
    {
        $question_id = $request['ReviewAnswer']['question_id'];
        $answerValue = $request['ReviewAnswer']['answer'];
        $result = array_map(function ($qId, $ans) {
            return ['question_id' => $qId, 'answer' => $ans];
        }, $question_id, $answerValue);

        $answers = ReviewAnswer::find()->where(['review_id' => $review->id])->all();
        if (empty($answers)) {
            $this->createReviewAnswers($result, $review);
        }
        foreach ($answers as $answer) {
            $this->updateReviewAnswer($result, $answer, $review);
        }
    }

    public function createReviewAnswers($result, $review)
    {
        foreach ($result as $key => $value) {
            if (empty($value['answer'])) {
                continue;
            }

            $answer = new ReviewAnswer();

            $answer->review_id = $review->id;
            $answer->question_id = $value['question_id'];
            $answer->answer = $value['answer'];
            $answer->status = ReviewAnswer::STATUS_PENDING;

            if ($answer->save()) {
            } else {
                print_r($answer->getErrors());
                exit;
            }
        }
    }

    public function updateReviewAnswer($result, $answer, $review)
    {
        foreach ($result as $key => $value) {
            $model = ReviewAnswer::find()->where(['review_id' => $answer->review_id])->andWhere(['question_id' => $value['question_id']])->one();

            if (!$model) {
                $answer = new ReviewAnswer();

                $answer->review_id = $review->id;
                $answer->question_id = $value['question_id'];
                $answer->answer = $value['answer'];
                $answer->status = ReviewAnswer::STATUS_PENDING;

                if ($answer->save()) {
                } else {
                    print_r($answer->getErrors());
                    exit;
                }
            } else {
                $model->answer = !empty($value['answer']) ?  $value['answer'] : null;

                if ($model->save()) {
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function saveReviewTitle($request, $model)
    {
        if (isset($request['reviewTitle']) && !empty($request['reviewTitle']) && !empty($model)) {
            $review = Review::find()->where(['id' => $model->review_id])->one();

            if (empty($review) || $review == false) {
                return false;
            }

            $review->title = $request['reviewTitle'] ?? '';

            if ($review->save()) {
            } else {
                $model->getErrors();
                exit;
            }
        }
    }

    public function actionStoreProfileImage()
    {
        if (isset($_FILES['file']['name'])) {
            preg_match('!\d+!', $_SERVER['HTTP_REFERER'], $matches);
            $review = !empty($matches[0]) ? Review::find()->where(['id' => $matches[0]])->one() : null;
            $model = !empty($review) ? Student::find()->where(['id' => $review->student_id])->one() : null;

            if (empty($model)) {
                return  $response = 0;
            }

            /* Getting file name */
            $file_size = $_FILES['file']['size'];
            $filename = uniqid('review-image-' . ($review->id ?? '') . '-' . ($model->id ?? '') . '-');
            $ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
            $imageName = $filename . '.' . $ext;

            if (($file_size > 100000)) {
                $message = 'File too large. File must be less than 100kb.';
                $data['success'] = 2;
                $data['message'] = $message;
                return json_encode($data);
            }

            $imageFileType = pathinfo($imageName, PATHINFO_EXTENSION);
            $imageFileType = strtolower($imageFileType);

            /* Valid extensions */
            $valid_extensions = ['jpg', 'jpeg', 'png', 'webp'];
            $response = 0;
            /* Check file extension */
            if (in_array(strtolower($imageFileType), $valid_extensions)) {
                /* Upload file */
                $s3 = new S3Service();
                if ($s3->uploadFile(DataHelper::s3Path($imageName, 'student'), $_FILES['file']['tmp_name'])) {
                    $response = 1;

                    $model->profile_pic = isset($imageName) && !empty($imageName) ? $imageName : '';

                    if ($model->save()) {
                    } else {
                        print_r($model->getErrors());
                    }
                }
            } else {
                $response = 0;
            }
            $data['id'] = $model->id ?? '';
            $data['file_name'] = $imageName ?? '';
            $data['success'] = $response;

            return json_encode($data);
        }
        return false;
    }

    public function actionStoreReviewCollegeImages()
    {
        if (empty($_SERVER) || empty($_SERVER['HTTP_REFERER'])) {
            return false;
        }

        preg_match('!\d+!', $_SERVER['HTTP_REFERER'], $matches);
        $review = !empty($matches[0]) ? Review::find()->where(['id' => $matches[0]])->one() : [];
        $fileName = ($review->student_id ?? '') . '__' . ($review->id ?? '') . '/';

        // Upload file
        $target_file = uniqid('review-image-');
        $ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
        $imageName = $target_file . '.' . $ext;

        $msg = '';
        $s3 = new S3Service();
        if ($s3->uploadFile(DataHelper::s3Path($fileName . $imageName, 'review'), $_FILES['file']['tmp_name'])) {
            $model = new ReviewImage();
            $model->file = (!empty($fileName) ? $fileName : '') . $imageName;
            $model->status = ReviewImage::STATUS_PENDING;

            if ($model->save()) {
                $this->updateReviewId($model->id);
                $success = 1;
                $msg = 'Successfully uploaded';
            } else {
                print_r($model->getErrors());
            }
        } else {
            $success = 0;
            $msg = 'Error while uploading';
        }
        $data['success'] = $success;
        $data['message'] = $msg;

        echo json_encode($data);
        exit;
    }

    public function updateReviewId($reviewImageId)
    {
        preg_match('!\d+!', $_SERVER['HTTP_REFERER'], $matches);

        $model = ReviewImage::find()->where(['id' => $reviewImageId])->one();

        if (!$model) {
            return [];
        }

        $model->review_id = $matches[0] ?? '';
        if ($model->review_id && $model->save()) {
        } else {
            echo "Review Id Empty \n";
            return 'false';
        }
    }

    public function actionSubmitReviewForm()
    {
        $request = Yii::$app->request->post();

        if (empty($request) || empty($request['location'])) {
            $data['success'] = false;
            $data['message'] = 'Error while uploading';
            return false;
        }
        preg_match('!\d+!', $request['location'], $matches);

        if (empty($matches[0])) {
            $data['success'] = false;
            $data['message'] = 'Error while uploading';
            return false;
        }
        $review = Review::find()->where(['id' => $matches[0]])->one();

        if (!$review) {
            return [];
        }

        $review->terms_agreed = $request['terms_agreed'] ?? '';
        if ($review->save()) {
            $success = 1;
            $msg = 'Successfully uploaded';
        } else {
            $success = 0;
            $msg = 'Error while uploading';
        }
        $data['success'] = $success;
        $data['message'] = $msg;

        echo json_encode($data);
        exit;
    }

    public function actionSaveReferralCode()
    {
        $request = Yii::$app->request->post();
        if (isset($request['referralcode']) && !empty($request['referralcode']) && $request['student_id']) {
            $referralCode = explode('=', $request['referralcode'])[1];
            $model = ReferralCode::find()->where(['student_id' => $request['student_id']])->one();

            if ($model) {
                $data['success'] = 1;
                $data['message'] = 'Updated';

                return json_encode($data);
            }

            $model = new ReferralCode();

            $model->business_unit = ReviewHelper::$business_unit_id['getmyuni'];
            $model->student_id = $request['student_id'] ?? '';
            $model->referral_code = $referralCode ?? '';
            $model->status = ReferralCode::STATUS_ACTIVE;

            if ($model->save()) {
            } else {
                $model->getErrors();
            }

            $data['success'] = 1;
            $data['message'] = 'Updated';

            echo json_encode($data);
            exit;
        }
        return false;
    }
}
