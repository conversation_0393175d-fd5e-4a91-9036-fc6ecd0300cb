<?php

namespace frontend\controllers;

use common\services\UserService;
use common\models\Category;
use Yii;
use common\models\Course;
use common\services\CollegeService;
use common\services\CourseService;
use common\services\FaqService;
use frontend\services\ArticleService;
use common\services\v2\NewsService;
use frontend\services\ExamService;
use yii\web\NotFoundHttpException;
use common\helpers\DataHelper;
use common\helpers\CourseHelper;
use common\models\LeadBucketTagging;
use common\services\QnaCommonService;
use yii\helpers\Json;
use yii\web\Response;
use common\models\Qna;

class CourseController extends Controller
{
    protected $collegeService;
    protected $courseService;
    protected $examService;
    protected $faqService;
    protected $articleService;
    protected $newsService;
    protected $qnaCommonService;
    public $pageType;
    public $entityType;
    public $entitySlug;

    public function __construct(
        $id,
        $module,
        CollegeService $collegeService,
        CourseService $courseService,
        ExamService $examService,
        FaqService $faqService,
        ArticleService $articleService,
        NewsService $newsService,
        QnaCommonService $qnaCommonService,
        $config = []
    ) {
        $this->collegeService = $collegeService;
        $this->courseService = $courseService;
        $this->examService = $examService;
        $this->faqService = $faqService;
        $this->articleService = $articleService;
        $this->newsService = $newsService;
        $this->qnaCommonService = $qnaCommonService;
        parent::__construct($id, $module, $config);
    }

    public function actionIndex()
    {

        $response = DataHelper::trackStudentActivity('course-index');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        return $this->render('index', [
            'content' => $this->courseService->getHomePageContent(),
            'popularCourse' => $this->courseService->getPopularCourse(),
        ]);
    }

    public function actionDetail($course, $page = 'about', $sub = '')
    {
        if (!empty($sub)) {
            return $this->dropDownPage($course, $page, $sub);
        }
        $response = DataHelper::trackStudentActivity('course-detail', $page);
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $this->entitySlug = $course;
        $parentCourse = [];
        $course = $this->courseService->getDetail($course);

        if (empty($course)) {
            throw new NotFoundHttpException();
        }

        $content = $this->courseService->getContent($course->id, $page);

        if (empty($content)) {
            throw new NotFoundHttpException();
        }

        $courseSubPageDropdown = $this->courseService->getSubPageDropdown($course, $page);
        $parentCourse = $course->getParent()->one();
        $courseSpecialization = $this->courseService->getParentSpecialization($course, $page);
        
        return $this->render('detail', [
            'course' => $course,
            'colleges' => $this->courseService->getTopColleges($course),
            'pageName' => $page,
            'features' => $this->courseService->getFeature($course->id, 'course', array_keys(CourseHelper::$featurArr)),
            'menu' => $this->courseService->getMenu($course->id),
            'content' => $content,
            'exams' => $this->courseService->getExamByCourse($course) ?? [],
            'parentCourse' => $parentCourse->name ?? $course->name,
            'coursebyStream' => $this->courseService->getStreamCourse($course, $page),
            'courseSpecialization' => $courseSpecialization ?? [],
            'faqs' => $this->faqService->getPageLevelFaqDetails(Course::ENTITY_COURSE, $course->id, $page, ''),
            // 'liveApplication' => $this->courseService->getLiveApplication($course->short_name, 5),
            'Chartdata' => $this->courseService->getChartData($course) ?? [],
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'stateList' => $this->courseService->getCourseByState($course) ?? [],
            'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Course::ENTITY_COURSE, $course->id, $page), (!empty($course->short_name) ? $course->short_name : $course->name)],
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COURSES, $page, ($course->short_name ?? $course->name), $course->slug),
            'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Course::ENTITY_COURSE, $course->id, $page), (!empty($course->short_name) ? $course->short_name : $course->name)],
            'dropdowns' => $courseSubPageDropdown,
            'parent' => true,
            'parentPage' => null,
            'popularDegreeCourse' => $this->courseService->gePopularDegreeCourse($course),
        ]);
    }

    public function actionCategory($discipline)
    {
        $this->entityType = 'course';
        $this->pageType = 'category';
        $stream = $this->courseService->getStream($discipline);

        if (!$stream) {
            throw new NotFoundHttpException();
        }

        $degreeCourse = $this->courseService->getCourseByStream($stream);

        if (empty($degreeCourse['tab'])) {
            throw new NotFoundHttpException();
        }
        $articleCate = $this->courseService->getArticleCategory($stream);

        return $this->render('category', [
            'stream' => $stream,
            'content' => $this->courseService->getStreamContent($stream->id, 'landing_page'),
            'degreeCourse' => $degreeCourse,
            'colleges' => $this->courseService->getCollegeByStream($stream),
            'exams' => $this->courseService->getExamByStream($stream),
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'articles' => !empty($articleCate) ? $this->articleService->getByCategory($articleCate->id, 10) : '',
            'articleCatSlug' => !empty($articleCate) ? $articleCate['slug'] : '',
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COURSES, 'category'),
        ]);
    }

    /**
     * The below function is used for geting the details of qna landing page
     */
    public function actionQnaLandingPage($slug)
    {
        $total_qn_ans = $this->qnaCommonService->getTotalQuestionsAnswers();
        $qnaDetails = $this->qnaCommonService->getQnaDetails($slug, 'course');
        // $qnaDetails = $this->qnaCommonService->getSubpageByEntity('course', $pageName, $qnaDetails);
        $qnaLandingEntityDetails = $this->qnaCommonService->getEntityQnaLandingPage($slug, 'course');

        return $this->render('../qna/_qnaLandingPageNew', [
            'qnadetails' => $qnaDetails,
            'qnaDetailsLatest' => $qnaLandingEntityDetails,
            'filterData' => QnaCommonService::$filterData,
            'total_qn_ans' => $total_qn_ans,
            'displayName' => isset($qnaDetails->course->short_name) ? $qnaDetails->course->short_name:$qnaDetails[0]->course->short_name
        ]);
    }
    protected function dropDownPage($course, $page, $sub)
    {
        $subPageSlug = $sub;
        $sub = ucwords(str_replace('-', ' ', $sub));
        $response = DataHelper::trackStudentActivity('course-detail', $page);
        $this->entityType = 'course';
        $this->pageType = $sub;
        $this->entitySlug = $course;
        $parentCourse = [];
        $course = $this->courseService->getDetail($course);

        if (empty($course)) {
            throw new NotFoundHttpException();
        }

        $content = $this->courseService->getContent($course->id, $sub);

        if (empty($content)) {
            throw new NotFoundHttpException();
        }

        $courseSubPageDropdown = $this->courseService->getSubPageDropdown($course, $page);
        $parentCourse = $course->getParent()->one();
        $courseSpecialization = $this->courseService->getParentSpecialization($course, $page);
        return $this->render('detail', [
            'course' => $course,
            'colleges' => $this->courseService->getTopColleges($course),
            'pageName' => $sub,
            'features' => $this->courseService->getFeature($course->id, 'course', array_keys(CourseHelper::$featurArr)),
            'menu' => $this->courseService->getMenu($course->id),
            'content' => $content,
            'exams' => $this->courseService->getExamByCourse($course) ?? [],
            'parentCourse' => $parentCourse->name ?? $course->name,
            'coursebyStream' => $this->courseService->getStreamCourse($course, $page),
            'courseSpecialization' => $courseSpecialization ?? [],
            'faqs' => $this->faqService->getPageLevelFaqDetails(Course::ENTITY_COURSE, $course->id, $page, $subPageSlug),
            // 'liveApplication' => $this->courseService->getLiveApplication($course->short_name, 5),
            'Chartdata' => $this->courseService->getChartData($course) ?? [],
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'stateList' => $this->courseService->getCourseByState($course) ?? [],
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COURSES, $page, ($course->short_name ?? $course->name), $course->slug),
            // 'recentActivity' => [$this->collegeService->getRecentActivityByEntity(Course::ENTITY_COURSE, $course->id, $page), (!empty($course->short_name) ? $course->short_name : $course->name)],
            'dropdowns' => $courseSubPageDropdown,
            'parent' => null,
            'parentPage' => $page,
            'popularDegreeCourse' => $this->courseService->gePopularDegreeCourse($course),
        ]);
    }
}
