<?php

namespace frontend\controllers;

use Carbon\Carbon;
use common\helpers\CollegeHelper;
use common\models\College;
use common\models\Course;
use common\models\CourseContent;
use common\models\documents\Sitemap;
use common\models\News;
use common\models\NewsCategory;
use common\models\Review;
use common\services\SitemapService;
use frontend\helpers\Url;
use Yii;
use yii\data\Pagination;
use yii\db\Query;
use yii\web\NotFoundHttpException;
use yii\caching\TagDependency;
use common\helpers\DataHelper;
use common\models\Career;
use common\models\CareerContent;
use common\models\CollegeCiSubpageContent;
use common\models\CollegePiSubpageContent;
use common\models\NcertArticles;
use common\models\NewsContent;
use common\models\SitemapUpdate;
use common\models\Qna;
use common\models\Olympiad;
use common\models\OlympiadContent;
use common\models\Scholarship;
use common\models\ScholarshipContent;
use yii\helpers\Inflector;

class SitemapController extends Controller
{
    protected $sitemapService;

    public function __construct(
        $id,
        $module,
        SitemapService $sitemapService,
        $config = []
    ) {
        $this->sitemapService = $sitemapService;
        parent::__construct($id, $module, $config);
    }

    /**sitemap category arr */
    public static $categoryArr = [
        // 'news',
        // 'news/category',
        // 'college',
        // 'article',
        // 'hi/article',
        // 'courses',
        // 'sa-article',
        // 'exam',
        // 'boards',
        // 'hi/boards',
        'article-sitemap.xml',
        'board-sitemap.xml',
        'course-sitemap.xml',
        'exam-sitemap.xml',
        'olympiad-sitemap.xml',
        'scholarship-sitemap.xml',
        'career-sitemap.xml',
        // 'user-review-sitemap',
        // 'careers',
        // 'immigrations',
        // 'olympiad',
        // 'scholarships'
        'listing',
    ];

    public function actionIndex()
    {
        $urls = [];
        foreach (self::$categoryArr as $category) {
            if ($category == 'listing') {
                $urls[] = [
                    'loc' => Url::base(true) . '/sitemap/' . $category
                ];
            } else {
                $urls[] = [
                    'loc' => Url::base(true) . '/' . $category
                ];
            }
        }

        $arr = ['info', 'ci', 'pi', 'admission','courses-fees','placements','facilities','scholarships','cut-off','reviews','result','ranking','news','verdict','syllabus','hostel'];
        $renameFile = ['admission' => 'admission-college', 'placements' => 'college-placement',  'courses-fees' => 'courseFees'];
        foreach ($arr as $a) {
            if (!empty($renameFile[$a])) {
                $urls[] = [
                    'loc' => Url::base(true) . '/' . $renameFile[$a] . '-sitemap.xml'
                ];
            } else {
                $urls[] = [
                    'loc' => Url::base(true) . '/' . 'college-' . $a . '-sitemap.xml'
                ];
            }
        }

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return  $this->renderPartial('news/index', [
            'data' => $urls,                                // с generate urls for sitemap
        ]);
    }

    // public function actionNews()
    // {

    //     $funArguments = func_get_args();
    //     $funArguments['pageType'] = 'news-sitemap';
    //     $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
    //     $key = $this->getHash($funArguments);
    //     // yii::$app->cache->flush();
    //     $data = Yii::$app->cache->getOrSet($key, function () {
    //         $date =  date('Y-m-d', strtotime('-3days'));
    //         $lang_code = DataHelper::getLangId();
    //         $query = News::find()
    //             ->joinWith(['newsContent nc'])
    //             ->where([News::tableName() . '.status' => News::STATUS_ACTIVE])
    //             ->andWhere(['nc.status' => NewsContent::STATUS_ACTIVE])
    //             ->andWhere(['lang_code' => $lang_code])
    //             ->orderBy(['updated_at' => SORT_DESC]);
    //         $urls = [];

    //         foreach ($query->batch() as $news) {
    //             foreach ($news as $val) {
    //                 if (empty($val)) {
    //                     continue;
    //                 }
    //                 $valDate = strtotime(date('Y-m-d', strtotime($val->updated_at)));
    //                 $valCurrentDate = strtotime($date);
    //                 if ($valCurrentDate > $valDate) {
    //                     $urls[] = [
    //                         'loc' => !empty($val->slug) ? Url::toNewsDetail($val->slug, DataHelper::getLangCode($val->lang_code)) : '',
    //                         'lastmod' => $val->updated_at,
    //                     ];
    //                 }
    //             }
    //         }
    //         return ['urls' => $urls];
    //     }, 60, new TagDependency(['tags' => 'news-site-map']));
    //     return $this->renderXml($data['urls']);
    // }

    // public function actionGoogleNews()
    // {
    //     $lang_code = DataHelper::getLangId();
    //     $date = Carbon::now()->subDay(2)->toDateTimeLocalString();

    //     $news = News::find()->where(['>=', 'published_at', $date])->andWhere(['lang_code' => $lang_code])->active()->orderBy(['published_at' => SORT_DESC])->all();

    //     if (empty($news)) {
    //         return '';
    //     }

    //     $urls = [];

    //     foreach ($news as $val) {
    //         if (empty($val)) {
    //             continue;
    //         }
    //         $urls[] = [
    //             'loc' => !empty($val['slug']) ? Url::toNewsDetail($val->slug, DataHelper::getLangCode($val->lang_code)) : '',
    //             'publication_date' => !empty($val->published_at) ? $val->published_at : $val->created_at,
    //             'title' => !empty($val->newsContent) ? $val->newsContent->meta_title :  '',
    //             'keywords' => !empty($val->newsContent) ? $val->newsContent->meta_keywords :  '',
    //             'image' => $val->banner_image ?? '',
    //             'last_mod' => $val->updated_at ?? ''
    //         ];
    //     }

    //     Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
    //     Yii::$app->response->headers->add('Content-Type', 'text/xml');

    //     return  $this->renderPartial('news/google-news', [
    //         'data' => $urls,
    //     ]);
    // }

    // public function actionCategory()
    // {
    //     $categories = NewsCategory::find()->all();
    //     $urls = [];

    //     foreach ($categories as $val) {
    //         $urls[] = [
    //             'loc' => Url::base(true) . Url::toNewsDetail($val->slug),
    //             'lastmod' => $val->updated_at,
    //         ];
    //     }
    //     Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
    //     Yii::$app->response->headers->add('Content-Type', 'text/xml');


    //     return  $this->renderPartial('news/category', [
    //         'data' => $urls,
    //     ]);
    // }

    public function renderXml($urls)
    {
        $newsCategories = NewsCategory::find()->active()->all();
        if (empty($newsCategories)) {
            return '';
        }

        foreach ($newsCategories as $category) {
            if (empty($category)) {
                continue;
            }
            $models[] = [
                'loc' => Url::toNewsDetail($category->slug),
                'lastmod' => $category->created_at ?? '',
            ];
        }

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');
        return  $this->renderPartial('news/index', [
            'data' => $urls,                                // с generate urls for sitemap
            'page' => 'news',
            'models' => $models
        ]);
    }

    /**
     * College Sitemap Index page
     */
    public function actionCollege()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');
        //arr pages need to remove from sitemap
        $arr = ['images-videos', 'qna', 'compare-college'];

        return $this->renderPartial('college/index', [
            'data' => array_diff(array_keys(CollegeHelper::$subPages), $arr)
        ]);
    }

    /**
     * Listing Sitemap Index page
     */

    public function actionListing($page = null)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        $query = Sitemap::find()->where(['entity' => Sitemap::ENTITY_LISTING])->andWhere(['status' => Sitemap::STATUS_ACTIVE]);

        $totalListing = $query->count();
        $pageSize = 10000;
        $totalPages = 0;

        if ($totalListing > 0) {
            if ($totalListing < $pageSize) {
                $totalPages = 1;
            } else {
                $totalPages = ceil($totalListing / $pageSize);
            }
        }

        if (!empty($page)) {
            if ($totalPages < 1 || $page > $totalPages) {
                throw new NotFoundHttpException('The requested page does not exist.');
            }

            $data = $query->offset(($page - 1) * $pageSize)->limit($pageSize)->all();
            return $this->renderPartial('listing/detail', [
                'data' => $data ?? []
            ]);
        }

        return $this->renderPartial('listing/index', [
            'totalPages' => $totalPages
        ]);
    }

    /**
     * Get the College Pages sitemap
     * @param $category | College Category
     * @param $slug | College Page, ex: info, courses-fees
     * @param $page | Page Number
     *
     * @return array | []
     */
    public function actionCollegePage($category, $slug, $page = null)
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $query = SitemapUpdate::find()
            ->where(['entity' => $category])
            ->andWhere(['sub_page' => $slug])
            ->andWhere(['status' => Sitemap::STATUS_ACTIVE])->orderBy(['updated_at' => SORT_DESC]);

        $pageSlug = ['ci', 'pi'];
        $pageSize = in_array($slug, $pageSlug) ? 10000 : 6000;

        if (in_array($slug, $pageSlug)) {
            $ciPiData = self::getCiPiSubpage($category, $slug);
        }

        $pages = new Pagination(['totalCount' => $query->count(), 'pageSize' => $pageSize]);

        if (!empty($page)) {
            if ($pages->getPageCount() < 1 || $page > $pages->getPageCount()) {
                throw new NotFoundHttpException('The requested page does not exist.');
            }
        }
        
        $data = $query->offset($pages->offset)
            ->limit($pages->limit)
            ->all();

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        if (empty($page)) {
            return $this->renderPartial('college/detail', [
                'data' => $pages->getPageCount() ?? [],
                'slug' => $slug,
                'page' => $page,
                'category' => $category,
                'ciPiData' => $ciPiData ?? [],
            ]);
        } else {
            return $this->renderPartial('college/detail', [
                'data' => $data ?? [],
                'page' => $page,
            ]);
        }
    }

    public function getCiPiSubpage($category, $slug)
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $ciPiSubpage = DataHelper::$courseCiPiSubpageItems;
        $subpages = array_column($ciPiSubpage, 'id');
        $modelClass = $slug == 'ci' ? CollegeCiSubpageContent::class :  CollegePiSubpageContent::class;
        $statusActive = $modelClass::STATUS_ACTIVE;

        $ciPiData = $modelClass::find()
            ->where(['subpage' => $subpages])
            ->andWhere(['status' => $statusActive])
            ->orderBy(['updated_at' => SORT_DESC])
            ->all();

        return $ciPiData;
    }

    /**
     * Get Article list
     */
    public function actionArticle()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $data = $this->sitemapService->getAll(Sitemap::ENTITY_ARTICLE);
        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('article/index', [
            'data' => $data
        ]);
    }

    /**
     * Get Study Abroad Article list
     */
    public function actionSaArticle()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $data = $this->sitemapService->getAll(Sitemap::ENTITY_STUDY_ABROAD);

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('article/index', [
            'data' => $data
        ]);
    }

    /**
     * Get the Exam Sitemap
     */
    public function actionExam()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $data = $this->sitemapService->getAll(Sitemap::ENTITY_EXAM);

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('exam/index', [
            'data' => $data
        ]);
    }

    /**
     * Get Broad Sitemap
     */
    public function actionBoards()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $data = $this->sitemapService->getAll(Sitemap::ENTITY_BOARD);

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('board/index', [
            'data' => $data
        ]);
    }

    /**
     * Get Course Sitemap
     */
    public function actionCourses()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $query = new Query();
        $query->select(['c.id', 'slug', 'page', 'cc.updated_at', 'cc.parent_id'])
            ->from('course c')
            ->innerJoin('course_content cc', 'cc.course_id = c.id')
            ->where(['c.status' => Course::STATUS_ACTIVE])
            ->andWhere(['cc.status' => CourseContent::STATUS_ACTIVE])->orderBy('updated_at DESC');
        $pages = $query->all();
        foreach ($pages as $page) {
            if ($page['page'] == 'about') {
                $url = $page['slug'] . '-course';
            } else {
                if (!empty($page['parent_id'])) {
                    $courseContent = CourseContent::findOne($page['parent_id']);
                    $url = $page['slug'] . '-' . $courseContent->page . '/' . Inflector::slug($page['page'], '-', true);
                } else {
                    $url = $page['slug'] . '-' . $page['page'];
                }
            }
            $data[] = [
                'slug' => $url,
                'lastModified' => $page['updated_at']
            ];
        }

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('course/index', [
            'data' => $data
        ]);
    }

    public function actionUserReviewSitemap($page = null)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        $query = Review::find()->select(['slug', 'updated_at'])->andWhere(['status' => Sitemap::STATUS_ACTIVE]);

        $pages = new Pagination(['totalCount' => $query->count(), 'pageSize' => 10000]);
        $data = $query->offset($pages->offset)
            ->limit($pages->limit)
            ->all();

        $totalPages = $pages->getPageCount();
        if (!empty($page)) {
            if ($totalPages < 1 || $page > $totalPages) {
                throw new NotFoundHttpException('The requested page does not exist.');
            }

            return $this->renderPartial('user-review/detail', [
                'data' => $data ?? [],
            ]);
        }

        return $this->renderPartial('user-review/index', [
            'totalPages' => $totalPages,
        ]);
    }

    /**
     * Get Course Sitemap
     */
    public function actionCareers()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $query = new Query();
        $query->select(['c.id', 'slug', 'page', 'cc.updated_at'])
            ->from('career c')
            ->innerJoin('career_content cc', 'cc.career_id = c.id')
            ->where(['c.status' => Career::STATUS_ACTIVE])
            ->andWhere(['cc.status' => CareerContent::STATUS_ACTIVE]);
        $pages = $query->all();

        foreach ($pages as $page) {
            if ($page['page'] == 'overview') {
                $url = $page['slug'];
            } else {
                $url = $page['slug'] . '-' . $page['page'];
            }
            $data[] = [
                'slug' => $url,
                'lastModified' => $page['updated_at']
            ];
        }

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('career/index', [
            'data' => $data
        ]);
    }

    /**
     * Get Ncert Sitemap
     */
    public function actionNcert()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $sitemap_ncert = NcertArticles::find()->select(['slug', 'updated_at'])->where(['status' => NcertArticles::STATUS_ACTIVE])->all();

        foreach ($sitemap_ncert as $ncert) {
            $data[] = [
                'slug' => $ncert['slug'],
                'lastModified' => $ncert['updated_at']
            ];
        }

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('ncert/index', [
            'data' => $data ?? []
        ]);
    }

    public function actionUpdates()
    {
        $date = Carbon::now()->subDay(1)->toDateTimeLocalString();

        $siteMapData = SitemapUpdate::find()->andWhere(['>=', 'updated_at', $date])->andWhere(['status' => 1])->andWhere(['<>', 'entity', 'news'])->orderBy([
            'updated_at' => SORT_DESC,
        ])->all();
        foreach ($siteMapData as $value) {
            $data[] = [
                'domain' => $value->domain,
                'slug' => $value->slug,
                'sub_page' => $value->sub_page,
                'priority' => $value->priority,
                'change_freq' => $value->change_freq,
                'lastmod' => $value->updated_at,
                'entity' => $value->entity
            ];
        }

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');
        return $this->renderPartial('updates/index', [
            'data' => $data ?? []
        ]);
    }

    /**
     * Get QNA Sitemap
     */
    public function actionQna()
    {
        throw new NotFoundHttpException('The requested page does not exist.');
        
        $sitemapQna = Qna::find()->select(['entity', 'slug', 'updated_at'])->where(['status' => Qna::STATUS_ACTIVE])->orderBy('updated_at DESC')->all();

        foreach ($sitemapQna as $value) {
            $data[] = [
                'slug' => Url::toQnaDetail($value->entity, $value->slug),
                'lastModified' => $value->updated_at
            ];
        }

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('qna/index', [
            'data' => $data ?? []
        ]);
    }

    /**
     * Get Olympiad Sitemap
     */
    public function actionOlympiad()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $data = [];
        $query = new Query();
        $query->select(['o.id', 'slug', 'page', 'oc.updated_at'])
            ->from('olympiad o')
            ->innerJoin('olympiad_content oc', 'oc.olympiad_id = o.id')
            ->where(['o.status' => Olympiad::STATUS_ACTIVE])
            ->andWhere(['oc.status' => OlympiadContent::STATUS_ACTIVE]);
        $query->orderBy('updated_at DESC');
        $pages = $query->all();
        foreach ($pages as $page) {
            if ($page['page'] == 'overview') {
                $url = $page['slug'];
            } else {
                $url = $page['slug'] . '-' . $page['page'];
            }
            $data[] = [
                'slug' => $url,
                'lastModified' => $page['updated_at']
            ];
        }
        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('olympiad/index', [
            'data' => $data
        ]);
    }

    /**
     * Get Scholarship Sitemap
     */
    public function actionScholarships()
    {
        throw new NotFoundHttpException('The requested page does not exist.');

        $data = [];
        $query = new Query();
        $query->select(['s.id', 'slug', 'page', 'sc.updated_at'])
            ->from('scholarship s')
            ->innerJoin('scholarship_content sc', 'sc.scholarship_id = s.id')
            ->where(['s.status' => Scholarship::STATUS_ACTIVE])
            ->andWhere(['sc.status' => ScholarshipContent::STATUS_ACTIVE]);
        $query->orderBy('updated_at DESC');
        $pages = $query->all();
        foreach ($pages as $page) {
            if ($page['page'] == 'overview') {
                $url = $page['slug'];
            } else {
                $url = $page['slug'] . '-' . $page['page'];
            }
            $data[] = [
                'slug' => $url,
                'lastModified' => $page['updated_at']
            ];
        }
        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('scholarships/index', [
            'data' => $data
        ]);
    }

    public function actionImmigrations()
    {
        $arr = [
            'pr-permanent-residence',
            'job-seeker-visa',
            'pr-permanent-residence/canada',
            'pr-permanent-residence/australia',
            'job-seeker-visa/germany',
            'job-seeker-visa/austria',
            'job-seeker-visa/uae',
            'job-seeker-visa/sweden',
        ];

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml');

        return $this->renderPartial('immigration/index', [
            'data' => $arr
        ]);
    }
}
