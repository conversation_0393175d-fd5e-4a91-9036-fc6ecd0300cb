<?php

namespace frontend\controllers;

use common\models\GmuSaLeads;
use common\models\SaLeadsStudentActivity;
use common\models\SaLeadsStudentOtp;
use common\services\SmsService;
use Yii;
use yii\web\Controller;
use yii\web\Response;
use yii\widgets\ActiveForm;

class StudyabroadleadController extends Controller
{
    /**
     * Undocumented function
     *
     * @return void
     */
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        if (!Yii::$app->request->isAjax) {
            return false;
        }

        $countryCode = empty($request['country_code']) ? $request['country_code_full'] : $request['country_code'];

        $saStudent = GmuSaLeads::find()->where(['phone' => $request['phone']])->one();
        if (!$saStudent) {
            $saStudent = new GmuSaLeads();

            $saStudent->phone = $request['phone'] ?? '';
            $saStudent->name = $request['name'] ?? '';
            $saStudent->email = $request['email'] ?? '';
            $saStudent->country_code = ltrim($countryCode, '+') ?? '';
            $saStudent->current_country_id = $request['current_country'] ?? '';
            $saStudent->current_city_id = $request['current_sa_city'] ?? '';
            $saStudent->source_url = $request['url'] ?? '';
            $saStudent->save();
        }

        $saStudent->name = $request['name'] ?? '';
        $saStudent->email = $request['email'] ?? '';
        $saStudent->country_code = ltrim($countryCode, '+') ?? '';
        $saStudent->current_country_id = $request['current_country'] ?? '';
        $saStudent->current_city_id = $request['current_sa_city'] ?? '';

        $saStudent->save();

        self::saStudentActivity($request, $saStudent, false);
    }

    public function saStudentActivity($request, $student, $numberChange = false)
    {
        $appeared_exam = json_encode($request['GmuSaLeads']['appeared_exam']);
        $saStudentActivity = new SaLeadsStudentActivity();
        $saStudentActivity->gmu_sa_lead_id = $student->id;
        $saStudentActivity->url = $request['url'];
        $saStudentActivity->entity = $request['entity'];
        $saStudentActivity->entity_id = $request['entity_id'];
        $saStudentActivity->study_destination_id = $request['sa_study_destination'];
        $saStudentActivity->planning_duration = $request['planning_duration'];
        $saStudentActivity->degree = $request['intrestred_degree'];
        $saStudentActivity->appeared_exam = $appeared_exam;
        $saStudentActivity->cta_location = $request['cta_location'];
        $saStudentActivity->cta_text = $request['cta_text'];
        $saStudentActivity->course = '';

        if ($saStudentActivity->save()) {
            $response = [
                'success' => true,
                'student_id' => $student->id,
                'numberChange' => $numberChange
            ];

            echo json_encode($response);
            exit;
        } else {
            $response = [
                'success' => false,
                'student_id' => $student->id,
                'numberChange' => $numberChange
            ];

            echo json_encode($response);
            exit;
        }
    }

    public function actionSaSendOtp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();

        $saStudent = GmuSaLeads::find()->where(['id' => $request['student_id']])->one();

        if ($saStudent == null) {
            return [
                'success' => false,
                'message' => 'Phone Number is not registered!',
            ];
        }

        if ($request['param'] == 'resend_otp') {
            $saStudentOtp = SaLeadsStudentOtp::find()
                ->where(['gmu_sa_lead_id' => $saStudent->id])
                ->andWhere(['is_mobile_verified' => SaLeadsStudentOtp::IS_MOBILE_VERIFIED_NO])
                ->orderBy(['id' => SORT_DESC])
                ->one();
        } else {
            $saStudentOtp = new SaLeadsStudentOtp();
        }

        $saStudentOtp->gmu_sa_lead_id = $saStudent->id;
        $saStudentOtp->otp = rand(1356, 8675);
        $saStudentOtp->save();
        SmsService::sendOtp($saStudent->phone, $saStudentOtp->otp);

        return [
            'success' => true,
            'message' => 'Otp sent successfully',
        ];
    }

    public function actionSaVerifyOtp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }

        $request = Yii::$app->request->post();

        if (strlen($request['otp']) < 4) {
            return ['success' => false, 'message' => 'Otp Field required'];
        }

        $studentOtp = SaLeadsStudentOtp::find()
            ->where(['gmu_sa_lead_id' => $request['student_id']])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        if (!$studentOtp || ($studentOtp->otp !== (int) $request['otp'])) {
            return ['success' => false, 'message' => 'Please enter a valid otp'];
        }

        $studentOtp->is_mobile_verified = SaLeadsStudentOtp::IS_MOBILE_VERIFIED_YES;
        $studentOtp->save();

        return [
            'success' => true,
            'message' => 'OTP verified.',
        ];
    }

    public function actionSaStudentSessionActivate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();

        if (empty($request['student_id'])) {
            return [
                'success' => false
            ];
        }

        $saStudent = GmuSaLeads::findIdentity($request['student_id']);

        if (!empty($saStudent)) {
            Yii::$app->saUser->login($saStudent, 3600 * 24 * 30);
            // dd($saStudent);

            return [
                'success' => true
            ];
        } else {
            return [
                'success' => false
            ];
        }
    }
}
