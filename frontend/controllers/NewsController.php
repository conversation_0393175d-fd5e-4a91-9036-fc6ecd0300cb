<?php

namespace frontend\controllers;

use common\services\UserService;
use common\services\v2\NewsService;
use frontend\helpers\Ad;
use stdClass;
use frontend\models\CommentForm;
use Yii;
use yii\caching\TagDependency;
use yii\web\NotFoundHttpException;
use common\helpers\DataHelper;
use common\models\LeadBucketTagging;
use common\models\UserTranslation;

class NewsController extends Controller
{
    protected $newsService;

    protected static $perPageCategory = 20;

    protected static $categories = ['latest', 'featured'];
    public $pageType;
    public $entityType;

    public function __construct(
        $id,
        $module,
        NewsService $newsService,
        $config = []
    ) {
        $this->newsService = $newsService;
        parent::__construct($id, $module, $config);
    }
    /**
     * News landing page
     * url: /news
     *
     **/
    public function actionIndex()
    {
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'news-index';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () {
            $latestNews = $this->newsService->getRecent(4);
            $featured = $this->newsService->getFeaturedNews(9, 8);
            $allCategory = $this->newsService->getAllCategories();
            $topsearchNews = $this->newsService->getByTopSearches();
            $response = DataHelper::trackStudentActivity('news-index');
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
            return  [
                'latestNews' => $latestNews ?? null,
                'featured' => $featured ?? null,
                'allCategory' => $allCategory,
                'topsearchNews' => $topsearchNews
            ];
        }, 60, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
        return $this->render('index', $data);
    }

    /**
     * This action is used to determine either the slug is post or category
     *
     * @param string $slug Category/News slug
     **/
    public function actionRoute($slug)
    {
        throw new NotFoundHttpException();
        
        if (in_array($slug, self::$categories)) {
            $response = DataHelper::trackStudentActivity('news-route-category');
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
            if ($slug == 'featured') {
                $posts = $this->newsService->getFeatured(9);
            } else {
                $posts = $this->newsService->getLatestNews();
            }
            return $this->staticCategory($posts, $slug);
        }
        $post = $this->newsService->getNews($slug);
        if (!empty($post->audio)) {
            $getS3Audio = DataHelper::s3Path($post->audio, 'news_audio', true);
            $post->audio = $getS3Audio;
        }
        if (!empty($post)) {
            return $this->detail($post);
        }

        $posts = $this->newsService->getCategoryBySlug($slug);
        if (!empty($posts)) {
            $response = DataHelper::trackStudentActivity('news-route-category');
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
            return $this->category($posts);
        }

        throw new NotFoundHttpException();
    }

    private function staticCategory($posts, $slug)
    {
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'news-static-category';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($slug, $posts) {
            $category = new stdClass;
            $category->name = ucfirst($slug);
            $category->slug = $slug;
            $featured = $this->newsService->getFeaturedNews(9, 4);
            $topsearchNews = $this->newsService->getByTopSearches();
            return  [
                'posts' => $posts,
                'category' => $category,
                'featuredNews' => $featured,
                'recentNews' => null,
                'topsearchNews' => $topsearchNews
            ];
        }, 60, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
        return $this->render('category', $data);
    }

    private function category($category)
    {
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'news-category';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($category) {
            $posts = $this->newsService->getByCategoryId($category->id);
            $featured = $this->newsService->getFeaturedNews(9, 4);
            $recents = $this->newsService->getRecent(5);
            return  [
                'category' => $category,
                'posts' => $posts ?? null,
                'featuredNews' => $featured ?? null,
                'recentNews' => $recents ?? null
            ];
        }, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
        return $this->render('category', $data);
    }

    private function detail($post)
    {
        $response = DataHelper::trackStudentActivity('news-detail');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];

        if ($ads = $this->newsService->getAdTargetData($post)) {
            Ad::setTargetArticleNews($ads);
        }

        $content = $this->newsService->getContent($post->id);
        if (empty($content)) {
            throw new NotFoundHttpException();
        }

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'news';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        $data = Yii::$app->cache->getOrSet($key, function () use ($post, $content) {

            $commentForm = new CommentForm();
            $featured = $this->newsService->getFeaturedNews(9, 4);
            $comments = $this->newsService->getComments($post->id);
            $recents = $this->newsService->getRecent(6);
            $category = $this->newsService->getCategoryBySlug($post->news_category_id);
            $topsearchNews = $this->newsService->getByTopSearches();
            $active_trans_data = [];

            foreach ($post->activeTranslation as $trans_data) {
                $active_trans['cu_lang'] = DataHelper::getLangCode($post->lang_code);
                $active_trans['slug'] = $trans_data['slug'];
                $active_trans['lang'] = DataHelper::getLangCode($trans_data['lang_code']);
                $active_trans_data[] = $active_trans;
            }
            $getAuthorTrans = $this->authorTranslation($content->author_id, $post->lang_code);
            if (!empty($getAuthorTrans)) {
                $post->newsContent->author->name = $getAuthorTrans->user_name;
                 $news_author = $post->newsContent->author;
            } else {
                $news_author = $post->newsContent->author;
            }
            return [
                'post' => $post ?? '',
                'category' => $category ?? null,
                'content' => $content ?? '',
                'comments' => $comments ?? null,
                'featured' => $featured ?? null,
                'editorials' => $editorials ?? null,
                'recents' => $recents ?? null,
                'relatedNws' => $this->newsService->getCategoryNews($category->id ?? '', 5) ?? null,
                'commentForm' => $commentForm,
                'topsearchNews' => $topsearchNews,
                'readNextFeature' => $this->newsService->getReadNextFeature($post->id, $category->id ?? ''),
                'author' => $news_author ?? '',
                'liveUpdate' => $post->liveUpdate ?? [],
                'newsMapping' => $post->news ?? [],
                'translation_data' => $active_trans_data,
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_NEWS, $post->id, $post->newsContent->meta_title),
            ];
        }, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
            return $this->render('detail', $data);
    }

    public function authorTranslation($author_id, $lang_code)
    {
        $translateAuthor = UserTranslation::find()->select(['user_name'])
            ->where(['tag_user_id'=>$author_id])
            ->andWhere(['lang_code'=>$lang_code])
            ->one();
        return $translateAuthor;
    }
}
