<?php

use common\helpers\ContentHelper;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use yii\helpers\BaseStringHelper;
use common\models\Lead;
use common\models\LiveUpdate;
use common\models\News;
use common\services\v2\NewsService;
use frontend\helpers\Lead as HelpersLead;
use common\helpers\DataHelper;

$this->context->ampCss = file_get_contents(Yii::getAlias('@frontend') . '/web' . Yii::$app->params['cssPath'] . 'amp/news-amp.css');
$this->context->ampCss .= file_get_contents(Yii::getAlias('@frontend') . '/web' . Yii::$app->params['cssPath'] . 'amp/news-landing-amp.css');

$this->title = 'Latest Education News: Exam Results, Board Exams , Admit Card, Admissions &  more';
$this->context->description = 'GetMyUni News offers the latest education news, updates and information on Admit Cards, Exam Results, Application forms, Time tables, Admissions, Boards & more';
$this->params['canonicalUrl'] = Url::base(true) . Url::toNews();
$isMobile = \Yii::$app->devicedetect->isMobile();
$liveTagID = LiveUpdate::LIVE_NEWS_TAG_ID;

// top searches
if (!empty($topsearchNews)) {
    $this->params['topsearchNews'] = $topsearchNews;
}

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = 'News';

$this->params['entity'] = News::ENTITY_NEWS;
$this->params['entity_id'] = '';
$this->params['entity_sub_type'] = 'news-amp-landing';
$this->params['ctaLocation1'] = 'news_landing_left_top_sticky_cta';
$this->params['ctaLocation2'] = 'news_landing_right_top_sticky_cta';
?>
<main>
    <div class="bannerSection">
        <h1>Latest Education Articles</h1>
        <div class="latestArticleSection row">
            <article class="articleDisplay">
                <?php $latestNewsCardCount = 1; ?>
                <?php foreach ($latestNews as $latest):
                    ?>
                    <div class="article-view">
                        <a href="<?= Url::toNewsDetail($latest['slug'], DataHelper::getLangCode($latest['lang_code'])) ?>" title="<?= $latest['title'] ?? '' ?>">
                            <figure>
                                <amp-img layout="responsive" width="274" height="207" src="<?= isset($latest['banner_image']) ? Url::toNewsImages($latest['banner_image']) : Url::toNewsImages(); ?>" alt="<?= $latest['title'] ?? '' ?>"></amp-img>
                            </figure>
                            <div class="aticleInfo">
                                <h2><a href="<?= Url::toNewsDetail($latest['slug'], DataHelper::getLangCode($latest['lang_code'])) ?>" title="<?= $latest['title'] ?? '' ?>">
                                        <?php $tags = (new NewsService)->liveTagExpiredAt($latest['expired_at'], $latest['tag_id']); ?>
                                        <?php
                                        if (!empty($tags) && !empty($tags == News::IS_LIVE_YES)) {
                                            echo  $this->render('partials/_live-updates-icon', [
                                                'models' => $latest['tag_id'],
                                                'isAmp'  => 1,
                                                'liveTagID' => $liveTagID,
                                                'smallIcone' => 0
                                            ]);
                                        }
                                        ?>
                                        <?= $latest['title'] ?? '' ?>
                                </h2>
                                <div class="updated-info row">
                                    <div class="updatedBy">
                                        <a class="authorName" title="<?= $latest['author'] ?? ''  ?>"><?= $latest['author'] ?? ''  ?></a>
                                    </div>
                                    <p><?= Yii::$app->formatter->asDate($latest['updated_at']) ?></p>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
                <div class="article-view">
                    <div class="viewAllDiv">
                        <a href="<?= Url::toNewsDetail('latest') ?>" title=" Latest Articles"><i class="spriteIcon viewAllIcon"></i>
                            VIEW ALL</a>
                    </div>
                </div>
            </article>
        </div>
    </div>


    <aside>
        <!-- <div class="horizontalRectangle">
            <div class="advertise">---Advertisement---</div>
            <div class="appendAdDiv" style="background:#EAEAEA;">
                <?php // echo Ad::unit('GMU_NEWS_LANDING_PAGE_WAP_ATF_300*50', '[300,50]')
                ?>
            </div>
        </div> -->
    </aside>

    <?php if (!empty($featured)): ?>
        <?= $this->render('partials/_post-list-cards', [
            'posts' => $featured,
            'title' => 'Featured',
            'categorySlug' => 'featured',
            'type' => 'amp'
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($allCategory)): ?>
        <?= $this->render('partials/_category-post-list-cards', [
            'posts' => $allCategory,
        ]) ?>
    <?php endif; ?>

    <aside>
        <!-- <div class="horizontalRectangle">
            <div class="advertise">---Advertisement---</div>
            <div class="appendAdDiv" style="background:#EAEAEA;">
                <?php // echo Ad::unit('GMU_NEWS_LANDING_PAGE_WAP_BTF_300*250', '[300,250]')
                ?>
            </div>
        </div> -->
    </aside>
</main>