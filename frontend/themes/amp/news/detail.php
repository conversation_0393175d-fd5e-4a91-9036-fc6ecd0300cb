<?php

use common\helpers\ContentHelper;
use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;
use common\models\LiveUpdate;
use common\models\News;
use common\services\v2\NewsService;
use frontend\helpers\Schema;
use common\helpers\DataHelper;

$currentUrl = Url::base(true) . Url::toNewsDetail($post->slug, DataHelper::getLangCode($post->lang_code));
$this->title = !empty($content) ? $content->meta_title . ' - Getmyuni' : '';
$this->context->description = !empty($content) ? $content->meta_description : '';
$isMobile = \Yii::$app->devicedetect->isMobile();
$liveTagID = LiveUpdate::LIVE_NEWS_TAG_ID;
$author = $content ? $content->author : '';
$authorImage = !empty($author) ? ContentHelper::getUserProfilePic($author->slug) : '';

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'News', 'url' => [Url::toNews()], 'title' => ''];
if (!empty($category)) {
    $this->params['breadcrumbs'][] = ['label' => $category->name ?? '' . ' News', 'url' => [Url::toNewsDetail($category->slug, DataHelper::getLangCode($post->lang_code))], 'title' => ($category->name ?? '') . ' News'];
}

// top searches
if (!empty($topsearchNews)) {
    $this->params['topsearchNews'] = $topsearchNews;
}
$this->params['breadcrumbs'][] = !empty($content) ? $content->meta_title : '';

// page specific css
$this->context->ampCss = file_get_contents(Yii::getAlias('@frontend') . '/web' . Yii::$app->params['cssPath'] . 'amp/news-amp.css');
// $this->context->ampCss = file_get_contents(Yii::getAlias('@frontend') . '/web/yas/css/version2/amp/news-lead-amp.css');

if (!empty($content->meta_keywords)) {
    $this->registerMetaTag(['name' => 'news_keywords', 'content' => $content->meta_keywords]);
    $this->registerMetaTag(['name' => 'Keywords', 'content' => $content->meta_keywords]);
}
$this->registerMetaTag(['name' => 'Googlebot-news', 'content' => 'index, follow']);
if (!empty($post) && !empty($post->banner_image)) {
    $this->registerMetaTag(['property' => 'og:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'og:image:height', 'content' => '667']);
    $this->registerMetaTag(['property' => 'og:image:alt', 'content' => $this->title]);
    $this->registerMetaTag(['property' => 'twitter:image:type', 'content' => 'image/jpeg']);
    $this->registerMetaTag(['property' => 'twitter:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'twitter:image:height', 'content' => '667']);
    $this->registerLinkTag(['rel' => 'preload', 'as' => 'image', 'href' => $post->banner_image ? Url::toNewsImages($post->banner_image) : '']);
    $this->context->ogImage = $post->banner_image ? Url::toNewsImages($post->banner_image) : '';
}

$this->params['canonicalUrl'] = $currentUrl;

$liveUpdateDate = !empty($post->liveUpdate) ? $post->liveUpdate[0]->updated_at : '';

// Schema.org
$this->params['schema'] = \yii\helpers\Json::encode([
    Schema::newsSchema($post, $liveUpdateDate, $currentUrl, 'amp')
], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

//LiveUpdate Schema
if (!empty($post) && isset($post->is_live) && $post->is_live == News::IS_LIVE_YES) {
    if (!empty($post->liveUpdate)) {
        $this->params['schema1'] = \yii\helpers\Json::encode([
            Schema::liveNewsSchema($post, $post->liveUpdate, $currentUrl, 'amp')
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }
}

$this->params['entity'] = News::ENTITY_NEWS;
$this->params['entity_id'] = $post->id;
$this->params['dynamicCta'] = $dynamicCta ?? [];
$this->params['newsName'] = $post->name ?? '';
$this->params['ctaLocation1'] = 'news_detail_' . $post->slug . '_left_top_sticky_cta';
$this->params['ctaLocation2'] = 'news_detail_' . $post->slug . '_right_top_sticky_cta';
$this->params['entity_sub_type'] = 'news-amp-detail';
?>
<style amp-boilerplat>
    .getSupport {
        display: none;
    }

    .getSupport.topCta {
        position: relative;
        bottom: 0;
        left: 0;
        margin-top: 10px;
        width: 100%;
        border-radius: 0;
        gap: 8px;
        padding: 0;
        z-index: 4;
        display: flex;
        box-shadow: none;
        border: none;
    }
</style>
<div class="row">
    <div class="col-12">
        <main>
            <article>
                <header class="bannerDiv bannerDivMargin">
                    <h1>
                        <?php $tags = (new NewsService)->liveTagExpiredAt($post->expired_at, $post->is_live); ?>
                        <?php
                        if (!empty($tags) && !empty($tags == News::IS_LIVE_YES)) {
                            echo $this->render('partials/_live-updates-icon', [
                                'models' => $post->is_live,
                                'isAmp'  => 0,
                                'liveTagID' => $liveTagID,
                                'smallIcone' => 0
                            ]);
                        }
                        ?>
                        <?= !empty($content) ? $content->h1 : '' ?>
                    </h1>
                    <?php if (!empty($content->meta_description)): ?>
                        <h2><?= $content->meta_description ?></h2>
                    <?php endif; ?>
                    <div class="updated-info row">
                        <?php if (!empty($authorImage)): ?>
                            <div class="updatedBy">
                                <img height="36" width="36" data-src="<?= $authorImage ?? '' ?>" src="<?= $authorImage ?? '' ?>" alt="">
                            </div>
                        <?php endif; ?>
                        <?php
                        $updatedAt = $content->updated_at ? new DateTime($content->updated_at) : '';
                        $date = $updatedAt ? $updatedAt->format('M d, Y') : '';
                        $time = $updatedAt ? $updatedAt->format('h:i A') : '';
                        ?>
                        <div class="authorAndDate">
                            <?php if (!empty($author)): ?>
                                <a href="<?= Url::toAllAuthorPost($author->slug) ?>" class="authorName"><?= $author->name ?? '' ?></a>
                            <?php endif; ?>
                            <span class="spriteIcon verifiedBlueTickIcon"></span>
                            <p>Updated on -<?= Yii::$app->formatter->asDate($date) . ' | ' . $time ?> IST
                                <!-- <a href="javascript::">Career Guide,</a>
                                        <a href="javascript::"> Full Form</a> -->
                            </p>
                        </div>
                    </div>
                    <div class="getSupport topCta">
                        <button class="open-lead-form" id="leadpopup" on="tap:my-bindable-lightbox-lead.open"><?= empty($this->params['dynamicCta']) || empty($this->params['dynamicCta']['cta_position_2']) || empty(array_filter($this->params['dynamicCta']['cta_position_2'])) ? 'Subscribe' : (!empty($this->params['dynamicCta']['cta_position_2']['cta_text']) ? $this->params['dynamicCta']['cta_position_2']['cta_text'] : 'Subscribe') ?></button>
                        <button class="open-lead-form" id="leadpopuptwo" on="tap:my-bindable-lightbox-lead.open"><?= empty($this->params['dynamicCta']) || empty($this->params['dynamicCta']['cta_position_3']) || empty(array_filter($this->params['dynamicCta']['cta_position_3'])) ? 'Set News Alert' : (!empty($this->params['dynamicCta']['cta_position_3']['cta_text']) ? $this->params['dynamicCta']['cta_position_3']['cta_text'] : 'Set News Alert') ?></button>
                    </div>
                </header>

                <!-- <div class="horizontalRectangle">
                    <div class="advertise">Advertisement</div>

                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php // echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WAP_ATF_300*50', '[300,50]')
                        ?>
                    </div>
                </div> -->
                <?php if (!empty($post) && !empty($post->banner_image)): ?>
                    <div class="bannerImg">
                        <amp-img layout="responsive" width="300" height="200" src="<?= !empty($post->banner_image) ? Url::toNewsImages($post->banner_image) : Url::toNewsImages(); ?>" alt="<?= $post->name ?? '' ?>">
                    </div>
                    <?= !empty($post->banner_caption) ? ContentHelper::htmlDecode($post->banner_caption, true) : '' ?>
                <?php endif; ?>

                <div class="articleInfo">
                    <?= !empty($content) ? ContentHelper::parseAmpNewContent($content->content) : '' ?>
                    <?php if (!empty($post) && !empty($post->liveUpdate)): ?>
                        <?= $this->render('partials/_live-updates', [
                            'models' => $post->liveUpdate ?? ''
                        ]) ?>
                    <?php endif; ?>
                </div>
                <div class="shareSection">
                    <div>
                        <p>Share Via</p>
                        <?php if (!$isMobile) { ?>
                            <a target="_blank" href="https://web.whatsapp.com/send?text=<?= $currentUrl; ?>" data-action="share/whatsapp/share"><span class="spriteIcon shareIcon whatsappIcon"></span></a>
                        <?php  } else { ?>
                            <a target="_blank" href="https://api.whatsapp.com/send?text=<?= $currentUrl; ?>" data-action="share/whatsapp/share"><span class="spriteIcon shareIcon whatsappIcon"></span></a>
                        <?php } ?>
                        <a target="_blank" href="http://twitter.com/share?url=<?= $currentUrl; ?>"> <span class="spriteIcon shareIcon bigTwitterIcon"></span> </a>
                        <a target="_blank" href="https://mail.google.com/mail/?view=cm&fs=1&tf=1&to=&su=Share+Link+&body=<?= $currentUrl ?>&ui=2&tf=1&pli=1"> <span class="spriteIcon shareIcon gmailIcon"></span> </a>
                        <!-- <span onclick="copyToClipboard('<?= $currentUrl ?>')" title="Copy to clipboard" class="spriteIcon shareIcon copyClipboardIcon"></span> -->
                        <!-- <span id="custom-tooltip" style="display:none;">copied!</sapn> -->
                    </div>
                    <a class="followUs" target="_blank" href="https://news.google.com/publications/CAAqBwgKMKLYqAswlOPAAw?hl=en-IN&gl=IN&ceid=IN:en">
                        <p>Follow Us On <span>Google News</span></p>
                        <span class="spriteIcon shareIcon googleNewsIcon"></span>
                    </a>
                </div>
                <?php if (!empty($readNextFeature)): ?>
                    <div class="nextStorySection">
                        <p class="fweight-500">NEXT STORY</p>
                        <?php foreach ($readNextFeature as $value): ?>
                            <?php if (empty($value)) {
                                return '';
                            } ?>
                            <div class="row">
                                <div class="imgDiv">
                                    <figure><img class="lazyload" data-src="<?= !empty($value['banner_image']) ? Url::toNewsImages($value['banner_image']) : '' ?>" src="<?= !empty($value['banner_image']) ? Url::toNewsImages($value['banner_image']) : '' ?>" alt="<?= $value['display_name'] ?? $value['name'] ?>"></figure>
                                </div>
                                <div class="textDiv">
                                    <a class="nextStoryHeading" href="<?= Url::toNewsDetail($value['slug'], DataHelper::getLangCode($value['lang_code'])) ?>"><?= $value['name'] ?? $value['display_name'] ?></a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </article>
        </main>

        <!-- <div class="horizontalRectangle">
            <div class="advertise">Advertisement</div>

            <div class="appendAdDiv" style="background:#EAEAEA;">
                <?php // echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WAP_MTF_2_300*250', '[300,250]')
                ?>
            </div>
        </div> -->


    </div>

    <div class="col-12">
        <aside>
            <!-- <div class="horizontalRectangle">
                <div class="advertise">Advertisement</div>

                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php // echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WAP_BTF_300*250', '[300,250]')
                    ?>
                </div>
            </div> -->

            <div class="newsSidebarSection">
                <amp-selector class="tabs-with-flex" role="tablist" keyboard-select-mode="focus">
                    <?php if (!empty($featured)): ?>
                        <div id="tab1" role="tab" aria-controls="tabpanel1" option selected>Featured News</div>
                    <?php endif; ?>

                    <?php if (!empty($featured)): ?>
                        <div id="tabpanel1" role="tabpanel" aria-labelledby="tab1">
                            <div class="trendingNews recentnewsList" id="trendingNews">
                                <?php foreach ($featured as $feature):
                                    $lang_code = array_search($feature['lang_code'], DataHelper::$languageCode);
                                    ?>
                                    <?php if (empty($feature)) {
                                        return '';
                                    } ?>
                                    <?php if (!empty($post) && $feature['slug'] == $post->slug): ?>
                                        <?php continue; ?>
                                    <?php endif; ?>
                                    <a class="listCard" href="<?= Url::toNewsDetail($feature['slug'], DataHelper::getLangCode($feature['lang_code'])) ?>" title="<?= $feature['title'] ? $feature['title'] : '' ?>">
                                        <div class="recentnewsDiv row">
                                            <div class="sidebarImgDiv">
                                                <amp-img layout="responsive" height="72px" width="96" src="<?= !empty($feature['banner_image']) ? Url::toNewsImages($feature['banner_image']) : Url::toNewsImages(); ?>" alt="<?= $feature['title'] ? $feature['title'] : '' ?>"></amp-img>
                                            </div>
                                            <div class="recentnewsDivText">
                                                <p class="sidebarTextLink">
                                                    <?php $tags = (new NewsService)->liveTagExpiredAt($feature['expired_at'], $feature['is_live']); ?>
                                                    <?php if (!empty($tags) && !empty($tags == News::IS_LIVE_YES)): ?>
                                                        <?= $this->render('../../../frontend/views/news/partials/_live-updates-icon', [
                                                            'models' => $feature['is_live'],
                                                            'isAmp'  => 1,
                                                            'liveTagID' => $liveTagID,
                                                            'smallIcone' => 1
                                                        ]); ?>
                                                    <?php endif; ?>
                                                    <?= !empty($feature['title']) ? $feature['title'] : '' ?></p>
                                            </div>
                                        </div>
                                    </a>
                                <?php endforeach; ?>

                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($recents)): ?>
                        <div id="tab2" role="tab" aria-controls="tabpanel2" option selected>Recent News</div>
                    <?php endif; ?>
                    <?php if (!empty($recents)): ?>
                        <div id="tabpanel2" role="tabpanel" aria-labelledby="tab2">
                            <div class="recentnews tab-content recentnewsList" id="recentnews">
                                <?php foreach ($recents as $recent): ?>
                                    <?php if (empty($recent)) {
                                        return '';
                                    } ?>
                                    <?php if (!empty($post) && $recent['slug'] == $post->slug): ?>
                                        <?php continue; ?>
                                    <?php endif; ?>
                                    <a class="listCard" href="<?= Url::toNewsDetail($recent['slug'], DataHelper::getLangCode($recent['lang_code'])) ?>" title="<?= !empty($recent['title']) ? $recent['title'] : '' ?>">
                                        <div class="recentnewsDiv row">
                                            <div class="sidebarImgDiv">
                                                <amp-img layout="responsive" height="72px" width="96" src="<?= !empty($recent['banner_image']) ? Url::toNewsImages($recent['banner_image']) : Url::toNewsImages(); ?>" alt="<?= !empty($recent['title']) ? $recent['title'] : '' ?>"></amp-img>
                                            </div>
                                            <div class="recentnewsDivText">
                                                <p class="sidebarTextLink">
                                                    <?php $tags = (new NewsService)->liveTagExpiredAt($recent['expired_at'], $recent['tag_id']); ?>
                                                    <?php
                                                    if (!empty($tags) && !empty($tags == News::IS_LIVE_YES)) {
                                                        echo  $this->render('../../../frontend/views/news/partials/_live-updates-icon', [
                                                            'models' => $recent['tag_id'],
                                                            'isAmp'  => 1,
                                                            'liveTagID' => $liveTagID,
                                                            'smallIcone' => 1
                                                        ]);
                                                    }
                                                    ?>
                                                    <?= !empty($recent['title']) ? $recent['title'] : '' ?></p>
                                            </div>
                                        </div>
                                    </a>
                                <?php endforeach; ?>

                            </div>
                        </div>
                    <?php endif; ?>
                </amp-selector>
            </div>

            <!-- <div class="verticleRectangle">
                <div class="advertise">Advertisement</div>
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php // echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WAP_MTF_1_300*250', '[300,250]')
                    ?>
                </div>
            </div> -->

        </aside>
    </div>
</div>

<div class="contentProvider">
    <div class="profilePic">
        <amp-img layout="responsive" width="48" height="48" src="https://getmyuni.com/yas/images/userIcon.png" alt="">
        </amp-img>
    </div>
    <div class="profileInfo">
        <p class="name"><?= $author ? $author->name : '' ?></p>
        <p class="position">Content Writer, GETMYUNI</p>
        <p><?= !empty($author->profile) ? $author->profile->about : '' ?></p>
    </div>
</div>

<section class="commentSection">
    <div class="form-group">
        <div class="col-lg-offset-1 col-lg-11">
            <a href="<?= $currentUrl ?>#write-comment" class="primaryBtn commentBtn w-100">POST YOUR COMMENT</a>
        </div>
    </div>
</section>

<?php if (!empty($relatedNws)): ?>
    <section class="latestInfoSection">
        <h2 class="row">
            RELATED NEWS</h2>
        <div class="latestInfoListContainer four-cardDisplay">
            <!--i class="spriteIcon scrollLeft over"></i>
            <i class="spriteIcon scrollRight"></i-->
            <div class="latestInfoList row">
                <?php $relatedNwsIteration = 0; ?>
                <?php foreach ($relatedNws as $related): ?>
                    <?php if ($post->slug == $related['slug'] || $relatedNwsIteration > 3): ?>
                        <?php continue; ?>
                    <?php endif; ?>
                    <div class="latestInfoDiv">
                        <a href="<?= Url::toNewsDetail($related['slug'], DataHelper::getLangCode($related['lang_code'])) ?>" title="<?= $related['title'] ?? '' ?>">
                            <figure>
                                <img width="274" height="207" data-src="<?= !empty($related['banner_image']) ? Url::toNewsImages($related['banner_image']) : Url::toNewsImages(); ?>" src="<?= !empty($related['banner_image']) ? Url::toNewsImages($related['banner_image']) : Url::toNewsImages(); ?>" alt="">
                            </figure>
                            <div class="latestInfoTxt">
                                <p class="latestInfoTxt-title"><?= !empty($related['title']) ? BaseStringHelper::truncate($related['title'] ?? '', 95) : '' ?></p>
                            </div>
                        </a>
                        <p class="articleAuthorName"><?= !empty($related['author']) ? $related['author'] : '' ?></p>
                    </div>
                    <?php $relatedNwsIteration++ ?>
                <?php endforeach;  ?>
            </div>
        </div>
    </section>
<?php endif; ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        window.addEventListener('scroll', function() {
            var offsetForToTop = window.scrollY;

            var getSupportElement = document.querySelector('.getSupport');

            if (offsetForToTop > 250) {
                getSupportElement.style.display = 'flex';
                getSupportElement.style.opacity = '1';
            } else {
                getSupportElement.style.opacity = '0';
                setTimeout(function() {
                    getSupportElement.style.display = 'none';
                }, 300); // Match fadeOut delay if desired
            }
        });
    });
</script>