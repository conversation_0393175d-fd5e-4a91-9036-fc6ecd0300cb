<?php

use common\helpers\ContentHelper;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use justinvoelker\separatedpager\LinkPager;
use yii\helpers\BaseStringHelper;
use common\models\Lead;
use common\models\LiveUpdate;
use common\models\News;
use frontend\helpers\Lead as HelpersLead;
use yii\widgets\ListView;

$this->context->ampCss = file_get_contents(Yii::getAlias('@frontend') . '/web' . Yii::$app->params['cssPath'] . 'amp/news-amp.css');
$this->context->ampCss .= file_get_contents(Yii::getAlias('@frontend') . '/web' . Yii::$app->params['cssPath'] . 'amp/news-category-amp.css');
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'News', 'url' => [Url::toNews()], 'title' => 'News'];


if (Yii::$app->request->get('page') > 1) {
    $this->params['breadcrumbs'][] = [
        'label' => $category->name ?? '',
        'url' => Url::canonical(),
        'title' => $category->name . ' News'
    ];
    $currentPageNum = $this->params['breadcrumbs'][] = 'Page ' . Yii::$app->request->get('page');
    $this->params['canonicalUrl'] = Url::base(true) . Url::toNewsDetail($category->slug) . '?page=' . Yii::$app->request->get('page');
} else {
    $this->params['canonicalUrl'] = Url::base(true) . Url::toNewsDetail($category->slug);
    $this->params['breadcrumbs'][] = $category->name;
}

$this->title = ($category->slug == 'latest' ? 'Latest' : 'Latest ' . $category->name) . ' News in India' . (isset($currentPageNum) ? ' - ' . $currentPageNum : '');
$this->context->description = "{$category->name} News in India: Find the latest <Category> and detailed information on notification, application form, admit card, result, cut off etc.";
$isMobile = \Yii::$app->devicedetect->isMobile();

$liveTagID = LiveUpdate::LIVE_NEWS_TAG_ID;
//top search
if (!empty($topsearchNews)) {
    $this->params['topsearchNews'] = $topsearchNews;
}

$this->params['entity'] = News::ENTITY_NEWS;
$this->params['entity_id'] = '';
$this->params['dynamicCta'] = $dynamicCta ?? [];
$this->params['newsName'] = '';
$this->params['ctaLocation1'] = 'news_category_left_top_sticky_cta';
$this->params['ctaLocation2'] = 'news_category_right_top_sticky_cta';
$this->params['entity_sub_type'] = 'news-amp-category';
?>

<div class="pageHeading">
    <h1><?= $category->name ?> News <?= isset($currentPageNum) ? ' - ' . $currentPageNum : '' ?></h1>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="categoryArticlesContainer">
            <div class="categoryArticlesList">
                <?php
                echo ListView::widget([
                    'dataProvider' => $posts,
                    'itemView' => 'partials/_amp-news-lists',
                    'pager' => [
                        'class' => 'LinkPager',
                    ],
                    'viewParams' => [
                        'fullView' => true,
                        'context' => 'main-page',
                        'isMobile' => $isMobile,
                    ],
                    'layout' => "{items}\n{pager}",
                    'pager' => [
                        'class' => '\justinvoelker\separatedpager\LinkPager',
                        'maxButtonCount' => $isMobile ? 4 : 7,
                        // 'prevPageLabel' => 'Previous',
                        // 'nextPageLabel' => 'Next',
                        'prevPageCssClass' => 'prev hidden-xs',
                        'nextPageCssClass' => 'next hidden-xs',
                        'activePageAsLink' => false,
                    ]
                ]);
                ?>
            </div>
        </div>
    </div>
    <div class="col-12">
        <aside>
            <div class="newsSidebarSection">
                <amp-selector class="tabs-with-flex" role="tablist" keyboard-select-mode="focus">
                    <?php if (!empty($featuredNews)): ?>
                        <div id="tab1" role="tab" aria-controls="tabpanel1" option selected>Featured News</div>
                        <div id="tabpanel1" role="tabpanel" aria-labelledby="tab1">
                            <div class="trendingNews recentnewsList " id="trendingNews">
                               
                                    <?php foreach ($featuredNews as $feature): ?>
                                        <a href="<?= !empty($feature['slug']) ? Url::toNewsDetail($feature['slug']) : '' ?>" title="<?= !empty($feature['title']) ? $feature['title'] : '' ?>" class="listCard">
                                            <div class="recentnewsDiv row">
                                                <div class="sidebarImgDiv">
                                                    <amp-img layout="responsive" height="72px" width="96" src="<?= !empty($feature['banner_image']) ? Url::toNewsImages($feature['banner_image']) : Url::toNewsImages(); ?>"></amp-img>
                                                </div>
                                                <div class="recentnewsDivText">
                                                    <p class="sidebarTextLink"><?= !empty($feature['title']) ? BaseStringHelper::truncateWords($feature['title'], 7) : '' ?></p>
                                                </div>
                                            </div>
                                        </a>
                                    <?php endforeach; ?>
                               
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($recentNews)): ?>
                        <div id="tab2" role="tab" aria-controls="tabpanel2" option selected>Recent News</div>
                        <div id="tabpanel2" role="tabpanel" aria-labelledby="tab2">
                            <div class="recentnews tab-content  recentnewsList" id="recentnews">
                                
                                    <?php foreach ($recentNews as $recent): ?>
                                        <a href="<?= !empty($recent['slug']) ? Url::toNewsDetail($recent['slug']) : '' ?>" title="<?= !empty($recent['title']) ? $recent['title'] : '' ?>" class="listCard">
                                            <div class="recentnewsDiv row">
                                                <div class="sidebarImgDiv">
                                                    <amp-img layout="responsive" height="72px" width="96" src="<?= !empty($recent['banner_image']) ? Url::toNewsImages($recent['banner_image']) : Url::toNewsImages(); ?>" alt="<?= !empty($recent['title']) ? $recent['title'] : '' ?>"></amp-img>
                                                </div>
                                                <div class="recentnewsDivText">
                                                    <p class="sidebarTextLink"><?= !empty($recent['title']) ? BaseStringHelper::truncateWords($recent['title'], 7) : '' ?></p>
                                                </div>
                                            </div>
                                        </a>
                                    <?php endforeach; ?>
                               
                            </div>
                        </div>
                    <?php endif; ?>
                </amp-selector>
            </div>


        </aside>

        <aside>
            <!-- <div class="verticleRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <div class="advertise">---Advertisement---</div>
                    <?php //echo Ad::unit('GMU_NEWS_CATEGORY_NEWS_WAP_BTF_300*250', '[300,250]')
                    ?>
                </div>
            </div> -->
        </aside>
    </div>
</div>