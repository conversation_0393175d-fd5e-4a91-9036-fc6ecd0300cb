<?php

use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;
use common\helpers\DataHelper;

$isAmp = isset($type) && $type == 'amp' ? true : false;

?>
<section class="latestInfoSection">

    <h2 class="row">
        <?= $title ?>
        <?php if (isset($categorySlug) && false == $isAmp): ?>
            <a href="<?= Url::toNewsDetail($categorySlug) ?>" title="<?= ucwords(str_replace('-', ' ', $categorySlug)) ?> News">VIEW ALL</a>
        <?php endif; ?>
    </h2>

    <div class="latestInfoList row">
        <?php foreach ($posts as $post):?>
            <?php if (empty($post)) {
                return '';
            } ?>
            <div class="latestInfoDiv">
                <a href="<?= Url::toNewsDetail($post['slug'], DataHelper::getLangCode($post['lang_code'])) ?>" title="<?= $post['title'] ?>">
                    <figure>
                        <?php if ($isAmp): ?>
                            <amp-img layout="responsive" width="323" height="231" src="<?= isset($post['banner_image']) ? Url::toNewsImages($post['banner_image']) : Url::toNewsImages(); ?>" alt="<?= $post['title'] ?>"></amp-img>
                        <?php else: ?>
                            <img width="274" height="207" data-src="<?= isset($post['banner_image']) ? Url::toNewsImages($post['banner_image']) : Url::toNewsImages(); ?>" src="<?= isset($post['banner_image']) ? Url::toNewsImages($post['banner_image']) : Url::toNewsImages(); ?>" alt="<?= $post['title'] ?>">
                        <?php endif; ?>
                    </figure>
                    <div class="latestInfoTxt">
                        <h3><?= BaseStringHelper::truncate($post['title'], 92) ?></h3>
                        <p><?= $post['author'] ?></p>
                    </div>
                </a>
            </div>
        <?php endforeach; ?>

        <div class="latestInfoDiv mobileOnly">
            <div class="viewAllDiv">
                <a href="<?= Url::toNewsDetail($categorySlug) ?>" title="<?= ucwords(str_replace('-', ' ', $categorySlug)) ?> News"><i class="spriteIcon viewAllIcon"></i>
                    VIEW ALL</a>
            </div>
        </div>
    </div>
</section>