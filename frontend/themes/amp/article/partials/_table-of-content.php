<?php
use frontend\helpers\Url;

if (!empty($h2Content)):
    ?>
    <amp-script   src="<?= Url::toDomain() ?>yas/js/version2/_table_of_content.js" width="1" height="1" sandbox="allow-forms" layout="container">
    <div class="table-content-ctn">
        <p class="table-content-heading-article" >
            <span>Table of Contents</span>
            <span class="spriteIcon downArrowIcon" id="list-toc"></span>
        </p>
        <ul class="table-content-article open" style="" id="table-content-article" >
            <?php foreach ($h2Content as $key => $text):
                $string = htmlentities($key, null, 'utf-8');
                $content = str_replace('&nbsp;', '', $string);
                $key = strtolower(html_entity_decode($content));
                if ($key != ''):
                    ?>

                    <li><a  href="#<?= $key; ?>" data-attr-id="<?= $key; ?>" class="scrollh2"><?= $text; ?></a></li>

                    <?php
                endif;
            endforeach;
            ?>
        </ul>
    </div>
    </amp-script>
    
<?php endif;?>






