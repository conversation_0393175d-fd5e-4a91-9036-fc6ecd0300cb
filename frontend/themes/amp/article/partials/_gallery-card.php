<?php

use common\helpers\CollegeHelper;
use frontend\helpers\Url;

foreach ($images as $key => $value):
    if ($key == 'videos') {
        continue;
    } ?>

    <h2><?= CollegeHelper::$galleryCategory[$key] ?> <?= Yii::t('app', 'Images')?></h2>
    <div class="galleryImageList row">
        <?php $i = 1;
        foreach ($value as $val): ?>
            <div class="galleryImage">
                <a href="<?= Url::getCollegeImage($college->slug, $val) ?>" data-fancybox="gallery" data-caption="Caption Images <?= $i ?> ">
                    <img class="lazyload" width="178" height="140" loading="lazy" data-src="<?= Url::getCollegeImage($college->slug, $val) ?>" src="<?= Url::getCollegeImage($college->slug, $val) ?>" alt="">
                </a>
            </div>
            <?php $i++;
        endforeach; ?>
    </div>

<?php endforeach; ?>