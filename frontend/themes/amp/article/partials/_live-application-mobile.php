<?php

use common\helpers\CollegeHelper;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
?>
<div class="sideBarSection <?= $isMobile ? 'mobileOnly' : 'desktopOnly' ?>">
    <p class="sidebarHeading"><span class="spriteIcon badgeIcon"></span> <?= Yii::t('app', 'LIVE APPLICATION FORMS')?></p>
    <div class="sidebarLinks overflow-scroll">

        <?php
        if ($entityName == 'courses'): ?>
            <?php foreach ($liveApplicationForm as $application):
                if (!isset($application['link']) || empty($application['link'])) {
                    continue;
                }
                ?>
                <a href="<?= $application['link'] ?>" title="<?= $application['name'] ?>" class="listCard" target="_blank" rel=nofollow>
                    <div class="row">
                        <div class="sidebarImgDiv">
                            <img class="lazyload" loading="lazy" width="72" height="72" data-src="<?= Url::getCollegeLogo($application['image']) ?>" src="<?= Url::getCollegeLogo($application['image']) ?>" alt="logo">
                        </div>
                        <div class="sidebarTextLink">
                            <p class="cardText"><?= $application['name'] ?></p>
                            <?= ($application['fees']) ? '| (Total Fees) ' . $application['fees'] : '' ?></p>
                            <p class="applyText"><?= Yii::t('app', 'APPLY NOW')?></p>
                        </div>
                    </div>
                </a>
                <?php
            endforeach; ?>
        <?php else:
            foreach ($liveApplicationForm as $application):
                if (!isset($application['redirect_url']) || empty($application['redirect_url'])) {
                    continue;
                }
                ?>
                <a href="<?= $application['redirect_url'] ?>" title="<?= $application['college_name'] ?>" class="listCard" target="_blank" rel=nofollow>
                    <div class="row">
                        <div class="sidebarImgDiv">
                        <?php if ($application['logo_image']) { ?>
                                <img class="lazyload" width="72" height="72" loading="lazy" data-src="<?= $assetUrl; ?>small/<?= $application['logo_image'] ?>" src="<?= $assetUrl; ?>small/<?= $application['logo_image'] ?>" alt="logo">
                        <?php } else { ?>
                                <img class="lazyload" width="72" height="72" loading="lazy" data-src="<?= $assetUrl; ?>small/<?= $application['college_slug'] ?>.jpg" src="<?= $assetUrl; ?>/small/<?= $application['college_slug'] ?>.jpg" alt="logo">
                        <?php } ?>
                        </div>
                        <div class="sidebarTextLink">
                            <p class="cardText"><?= $application['college_name'] ?></p>
                            <p class="subText"><?= $application['course_name'] ?>
                            <?= ($application['total_fees']) ? '| (Total Fees) ₹' . CollegeHelper::feesFormat($application['total_fees']) : '' ?></p>
                            <p class="applyText"><?= Yii::t('app', 'APPLY NOW')?></p>
                        </div>
                    </div>
                </a>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>