<?php

use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();

?>

<section class="pageData">
    <h2 class="row">
        <?= $title ?>
        <?php if (isset($viewAllUrl) && !$isMobile): ?>
            <a href="<?= Url::toAllCollege($viewAllUrl) ?>"><?= Yii::t('app', 'View All')?></a>
        <?php endif; ?>
    </h2>
    <div class="customSlider four-cardDisplay">

        <?php if (count($colleges) > 4): ?>
            <i class="spriteIcon scrollLeft over"></i>
            <i class="spriteIcon scrollRight"></i>
        <?php endif; ?>

        <div class="customSliderCards">
            <?php foreach ($colleges as $college): ?>
                <div class="sliderCardInfo">
                   
                        <a href="<?= Url::toCollege($college->slug) ?>" title="<?= $college->name ?>">
                        <!-- <noscript>
                            <picture> -->
                                <source media="(max-width: 500px)" srcset="<?= !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner() ?>">
                                <img class="lazyload" width="275" height="207" loading="lazy" data-src="<?= !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner() ?>" src="<?= !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner() ?>" alt="">
                            <!-- </picture>
                        </noscript> -->

                            <div class="textDiv pb-0">
                                <!-- <noscript>
                                    <picture> -->
                                        <source media="(max-width: 500px)" srcset="<?= !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo() ?>">
                                        <img class="collegeLogo lazyload" width="56" height="56" loading="lazy" data-src="<?= !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo() ?>" src="<?= !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo() ?>" alt="">
                                    <!-- </picture>
                                </noscript> -->
                                <p class="widgetCardHeading"><?= $college->name ?></p>
                                </p>
                            </div>
                        </a>
                        <p class="subText pt-0"><span class="spriteIcon locationIcon"></span> <?= $college->city->name ?>, <?= $college->city->state->name ?>

                   
                </div>
            <?php endforeach ?>

            <?php if (isset($viewAllUrl) && $isMobile): ?>
                <div class="sliderCardInfo mobileOnly">
                    <div class="viewAllDiv">
                        <a href="<?= Url::toAllCollege($viewAllUrl) ?>"><i class="spriteIcon viewAllIcon"></i><?= Yii::t('app', 'View All')?></a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>