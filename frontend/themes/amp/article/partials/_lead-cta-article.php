<?php

use common\services\UserService;
use common\models\Article;
use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
$defaultEmptyCondition1 = empty($dynamicCta) || (isset($dynamicCta['cta_position_0']) && empty($dynamicCta['cta_position_0'])) || (isset($dynamicCta['cta_position_0']) && empty(array_filter($dynamicCta['cta_position_0'])));
$defaultEmptyCondition2 = empty($dynamicCta) || (isset($dynamicCta['cta_position_1']) && empty($dynamicCta['cta_position_1'])) || (isset($dynamicCta['cta_position_1']) && empty(array_filter($dynamicCta['cta_position_1'])));

?>
<?= frontend\helpers\Html::leadButton(
    $defaultEmptyCondition1 ? 'Subscribe' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Subscribe'),
    [
        'entity' => Article::ENTITY_ARTICLE,
        'entityId' => $article->id,
        'ctaLocation' => $defaultEmptyCondition1 ? UserService::parseDynamicCta('articles_{slug}_detail_web_lead_capture_panel_left_cta2', '', $article->slug) : ($dynamicCta['cta_position_0']['web'] ?? null),
        'ctaText' => $defaultEmptyCondition1 ? 'Subscribe' : ($dynamicCta['cta_position_0']['cta_text'] ?? 'Subscribe'),
        'leadformtitle' => $defaultEmptyCondition1 ? 'REGISTER FOR EXPERT GUIDANCE' : ($dynamicCta['cta_position_0']['lead_form_title'] ?? 'REGISTER FOR EXPERT GUIDANCE'),
        'subheadingtext' => $defaultEmptyCondition1 ? 'Register and talk to our experts.' : ($dynamicCta['cta_position_0']['lead_form_description'] ?? 'Register and talk to our experts.'),
        'image' => Url::defaultCollegeLogo(),
        'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition1 ? null : ($dynamicCta['cta_position_0']['page_link'] ?? null)) : null,
    ],
    ['class' => 'talkToExpert registerNow'],
    'js-open-lead-form-new'
)
?>

<?= frontend\helpers\Html::leadButton(
    $defaultEmptyCondition2 ? 'Get ₹1 Lakh Scholarship' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Get ₹1 Lakh Scholarship'),
    [
        'entity' => Article::ENTITY_ARTICLE,
        'entityId' => $article->id,
        'ctaLocation' => $defaultEmptyCondition2 ? UserService::parseDynamicCta('articles_{slug}_detail_web_lead_capture_panel_right_cta2', '', $article->slug) : ($dynamicCta['cta_position_1']['web'] ?? null),
        'ctaText' => $defaultEmptyCondition2 ? 'Get 1 Lakh Scholarship' : ($dynamicCta['cta_position_1']['cta_text'] ?? 'Get 1 Lakh Scholarship'),
        'leadformtitle' => $defaultEmptyCondition2 ? 'APPLY NOW TO GET ONE LAKH SCHOLARSHIP' : ($dynamicCta['cta_position_1']['lead_form_title'] ?? 'APPLY NOW TO GET ONE LAKH SCHOLARSHIP'),
        'subheadingtext' => $defaultEmptyCondition2 ? 'Fill in your details and stand a chance to get our student scholarship.' : ($dynamicCta['cta_position_1']['lead_form_description'] ?? 'Fill in your details and stand a chance to get our student scholarship.'),
        'image' => Url::defaultCollegeLogo(),
        'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition2 ? null : ($dynamicCta['cta_position_1']['page_link'] ?? null)) : null,
    ],
    ['class' => 'articleScholarship applyNow registerNow'],
    'js-open-lead-form-new'
);

