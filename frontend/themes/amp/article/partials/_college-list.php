<?php

use common\helpers\CourseHelper;
use common\helpers\DataHelper;
use common\models\College;
use frontend\helpers\Url;

if (!empty($collegeList)):
    ?>

    <div class="sideBarSection customLogo">
        <p class="listCard"><?= Yii::t('app', 'Top')?> <?= Yii::t('app', $stream->name) ?> <?= Yii::t('app', 'Colleges')?></p>
        <div class="sidebarLinks">
            <?php foreach ($collegeList as $college): ?>
                <a href="<?= Url::toCollege($college['slug']) ?>" title="<?= $college['name'] ?>" class="listCard row">
                        <div class="sidebarImgDiv">
                            <img class="lazyload" loading="lazy" width="56" height="56" data-src="<?= !empty($college['logo_image']) ? Url::getCollegeLogo($college['logo_image']) : Url::defaultCollegeLogo() ?>" src="<?= !empty($college['logo_image']) ? Url::getCollegeLogo($college['logo_image']) : Url::defaultCollegeLogo() ?>" alt="logo">
                        </div>
                        <div class="sidebarTextLink">
                            <p class="cardText"><?= $college['display_name'] ?></p>
                        </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif;
if (!empty($collegeCard)):
    ?>
    <div class="pageData">
        <h2><?= Yii::t('app', 'Top')?> <?= Yii::t('app', $stream->name) ?> <?= Yii::t('app', 'Colleges')?></h2>
        <div class="customSlider custom-cardDisplay">
            <i class="spriteIcon scrollLeft over"></i>
            <?php if (count($collegeCard) > 2): ?>
                <i class="spriteIcon scrollRight"></i>
            <?php endif; ?>
            <div class="customSliderCards">
                <?php foreach ($collegeCard as $college): ?>
                    <div class="sliderCardInfo">
                            <a href="<?= Url::toCollege($college['slug']) ?>" title="<?= $college['name'] ?>">
                                <img width="273" height="207" src="<?= !empty($college['cover_image']) ? Url::getCollegeBannerImage($college['cover_image']) : Url::toDefaultCollegeBanner() ?>" loading="lazy" alt="">
                                <div class="textDiv">
                                    <img width="56" height="56" src="<?= !empty($college['logo_image']) ? Url::getCollegeLogo($college['logo_image']) : Url::defaultCollegeLogo() ?>" class="collegeLogo" loading="lazy" alt="">
                                    <p class="widgetCardHeading"><?= $college['display_name']; ?></p>
                                    <p class="subText">
                                        <span class="spriteIcon locationIcon"></span>
                                        <?= $college['city']; ?>,<?= $college['state']; ?>
                                    </p>
                                </div>
                            </a>
                    </div>
                <?php endforeach;
                if (!in_array($stream->slug, CourseHelper::$filterPageSlug) && $is_sponsored == College::SPONSORED_NO):
                    ?>
                    <div class="sliderCardInfo">
                        <div class="viewAllDiv">
                            <?php $url = in_array($stream->slug, array_keys(DataHelper::$collegeFilter301Url)) ? DataHelper::$collegeFilter301Url[$stream->slug] : $stream->slug ?>
                            <a href="<?= Url::toDomain() . $url . '-colleges' ?>"><i class="spriteIcon viewAllIcon"></i>VIEW ALL</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif; ?>