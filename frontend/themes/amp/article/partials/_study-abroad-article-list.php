<?php

use common\helpers\ArticleDataHelper;
use common\models\Article;
use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;

?>
<article class="catgegoryArticle">
    <div class="row">
        <div class="articleBanner">
            <?php if ($model->country_slug != null && $model->entity == Article::ENTITY_STUDY_ABROAD): ?>
                <figure>
                    <?php $cardImage = $model->cover_image ? Url::getStudyAbroadImage($model->cover_image) : Url::getStudyAbroadImage() ?>
                    <img class="lazyload" loading="lazy" data-src="<?= $cardImage ?>" src="<?= $cardImage ?>" alt="<?= $model->h1 ?>" />
                    <?php /*<img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toCountryDetail($model->country_slug, $model->slug) ?>')" data-src="<?= $cardImage ?>" src="<?= $cardImage ?>" alt="<?= $model->h1 ?>" />*/?>

                </figure>
            <?php else: ?>
                    <figure>
                    <?php /*<img class="lazyload" loading="lazy" onclick="gmu.url.goto('<?= Url::toCountryDetail($model->country_slug, $model->slug) ?>')" data-src="<?= Url::getStudyAbroadImage() ?>" alt="<?= $model->h1 ?>" />*/?>

                    <img class="lazyload" loading="lazy" data-src="<?= Url::getStudyAbroadImage() ?>" alt="<?= $model->h1 ?>" />
                </figure>
            <?php endif; ?>
        </div>
        <div class="articleText">
            <h2>
                <a href="<?= Url::toCountryDetail($model->country_slug, $model->slug) ?>" title="<?= $model->h1 ?>"> <?= BaseStringHelper::truncateWords($model->h1, 9) ?></a>
            </h2>

            <p><?= $model->meta_description ?></p>
            <div class="updated-info row">

                <?php if ($model->author): ?>
                    <div class="updatedBy">
                        <?php $authorImage = isset($model->author->profile->image) ? Yii::getAlias('@profileDPFrontend') . '/' . $model->author->profile->image : '/yas/images/usericon.png' ?>
                        <img class="lazyload" loading="lazy" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $model->author->name . ' Image' ?>">
                        <p class="authorName"><?= isset($model->author->name) ? $model->author->name : '' ?></p>
                    </div>
                <?php endif; ?>
                <p><?= Yii::$app->formatter->asDate($model->updated_at ?? 'today') ?></p>

            </div>
        </div>
    </div>
</article>