<?php

use common\helpers\ContentHelper;
use common\helpers\CollegeHelper;
use frontend\helpers\Url;

$reviewRatingList = explode('#~*', $model['reviewRating']);
$reviewContent = explode('#~*', $model['review']);

?>

<div class="reviewCard">
    <div class="row">
        <img class="lazyload" width="40" height="40" loading="lazy" data-src="<?= $model['profile_pic'] ?>" src="<?= $model['profile_pic'] ?>" alt="">
        <div>
            <p><?= CollegeHelper::getUserName($model['first_name'], $model['email']) ?>
                <span>| <?= $model['program_name'] ?>, batch of <?= $model['gradyear'] ?></span>
            </p>
            <p>Reviewed on <?= date('M d, Y', strtotime($model['admin_updated_on'])) ?></p>
        </div>
    </div>
    <ul class="reviewKeypoints">
        <?php if (!empty(CollegeHelper::getUserRating($reviewRatingList))): ?>
            <li><?= CollegeHelper::getUserRating($reviewRatingList) ?>
                <?= CollegeHelper::getTotalStars(CollegeHelper::getUserRating($reviewRatingList)); ?>
            </li>
        <?php endif; ?>
        <?php foreach ($reviewRatingList as $reviewRating):
            $data = explode('@***@', $reviewRating);
            if ($data[1] > 0):
                ?>
                <li><?= CollegeHelper::$reviewCategory[$data[0]] ?> 
                    <span>
                        <?= ($data[1] == 1) ? $data[1] : floor($data[1]) * 0.5 ?>
                    <i class="spriteIcon empty-star"></i>
                    </span>
                </li>
                <?php
            endif;
        endforeach;
        ?>
    </ul>
    <!-- quick fix -->
    <div>
        <?php
        $count = 1;
        foreach ($reviewContent as $content):
            $data = explode('@***@', $content);

            if ($count < 3):
                ?>
                <?php if (!empty($data[0])): ?> 
                    <?= CollegeHelper::$reviewCategory[rtrim($data[0], '@**')] ?>
                <?php endif; ?>
                <?= isset($data[1]) ? ContentHelper::removeStyleTag(stripslashes(html_entity_decode($data[1]))) : '' ?>
                <?php
            endif;
            $count++;
        endforeach;
        ?>
    </div>
    <div class="redirectPage">
        <div class="redirectreviewCard redirectionUrl">
            <a class="" href="<?= Url::toUserReviewUrl(CollegeHelper::checkAnonymous($model['reviewed_by']), $model['clgUrl']) ?>" title="Read <?= CollegeHelper::getUserName($model['first_name'], $model['email']) ?> Full review">Read More <span class="spriteIcon urlIcon"></span></a>
            <!-- <p>Was this review helpful <span class="spriteIcon likeBtn"></span> | <span class="spriteIcon unlikeBtn"></span></p> -->
        </div>
    </div>
</div>
