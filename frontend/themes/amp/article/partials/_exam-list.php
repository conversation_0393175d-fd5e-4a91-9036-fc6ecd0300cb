<?php

use frontend\helpers\Url;

if (!empty($examList)):
    ?>
    <div class="sideBarSection customLogo">
        <p class="listCard"><?= Yii::t('app', 'Top')?> <?= $stream->name ?> <?= Yii::t('app', 'Exams')?></p>
        <div class="sidebarLinks">
            <?php foreach ($examList as $exam): ?>
                <a href="<?= Url::toExamDetail($exam['slug']) ?>" title="<?= $exam['name'] ?>" class="listCard row">
                        <div class="sidebarImgDiv">
                            <img src="<?= $exam['cover_image'] ? Yii::getAlias('@getmyuniExamAsset/') . $exam['cover_image'] : 'https://media.getmyuni.com/yas/images/defaultCardBanner.png' ?>" alt="logo" loading="lazy" width="56" height="56">
                        </div>
                        <div class="sidebarTextLink">
                            <p class="cardText"><?= $exam['display_name'] ?></p>
                        </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif;
if (!empty($examCard)): ?>
    <div class="pageData">
        <h2 class="row"><?= $title ?></h2>
        <div class="customSlider two-cardDisplay">
            <i class="spriteIcon scrollLeft over"></i>
            <?php if (count($examCard) > 2): ?>
                <i class="spriteIcon scrollRight"></i>
            <?php endif; ?>

            <div class="customSliderCards">
                <?php foreach ($examCard as $exam): ?>
                    <div class="sliderCardInfo">
                        <div class="row">
                            <img width="72" height="72" loading="lazy" src="<?= !empty($exam['image']) ? Url::toExamImage($exam['image']) : Url::toDefaultImage($exam['image']) ?>" alt="" class="clgLogo">
                            <div>
                                <a href="<?= Url::toExamDetail($exam['slug']) ?>" title="<?= $exam['name'] ?>"><?= $exam['display_name'] ?? '' ?></a>
                                <?php if (!empty($exam['exam_start'])): ?>
                                    <p><?= Yii::t('app', 'Exam Date')?>: <span><?= date('M d, Y', strtotime($exam['exam_start'])) ?></span></p>
                                <?php endif; ?>
                                <?php if (!empty($exam['result_date'])): ?>
                                    <p><?= Yii::t('app', 'Result Date')?>: <span><?= date('M d, Y', strtotime($exam['result_date'])) ?></span></p>
                                <?php endif; ?>

                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
                <div class="sliderCardInfo">
                    <div class="viewAllDiv">
                        <a href="<?= url::toDisciplineExam($stream->slug) ?>" title="<?= $stream->name ?> Entrance Exams in India">
                            <i class="spriteIcon viewAllIcon"></i><?= Yii::t('app', 'View All')?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>