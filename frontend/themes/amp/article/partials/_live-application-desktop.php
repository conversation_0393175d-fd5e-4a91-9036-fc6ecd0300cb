<?php

use frontend\helpers\Url;
use common\helpers\CollegeHelper;

$isMobile = \Yii::$app->devicedetect->isMobile();

$cardCount = 0;
?>
<section class="liveApllicationForms desktopOnly">
    <h2 class="row"><?= Yii::t('app', 'LIVE APPLICATION FORMS') ?></h2>
    <div class="liveApllicationFormsInner">
        <?php if (count($liveApplicationForm) > 3): ?>
            <!--button class="spriteIcon scrollLeft over"></button>
            <button class="spriteIcon scrollRight"></button-->
        <?php endif; ?>
        <div class="row">
            <?php if ($entityName == 'courses'): ?>
                <?php foreach (array_slice($liveApplicationForm, 0, 5) as $application):
                    if (!isset($application['link']) || empty($application['link'])) {
                        continue;
                    }
                    ?>
                    <div class="applicationDiv">
                        <figure>
                            <img width="80" height="80" data-src="<?= Url::getCollegeLogo($application['image']) ?>" src="<?= Url::getCollegeLogo($application['image']) ?>" alt="logo">
                        </figure>
                        <div>
                            <p class="clgName"><?= $application['name']; ?> </p>
                            <p>
                                <?= $entityDisplayName; ?>
                            </p>
                            <p class="course-fees"><?= ($application['fees']) ? ' (Total Fees) ' . $application['fees'] : '' ?></p>

                        </div>
                        <a class="primaryBtn applicaticationFormBtn" rel="sponsored nofollow" href=" <?= $application['link'];  ?>" target="_blank"><?= Yii::t('app', 'APPLY NOW') ?> </a>
                    </div>
                <?php endforeach; ?>

            <?php else: ?>
                <?php foreach (array_slice($liveApplicationForm, 0, 5) as $application):
                    if (empty($application['redirect_url'])) {
                        continue;
                    }
                    $link = $application['redirect_url'];
                    if (!isset($link) || empty($link)) {
                        continue;
                    }
                    ?>
                    <div class="applicationDiv">
                        <figure>
                            <img width="80" height="80" src="<?= !empty($application['college_id']) && isset($application['logo_image']) && !empty($application['logo_image']) ? Url::getCollegeLogo($application['logo_image']) : Url::defaultCollegeLogo() ?>" alt="">
                        </figure>
                        <div>

                            <p class="clgName"><?= $application['college_name'] ?> </p>
                            <p>
                                <?= $application['course_name'] ?>
                            </p>
                            <p class="course-fees">
                                <?php if (!empty($application['total_fees'])) { ?>
                                    (<?= Yii::t('app', 'Total Fees') ?>) <?= Yii::t('app', 'Rs') ?>Rs. <?= CollegeHelper::feesFormat($application['total_fees']) ?>
                                <?php } ?>
                            </p>
                        </div>
                        <a class="primaryBtn applicaticationFormBtn" rel="sponsored nofollow" href="<?= $link ?>" target="_blank"><?= Yii::t('app', 'APPLY NOW') ?></a>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>