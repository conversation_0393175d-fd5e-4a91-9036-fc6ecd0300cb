<?php
use frontend\helpers\Url;
use common\helpers\CollegeHelper;
use common\models\College;

$isMobile = \Yii::$app->devicedetect->isMobile();

?>
<section class="pageData">
    <h2 class="row">
        Similar <?= $collegeByDiscipline[0]['streamName'] ?> Colleges
        <?php if (!$isMobile):
            if (isset($collegeByDiscipline[0]['streamSlug']) && $sponsorStatus == College::SPONSORED_NO):
                ?>
            <a href="<?= Url::toDisciplineCollege(CollegeHelper::$searchPageDisciplineSlug[$collegeByDiscipline[0]['streamSlug']], $college->city->slug) ?>">View All</a>
            <?php endif;
        endif;
        ?>
    </h2>
    <div class="customSlider four-cardDisplay">

        <?php if (count($collegeByDiscipline) > 4): ?>
            <i class="spriteIcon scrollLeft over"></i>
            <i class="spriteIcon scrollRight"></i>
        <?php endif; ?>

        <div class="customSliderCards">
            <?php foreach ($collegeByDiscipline as $discipline): ?>
                <div class="sliderCardInfo">
                        <a href="<?= Url::toCollege($discipline['slug']) ?>" title="<?= $discipline['name'] ?>">
                            <figure>
                                <img class="lazyload" width="275" height="207" loading="lazy" data-src="<?= !empty($discipline['cover_image']) ? Url::getCollegeBannerImage($discipline['cover_image']) : Url::toDefaultCollegeBanner() ?>" src="<?= !empty($discipline['cover_image']) ? Url::getCollegeBannerImage($discipline['cover_image']) : Url::toDefaultCollegeBanner() ?>" alt="">
                            </figure>

                            <div class="textDiv pb-0">
                                <img class="collegeLogo lazyload" width="56" height="56" loading="lazy" data-src="<?= !empty($discipline['logo_image']) ? Url::getCollegeLogo($discipline['logo_image']) : Url::defaultCollegeLogo() ?>" src="<?= !empty($discipline['logo_image']) ? Url::getCollegeLogo($discipline['logo_image']) : Url::defaultCollegeLogo() ?>" alt="">
                                <p class="widgetCardHeading"><?= $discipline['name'] ?></p>
                            </div>
                        </a>
                        <p class="subText pt-0"><span class="spriteIcon locationIcon"></span> <?= $college->city->name ?>, <?= $college->city->state->name ?>
                                </p> 
                   
                </div>
            <?php endforeach ?>

            <?php if ($isMobile): ?>
                <div class="sliderCardInfo mobileOnly">
                    <div class="viewAllDiv">
                        <?php if (isset($collegeByDiscipline[0]['streamSlug']) && !empty($collegeByDiscipline[0]['streamSlug']) && $sponsorStatus == College::SPONSORED_NO): ?>
                            <a href="<?= Url::toDisciplineCollege(CollegeHelper::$searchPageDisciplineSlug[$collegeByDiscipline[0]['streamSlug']], $college->city->slug) ?>"><i class="spriteIcon viewAllIcon"></i>VIEW ALL</a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>