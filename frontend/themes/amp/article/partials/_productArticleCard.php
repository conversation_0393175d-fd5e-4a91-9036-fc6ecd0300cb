<?php

use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;
use common\helpers\DataHelper;

$cardCount = 0;
$totalCards = 10;
$title = isset($title) && !empty($title) ? $title : 'Related Articles';
?>
 
<?php if (count($relatedArticles) > 4) { ?>
    <section class="latestInfoSection">

        <h2 class="row">
            <?= Yii::t('app', $title); ?>
        </h2>

        <div class="latestInfoListContainer four-cardDisplay">
            <?php if (count($relatedArticles) >= 4): ?>
                <i class="spriteIcon scrollLeft over"></i>
                <i class="spriteIcon scrollRight"></i>
            <?php endif; ?>
            <div class="latestInfoList row">
                <?php foreach ($relatedArticles as $relatedArticle): ?>
                    <?php
                    // breaks when totalCard count match
                    if ($cardCount == $totalCards) {
                        break;
                    }

                    $cardCount++;
                    ?>

                    <?php $post = (object) $relatedArticle; ?>
                    <?php if (isset($article) && $article->slug == $post->slug): ?>
                        <?php continue ?>
                    <?php endif; ?>
                    <div class="latestInfoDiv">
                        <a href="<?= Url::toArticleDetail($post->slug, DataHelper::getLangCode($relatedArticle['lang_code'])) ?>" title="<?= $post->title ?? '' ?>">
                            <figure>
                                <img class="lazyload" loading="lazy" width="274" height="207" data-src="<?= $post->cover_image ? ArticleDataHelper::getImage($post->cover_image) : ArticleDataHelper::getImage() ?>" src="<?= $post->cover_image ? ArticleDataHelper::getImage($post->cover_image) : ArticleDataHelper::getImage() ?>" alt="<?= $post->h1 ?>">
                            </figure>
                            <div class="latestInfoTxt">
                                <p>
                                    <?= BaseStringHelper::truncate($post->title, 95) ?? '' ?>
                                </p>
                            </div>
                        </a>
                        <div class="authorAndDate">
                            <?php
                            $authorName = '';
                            $authorSlug = '';
                            $langCode = $relatedArticle->lang_code ?? '';
                            if ($post instanceof common\models\Article && !empty($post->author)) {
                                $authorName = $post->author->name ?? '';
                                $authorSlug = $post->author->slug ?? '';
                            } else {
                                $authorName = is_object($post) ? $post->name : 'Getmyuni Content Team';
                                $authorSlug = is_object($post) && !empty($post->user_slug) ? $post->user_slug : 'getmyuni-content-team';
                            }
                            ?>
                            <a class="authorName" href="<?= ($langCode == 1 ) ? Url::toAllAuthorPost($authorSlug) :  Url::toAllAuthorPost($authorSlug, $langCode) ?>" title="<?= $authorName ?>" title="<?= $authorName ?>">
                                <?= $authorName ?>
                            </a>
                            <?php if (!empty($relatedArticle['updated_at'])): ?>
                                <p class="widgetAuthorName"><?= Yii::$app->formatter->asDate(date('Y-m-d', strtotime($relatedArticle['updated_at']))); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach;
                ?>
                <?php /*if (count($relatedArticles) > 5) : ?>
               <div class="latestInfoDiv">
                   <div class="viewAllDiv">
                       <a href=""><i class="spriteIcon viewAllIcon"></i>
                           VIEW ALL</a>
                   </div>
               </div>
           <?php endif;*/ ?>

            </div>
        </div>
    </section>
<?php } ?>