<?php

use common\helpers\CollegeHelper;

if ((isset($rating[0]) && $rating[0]['ra1count'] != 0) && ($rating[0]['ra1sum'] != 0)):
    if (CollegeHelper::getTotalRating($categoryRating) != 0):
        ?>
<div class="allReviews">
    <div class="row">
        <?php if (!empty(CollegeHelper::getTotalRating($categoryRating))): ?>
            <div class="col-md-3">
                <p class="ratingHeading">Component Rating<span> (Out of 5)</span></p>
                <?php if ($rating[0]['ra1sum'] > 0 && $rating[0]['ra1count'] > 0): ?>
                    <p class="avgRating"><?= CollegeHelper::getTotalRating($categoryRating) ?></p>
                <?php endif; ?>
                <div class="d-inlineblock">
                    <?php if ($rating[0]['ra1sum'] > 0 && $rating[0]['ra1count'] > 0): ?>
                        <p class="ratings">
                        <?= (CollegeHelper::getTotalRating($categoryRating) == 1 ) ? CollegeHelper::getTotalStars(1) : CollegeHelper::getTotalStars(CollegeHelper::getTotalRating($categoryRating)); ?>
                        </p>
                    <?php endif; ?>
                    <p class="subText"> Based on<?= $rating[0]['connectionCount'] ?> Verified Reviews</p>
                </div>
            </div>
        <?php endif; ?>         
           <?php if (!empty($distrbutionRating)): ?>
                <div class="col-md-4">
                    <p class="ratingHeading">Distribution of Rating</p>
                        <div class="row reviewRation">
                            <p>&gt;4-5 star</p>
                            <div class="bargraphContainer">
                                <div class="bargraphBody">
                                    <div class="valueIndicator" style="width: <?= isset($distrbutionRating['progress4']) ? $distrbutionRating['progress4'] : '' ?>%;"></div>
                                </div>
                            </div>
                            <p><?= $distrbutionRating['rating4'] ?></p>
                        </div>
                    <div class="row reviewRation">
                        <p>&gt;3-4 star</p>
                        <div class="bargraphContainer">
                            <div class="bargraphBody">
                                <div class="valueIndicator" style="width: <?= isset($distrbutionRating['progress3']) ? $distrbutionRating['progress3'] : '' ?>%;"></div>
                            </div>
                        </div>
                        <p><?= $distrbutionRating['rating3'] ?></p>
                    </div>

                    <div class="row reviewRation">
                        <p>&gt;2-3 star</p>
                        <div class="bargraphContainer">
                            <div class="bargraphBody">
                                <div class="valueIndicator" style="width: <?= isset($distrbutionRating['progress2']) ? $distrbutionRating['progress2'] : '' ?>%;"></div>
                            </div>
                        </div>
                        <p><?= $distrbutionRating['rating2'] ?></p>
                    </div>
                    
                    <div class="row reviewRation">
                        <p>1-2 star</p>
                        <div class="bargraphContainer">
                            <div class="bargraphBody">
                                <div class="valueIndicator" style="width: <?= isset($distrbutionRating['progress1']) ? $distrbutionRating['progress1'] : '' ?>%;"></div>
                            </div>
                        </div>
                        <p><?= $distrbutionRating['rating1'] ?></p>
                    </div>

                </div>
           <?php endif; ?>
        <?php if (!empty($categoryRating)): ?>
            <div class="col-md-5">
                <p class="ratingHeading">Component Rating<span> (Out of 5)</span></p>
                <div class="componentRatio">
                    <?php foreach ($categoryRating as $key => $val): ?>
                        <div class="row">
                            <p class="col-md-7 col-7 pl-0"><?= $key ?></p>
                            <p class="col-md-5 col-5 p-0"><span class="reviewValue m-0"><?= ($val == 0.5) ? 1 : $val ?></span>
                                <span>
                                    <?= ($val == 0.5) ? CollegeHelper::getTotalStars(1) : CollegeHelper::getTotalStars($val); ?>
                                </span>
                            </p>
                        </div>
                    <?php endforeach; ?>

                    <!-- <div class="row">
                        <p class="col-md-6 col-7">Value for Money</p>
                        <p class="col-md-6 col-5">4.5 <span><i class="spriteIcon full-star"></i><i class="spriteIcon full-star"></i><i class="spriteIcon full-star"></i><i class="spriteIcon full-star"></i><i class="spriteIcon half-star"></i></span>
                        </p>
                    </div> -->
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
        <?php
    endif;
endif;?>