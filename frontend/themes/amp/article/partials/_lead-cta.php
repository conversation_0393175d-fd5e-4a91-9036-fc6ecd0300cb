<?php

use common\services\UserService;

$isMobile = \Yii::$app->devicedetect->isMobile();
$defaultEmptyCondition0 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_0']) || empty(array_filter($dynamicCta['cta_position_0'])));
$defaultEmptyCondition1 = empty($dynamicCta) || (!isset($dynamicCta['cta_position_1']) || empty(array_filter($dynamicCta['cta_position_1'])));
?>

<?php if (isset($lead_cta) && $lead_cta == 1):
    $cta_slug1 = $defaultEmptyCondition0 ? 'Free Counseling' : (!empty($dynamicCta['cta_position_0']['cta_text']) ? $dynamicCta['cta_position_0']['cta_text'] : 'Free Counseling');
    $cta_slug2 = $defaultEmptyCondition1 ? 'Get ₹1 Lakh Scholarship' : (!empty($dynamicCta['cta_position_1']['cta_text']) ? $dynamicCta['cta_position_1']['cta_text'] : 'Get ₹1 Lakh Scholarship'); ?>
    <?= frontend\helpers\Html::leadButton(
        $cta_slug1,
        [
            'entity' => $data_entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition0 ? UserService::parseDynamicCta('articles_{slug}_detail_wap_bottom_left_sticky_cta2', '', $slug) : ($dynamicCta['cta_position_0']['wap'] ?? null)) : ($defaultEmptyCondition0 ? UserService::parseDynamicCta('articles_{slug}_detail_web_lead_capture_panel_left_cta2', '', $slug) : ($dynamicCta['cta_position_0']['web'] ?? null)),
            'ctaText' => $cta_slug1,
            'ctaTitle' => $defaultEmptyCondition0 ? '' : (!empty($dynamicCta['cta_position_0']['cta_title']) ? $dynamicCta['cta_position_0']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition0 ? 'REGISTER FOR EXPERT GUIDANCE' : (!empty($dynamicCta['cta_position_0']['lead_form_title']) ? $dynamicCta['cta_position_0']['lead_form_title'] : 'REGISTER FOR EXPERT GUIDANCE'),
            'subheadingtext' => $defaultEmptyCondition0 ? 'Register and talk to our experts.' : (!empty($dynamicCta['cta_position_0']['lead_form_description']) ? $dynamicCta['cta_position_0']['lead_form_description'] : 'Register and talk to our experts.'),
            'image' => '/yas/images/defaultcardbanner.png',
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition0 ? null : ($dynamicCta['cta_position_0']['page_link'] ?? null)) : null,
         ],
        ['class' => 'talkToExpert registerNow courseCategory articleLeadValue cta_impression CTA_click'],
        'js-open-lead-form-new'
    )
    ?>
    <?= frontend\helpers\Html::leadButton(
        $cta_slug2,
        [
            'entity' => $data_entity,
            'entityId' => $entity_id,
            'ctaLocation' => $isMobile ? ($defaultEmptyCondition1 ? UserService::parseDynamicCta('articles_{slug}_detail_wap_bottom_right_sticky_cta2', '', $slug) : ($dynamicCta['cta_position_1']['wap'] ?? null)) : ($defaultEmptyCondition1 ? UserService::parseDynamicCta('articles_{slug}_detail_web_lead_capture_panel_right_cta2', '', $slug) : ($dynamicCta['cta_position_1']['web'] ?? null)),
            'ctaText' => $defaultEmptyCondition1 ? 'Get 1 Lakh Scholarship' : (!empty($dynamicCta['cta_position_1']['cta_text']) ? $dynamicCta['cta_position_1']['cta_text'] : 'Get 1 Lakh Scholarship'),
            'ctaTitle' => $defaultEmptyCondition1 ? '' : (!empty($dynamicCta['cta_position_1']['cta_title']) ? $dynamicCta['cta_position_1']['cta_title'] : null),
            'leadformtitle' => $defaultEmptyCondition1 ? 'APPLY NOW' : (!empty($dynamicCta['cta_position_1']['lead_form_title']) ? $dynamicCta['cta_position_1']['lead_form_title'] : 'APPLY NOW'),
            'subheadingtext' => $defaultEmptyCondition1 ? 'Fill in your details' : (!empty($dynamicCta['cta_position_1']['lead_form_description']) ? $dynamicCta['cta_position_1']['lead_form_description'] : 'Fill in your details'),
            'image' => '/yas/images/defaultcardbanner.png',
            'redirection' => empty($sponsorClientUrl) ? ($defaultEmptyCondition1 ? null : ($dynamicCta['cta_position_1']['page_link'] ?? null)) : null,
         ],
        ['class' => 'articleScholarship applyNow registerNow courseCategory articleLeadValue cta_impression CTA_click'],
        'js-open-lead-form-new'
    )
    ?>
<?php endif;
