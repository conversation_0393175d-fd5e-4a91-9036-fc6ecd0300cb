<?php

use common\helpers\DataHelper;
use common\models\Degree;
use common\models\Stream;
use common\services\UserService;
use frontend\helpers\Url;
use frontend\models\LeadForm;
use yii\helpers\ArrayHelper;

$student = \Yii::$app->user->identity;

$formTitleAutoPopup = 'Signup to continue';
if (!empty($this->params['dynamicCta']) && !empty($this->params['dynamicCta']['cta_position_4'])) {
    $formTitleAutoPopup = $this->params['dynamicCta']['cta_position_4']['lead_form_title'];
}

$streamLevel = [];
if (!empty($this->params['entity_id'])) {
    $streamLevel = UserService::getStreamLevelForAmp($this->params['entity_id']) ?? [];
}

$category = 'article-amp';
$category_sub_page = 'article-detail';

$currentUrl = isset($this->params['canonicalUrl']) ? $this->params['canonicalUrl'] : Url::base(true) . Yii::$app->request->getUrl();
$model = new LeadForm();
$cities = $model->city;
$cities = ArrayHelper::map($cities, 'id', 'name', 'state_slug');
$userCity = DataHelper::getUserLocation();
$isMobile = \Yii::$app->devicedetect->isMobile();
$emailValue = !empty($student->email) ? $student->email : '';
$styleNoneStream = !empty($streamLevel) && !empty($streamLevel['stream_id']) ? 'none' : 'block';
$styleNoneLevel = !empty($streamLevel) && !empty($streamLevel['highest_qualification']) ? 'none' : 'block';
?>
<amp-script src="<?= Url::toDomain() ?>yas/js/version2/amp_script.js" width="1" height="1" sandbox="allow-forms">
    <div class="pageMask" style="display:block"></div>
    <div class="leadFormContainerNews">
        <div class="closeLeadFormContainer">
            <button id="closeButton" on="tap:<?= $id == 1 ? 'amp-popup.dismiss' : $formId . '.close' ?>" class="webpSpriteIcon closeLeadForm" tabindex="0"></button>
            <!-- span id="close-atc-error" class="absolute center z4 " on="tap:atc-error-modal.close" role="button" tabindex="0">X</span> -->
        </div>
        <div class="leadFormDiv">
            <?php $url = Url::base(true) . '/site/amp-lead';
            $finalUrl = str_replace(['http:', 'https:'], '', $url); ?>
            <form class="sample-form" method="POST" action-xhr=<?= $finalUrl ?> target="_top" custom-validation-reporting="show-all-on-submit" on="submit-success:AMP.navigateTo(url='<?= Url::base(true) . Yii::$app->request->getUrl() ?>')">
                <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
                <input type="hidden" name="entity" value=<?= $this->params['entity'] ?? 'amp-news' ?>>
                <input type="hidden" name="entity_id" value=<?= $this->params['entity_id'] ?? 0 ?>>
                <input type="hidden" name="url" value=<?= $this->params['canonicalUrl'] ?>>
                <input type="hidden" name="platform" value=<?= $isMobile ?>>
                <input type="hidden" name="cta_location" value="<?= $id == 1 ? 'auto_popup' : $ctaLocation ?>">
                <input type="hidden" name="cta_text" value="<?= $id == 1 ? 'Auto Pop Up' : strip_tags($ctaText) ?>">
                <input type="hidden" name="category" value=<?= $category ?>>
                <input type="hidden" name="category_sub_page" value=<?= $category_sub_page ?>>
                <input type="hidden" name="id" id="id" value=<?= $id ?>>
                <input type="hidden" id="selectCityNews" name="current_city_ip" value="<?= !empty($userCity) ? $userCity['cityId'] : '' ?>">
                <input type="hidden" id="current_city_ip_amp" name="current_state_ip" value="<?= !empty($userCity) ? $userCity['stateId'] : '' ?>">
                <div class="userInputs">
                    <div class="formHeadingDiv row">
                        <div class="formImg">
                            <span class="getmyuniLogoIcon webpSpriteIcon"></span>
                        </div>
                        <div class="formHeading">
                            <p class="headingText"><?= $id == 1 ? $formTitleAutoPopup : $formTitle ?></p>
                            <p class="subHeadingText">Get latest updates and access to all
                                premium content</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group userName">
                                <i class="spriteIcon userIcon"></i>
                                <input type="text" id="show-all-on-submit-name" placeholder="Enter Name" class="ampName" name="name" value="<?= empty($student->name) ? '' : $student->name ?>" autocomplete="off" maxlength="50" required>
                                <span visible-when-invalid="valueMissing" validation-for="show-all-on-submit-name">name cannot be blank</span>
                                <span visible-when-invalid="patternMismatch" validation-for="show-all-on-submit-name">name should contain only alphabets</span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mobileNumber">
                                <div class="mobileNumber row">
                                    <div class="countryCode">
                                        <div class="dialCodeDiv">
                                            <i class="spriteIcon flagIcon"></i>
                                            <span class="dialCode">+91</span>
                                        </div>
                                    </div>
                                    <div class="numberInput">
                                        <input type="text" id="show-all-on-submit-mobile" maxlength="10" class="ampMobile" placeholder="Mobile Number" required name="phone" value="<?= empty($student->phone) ? '' : $student->phone ?>" autocomplete="off">
                                    </div>
                                </div>
                                <span visible-when-invalid="valueMissing" validation-for="show-all-on-submit-mobile">mobile cannot be blank</span>
                                <span visible-when-invalid="patternMismatch" validation-for="show-all-on-submit-mobile">Invalid Mobile Number</span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group userMail">
                                <i class="spriteIcon mailIcon"></i>
                                <input type="text" id="show-all-on-submit-email" placeholder="Email Address" class="ampEmail" name="email" required value="<?= empty($student->email) ? '' : $student->email ?>" maxlength="50" autocomplete="off">
                                <!-- <span class="domainExtention">@gmail.com</span> -->
                                <span visible-when-invalid="valueMissing" validation-for="show-all-on-submit-email">email cannot be blank</span>
                                <!-- <span visible-when-invalid="patternMismatch" validation-for="show-all-on-submit-email">Please enter a valid email address</span> -->
                                <span id="<?= $id == 1 ? 'email_error_div_auto' : 'email_error_div' ?>"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group selectCity">
                                <i class="spriteIcon locationIcon"></i>
                                <select name="current_city" required="" class="user-invalid valueMissing select2 formGroup ampCity" id="show-all-on-submit-city" aria-invalid="false">
                                    <option value=" " disabled selected>Select Your Current City</option>
                                    <?php
                                    foreach ($cities as $key => $value) {
                                        echo "<optgroup label='$key'>";
                                        foreach ($value as $k => $v) {
                                            echo "<option value='$k'>$v</option>";
                                        }
                                        echo '</optgroup>';
                                    }
                                    ?>
                                </select>
                                <span visible-when-invalid="valueMissing" validation-for="show-all-on-submit-city">City cannot be blank</span>
                            </div>
                        </div>

                        <div class="col-md-6" style="min-height: 50px;height: 50px;display: <?= $styleNoneStream ?>">
                            <div class="form-group streamCategory">
                                <i class="spriteIcon bookIcon"></i>
                                <select name="interested_stream" required="" class="user-invalid valueMissing select2 formGroup ampStream" id="show-all-on-submit-course <?= !empty($streamLevel) && !empty($streamLevel['stream_id']) ? $streamLevel['stream_id'] : '' ?>" aria-invalid="false">
                                    <option value=" ">Select Interested Stream</option>
                                    <?php
                                    foreach (ArrayHelper::map(Stream::find()->where(['status' => Stream::STATUS_ACTIVE])->all(), 'id', 'name') as $key => $value) {
                                        if (!empty($streamLevel) && !empty($streamLevel['stream_id']) && $key == $streamLevel['stream_id']) {
                                            echo "<option value='$key' selected>$value</option>";
                                        } else {
                                            echo "<option value='$key'>$value</option>";
                                        }
                                    }
                                    ?>
                                </select>
                                <span visible-when-invalid="valueMissing" validation-for="show-all-on-submit-course">Stream cannot be blank</span>
                            </div>
                        </div>

                        <div class="col-md-6" style="min-height: 50px;height: 50px;display: <?= $styleNoneLevel ?>">
                            <div class="form-group levelCategory">
                                <i class="spriteIcon bookIcon"></i>
                                <select name="interested_level" required="" class="user-invalid valueMissing select2 formGroup ampLevel" id="show-all-on-submit-course <?= !empty($streamLevel) && !empty($streamLevel['highest_qualification']) ? $streamLevel['highest_qualification'] : '' ?>" aria-invalid="false">
                                    <option value=" ">Select Level</option>
                                    <?php
                                    foreach (ArrayHelper::map(Degree::find()->where(['status' => Degree::STATUS_ACTIVE])->all(), 'id', 'name') as $key => $value) {
                                        if (!empty($streamLevel) && !empty($streamLevel['highest_qualification']) && $key == $streamLevel['highest_qualification']) {
                                            echo "<option value='$key' selected>$value</option>";
                                        } else {
                                            echo "<option value='$key'>$value</option>";
                                        }
                                    }
                                    ?>
                                </select>
                                <span visible-when-invalid="valueMissing" validation-for="show-all-on-submit-course">Level cannot be blank</span>
                            </div>
                        </div>
                    </div>
                    <div class="row m-0">
                        <div class="checkbox-group col-md-10 pr-0 pl-0">
                            <input type="checkbox" id="distanceEducation" name="distanceEducation" value="distanceEducation" checked="checked">

                            <label for="distanceEducation">By clicking, I agree GetMyUni Terms and Conditions.
                            </label>
                        </div>
                        <div class="formSumbitBtn col-md-2 p-0">
                            <button id="leadpopupsubmit" disabled class="primaryBtn" type="submit" tabindex="0"
                                on="tap:amp-popup.dismiss">
                                Submit
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</amp-script>