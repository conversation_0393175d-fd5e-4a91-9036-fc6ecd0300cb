<?php

use frontend\helpers\Url;
use yii\helpers\Html;
use frontend\helpers\Schema;
use yii\widgets\Breadcrumbs;

// $userCity = DataHelper::getUserLocation();
$isMobile = \Yii::$app->devicedetect->isMobile();
$currentUrl = isset($this->params['canonicalUrl']) ? $this->params['canonicalUrl'] : Url::base(true) . Yii::$app->request->getUrl();

$this->registerMetaTag(['name' => 'description', 'content' => $this->context->description]);
$this->registerMetaTag(['property' => 'og:type', 'content' => $this->context->ogType ?? 'website']);
$this->registerMetaTag(['property' => 'og:title', 'content' => $this->title]);
$this->registerMetaTag(['property' => 'og:url', 'content' => $currentUrl]);
$this->registerMetaTag(['property' => 'og:site_name', 'content' => 'Getmyuni']);
$this->registerMetaTag(['property' => 'og:description', 'content' => $this->context->description]);
$this->registerMetaTag(['property' => 'twitter:card', 'content' => 'summary_large_image']);
$this->registerMetaTag(['property' => 'twitter:site', 'content' => 'Getmyuni']);
$this->registerMetaTag(['property' => 'twitter:creator', 'content' => '@getmyuniedu']);
$this->registerMetaTag(['property' => 'twitter:url', 'content' => $currentUrl]);
$this->registerMetaTag(['property' => 'twitter:title', 'content' => $this->title]);
$this->registerMetaTag(['property' => 'twitter:description', 'content' => $this->context->description]);
if (Yii::$app->controller->id == 'news') {
    $this->registerLinkTag(['rel' => 'canonical', 'href' => $currentUrl]);
} else {
    $this->registerLinkTag(['rel' => 'canonical', 'href' => str_replace('/amp/articles', '/articles', $currentUrl)]);
}


if (!empty($this->context->ogImage)) {
    $this->registerMetaTag(['property' => 'og:image', 'content' => $this->context->ogImage]);
    $this->registerMetaTag(['property' => 'twitter:image', 'content' => $this->context->ogImage]);
}

//remove later
$defaultEmptyCondition1 = empty($this->params['dynamicCta']) || empty($this->params['dynamicCta']['cta_position_0']) || empty(array_filter($this->params['dynamicCta']['cta_position_0']));
$defaultEmptyCondition2 = empty($this->params['dynamicCta']) || empty($this->params['dynamicCta']['cta_position_1']) || empty(array_filter($this->params['dynamicCta']['cta_position_1']));
$emailValue = !empty($student->email) ? $student->email : '';
?>

<?php $this->beginPage() ?>
<!DOCTYPE html>
<html ⚡ lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,maximum-scale=1,initial-scale=1" />
    <meta name="theme-color" content="#545ebd">
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    <link rel="preconnect" href="https://www.googletagmanager.com/" crossorigin>
    <link rel="shortcut icon" type="image/png" href="<?= Url::toDomain() ?>favicon.png" />
    <link rel="icon" href="<?= Url::toDomain() ?>favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto">
    <?php $this->registerCsrfMetaTags() ?>
    <title><?= Html::encode($this->title) ?></title>
    <?php $this->head() ?>

    <style amp-custom>
        <?= $this->context->ampCss ?>.topSearcheSection {
            /*  background: #0966c2;*/
            color: var(--color-white);
            padding: 10px;
        }

        .ampEmail {
            position: relative;
            display: inline-block;
        }

        .domainExtention {
            position: absolute;
            right: 10px;
            top: 35%;
            transform: translateY(-50%);
            font-size: 14px;
            color: #989898;
        }

        .webpSpriteIcon {
            display: inline-block;
            background: url("/yas/images/master_sprite.webp");
            text-align: left;
            overflow: hidden;
        }

        select,
        select option {
            color: #000000;
        }

        select:invalid,
        select option[value=""] {
            color: #989898;
        }

        .flagIcon {
            background-position: -329px -167px;
            width: 27px;
            height: 18px;
            margin-right: 5px;
            vertical-align: sub;
        }

        .scrollToTop {
            position: fixed;
            right: 20px;
            bottom: 100px;
            z-index: 5;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50px;
            background-image: url("../../yas/images/master_sprite.webp");
            background-position: 448px -395px;
            box-shadow: 0 4px 4px rgb(0 0 0 / 25%);
            border: none;
            padding: 0;
        }

        .headerCTAPair {
            display: flex;
            padding: 10px;
            border: solid 1px #d8d8d8;
            background-color: #fff;
            margin-bottom: 10px;
            position: sticky;
            top: 0px;
            z-index: 35;
        }

        .headerCTAPair button {
            flex: 1;
            border-radius: 3px;
            height: 34px;
            border: none;
            font-size: 13px;
            font-weight: bold;
            line-height: 1.71;
        }

        .headerCTAPair button:first-child {
            border: solid 1px #0966c2;
            background-color: #0966c2;
            margin-right: 5px;
            color: #fff;
        }

        .headerCTAPair button:last-child {
            background-color: #ff4e53;
            color: #fff;
        }

        #leadpopup a,
        #leadpopuptwo a {
            color: #fff;
        }

        .whiteDownloadIcon {
            margin-right: 5px;
            width: 19px;
            height: 18px;
            background-position: 233px -353px;
            vertical-align: text-bottom;
            margin-left: 0px;
        }

        .articleInfo .readMoreNewsDetail {
            color: #e03e2d;
            font-weight: bold;
        }

        .container .newsDetailsBreadCrumb {
            background-color: initial;
        }

        .leadFormDiv select {
            height: 40px;
            padding-left: 0;
            text-indent: 40px;
        }

        amp-script {
            opacity: 1;
        }

        button.primaryBtn[disabled] {
            border: 1px solid #e9acac;
            background: #e9acac;
        }

        .leadFormDiv select {
            color: #989898;
        }

        body #amp-popup.amp-active {
            background: rgba(31, 30, 30, 0.84);
            border-color: #242323b3;
            z-index: 10000;
            height: -webkit-fill-available;
            height: -moz-fill-available;
            visibility: hidden;
            animation: 0s linear 10s forwards amp-pop-up-delay;
            /* display: none; */
        }

        @keyframes amp-pop-up-delay {
            to {
                visibility: visible
            }
        }

        #leadpopup,
        #leadpopupthree {
            background-color: #0966c2;
        }

        #leadpopuptwo,
        #leadpopupfour {
            background: #ff4e53;
        }

        #email_error_div,
        #email_error_div_auto {
            font-size: 12px;
            color: red;
        }

        .topSearchDiv ul li a {
            white-space: nowrap
        }

        .getSupport.topCta {
            position: relative;
            margin-top: 16px;
            width: 100%;
            border-radius: 0;
            gap: 8px;
            padding: 0;
            z-index: 4;
            display: flex;
            box-shadow: none;
            border: none;
        }

        .getSupport.bottomCta {
            position: fixed;
            bottom: 0;
            left: 0;
            display: flex;
        }

        .getSupport {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            margin: 0;
            padding: 10px;
            border-radius: 0;
            z-index: 5;
            gap: 8px;
            font-size: 15px;
            font-weight: 400;
            line-height: 22px;
            color: #282828;
            align-items: center;
            justify-content: center;
            box-shadow: none;
            border: var(--border-line);
            background-color: var(--color-white);
            opacity: 1;
            transition: opacity 0.3s ease-in-out;
        }

        .getSupport button {
            width: 49%;
            border-radius: 2px;
            font-size: 13px;
            padding: 6px 4px;
            font-weight: 600;
            width: 149px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-grow: 1;
            border: none;
            outline: none;
            color: #fff;
            height: 35px;
        }
    </style>

    <script async custom-element="amp-ad" src="https://cdn.ampproject.org/v0/amp-ad-0.1.js"></script>
    <script async custom-element="amp-sidebar" src="https://cdn.ampproject.org/v0/amp-sidebar-0.1.js"></script>
    <script async custom-element="amp-bind" src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"></script>
    <script async custom-element="amp-lightbox" src="https://cdn.ampproject.org/v0/amp-lightbox-0.1.js"></script>
    <script async custom-element="amp-selector" src="https://cdn.ampproject.org/v0/amp-selector-0.1.js"></script>
    <script async custom-element="amp-accordion" src="https://cdn.ampproject.org/v0/amp-accordion-0.1.js"></script>
    <script async custom-element="amp-form" src="https://cdn.ampproject.org/v0/amp-form-0.1.js"></script>
    <script async custom-template="amp-mustache" src="https://cdn.ampproject.org/v0/amp-mustache-0.2.js"></script>
    <script async custom-element="amp-anim" src="https://cdn.ampproject.org/v0/amp-anim-0.1.js"></script>
    <script async custom-element="amp-analytics" src="https://cdn.ampproject.org/v0/amp-analytics-0.1.js"></script>
    <script async custom-element="amp-twitter" src="https://cdn.ampproject.org/v0/amp-twitter-0.1.js"></script>
    <script async custom-element="amp-position-observer" src="https://cdn.ampproject.org/v0/amp-position-observer-0.1.js"></script>
    <script async custom-element="amp-animation" src="https://cdn.ampproject.org/v0/amp-animation-0.1.js"></script>
    <script async custom-element="amp-script" src="https://cdn.ampproject.org/v0/amp-script-0.1.js"></script>
    <script async custom-element="amp-user-notification" src="https://cdn.ampproject.org/v0/amp-user-notification-0.1.js"></script>
    <script async custom-element="amp-iframe" src="https://cdn.ampproject.org/v0/amp-iframe-0.1.js"></script>

    <style amp-boilerplate>
        body {
            -webkit-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            -moz-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            -ms-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            animation: -amp-start 8s steps(1, end) 0s 1 normal both
        }

        @-webkit-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @-moz-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @-ms-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @-o-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }
    </style>
    <noscript>
        <style amp-boilerplate>
            body {
                -webkit-animation: none;
                -moz-animation: none;
                -ms-animation: none;
                animation: none
            }
        </style>
    </noscript>

    <script async src="https://cdn.ampproject.org/v0.js"></script>
    <?php if (isset($this->params['schema'])): ?>
        <?= \yii\helpers\Html::script($this->params['schema'], ['type' => 'application/ld+json']) ?>
    <?php endif; ?>
    <?php if (isset($this->params['schema1'])): ?>
        <?= \yii\helpers\Html::script($this->params['schema1'], ['type' => 'application/ld+json']) ?>
    <?php endif; ?>
</head>

<body>
    <?php $this->beginBody() ?>
    <amp-animation id="showAnim" layout="nodisplay">
        <script type="application/json">
            {
                "duration": "200ms",
                "fill": "both",
                "iterations": "1",
                "direction": "alternate",
                "animations": [{
                    "selector": "#scrollToTopButton",
                    "keyframes": [{
                        "opacity": "1",
                        "visibility": "visible"
                    }]
                }]
            }
        </script>
    </amp-animation>
    <amp-animation id="hideAnim" layout="nodisplay">
        <script type="application/json">
            {
                "duration": "200ms",
                "fill": "both",
                "iterations": "1",
                "direction": "alternate",
                "animations": [{
                    "selector": "#scrollToTopButton",
                    "keyframes": [{
                        "opacity": "0",
                        "visibility": "hidden"
                    }]
                }]
            }
        </script>
    </amp-animation>
    <header class="page-header" id="top">
        <amp-position-observer on="enter:hideAnim.start; exit:showAnim.start" layout="nodisplay">
        </amp-position-observer>
        <div class="topHeader">
            <div class="container">
                <div class="row">
                    <div class="">
                        <?php
                        if (isset($this->params['entity'])  && isset($this->params['entity_sub_type']) && $this->params['entity'] != 'articles' && $this->params['entity_sub_type'] != 'detail') {
                            ?>
                            <button on="tap:sidebar1" class="spriteIcon hambergerIcon"></button>
                        <?php } ?>
                    </div>
                    <a href="#" class="spriteIcon headerLogo"></a>

                    <a on="tap:AMP.setState({showLightbox: true})" class="spriteIcon searchIcon"></a>

                </div>
            </div>
        </div>
        <amp-sidebar id="sidebar1" layout="nodisplay" style="width:300px">
            <amp-nested-menu layout="fill" class="slider-menu-option">
                <ul>
                    <li>
                        <a href="<?= Url::toDomain() ?>news/latest" title="Latest Education News">Latest Education
                            News</a>
                    </li>
                    <li><a href="<?= Url::toDomain() ?>news/featured" title="Featured">Featured</a></li>
                    <li>
                        <a href="<?= Url::toDomain() ?>news/entrance-exams" title="Entrance Exams">Entrance Exams</a>
                    </li>
                    <li><a href="<?= Url::toDomain() ?>news/board-exams" title="Board Exams">Board Exams</a></li>
                    <li>
                        <a href="<?= Url::toDomain() ?>news/competitive-exams" title="Competitive Exams">Competitive
                            Exams</a>
                    </li>
                    <li><a href="<?= Url::toDomain() ?>news/colleges" title="Colleges">Colleges</a></li>
                </ul>
            </amp-nested-menu>
        </amp-sidebar>

        <amp-lightbox id="my-bindable-lightbox" [open]="showLightbox" layout="nodisplay" on="lightboxClose:AMP.setState({showLightbox: false})">
            <div class="lightbox">
                <div class="advanceSearch">

                    <div class="container">
                        <div class="searchSection">
                            <div class="row search_heading"><span>Advanced Search</span>
                                <span role="button" tabindex="0" on="tap:my-bindable-lightbox.close" class="spriteIcon cancelIcon"></span>
                            </div>
                            <amp-selector class="tabs-with-flex" role="tablist" keyboard-select-mode="focus">
                                <div id="tab1" role="tab" aria-controls="tabpanel1" option selected>Colleges</div>
                                <div id="tabpanel1" role="tabpanel" aria-labelledby="tab1">
                                    <input type="text" id="coll-name-box" placeholder=" Enter College Name" autofocus="" spellcheck="false" autocomplete="off">
                                </div>
                                <div id="tab2" role="tab" aria-controls="tabpanel2" option>Exams</div>
                                <div id="tabpanel2" role="tabpanel" aria-labelledby="tab2">
                                    <input type="text" id="exam-name" placeholder="Enter Exam Name eg: JEE,CAT,XAT">

                                </div>
                                <div id="tab3" role="tab" aria-controls="tabpanel3" option>Courses</div>
                                <div id="tabpanel3" role="tabpanel" aria-labelledby="tab3">
                                    <input type="text" id="course-name" placeholder="Enter Course Name">

                                </div>
                            </amp-selector>
                        </div>

                    </div>
                </div>
            </div>
        </amp-lightbox>
    </header>
    <amp-state id="popupState">
        <script type="application/json">
            {
                "hidePopup": false
            }
        </script>
    </amp-state>
    <amp-user-notification id="amp-popup" layout="nodisplay" data-persist-dismissal="true" [hidden]="hidePopup">
        <?= $this->render('/layouts/_lead_form', [
            'id' => 1,
            'cta_location' => 'auto-pop-up'
        ]); ?>
    </amp-user-notification>

    <?php if (Yii::$app->controller->id == 'news'): ?>
        <?php if (isset($this->params['topsearchNews']) && !empty($this->params['topsearchNews'])): ?>
            <?= $this->render('/layouts/_topserach', ['topsearchNews' => $this->params['topsearchNews']]); ?>
        <?php endif;
    endif; ?>

    <div class="">
        <!-- do not delete this -->
    </div>

    <div class="container">
        <?= $content ?>
        <?php if (isset($this->params['breadcrumbs']) && !empty($this->params['breadcrumbs'])): ?>
            <?php echo Schema::breadcrumb($this->params['breadcrumbs']) ?>
            <nav class="breadcrumbDiv newsDetailsBreadCrumb">
                <div class="container">
                    <?= Breadcrumbs::widget([
                        'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                        'homeLink' => false
                    ]) ?>
                </div>
            </nav>
        <?php endif; ?>
    </div>
    <footer class="pageFooter">
        <div class="footerPrimarySection">
            <div class="container">
                <div class="row">
                    <a href="#" class="spriteIcon headerLogo"></a>

                    <div>
                        <ul class="socialMedia">
                            <li>Connect with us</li>
                            <li><a href="https://www.facebook.com/getmyuniedu" title="Facebook" rel="noopener nofollow" target="_blank" class="spriteIcon fbIcon"></a>
                            </li>
                            <li><a href="https://twitter.com/getmyuniedu" title="Twitter" rel="noopener nofollow" target="_blank" class="spriteIcon twitterIcon"></a></li>
                            <li><a href="https://www.instagram.com/getmyuni/" title="Instagram" rel="noopener nofollow" target="_blank" class="spriteIcon instaIcon"></a>
                            </li>
                            <li><a href="https://www.linkedin.com/company/getmyuni" title="Linkedin" rel="noopener nofollow" target="_blank" class="spriteIcon linkdIn"></a>
                            </li>
                            <li><a href="https://www.youtube.com/channel/UCvczFiMv9OZwYkFydoNdMCA" title="Youtube" rel="noopener nofollow" target="_blank" class="spriteIcon youtubeIcon"></a>
                            </li>
                        </ul>
                        <ul class="contactInfo">
                            <li><span class="spriteIcon phoneIcon"></span><a href="tel:+91 7969542200" title="+91 7969542200">+91 7969542200</a>
                            </li>
                            <li><span class="spriteIcon whiteMailIcon"></span> <a href="mailTo:<EMAIL>" title="<EMAIL>"><EMAIL></a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="footerSecondSection">
            <div class="container">
                <div class="row">
                    <ul>
                        <li><a href="<?= Url::toDomain() ?>about-us" title="About Us">About Us</a>
                        </li>
                        <li><a href="<?= Url::toDomain() ?>contact-us" title="Contact Us">Contact
                                Us</a></li>
                        <li><a href="<?= Url::toDomain() ?>privacy-policy" title="Privacy Policy">Privacy
                                Policy</a></li>
                        <li><a href="<?= Url::toDomain() ?>terms-and-conditions" title="Terms &amp; Conditions">Terms
                                &amp; Conditions</a></li>
                    </ul>
                    <p class="copyrightsText">
                        © 2021 Getmyuni.com. All Rights Reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>
    <button id="scrollToTopButton" on="tap:top.scrollTo(duration=200)" class="scrollToTop"></button>
    </amp-analytics>
    <?php $this->endBody() ?>
    <amp-analytics config="https://www.googletagmanager.com/amp.json?id=GTM-PGK7WHT&gtm.url=SOURCE_URL" data-credentials="include"></amp-analytics>
    <amp-analytics type="googleanalytics" config="https://amp.analytics-debugger.com/ga4.json" data-credentials="include">
        <script type="application/json">
            {
                "vars": {
                    "GA4_MEASUREMENT_ID": "G-LXCLLX7LL6",
                    "GA4_ENDPOINT_HOSTNAME": "www.google-analytics.com",
                    "DEFAULT_PAGEVIEW_ENABLED": true,
                    "GOOGLE_CONSENT_ENABLED": false,
                    "WEBVITALS_TRACKING": true,
                    "PERFORMANCE_TIMING_TRACKING": false,
                    "SEND_DOUBLECLICK_BEACON": false
                }
            }
        </script>

    </amp-analytics>
</body>

</html>
<?php $this->endPage() ?>