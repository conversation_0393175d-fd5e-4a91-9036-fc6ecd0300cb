<?php


namespace frontend\services;

use Yii;
use yii\db\Query;

class RankPredictorService
{
    public function predictRank($examId, $marks)
    {
        $connect = new Query();
        $result = $connect->select('*')
            ->from('rank_predictor')
            ->where(['exam_id' => $examId])
            ->orderBy(new \yii\db\Expression('ABS(marks - ' . $marks . '), rankFrom'))
            ->limit(1)
            ->one();
        if ($result) {
            $data = ($result['rankTo'] != '') ? sprintf('%d to %d', $result['rankFrom'], $result['rankTo']) : $result['rankFrom'];
            return $data ?? '';
        }
        return 'Oops something went wrong!';
    }
}
