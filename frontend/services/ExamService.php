<?php


namespace frontend\services;

use yii\db\Query;
use common\helpers\DataHelper;
use common\models\GmuMetaCategory;
use common\models\LiveApplication;
use common\models\LiveApplicationCombination;
use common\models\old\GmuExamPaper;
use common\models\SeoInfo;
use common\models\ManageNavMenuOrder;
use common\models\Exam;
use common\services\ExamService as CommonExamService;
use Yii;
use common\models\ExamContent;
use common\models\InstituteProgramRankMapping;
use common\models\MediaDrive;
use common\models\UserTranslation;

class ExamService extends CommonExamService
{
    public function getLiveApplicationFormData($exam)
    {
        if (!$examCourses = $exam->courses) {
            return null;
        }

        $data = LiveApplicationCombination::find()
            ->select([
                'live_application.college_id AS college_id',
                'college.logo_image AS logo_image',
                'college.name AS college_name',
                'college.slug AS college_slug',
                'live_application.course_name AS course_name',
                'live_application.total_fees AS total_fees',
                'live_application.redirect_url AS redirect_url'
            ])
            ->innerJoinWith('liveApplication.college')
            ->where(['in', 'live_application_combination.course_id', array_column($examCourses, 'id')])
            ->andWhere(['live_application_combination.status' => LiveApplicationCombination::STATUS_ACTIVE])
            ->andWhere(['live_application.status' => LiveApplication::STATUS_ACTIVE])
            ->distinct(true)
            ->asArray()
            ->all();

        return $data ?? [];
    }

    public function getMappedCourses(array $courses)
    {
        if (empty($courses)) {
            return [];
        }

        $query = new Query();
        $query->select(['discipline', 'vanityurl'])
            ->from('gmu_course_type_mapping')
            ->where(['in', 'course_type_short', $courses]);

        // dd($query->createCommand()->getRawSql());
        return $query->all(\Yii::$app->gmudb);
    }

    public function usefulLinks($exam, string $page, $limit = 2)
    {
        if (empty($exam)) {
            return [];
        }

        $links = SeoInfo::find()->where([
            'entity' => 'exam',
            'entity_id' => $exam['id'],
        ])->andWhere(['not', ['page' => $page]])
            ->limit($limit)->all();

        $data = [];
        foreach ($links as $link) {
            if (isset(DataHelper::examContentList()[$link->page])) {
                $data[] = [
                    'title' => $exam->display_name . ' ' . DataHelper::examContentList()[$link->page],
                    'slug' => $link->page,
                    'description' => $link->h1
                ];
            }
        }
        // print_r(Yii::$app->request->bodyParams);

        return $data;
    }

    /**
     * Get page Content and Meta data
     */
    public function getFilterPageInfo($slug = '')
    {
        $lang_code = DataHelper::getLangId();
        return GmuMetaCategory::find()
            ->select(['h1', 'title', 'description', 'top_content', 'lang_code'])
            ->where(['slug' => $slug])
            ->andWhere(['status' => GmuMetaCategory::STATUS_ACTIVE])
            ->andWhere(['lang_code' => $lang_code])
            ->one();
    }

    public function getPagePdfs($examId, $page)
    {
        $dataHelper = DataHelper::examPdfPages();

        if (!isset($dataHelper[$page])) {
            return [];
        }
        return GmuExamPaper::find()
            ->where(['exam_id' => $examId])
            ->andWhere(['paper_category' => $dataHelper[$page]])
            ->all();
    }

    /**
     * Get exam Sub Pages
     *
     * @return object
     */
    public function getSubPageDropdown($exam, $page)
    {
        $dropDownArr = [];

        $examContentSpecificPage = ExamContent::find()
            ->select(['exam_content.id', 'exam_content.parent_id', 'parent.slug AS parent_slug'])
            ->leftJoin('exam_content AS parent', 'exam_content.parent_id = parent.id')
            ->andWhere(['exam_content.slug' => $page])
            ->asArray()
            ->one();

        $examContentData = ExamContent::find()
            ->select(['parent.id AS parent_id', 'parent.name AS parent_name', 'child.id AS child_id', 'child.name AS child_name', 'child.slug AS child_slug'])
            ->from(['parent' => ExamContent::tableName()])
            ->leftJoin(['child' => ExamContent::tableName()], 'child.parent_id = parent.id')
            ->where(['parent.exam_id' => $exam->id, 'parent.parent_id' => null, 'parent.status' => 1])
            ->andWhere(['child.status' => 1])
            ->orderBy(['child.name' => SORT_DESC])
            ->asArray()
            ->all();

        foreach ($examContentData as $data) {
            if (!empty($data['child_id'])) {
                $dropDownArr[$data['parent_name']][] = [
                    'id' => $data['child_id'],
                    'name' => $data['child_name'],
                    'slug' => $data['child_slug'],
                ];
            }
        }

        return [$dropDownArr, $examContentSpecificPage->parent_slug ?? null];
    }

    /**
     * Check exam for language
     *
     * @return object
     */
    public static function checkExamLanguage($slug, $lang)
    {
        $checkExam = Exam::find()
            ->select(['id'])
            ->where(['slug' => $slug])
            ->andWhere(['lang_code' => Datahelper::$languageCode[$lang]])
            ->andWhere(['status' => Exam::STATUS_ACTIVE])
            ->count();

        return (!empty($checkExam) && ($checkExam > 0)) ? 1 : 0;
    }

    /**
     * Get exam hindi Sub Pages
     *
     * @return object
     */

    public function checkHindSubpage(string $slug, string $section = '', $lang_code = '')
    {
        $hind_exam_id = exam::find()->select('id')->andWhere(['slug' => $slug])
            ->andWhere(['!=', 'lang_code', $lang_code])->one();

        if (empty($hind_exam_id)) {
            return '';
        }

        $examHindiContent = ExamContent::find()
            ->andWhere(['exam_id' => $hind_exam_id['id']])
            ->andWhere(['!=', 'lang_code', $lang_code]);

        if (empty($section)) {
            $examHindiContent = $examHindiContent->andWhere(['slug' => 'overview']);
        } else {
            $examHindiContent = $examHindiContent->andWhere(['slug' => $section]);
        }

        return $examHindiContent->andWhere(['status' => ExamContent::STATUS_ACTIVE, 'parent_id' => null])->one();
    }

    public function authorTranslation($author_id, $lang_code)
    {
        return UserTranslation::find()
            ->select(['user_name'])
            ->where(['tag_user_id' => $author_id])
            ->andWhere(['lang_code' => $lang_code])
            ->one();
    }

    // public function getMenuOrder($exam_id, $entity)
    // {
    //     $getMenuOrder = ManageNavMenuOrder::find()
    //                     ->select(['menu_order'])
    //                     ->where(['entity_id'=>$exam_id])
    //                     ->andWhere(['entity'=>DataHelper::$manageMenuOrder[$entity]])
    //                     ->one();
    //     if (!empty($getMenuOrder)) {
    //         foreach (unserialize($getMenuOrder->menu_order) as $key => $val) {
    //             $menuOrder[$key] = strstr($val, '--', true);
    //         }
    //      // echo "<pre>"; print_r( $menuOrder); die;
    //         return  $menuOrder;
    //     } else {
    //         return [];
    //     }
    // }

    public function getMenuOrder($exam_id, $entity)
    {
        $getMenuOrder = ManageNavMenuOrder::find()
            ->select(['menu_order'])
            ->where(['entity_id' => $exam_id])
            ->andWhere(['entity' => DataHelper::$manageMenuOrder[$entity]])
            ->one();
        $examContentList = ExamContent::find()
            ->select(['slug', 'name', 'id', 'exam_id'])
            ->where(['exam_id' => $exam_id])
            ->andWhere(['parent_id' => null])
            ->andWhere(['status' => ExamContent::STATUS_ACTIVE])
            ->asArray()
            ->all();
        
        if (!empty($getMenuOrder)) {
            foreach (unserialize($getMenuOrder->menu_order) as $key => $val) {
                $menuOrder[$key] = strstr($val, '--', true);
            }
            foreach ($examContentList as $exam) {
                if (!isset($menuOrder[$exam['slug']])) {
                    $menuOrder[$exam['slug']] = $exam['name'];
                }
            }
            return  $menuOrder;
        } else {
            return [];
        }
    }

    public function getDownloadableResource($examId, $examStreamId, $entity, $primary_enity = '')
    {
        $buckets = DataHelper::$mediaDownloadableResource;

        $query =  MediaDrive::find()
            ->alias('md')
            ->select([
                'md.year',
                'md.file_name',
                'mdut.upload_type',
                'e.display_name AS exam_name',
            ])
            ->leftJoin('media_drive_upload_type mdut', 'mdut.id = md.sub_page')
            ->leftJoin('exam e', 'e.id = md.entity_id')
            ->where([
                'e.primary_stream_id' => $examStreamId,
                'mdut.entity' => $entity
            ])
            ->andWhere(['IN', 'mdut.upload_type', $buckets])
            ->andWhere(['md.status' => MediaDrive::STATUS_ACTIVE])
            ->orderBy(['md.year' => SORT_DESC])
            ->asArray();

        if ($primary_enity == 'articles') {
            $query->andWhere(['md.entity_id' => $examId]);
        }

        $mediaDownloadFiles = $query->all();

        $groupedData = [];
        foreach ($mediaDownloadFiles as $row) {
            $uploadTypeValue = $row['upload_type'] == 'Unofficial and Official Answer Keys' ? 'Answer Key' : $row['upload_type'];
            $examName = $row['exam_name'];
            $uploadType = $uploadTypeValue;
            $year = $row['year'];

            $groupedData[$examName][$uploadType][$year][] = [
                'file_name' => $row['file_name'],
            ];
        }

        return $groupedData;
    }

    public static function getCollegePredictorExams($examStreamId, $excludeExamId)
    {
        if (empty($examStreamId)) {
            return [];
        }

        $exams = Exam::find()
            ->select(['exam.display_name', 'exam.name', 'exam.slug', 'exam.cover_image', 'exam.id'])
            ->leftJoin('exam_content', 'exam.id = exam_content.exam_id')
            ->where(['exam.primary_stream_id' => $examStreamId])
            ->andWhere(['exam_content.slug' => 'college-predictor'])
            ->andWhere(['!=', 'exam.id', $excludeExamId])
            ->all();

        return $exams ?? [];
    }

    /**
     * Get all exams with subpage and group them by stream
     *
     * @return array Exams grouped by stream
     */
    public function getAllExamStream($subPageSlug)
    {
        // Get all exams with subpage
        $exams = Exam::find()
            ->select(['exam.display_name', 'exam.name', 'exam.slug', 'exam.cover_image', 'exam.id', 'exam.primary_stream_id'])
            ->leftJoin('exam_content', 'exam.id = exam_content.exam_id')
            ->leftJoin('stream', 'stream.id = exam.primary_stream_id')
            // ->leftJoin('institute_program_rank_mapping iprm', 'iprm.exam_id = exam.id')
            ->where(['exam_content.slug' => $subPageSlug])
            ->andWhere(['not', ['exam.primary_stream_id' => null]])
            ->andWhere(['exam_content.status' => ExamContent::STATUS_ACTIVE])
            // ->andWhere(['iprm.status' => InstituteProgramRankMapping::STATUS_ACTIVE])
            ->all();

        $streamExams = [];

        // Group exams by stream directly
        foreach ($exams as $exam) {
            if (!empty($exam->primary_stream_id)) {
                // Get the stream for this exam
                $stream = $exam->stream;

                if ($stream) {
                    // Initialize the stream entry if it doesn't exist
                    if (!isset($streamExams[$stream->id])) {
                        $streamExams[$stream->id] = [
                            'stream' => $stream,
                            'exams' => []
                        ];
                    }

                    // Add the exam to its stream
                    $streamExams[$stream->id]['exams'][] = $exam;
                }
            }
        }

        return $streamExams;
    }

    public function checkExamCollegePredictorMap($examId)
    {
        $predictor = InstituteProgramRankMapping::find()
            ->select(['exam_id'])
            ->where(['exam_id' => $examId])
            ->andWhere(['status' => InstituteProgramRankMapping::STATUS_ACTIVE])
            ->one();

        return $predictor ?? [];
    }

    public function getParentId($examId, $parentPage)
    {
        $parentId = ExamContent::find()
            ->select(['id'])
            ->where(['exam_id' => $examId])
            ->andWhere(['slug' => $parentPage])
            ->one();

        return $parentId->id ?? '';
    }
}
