<?php

namespace frontend\services;

use common\models\Article;
use common\models\Category;
use common\models\CategoryTranslation;
use common\models\Comment;
use common\models\Tag;
use common\helpers\DataHelper;
use common\models\ArticleSubpage;
use common\models\ArticleSubpageSection;
use common\models\ArticleSubpageSubsectionSubtopic;
use common\models\ArticleSubpageSubsectionQuesAns;
use common\models\CollegeContent;
use common\models\Stream;
use common\services\CollegeService;
use common\services\UserService;
use Yii;
use yii\caching\TagDependency;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\web\Response;

class ArticleService
{
    /**
     * Returns list of articles
     *
     * @param string $orderOn Order on column (default: created_at)
     * @param string $orderBy Order by ascending/descending order (default: desc)
     * @param int $limit Limit (default: 5)
     * @param array $with Fetch relation data. ie: pass ['author'] to get author data
     * @return array
     **/
    public function getAll($limit = 5, $with = [], $exclude = null, $orderOn = 'created_at', $orderBy = 'desc', $entity = 'study-abroad'): array
    {
        $lang_code = DataHelper::getLangId();
        $articles = Article::find()->select(['slug', 'title', 'cover_image', 'entity', 'lang_code', 'author_id', 'updated_at'])
            ->active()
            ->andWhere(['not', ['entity' => $entity]])
            ->andWhere(['lang_code' => $lang_code]);
        if (!empty($with)) {
            $articles->with($with);
        }
        
        if ($orderBy == 'desc') {
            $articles->orderBy([$orderOn => SORT_DESC]);
        } else {
            $articles->orderBy([$orderOn => SORT_ASC]);
        }
        
        if (!empty($exclude)) {
            $articles->byExcludeCategory($exclude);
        }
        
        return $articles->limit($limit)->all();
    }

    public function getDetail($identifier, $entity = 'study-abroad')
    {
        $lang_code = DataHelper::getLangId();

        $article = Article::find()->where(['not', ['entity' => $entity]])->where(['lang_code' => $lang_code])->active()->bySlug($identifier)->one();

        if (empty($article)) {
            $article = Article::find()->active()->byId($identifier)->one();
        }

        return $article;
    }

    public function getArticlePracticeDetail($article)
    {
        $articleSectionExam = [];
        if (!empty($article->articleSection) && !empty($article->articleSubpage)) {
            foreach ($article->articleSection as $examSubject) {
                $topicData = ArticleSubpageSubsectionSubtopic::find()->where([])->select(['slug','name','id'])
                ->where(['article_id'=>$article->id])
                ->andWhere(['article_subpage_section_id'=>$examSubject->id])
                ->orderBy(new \yii\db\Expression('rand()'))
                ->limit(5)
                ->all();
                if (!empty($topicData)) {
                    foreach ($topicData as $topic) {
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $topic->name;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $topic->id;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $examSubject->slug;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $examSubject->id;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions']['description'] = $examSubject->description;
                    }
                }
            }
        }
        
        return $articleSectionExam ?? [];
    }

    public function getArticleSubpage($slug)
    {
        $articleSection = ArticleSubpageSection::find()->select(['article_id','slug','id'])->where(['slug'=>$slug])->one();
        if (!empty($articleSection)) {
            $article = Article::find()->where(['id'=>$articleSection->article_id])->active()->one();
            return $article;
        } else {
            $articleSectionSubTopic = ArticleSubpageSubsectionSubtopic::find()->select(['article_id','slug','id'])->where(['slug'=>$slug])->one();
            $article = Article::find()->where(['id'=>$articleSectionSubTopic->article_id])->active()->one();
            return $article;
        }
    }


    public function postsByCategories($exclude = null)
    {
        $data = [];
        $categories = Category::find()
            ->orderBy('case(position) when 0 then 10 else position end')
            ->active();

        if (!empty($exclude)) {
            $categories->byExcludeCategory($exclude);
        }

        $categories = $categories->all();
        foreach ($categories as $category) {
            $lang_code = DataHelper::getLangId();
            if ($lang_code != 1) {
                $categories_translation = CategoryTranslation::find()->select(['lang_code', 'display_name'])->where(['category_id' => $category->id])->andWhere(['lang_code' => $lang_code])->one();
                if (!empty($categories_translation)) {
                    $name = $categories_translation->display_name;
                }
            } else {
                $name =  $category->name;
            }

            $data[] = [
                'name' => $name ?? '',
                'slug' => $category->slug,
                'articles' => $this->getByCategory($category->id, 15),
                'defaultUser' => UserService::getDefaultUserData(),
            ];
        }

        return $data;
    }

    public function getByCategory($categoryId, $limit = 10, $entity = 'study-abroad')
    {
        $lang_code = DataHelper::getLangId();
        $query = new Query();
        $query->select([
            'a.id', 'a.entity', 'a.entity_id', 'a.country_slug', 'a.title', 'a.slug', 'a.cover_image', 'a.h1', 'a.meta_title',
            'a.updated_at', 'u.name', 'u.status as userStatus', 'u.username', 'u.slug as user_slug', 'a.lang_code', 'ut.user_name', 'p.about'
        ])->from('article as a')
            ->leftJoin('user as u', 'u.id = a.author_id')
            ->leftJoin('user_translation as ut', 'ut.tag_user_id = a.author_id')
            ->leftJoin('profile as p', 'p.user_id = a.author_id')
            ->where(['a.category_id' => $categoryId])
            ->andWhere(['a.lang_code' => $lang_code])
            ->andWhere(['a.status' => Article::STATUS_ACTIVE])
            ->andWhere(['not', ['a.entity' => $entity]])
            ->orderBy(['a.updated_at' => SORT_DESC]);

        return $query->limit($limit)->all();
    }

    public function getTrendingArticleWithAuthor($limit = 4, $exclude = null, $entity = 'study-abroad')
    {
        $lang_code = DataHelper::getLangId();
        $article = Article::find()->select(['slug', 'title', 'entity', 'cover_image', 'h1', 'author_id', 'lang_code'])
            ->where(['is_popular' => Article::POPULAR_YES])
            ->andWhere(['not', ['entity' => $entity]])
            ->andWhere(['lang_code' => $lang_code])
            ->andWhere(['!=', 'category_id', Category::EXCLUDE_CATEGORY])
            ->andWhere(['status' => Article::STATUS_ACTIVE])
            ->limit($limit)
            ->orderBy(['updated_at' => SORT_DESC]);

        if ($exclude) {
            $article->andWhere(['not in', 'slug', $exclude]);
        }

        return $article->all();
    }


    public function getTrendings($limit = 4, $entity = 'study-abroad', $stream = null)
    {
        $lang_code = DataHelper::getLangId();
        $query = Article::find()->select(['slug', 'title', 'entity', 'cover_image', 'country_slug', 'h1', 'author_id', 'lang_code'])
            ->where(['is_popular' => Article::POPULAR_YES])
            ->andWhere(['lang_code' => $lang_code])
            ->andWhere(['not', ['entity' => $entity]])
            ->andWhere(['!=', 'category_id', Category::EXCLUDE_CATEGORY])
            ->andWhere(['status' => Article::STATUS_ACTIVE])
            ->limit($limit)
            ->orderBy(['updated_at' => SORT_DESC]);

        if (!empty($stream)) {
            $query->andWhere(['stream_id' => $stream]);
        }

        $article = $query->all();

        return $article ?? [];
    }

    public function getRecentArticles($exclude = '', $limit = 5, $entity = 'articles', $stream = null)
    {
        $lang_code = DataHelper::getLangId();

        $query = Article::find()->select(['slug', 'title', 'entity', 'cover_image', 'lang_code'])
            ->from('article')
            ->andWhere(['entity' => $entity])
            ->andWhere(['lang_code' => $lang_code])
            ->andWhere(['status' => Article::STATUS_ACTIVE])
            ->andWhere(['is_popular' => Article::POPULAR_NO])
            ->limit($limit)
            ->orderBy(['updated_at' => SORT_DESC]);

        if (!empty($stream)) {
            $query->andWhere(['stream_id' => $stream]);
        }

        if (!empty($exclude)) {
            $query->andWhere(['not', ['category_id' => $exclude]]);
        }

        $article = $query->all();

        return $article ?? [];
    }

    public function byTagId(Tag $tag, $limit = null, $perPage = 10)
    {
        if ($limit) {
            return $tag->getPublishedArticles($limit);
        }

        $articles = $tag->getPublishedArticles();
        $articles->setPagination([
            'pageSize' => $perPage,
            'defaultPageSize' => $perPage // Added this to remove per_page params from url
        ]);

        return $articles;
    }

    public function getComments($id, $entity = '')
    {
        return Comment::find()
            ->with('children')
            ->where(['entity' => $entity])
            ->andWhere(['entity_id' => $id])
            ->andWhere(['parent_id' => null])
            ->active()
            ->all();
    }

    public function getTagBySlug($slug)
    {
        return Tag::find()->bySlug($slug)->active()->one();
    }

    public function getPostByTag($id)
    {
        return Tag::find()->getPublishedArticlesById($id);
    }

    public function getAdTargetData(Article $article)
    {
        $hash = __CLASS__ . __FUNCTION__ . md5(base64_encode(serialize($article)));
        $data = Yii::$app->cache->getOrSet($hash, function () use ($article) {
            if (empty($article->college)) {
                return [];
            }
            foreach ($article->college as $college) {
                $query = new Query();
                $query->distinct();
                $query->select(['college.slug as collegeName', 'city.slug as cityName', 'state.slug as stateName', 'course.name as courseName', 'cc.course_id', 'stream.name as streamName', 'course.degree'])
                    ->from('college college')
                    ->leftJoin('college_program as cc', 'cc.college_id = college.id')
                    ->leftJoin('course as course', 'course.id = cc.course_id')
                    ->leftJoin('stream as stream', 'stream.id = course.stream_id')
                    ->leftJoin('city as city', 'city.id = college.city_id')
                    ->leftJoin('state as state', 'state.id = city.state_id')
                    ->where(['college.id' => $college->id]);
                $collegeData = $query->all(\Yii::$app->db);

                $courses = array_unique(ArrayHelper::getColumn($collegeData, 'courseName'));
                $streams = array_unique(ArrayHelper::getColumn($collegeData, 'streamName'));
                $degrees = array_unique(ArrayHelper::getColumn($collegeData, 'degree'));
                $collegeName = array_unique(ArrayHelper::getColumn($collegeData, 'collegeName'));
                $cityName = array_unique(ArrayHelper::getColumn($collegeData, 'cityName'));
                $stateName = array_unique(ArrayHelper::getColumn($collegeData, 'stateName'));

                $result[] = [
                    'CollegeName' => $collegeName ? $collegeName[0] : '',
                    'State' => $stateName ? $stateName[0] : '',
                    'City' => $cityName ?  $cityName[0] : '',
                    'degree' => $degrees ?? '',
                    'discipline' => $streams ?? '',
                    'courses' => $courses ?? ''
                ];
            }

            if (!empty($result)) {
                return $result ?? [];
            }
        }, 60 * 60 * 6, new TagDependency(['tags' => 'get-ad-target-data-' . $article->slug]));

        return $data ?? [];
    }

    public static function createCollegeContentArticle($collegeIds)
    {
        foreach ($collegeIds as $collegeId) {
            $content =  CollegeContent::find()
                ->where(['entity_id' => $collegeId])
                ->andWhere(['sub_page' => 'news'])
                ->one();

            if (empty($content)) {
                $articles = new Query();
                $articles->select(['a.id'])
                    ->from(['article a'])
                    ->innerJoin('article_college ac', 'a.id = ac.article_id')
                    ->where(['ac.college_id' => $collegeId])
                    ->andWhere(['a.status' => Article::STATUS_ACTIVE]);

                if ($articles->count() >= 4) {
                    $newContent = new CollegeContent();
                    $newContent->author_id = 1;
                    $newContent->entity = 'college';
                    $newContent->entity_id = $collegeId;
                    $newContent->sub_page = 'news';
                    $newContent->status = 1;
                    $newContent->save();
                }
            }
        }

        Yii::$app->response->format = Response::FORMAT_JSON;
        return ['status' => true, 'msg' => 'News college content saved successfully.'];
    }

    public function postsByStreams()
    {
        $data = [];
        $streams = Stream::find()->active();
        $streams = $streams->all();
        foreach ($streams as $stream) {
            $data[] = [
                'name' => $stream->name,
                'slug' => $stream->slug
            ];
        }
        return $data;
    }

    public function getArticleSubpageSection($slug)
    {
        $articleSubpageSection =  ArticleSubpageSection::find()->where(['slug'=>$slug])->one();
        return $articleSubpageSection ?? '';
    }

    public function getArticleSubpageSectionTopic($slug)
    {
        $articleSubpageSection =  ArticleSubpageSubsectionSubtopic::find()->where(['slug'=>$slug])->one();
        return $articleSubpageSection ?? '';
    }
    public function getArticlePracticeDetailSubTopic($article, $section_id)
    {
        $articleSectionExamSubTopic = [];
        if (!empty($article->articleSection) && !empty($article->articleSubpage)) {
            //foreach ($article->articleSection as $examSubject) {
                $examSubject = ArticleSubpageSection::find()->where(['id'=>$section_id])->one();
                $topicData = ArticleSubpageSubsectionSubtopic::find()->where([])->select(['slug','name','id'])
                ->where(['article_id'=>$article->id])
                ->andWhere(['article_subpage_section_id'=>$section_id])
                ->orderBy(new \yii\db\Expression('rand()'))
                ->limit(5)
                ->all();
            if (!empty($topicData)) {
                foreach ($topicData as $topic) {
                    $topicDataQuesAns = ArticleSubpageSubsectionQuesAns::find()->where([])->select(['question','answer','id','explation'])
                                ->where(['article_id'=>$article->id])
                                ->andWhere(['article_subpage_section_subtopic_id'=>$topic->id])
                                ->orderBy(new \yii\db\Expression('rand()'))
                                ->limit(2)
                                ->all();
                    foreach ($topicDataQuesAns as $question) {
                        $articleSectionExamSubTopic[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->id][]['question'] = unserialize($question->question);
                        $articleSectionExamSubTopic[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->id][]['question_id'] = $question->id;
                        $articleSectionExamSubTopic[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->id][]['exam'] = $examSubject->slug;
                        $articleSectionExamSubTopic[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->id][]['answer'] = $question->explation;
                        $articleSectionExamSubTopic[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->id][]['answer_show'] = $question->answer;
                    }
                }
            }
            //}
        }
        return $articleSectionExamSubTopic ?? [];
    }

    public function getArticlePracticeDetailSubTopicQues($article, $article_subpage_section_id, $article_subpage_section_topic_id, $articleSubSectionTopic_id)
    {
        $questionSets = [];
        if (!empty($article->articleSection) && !empty($article->articleSubpage)) {
                $examSubject = ArticleSubpageSection::find()->where(['id'=>$article_subpage_section_id])->one();
                $topicData = ArticleSubpageSubsectionQuesAns::find()->where([])->select(['question','answer','explation','id','question_instruction','instruction_id'])
                ->where(['article_id'=>$article->id])
                ->andWhere(['article_subpage_section_id'=>$article_subpage_section_id])
                ->andWhere(['article_subpage_subsection_id'=>$article_subpage_section_topic_id])
                ->andWhere(['article_subpage_section_subtopic_id'=>$articleSubSectionTopic_id])
                ->andWhere(['instruction_id'=>''])
                ->all();
            if (!empty($topicData)) {
                $j=1;
                $i=1;
                $k=0;
                $loopCount = ceil(count($topicData)/10);
                for ($i = 1; $i <= $loopCount; $i++) {
                    for ($j = 0; $j < 10; $j++) {
                        $option = unserialize($topicData[$k]->question);
                        $questionSets['Practice Set ' . $i][$j]['question'] = $option['question']['question'];
                        $questionSets['Practice Set ' . $i][$j]['options'] = $option['question']['option'];
                        $questionSets['Practice Set ' . $i][$j]['answer'] = $topicData[$k]->answer;
                        $questionSets['Practice Set ' . $i][$j]['explation'] = $topicData[$k]->explation;
                        $questionSets['Practice Set ' . $i][$j]['id'] = $topicData[$k]->id;
                        $questionSets['Practice Set ' . $i][$j]['article_subpage_section_id'] = $article_subpage_section_id;
                        $questionSets['Practice Set ' . $i][$j]['article_subpage_section_topic_id'] = $article_subpage_section_topic_id;
                        $questionSets['Practice Set ' . $i][$j]['articleSubSectionTopic_id'] = $articleSubSectionTopic_id;
                        $questionSets['Practice Set ' . $i][$j]['instruction_id'] = $topicData[$k]->instruction_id;


                        if (count($topicData)-1 <= $k) {
                            break;
                        }
                        $k++;
                    }
                }
            }
            $topicDataInstructionQuestion = ArticleSubpageSubsectionQuesAns::find()->where([])->select(['question','answer','explation','id','question_instruction','instruction_id'])
            ->where(['article_id'=>$article->id])
            ->andWhere(['article_subpage_section_id'=>$article_subpage_section_id])
            ->andWhere(['article_subpage_subsection_id'=>$article_subpage_section_topic_id])
            ->andWhere(['article_subpage_section_subtopic_id'=>$articleSubSectionTopic_id])
            ->andWhere(['!=','instruction_id',''])
            ->orderBy('instruction_id')
            ->all();
            $topicDataInstructionQuestionIds = ArticleSubpageSubsectionQuesAns::find()->where([])->select(['instruction_id'])
            ->where(['article_id'=>$article->id])
            ->andWhere(['article_subpage_section_id'=>$article_subpage_section_id])
            ->andWhere(['article_subpage_subsection_id'=>$article_subpage_section_topic_id])
            ->andWhere(['article_subpage_section_subtopic_id'=>$articleSubSectionTopic_id])
            ->andWhere(['!=','instruction_id',''])
            ->distinct()
            ->orderBy('instruction_id')
            ->asArray()
            ->all();
            if (!empty($topicDataInstructionQuestion)) {
                $k=0;
                $instructionID ='';
                foreach ($topicDataInstructionQuestionIds as $id) {
                    $j=0;
                    foreach ($topicDataInstructionQuestion as $optionValue) {
                        if ($id['instruction_id']==$optionValue->instruction_id) {
                            $option = unserialize($optionValue->question);
                            $questionSets['Practice Set ' . $i][$j]['question'] = $option['question']['question'];
                            $questionSets['Practice Set ' . $i][$j]['options'] = $option['question']['option'];
                            $questionSets['Practice Set ' . $i][$j]['answer'] = $optionValue->answer;
                            $questionSets['Practice Set ' . $i][$j]['explation'] = $optionValue->explation;
                            $questionSets['Practice Set ' . $i][$j]['question_instruction'] = $optionValue->question_instruction;
                            $questionSets['Practice Set ' . $i][$j]['instruction_id'] = $optionValue->instruction_id;
                            $questionSets['Practice Set ' . $i][$j]['id'] = $optionValue->id;
                            $questionSets['Practice Set ' . $i][$j]['article_subpage_section_id'] = $article_subpage_section_id;
                            $questionSets['Practice Set ' . $i][$j]['article_subpage_section_topic_id'] = $article_subpage_section_topic_id;
                            $questionSets['Practice Set ' . $i][$j]['articleSubSectionTopic_id'] = $articleSubSectionTopic_id;

                            $j++;
                        }
                    }
                    $i++;
                }
            }
        }
       // echo "<pre>"; print_r($questionSets); die;
        return $questionSets ?? [];
    }

    public function getArticlePracticleSectionData($article, $article_subpage_section_id)
    {

        $examSubject = ArticleSubpageSection::find()->where(['id'=>$article_subpage_section_id])->one();
        $articleSubPage = ArticleSubpage::find()->where(['id'=>$examSubject->article_subpage_id])->one();
        $sectionData = [];
        $sectionData['name'] = $articleSubPage->name;
        $sectionData['section_name'] = $examSubject->name;
        return  $sectionData ?? [];
    }

    public function getArticleAllScetion($article)
    {
        $allSection = [];
        $examSubject = ArticleSubpageSection::find()->where(['article_id'=>$article->id])->andWhere(['status'=>ArticleSubpageSection::STATUS_ACTIVE])->all();
        foreach ($examSubject as $exam) {
            $allSection[$exam->slug]['name'] = $exam->name;
            $allSection[$exam->slug]['article_id'] = $exam->article_id;
            $allSection[$exam->slug]['id'] = $exam->id;
            $allSection[$exam->slug]['slug'] = $exam->slug;
        }
        return $allSection ?? [];
    }
    
    public function getArticleAllScetionWidget($article, $sectionSkipID)
    {
        $allSection = [];
        $examSubject = ArticleSubpageSection::find()->where(['article_id'=>$article->id])
                        ->andWhere(['!=','id',$sectionSkipID])
                        ->andWhere(['status'=>ArticleSubpageSection::STATUS_ACTIVE])
                       ->all();
        foreach ($examSubject as $exam) {
            $allSection[$exam->slug]['name'] = $exam->name;
            $allSection[$exam->slug]['article_id'] = $exam->article_id;
            $allSection[$exam->slug]['id'] = $exam->id;
            $allSection[$exam->slug]['slug'] = $exam->slug;
        }
        return $allSection ?? [];
    }

    public function getArticlePracticeDetailActiveSection($article, $section_id)
    {
        $articleSectionExam = [];
        if (!empty($article->articleSection) && !empty($article->articleSubpage)) {
            foreach ($article->articleSection as $examSubject) {
                if ($section_id!=$examSubject->id) {
                    continue;
                }
                $topicData = ArticleSubpageSubsectionSubtopic::find()->where([])->select(['slug','name','id'])
                ->where(['article_id'=>$article->id])
                ->andWhere(['article_subpage_section_id'=>$examSubject->id])
                ->orderBy(new \yii\db\Expression('rand()'))
                //->limit(30)
                ->all();
                if (!empty($topicData)) {
                    foreach ($topicData as $topic) {
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $topic->name;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $topic->id;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $examSubject->slug;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions']['description'] = $examSubject->description;
                    }
                }
            }
        }
        return $articleSectionExam ?? [];
    }
    public function getArticlePracticeDetailRestSection($article, $section_id)
    {
        $articleSectionExam = [];
        if (!empty($article->articleSection) && !empty($article->articleSubpage)) {
            foreach ($article->articleSection as $examSubject) {
                if ($section_id==$examSubject->id) {
                    continue;
                }
                $topicData = ArticleSubpageSubsectionSubtopic::find()->where([])->select(['slug','name','id'])
                ->where(['article_id'=>$article->id])
                ->andWhere(['article_subpage_section_id'=>$examSubject->id])
                ->orderBy(new \yii\db\Expression('rand()'))
                ->limit(5)
                ->all();
                if (!empty($topicData)) {
                    foreach ($topicData as $topic) {
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $topic->name;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $topic->id;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions'][$topic->slug][] = $examSubject->slug;
                        $articleSectionExam[$article->articleSubpage->name . ' ' . $examSubject->name . ' Important Questions']['description'] = $examSubject->description;
                    }
                }
            }
        }
        return $articleSectionExam ?? [];
    }

    public function getArticlePracticeDetailSubTopicQuesAjax($article, $article_subpage_section_id, $article_subpage_section_topic_id, $articleSubSectionTopic_id, $offset, $isInstruction)
    {
        $questionSets = [];
        
        if (!empty($article->articleSection) && !empty($article->articleSubpage)) {
            if ($isInstruction == '') {
                $examSubject = ArticleSubpageSection::find()->where(['id'=>$article_subpage_section_id])->one();
                $topicData = ArticleSubpageSubsectionQuesAns::find()->where([])->select(['question','answer','explation','id','question_instruction','instruction_id'])
                ->where(['article_id'=>$article->id])
                ->andWhere(['article_subpage_section_id'=>$article_subpage_section_id])
                ->andWhere(['article_subpage_subsection_id'=>$article_subpage_section_topic_id])
                ->andWhere(['article_subpage_section_subtopic_id'=>$articleSubSectionTopic_id])
                ->andWhere(['instruction_id'=>''])
                ->offset($offset*10)
                ->limit(10)
                ->all();
                $offset = $offset+1;
                if (!empty($topicData)) {
                    $j=1;
                    $i=1;
                    $k=0;
                    $loopCount = ceil(count($topicData)/10);
                    for ($i = 1; $i <= $loopCount; $i++) {
                        for ($j = 0; $j < 10; $j++) {
                            $option = unserialize($topicData[$k]->question);
                            $questionSets['Practice Set ' . $offset][$j]['question'] = $option['question']['question'];
                            $questionSets['Practice Set ' . $offset][$j]['options'] = $option['question']['option'];
                            $questionSets['Practice Set ' . $offset][$j]['answer'] = $topicData[$k]->answer;
                            $questionSets['Practice Set ' . $offset][$j]['explation'] = $topicData[$k]->explation;
                            $questionSets['Practice Set ' . $offset][$j]['id'] = $topicData[$k]->id;

                            if (count($topicData)-1 <= $k) {
                                break;
                            }
                            $k++;
                        }
                    }
                }
            } else {
                $topicDataInstructionQuestion = ArticleSubpageSubsectionQuesAns::find()->where([])->select(['question','answer','explation','id','question_instruction','instruction_id'])
                ->where(['article_id'=>$article->id])
                ->andWhere(['article_subpage_section_id'=>$article_subpage_section_id])
                ->andWhere(['article_subpage_subsection_id'=>$article_subpage_section_topic_id])
                ->andWhere(['article_subpage_section_subtopic_id'=>$articleSubSectionTopic_id])
                ->andWhere(['!=','instruction_id',''])
                ->andWhere(['instruction_id'=>$isInstruction])
                ->orderBy('instruction_id')
                ->all();
                $topicDataInstructionQuestionIds = ArticleSubpageSubsectionQuesAns::find()->where([])->select(['instruction_id'])
                ->where(['article_id'=>$article->id])
                ->andWhere(['article_subpage_section_id'=>$article_subpage_section_id])
                ->andWhere(['article_subpage_subsection_id'=>$article_subpage_section_topic_id])
                ->andWhere(['article_subpage_section_subtopic_id'=>$articleSubSectionTopic_id])
                ->andWhere(['!=','instruction_id',''])
                ->andWhere(['instruction_id'=>$isInstruction])
                ->distinct()
                ->orderBy('instruction_id')
                ->asArray()
                ->all();
                if (!empty($topicDataInstructionQuestion)) {
                    $k=0;
                    $instructionID ='';
                    foreach ($topicDataInstructionQuestionIds as $id) {
                        $j=0;
                        foreach ($topicDataInstructionQuestion as $optionValue) {
                            if ($id['instruction_id']==$optionValue->instruction_id) {
                                $option = unserialize($optionValue->question);
                                $questionSets['Practice Set ' . $offset][$j]['question'] = $option['question']['question'];
                                $questionSets['Practice Set ' . $offset][$j]['options'] = $option['question']['option'];
                                $questionSets['Practice Set ' . $offset][$j]['answer'] = $optionValue->answer;
                                $questionSets['Practice Set ' . $offset][$j]['explation'] = $optionValue->explation;
                                $questionSets['Practice Set ' . $offset][$j]['question_instruction'] = $optionValue->question_instruction;
                                $questionSets['Practice Set ' . $offset][$j]['instruction_id'] = $optionValue->instruction_id;
                                $questionSets['Practice Set ' . $offset][$j]['id'] = $optionValue->id;
                                $j++;
                            }
                        }
                       // $i++;
                    }
                }
            }
        }
        //echo "<pre>"; print_r($questionSets); die;
        return $questionSets ?? [];
    }
}
