<?php

namespace console\controllers;

use common\helpers\ContentHelper;
use common\models\LiveUpdate;
use common\models\News;
use common\models\NewsCategory;
use common\models\NewsContent;
use common\models\NewsSubdomainLiveUpdate;
use common\models\NewsSubdomain;
use common\models\Tags;
use common\models\User;
use common\event\SitemapEvent;
use common\services\NewsService;
use Exception;
use Yii;
use yii\console\Controller;
use Carbon\Carbon;
use common\models\Article;
use yii\db\Query;

class NewsController extends Controller
{
    //import all news posts to news table
    public function actionAllPostsToYiiTable()
    {
        $newService = new NewsService();
        $page = 1;
        $per_page = 20;
        $pagination = true;
        $notMapped = [];

        do {
            $allNews = $newService->getAllPosts($page, $per_page)->response;

            foreach ($allNews as $news) {
                sleep(2);
                if (empty($news)) {
                    continue;
                }

                if (count($allNews) < $per_page) {
                    $pagination = false;
                }

                $model = News::find()
                    ->where(['id' => $news->id])
                    ->one();

                if (!$model) {
                    $model = new News();
                }

                $banner_image = isset($news->_embedded->{'wp:featuredmedia'}[0]->source_url) ? explode('/', $news->_embedded->{'wp:featuredmedia'}[0]->source_url) : '';
                $bannerCaption = isset($news->_embedded->{'wp:featuredmedia'}[0]->caption) ? ($news->_embedded->{'wp:featuredmedia'}[0]->caption->rendered ?? '') : '';

                $model->id = $news->id;
                $model->news_category_id = isset($news->categories) ? $this->getOrCreateCategory($news->categories[0]) : '';
                $model->name = isset($news->title) ? $news->title->rendered : '';
                $model->display_name = !empty($model->display_name) ? $model->display_name : null;
                $model->slug = $news->slug ?? '';
                $model->banner_image = !empty($banner_image) ? $banner_image[7] : '';
                $model->banner_caption = $bannerCaption ?? '';
                $model->is_popular = !empty($model->is_popular) ? $model->is_popular : News::POPULAR_NO;

                if (!empty($news->tags)) {
                    foreach ($news->tags as $liveTag) {
                        if ($liveTag == LiveUpdate::LIVE_NEWS_TAG_ID) {
                            $model->is_live = News::IS_LIVE_YES;
                        }
                    }
                }
                $model->is_live = News::IS_LIVE_NO;
                $model->position = null;
                $model->expired_at = !empty($model->expired_at) ? $model->expired_at : null;
                $model->status = News::STATUS_ACTIVE;
                $model->created_at = $news->date ?? new \yii\db\Expression('NOW()');
                $model->updated_at = $news->modified ?? new \yii\db\Expression('NOW()');

                if ($model->save()) {
                    echo "{$model->name} \t {$model->slug} \n";

                    if (isset($news->_embedded->{'wp:featuredmedia'}[0]) && empty($news->_embedded->{'wp:featuredmedia'}[0]->source_url)) {
                        continue;
                    }

                    try {
                        $imagePath = $news->_embedded->{'wp:featuredmedia'}[0]->source_url;
                        $newPath = '/var/www/html/gmu-yii-backend/frontend/web/yas/images/news-images/';
                        $newName  = $newPath . $banner_image[7];

                        $copied = copy($imagePath, $newName);

                        if ((!$copied)) {
                            echo "Error Not Copied \n";
                        } else {
                            echo "{$model->banner_image} copied \n";
                        }
                    } catch (Exception $e) {
                        print_r($e->getMessage());
                    }

                    if (empty($news->content)) {
                        echo "content is empty for {$model->slug} \n";
                        continue;
                    }

                    $newsContent = NewsContent::find()->where(['news_id' => $model->id])->one();
                    $metaData = isset($news->yoast_head) ? ContentHelper::parseMetaTags($news->yoast_head) : '';
                    $author = isset($news->_embedded->author) ? ($news->_embedded->author[0] ? $news->_embedded->author[0]->slug : '') : '';

                    if (!$newsContent) {
                        $newsContent = new NewsContent();
                    }

                    $newsContent->news_id = $model->id ?? '';
                    $newsContent->author_id = $this->getAuthor($author) ?? '';
                    $newsContent->content = isset($news->content) ? html_entity_decode($news->content->rendered) : '';
                    $newsContent->meta_title = $metaData['title'] ?? '';
                    $newsContent->meta_description = $metaData['description'] ?? '';
                    $newsContent->h1 = isset($news->title->rendered) ? $news->title->rendered : '';
                    $newsContent->created_at = $news->date ?? new \yii\db\Expression('NOW()');
                    $newsContent->updated_at = $news->modified ?? new \yii\db\Expression('NOW()');
                    $newsContent->status = NewsContent::STATUS_ACTIVE;

                    try {
                        $newsContent->save();

                        echo "{$newsContent->news_id} \n";

                        if (empty($news->tags)) {
                            echo "tag id is empty for {$model->slug} \n";
                            continue;
                        }
                        foreach ($news->tags as $tagId) {
                            $tags = $this->getOrCreateTag($tagId);

                            $model->link('tags', $tags);

                            echo "\t $model->name  \t $tags->id \n";
                        }
                        print_r($notMapped);
                    } catch (Exception $e) {
                        print_r($e->getMessage());
                    }
                }
            }
            $page++;
        } while ($pagination);
        return;
    }

    public function getOrCreateCategory($categoryId)
    {
        $model = NewsCategory::find()->where(['id' => $categoryId])->one();

        if ($model) {
            return $model->id;
        } else {
            $model = new NewsCategory();
        }

        $category = (new NewsService())->getCategoryById($categoryId);

        $model->id = $category->id;
        $model->name = $category->name ?? '';
        $model->slug = $category->slug ?? '';
        $model->is_popular = NewsCategory::POPULAR_NO;
        $model->is_featured = NewsCategory::FEATURED_NO;
        $model->position = null;
        $model->status = NewsCategory::STATUS_ACTIVE;

        if ($model->save()) {
            return $model->id;
        } else {
            return null;
        }
    }

    public function getOrCreateTag($tagId)
    {
        $model = Tags::findOne(['id' => $tagId]);

        if ($model) {
            return $model;
        } else {
            $model = new Tags();
        }

        $tag = (new NewsService())->getTagById($tagId);

        $model->id = $tag->id;
        $model->name = $tag->name ?? '';
        $model->slug = $tag->slug ?? '';
        $model->is_popular = Tags::POPULAR_NO;
        $model->is_featured = Tags::FEATURED_NO;
        $model->status = Tags::STATUS_ACTIVE;

        if ($model->save()) {
            return $model;
        } else {
            return null;
        }
    }

    public function getAuthor($slug)
    {
        $authorId = User::find()->select(['id'])->where(['slug' => $slug])->one();

        return $authorId->id ?? '';
    }

    public static $emptyUrls = [
        'jeecup-2022-counselling-process-for-round-4-commences-today-check-important-dates-here',
        'west-bengal-neet-pg-counselling-2022-final-counselling-list-for-medical-and-dental-courses-out-on-wbmcc-nic-in',
        'tnea-results-2022-will-be-out-soon-heres-how-to-download-your-scorecard',
        'jee-main-2022-july-30-shift-1-b-tech-question-paper-with-solution-download-pdf',
        'apsche-likely-to-release-the-ap-eamcet-2022-counselling-seat-allotment-results-today-cets-apsche-ap-gov-in',
        'cbse-10th-compartment-result-2022-declared-at-cbseresults-nic-in-download-now',
        'ap-eamcet-2022-day-4-exam-have-been-successfully-conducted-here-are-the-memory-based-question-papers-for-5-6-and-7-july',
        'nmat-2022-nmims-sbm-commences-registration-for-mba-2023-25-batch',
        'jee-advanced-2022-aat-results-released-yesterday-qualified-students-can-use-direct-link-here-for-josaa-counselling-registration',
        'ts-pgecet-answer-key-2022-released-download-now',
        'hpu-mat-2022-second-merit-list-for-non-subsidised-seats-out-deadline-to-deposit-admission-fee-is-sep-8',
        'utkal-university-entrance-test-2022-application-process-begins-check-dates-fees-application-process',
        'jharkhand-hsc-students-protest-against-unfair-results',
        'mah-mba-cet-2022-exam-scheduled-from-aug-23-check-exam-day-preparation-tips-revision-topics',
        'iimc-entrance-2022-result-declared-check-steps-to-download',
        'dsssb-pgt-exam-dates-2022-released-for-sanskrit-urdu-music-computer-pgt-posts-at-dsssb-delhi-gov-in',
        'mdu-rohtak-admissions-2022-application-deadline-extended-for-mtech-mpharm-programmes-till-august-27',
        'upesmet-2022-exam-day-guidelines-exams-commence-tomorrow-3-september-check-dos-donts-here',
        'up-btech-counselling-2022-registrations-expected-to-be-out-on-31-aug-check-detailed-process-here',
        'ugc-net-2022-subject-wise-exam-schedule-announced-for-july-9-11-12-check-details-here',
        'ts-pecet-2022-application-form-submission-window-deadline-with-late-fee-of-inr-500-5-september-apply-now',
        'wb-anm-gnm-2nd-seat-allotment-2022-list-published-direct-link-to-the-list-documents-checklist-here',
        'reap-2022-application-process-deadline-tomorrow-check-eligibility-criteria-steps-to-register',
        'cat-application-form-2022-window-closing-tomorrow-at-iimcat-ac-in-steps-to-apply-eligibility-criteria',
        'cbse-revaluation-2022-application-window-for-class-10-and-12-opens-tomorrow-check-dates-fee-details',
        'nit-hamirpur-admissions-2022-application-window-opened-for-btech-barch-and-dual-degree-josaa-csab-2022-programmes',
        'jmi-ug-admissions-2022-now-open-applications-through-nata-cuet-jee-main-eligible',
        'mhcet-law-2022-for-3-year-llb-entrance-exam-rescheduled-for-centres-that-reported-technical-challenges',
        'rrb-phase-5-exam-city-date-viewing-facility-for-rrc-south-western-railway-hubli-to-be-activated-at-11-am-today',
        'mh-cet-law-result-2022-for-3-year-llb-releasing-today-download-scorecard-at-cetcell-mahacet-org',
        'iift-2023-application-form-window-opens-soon-check-eligibility-criteria-and-documents-required',
        'tnpsc-group-5a-recruitment-2022-notification-issued-for-161-posts-of-assistant-section-officer-on-tnpsc-gov-in',
        'neet-2022-result-expected-soon-check-neet-2022-cutoff-qualifying-marks-expected-cut-off-for-government-colleges',
        'pgimer-counselling-2022-round-2-of-counselling-admissions-for-bsc-nursing-commence-today-at-11-am',
        'cuet-2022-faces-reschedule-requests-students-flood-twitter-with-requests',
        'neet-ug-2021-migration-certificate-not-mandatory-latest-notice-by-mcc-check-details-here',
        'tsche-extends-application-without-late-fee-for-ts-lawcet-2022-and-pglcet-2022-till-july-5-check-details-here',
        'rrb-group-d-cutoff-2022-likely-out-after-result-check-expected-cutoff-previous-years-trends',
        'icai-ca-foundation-registration-2022-window-opens-for-december-session-today-check-steps-to-apply-fees-dates',
        'pm-yasasvi-scholarships-2022-application-deadline-postponed-to-11-september-direct-link-here-apply-now',
        'dsssb-2022-exam-date-announced-for-august-september-check-schedule-for-ae-dy-manager-here',
        'jee-main-2022-question-paper-session-2-download-memory-based-questions-pdf-for-all-shifts',
        'tnea-2022-counselling-deferred-due-to-delay-in-neet-ug-results-admissions-to-resume-two-days-after-announcement',
        'iim-bangalore-admissions-2023-for-epgp-underway-check-eligibility-criteria-here-apply-before-10-october',
        'jac-chandigarh-counselling-2022-seat-allotment-round-1-announced-at-jacchd-admissions-nic-in-steps-to-check',
        'ssc-cgl-2022-notification-tentatively-releasing-tomorrow-at-ssc-nic-in-higher-vacancies-expected',
        'aicte-doctoral-fellowship-scheme-2022-iiit-delhi-starts-registration-process-for-phd-programmes-iiitd-ac-in',
        'aiims-m-sc-nursing-2022-choice-filling-window-closes-tomorrow-first-seat-allotment-out-on-sep-8',
        'wbjeeb-has-announced-the-wbjee-2022-counselling-dates-for-jee-main-qualified-candidates-at-wbjeeb-nic-in',
        'iim-bangalore-adds-new-exam-city-centres-for-cat-exam-2022-check-eligibility-criteria-to-apply',
        'lucknow-university-published-ugpet-answer-key-2022-for-bba-courses-download-pdf-at-lkouniv-ac-in',
        'josaa-2022-application-form-available-now-heres-how-to-complete-your-josaa-counselling-registration',
        'cuet-ug-results-2022-likely-out-tomorrow-check-list-of-central-universities-accepting-cuet-scores',
        'upsc-cds-2-mathematics-exam-date-on-4-sep-check-important-topics-last-minute-preparation-tips',
        'uttarakhand-polytechnic-seat-allotment-results-2022-for-round-2-to-be-out-today-check-steps-to-download',
        'mh-law-cet-exam-2022-students-appeal-to-state-ministers-after-technical-issue-leads-to-exam-cancellation',
        'sbi-po-recruitment-2022-official-notification-published-registration-starts-today',
        'cseet-syllabus-2023-released-icsi-announces-executive-professional-courses-exam-syllabus',
        'sams-odisha-plus-3-merit-list-2022-for-round-1-expected-today-at-2-pm-download-on-samsodisha-gov-in',
        'cee-kerala-published-keam-seat-allotment-2022-for-round-1-at-cee-kerala-gov-in-check-documents-required-for-admission',
        'ssc-stenographer-exam-2020-document-verification-schedule-out-check-list-of-original-documents-required',
        'upsc-cds-2-english-exam-date-on-4-sep-check-important-topics-last-minute-preparation-tips',
        'upsssc-final-answer-keys-for-combined-assistant-aro-aso-published-direct-download-link-here',
        'bpsc-67th-prelims-admit-card-2022-for-re-exam-expected-to-be-out-on-sep-20-re-exam-postponed-to-sep-30',
        'lbs-rank-list-2022-bsc-nursing-and-paramedical-courses-rank-list-2022-out-on-lbscentre-kerala-gov-in',
        'indian-coast-guard-application-form-2023-deadline-extended-for-yantrik-navik-posts-register-now',
        'tsbie-released-ts-inter-application-form-2022-23-at-tsbie-cgg-gov-in-check-eligibility-criteria',
        'tnusrb-recruitment-2022-exam-schedule-for-grade-2-police-constables-jail-wardens-firemen-posts-out',
        'tripura-state-education-minister-announces-the-foundation-for-5-new-colleges-in-the-state-details-here',
        'reserve-bank-of-india-declared-rbi-grade-b-phase-2-result-2022-for-depr-and-dsim-posts-download-here',
        'du-admissions-2022-to-be-based-on-cuet-scores-class-12th-best-of-four-marks-to-be-used-for-tie-breaking',
        'jee-main-2022-trends-on-twitter-with-jeestudentswantjustice-aspirants-demand-postponement-of-session-2',
        'ap-inter-results-2022-live-news-coverage',
        'staff-selection-commission-delayed-release-of-ssc-cgl-notification-2022-for-group-b-and-group-c-posts',
        'indian-coast-guard-2023-notification-for-assistant-commandant-released-for-02-2023-batch-apply-online-from-aug-17',
        'ugc-opens-applications-for-fellowships-research-grantrs-launched-on-teachers-day-apply-till-october-10',
        'delhi-government-launches-second-delhi-skill-and-entrepreneurship-university-lighthouse-centre-details-here',
        'up-scholarship-2022-online-application-dates-with-deadlines-for-post-matric-students-announced-details-here',
        'jkbose-10th-result-2022-for-kashmir-division-announced-at-jkbose-ac-in-direct-link-to-download',
        'tn-board-results-2022-tamil-nadu-class-11th-12th-marksheets-to-be-released-today-at-tnresults-nic-in',
        'nmc-recommended-to-rethink-madras-hc-order-of-reserving-50-private-seats-for-same-tuition-fee-as-govt-colleges',
        'up-board-classes-9-11-admissions-2022-application-process-ends-on-sep-10-correction-starts-on-sep-16',
    ];

    public function actionImport404UrlContent()
    {
        if (empty(self::$emptyUrls)) {
            return [];
        }

        foreach (self::$emptyUrls as $url) {
            $news = (new NewsService())->getBySlug($url);

            if (empty($news)) {
                continue;
            }
            $model = NewsContent::find()->where(['news_id' => $news->id])->one();
            $metaData = isset($news->yoast_head) ? ContentHelper::parseMetaTags($news->yoast_head) : '';
            $author = isset($news->_embedded->author) ? ($news->_embedded->author[0] ? $news->_embedded->author[0]->slug : '') : '';

            if (!$model) {
                $model = new NewsContent();
            }

            $model->news_id = $news->id ?? '';
            $model->author_id = $this->getAuthor($author) ?? '';
            $model->content = isset($news->content) ? html_entity_decode($news->content->rendered) : '';
            $model->meta_title = $metaData['title'] ?? '';
            $model->meta_description = $metaData['description'] ?? '';
            $model->h1 = isset($news->title->rendered) ? $news->title->rendered : '';
            $model->created_at = $news->date ?? new \yii\db\Expression('NOW()');
            $model->updated_at = $news->modified ?? new \yii\db\Expression('NOW()');
            $model->status = NewsContent::STATUS_ACTIVE;

            try {
                $model->save();
                echo "{$model->news_id} \n";
            } catch (Exception $e) {
                print_r($e->getMessage());
            }
        }
    }
    public function actionNewsDraftToAcive()
    {
        $news = News::find()->where(['status' => News::STATUS_DRAFT])->all();

        foreach ($news as $value) {
            if (empty($news) || empty($value->scheduled_at)) {
                continue;
            }
            $date = date('Y-m-d H:i');
            $newsDate = date('Y-m-d H:i', strtotime($value->scheduled_at));
            $model = News::find()->where(['id' => $value->id])->one();
            if ($date == $newsDate) {
                $model->status = News::STATUS_ACTIVE;
            } else {
                echo "{$value->scheduled_at} does not match with the current date time \n";
                continue;
            }
            try {
                $model->save();
                echo "{$model->name} \n";

                $newsContent = NewsContent::find()->where(['news_id' => $value->id])->andWhere(['status' => NewsContent::STATUS_INACTIVE])->one();
                if (empty($newsContent)) {
                    echo "News Content not found \n";
                    continue;
                }

                $newsContent->status = News::STATUS_ACTIVE;

                try {
                    $newsContent->save();
                    echo "{$newsContent->id} \n";
                } catch (Exception $e) {
                    print_r($e->getMessage());
                }
            } catch (Exception $e) {
                print_r($e->getMessage());
            }
        }
    }

    //update the published_at with wp created_at date
    public function actionUpdatePublishedAt()
    {
        $query = News::find();

        foreach ($query->batch() as $news) {
            sleep(1);
            foreach ($news as $model) {
                if (empty($model)) {
                    continue;
                }

                $newsData = (new NewsService())->getBySlug($model->slug);

                if (empty($newsData)) {
                    continue;
                }

                $model->published_at = date('Y-m-d H:i:s', strtotime($newsData->date));

                try {
                    $model->save();
                    echo "{$model->name} \t {$model->published_at} \n";
                } catch (Exception $e) {
                    print_r($e->getMessage());
                }
            }
        }
    }

    //update the empty published_at news with created_at
    public function actionUpdateDate()
    {
        $news = News::find()->where(['published_at' => null])->all();

        foreach ($news as $model) {
            if (empty($model)) {
                continue;
            }

            $model->published_at = $model->created_at ?? new \yii\db\Expression('NOW()');

            try {
                $model->save();
                echo "{$model->name} \n";
            } catch (Exception $e) {
                print_r($e->getMessage());
            }
        }
    }

    // function for News Live Schedule update
    public function actionNewsLiveUpdateScheduler()
    {
        $query = new Query();
        $result = $query->select(['id', 'title', 'status', 'scheduled_at'])
            ->from('news_subdomain_live_update')
            ->where(['status' => NewsSubdomainLiveUpdate::STATUS_INACTIVE])
            ->andWhere(['between', 'scheduled_at', Carbon::now(), Carbon::now()->addMinutes(10)])
            ->all();
        foreach ($result as $newsUpdateStatus) {
            try {
                \Yii::$app->db->createCommand('UPDATE news_subdomain_live_update SET status=:status WHERE id=:id')
                    ->bindValue(':id', $newsUpdateStatus['id'])
                    ->bindValue(':status', NewsSubdomainLiveUpdate::STATUS_ACTIVE)
                    ->execute();
                echo  $newsUpdateStatus['title'] . " is active.\n";
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
        }
    }

    // Update Article Status based on the scheduled date
    public function actionUpdateArticleStatus()
    {
        $articles = Article::find()
            ->select(['id', 'title', 'status'])
            ->where(['status' => Article::STATUS_DRAFT])
            ->andWhere(['between', 'scheduled_at', Carbon::now(), Carbon::now()->addMinutes(10)])
            ->all();

        foreach ($articles as $article) {
            try {
                $response = Yii::$app->db->createCommand()->update(
                    Article::tableName(),
                    [
                        'status' => Article::STATUS_ACTIVE,
                        'published_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ],
                    ['id' => $article->id]
                )->execute();

                echo  $article->id . ' ' . $article->title . " is active. \n";
            } catch (\Exception $e) {
                echo 'Exception occured: ' . $e->getMessage();
            }
        }
    }

    // update the live News
    public function actionUpdateLiveNewsStatus()
    {
        $news = NewsSubdomainLiveUpdate::find()
            ->select(['id','title', 'status', 'scheduled_at', 'news_id'])
            ->where(['status' => NewsSubdomainLiveUpdate::STATUS_INACTIVE])
            ->andWhere(['between', 'scheduled_at', Carbon::now()->toDateTimeString(), Carbon::now()->addMinutes(3)->toDateTimeString()])
            ->all();

        foreach ($news as $news) {
            if (empty($news->scheduled_at)) {
                continue;
            }
            try {
                $response = Yii::$app->db->createCommand()->update(
                    NewsSubdomainLiveUpdate::tableName(),
                    [
                        'status' => NewsSubdomainLiveUpdate::STATUS_ACTIVE,
                        'updated_at' => $news->scheduled_at
                    ],
                    ['id' => $news->id]
                )->execute();

                (new SitemapEvent())->updateNewsUpdateXml($news->news_id, NewsSubdomainLiveUpdate::STATUS_ACTIVE, $news->scheduled_at, $news->scheduled_at);

                echo  $news->id . ' ' . $news->title . " is active. \n";
            } catch (\Exception $e) {
                echo 'Exception occured: ' . $e->getMessage();
            }
        }
    }
}
