<?php

namespace console\controllers;

use common\helpers\ConsoleHelper;
use common\models\old\CollegesRankSource;
use common\models\old\CollegesRankDomain;
use common\models\CollegeRankingPublisher;
use common\models\CollegeRankings;
use common\models\College;
use common\models\old\CollegesRank;
use common\models\OtherRankings;
use Yii;
use yii\console\Controller;
use common\models\CollegeContent;
use common\models\Stream;

class CollegeRankImportController extends Controller
{

    protected static $entity = [
        'course' => 'course',
        'stream' => 'stream',
        'specialization' => 'specialization_new',
        'other' => 'other_rankings'
    ];

    protected static $fields = [
        'course' => ['id', 'name as display_name'],
        'stream' => ['id', 'name as display_name'],
        'specialization' => ['id', 'display_name'],
        'other' => ['id', 'name as display_name']
    ];

    public function actionRank()
    {
        $allcollegeRank  = CollegesRank::find()->where(['!=', 'source', ''])->orderby('source')->all();
        foreach ($allcollegeRank as $ranks) {
            if ($ranks->source == '' || $ranks->college_id == '') {
                continue;
            }
            $collegeRankData = [];
            if ((str_contains($ranks->rank, '+') || str_contains($ranks->rank, '-')) && $ranks->source == 'qs-world-university-rankings') {
                continue;
            }
            $collegeRankData['rank'] = $ranks->rank;
            if ($ranks->domain == '') {
                $domaiName = CollegesRankDomain::find()->select(['slug'])->where(['id' => $ranks->domain_id])->one();
                $criteriaData = self::$streamMapping[$domaiName->slug];
            } else {
                $criteriaData = self::$streamMapping[$ranks->domain];
            }
            $collegData = College::find()->select(['id', 'name', 'slug'])->where(['old_id' => $ranks->college_id])->one();
            if (isset($collegData->id)) {
                $collegeRankData['college_id'] = $collegData->id;
            } else {
                continue;
                $collegeRankData['college_id'] = $ranks->college_id;
            }
            $source = CollegeRankingPublisher::find()->select(['id', 'name'])
                ->where(['slug' => $ranks->source])
                ->one();
            if ($criteriaData['type'] == CollegeRankings::CRITERIA_STREAM) {
                $collegeRankData['criteria_id'] = $criteriaData['id'];
                $collegeRankData['criteria'] = CollegeRankings::CRITERIA_STREAM;
            } elseif ($criteriaData['type'] == CollegeRankings::CRITERIA_COURSE) {
                $collegeRankData['criteria_id'] = $criteriaData['id'];
                $collegeRankData['criteria'] = CollegeRankings::CRITERIA_COURSE;
            } elseif ($criteriaData['type'] == CollegeRankings::CRITERIA_OTHER) {
                $otherRankRecord =  OtherRankings::find()->select(['id'])
                    ->where(['slug' => $criteriaData['slug']])
                    ->andWhere(['publisher_id' =>  $source->id])
                    ->one();
                if (!empty($otherRankRecord)) {
                    $collegeRankData['criteria_id'] = $criteriaData['id'];
                } else {
                    $otherRanking = new OtherRankings();
                    $otherRanking->name = ucfirst($criteriaData['slug']);
                    $otherRanking->publisher_id = $source->id;
                    $otherRanking->slug = $criteriaData['slug'];
                    $otherRanking->created_at = date('Y-m-d H:i:s');
                    $otherRanking->updated_at = date('Y-m-d H:i:s');
                    $otherRanking->save();
                    $collegeRankData['criteria_id'] = $otherRanking->id;
                }
                $collegeRankData['criteria'] = CollegeRankings::CRITERIA_OTHER;
            }

            $collegeRankData['status'] = CollegeRankings::STATUS_ACTIVE;

            $collegeRankData['year'] = self::$year[$ranks->source];
            $collegeRankData['publisher_id'] = $source->id;
            $model = new CollegeRankings();
            $model->scenario = CollegeRankings::SCENARIO_IMPORTER;
            $model->college_id = (int)$collegeRankData['college_id'];
            $model->publisher_id = (int)$collegeRankData['publisher_id'];
            $model->criteria_id = $collegeRankData['criteria_id'];
            $model->criteria = $collegeRankData['criteria'];
            $model->rank = $collegeRankData['rank'];
            $model->year = $collegeRankData['year'];
            $model->status = $collegeRankData['status'];
            if ($model->save()) {
                echo "{$ranks->id} \t {$model->publisher_id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }
    protected static $streamMapping = [
        'architecture' => ['id' => 3, 'type' => 'stream', 'slug' => 'architecture'],
        'pharmacy' => ['id' => 18, 'type' => 'stream', 'slug' => 'pharmacy'],
        'dental' => ['id' => 8, 'type' => 'stream', 'slug' => 'dental'],
        'arts' => ['id' => 4, 'type' => 'stream', 'slug' => 'arts'],
        'commerce' => ['id' => 6, 'type' => 'stream', 'slug' => 'commerce'],
        'engineering' => ['id' => 11, 'type' => 'stream', 'slug' => 'engineering'],
        'fashion' => ['id' => 408, 'type' => 'course', 'slug' => 'fashion-design'],
        'hospitality' => ['id' => 12, 'type' => 'stream', 'slug' => 'hotel-management'],
        'law' => ['id' => 13, 'type' => 'stream', 'slug' => 'law'],
        'mba' => ['id' => 95, 'type' => 'course', 'slug' => 'mba'],
        'media' => ['id' => 15, 'type' => 'stream', 'slug' => 'mass-communication'],
        'medical' => ['id' => 16, 'type' => 'stream', 'slug' => 'medical'],
        'science' => ['id' => 19, 'type' => 'stream', 'slug' => 'science'],
        'bba' => ['id' => 8, 'type' => 'course', 'slug' => 'bba'],
        'b-school' => ['id' => 2, 'type' => 'other', 'slug' => 'b-school'],
        'university' => ['id' => 3, 'type' => 'other', 'slug' => 'university']
    ];
    protected static $year = [
        'business-today' => '2019',
        'nirf' => '2023',
        'economic-times' => '2019',
        'india-today' => '2022',
        'outlook' => '2020',
        'silicon-india' => '2019',
        'the-week' => '2021',
        'times-b' => '2021',
        'times-group' => '2021',
        'qs-world-university-rankings' => '2023',
    ];
    public function actionRankPanelData()
    {
        $allDomainData = CollegesRank::find()->where(['=', 'domain', ''])
            ->orWhere(['=', 'source', ''])
            ->all();
        $i = 0;
        foreach ($allDomainData as $domain) {
            if ($domain->domain == '') {
                $domaiName = CollegesRankDomain::find()->where(['id' => $domain->domain_id])->one();
                $domain->domain =  $domaiName->slug;
                echo "{$domain->id} \t {$domaiName->slug} \n";
            }
            if ($domain->source == '') {
                $source = CollegesRankSource::find()->where(['id' => $domain->source_id])->one();
                $domain->source =  $source->slug;
                echo "{$domain->id} \t {$source->slug} \n";
            }
            $domain->update(false);
            $i++;
        }
        echo $i;
    }


    public function actionCreateCollegeRankPage()
    {

        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college-rank-page.csv';
        $fileUpload = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college-rank-file.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $data['col'][] = 'publisher_slug';
        $data['col'][] = 'college_slug';
        $data['col'][] = 'criteria';
        $data['col'][] = 'criteria_value';
        $data['col'][] = 'rank';
        $data['col'][] = 'year';
        $data['col'][] = 'status';
        $i = 0;
        //ob_clean();
       
        $fp = fopen($fileUpload, 'w');
        fputcsv($fp, array_values($data['col']));
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if ($fileop[6]==0) {
                continue;
            }
            $collegeContent = CollegeContent::find()
                        ->where(['entity_id'=>$fileop[0]])
                        ->andWhere(['sub_page'=>'ranking'])
                        ->andWhere(['entity'=>'college'])
                        ->exists();
            $rankPublisher = CollegeRankingPublisher::find()
                        ->select(['slug'])
                        ->where(['id'=>$fileop[9]])
                        ->one();
             $collegeSlug = College::find()
                       ->select(['slug'])
                        ->where(['id'=>$fileop[0]])
                        ->one();
            if (empty($collegeSlug)) {
                continue;
            }
             $streamSlug = Stream::find()
                        ->select(['slug','id'])
                         ->where(['id'=>$fileop[8]])
                         ->one();
            $data['row'][$i] =  [$rankPublisher->slug,$collegeSlug->slug,'stream',$streamSlug->slug,$fileop[6],$fileop[7],1];
            fputcsv($fp, $data['row'][$i]);
            if (!$collegeContent) {
                $rankPage = new CollegeContent();
                $rankPage->entity_id = $fileop[0];
                $rankPage->entity = 'college';
                $rankPage->sub_page = 'ranking';
                $rankPage->status = CollegeContent::STATUS_ACTIVE;
                $rankPage->author_id = 1;
                $rankPage->save();
            }
        }
        fclose($fp);
        fclose($handle);
    }
  
   /* Import College Rank from file*/

    public function actionImportCollegeRank()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college-rank-file.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if (isset($fileop[0]) && $fileop[0] != '') {
                $publisherID = CollegeRankingPublisher::find()->select(['id'])->where(['slug' => $fileop[0]])->one();
                $collegeID = College::find()->select(['id'])->where(['slug' => $fileop[1]])->one();
                $criteriaId = null;
                $where = [];
                if (isset($fileop[2])  &&  $fileop[2] != '') {
                    $where['slug'] = $fileop[3];
                    if ($fileop[2] == CollegeRankings::CRITERIA_OTHER) {
                        $where['publisher_id'] = $publisherID->id;
                    }
                    $model = self::$entity[$fileop[2]];
                    if (isset($model) && $model != '') {
                        list($id) = self::$fields[$fileop[2]];
                    }
                    $data = (new \yii\db\Query())
                        ->select([$id])
                        ->from($model)
                        ->where($where)
                        ->one();
                       
                    if (!empty($data)) {
                        $criteriaId = $data['id'];
                    } else {
                        if ($fileop[2] == CollegeRankings::CRITERIA_OTHER) {
                            $rankModel = new OtherRankings();
                            $rankModel->publisher_id = $publisherID->id;
                            $rankModel->name = ucfirst($fileop[3]);
                            $rankModel->slug = $fileop[3];
                            $rankModel->save();
                            $criteriaId = $rankModel->id;
                        }
                    }
                } else {
                    continue;
                }
                   
                $this->saveORUpdateRank(
                    $publisherID->id,
                    $collegeID->id,
                    $fileop[2],
                    $criteriaId,
                    $fileop[4],
                    $fileop[5],
                    $fileop[6],
                    $id = ''
                );
            }
        }
    }

     /*
        Save OR Update Rank Panel from file
    */
    public function saveORUpdateRank(
        $publisherID,
        $collegeID,
        $criteria,
        $criteria_id,
        $rank,
        $year,
        $status,
        $id
    ) {
        if ($id != '') {
            $checkDuplicate = CollegeRankings::find()->where(['id' => $id])->one();
        } else {
            $checkDuplicate = CollegeRankings::find()->where(['publisher_id' =>  $publisherID])
                ->andWhere(['college_id' => $collegeID])
                ->andWhere(['criteria' => $criteria])
                ->andWhere(['year' => $year])
                ->andWhere(['criteria_id' => $criteria_id])->one();
        }
        if (!empty($checkDuplicate)) {
            $checkDuplicate->college_id = $collegeID;
            $checkDuplicate->publisher_id =  $publisherID;
            $checkDuplicate->criteria = $criteria;
            $checkDuplicate->criteria_id = $criteria_id;
            $checkDuplicate->rank = $rank;
            $checkDuplicate->year = $year;
            if ($status == CollegeRankings::STATUS_ACTIVE) {
                $checkDuplicate->status = CollegeRankings::STATUS_ACTIVE;
            } else {
                $checkDuplicate->status = CollegeRankings::STATUS_INACTIVE;
            }

            if ($checkDuplicate->update()) {
                return true;
            } else {
                return false;
            }
        } else {
            $model = new CollegeRankings();
            $model->scenario = CollegeRankings::SCENARIO_IMPORTER;
            $model->college_id = $collegeID;
            $model->publisher_id = $publisherID;
            $model->criteria = $criteria;
            $model->criteria_id =  $criteria_id;
            $model->rank =  $rank;
            $model->year = $year;
            if ($status == CollegeRankings::STATUS_ACTIVE) {
                $model->status = CollegeRankings::STATUS_ACTIVE;
            } else {
                $model->status = CollegeRankings::STATUS_INACTIVE;
            }
            if ($model->save()) {
                return true;
            } else {
                return false;
            }
        }
    }
}
