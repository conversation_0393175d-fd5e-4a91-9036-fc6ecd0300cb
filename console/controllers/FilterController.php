<?php

namespace console\controllers;

use common\event\CollegeFilterEvent;
use common\event\modelFilterEvent;
use common\helpers\CollegeHelper;
use common\models\City;
use common\models\College;
use common\models\CollegeCourse;
use common\models\Course;
use common\models\CutOff;
use common\models\documents\College as DocumentsCollege;
use common\models\Exam;
use common\models\Feature;
use common\models\FeatureGroup;
use common\models\FilterGroup;
use common\models\Filter;
use common\models\FilterPageSeo;
use common\models\Specialization;
use common\models\CollegeProgram;
use common\models\Program;
use common\models\ProgramCourseMapping;
use common\models\State;
use common\models\Stream;
use common\services\CollegeService;
use Exception;
use Yii;
use yii\console\Controller;
use yii\db\Query;
use yii\helpers\Inflector;
use MatthiasMullie\Minify;

class FilterController extends Controller
{
    protected static $filterGroups = [
        'state' => ['name' => 'State', 'position' => 1, 'value_type' => 'radio', 'url_position' => 2, 'mappedField' => 'state', 'is_url_param' => true, 'rule' => ['hasChild' => true, 'child' => 'city', 'priority' => 2]],
        'city' => ['name' => 'City', 'position' => 2, 'value_type' => 'checkbox', 'url_position' => 2, 'mappedField' => 'city', 'is_url_param' => true, 'rule' => ['hasChild' => false, 'child' => '', 'priority' => 1]],
        'stream' => ['name' => 'Streams', 'position' => 3, 'value_type' => 'radio', 'url_position' => 1, 'mappedField' => 'stream', 'is_url_param' => true, 'rule' => ['hasChild' => true, 'child' => 'course', 'priority' => 2]],
        'course' => ['name' => 'Courses', 'position' => 4, 'value_type' => 'radio', 'url_position' => 1, 'mappedField' => 'course', 'is_url_param' => true, 'rule' => ['hasChild' => true, 'child' => 'specialization', 'priority' => 1]],
        'specialization' => ['name' => 'Specialization', 'position' => 5, 'value_type' => 'radio', 'url_position' => 1, 'mappedField' => 'specialization', 'is_url_param' => true, 'rule' => ['hasChild' => false, 'child' => '', 'priority' => 1]],
        'mode' => ['name' => 'Program Mode', 'position' => 6, 'value_type' => 'checkbox', 'url_position' => 0, 'mappedField' => 'mode', 'is_url_param' => false, 'rule' => ['hasChild' => false, 'child' => '', 'priority' => 0]],
        'ownership' => ['name' => 'Ownership', 'position' => 7, 'value_type' => 'radio', 'url_position' => 1, 'mappedField' => 'ownership', 'is_url_param' => true, 'rule' => ['hasChild' => false, 'child' => '', 'priority' => 1]],
        'exam' => ['name' => 'Exams Accepted', 'position' => 8, 'value_type' => 'checkbox', 'url_position' => 1, 'mappedField' => 'exam', 'is_url_param' => true, 'rule' => ['hasChild' => false, 'child' => '', 'priority' => 1]],
        'courseType' => ['name' => 'Course Type', 'position' => 9, 'value_type' => 'checkbox', 'url_position' => 0, 'mappedField' => 'course_type', 'is_url_param' => false, 'rule' => ['hasChild' => false, 'child' => '', 'priority' => 0]],
        'affiliatedBy' => ['name' => 'Affiliated By', 'position' => 10, 'value_type' => 'checkbox', 'url_position' => 0, 'mappedField' => 'affiliated_by', 'is_url_param' => false, 'rule' => ['hasChild' => false, 'child' => '', 'priority' => 0]],
        'totalFees' => ['name' => 'Total Fees', 'position' => 11, 'value_type' => 'checkbox', 'url_position' => 0, 'mappedField' => 'fees', 'is_url_param' => false, 'rule' => ['hasChild' => false, 'child' => '', 'priority' => 0]],
        'approvals' => ['name' => 'Approvals', 'position' => 12, 'value_type' => 'checkbox', 'url_position' => 0, 'mappedField' => 'approvals', 'is_url_param' => false, 'rule' => ['hasChild' => false, 'child' => '', 'priority' => 0]],
    ];

    public static $feesRange = [
        '0-100000',
        '100001-200000',
        '200001-300000',
        '300001-500000',
        '500001',
    ];

    public function actionFilterGroup()
    {
        foreach (self::$filterGroups as $name => $value) {
            $model = FilterGroup::find()
                ->where(['name' => $value['name']])
                // ->andWhere(['position' => $value['position']])
                // ->andWhere(['value_type' => $value['value_type']])
                ->one();

            if (!$model) {
                $model = new FilterGroup();
            }

            $model->name = $value['name'];
            $model->description = null;
            $model->position = $value['position'];
            $model->url_position = $value['url_position'];
            $model->mapped_field = $value['mappedField'];
            $model->is_url_param = $value['is_url_param'];
            $model->rule = $value['rule'];
            $model->value_type = $value['value_type'];
            $model->status = FilterGroup::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionAll()
    {
        $this->actionFilterGroup();
        $this->actionState();
        $this->actionCity();
        $this->actionStream();
        $this->actionCourse();
        $this->actionSpecialization();
        $this->actionProgramMode();
        $this->actionOwnerShip();
        $this->actionExam();
        $this->actionCourseType();
        $this->actionAffiliatedBy();
        $this->actionTotalFees();
        $this->actionApprovals();
    }

    public function actionState()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'State'])->one();
        if (!$filterGroup) {
            echo 'filter group state does not exist';
            return;
        }

        $query = State::find();

        foreach ($query->batch() as $states) {
            foreach ($states as $state) {
                $model = Filter::find()
                    ->where(['filter_group_id' => $filterGroup->id])
                    ->andWhere(['slug' => $state->slug])
                    ->one();

                if (!$model) {
                    $model = new Filter();
                }

                $model->filter_group_id  = $filterGroup->id;
                $model->parent_id  = null;
                $model->name  = $state->name;
                $model->seo_title  = null;
                $model->slug  = $state->slug;
                $model->rule  = null;
                $model->value  = null;
                $model->type  = 'Checkbox';
                $model->position  = 0;
                $model->status  = Filter::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->name} \t {$model->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionCity()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'City'])->one();
        if (!$filterGroup) {
            echo 'filter group city does not exist';
            return;
        }

        $query = City::find();

        foreach ($query->batch() as $cities) {
            foreach ($cities as $city) {
                $checkCollegeCity = College::find()->where(['city_id' => $city->id])->one();
                if (!$checkCollegeCity) {
                    continue;
                }
                $parentId = Filter::find()->where(['slug' => $city->state->slug])->one();
                if (!$parentId) {
                    continue;
                }
                $model = Filter::find()
                    ->where(['filter_group_id' => $filterGroup->id])
                    ->andWhere(['slug' => $city->slug])
                    ->one();

                if (!$model) {
                    $model = new Filter();
                }

                $model->filter_group_id  = $filterGroup->id;
                $model->parent_id  = $parentId->id;
                $model->name  = $city->name;
                $model->seo_title  = null;
                $model->slug  = $city->slug;
                $model->rule  = null;
                $model->value  = null;
                $model->type  = 'Checkbox';
                $model->position  = $city->position;
                $model->status  = Filter::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->name} \t {$model->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionStream()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Streams'])->one();
        if (!$filterGroup) {
            echo 'filter group stream does not exist';
            return;
        }

        $query = Stream::find();

        foreach ($query->batch() as $streams) {
            foreach ($streams as $stream) {
                $model = Filter::find()
                    ->where(['filter_group_id' => $filterGroup->id])
                    ->andWhere(['slug' => $stream->slug])
                    ->one();

                if (!$model) {
                    $model = new Filter();
                }

                $model->filter_group_id  = $filterGroup->id;
                $model->parent_id  = null;
                $model->name  = $stream->name;
                $model->seo_title  = null;
                $model->slug  = $stream->slug;
                $model->rule  = null;
                $model->value  = null;
                $model->type  = 'Checkbox';
                $model->position  = 0;
                $model->status  = Filter::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->name} \t {$model->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionCourse()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Courses'])->one();
        if (!$filterGroup) {
            echo 'filter group courses does not exist';
            return;
        }

        $query = Course::find()->where(['parent_id' => null]);
            // ->where(['parent_id' => null])->andWhere(['not', ['id' => Course::EXCLUDE_PHD]]);

        foreach ($query->batch() as $courses) {
            foreach ($courses as $course) {
                $model = Filter::find()
                    ->where(['filter_group_id' => $filterGroup->id])
                    ->andWhere(['slug' => $course->slug])
                    ->one();

                if (!$model) {
                    $model = new Filter();
                }

                $oldQuery = new Query();
                $oldQuery->select(['filter.id'])
                    ->from(['filter as filter'])
                    ->leftJoin('stream as stream', 'filter.slug = stream.slug')
                    ->leftJoin('course as course', 'course.stream_id = stream.id')
                    ->where(['course.slug' => $course->slug]);
                $streamId = $oldQuery->one();

                if (!$streamId) {
                    continue;
                }

                $model->filter_group_id  = $filterGroup->id;
                $model->parent_id  = $streamId['id'];
                $model->name  = $course->short_name;
                $model->seo_title  = null;
                $model->slug  = $course->slug;
                $model->rule  = null;
                $model->value  = null;
                $model->type  = 'Checkbox';
                $model->position  = 0;
                $model->status  = Filter::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->name} \t {$model->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionProgramMode()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Program Mode'])->one();
        if (!$filterGroup) {
            echo 'filter group program mode does not exist';
            return;
        }

        $collegeMode = CollegeCourse::find()->select(['mode'])->distinct()->all();

        foreach ($collegeMode as $type) {
            $model = Filter::find()
                ->where(['filter_group_id' => $filterGroup->id])
                ->andWhere(['slug' => $type->mode])
                ->one();

            if (!$model) {
                $model = new Filter();
            }

            $model->filter_group_id  = $filterGroup->id;
            $model->parent_id  = null;
            if ($type->mode == 'offline') {
                $model->name  = 'Offline';
            } elseif ($type->mode == 'online') {
                $model->name  = 'Online';
            } else {
                $model->name  = 'Distance Education';
            }
            $model->seo_title  = null;
            $model->slug  = $type->mode;
            $model->rule  = null;
            $model->value  = null;
            $model->type  = 'Checkbox';
            $model->position  = 0;
            $model->status  = Filter::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->name} \t {$model->slug} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionExam()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Exams Accepted'])->one();
        if (!$filterGroup) {
            echo 'filter group exam accepted does not exist';
            return;
        }

        $query = Exam::find();

        foreach ($query->batch() as $exams) {
            foreach ($exams as $exam) {
                $model = Filter::find()
                    ->where(['filter_group_id' => $filterGroup->id])
                    ->andWhere(['slug' => $exam->slug])
                    ->one();

                if (!$model) {
                    $model = new Filter();
                }

                $model->filter_group_id  = $filterGroup->id;
                $model->parent_id  = null;
                $model->name  = $exam->display_name;
                $model->seo_title  = null;
                $model->slug  = $exam->slug;
                $model->rule  = null;
                $model->value  = null;
                $model->type  = 'Checkbox';
                $model->position  = 0;
                $model->status  = Filter::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->name} \t {$model->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionAffiliatedBy()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Affiliated By'])->one();
        if (!$filterGroup) {
            echo 'filter group affiliated by does not exist';
            return;
        }

        $query = College::find()
            ->where(['type' => College::TYPE_UNIVERSITY]);

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                $model = Filter::find()
                    ->where(['filter_group_id' => $filterGroup->id])
                    ->andWhere(['slug' => $college->slug])
                    ->one();

                if (!$model) {
                    $model = new Filter();
                }

                $model->filter_group_id  = $filterGroup->id;
                $model->parent_id  = null;
                $model->name  = $college->name;
                $model->seo_title  = null;
                $model->slug  = $college->slug;
                $model->rule  = null;
                $model->value  = null;
                $model->type  = 'Checkbox';
                $model->position  = 0;
                $model->status  = Filter::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->name} \t {$model->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionOwnerShip()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Ownership'])->one();
        if (!$filterGroup) {
            echo 'filter group Ownership does not exist';
            return;
        }

        $filters = ['Public', 'Private'];

        foreach ($filters as $filter) {
            $model = Filter::find()->where(['slug' => Inflector::slug($filter)])->one();
            if (!$model) {
                $model = new Filter();
            }

            $model->filter_group_id = $filterGroup->id;
            $model->name = $model->name ?? $filter;
            $model->slug = $model->slug ?? Inflector::slug($filter);
            $model->type = 'checkbox';
            $model->position = 0;
            $model->status  = Filter::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->name} has been added. \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionCourseType()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Course Type'])->one();
        if (!$filterGroup) {
            echo 'filter group Ownership does not exist';
            return;
        }

        $degrees = CollegeCourse::find()->select('degree')->groupBy('degree')->asArray()->all();

        $filters = array_column($degrees, 'degree');


        foreach ($filters as $filter) {
            $model = Filter::find()->where(['slug' => Inflector::slug($filter)])->one();
            if (!$model) {
                $model = new Filter();
            }

            $model->filter_group_id = $filterGroup->id;
            $model->name = $model->name ?? ucwords(str_replace('-', ' ', $filter));
            $model->slug = $model->slug ?? Inflector::slug($filter);
            $model->type = 'checkbox';
            $model->position = 0;
            $model->status  = Filter::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->name} degree has been added. \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionTotalFees()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Total Fees'])->one();
        if (!$filterGroup) {
            echo 'filter group Total Fees does not exist';
            return;
        }

        $filters = [
            '0-100000' => 'Less than 1 Lakh',
            '100001-200000' => '1 to 2 Lakhs',
            '200001-300000' => '2 to 3 Lakhs',
            '300001-500000' => '3 to 5 Lakhs',
            '500001' => 'More than 5 Lakhs',
        ];

        foreach ($filters as $key => $value) {
            $key = (string) $key;
            $model = Filter::find()->where(['slug' => $key])->one();
            if (!$model) {
                $model = new Filter();
            }

            $model->filter_group_id = $filterGroup->id;
            $model->name = $model->name ?? $value;
            $model->slug = $model->slug ?? $key;
            $model->type = 'checkbox';
            $model->position = 0;
            $model->status  = Filter::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->name} filter has been added. \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    // approvals
    public function actionApprovals()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Approvals'])->one();
        if (!$filterGroup) {
            echo 'filter group Approvals does not exist';
            return;
        }

        $featureGroup = FeatureGroup::find()->where(['slug' => 'approvals'])->one();
        if (!$featureGroup) {
            echo 'approvals feature group not found';
            return;
        }

        $filters = Feature::find()
            ->select('name, slug')
            ->where(['feature_group_id' => $featureGroup->id])
            ->groupBy('slug')
            ->active()
            ->asArray()
            ->all();

        foreach ($filters as $filter) {
            $model = Filter::find()->where(['slug' => $filter['slug']])->one();
            if (!$model) {
                $model = new Filter();
            }

            $model->filter_group_id = $filterGroup->id;
            $model->name = $model->name ?? $filter['name'];
            $model->slug = $model->slug ?? $filter['slug'];
            $model->type = 'checkbox';
            $model->position = 0;
            $model->status  = Filter::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->name} approval has been added. \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    // College import to mongodb
    // each college one review, over all rating,
    // placement: boolean, cutoff: boolean, admissions: boolean
    public function actionImportCollege()
    {
        $queries = College::find()->active();
        foreach ($queries->batch() as $colleges) {
            foreach ($colleges as $college) {
                (new CollegeFilterEvent())->updateCollegeFilter($college);
            }
        }
    }

    public function actionSearchPageContent()
    {
        $items = [];
        $query = (new Query())->from('gmu_search_pages_content');
        foreach ($query->batch(100, Yii::$app->gmudb) as $searchPageContents) {
            foreach ($searchPageContents as $searchPageContent) {
                if ($searchPageContent['discipline'] != null) {
                    if ($searchPageContent['discipline'] == 'business-management') {
                        $slug = 'management-colleges';
                        $h1 =  'Top Management Colleges in India';
                    } else {
                        $slug = $searchPageContent['discipline'] . '-colleges';
                        $h1 =  'Top ' . ucwords(str_replace('-', ' ', $searchPageContent['discipline'])) . ' Colleges in India';
                    }
                    if ($searchPageContent['location_id'] != 999) {
                        $location = $this->getLocationById($searchPageContent['location_id']);
                        if (!empty($location)) {
                            if ($searchPageContent['discipline'] == 'business-management') {
                                $slug = 'management-colleges/' . $location['city_slug'];
                                $h1 = 'Top Management Colleges in ' . ucwords($location['city_slug']);
                            } else {
                                $slug .= '/' . $location['city_slug'];
                                $h1 = 'Top ' . ucwords(str_replace('-', ' ', $searchPageContent['discipline'])) . ' Colleges in ' . $location['city_slug'];
                            }
                        }
                    }
                    $items['slug'] = $slug;
                    $items['h1'] = $h1;
                    $items['content'] = $searchPageContent['content'];
                    $items['status'] = FilterPageSeo::STATUS_ACTIVE;
                } else if ($searchPageContent['course_short_vanity'] != null) {
                    $slug = $searchPageContent['course_short_vanity'] . '-colleges';
                    // $h1 = 'Top ' . ucwords(str_replace('-', ' ', $searchPageContent['course_short_vanity'])) . ' Colleges in India';
                    $h1 = '';
                    if ($searchPageContent['location_id'] != 999) {
                        $location = $this->getLocationById($searchPageContent['location_id']);
                        if (!empty($location)) {
                            $slug .= '/' . $location['city_slug'];
                            $h1 = 'Top ' . ucwords(str_replace('-', ' ', $searchPageContent['course_short_vanity'])) . ' Colleges in ' . $location['city_slug'];
                        }
                    }

                    $items['slug'] = $slug;
                    $items['h1'] = $h1;
                    $items['content'] = $searchPageContent['content'];
                    $items['status'] = FilterPageSeo::STATUS_ACTIVE;
                }

                $model = FilterPageSeo::find()->where(['slug' => $items['slug']])->one();
                if (!$model) {
                    $model = new FilterPageSeo();
                }

                $model->entity = 'college-search';
                $model->slug = $items['slug'];
                $model->content = htmlspecialchars_decode($items['content']);
                // $model->h1 = $items['h1'];
                $model->status = $items['status'];

                if ($model->save()) {
                    echo "{$model->id} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function getLocationById($locationId)
    {
        $location = (new Query())->from('gmu_city_state')->where(['id' => $locationId])->one(Yii::$app->gmudb);
        if (empty($location)) {
            return [];
        }

        return $location;
    }

    public function actionUpdateCollegePosition()
    {
        $query = College::find();
        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                $collection = \common\models\documents\College::find()->where(['college_id' => $college->id])->one();
                if (!$collection) {
                    print_r('college not found ' . $college->name . ' ' . $college->id);
                    continue;
                }

                if ($college->position) {
                    $collection->position = $college->position;
                } else {
                    $collection->position = 999999;
                }

                if ($collection->save()) {
                    echo "Position updated for {$college->name} || {$college->position} \n";
                } else {
                    print_r($collection->getErrors());
                }
            }
        }
    }

    public function actionUpdateCityPosition()
    {

        $cityAll = City::updateAll(['position' => 999999]);

        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cities_positions_mapping.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[2])) {
                $i++;
                continue;
            }

            $model = City::find()->where(['slug' => $fileop[2]])->one();
            if (!$model) {
                continue;
            }

            $model->position = ((int) $fileop[0] ?? 999999);
            if ($model->save()) {
                echo "{$model->id} \t {$model->slug} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateCourseStreamId()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_stream_id.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[2])) {
                $i++;
                continue;
            }

            $model = Course::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                $notMapped[] = "$fileop[3] \t $fileop[4]";
                continue;
            }

            $model->stream_id = (int) $fileop[2];

            if ($model->save()) {
                echo "{$model->stream_id} \t {$model->slug} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateCollegeCourseId()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_course_course_id_new.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[2])) {
                $i++;
                continue;
            }

            $model = CollegeCourse::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                $notMapped[] = "$fileop[4] \t $fileop[5]";
                continue;
            }

            $model->course_id = (int) $fileop[2];
            $model->specialization_id = !empty((int)$fileop[3]) ? $fileop[3] : null;

            if ($model->save()) {
                echo "{$model->course_id} \t {$model->slug} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionGenerateCache()
    {
        $baseUrl = 'https://frontend-gmu-three.schl.in';
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college-listing-page-urls.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $errorUrls = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            $i++;
            $url = $baseUrl . $fileop[0];

            try {
                file_get_contents($url);
            } catch (Exception $e) {
                $errorUrls[] = $url;
                echo $url . "\n";
            }
        }
        $this->notifyThroughMail($errorUrls);
    }

    public function notifyThroughMail($urls)
    {
        try {
            Yii::$app->mailer->compose('@backend/views/mail/cache-generate-urls', [
                'urls' => $urls
            ])
                ->setFrom(['<EMAIL>' => 'Getmyuni Cache Generation'])
                ->setTo('<EMAIL>')
                ->setSubject('Cache Generate errors')
                ->send();
        } catch (Exception $e) {
            Yii::error($e->getMessage(), 'Notification Error');
        }
    }


    public function actionUpdateSponsored()
    {
        $query = DocumentsCollege::find();

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                if (!$college) {
                    continue;
                }

                $sponsoredCollege = College::find()->where(['id' => $college->college_id])->one();

                if (!$sponsoredCollege) {
                    continue;
                }

                $college->is_sponsored = $sponsoredCollege->is_sponsored ?? '';

                try {
                    if ($college->save()) {
                        echo "{$college->name} \t {$college->is_sponsored} \n";
                    } else {
                        print_r($college->getErrors());
                    }
                } catch (\Exception $e) {
                    echo $e->getMessage();
                }
            }
        }
    }

    public function actionSpecialization()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Specialization'])->one();
        if (!$filterGroup) {
            echo 'filter group specialization does not exist';
            return;
        }

        $query = CollegeProgram::find();

        foreach ($query->batch() as $programs) {
            foreach ($programs as $program) {
                $collegeProgram = ProgramCourseMapping::find()->where(['program_id' => $program->program_id])->one();

                if (empty($collegeProgram)) {
                    continue;
                }

                if ($collegeProgram->specialization_id == null) {
                    continue;
                }

                if (!$collegeProgram->course->stream) {
                    continue;
                }

                $parent = Filter::find()->where(['slug' => $collegeProgram->course->stream->slug])->one();
                if (!$parent) {
                    continue;
                }

                if ($collegeProgram->program->is_hons == 1) {
                    if (str_contains($collegeProgram->course->slug, '-hons')) {
                        $arr = explode('-', $collegeProgram->course->slug);
                        $filterSlug = $arr[0] . '-' . $collegeProgram->specialization->slug . '-hons';
                        $filterName = $collegeProgram->course->short_name . ' ' . $collegeProgram->specialization->display_name . ' Hons';
                    } else {
                        $filterSlug = $collegeProgram->course->slug . '-' . $collegeProgram->specialization->slug . '-hons';
                        $filterName = $collegeProgram->course->short_name . ' ' . $collegeProgram->specialization->display_name . ' Hons';
                    }
                } else {
                    $filterSlug = $collegeProgram->course->slug . '-' . $collegeProgram->specialization->slug;
                    $filterName = $collegeProgram->course->short_name . ' ' . $collegeProgram->specialization->display_name;
                }

                $model = Filter::find()->where(['slug' => $filterSlug])->one();
                if (!$model) {
                    $model = new Filter();
                }
                
                $model->filter_group_id = $filterGroup->id;
                $model->name = $filterName;
                // $model->name = $collegeProgram->course->short_name . ' ' . $collegeProgram->specialization->display_name;
                $model->slug = $filterSlug;
                $model->parent_id = $parent->id;
                $model->type = 'Checkbox';
                $model->status = Filter::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->id} \t {$model->name} \t {$collegeProgram->program->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    /** cut off upload */
    public function actionCutOffData()
    {
        $cutOffCategory = CollegeService::getCategory();
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cutoff_r2.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $college = College::find()->select('id')->where(['slug' => $fileop[0]])->one();
            if (!$college) {
                continue;
            }

            $exam = Exam::find()->select('id')->where(['slug' => $fileop[1]])->one();
            if (!$exam) {
                continue;
            }

            $course = Course::find()->select('id')->where(['slug' => $fileop[2]])->one();
            if (!$course) {
                continue;
            }

            if (!empty($fileop[3])) {
                $specialization = Specialization::find()->select(['id'])->where(['slug' => $fileop[3]])->one();
                if (!$specialization) {
                    continue;
                }
            }

            $newModel = new CutOff();
            $newModel->college_id = $college->id;
            $newModel->exam_id = $exam->id;
            if (isset($fileop[2])) {
                $newModel->course_id= $course->id;
            }
            if (isset($fileop[3])) {
                $newModel->specialization_id= !empty($specialization) ? $specialization->id : '';
            }

            $newModel->program_name  = $fileop[4];
            
            if (isset($fileop[5])) {
                $newModel->category = isset($cutOffCategory[$fileop[5]]) ? $cutOffCategory[$fileop[5]] : '';
            }

            if (isset($fileop[6])) {
                $newModel->gender= isset(CollegeHelper::$cutoffGender[$fileop[6]]) ? CollegeHelper::$cutoffGender[$fileop[6]] : '';
            }
            if (isset($fileop[7])) {
                $newModel->type= isset(CollegeHelper::$cutOffType[$fileop[7]]) ? CollegeHelper::$cutOffType[$fileop[7]] : '';
            }
            if (isset($fileop[8])) {
                $newModel->year = (int)$fileop[8];
            }
            if (isset($fileop[9])) {
                $newModel->round = (int)$fileop[9];
            }
            if (isset($fileop[10])) {
                $newModel->opening_rank= !empty($fileop[10]) ? (int)$fileop[10] : '';
            }
            if (isset($fileop[11])) {
                $newModel->closing_rank= !empty($fileop[11]) ? (int)$fileop[11] : '';
            }
            if (isset($fileop[12])) {
                $newModel->percentile = $fileop[12];
            }
            if (isset($fileop[13])) {
                $newModel->closing_score = $fileop[13];
            }

            if ($newModel->save()) {
                echo "{$fileop[0]} \t {$fileop[1]} \n";
            } else {
                print_r($newModel->getErrors());
            }
        }
    }

    /** cut off data with ids */
    public function actionCutOffDataWithIds()
    {
        $cutOffCategory = CollegeService::getCategory();
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cut_off.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $newModel = new CutOff();
            $newModel->college_id = $fileop[0];
            $newModel->exam_id = $fileop[1];
            $newModel->course_id= $fileop[2];
            $newModel->specialization_id= $fileop[3];
            $newModel->program_name = $fileop[4];
            
            if (isset($fileop[5])) {
                $newModel->category = isset($cutOffCategory[$fileop[5]]) ? $cutOffCategory[$fileop[5]] : '';
            }
            if (isset($fileop[6])) {
                $newModel->gender= isset(CollegeHelper::$cutoffGender[$fileop[6]]) ? CollegeHelper::$cutoffGender[$fileop[6]] : '';
            }
            if (isset($fileop[7])) {
                $newModel->type= isset(CollegeHelper::$cutOffType[$fileop[7]]) ? CollegeHelper::$cutOffType[$fileop[7]] : '';
            }
            if (isset($fileop[8])) {
                $newModel->year = (int)$fileop[8];
            }
            if (isset($fileop[9])) {
                $newModel->round = (int)$fileop[9];
            }
            if (isset($fileop[10])) {
                $newModel->opening_rank= !empty($fileop[10]) ? (int)$fileop[10] : '';
            }
            if (isset($fileop[11])) {
                $newModel->closing_rank= !empty($fileop[11]) ? (int)$fileop[11] : '';
                ;
            }
            if (isset($fileop[12])) {
                $newModel->percentile = $fileop[12];
            }
            if (isset($fileop[13])) {
                $newModel->closing_score = $fileop[13];
            }

            if ($newModel->save()) {
                echo "{$fileop[0]} \t {$fileop[1]} \n";
            } else {
                print_r($newModel->getErrors());
            }
        }
    }

    public function actionNewCourseFilterMapping()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Courses'])->one();
        if (!$filterGroup) {
            echo 'filter group courses does not exist';

            return;
        }
        $query = ProgramCourseMapping::find()->where(['specialization_id' => null]);

        foreach ($query->batch() as $programMapping) {
            foreach ($programMapping as $mapping) {
                // dd($mapping->program_id);
                $collegeProgram = Program::find()->where(['id' => $mapping->program_id])->one();
                // dd($collegeProgram);
                if (empty($collegeProgram)) {
                    continue;
                }

                // if ($collegeProgram->is_hons == 0) {
                //     continue;
                // }
                
                if (empty($mapping->course->stream)) {
                    continue;
                }

                if (!empty($mapping->specialization_id)) {
                    continue;
                }
                $parent = Filter::find()->where(['slug' => $mapping->course->stream->slug])->one();
                if (!$parent) {
                    continue;
                }

                if ($collegeProgram->is_hons == 1) {
                    $filterSlug = $mapping->course->slug . '-hons';
                    $filterName = $mapping->course->short_name . ' Hons';
                } else {
                    $filterSlug = $mapping->course->slug;
                    $filterName = $mapping->course->short_name;
                }

                $model = Filter::find()->where(['slug' => $filterSlug])->one();
                if (!$model) {
                    $model = new Filter();
                }
                
                $model->filter_group_id = $filterGroup->id;
                $model->name = $filterName;
                // $model->name = $collegeProgram->course->short_name . ' ' . $collegeProgram->specialization->display_name;
                $model->slug = $filterSlug;
                $model->parent_id = $parent->id;
                $model->type = 'Checkbox';
                $model->status = Filter::STATUS_ACTIVE;
                // dd($model);
                if ($model->save()) {
                    echo "{$model->id} \t {$model->name} \t {$mapping->program->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    // CollegePorgam import to mongodb
    public function actionImportCollegeProgram()
    {
        $queries = CollegeProgram::find();
        foreach ($queries->batch() as $programs) {
            foreach ($programs as $program) {
                if ($program->status == CollegeProgram::STATUS_INACTIVE) {
                    continue;
                }
                CollegeService::updateProgramDocument($program);
            }
        }
    }

    public function actionGenerateAssets()
    {
        self::minifyAndCopyFiles(Yii::getAlias('@frontend') . '/web/yas/css/version2/', Yii::getAlias('@frontend') . '/web/yas/css/version2/min/');
        self::minifyAndCopyFiles(Yii::getAlias('@frontend') . '/web/yas/js/version2/', Yii::getAlias('@frontend') . '/web/yas/js/version2/min/');
        echo 'Assets Generated!';
    }

    public static function minifyAndCopyFiles($sourceDir, $targetDir)
    {
        $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($sourceDir, \RecursiveDirectoryIterator::SKIP_DOTS));
    
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filePath = $file->getPathname();
                $relativePath = str_replace($sourceDir, '', $filePath);
                $targetPath = $targetDir . $relativePath;
    
                if (strpos($filePath, 'min') !== false) {
                    continue;
                }
    
                // Determine file type (CSS or JS) and create minified version
                $extension = pathinfo($file->getFilename(), PATHINFO_EXTENSION);
    
                if ($extension === 'css') {
                    $minifier = new Minify\CSS($filePath);
                    $minifier->minify($targetPath);
                } elseif ($extension === 'js') {
                    $minifier = new Minify\JS($filePath);
                    $minifier->minify($targetPath);
                }
            }
        }
    }
}
