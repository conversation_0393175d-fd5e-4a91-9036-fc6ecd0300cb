<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;
use yii\helpers\console;
use Carbon\Carbon;
use yii\db\DefaultValueConstraint;
use yii\httpclient\Client;
use common\event\SitemapEvent;
use common\models\NewsSubdomainLiveUpdate;
use common\services\DatabaseCleanService;
use yii\db\Query;

class DatabaseCleanController extends controller
{
    // tablename => ['column', 'months']
    protected $oldRowCleanUpLists = [
        'gmu_transactional_sms_lead' => ['updated_on', '2'],
        'gmu_sms_delivery_log' => ['created_on', '2'],
        'gmu_route_sms_api_log' => ['created_at', '2'],
    ];

    // tablename => ['column', 'days']
    protected $failedJobsList = [
        'failed_jobs' => ['failed_at', '3'],
    ];

    //truncate table list
    protected $cleanAllList = ['gmu_sms_delivery_log_backup'];


    protected $emptyColumnLists = [
        'gmu_bdu_log' => ['action', 'created_on', '3'],
    ];

    protected $emptyColumnByDaysList = [
        'gmu_email_bounce' => ['response', 'created_on', '15'],
    ];

    //function to call
    public function actionCleanTables()
    {
        // $this->cleanAll();
        // $this->cleanUpOldRow();
        // $this->emptyColumnByDays();
        // // $this->emptyColumn();
        // $this->failedJobs();

        $cleanService = new DatabaseCleanService();

        $cleanService->generateBackupdata();
    }

    //scripts for command

    public function cleanUpOldRow()
    {
        // print_r($month);exit();
        foreach ($this->oldRowCleanUpLists as $table => $field) {
            list($column, $month) = $field;

            try {
                $params = [':date' => Carbon::now()->subMonths($month)];
                $command = Yii::$app->db->createCommand(
                    "delete from {$table} where date({$column}) < :date",
                    $params
                )->execute();

                echo $table . ' table cleaned monthwise.\n';
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function emptyColumn()
    {
        foreach ($this->oldRowCleanUpLists as $table => $field) {
            list($columnToBeNull, $basedColumn, $month) = $field;

            try {
                $params = [':date' => Carbon::now()->subMonths($month)];
                $command = Yii::$app->db->createCommand(
                    "update {$table} set {$columnToBeNull} = ''"
                )->execute();

                echo $table . ' table row empty.\n';
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function failedJobs()
    {

        foreach ($this->failedJobsList as $table => $field) {
            list($column, $day) = $field;
            // print_r($field);

            try {
                $params = [':date' => Carbon::now()->subDays($day)];
                $command = Yii::$app->db->createCommand(
                    "delete from {$table} where date({$column}) < :date",
                    $params
                )->execute();

                echo $table . ' table cleaned daywise.\n';
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function cleanAll()
    {
        // print_r($month);exit();
        foreach ($this->cleanAllList as $table) {
            try {
                $command = Yii::$app->db->createCommand()->truncateTable($table)->execute();

                // echo $command->sql;
                echo $table . ' table truncated.\n';
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function emptyColumnByDays()
    {
        foreach ($this->emptyColumnByDaysList as $table => $field) {
            list($columnToBeNull, $basedColumn, $days) = $field;

            // print_r($table);
            // print_r($field);exit();

            try {
                $params = [':date' => Carbon::now()->subDays($days)];
                $command = Yii::$app->db->createCommand(
                    "update {$table} set {$columnToBeNull} = '' where date({$basedColumn}) < :date",
                    $params
                )->execute();

                echo 'column ' . $columnToBeNull . ' is empty in table ' . $table;
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
            // sleep for 10sec
            sleep(10);
        }
    }

    public function actionPublishNews()
    {
        $query = new Query();
        $query->select(['id', 'name', 'slug', 'scheduled_at'])
            ->from('news_subdomain')->where(['between', 'scheduled_at', Carbon::now(), Carbon::now()->addMinutes(10)])->andWhere(['status' => 0]);
        $result = $query->all();
        
        foreach ($result as $value) {
            if (empty($value['scheduled_at'])) {
                continue;
            }
            try {
                Yii::$app->db->createCommand('UPDATE news_subdomain SET status=:status,published_at=:published_at, updated_at=:updated_at WHERE id=:id')
                    ->bindValue(':id', $value['id'])
                    ->bindValue(':status', 1)
                    ->bindValue(':published_at', $value['scheduled_at'])
                    ->bindValue(':updated_at', $value['scheduled_at'])
                    ->execute();

                (new SitemapEvent())->updateNewsUpdateXml($value['slug'], NewsSubdomainLiveUpdate::STATUS_ACTIVE, $value['scheduled_at'], $value['scheduled_at']);
                
                echo $value['name'] . " is active.\n";
            } catch (\Exception $e) {
                // send notification
                echo 'Exception occured: ' . $e->getMessage();
            }
        }
    }
}
