<?php

namespace console\controllers;

use Carbon\Carbon;
use common\models\old\CollegeDekhoLeadResponseLog;
use common\models\old\GmuCityStateNew;
use common\models\old\GmuLead;
use common\models\User;
use GuzzleHttp\Client;
use Yii;

class CollegedekhoController extends \yii\console\Controller
{
    public function actionIndex()
    {
        try {
            $timeFrom = Carbon::now()->subDays(7)->subMinutes(10)->toDateTimeString();
            $timeTo = Carbon::now()->subDays(7)->toDateTimeString();

            $query = GmuLead::find()
                ->where(['between', 'created_on', $timeFrom, $timeTo])
                ->andWhere(['or', ['gmu_clg_id' => 0], ['gmu_clg_id' => null]])
                ->andWhere(['vendor_id' => null]);
            foreach ($query->batch() as $leads) {
                foreach ($leads as $lead) {
                    $data = [];
                    $data['name'] = $lead->full_name;
                    $data['phone_no'] = $lead->mobile_num;
                    $data['email'] = $lead->email_id;

                    $currentLocation = $this->cityMapping($lead->current_location);
                    if (!empty($currentLocation)) {
                        $data['current_city'] = $currentLocation['city'];
                        $data['current_state'] = $currentLocation['state'];
                    }

                    $data['preferred_stream'] = $this->streamMapping($lead->gmu_course);

                    $data['current_level'] = $this->levelMapping($lead->gmu_qualification);
                    $data['preferred_level'] = $this->preferedLevelMapping($lead->gmu_qualification);

                    $preferedLocation = $this->cityMapping($lead->location);
                    if (!empty($preferedLocation)) {
                        $data['preferred_city'] = $preferedLocation['city'];
                        $data['preferred_state'] = $preferedLocation['state'];
                        $data['preferred_country'] = 'india';
                    }
                    if (!empty($lead->utm_source)) {
                        $data['utm_source'] = $lead->utm_source;
                    }

                    $this->updateLog($this->send($data), $lead->id);
                }
            }
        } catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
        }
    }

    private function levelMapping($level)
    {
        $mapper = [
            'studying-completed-graduation' => 1,
            'studying-completed-masters-pg' => 2,
            'studying-completed-diploma' => 3,
            'PhD' => 4,
            'Certificate' => 5,
            'studying-completed-10th' => 6,
            'studying-completed-12th' => 6,
        ];

        $response = $mapper[$level] ?? null;

        return $response;
    }

    private function preferedLevelMapping($level)
    {
        $mapper = [
            'studying-completed-graduation' => 2,
            'studying-completed-masters-pg' => 4,
            'studying-completed-diploma' => 1,
            'studying-completed-10th' => 3,
            'studying-completed-12th' => 1,
        ];

        $response = $mapper[$level] ?? null;

        return $response;
    }

    private function streamMapping($stream)
    {
        $mapper = [
            'engineering-btech-mtech' => 'engineering',
            'management-bba-mba' => 'mba',
            'distance-learning-mba' => 'mba',
            'bca-mca-computers' => 'information-technology',
            'fashion' => 'engineering',
            'design' => 'design',
            'architecture' => 'engineering',
            'media-films' => 'mass-comm',
            'journalism' => 'mass-comm',
            'law' => 'law-humanities',
            'arts-humanities' => 'arts',
            'science-bsc-msc' => 'sciences',
            'animation-multimedia' => 'mass-comm',
            'finance-accounts-bcom-mcom' => 'commerce-banking',
            'medicine-healthcare-mbbs-bds-bpharm' => 'medical',
            'hotel-management-hospitality' => 'hospitality-aviation',
            'aviation' => 'hospitality-aviation',
            'fashion-design' => 'design',
        ];

        $response = $mapper[$stream] ?? 'others';

        return $response;
    }

    private function cityMapping($city)
    {
        $gmuCityTableQuery = GmuCityStateNew::getStateCItySlugbyCityName($city);
        if (empty($gmuCityTableQuery)) {
            return;
        }

        $collegeDekhoCityList = json_decode(file_get_contents(Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'storage' . DIRECTORY_SEPARATOR . 'cdekho_city_mapping.json'), true);

        $collegeDekhoCityDataKey = array_search($gmuCityTableQuery->city_slug, array_column($collegeDekhoCityList, 'gmu_slug'));

        if (empty($collegeDekhoCityDataKey)) {
            return;
        }

        $data = [];
        $data['state'] = $gmuCityTableQuery->state_slug;
        $data['city'] = $collegeDekhoCityList[$collegeDekhoCityDataKey]['slug'];

        return $data;
    }

    public function send($data)
    {
        $url = 'https://www.collegedekho.com/api/client-push-leads/gmuorganic/';

        try {
            $client = new Client();
            $response = $client->post(
                $url,
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Authorization' => 'Token 7dca46afcc3986c7159d1122928ecffbb5a9bf2a',
                    ],
                    'body' => json_encode($data),
                ]
            );

            if ($response->getStatusCode() == 200) {
                return [
                    'status' => true,
                    'response' => $response->getBody()->getContents(),
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => false,
                'response' => substr($e->getMessage(), strpos($e->getMessage(), 'response:') + 10),
            ];
        }
    }

    public function updateLog($response, int $leadId)
    {
        $leadLog = new CollegeDekhoLeadResponseLog();
        $leadLog->lead_id = $leadId;
        $leadLog->response = $response['response'];
        $leadLog->lead_type = CollegeDekhoLeadResponseLog::LEAD_TYPE_ORGANIC;
        $leadLog->created_on = Carbon::now();
        return $leadLog->save();
    }

    public function actionAddUserFreelancer()
    {
                $user = new User();
                $user->username = 'freelancer';
                $user->username = 'freelancer';
                $user->name = 'Freelancer';
                $user->email = '<EMAIL>';
                $user->status = 10;
                $user->setPassword('freelancer');
                $user->generateAuthKey();
                return $user->save();
    }
}
