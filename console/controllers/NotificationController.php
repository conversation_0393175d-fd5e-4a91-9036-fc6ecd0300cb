<?php

namespace console\controllers;

use common\models\ExamDate;
use Exception;
use Yii;
use Carbon\Carbon;
use common\models\NewsSubdomain;
use common\models\Review;
use common\models\User;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use frontend\helpers\Url;

class NotificationController extends \yii\console\Controller
{
    public function sendMail($to, $subject, $data, $template)
    {
        if (is_array($to)) {
            foreach ($to as $email) {
                try {
                    Yii::$app->mailer->compose($template, $data)
                        ->setFrom(['<EMAIL>' => 'Getmyuni Notifications'])
                        ->setTo($email)
                        ->setSubject($subject)
                        ->send();
                } catch (Exception $e) {
                    Yii::error($e->getMessage(), 'Notification Error');
                }
            }
        }
    }

    public function actionSendWeeklyExamDate()
    {
        $now = Carbon::now();
        $start = $now->startOfWeek(Carbon::MONDAY)->toDateTimeString();
        $end = $now->endOfWeek(Carbon::SUNDAY)->toDateTimeString();

        $exams = ExamDate::find()
            ->select(['name', 'start', 'exam_id'])
            ->with([
                'exam' => function ($model) {
                    return $model->select(['display_name', 'id']);
                }
            ])
            ->where(['between', 'start', $start, $end])
            ->orderBy('start', SORT_DESC)
            ->all();

        return $this->sendMail(
            $this->examDateUsers(),
            'Prior 7 days of event',
            [
                'dates' => $exams,
                'start' => $start,
                'end' => $end
            ],
            '@backend/views/mail/exam/exam-date-notification'
        );
    }

    public function actionSendTomorrowExamDate()
    {

        $now = Carbon::now()->addDay()->format('Y-m-d');
        $exams = ExamDate::find()
            ->select(['name', 'start', 'exam_id'])
            ->with([
                'exam' => function ($model) {
                    return $model->select(['display_name', 'id']);
                }
            ])
            ->where(['date(start)' => $now])
            ->orderBy('start', SORT_DESC)
            ->all();

        return $this->sendMail(
            $this->examDateUsers(),
            'One Day Prior Reminder',
            [
                'dates' => $exams
            ],
            '@backend/views/mail/exam/exam-date-notification'
        );
    }

    // Send Email Notification for news publish

    public function actionSendEmailNewsPublishDate()
    {
        $query = new \yii\db\Query();
        $allNews =   $query->from(['nsub' => 'news_subdomain'])
            ->select(['slug', 'h1', 'name', 'nsub.status'])
            ->innerJoin(['ncsub' => 'news_content_subdomain'], '`nsub`.`id` = `news_id`')
            ->where(['between', 'published_at', Carbon::now(), Carbon::now()->addMinutes(12)])
            ->andWhere(['nsub.status' => NewsSubdomain::STATUS_ACTIVE])
            ->orderBy('published_at', SORT_DESC)
            ->all();
        $items = [];
        foreach ($allNews as $news) {
            $items[] = [
                'h1' => $news['h1'] ? $news['h1'] : '',
                'name' => $news['name'] ?? '',
                'slug' => Url::toNewGetmyuni() . $news['slug'],
            ];
        }
        if (!empty($items)) {
            $this->sendMail(
                $this->newsPublishDateUser(),
                'News Published List',
                [
                    'dates' => $items

                ],
                '@backend/views/mail/news-subdomain/news-subdomain-date-notification'
            );
        }
    }

    /* public function sendMailLocal($to, $subject, $data, $template)
    {
        if (is_array($to)) {
            foreach ($to as $email) {
                try {
                    Yii::$app->sysMailer->compose($template, $data)
                        ->setFrom(['<EMAIL>' => 'Getmyuni Notifications'])
                        ->setTo($email)
                        ->setSubject($subject)
                        ->send();
                } catch (Exception $e) {
                    Yii::error($e->getMessage(), 'Notification Error');
                }
            }
        }
    }*/

    private function examDateUsers()
    {
        $emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        return $emails;
    }

    private function newsPublishDateUser()
    {
        $emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        return $emails;
    }

    public function actionSendEmailToUserReview()
    {

        $newTime = strtotime('-5 minutes');
        $startTime =  date('Y-m-d H:i:00', $newTime);
        $endTime = date('Y-m-d H:i:00');
        $reviewRows = (new \yii\db\Query())
                ->select(['review.id as review_id', 'review.status as review_status','college.display_name as display_name','student.email as studentMail','student.name as studentName'])
                ->from('review')
                ->innerjoin('student', 'student.id=review.student_id')
                ->innerjoin('college', 'college.id=review.college_id')
                 ->where(['between', 'review.created_at', $startTime  , $endTime])
                ->andWhere(['review.status'=>Review::STATUS_PENDING])
                ->orderby(['review.id'=>SORT_ASC])
                ->all();
        foreach ($reviewRows as $row) {
            $sendUserMail[] = $row['studentMail'];
            $reviews[] = [
                'college_name' =>  $row['display_name'] ? $row['display_name']:'',
                'email'=>$row['studentMail'],
                'student_name'=>$row['studentName']
            ];
            if (!empty($sendUserMail)) {
                $this->sendMail(
                    $sendUserMail,
                    ' Thank you! Getmyuni has received your College Review.',
                    [
                        'datas' => $reviews
        
                    ],
                    '@backend/views/mail/reviewMail/reviewMailToUser'
                );
            }
        }
    }

    public function actionSendEmailToUserUprovedReview()
    {

        $newTime = strtotime('-5 minutes');
        $startTime =  date('Y-m-d H:i:00', $newTime);
        $endTime = date('Y-m-d H:i:00');
        $reviewRows = (new \yii\db\Query())
                ->select(['review.id as review_id', 'review.status as review_status','college.display_name as display_name','student.email as studentMail','student.name as studentName'])
                ->from('review')
                ->innerjoin('student', 'student.id=review.student_id')
                ->innerjoin('college', 'college.id=review.college_id')
                ->where(['between', 'review.updated_at', $startTime  , $endTime])
                ->andWhere(['review.status'=>Review::STATUS_APPROVED])
                ->all();
        foreach ($reviewRows as $row) {
            $sendUserMail[] = $row['studentMail'];
            $reviews[] = [
                'college_name' =>  $row['display_name'] ? $row['display_name']:'',
                'email'=>$row['studentMail'],
                'student_name'=>$row['studentName']
            ];
            if (!empty($sendUserMail)) {
                $this->sendMail(
                    $sendUserMail,
                    'Congratulations! Your College Review has been published on Getmyuni',
                    [
                        'datas' => $reviews
        
                    ],
                    '@backend/views/mail/reviewMail/reviewMailToUserAfterStatusApprove'
                );
            }
        }
    }
}
