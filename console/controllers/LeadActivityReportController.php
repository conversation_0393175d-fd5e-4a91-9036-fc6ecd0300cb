<?php

namespace console\controllers;

use Carbon\Carbon;
use Yii;

class LeadActivityReportController extends \yii\console\Controller
{
    private $_gmudb;

    protected $emailLists = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
    ];

    public function __construct(
        $id,
        $module,
        $config = []
    ) {
        $this->_gmudb = Yii::$app->gmudb;
        parent::__construct($id, $module, $config);
    }

    public function actionIndex()
    {
        $this->leadAnalyticsReport();
    }

    public function leadAnalyticsReport()
    {
        $previousDate = Carbon::now()->subDays(1)->format('Y-m-d');
        $lastSevenDay = Carbon::now()->subDays(8)->format('Y-m-d');
        $previousDateLeads = $this->getLeadByDate($previousDate);
        $lastSevenDaysLeads = $this->getLeadByDate($lastSevenDay);
        $lastWeekLeads = $this->getLastOneWeekLeads();

        $uniqueKeys = array_unique(array_merge(array_keys($previousDateLeads), array_keys($lastSevenDaysLeads)));
        $compareItems = [];
        foreach ($uniqueKeys as $uniqueKey) {
            $temp = [];
            $temp = [
                'source' => $uniqueKey,
                $lastSevenDay => $lastSevenDaysLeads[$uniqueKey][$lastSevenDay] ?? '-',
                $previousDate => $previousDateLeads[$uniqueKey][$previousDate] ?? '-',
            ];

            if (isset($lastSevenDaysLeads[$uniqueKey][$lastSevenDay]) && isset($previousDateLeads[$uniqueKey][$previousDate])) {
                $temp['change'] = (($previousDateLeads[$uniqueKey][$previousDate] - $lastSevenDaysLeads[$uniqueKey][$lastSevenDay]) / $lastSevenDaysLeads[$uniqueKey][$lastSevenDay]) * 100;
            } else {
                $temp['change'] = '-';
            }

            $compareItems[$uniqueKey] = $temp;
        }

        $message = Yii::$app->sysMailer->compose('lead-activity', [
            'name' => 'Team',
            'email' => '<EMAIL>',
            'compareItems' => $compareItems,
            'previousDateLeads' => $previousDateLeads,
            'lastWeekLeads' => $lastWeekLeads,
        ])
            ->setFrom(['<EMAIL>' => 'GetMyUni'])
            ->setTo($this->emailLists)
            ->setSubject('Getmyuni: Lead Analytics Report');

        $message->send();
    }

    private function getLeadByDate($date)
    {
        $leads = $this->getLeads($date);
        $items = [];
        foreach ($leads->queryAll() as $lead) {
            if (empty($lead['source'])) {
                $items['CollegeDekho'] = [
                    $date => $lead['count'],
                ];
            } else {
                $items[$lead['source']] = [
                    $date => $lead['count'],
                ];
            }
        }

        return $items;
    }

    private function getLastOneWeekLeads()
    {
        $leads = $this->_gmudb->createCommand("
            select case
                WHEN url LIKE '%/college/%' AND url NOT LIKE '%?show_lead_form%' THEN 'College'
                WHEN url LIKE '%?show_lead_form%' THEN 'College freeze form'
                WHEN url LIKE '%-colleges%' THEN 'Top Search Listing'
                WHEN url LIKE '%/exams%' THEN 'Exam'
                WHEN url LIKE '%common-application-form%' THEN 'CAF'
                WHEN url LIKE '%caf' THEN 'CAF'
                WHEN url LIKE '%articles%' THEN 'Articles'
                WHEN url LIKE '%-course' THEN 'Course'
                WHEN url LIKE '%-syllabus-subjects' THEN 'Course'
                WHEN url LIKE '%-scope-salary' THEN 'Course'
                WHEN url LIKE '%boards%' THEN 'Board'
                WHEN url LIKE '%news/%' THEN 'News'
                WHEN url LIKE '%sarkari%' THEN 'Sarkari Exam'
                WHEN url LIKE '%olympiad%' THEN 'Olympiad'
                WHEN url LIKE '%scholarship-program%' THEN 'Scholarships'
                WHEN url LIKE '%/scholarships/%' THEN 'Scholarships'
                WHEN url LIKE '%/college-admissions/%' THEN 'Paid Google'
                WHEN url LIKE '%stories%' THEN 'Stories'
                WHEN url LIKE '%news%' THEN 'News'
                WHEN url LIKE '%-course#%' THEN 'Course'
                WHEN url LIKE '%https://www.getmyuni.com/' THEN 'Homepage'
                WHEN url like '%/reviews%' THEN 'Reviews'
                WHEN url LIKE '%/college-compare?%' THEN 'Compare College'
                WHEN url LIKE '%-course?%' THEN 'Course'
                WHEN url LIKE '%?utm_source=jagranjosh' THEN 'Affiliate-JagranJosh'
                WHEN url LIKE '%/scholarships' THEN 'Scholarships'
            END AS source,
            count(DISTINCT mobile_num) AS count
            FROM gmu_leads
            WHERE date(`created_on`) between '" . Carbon::now()->subDays(8)->format('Y-m-d') . "' and '" . Carbon::now()->subDays(1)->format('Y-m-d') . "'
            GROUP BY source
            ORDER BY count(DISTINCT mobile_num) DESC
        ");

        $items = [];
        foreach ($leads->queryAll() as $lead) {
            if (empty($lead['source'])) {
                $items['CollegeDekho'] = $lead['count'];
            } else {
                $items[$lead['source']] = $lead['count'];
            }
        }

        return $items;
    }

    private function getLeads($date)
    {
        return $this->_gmudb->createCommand("
            select case
                WHEN url LIKE '%/college/%' AND url NOT LIKE '%?show_lead_form%' THEN 'College'
                WHEN url LIKE '%?show_lead_form%' THEN 'College freeze form'
                WHEN url LIKE '%-colleges%' THEN 'Top Search Listing'
                WHEN url LIKE '%/exams%' THEN 'Exam'
                WHEN url LIKE '%common-application-form%' THEN 'CAF'
                WHEN url LIKE '%caf' THEN 'CAF'
                WHEN url LIKE '%articles%' THEN 'Articles'
                WHEN url LIKE '%-course' THEN 'Course'
                WHEN url LIKE '%-syllabus-subjects' THEN 'Course'
                WHEN url LIKE '%-scope-salary' THEN 'Course'
                WHEN url LIKE '%boards%' THEN 'Board'
                WHEN url LIKE '%news/%' THEN 'News'
                WHEN url LIKE '%sarkari%' THEN 'Sarkari Exam'
                WHEN url LIKE '%olympiad%' THEN 'Olympiad'
                WHEN url LIKE '%scholarship-program%' THEN 'Scholarships'
                WHEN url LIKE '%/scholarships/%' THEN 'Scholarships'
                WHEN url LIKE '%/college-admissions/%' THEN 'Paid Google'
                WHEN url LIKE '%stories%' THEN 'Stories'
                WHEN url LIKE '%news%' THEN 'News'
                WHEN url LIKE '%-course#%' THEN 'Course'
                WHEN url LIKE '%https://www.getmyuni.com/' THEN 'Homepage'
                WHEN url like '%/reviews%' THEN 'Reviews'
                WHEN url LIKE '%/college-compare?%' THEN 'Compare College'
                WHEN url LIKE '%-course?%' THEN 'Course'
                WHEN url LIKE '%?utm_source=jagranjosh' THEN 'Affiliate-JagranJosh'
                WHEN url LIKE '%/scholarships' THEN 'Scholarships'
            END AS source,
            count(DISTINCT mobile_num) AS count
            FROM gmu_leads
            WHERE date(`created_on`) = '" . $date . "'
            GROUP BY source
            ORDER BY count(DISTINCT mobile_num) DESC
        ");
    }
}
