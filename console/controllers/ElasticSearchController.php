<?php

namespace console\controllers;

use common\models\College;
use common\models\Exam;
use common\models\Course;
use common\models\Article;
use common\models\Board;
use common\models\Olympiad;
use common\models\GlobalSearch;
use common\models\Scholarship;
use common\models\CollegeStreamRank;
use common\services\ElasticSearchService;
use common\services\MysqlSearchService;
use frontend\models\CollegeCourseSearch;
use yii\console\Controller;
use common\models\CollegeElastic;
use common\event\CollegeFilterEvent;
use yii\helpers\Inflector;
use common\models\Stream;
use common\models\SponsorCollege;


use Yii;

class ElasticSearchController extends controller
{

    protected $mysqlSearchService;
    protected $elasticSearchService;
    protected $model;

    public function __construct(
        $id,
        $module,
        $model,
        MysqlSearchService $mysqlSearchService,
        ElasticSearchService $elasticSearchService,
        $config = []
    ) {
        $this->mysqlSearchService = $mysqlSearchService;
        $this->elasticSearchService = $elasticSearchService;
        parent::__construct($id, $module, $config, $model);
    }

    public function actionElasticAll()
    {
        // $this->actionArticleToElastic();
        // GlobalSearch::createIndex();
        GlobalSearch::updateMapping();
        $this->actionExamToElastic();
        $this->actionCourseToElastic();
        $this->actionBoardsToElastic();
        $this->actionCollegeToElastic();
        $this->actionScholarshipToElastic();
    }

    /** Import college list to elastic search */
    public function actionCollegeToElastic()
    {
        $query = College::find();

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                try {
                    $result = $this->elasticSearchService->updateElasticSearch($college, 'college');
                    $this->mysqlSearchService->updateMysqlSearch($college, 'college');
                    if ($result) {
                        echo "{$college->id} \t {$college->name} \n";
                    } else {
                        echo $college->id . " Not Done\n";
                    }
                } catch (\Exception $e) {
                    echo 'Message: ' . $e->getMessage() . "\n";
                }
            }
        }
    }

    /** Import exams list to elastic search */
    public function actionExamToElastic()
    {
        $exmas = Exam::find()->active()->all();
        foreach ($exmas as $exam) {
            try {
                $result = $this->elasticSearchService->updateElasticSearch($exam, 'exam');
                $this->mysqlSearchService->updateMysqlSearch($exam, 'exam');
                if ($result) {
                    echo $exam->id . ' ' . $exam->name . " \n";
                } else {
                    echo $exam->id . " Not Done\n";
                }
            } catch (\Exception $e) {
                echo 'Message: ' . $e->getMessage();
            }
        }
    }

    /** Import course list to elastic search */
    public function actionCourseToElastic()
    {
        $allCourses = Course::find()->active()->all();

        foreach ($allCourses as $course) {
            try {
                $result = $this->elasticSearchService->updateElasticSearch($course, 'course');
                $this->mysqlSearchService->updateMysqlSearch($course, 'course');
                if ($result) {
                    echo $course->id . ' ' . $course->name . " \n";
                } else {
                    echo $course->id . " Not Done\n";
                }
            } catch (\Exception $e) {
                echo 'Message: ' . $e->getMessage();
            }
        }
    }

    /** Import Article list to elastic search */
    public function actionArticleToElastic()
    {
        $articles = Article::find()->active()->all();
        foreach ($articles as $article) {
            try {
                $result = $this->elasticSearchService->updateElasticSearch($article, 'article');
                $this->mysqlSearchService->updateMysqlSearch($article, 'article');

                if ($result) {
                    echo $article->id . ' ' . $article->name . " \n";
                } else {
                    print_r($article->getErrors());
                }
            } catch (\Exception $e) {
                echo 'Message: ' . $e->getMessage();
            }
        }
    }
    
    /** Import data of Board to Elastic */
    public function actionBoardsToElastic()
    {
        $boards = Board::find()->active()->all();
        foreach ($boards as $board) {
            try {
                $result = $this->elasticSearchService->updateElasticSearch($board, 'board');
                $this->mysqlSearchService->updateMysqlSearch($board, 'board');

                if ($result) {
                    echo $board->name . ' ' . $board->slug . " \n";
                } else {
                    echo $board->slug . " Not Done\n";
                }
            } catch (\Exception $e) {
                echo 'Message: ' . $e->getMessage();
            }
        }
    }

    public function actionCollegeElasticFilter()
    {
        // $this->actionArticleToElastic();
        CollegeElastic::createIndex();
        // CollegeElastic::deleteIndex();
    }

    public function actionImportCollege()
    {
        $queries = College::find()->active();
        foreach ($queries->batch() as $colleges) {
            foreach ($colleges as $college) {
                (new CollegeFilterEvent())->updateElasticCollegeFilter($college);
            }
        }
    }
    
    /** Import data of Scholarship to Elastic */
    public function actionScholarshipToElastic()
    {
        $scholarships = Scholarship::find()->active()->all();
        foreach ($scholarships as $scholarship) {
            try {
                $result = $this->elasticSearchService->updateElasticSearch($scholarship, 'scholarship');
                $this->mysqlSearchService->updateMysqlSearch($scholarship, 'scholarship');

                if ($result) {
                    echo $scholarship->name . ' ' . $scholarship->slug . " \n";
                } else {
                    echo $scholarship->slug . " Not Done\n";
                }
            } catch (\Exception $e) {
                echo 'Message: ' . $e->getMessage();
            }
        }
    }

    public function actionOlympiadToElastic()
    {
        $olympiads = Olympiad::find()->active()->all();
        foreach ($olympiads as $olympiad) {
            try {
                $result = $this->elasticSearchService->updateElasticSearch($olympiad, 'olympiad');
                $this->mysqlSearchService->updateMysqlSearch($olympiad, 'olympiad');

                if ($result) {
                    echo $olympiad->name . ' ' . $olympiad->slug . " \n";
                } else {
                    echo $olympiad->slug . " Not Done\n";
                }
            } catch (\Exception $e) {
                echo 'Message: ' . $e->getMessage();
            }
        }
    }

    public function actionImportCollegeStreamRank()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'GmuRanking.csv';
        
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i=0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            $stream = Stream::find()->select(['slug','id'])->where(['id' => $fileop[3]])->one();
            $rankExist =  CollegeStreamRank::find()->where(['college_id'=> $fileop[0]])->andWhere(['stream_id'=> $fileop[3]])->one();
            $columnNameRanks = $stream->slug . '_rank';
            $elasticArrayRank['script']['source']  = "ctx._source['" . $columnNameRanks . "'] =" . $fileop[1];
            $elasticArrayRank['query']['match_phrase']['college_id']  = $fileop[0];
            $this->updateSponserCollegeStatus($fileop[0]);
            if (!empty($rankExist)) {
                $rankExist->college_id = $fileop[0];
                $rankExist->stream_id = $fileop[3];
                $rankExist->rank = $fileop[1];
                $rankExist->stream = $stream->slug;
                $columnNameRank = $rankExist->stream . '_rank';
                if ($rankExist->update()) {
                    $columnName = $rankExist->stream . '_rank';
                    $elasticArray['properties'][$columnName]['type'] = 'double';
                    $elasticData =  $this->elasticUpdateCurl($elasticArray);
                    $elasticDataRank =  $this->elasticRankUpdate($elasticArrayRank);
                    echo "{$rankExist->id} \t {$fileop[0]} \n";
                }
            } else {
                $collegeStreamRank = new CollegeStreamRank();
                $collegeStreamRank->college_id = $fileop[0];
                $collegeStreamRank->stream_id = $fileop[3];
                $collegeStreamRank->rank = $fileop[1];
                $collegeStreamRank->stream = $stream->slug;
                if ($collegeStreamRank->save()) {
                    $elasticArray['properties'][$columnNameRanks]['type'] = 'double';
                    $elasticData =  $this->elasticUpdateCurl($elasticArray);
                    $elasticDataRank =  $this->elasticRankUpdate($elasticArrayRank);
                    echo "{$collegeStreamRank->id} \t {$fileop[0]} \n";
                }
            }
        }
    }
    // add Elatic field
    public function elasticUpdateCurl($updateArray)
    {
        
        $url = 'http://10.0.0.33:9200/all-colleges/_mapping/_doc?include_type_name=true';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json; charset=utf-8']);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($updateArray));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $return = curl_exec($ch) or die(curl_error($ch));
        curl_close($ch);
        $array_return = json_decode($return, true);
        return $array_return ?? [];
    }

    public function elasticRankUpdate($elasticArrayRank)
    {
        
        $url = 'http://10.0.0.33:9200/all-colleges/_update_by_query?refresh=true';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json; charset=utf-8']);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($elasticArrayRank));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $return = curl_exec($ch) or die(curl_error($ch));
        curl_close($ch);
        $array_return = json_decode($return, true);
        return $array_return ?? [];
    }

    public function updateSponserCollegeStatus($college_id)
    {
        $countActiveSponsorCollege = SponsorCollege::find()->where(['college_id'=>$college_id])->andWhere(['status'=>1])->count();
        $countrCollege = College::find()->where(['id'=>$college_id])->andWhere(['is_sponsored'=>1])->count();
         $status =0;
        if ($countActiveSponsorCollege==0 &&  $countrCollege==1) {
            $elasticArrayRank['script']['source']  = "ctx._source['is_sponsored'] =" . $status;
            $elasticArrayRank['query']['match_phrase']['college_id']  = $college_id;
            $elasticDataRank =  $this->elasticRankUpdate($elasticArrayRank);
        }
    }
}
